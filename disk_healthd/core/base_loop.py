# Copyright (c) 2013-2020, SMARTX
# All rights reserved.

import logging
import random
from threading import Thread
import time

from disk_healthd.core import monotonic_time


class BaseLoop:
    TICKER_TIME = 5.0

    def __init__(self, interval):
        self.interval = interval
        self._stop = False
        self.worker = None
        self.name = self.__class__.__name__
        self._random_delay = True

    def loop(self):
        raise NotImplementedError()

    def loop_warp(self):
        if self._random_delay:
            random_delay = random.uniform(1, 5)
            logging.info("{}: loop started with random delay {:.3f}s.".format(self.name, random_delay))
            time.sleep(random_delay)

        try:
            self.loop()
        except Exception as e:
            logging.exception("{}: loop break due to exception: {}".format(self.name, e))

        logging.info("{}: loop exited.".format(self.name))

    def start(self):
        self.worker = Thread(target=self.loop_warp)
        self.worker.daemon = True
        self._stop = False
        self.worker.start()

    def stop(self):
        self._stop = True

    def join(self):
        if self.worker:
            self.worker.join()

    def tick_sleep(self, time_to_wait):
        while not self._stop and time_to_wait > 0:
            start = monotonic_time.time()
            time.sleep(max(min(self.TICKER_TIME, time_to_wait), 0.1))
            time_used = monotonic_time.time() - start
            time_to_wait = max(time_to_wait - time_used, 0)
