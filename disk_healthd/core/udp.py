# Copyright (c) 2013-2024, SMARTX
# All rights reserved.

import dataclasses
import logging
from socketserver import BaseRequestHandler, ThreadingUDPServer
import struct
from threading import Thread


@dataclasses.dataclass
class UdpRequestContext:
    msg_type_id: int
    msg_content: bytes


class UdpRequestRouter:
    def __init__(self):
        self.service_map = {}

    def register_service(self, service_class):
        for msg_type in service_class.register_msg_type:
            self.service_map[msg_type.value] = service_class

    def get_handle_func(self, msg_type_id):
        if service_class := self.service_map.get(msg_type_id):
            return service_class.handle_request
        return None


class _UdpRequestHandler(BaseRequestHandler):
    request_router: UdpRequestRouter = None

    def handle(self):
        packet, socket = self.request
        msg_type_id = struct.unpack("@Q", packet[: struct.calcsize("@Q")])[0]

        handle_func = self.request_router.get_handle_func(msg_type_id)
        if not handle_func:
            logging.info(f"Unknown UDP message type received: {msg_type_id}")
            return

        try:
            response = handle_func(UdpRequestContext(msg_type_id, packet))
        except Exception as e:
            logging.exception(f"An error occurred during handle UDP request: {e}")
            return

        socket.sendto(response, self.client_address)


class UdpServer:
    def __init__(self, request_router):
        self.router = request_router
        self.thread = None

    def run(self, host: str, port: int):
        _UdpRequestHandler.request_router = self.router
        with ThreadingUDPServer((host, port), _UdpRequestHandler) as server:
            logging.info(f"UDP Server started at {host}:{port}")
            server.serve_forever()

    def start(self, host: str, port: int):
        self.thread = Thread(target=self.run, args=(host, port))
        self.thread.daemon = True
        self.thread.start()
