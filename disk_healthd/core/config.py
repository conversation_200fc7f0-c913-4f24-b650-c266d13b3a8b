# Copyright (c) 2013-2020, SMARTX
# All rights reserved.
from configparser import SafeConfigParser
import logging


class Config:
    DEFAULT_CONFIG = "/usr/share/disk_healthd/disk_healthd.conf.default"
    ETC_CONFIG = "/etc/disk_healthd/disk_healthd.conf"

    def __init__(self):
        self._loaded = False

    def load(self, prefix=""):
        if not self._loaded:
            self._load_from_file(prefix + self.DEFAULT_CONFIG)
            self._load_from_file(prefix + self.ETC_CONFIG)
            self._loaded = True

    def _load_from_file(self, config_path):
        logging.info("loading configure from {}".format(config_path))
        parser = SafeConfigParser()
        if not parser.read(config_path):
            raise Exception("load configure {} failed".format(config_path))

        class _Section:
            pass

        for section in parser.sections():
            if hasattr(self, section):
                section_obj = getattr(self, section)
            else:
                section_obj = _Section()
                setattr(self, section, section_obj)

            for key, value in parser.items(section):
                logging.info("load_config: {}.{} = {}".format(section, key, value))
                if str(value).isdigit():
                    value = parser.getint(section, key)
                elif value in ["1", "yes", "true", "on", "0", "no", "false", "off"]:
                    value = parser.getboolean(section, key)
                else:
                    value = parser.get(section, key)
                setattr(section_obj, key.upper(), value)


config = Config()
