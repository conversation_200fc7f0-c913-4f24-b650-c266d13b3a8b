# Copyright (c) 2013-2020, SMARTX
# All rights reserved.

import logging
import os
from time import sleep

from inotify_simple import INotify, flags

from disk_healthd.core.disk_map import disk_map
from disk_healthd.core.slow_disk import OfflineDisk<PERSON>el<PERSON>, SlowDiskHelper


class DiskEventWatcher:
    # Cooperate with udev event rule config: 98-tuna-udev.rules

    DISK_ADD = "DISK_ADD"
    WATCH_FILE = "/var/lib/disk_healthd/disk_update"
    PART_UUID_DIR = "/dev/disk/by-partuuid"

    def __init__(self):
        self._touch_watch_file()
        self._inotify = INotify()
        self.callbacks = {}

    def _touch_watch_file(self):
        if not os.path.exists(self.WATCH_FILE):
            basedir = os.path.dirname(self.WATCH_FILE)
            os.makedirs(basedir)
            open(self.WATCH_FILE, "a").close()

    def add_callback(self, event, func):
        self.callbacks[event] = func

    def _wait_and_handle_event(self):
        # timeout: break block every 10min
        # read_delay: block until first event coming and wait another 2s for following events
        event_disk_add = False
        for event in self._inotify.read(timeout=1000 * 60 * 10, read_delay=2000):
            event_flags = [str(f) for f in flags.from_mask(event.mask)]
            logging.info("disk_watch: {} {}".format(event, "|".join(event_flags)))
            event_disk_add = True

        if event_disk_add and self.callbacks.get(self.DISK_ADD):
            logging.info("disk_watch: disk updated, reload disks and loops")
            self.callbacks.get(self.DISK_ADD)()
            return

        disk_add, disk_remove = disk_map.check_disks_changed()
        partition_changed = disk_map.check_partition_changed()
        logging.info(f"disk_watch: {disk_add=} {disk_remove=} {partition_changed=}")

        if (disk_add or partition_changed) and self.callbacks.get(self.DISK_ADD):
            logging.info("disk_watch: new disk or partition changed, reload disks and loops")
            self.callbacks.get(self.DISK_ADD)()
            return

        SlowDiskHelper().remove_absent_disks()
        OfflineDiskHelper().remove_absent_disks()

    def watch(self):
        self._inotify.add_watch(self.WATCH_FILE, flags.MODIFY)
        if not os.path.isdir(self.PART_UUID_DIR):
            logging.warning(f"disk_watch: inotify not ready for {self.PART_UUID_DIR}")
        else:
            self._inotify.add_watch(self.PART_UUID_DIR, flags.CREATE | flags.DELETE)
            logging.info(f"disk_watch: inotify watch on {self.PART_UUID_DIR}")

        while True:
            try:
                self._wait_and_handle_event()
            except Exception as e:
                logging.exception("DiskEventWatcher: an error occurred during wait_event: {}".format(e))
                logging.info("DiskEventWatcher: continue after 30s.")
                sleep(30)
