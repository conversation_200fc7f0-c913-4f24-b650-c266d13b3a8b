# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
import logging
import os
import time

from disk_healthd.core.disk_map import disk_map
from tuna.hardware.disk import Disk
from tuna.node.storage_manager import StorageManager
from tuna.service.slow_disk_service import OfflineRecordService, SlowDiskService

IGNORED = "IGNORED"
SLOW_DETECTED = "SLOW_DETECTED"
MARK_TO_ISOLATE = "MARK_TO_ISOLATE"
CHUNK_INVOLVED = "CHUNK_INVOLVED"
CAP_EXCLUDED = "CAP_EXCLUDED"


class SlowDiskHelper:
    def __init__(self):
        self.service = SlowDiskService()

    def ensure_schema_init(self):
        self.service.ensure_indexes()
        while not self.service.ensure_disk_record_init():
            logging.warning("slow_disk_helper: failed to ensure node slow disk record, retry.")
            time.sleep(1)

    def _get_review_record(self):
        record_views = self.service.get_record_views()
        if not record_views:
            logging.warning("slow_disk_helper: slow disk schema not init.")
            return None, None

        current_record = self.service.get_current_node_record(record_views)
        if not current_record:
            logging.warning("slow_disk_helper: current node record not init.")
            return record_views, None

        return record_views, current_record

    def remove_absent_disks(self):
        for _ in range(10):
            if self._remove_absent_disks():
                return
            else:
                time.sleep(1)
        logging.warning("slow_disk_helper: remove_absent_disks failed, try next time.")

    def _remove_absent_disks(self):
        record_views, record = self._get_review_record()
        if not (record_views and record):
            return True

        view_changed = False
        for disk in record.get("slow_disks", []):
            disk_name = disk["disk_name"]
            if Disk(disk_name).is_device_state_offline():
                logging.info(f"{disk_name} is offline, skip auto release absent slow disk")
                continue
            disk_removed = disk["disk_serial"] not in disk_map.last_disk_serials
            can_be_clean = disk["status"] in [IGNORED, SLOW_DETECTED, CAP_EXCLUDED]
            if disk_removed and can_be_clean:
                msg = "slow_disk_helper: mark disk {} {} to be removed."
                logging.info(msg.format(disk["disk_name"], disk["disk_serial"]))
                disk["remove"] = True
                view_changed = True

        if view_changed:
            record["slow_disks"] = [disk for disk in record["slow_disks"] if not disk.get("remove")]
            return self.service.save_record(record_views)

        return True

    def add_slow_disks(self, new_slow_disks):
        for _ in range(10):
            if self._add_slow_disks(new_slow_disks):
                return
            else:
                time.sleep(0.1)
        logging.warning("slow_disk_helper: failed to add new slow disk record.")

    def _add_slow_disks(self, new_slow_disks):
        record_views, record = self._get_review_record()
        if not (record_views and record):
            return True

        view_changed = False

        new_serials = {disk_info["serial"] for disk_info in new_slow_disks if disk_info.get("serial")}
        for disk in record["slow_disks"]:
            if disk["status"] == IGNORED and disk["disk_serial"] in new_serials:
                msg = "slow_disk_helper: update disk {} {} status: IGNORED -> SLOW_DETECTED"
                logging.info(msg.format(disk["disk_name"], disk["disk_serial"]))
                view_changed = True
                disk["status"] = SLOW_DETECTED

        exist_serials = {disk["disk_serial"] for disk in record["slow_disks"]}
        for disk_info in new_slow_disks:
            if disk_info.get("serial") and disk_info["serial"] not in exist_serials:
                msg = "slow_disk_helper: add new slow disk {} {} as SLOW_DETECTED"
                logging.info(msg.format(disk_info["name"], disk_info["serial"]))
                view_changed = True
                record["slow_disks"].append(
                    {
                        "disk_name": disk_info["name"],
                        "disk_serial": disk_info["serial"],
                        "status": SLOW_DETECTED,
                    }
                )

        if view_changed:
            return self.service.save_record(record_views)

        return True


class OfflineDiskHelper:
    def __init__(self):
        self.service = OfflineRecordService()

    def ensure_schema_init(self):
        self.service.ensure_indexes()
        while not self.service.ensure_disk_record_init():
            logging.warning("offline_disk_helper: failed to ensure node offline disk record, retry.")
            time.sleep(1)

    def _get_host_review_record(self):
        record_views = self.service.get_record_views()
        if not record_views:
            logging.warning("offline_disk_helper: offline disk schema not init.")
            return None, None

        current_record = self.service.get_current_node_record(record_views)
        if not current_record:
            logging.warning("offline_disk_helper: current node record not init.")
            return record_views, None

        return record_views, current_record

    def remove_absent_disks(self):
        for _ in range(10):
            if self._remove_absent_disks():
                return
            else:
                time.sleep(1)
        logging.warning("offline_disk_helper: remove_absent_disks failed, try next time.")

    def _remove_absent_disks(self):
        record_views, record = self._get_host_review_record()
        if not (record_views and record):
            return True

        view_changed = False
        for disk in record.get("offline_disks", []):
            disk_path, disk_serial, disk_wwid = disk["disk_path"], disk["disk_serial"], disk["disk_wwid"]
            if Disk(os.path.basename(disk_path)).is_device_state_offline():
                logging.info(f"{disk_path} is offline, skip checking auto release absent offline disk")
                continue
            if disk_wwid not in disk_map.last_disk_wwids:
                logging.info(f"offline_disk_helper: mark disk {disk_path} {disk_serial=} {disk_wwid=} to be removed.")
                disk["remove"] = True
                view_changed = True
        if not view_changed:
            return True

        is_storage_unhealthy = StorageManager().is_exist_data_recover() or StorageManager().is_exist_data_dead()
        if is_storage_unhealthy:
            logging.info("current storage is unhealthy, skip auto release absent offline disk")
            return True

        record["offline_disks"] = [disk for disk in record["offline_disks"] if not disk.get("remove")]
        return self.service.save_record(record_views)
