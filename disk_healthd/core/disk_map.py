# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
import os

from tuna.hardware.disk import Disk
from tuna.node.storage_manager import StorageDiskManager
from tuna.service.disk_status_service import DiskStatusService


class DiskMap:
    def __init__(self):
        DiskStatusService().ensure_indexes()
        self.disk_names = {}
        self.disk_serials = {}
        self.disk_wwids = {}
        self.last_disk_serials = set()
        self.last_disk_wwids = set()
        self.last_part_uuids = set()

    @staticmethod
    def _get_all_part_uuids():
        part_uuid_path = "/dev/disk/by-partuuid/"
        if not os.path.isdir(part_uuid_path):
            return set()
        return set(os.listdir(part_uuid_path))

    def cumulative_update(self):
        disk_names = Disk.disk_names()
        self.last_disk_serials = set()
        self.last_disk_wwids = set()
        self.last_part_uuids = self._get_all_part_uuids()
        for disk_name in disk_names:
            disk_info = Disk(disk_name).get_disk_full_info()
            self.disk_names[disk_name] = disk_info
            disk_serial = disk_info.get("serial")
            disk_wwid = disk_info.get("wwid")
            if disk_serial:
                self.disk_serials[disk_serial] = disk_info
                self.last_disk_serials.add(disk_serial)
            if disk_wwid:
                self.disk_wwids[disk_wwid] = disk_info
                self.last_disk_wwids.add(disk_wwid)

    def upgrade_disk_records(self):
        DiskStatusService().upgrade_node_disk_status_records(list(self.disk_names.values()))

    def ensure_disk_records(self):
        DiskStatusService().ensure_disk_records(list(self.disk_names.values()))

    def check_disks_changed(self):
        current_disk_serials = set()
        current_disk_wwids = set()
        disk_names = Disk.disk_names()
        for disk_name in disk_names:
            if serial := Disk(disk_name).get_serial_number():
                current_disk_serials.add(serial)
            if wwid := Disk(disk_name).get_disk_wwid():
                current_disk_wwids.add(wwid)

        disk_serial_add = bool(current_disk_serials - self.last_disk_serials)
        disk_wwid_add = bool(current_disk_wwids - self.last_disk_wwids)
        disk_serial_remove = bool(self.last_disk_serials - current_disk_serials)
        disk_wwid_remove = bool(self.last_disk_wwids - current_disk_wwids)

        self.last_disk_serials = current_disk_serials
        self.last_disk_wwids = current_disk_wwids

        return any([disk_serial_add, disk_wwid_add]), any([disk_serial_remove, disk_wwid_remove])

    def check_partition_changed(self):
        partition_changed = False

        current_part_uuids = self._get_all_part_uuids()
        if current_part_uuids != self.last_part_uuids:
            partition_changed = True

        self.last_part_uuids = current_part_uuids
        return partition_changed

    def match_one_disk_for_chunk_part(self, chunk_part):
        for disk in self.disk_names.values():
            if StorageDiskManager.is_chunk_part_belong_to_disk(chunk_part, disk):
                return disk
        return None


disk_map = DiskMap()
