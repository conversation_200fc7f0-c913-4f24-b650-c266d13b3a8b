# Copyright (c) 2013-2020, SMARTX
# All rights reserved.

import ctypes
import ctypes.util
import os

__all__ = ["time", "CLOCK_MONOTONIC", "CLOCK_MONOTONIC_RAW"]

# linux/time.h
CLOCK_MONOTONIC = 1
CLOCK_MONOTONIC_RAW = 4

try:
    clock_gettime = ctypes.CDLL(ctypes.util.find_library("c"), use_errno=True).clock_gettime
except Exception:
    clock_gettime = ctypes.CDLL(ctypes.util.find_library("rt"), use_errno=True).clock_gettime


class timespec(ctypes.Structure):
    _fields_ = [("tv_sec", ctypes.c_long), ("tv_nsec", ctypes.c_long)]


clock_gettime.argtypes = [ctypes.c_int, ctypes.POINTER(timespec)]


def time(clock=CLOCK_MONOTONIC_RAW):
    ts = timespec()
    if clock_gettime(clock, ctypes.pointer(ts)) == 0:
        return ts.tv_sec + ts.tv_nsec * 1e-9
    else:
        errno_ = ctypes.get_errno()
        raise OSError(errno_, os.strerror(errno_))
