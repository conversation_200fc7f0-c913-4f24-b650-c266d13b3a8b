# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
from datetime import datetime
from unittest import TestCase
from unittest.mock import call, Mock, patch

from disk_healthd.core.udp import UdpRequestContext
from disk_healthd.disk_offline.messages import (
    HeartbeatRequest,
    HeartbeatResponse,
    MsgType,
    OfflineAckRequest,
    OfflineAckResponse,
    OfflineRequest,
    OfflineResponse,
    WarningRequest,
    WarningResponse,
)
from disk_healthd.disk_offline.services import (
    MARK_TO_OFFLINE,
    OFFLINE_ACKED,
    DiskNotFound,
    DiskNotUnique,
    DiskOfflineService,
    DiskStatusService,
    HostService,
    OfflineRecordService,
    OverOfflineNum,
    RpcToChunkError,
    StorageDiskManager,
    UpdateRecordDbError,
    ZbsChunk,
)


def test_retry_func():
    raise_exp = Mock(side_effect=Exception("test exp"))
    with TestCase().assertRaises(Exception):
        DiskOfflineService._retry_func(func=raise_exp, retry_num=3, exp=Exception)
    assert raise_exp.call_count == 3


def test_handle_heartbeat():
    context = UdpRequestContext(
        MsgType.HEART_BEAT,
        HeartbeatRequest(
            type_id=MsgType.HEART_BEAT,
            request_id=1,
            timestamp=1,
            node_uuid=b"test_host_uuid",
        ).to_bytes(),
    )
    req = HeartbeatResponse.from_bytes(DiskOfflineService.handle_request(context))
    assert req.type_id == MsgType.HEART_BEAT
    assert req.request_id == 1
    assert req.get_host_uuid() == "test_host_uuid"


@patch.object(DiskOfflineService, "_get_disk_record_by_wwid")
def test_handle_warning(mock_get_disk_record):
    mock_get_disk_record.return_value = None

    context = UdpRequestContext(
        MsgType.WARNING,
        WarningRequest(
            type_id=MsgType.WARNING,
            request_id=1,
            timestamp=1,
            node_uuid=b"test_host_uuid",
            disk_wwid=b"test_disk_wwid",
            msg=b"test_msg",
        ).to_bytes(),
    )
    req = WarningResponse.from_bytes(DiskOfflineService.handle_request(context))
    assert req.type_id == MsgType.WARNING
    assert req.request_id == 1
    assert req.get_host_uuid() == "test_host_uuid"
    assert mock_get_disk_record.called


@patch.object(DiskOfflineService, "_handle_offline")
def test_handle_offline(mock_handle_offline):
    for exp, reason in [
        (DiskNotUnique("test disk not unique"), "disk not unique"),
        (DiskNotFound("test disk not found"), "disk not found"),
        (OverOfflineNum("test over offline num"), "more than one offline"),
        (Exception("test internal error"), "internal error"),
    ]:
        mock_handle_offline.side_effect = exp

        context = UdpRequestContext(
            MsgType.OFFLINE,
            OfflineRequest(
                type_id=MsgType.OFFLINE,
                request_id=1,
                timestamp=1,
                node_uuid=b"test_host_uuid",
                disk_wwid=b"test_wwid",
                reason=(1 << 0) + (1 << 1) + (1 << 2),
            ).to_bytes(),
        )
        req = OfflineResponse.from_bytes(DiskOfflineService.handle_request(context))
        assert req.type_id == MsgType.OFFLINE
        assert req.request_id == 1
        assert req.get_host_uuid() == "test_host_uuid"
        assert req.granted == 0
        assert req.get_disk_wwid() == "test_wwid"
        assert req.get_reason() == reason


@patch.object(DiskOfflineService, "_rpc_to_zbs_chunk")
@patch.object(DiskStatusService, "update_disks_fields_with_query")
@patch.object(OfflineRecordService, "save_record")
@patch.object(OfflineRecordService, "get_record_views")
@patch.object(DiskOfflineService, "_get_disk_record_by_wwid")
def test_handle_offline_impl(
    mock_disk_status_record,
    mock_record_views,
    mock_save_record,
    mock_update_disk_status,
    mock_rpc_zbs,
):
    req = OfflineRequest(
        type_id=MsgType.OFFLINE,
        request_id=1,
        timestamp=1,
        node_uuid=b"test_host_uuid",
        disk_wwid=b"test_disk_wwid",
        reason=(1 << 0) + (1 << 1) + (1 << 2),
    )

    # test disk status record not found
    mock_disk_status_record.side_effect = DiskNotFound("test disk not found")
    with TestCase().assertRaises(DiskNotFound):
        DiskOfflineService._handle_offline(req)

    # test node record not found
    mock_disk_status_record.reset_mock(return_value=True, side_effect=True)
    mock_disk_status_record.return_value = {
        "trace_id": "test_trace_id",
        "disk_path": "test_disk_path",
        "disk_serial": "test_disk_serial",
        "disk_wwid": "test_disk_wwid",
        "host_uuid": "test_host_uuid",
        "data_ip": "test_data_ip",
        "healthy": True,
    }
    mock_record_views.return_value = {
        "version": -2,
        "resource_name": "offline_disks",
        "hosts": [],
    }
    with TestCase().assertRaises(Exception):
        DiskOfflineService._handle_offline(req)

    # test OverOfflineNum on other host
    mock_record_views.return_value = {
        "version": -2,
        "resource_name": "offline_disks",
        "hosts": [
            {
                "host_uuid": "test_host_uuid",
                "data_ip": "test_data_ip",
                "offline_disks": [],
            },
            {
                "host_uuid": "test_host_uuid_other",
                "data_ip": "test_data_ip_other",
                "offline_disks": [
                    {
                        "trace_id": "test_trace_id",
                        "disk_path": "test_disk_path",
                        "disk_serial": "test_disk_serial",
                        "disk_wwid": "test_disk_wwid",
                        "record_by": "test_record_by",
                        "status": OFFLINE_ACKED,
                    },
                ],
            },
        ],
    }
    with TestCase().assertRaises(OverOfflineNum):
        DiskOfflineService._handle_offline(req)

    # test OverOfflineNum on target host
    mock_record_views.return_value = {
        "version": -2,
        "resource_name": "offline_disks",
        "hosts": [
            {
                "host_uuid": "test_host_uuid",
                "data_ip": "test_data_ip",
                "offline_disks": [
                    {
                        "trace_id": "test_trace_id_other",
                        "disk_path": "test_disk_path_other",
                        "disk_serial": "test_disk_serial_other",
                        "disk_wwid": "test_disk_wwid_other",
                        "record_by": "test_record_by_other",
                        "status": OFFLINE_ACKED,
                    },
                ],
            },
            {
                "host_uuid": "test_host_uuid_other",
                "data_ip": "test_data_ip_other",
                "offline_disks": [],
            },
        ],
    }
    with TestCase().assertRaises(OverOfflineNum):
        DiskOfflineService._handle_offline(req)

    # test add_new_record save failed
    mock_record_views.return_value = {
        "version": -2,
        "resource_name": "offline_disks",
        "hosts": [
            {
                "host_uuid": "test_host_uuid",
                "data_ip": "test_data_ip",
                "offline_disks": [],
            },
        ],
    }
    mock_save_record.return_value = False
    with TestCase().assertRaises(UpdateRecordDbError):
        DiskOfflineService._handle_offline(req)

    # test disk offline success
    mock_save_record.return_value = True
    res = DiskOfflineService._handle_offline(req)
    mock_update_disk_status.assert_called_with(
        [
            (
                {"trace_id": "test_trace_id"},
                {
                    "hints.cmd_abort_offline": True,
                    "hints.io_timeout_offline": True,
                    "hints.io_eh_queue_offline": True,
                    "healthy": False,
                },
            )
        ]
    )
    mock_rpc_zbs.assert_called_with(
        {
            "host_uuid": "test_host_uuid",
            "data_ip": "test_data_ip",
            "offline_disks": [
                {
                    "trace_id": "test_trace_id",
                    "disk_path": "test_disk_path",
                    "disk_serial": "test_disk_serial",
                    "disk_wwid": "test_disk_wwid",
                    "record_by": f"{OfflineRecordService.CURRENT_HOST_UUID}@{OfflineRecordService.CURRENT_DATA_IP}",
                    "status": MARK_TO_OFFLINE,
                },
            ],
        }
    )
    assert res.type_id == MsgType.OFFLINE
    assert res.request_id == 1
    assert res.get_host_uuid() == "test_host_uuid"
    assert res.granted == 1
    assert res.get_disk_wwid() == "test_disk_wwid"
    assert res.get_reason() == "looks good to me"


def test_handle_offline_impl_with_detected_at_logic():
    with (
        patch.object(DiskOfflineService, "_get_disk_record_by_wwid") as mock_get_disk_record_by_wwid,
        patch("disk_healthd.disk_offline.services.OfflineRecordService") as MockOfflineRecordService,
        patch("disk_healthd.disk_offline.services.DiskStatusService") as MockDiskStatusService,
        patch.object(DiskOfflineService, "_rpc_to_zbs_chunk") as mock_rpc_to_zbs_chunk,
        patch("disk_healthd.disk_offline.services.datetime") as mock_datetime,
    ):
        mock_req = Mock(
            request_id=1,
            node_uuid=b"mock_uuid1",
            disk_wwid=b"mock_disk_wwid",
        )
        mock_req.get_host_uuid.return_value = "mock_uuid1"
        mock_req.get_disk_wwid.return_value = "mock_disk_wwid"
        mock_req.has_cmd_abort.return_value = True
        mock_req.has_io_timeout.return_value = True
        mock_req.has_io_eh_queue.return_value = True
        mock_get_disk_record_by_wwid.return_value = {
            "trace_id": "trace_id_of_sda",
            "disk_path": "/dev/sda",
            "disk_serial": "serial_of_sda",
            "disk_wwid": "wwid_of_sda",
        }
        MockOfflineRecordService.CURRENT_HOST_UUID = "mock_uuid1"
        MockOfflineRecordService.CURRENT_DATA_IP = "************"
        mock_offline_record_service = MockOfflineRecordService.return_value
        mock_record_views = {
            "version": 1,
            "resource_name": "offline_disks",
            "hosts": [
                {
                    "host_uuid": "mock_uuid1",
                    "data_ip": "************",
                    "offline_disks": [],
                },
                {
                    "host_uuid": "mock_uuid2",
                    "data_ip": "************",
                    "offline_disks": [],
                },
                {
                    "host_uuid": "mock_uuid3",
                    "data_ip": "************",
                    "offline_disks": [],
                },
            ],
        }
        mock_offline_record_service.get_record_views.return_value = mock_record_views
        mock_target_record = {
            "host_uuid": "mock_uuid1",
            "data_ip": "************",
            "offline_disks": [],
        }
        mock_offline_record_service.get_node_record.return_value = mock_target_record
        mock_disk_status_service = MockDiskStatusService.return_value
        mock_disk_status_service.list_disk_status_by_filter.return_value = [
            {
                "trace_id": "trace_id_of_sda",
                "hints": {
                    "cmd_abort_offline": True,
                    "io_timeout_offline": True,
                    "io_eh_queue_offline": True,
                },
                "detected_at": {"cmd_abort_offline": "2024"},
            }
        ]
        mock_rpc_to_zbs_chunk.return_value = True
        mock_now_date = datetime(2024, 10, 24, 12, 12, 12)
        mock_datetime.now.return_value = mock_now_date

        res = DiskOfflineService._handle_offline(mock_req)

        assert res.type_id == MsgType.OFFLINE
        assert res.request_id == 1
        assert res.get_host_uuid() == "mock_uuid1"
        assert res.granted == 1
        assert res.get_disk_wwid() == "mock_disk_wwid"
        assert res.get_reason() == "looks good to me"
        mock_get_disk_record_by_wwid.assert_called_once_with("mock_uuid1", "mock_disk_wwid", raise_error=True)
        mock_offline_record_service.get_record_views.assert_called_once_with()
        mock_offline_record_service.get_node_record.assert_called_once_with(mock_record_views, "mock_uuid1")
        mock_offline_record_service.save_record.assert_called_once_with(mock_record_views)
        mock_disk_status_service.list_disk_status_by_filter.assert_called_once_with(id_filter={"trace_id": "trace_id_of_sda"})
        mock_disk_status_service.update_disks_fields_with_query.assert_has_calls([
            call([
                (
                    {"trace_id": "trace_id_of_sda"},
                    {
                        "hints.cmd_abort_offline": True,
                        "hints.io_timeout_offline": True,
                        "hints.io_eh_queue_offline": True,
                        "healthy": False,
                    },
                )
            ]),
            call([
                (
                    {"trace_id": "trace_id_of_sda"},
                    {
                        "detected_at.io_timeout_offline": mock_now_date,
                        "detected_at.io_eh_queue_offline": mock_now_date,
                    },
                )
            ]),
        ])
        mock_rpc_to_zbs_chunk.assert_called_once_with(mock_target_record)


@patch.object(StorageDiskManager, "is_chunk_part_belong_to_disk")
@patch.object(ZbsChunk, "partition_set_unhealthy")
@patch.object(ZbsChunk, "cache_set_unhealthy")
@patch.object(ZbsChunk, "journal_set_unhealthy")
@patch.object(StorageDiskManager, "get_disk_or_partition_info")
@patch.object(HostService, "get_host_partial_info")
def test_rpc_to_zbs_chunk(
    mock_host_info,
    mock_chunk_parts,
    mock_journal_unhealthy,
    mock_cache_unhealthy,
    mock_part_unhealthy,
    mock_belong_to_disk,
):
    target_record = {
        "host_uuid": "test_host_uuid",
        "data_ip": "test_data_ip",
        "offline_disks": [
            {
                "trace_id": "test_trace_id",
                "disk_path": "test_disk_path",
                "disk_serial": "test_disk_serial",
                "disk_wwid": "test_disk_wwid",
                "record_by": f"{OfflineRecordService.CURRENT_HOST_UUID}@{OfflineRecordService.CURRENT_DATA_IP}",
                "status": MARK_TO_OFFLINE,
            },
        ],
    }

    # host info not found
    mock_host_info.return_value = None
    with TestCase().assertRaises(Exception):
        DiskOfflineService._rpc_to_zbs_chunk(target_record)

    # target disk not found
    mock_host_info.return_value = {"disks": []}
    with TestCase().assertRaises(Exception):
        DiskOfflineService._rpc_to_zbs_chunk(target_record)

    # rpc raise error
    mock_host_info.return_value = {
        "disks": [
            {
                "path": "test_disk_path",
                "serial": "test_disk_serial",
                "wwid": "test_disk_wwid",
            }
        ]
    }
    mock_chunk_parts.side_effect = Exception("test get chunk parts error")
    with TestCase().assertRaises(RpcToChunkError):
        DiskOfflineService._rpc_to_zbs_chunk(target_record)

    mock_chunk_parts.reset_mock(return_value=True, side_effect=True)
    mock_belong_to_disk.return_value = True
    mock_chunk_parts.return_value = {
        "test_chunk_part_uuid": {
            "usage": "partition",
            "num_io_errors": 1,
            "num_slow_io": 1,
            "num_checksum_errors": 1,
            "path": "",
            "device_id": "",
            "uuid": "test_chunk_part_uuid",
            "part_uuid": "test_part_uuid",
            "errflags": 4,
            "warnflags": 1,
            "used_size": 1,
            "status": 0,
        },
        "test_chunk_cache_uuid": {
            "usage": "cache",
            "num_io_errors": 1,
            "num_slow_io": 1,
            "path": "",
            "device_id": "",
            "uuid": "test_chunk_cache_uuid",
            "part_uuid": "test_part_uuid",
            "errflags": 4,
            "warnflags": 1,
            "num_used": 0,
            "used_size": 1,
            "status": 0,
        },
        "test_chunk_journal_uuid": {
            "usage": "journal",
            "num_io_errors": 1,
            "num_slow_io": 1,
            "path": "",
            "device_id": "",
            "uuid": "test_chunk_journal_uuid",
            "part_uuid": "test_part_uuid",
            "errflags": 4,
            "warnflags": 1,
        },
    }
    assert DiskOfflineService._rpc_to_zbs_chunk(target_record)
    assert mock_part_unhealthy.called
    assert mock_cache_unhealthy.called
    assert mock_journal_unhealthy.called


@patch.object(DiskOfflineService, "_mark_offline_acked")
@patch.object(DiskOfflineService, "_get_disk_record_by_wwid")
def test_handle_offline_ack(mock_get_disk_record, mock_mark_offline_acked):
    mock_get_disk_record.return_value = None

    context = UdpRequestContext(
        MsgType.OFFLINE_ACK,
        OfflineAckRequest(
            type_id=MsgType.OFFLINE_ACK,
            request_id=1,
            timestamp=1,
            node_uuid=b"test_host_uuid",
            disk_wwid=b"test_disk_wwid",
        ).to_bytes(),
    )
    req = OfflineAckResponse.from_bytes(DiskOfflineService.handle_request(context))
    assert req.type_id == MsgType.OFFLINE_ACK
    assert req.request_id == 1
    assert req.get_host_uuid() == "test_host_uuid"
    assert mock_get_disk_record.called
    assert mock_mark_offline_acked.called


@patch.object(OfflineRecordService, "save_record")
@patch.object(OfflineRecordService, "get_record_views")
def test_mark_offline_acked(mock_record_views, mock_save_record):
    mock_record_views.return_value = {
        "version": -2,
        "resource_name": "offline_disks",
        "hosts": [
            {
                "host_uuid": "test_host_uuid",
                "data_ip": "test_data_ip",
                "offline_disks": [
                    {
                        "trace_id": "test_trace_id",
                        "disk_path": "test_disk_path",
                        "disk_serial": "test_disk_serial",
                        "disk_wwid": "test_disk_wwid",
                        "record_by": "test_record_by",
                        "status": MARK_TO_OFFLINE,
                    }
                ],
            },
        ],
    }
    res = DiskOfflineService._mark_offline_acked("test_host_uuid", "not_found_disk_wwid")
    assert res is False

    mock_save_record.return_value = False
    with TestCase().assertRaises(UpdateRecordDbError):
        DiskOfflineService._mark_offline_acked("test_host_uuid", "test_disk_wwid")

    mock_save_record.return_value = True
    res = DiskOfflineService._mark_offline_acked("test_host_uuid", "test_disk_wwid")
    assert res is True


@patch.object(DiskStatusService, "list_disk_status_by_filter")
def test_get_disk_record_by_wwid(mock_list_disk_status):
    mock_list_disk_status.return_value = []
    res = DiskOfflineService._get_disk_record_by_wwid("test_host_uuid", "test_disk_wwid")
    assert res is None

    mock_list_disk_status.return_value = [
        {
            "trace_id": "test_trace_id",
            "disk_path": "test_disk_path",
            "disk_serial": "test_disk_serial",
            "disk_wwid": "test_disk_wwid",
            "host_uuid": "test_host_uuid",
            "data_ip": "test_data_ip",
            "healthy": True,
        },
        {
            "trace_id": "test_trace_id",
            "disk_path": "test_disk_path",
            "disk_serial": "test_disk_serial",
            "disk_wwid": "test_disk_wwid",
            "host_uuid": "test_host_uuid",
            "data_ip": "test_data_ip",
            "healthy": False,
        },
    ]
    res = DiskOfflineService._get_disk_record_by_wwid("test_host_uuid", "test_disk_wwid")
    assert res == {
        "trace_id": "test_trace_id",
        "disk_path": "test_disk_path",
        "disk_serial": "test_disk_serial",
        "disk_wwid": "test_disk_wwid",
        "host_uuid": "test_host_uuid",
        "data_ip": "test_data_ip",
        "healthy": True,
    }

    with TestCase().assertRaises(DiskNotUnique):
        DiskOfflineService._get_disk_record_by_wwid(
            "test_host_uuid",
            "test_disk_wwid",
            raise_error=True,
        )
