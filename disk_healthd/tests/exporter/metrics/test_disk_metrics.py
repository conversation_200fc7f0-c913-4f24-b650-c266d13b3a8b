# Copyright (c) 2013-2023, SMARTX
# All rights reserved.

from unittest.mock import Mock, patch

from disk_healthd.tests.utils import init_config


def test_check_lsm_isolate_support():
    init_config()

    from disk_healthd.exporter.metrics import disk

    with (
        patch.object(disk, "is_elf_only") as mock_is_elf_only,
        patch("zbs.chunk.client.ZbsChunk") as MockZbsChunk,
    ):
        from zbs.proto import chunk_pb2 as chunk

        # case 1: elf only
        mock_is_elf_only.return_value = True

        assert not disk.DiskStatusMetric([])._check_lsm_isolate_support()

        mock_is_elf_only.assert_called_once_with()

        # case 2: not elf only & lsm support isolate
        mock_is_elf_only.reset_mock()

        mock_is_elf_only.return_value = False
        mock_zbs_chunk = MockZbsChunk.return_value
        mock_zbs_chunk.summary_info.return_value = Mock(instances_response=[Mock(lsm_capability=chunk.LSM_CAP_CACHE_ISOLATE)])

        assert disk.DiskStatusMetric([])._check_lsm_isolate_support()
        mock_is_elf_only.assert_called_once_with()
        mock_zbs_chunk.summary_info.assert_called_once_with(keep_v2_message_format=True)
        MockZbsChunk.assert_called_once_with()

        # case 3: not elf only & lsm not support isolate
        mock_is_elf_only.reset_mock()
        MockZbsChunk.reset_mock()

        mock_is_elf_only.return_value = False
        mock_zbs_chunk = MockZbsChunk.return_value
        mock_zbs_chunk.summary_info.return_value = Mock(instances_response=[Mock(lsm_capability=0)])

        assert not disk.DiskStatusMetric([])._check_lsm_isolate_support()
        mock_is_elf_only.assert_called_once_with()
        mock_zbs_chunk.summary_info.assert_called_once_with(keep_v2_message_format=True)
        MockZbsChunk.assert_called_once_with()

        # case 4: not elf only & has exception
        mock_is_elf_only.reset_mock()
        MockZbsChunk.reset_mock()

        mock_is_elf_only.return_value = False
        mock_zbs_chunk = MockZbsChunk.return_value
        mock_zbs_chunk.summary_info.side_effect = Exception("error")

        assert disk.DiskStatusMetric([])._check_lsm_isolate_support() is None
        mock_is_elf_only.assert_called_once_with()
        mock_zbs_chunk.summary_info.assert_called_once_with(keep_v2_message_format=True)
        MockZbsChunk.assert_called_once_with()


def test_contains_last_raid_dev():
    init_config()

    from disk_healthd.exporter.metrics import disk

    with (
        patch.object(disk.Raid, "list_active_devices") as mick_list_active_devs,
        patch.object(disk.Raid, "raids") as mock_raids,
    ):
        mock_raids.return_value = ["md127"]
        mick_list_active_devs.return_value = ["sdb1", "sdc1"]
        res = disk.DiskStatusMetric([])._contains_last_raid_dev({"partitions": [{"name": "sdb1"}, {"name": "sdb2"}]})
        assert res is False

        mick_list_active_devs.return_value = ["sdb1"]
        res = disk.DiskStatusMetric([])._contains_last_raid_dev({"partitions": [{"name": "sdb1"}, {"name": "sdb2"}]})
        assert res is True

        mick_list_active_devs.return_value = ["sdc1"]
        res = disk.DiskStatusMetric([])._contains_last_raid_dev({"partitions": [{"name": "sdb1"}, {"name": "sdb2"}]})
        assert res is False


def test_disk_status_metrics():
    init_config()

    from disk_healthd.exporter.metrics import disk

    with (
        patch.object(disk.DiskStatusMetric, "_contains_last_raid_dev") as mock_contains_last_raid_dev,
        patch.object(disk.DiskStatusMetric, "_check_lsm_isolate_support") as mock_check_lsm_isolate_support,
        patch.object(disk.Disk, "disk_names") as mock_disk_names,
    ):
        disks = [
            {  # unhealthy_disk_with_last_smtx_system
                "name": "sda",
                "path": "/dev/sda",
                "serial": "2604fb72-21ac-4781-b87e-d83e0d22f571",
                "type": "HDD",
                "is_rotational": True,
                "function": "smtx_system",
                "hints": {
                    "chunk_errflag": True,
                    "chunk_io_error": False,
                    "chunk_checksum_error": False,
                    "chunk_warnflag": False,
                    "iostat_latency": False,
                    "smart_check": False,
                    "faulty_in_raid": False,
                },
                "is_healthy_in_chunk": True,
                "exist_errflags": False,
                "mount_status": "mounted",
                "num_io_errors": 0,
                "partitions": [
                    {"name": "sda3", "path": "/dev/sda3", "usage": "journal"},
                    {"name": "sda2", "path": "/dev/sda2", "usage": "metad"},
                    {"name": "sda1", "path": "/dev/sda1", "usage": "system"},
                    {"name": None, "size": 10738466816, "usage": "unparted"},
                ],
            },
            {  # unhealthy_in_isolate
                "name": "sdb",
                "path": "/dev/sdb",
                "serial": "2604fb72-21ac-4781-b87e-d83e0d22f572",
                "type": "HDD",
                "is_rotational": True,
                "function": "data",
                "hints": {
                    "chunk_errflag": True,
                    "chunk_io_error": False,
                    "chunk_checksum_error": False,
                    "chunk_warnflag": False,
                    "iostat_latency": False,
                    "smart_check": False,
                    "faulty_in_raid": False,
                },
                "is_healthy_in_chunk": False,
                "num_io_errors": 0,
                "exist_errflags": True,
                "mount_status": "mounted",
                "partitions": [
                    {"name": "sdb4", "path": "/dev/sdb4", "usage": "partition"},
                    {"name": "sdb3", "path": "/dev/sdb3", "usage": "journal"},
                    {"name": None, "size": 10738466816, "usage": "unparted"},
                ],
            },
            {  # sub_healthy_in_isolate
                "name": "sdc",
                "path": "/dev/sdc",
                "serial": "2604fb72-21ac-4781-b87e-d83e0d22f573",
                "type": "HDD",
                "is_rotational": True,
                "function": "data",
                "hints": {
                    "chunk_errflag": False,
                    "chunk_io_error": False,
                    "chunk_checksum_error": False,
                    "chunk_warnflag": False,
                    "iostat_latency": True,
                    "smart_check": False,
                    "faulty_in_raid": False,
                },
                "is_healthy_in_chunk": True,
                "mount_status": "mounted",
                "num_io_errors": 0,
                "partitions": [
                    {"name": "sdc4", "path": "/dev/sdc4", "usage": "partition"},
                    {"name": "sdc3", "path": "/dev/sdc3", "usage": "journal"},
                    {"name": None, "size": 10738466816, "usage": "unparted"},
                ],
            },
            {  # sub_healthy_in_isolate
                "name": "sdcc",
                "path": "/dev/sdcc",
                "serial": "2604fb72-21ac-4781-b87e-d83e0d22f574",
                "type": "SSD",
                "is_rotational": False,
                "function": "data",
                "hints": {
                    "chunk_errflag": False,
                    "chunk_io_error": False,
                    "chunk_checksum_error": False,
                    "chunk_warnflag": False,
                    "iostat_latency": True,
                    "smart_check": False,
                    "faulty_in_raid": False,
                },
                "extra_info": {
                    "rw_await": 3001,
                    "iops": 1000,
                    "sector_ps": 102000,
                },
                "is_healthy_in_chunk": True,
                "mount_status": "mounted",
                "num_io_errors": 0,
                "partitions": [
                    {"name": "sdcc4", "path": "/dev/sdcc4", "usage": "partition"},
                    {"name": "sdcc3", "path": "/dev/sdcc3", "usage": "journal"},
                    {"name": None, "size": 10738466816, "usage": "unparted"},
                ],
            },
            {  # sub_healthy_in_isolate
                "name": "sdccc",
                "path": "/dev/sdccc",
                "serial": "2604fb72-21ac-4781-b87e-d83e0d22f575",
                "type": "SSD",
                "is_rotational": False,
                "function": "data",
                "hints": {
                    "chunk_errflag": False,
                    "chunk_io_error": False,
                    "chunk_checksum_error": False,
                    "chunk_warnflag": False,
                    "iostat_latency": True,
                    "smart_check": False,
                    "faulty_in_raid": False,
                },
                "extra_info": {
                    "rw_await": 600,
                    "iops": 1000,
                    "sector_ps": 102000,
                },
                "is_healthy_in_chunk": True,
                "mount_status": "mounted",
                "num_io_errors": 0,
                "partitions": [
                    {"name": "sdcc4", "path": "/dev/sdccc4", "usage": "partition"},
                    {"name": "sdcc3", "path": "/dev/sdccc3", "usage": "journal"},
                    {"name": None, "size": 10738466816, "usage": "unparted"},
                ],
            },
            {  # sub_healthy_in_isolate
                "name": "sdcccc",
                "path": "/dev/sdcccc",
                "serial": "2604fb72-21ac-4781-b87e-d83e0d22f576",
                "type": "SSD",
                "is_rotational": False,
                "function": "data",
                "hints": {
                    "chunk_errflag": False,
                    "chunk_io_error": False,
                    "chunk_checksum_error": False,
                    "chunk_warnflag": False,
                    "iostat_latency": True,
                    "smart_check": False,
                    "faulty_in_raid": False,
                },
                "extra_info": {
                    "rw_await": 3000,
                    "iops": 3000,
                    "sector_ps": 202000,
                },
                "is_healthy_in_chunk": True,
                "mount_status": "mounted",
                "num_io_errors": 0,
                "partitions": [
                    {"name": "sdccc4", "path": "/dev/sdcccc4", "usage": "partition"},
                    {"name": "sdccc3", "path": "/dev/sdcccc3", "usage": "journal"},
                    {"name": None, "size": 10738466816, "usage": "unparted"},
                ],
            },
            {  # unhealthy_disk_with_left_partition
                "name": "sdd",
                "path": "/dev/sdd",
                "serial": "2604fb72-21ac-4781-b87e-d83e0d22f574",
                "type": "HDD",
                "is_rotational": True,
                "function": "data",
                "hints": {
                    "chunk_errflag": True,
                    "chunk_io_error": False,
                    "chunk_checksum_error": False,
                    "chunk_warnflag": False,
                    "iostat_latency": False,
                    "smart_check": False,
                    "faulty_in_raid": False,
                },
                "is_healthy_in_chunk": True,
                "mount_status": "mounted",
                "num_io_errors": 0,
                "partitions": [
                    {"name": "sdd3", "path": "/dev/sdd3", "usage": "journal"},
                    {"name": None, "size": 10738466816, "usage": "unparted"},
                ],
            },
            {  # sub_healthy_without_isolate
                "name": "sde",
                "path": "/dev/sde",
                "serial": "2604fb72-21ac-4781-b87e-d83e0d22f575",
                "type": "SSD",
                "is_rotational": False,
                "function": "data",
                "hints": {
                    "chunk_errflag": False,
                    "chunk_io_error": True,
                    "chunk_checksum_error": True,
                    "chunk_warnflag": False,
                    "iostat_latency": False,
                    "smart_check": False,
                    "faulty_in_raid": False,
                },
                "is_healthy_in_chunk": True,
                "mount_status": "mounted",
                "num_io_errors": 0,
                "partitions": [
                    {"name": "sde4", "path": "/dev/sde4", "usage": "partition"},
                    {"name": "sde3", "path": "/dev/sde3", "usage": "journal"},
                ],
            },
            {  # smart_test_failed_disk
                "name": "sdf",
                "path": "/dev/sdf",
                "serial": "2604fb72-21ac-4781-b87e-d83e0d22f576",
                "type": "HDD",
                "is_rotational": True,
                "function": "data",
                "hints": {
                    "chunk_errflag": False,
                    "chunk_io_error": False,
                    "chunk_checksum_error": False,
                    "chunk_warnflag": False,
                    "iostat_latency": False,
                    "smart_check": True,
                    "faulty_in_raid": False,
                },
                "is_healthy_in_chunk": True,
                "mount_status": "mounted",
                "num_io_errors": 0,
                "partitions": [
                    {"name": "sdf4", "path": "/dev/sdf4", "usage": "partition"},
                    {"name": "sdf3", "path": "/dev/sdf3", "usage": "journal"},
                    {"name": None, "size": 10738466816, "usage": "unparted"},
                ],
            },
            {  # unhealthy_disk_not_in_use
                "name": "sdg",
                "path": "/dev/sdg",
                "serial": "2604fb72-21ac-4781-b87e-d83e0d22f577",
                "type": "HDD",
                "is_rotational": True,
                "hints": {
                    "chunk_errflag": True,
                    "chunk_io_error": False,
                    "chunk_checksum_error": False,
                    "chunk_warnflag": False,
                    "iostat_latency": False,
                    "smart_check": False,
                    "faulty_in_raid": True,
                },
                "extra_info": {"celsius_temperature": 25, "lifespan": 100},
                "mount_status": "unmounted",
                "partitions": [{"name": None, "size": 10738466816, "usage": "unparted"}],
            },
            {  # io_block_offline
                "name": "sdh",
                "path": "/dev/sdh",
                "serial": "2604fb72-21ac-4781-b87e-d83e0d22f578",
                "type": "HDD",
                "is_rotational": True,
                "hints": {
                    "chunk_errflag": False,
                    "chunk_io_error": False,
                    "chunk_checksum_error": False,
                    "chunk_warnflag": False,
                    "iostat_latency": False,
                    "smart_check": False,
                    "faulty_in_raid": False,
                    "io_timeout_offline": True,
                    "cmd_abort_offline": True,
                    "io_eh_queue_offline": True,
                },
                "extra_info": {"celsius_temperature": 25, "lifespan": 100},
                "is_healthy_in_chunk": True,
                "mount_status": "mounted",
                "partitions": [{"name": None, "size": 10738466816, "usage": "unparted"}],
            },
            {  # sub_healthy_without_isolate by reallocated_sectors_count_overflow
                "name": "sdi",
                "path": "/dev/sdi",
                "serial": "2604fb72-21ac-4781-b87e-d83e0d22f575",
                "type": "SSD",
                "is_rotational": False,
                "function": "data",
                "hints": {
                    "chunk_errflag": False,
                    "chunk_io_error": False,
                    "chunk_checksum_error": False,
                    "chunk_warnflag": False,
                    "iostat_latency": False,
                    "smart_check": False,
                    "faulty_in_raid": False,
                    "reallocated_sectors_count_overflow": True,
                },
                "is_healthy_in_chunk": True,
                "mount_status": "mounted",
                "num_io_errors": 0,
                "partitions": [
                    {"name": "sdi4", "path": "/dev/sdi4", "usage": "partition"},
                    {"name": "sdi3", "path": "/dev/sdi3", "usage": "journal"},
                ],
            },
            {  # unhealthy_disk_with_last_smtx_system by reallocated_sectors_count_overflow
                "name": "sdj",
                "path": "/dev/sdj",
                "serial": "2604fb72-21ac-4781-b87e-d83e0d22f571",
                "type": "HDD",
                "is_rotational": True,
                "function": "smtx_system",
                "hints": {
                    "chunk_errflag": False,
                    "chunk_io_error": False,
                    "chunk_checksum_error": False,
                    "chunk_warnflag": False,
                    "iostat_latency": False,
                    "smart_check": False,
                    "faulty_in_raid": False,
                    "reallocated_sectors_count_overflow": True,
                },
                "is_healthy_in_chunk": True,
                "exist_errflags": False,
                "mount_status": "mounted",
                "num_io_errors": 0,
                "partitions": [
                    {"name": "sdj3", "path": "/dev/sdj3", "usage": "journal"},
                    {"name": "sdj2", "path": "/dev/sdj2", "usage": "metad"},
                    {"name": "sdj1", "path": "/dev/sdj1", "usage": "system"},
                    {"name": None, "size": 10738466816, "usage": "unparted"},
                ],
            },
        ]

        mock_disk_names.return_value = [item["name"] for item in disks]
        mock_check_lsm_isolate_support.return_value = True
        mock_contains_last_raid_dev.side_effect = lambda x: x.get("function") == "smtx_system"

        disk_metrics = disk.DiskStatusMetric(disks)

        assert len(disk_metrics.unhealthy_disk_with_last_smtx_system) == 2
        assert disk_metrics.unhealthy_disk_with_last_smtx_system[0]["name"] == "sda"
        assert disk_metrics.unhealthy_disk_with_last_smtx_system[1]["name"] == "sdj"

        assert len(disk_metrics.unhealthy_in_isolate) == 1
        assert disk_metrics.unhealthy_in_isolate[0]["name"] == "sdb"

        assert len(disk_metrics.sub_healthy_in_isolate) == 2
        assert disk_metrics.sub_healthy_in_isolate[0]["name"] == "sdc"
        assert disk_metrics.sub_healthy_in_isolate[1]["name"] == "sdcc"

        assert len(disk_metrics.unhealthy_disk_with_left_partition) == 1
        assert disk_metrics.unhealthy_disk_with_left_partition[0]["name"] == "sdd"

        assert len(disk_metrics.sub_healthy_without_isolate) == 4
        assert disk_metrics.sub_healthy_without_isolate[0]["name"] == "sdccc"
        assert disk_metrics.sub_healthy_without_isolate[1]["name"] == "sdcccc"
        assert disk_metrics.sub_healthy_without_isolate[2]["name"] == "sde"
        assert disk_metrics.sub_healthy_without_isolate[3]["name"] == "sdi"

        assert len(disk_metrics.smart_test_failed_disk) == 1
        assert disk_metrics.smart_test_failed_disk[0]["name"] == "sdf"

        assert len(disk_metrics.unhealthy_disk_not_in_use) == 1
        assert disk_metrics.unhealthy_disk_not_in_use[0]["name"] == "sdg"

        assert len(disk_metrics.io_block_offline) == 1
        assert disk_metrics.io_block_offline[0]["name"] == "sdh"
