# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
from unittest.mock import patch
import prometheus_client
from prometheus_client import CollectorRegistry


@patch.object(prometheus_client, "generate_latest", return_value="")
@patch.object(CollectorRegistry, "register")
@patch("common.exporter.cache_utils.CachedCollectorWrapper")
def test_disk_healthd_exporter_apis(*args):
    from disk_healthd.rest_server import flask_app

    routes = [
        "disk_stats",
        "disk_status",
        "disk_event",
        "hardware_raid",
    ]
    client = flask_app.test_client()
    for route in routes:
        res = client.get(f"/api/v2/exporter/disk_healthd/{route}")
        assert res.status_code == 200
