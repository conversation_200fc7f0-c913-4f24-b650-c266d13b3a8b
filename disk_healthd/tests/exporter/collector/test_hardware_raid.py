# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
from unittest.mock import patch

from disk_healthd.exporter.collector.hardware_raid import HardwareRaidCollector
from disk_healthd.exporter.metrics.hardware_raid import HardwareRaidService


@patch.object(HardwareRaidService, "list_vd_from_db_by_host_uuid")
def test_hardware_raid(mock_list_vds):
    mock_list_vds.return_value = [
        {
            "device_id": "1cbd5b26-19de-5535-b87d-bb286355842b",
            "cmd_tool": "mnv_cli",
            "hints": {"vd_status_error": False, "pd_smart_error": False, "pd_num_insufficient": False, "write_cache_policy_incorrect": False},
            "host_uuid": "4f339f6a-11e9-11ef-b511-52540011397d",
            "name": "VD_TEST",
            "os_drive_path": "/dev/nvme0n1",
            "pds": [
                {
                    "pd_id": "0",
                    "model": "SAMSUNG MZ1L2960HCJR-00A07",
                    "serial": "S665NS0W902277",
                    "firmware": "GDC7502Q",
                    "type": "NVMe",
                    "smart_error": False,
                    "lifespan": 100,
                },
                {
                    "pd_id": "1",
                    "model": "SAMSUNG MZ1L2960HCJR-00A07",
                    "serial": "S665NS0W902274",
                    "firmware": "GDC7502Q",
                    "type": "NVMe",
                    "smart_error": False,
                    "lifespan": 100,
                },
            ],
            "raid_mode": "RAID1",
            "raw_status": "Functional",
            "vd_id": "0",
            "write_cache_policy": None,
        }
    ]
    res = HardwareRaidCollector().collect()
    assert len(res) == 5
