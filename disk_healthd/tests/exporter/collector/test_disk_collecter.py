# Copyright (c) 2013-2023, SMARTX
# All rights reserved.
from unittest.mock import patch, Mock

from disk_healthd.tests.utils import init_config


def test_disk_collector():
    init_config()

    from disk_healthd.exporter.collector import disk

    with (
        patch.object(disk, "DiskStatusMetric") as mock_disk_metrics,
        patch.object(disk.DiskStatusService, "attach_disk_status") as mock_disk_status,
        patch.object(disk.HostService, "get_current_host_info") as mock_host_info,
    ):
        mock_host_info.return_value = None
        res = disk.DiskStatusCollector().collect()
        assert not res

        mock_host_info.return_value = {
            "disks": [
                {"name": "sdi", "serial": "test_sdi", "extra_info": {"lifespan": 100, "celsius_temperature": 30}},
                {"name": "sdj", "serial": "test_sdj", "extra_info": {"lifespan": 100, "celsius_temperature": 30}},
            ],
        }
        mock_disk_metrics.return_value = Mock(
            io_block_offline=[{"name": "sdk", "serial": "test_sdk"}],
            unhealthy_in_isolate=[{"name": "sda", "serial": "test_sda"}],
            sub_healthy_in_isolate=[{"name": "sdb", "serial": "test_sdb"}],
            sub_healthy_without_isolate=[{"name": "sdc", "serial": "test_sdc"}],
            unhealthy_disk_with_left_partition=[{"name": "sdd", "serial": "test_sdd"}],
            unhealthy_disk_with_last_smtx_system=[{"name": "sde", "serial": "test_sde"}],
            smart_test_failed_disk=[{"name": "sdf", "serial": "test_sdf"}],
            unhealthy_disk_not_in_use=[{"name": "sdg", "serial": "test_sdg"}],
        )
        mock_disk_status.return_value = None
        res = disk.DiskStatusCollector().collect()
        assert len(res) > 7
