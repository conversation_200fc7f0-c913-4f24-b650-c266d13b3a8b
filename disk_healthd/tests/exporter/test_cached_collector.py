# Copyright (c) 2013-2024, SMARTX
# All rights reserved.

from unittest.mock import patch


@patch("common.exporter.cache_utils.CachedCollectorWrapper")
def test_cached_collector(mock_cache):
    from disk_healthd.exporter import cached_collector

    with patch.object(cached_collector, "is_witness") as mock_is_witness:
        mock_is_witness.return_value = True
        assert cached_collector.disk_collector() is None
        assert cached_collector.disk_status_collector() is None
        assert cached_collector.hardware_raid_collector() is None

        mock_is_witness.return_value = False
        assert cached_collector.disk_collector() is not None
        assert cached_collector.disk_status_collector() is not None
        assert cached_collector.hardware_raid_collector() is not None
