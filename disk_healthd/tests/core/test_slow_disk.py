# Copyright (c) 2013-2021, SMARTX
# All rights reserved.

from copy import deepcopy
from unittest.mock import patch

from disk_healthd.core.slow_disk import CAP_EXCLUDED, Disk, IGNORED, OfflineDisk<PERSON><PERSON><PERSON>, SlowDisk<PERSON>elper, StorageManager

# slow disk


def test_slow_disk_get_review_record():
    s = SlowDiskHelper()
    s.ensure_schema_init()
    record_views, current_record = s._get_review_record()
    assert record_views is not None
    assert current_record is not None


def test_slow_disk_remove_absent_disks():
    with (
        patch.object(SlowDiskHelper, "_get_review_record") as mock_get_review_record,
        patch.object(Disk, "is_device_state_offline") as mock_is_disk_offline,
    ):
        record_in_db = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": "*******",
                    "host_uuid": "test_host_uuid",
                    "slow_disks": [
                        {"disk_name": "sda", "disk_serial": "abc123", "status": IGNORED},
                        {"disk_name": "sdb", "disk_serial": "abc123", "status": CAP_EXCLUDED},
                    ],
                }
            ],
        }
        record_views = deepcopy(record_in_db)
        current_record = record_views["hosts"][0]
        mock_get_review_record.return_value = (record_views, current_record)
        mock_is_disk_offline.return_value = False
        s = SlowDiskHelper()
        assert s._remove_absent_disks() is False

        record_views = deepcopy(record_in_db)
        current_record = record_views["hosts"][0]
        mock_get_review_record.return_value = (record_views, current_record)
        mock_is_disk_offline.return_value = True
        s = SlowDiskHelper()
        assert s._remove_absent_disks() is True

    with patch.object(SlowDiskHelper, "_remove_absent_disks") as mock_remove_absent_disks:
        mock_remove_absent_disks.return_value = True
        s = SlowDiskHelper()
        s.remove_absent_disks()


def test_add_slow_disks():
    with patch.object(SlowDiskHelper, "_get_review_record") as mock_get_review_record:
        record_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": "*******",
                    "host_uuid": "test_host_uuid",
                    "slow_disks": [{"disk_name": "sda", "disk_serial": "abc123", "status": IGNORED}],
                }
            ],
        }
        current_record = record_views["hosts"][0]
        mock_get_review_record.return_value = (record_views, current_record)
        s = SlowDiskHelper()
        new_slow_disks = [
            {"name": "sda", "serial": "abc123", "is_rotational": True},
            {"name": "sdb", "serial": "abc456", "is_rotational": False},
        ]
        assert s._add_slow_disks(new_slow_disks) is False

    with patch.object(SlowDiskHelper, "_add_slow_disks") as mock_add_slow_disks:
        mock_add_slow_disks.return_value = True
        s = SlowDiskHelper()
        s.add_slow_disks([])


# offline disk


def test_offline_disk_get_review_record():
    s = OfflineDiskHelper()
    s.ensure_schema_init()
    record_views, current_record = s._get_host_review_record()
    assert record_views is not None
    assert current_record is not None


def test_offline_disk_remove_absent_disks():
    with (
        patch.object(OfflineDiskHelper, "_get_host_review_record") as mock_get_review_record,
        patch.object(StorageManager, "is_exist_data_recover") as mock_data_recover,
        patch.object(StorageManager, "is_exist_data_dead") as mock_data_dead,
        patch.object(Disk, "is_device_state_offline") as mock_is_disk_offline,
    ):
        record_in_db = {
            "resource_name": "offline_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": "*******",
                    "host_uuid": "test_host_uuid",
                    "offline_disks": [
                        {
                            "trace_id": "test_trace_id",
                            "disk_path": "/dev/sdx",
                            "disk_serial": "test_disk_serial",
                            "disk_wwid": "test_disk_wwid",
                            "record_by": f"test_current_host_uuid@current_*******",
                            "status": "MARK_TO_OFFLINE",
                        },
                        {
                            "trace_id": "test_trace_id",
                            "disk_path": "/dev/sdy",
                            "disk_serial": "test_disk_serial",
                            "disk_wwid": "test_disk_wwid",
                            "record_by": f"test_current_host_uuid@current_*******",
                            "status": "MARK_TO_OFFLINE",
                        },
                    ],
                }
            ],
        }

        record_views = deepcopy(record_in_db)
        current_record = record_views["hosts"][0]
        mock_get_review_record.return_value = (record_views, current_record)
        mock_data_recover.return_value = False
        mock_data_dead.return_value = False
        mock_is_disk_offline.return_value = False
        s = OfflineDiskHelper()
        assert s._remove_absent_disks() is False

        # skip if disk is offline
        record_views = deepcopy(record_in_db)
        current_record = record_views["hosts"][0]
        mock_get_review_record.return_value = (record_views, current_record)
        mock_is_disk_offline.return_value = True
        s = OfflineDiskHelper()
        assert s._remove_absent_disks() is True

        # skip if storage unhealthy
        record_views = deepcopy(record_in_db)
        current_record = record_views["hosts"][0]
        mock_get_review_record.return_value = (record_views, current_record)
        mock_data_recover.return_value = True
        mock_data_dead.return_value = False
        mock_is_disk_offline.return_value = False
        s = OfflineDiskHelper()
        assert s._remove_absent_disks() is True

    with patch.object(OfflineDiskHelper, "_remove_absent_disks") as mock_remove_absent_disks:
        mock_remove_absent_disks.return_value = True
        s = OfflineDiskHelper()
        s.remove_absent_disks()
