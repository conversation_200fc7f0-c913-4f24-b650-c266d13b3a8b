# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
from unittest.mock import Mock, patch

from inotify_simple import Event, flags

from disk_healthd.core import inotify_update
from disk_healthd.core.inotify_update import DiskEventWatcher


@patch.object(inotify_update.OfflineDiskHelper, "remove_absent_disks")
@patch.object(inotify_update.SlowDiskHelper, "remove_absent_disks")
@patch.object(DiskEventWatcher, "_touch_watch_file")
@patch.object(inotify_update.disk_map, "check_disks_changed")
@patch.object(inotify_update.INotify, "read")
def test_inotify_update(
    mock_inotify_read,
    mock_check_disks_changed,
    mock_touch_watch_file,
    mock_slow_remove,
    mock_offline_remove,
):
    mock_touch_watch_file.return_value = True

    callback_fn = Mock(return_value=True)
    watcher = DiskEventWatcher()
    watcher.add_callback(watcher.DISK_ADD, callback_fn)

    mock_event = Event(0, flags.MODIFY, "", "test")
    mock_inotify_read.return_value = [mock_event]
    watcher._wait_and_handle_event()
    assert callback_fn.call_count == 1

    mock_inotify_read.return_value = []
    mock_check_disks_changed.return_value = (True, True)
    watcher._wait_and_handle_event()
    assert callback_fn.call_count == 2
    assert not mock_slow_remove.called
    assert not mock_offline_remove.called

    mock_check_disks_changed.return_value = (False, False)
    watcher._wait_and_handle_event()
    assert callback_fn.call_count == 2
    assert mock_slow_remove.called
    assert mock_offline_remove.called
