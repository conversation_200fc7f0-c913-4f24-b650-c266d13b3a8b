# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
from unittest.mock import patch

from disk_healthd.core import disk_map


@patch.object(disk_map.Disk, "get_disk_full_info")
@patch.object(disk_map.Disk, "get_disk_wwid")
@patch.object(disk_map.Disk, "get_serial_number")
@patch.object(disk_map.Disk, "disk_names")
def test_disk_map(mock_disk_names, mock_get_serial_number, mock_get_disk_wwid, mock_get_disk_full_info):
    mock_disk_names.return_value = ["sda"]
    mock_get_serial_number.return_value = "c51a5855-8c1b-4f8e-8be1-b07125f8166c"
    mock_get_disk_wwid.return_value = "wwid_test_b07125f8166c"
    mock_get_disk_full_info.return_value = {
        "controller": "virtio_scsi",
        "extra_info": {},
        "firmware": "2.5+",
        "id": "sda-QEMU_HARDDISK-c51a5855_8c1b_4f8e_8be1_b07125f8166c",
        "is_healthy": True,
        "is_healthy_in_chunk": True,
        "is_mounted": True,
        "is_offline": False,
        "is_os_disk": True,
        "is_physical_healthy": True,
        "is_rotational": True,
        "migrating": False,
        "model": "QEMU_HARDDISK",
        "mount_status": "mounted",
        "name": "sda",
        "partitions": [
            {
                "name": "sda1",
                "path": "/dev/sda1",
                "size": 536870912,
                "part_uuid": "test_part_uuid",
                "usage": "boot",
            },
        ],
        "path": "/dev/sda",
        "serial": "c51a5855-8c1b-4f8e-8be1-b07125f8166c",
        "wwid": "wwid_test_b07125f8166c",
        "size": **********,
        "staging": True,
        "type": "HDD",
    }

    disk_map.disk_map.cumulative_update()
    assert "sda" in disk_map.disk_map.disk_names
    assert "c51a5855-8c1b-4f8e-8be1-b07125f8166c" in disk_map.disk_map.disk_serials
    assert "wwid_test_b07125f8166c" in disk_map.disk_map.disk_wwids

    disk_add, disk_remove = disk_map.disk_map.check_disks_changed()
    assert (disk_add, disk_remove) == (False, False)

    # test disk add and remove
    mock_get_serial_number.return_value = "test_add_disk_serial"
    mock_get_disk_wwid.return_value = "test_add_disk_wwid"
    mock_get_disk_full_info.return_value = {
        "controller": "virtio_scsi",
        "extra_info": {},
        "firmware": "2.5+",
        "id": "sda-QEMU_HARDDISK-c51a5855_8c1b_4f8e_8be1_b07125f8166c",
        "is_healthy": True,
        "is_healthy_in_chunk": True,
        "is_mounted": True,
        "is_offline": False,
        "is_os_disk": True,
        "is_physical_healthy": True,
        "is_rotational": True,
        "migrating": False,
        "model": "QEMU_HARDDISK",
        "mount_status": "mounted",
        "name": "sda",
        "partitions": [
            {
                "name": "sda1",
                "path": "/dev/sda1",
                "size": 536870912,
                "part_uuid": "test_part_uuid",
                "usage": "boot",
            },
        ],
        "path": "/dev/sda",
        "serial": "test_add_disk_serial",
        "wwid": "test_add_disk_wwid",
        "size": **********,
        "staging": True,
        "type": "HDD",
    }
    disk_add, disk_remove = disk_map.disk_map.check_disks_changed()
    assert (disk_add, disk_remove) == (True, True)
    assert "test_add_disk_serial" in disk_map.disk_map.last_disk_serials
    assert "test_add_disk_wwid" in disk_map.disk_map.last_disk_wwids

    assert disk_map.disk_map.check_partition_changed() is False

    chunk_part = {"part_uuid": "test_part_uuid", "path": "/dev/sda1"}
    part = disk_map.disk_map.match_one_disk_for_chunk_part(chunk_part)
    assert part is disk_map.disk_map.disk_names["sda"]
