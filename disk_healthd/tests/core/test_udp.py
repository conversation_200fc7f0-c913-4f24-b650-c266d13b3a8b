# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
from unittest.mock import Mock, patch

from disk_healthd.core.udp import UdpRe<PERSON><PERSON>outer, _UdpRequestHand<PERSON>
from disk_healthd.disk_offline.messages import HeartbeatRequest, MsgType
from disk_healthd.disk_offline.services import DiskOfflineService


def test_udp_router_and_handler():
    request = HeartbeatRequest(type_id=MsgType.HEART_BEAT, request_id=1, timestamp=1, node_uuid=b"").to_bytes()
    socket = Mock(send_to=lambda res, addr: None)

    # without any handle
    router = UdpRequestRouter()
    _UdpRequestHandler.request_router = router
    _UdpRequestHandler((request, socket), ("12.34.56.78", 12345), Mock())

    # disk offline handle
    with patch.object(DiskOfflineService, "handle_request") as mock_handle_request:
        router = UdpRequestRouter()
        router.register_service(DiskOfflineService)
        _UdpRequestHandler.request_router = router
        _UdpRequestHandler((request, socket), ("12.34.56.78", 12345), <PERSON><PERSON>())
        assert mock_handle_request.called

    _UdpRequestHandler.request_router = None
