# Copyright (c) 2013-2021, SMARTX
# All rights reserved.

from disk_healthd.core.base_loop import BaseLoop


class _TestLoop(BaseLoop):
    def loop(self):
        while not self._stop:
            self.tick_sleep(self.interval)


def test_base_loop():
    loop = _TestLoop(0.1)
    loop.tick_sleep(0.1)
    loop._random_delay = False
    loop.start()
    loop.stop()
    loop.join()
    assert loop._stop is True
