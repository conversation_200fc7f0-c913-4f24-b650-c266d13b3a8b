# Copyright (c) 2013-2021, SMARTX
# All rights reserved.

from unittest.mock import Mock, patch

from disk_healthd.tests.utils import init_config


def test_disk_monitor():
    init_config()

    from disk_healthd.core.base_loop import Base<PERSON>oop
    from disk_healthd.core.inotify_update import Di<PERSON><PERSON><PERSON>Watcher
    from disk_healthd.core.slow_disk import Off<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SlowDisk<PERSON>elper
    from disk_healthd.disk_monitor import DiskMonitor

    with (
        patch("disk_healthd.disk_monitor.disk_map") as mock_disk_map,
        patch("disk_healthd.disk_monitor.Thread") as mock_thread,
        patch.object(BaseLoop, "loop_warp") as mock_loop,
        patch.object(BaseLoop, "stop") as mock_stop,
        patch.object(BaseLoop, "join") as mock_join,
        patch.object(DiskEventWatcher, "watch") as mock_watch,
        patch.object(SlowDisk<PERSON>elper, "remove_absent_disks") as mock_slow_remove,
        patch.object(OfflineDisk<PERSON>elper, "remove_absent_disks") as mock_off_remove,
    ):
        mock_disk_map.cumulative_update = Mock(return_value=True)
        mock_disk_map.upgrade_disk_records = Mock(return_value=True)
        mock_disk_map.ensure_disk_records = Mock(return_value=True)
        mock_thread.return_value = Mock(daemon=False, start=lambda: None)
        mock_watch.return_value = True
        mock_loop.return_value = True
        mock_stop.return_value = True
        mock_join.return_value = True

        monitor = DiskMonitor()
        monitor.run()
        monitor.start()
        monitor.graceful_restart()

        assert mock_slow_remove.called
        assert mock_off_remove.called
