# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
from unittest.mock import Mock, patch

from disk_healthd.main import main, gunicorn_entry


def test_main():
    with (
        patch("disk_healthd.main.setup_logger") as mock_setup_logger,
        patch("disk_healthd.core.config.config") as mock_config,
        patch("disk_healthd.core.disk_map.disk_map.cumulative_update") as mock_cumulative_update,
        patch("disk_healthd.core.disk_map.disk_map.upgrade_disk_records") as mock_upgrade_disk_records,
        patch("disk_healthd.core.disk_map.disk_map.ensure_disk_records") as mock_ensure_disk_records,
        patch("disk_healthd.core.slow_disk.SlowDiskHelper.ensure_schema_init") as mock_slow_disk_ensure_schema_init,
        patch("disk_healthd.core.slow_disk.OfflineDiskHelper.ensure_schema_init") as mock_off_disk_ensure_schema_init,
        patch("disk_healthd.core.slow_disk.SlowDiskHelper.remove_absent_disks") as mock_slow_remove,
        patch("disk_healthd.core.slow_disk.OfflineDiskHelper.remove_absent_disks") as mock_off_remove,
        patch("disk_healthd.disk_monitor.DiskMonitor") as mock_monitor,
        patch("tuna.config.config_manager.ZBSConfig.get_data_ip") as mock_get_data_ip,
        patch("disk_healthd.udp_server.udp_server.start") as mock_upd_server_start,
        patch("disk_healthd.rest_server.flask_app") as mock_flask_app,
    ):
        mock_setup_logger.return_value = None
        mock_config.load = lambda: None
        mock_monitor.return_value = Mock(run=lambda: None, start=lambda: None)
        mock_flask_app.run = lambda host, port: None
        main()
        gunicorn_entry()

        assert mock_setup_logger.called
        assert mock_cumulative_update.called
        assert mock_upgrade_disk_records.called
        assert mock_ensure_disk_records.called
        assert mock_slow_disk_ensure_schema_init.called
        assert mock_off_disk_ensure_schema_init.called
        assert mock_slow_remove.called
        assert mock_off_remove.called
        assert mock_get_data_ip.called
        assert mock_upd_server_start.called
