# Copyright (c) 2013-2023, SMARTX
# All rights reserved.
from datetime import datetime
from unittest.mock import call, patch

from disk_healthd.check_loops import raid_status_loop


@patch.object(raid_status_loop.RaidStatusLoop, "check_raid_faulty", autospec=True)
def test_raid_loop(mock_check_raid):
    mock_check_raid.side_effect = lambda self: self.stop()
    raid_loop = raid_status_loop.RaidStatusLoop()
    raid_loop.loop()
    assert mock_check_raid.called
    assert raid_loop._stop is True


@patch("disk_healthd.check_loops.raid_status_loop.datetime")
@patch("disk_healthd.check_loops.raid_status_loop.disk_map")
@patch("disk_healthd.check_loops.raid_status_loop.DiskStatusService")
@patch.object(raid_status_loop.Raid, "list_all_raid_details")
def test_check_raid_faulty(
    mock_list_raid,
    MockDiskStatusService,
    mock_disk_map,
    mock_datetime,
):
    MockDiskStatusService._gen_disk_trace_id.side_effect = ["trace_id_of_sdb", "trace_id_of_sdc"]
    mock_disk_status_service = MockDiskStatusService.return_value
    mock_disk_status_service.list_local_disk_status.return_value = [
        {
            "trace_id": "trace_id_of_sdb",
            "hints": {"faulty_in_raid": True},
            "detected_at": {},
        },
        {
            "trace_id": "trace_id_of_sdc",
            "hints": {"faulty_in_raid": True},
            "detected_at": {"faulty_in_raid": "2024"},
        }
    ]
    mock_disk_map.disk_names = {
        "sdb": {"name": "sdb"},
        "sdc": {"name": "sdc"},
    }
    mock_list_raid.return_value = [
        {
            "device": "/dev/md127",
            "version": "1.2",
            "creation_time": "Tue Aug 29 00:27:27 2023",
            "raid_level": "raid1",
            "array_size": "89062400 (84.94 GiB 91.20 GB)",
            "used_dev_size": "89062400 (84.94 GiB 91.20 GB)",
            "raid_devices": 1,
            "total_devices": 1,
            "persistence": "Superblock is persistent",
            "intent_bitmap": "Internal",
            "update_time": "Thu Sep  7 19:38:18 2023",
            "state": "active",
            "active_devices": 1,
            "working_devices": 1,
            "failed_devices": 0,
            "spare_devices": 0,
            "consistency_policy": "bitmap",
            "name": "localhost:root",
            "uuid": "8e9950ff:526fe86a:24b72fed:c82ea51d",
            "events": "19738",
            "device_table": [
                {
                    "number": 0,
                    "major": 8,
                    "minor": 33,
                    "state": ["faulty"],
                    "device": "/dev/sdb1",
                    "raid_device": 0,
                },
                {
                    "number": 0,
                    "major": 8,
                    "minor": 33,
                    "state": ["active", "sync", "failfast"],
                    "device": "/dev/sdc1",
                    "raid_device": 0,
                },
            ],
            "array_size_num": 89062400,
            "used_dev_size_num": 89062400,
            "name_val": "localhost:root",
            "uuid_val": "8e9950ff:526fe86a:24b72fed:c82ea51d",
            "events_num": 19738,
            "state_list": ["active"],
            "creation_time_epoch": 1693240047,
            "update_time_epoch": 1694086698,
        },
        {
            "device": "/dev/md0",
            "version": "1.2",
            "creation_time": "Tue Aug 29 16:57:25 2023",
            "raid_level": "raid1",
            "array_size": "20955136 (19.98 GiB 21.46 GB)",
            "used_dev_size": "20955136 (19.98 GiB 21.46 GB)",
            "raid_devices": 2,
            "total_devices": 1,
            "persistence": "Superblock is persistent",
            "update_time": "Thu Sep  7 19:38:18 2023",
            "state": "active, degraded",
            "active_devices": 1,
            "working_devices": 1,
            "failed_devices": 0,
            "spare_devices": 0,
            "consistency_policy": "resync",
            "name": "zbs",
            "uuid": "39066f7f:e1016c51:319356a9:49fba466",
            "events": "27366",
            "device_table": [
                {
                    "number": 0,
                    "major": 8,
                    "minor": 34,
                    "state": ["active", "sync", "failfast"],
                    "device": "/dev/sdb2",
                    "raid_device": 0,
                },
                {
                    "number": 0,
                    "major": 8,
                    "minor": 34,
                    "state": ["faulty"],
                    "device": "/dev/sdc2",
                    "raid_device": 0,
                },
            ],
            "array_size_num": 20955136,
            "used_dev_size_num": 20955136,
            "name_val": "zbs",
            "uuid_val": "39066f7f:e1016c51:319356a9:49fba466",
            "events_num": 27366,
            "state_list": ["active", "degraded"],
            "creation_time_epoch": 1693299445,
            "update_time_epoch": 1694086698,
        },
    ]
    mock_now_date = datetime(2024, 10, 24, 12, 12, 12)
    mock_datetime.now.return_value = mock_now_date

    raid_loop = raid_status_loop.RaidStatusLoop()
    for i in range(1, 6):
        raid_loop.check_raid_faulty()
        assert raid_loop._latest_detected_count == {"/dev/sdc2": i, "/dev/sdb1": i}
    assert mock_disk_status_service.update_disks_with_fields.call_count == 2
    mock_disk_status_service.list_local_disk_status.assert_called_with()
    mock_disk_status_service.update_disks_with_fields.assert_has_calls([
        call([
            ({"name": "sdb"}, {"healthy": False, "hints.faulty_in_raid": True}),
            ({"name": "sdc"}, {"healthy": False, "hints.faulty_in_raid": True}),
        ]),
        call([({"name": "sdb"}, {"detected_at.faulty_in_raid": mock_now_date})]),
    ])

    mock_list_raid.return_value = []
    raid_loop.check_raid_faulty()
    assert raid_loop._latest_detected_count == {}
    assert mock_disk_status_service.update_disks_with_fields.call_count == 2
