# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
from datetime import datetime
from unittest.mock import call, patch

from disk_healthd.tests.utils import init_config


def test_check_chunk_status():
    init_config()

    from disk_healthd.check_loops.chunk_status_loop import ChunkStat<PERSON><PERSON>

    with (
        patch("disk_healthd.check_loops.chunk_status_loop.StorageDiskManager.get_disk_or_partition_info") as mock_get_disk_or_partition_info,
        patch("disk_healthd.check_loops.chunk_status_loop.disk_map") as mock_disk_map,
        patch("disk_healthd.check_loops.chunk_status_loop.DiskStatusService") as MockDiskStatusService,
        patch("disk_healthd.check_loops.chunk_status_loop.datetime") as mock_datetime,
    ):
        mock_get_disk_or_partition_info.return_value = {
            "sda1": {
                "uuid": "uuid_of_sda1",
                "device_id": "device_id_of_sda1",
                "path": "/dev/sda1",
                "num_io_errors": 301,
                "num_checksum_errors": 101,
                "errflags": 4,
                "warnflags": 1,
                "usage": "partition",
                "chunk_ins_id": 1,
            },
            "sdb1": {
                "uuid": "uuid_of_sdb1",
                "device_id": "device_id_of_sdb1",
                "path": "/dev/sdb1",
                "num_io_errors": 302,
                "num_checksum_errors": 102,
                "errflags": 4,
                "warnflags": 1,
                "usage": "partition",
                "chunk_ins_id": 1,
            },
            "sdc1": {
                "uuid": "uuid_of_sdc1",
                "device_id": "device_id_of_sdc1",
                "path": "/dev/sdc1",
                "num_io_errors": 108,
                "num_checksum_errors": 4,
                "errflags": 4,
                "warnflags": 1,
                "usage": "partition",
                "chunk_ins_id": 1,
            },
            "sdd1": {
                "uuid": "uuid_of_sdd1",
                "device_id": "device_id_of_sdd1",
                "path": "/dev/sdd1",
                "num_io_errors": 300,
                "num_checksum_errors": 103,
                "errflags": 4,
                "warnflags": 1,
                "usage": "partition",
                "chunk_ins_id": 1,
            },
        }
        mock_disk_map.match_one_disk_for_chunk_part.side_effect = [
            {"name": "sda1"},
            {"name": "sdb1"},
            None,
            {"name": "sdd1"},
        ]
        MockDiskStatusService._gen_disk_trace_id.side_effect = [
            "trace_id_of_sda1",
            "trace_id_of_sdb1",
            "trace_id_of_sdd1",
        ]
        mock_disk_status_service = MockDiskStatusService.return_value
        mock_disk_status_service.list_local_disk_status.return_value = [
            {
                "trace_id": "trace_id_of_sda1",
                "hints": {
                    "chunk_errflag": True,
                    "chunk_warnflag": True,
                },
                "detected_at": {
                    "chunk_errflag": "2021-08-01T00:00:00Z",
                    "chunk_warnflag": "2021-08-01T00:00:00Z",
                },
            },
            {
                "trace_id": "trace_id_of_sdb1",
                "hints": {
                    "chunk_io_error": True,
                    "chunk_checksum_error": True,
                },
                "detected_at": {
                    "chunk_io_error": "2021-08-01T00:00:00Z",
                    "chunk_checksum_error": "2021-08-01T00:00:00Z",
                },
            },
            {
                "trace_id": "trace_id_of_sdd1",
                "hints": {
                    "chunk_checksum_error": True,
                },
                "detected_at": {
                    "chunk_checksum_error": "2021-08-01T00:00:00Z",
                },
            },
        ]
        mock_now_date = datetime(2024,10,24,12,12,12)
        mock_datetime.now.return_value = mock_now_date

        ChunkStatLoop().check_chunk_status()

        mock_get_disk_or_partition_info.assert_called_once_with()
        mock_disk_map.match_one_disk_for_chunk_part.assert_has_calls([
            call({
                "uuid": "uuid_of_sda1",
                "device_id": "device_id_of_sda1",
                "path": "/dev/sda1",
                "num_io_errors": 301,
                "num_checksum_errors": 101,
                "errflags": 4,
                "warnflags": 1,
                "usage": "partition",
                "chunk_ins_id": 1,
            }),
            call({
                "uuid": "uuid_of_sdb1",
                "device_id": "device_id_of_sdb1",
                "path": "/dev/sdb1",
                "num_io_errors": 302,
                "num_checksum_errors": 102,
                "errflags": 4,
                "warnflags": 1,
                "usage": "partition",
                "chunk_ins_id": 1,
            }),
            call({
                "uuid": "uuid_of_sdc1",
                "device_id": "device_id_of_sdc1",
                "path": "/dev/sdc1",
                "num_io_errors": 108,
                "num_checksum_errors": 4,
                "errflags": 4,
                "warnflags": 1,
                "usage": "partition",
                "chunk_ins_id": 1,
            }),
            call({
                "uuid": "uuid_of_sdd1",
                "device_id": "device_id_of_sdd1",
                "path": "/dev/sdd1",
                "num_io_errors": 300,
                "num_checksum_errors": 103,
                "errflags": 4,
                "warnflags": 1,
                "usage": "partition",
                "chunk_ins_id": 1,
            }),
        ])
        mock_datetime.now.assert_called_once_with()
        mock_disk_status_service.list_local_disk_status.assert_called_once_with()
        MockDiskStatusService._gen_disk_trace_id.assert_has_calls([
            call({"name": "sda1"}),
            call({"name": "sdb1"}),
            call({"name": "sdd1"}),
        ])
        mock_disk_status_service.update_disks_with_fields.assert_has_calls([
            call([
                (
                    {"name": "sda1"},
                    {
                        "extra_info.chunk_num_io_errors": 301,
                        "extra_info.num_io_errors_threshold": ">300",
                        "extra_info.chunk_num_checksum_errors": 101,
                        "extra_info.num_checksum_errors_threshold": ">100",
                        "hints.chunk_io_error": True,
                        "hints.chunk_checksum_error": True,
                        "hints.chunk_errflag": True,
                        "hints.chunk_warnflag": True,
                        "healthy": False,
                    },
                ),
                (
                    {"name": "sdb1"},
                    {
                        "extra_info.chunk_num_io_errors": 302,
                        "extra_info.num_io_errors_threshold": ">300",
                        "extra_info.chunk_num_checksum_errors": 102,
                        "extra_info.num_checksum_errors_threshold": ">100",
                        "hints.chunk_io_error": True,
                        "hints.chunk_checksum_error": True,
                        "hints.chunk_errflag": True,
                        "hints.chunk_warnflag": True,
                        "healthy": False,
                    },
                ),
                (
                    {"name": "sdd1"},
                    {
                        "extra_info.chunk_num_checksum_errors": 103,
                        "extra_info.num_checksum_errors_threshold": ">100",
                        "hints.chunk_checksum_error": True,
                        "hints.chunk_errflag": True,
                        "hints.chunk_warnflag": True,
                        "healthy": False,
                    },
                ),
            ]),
            call([
                (
                    {"name": "sda1"},
                    {
                        "detected_at.chunk_io_error": mock_now_date,
                        "detected_at.chunk_checksum_error": mock_now_date,
                    },
                ),
                (
                    {"name": "sdb1"},
                    {
                        "detected_at.chunk_errflag": mock_now_date,
                        "detected_at.chunk_warnflag": mock_now_date,
                    },
                ),
                (
                    {"name": "sdd1"},
                    {
                        "detected_at.chunk_errflag": mock_now_date,
                        "detected_at.chunk_warnflag": mock_now_date,
                    },
                ),
            ]),
        ])
