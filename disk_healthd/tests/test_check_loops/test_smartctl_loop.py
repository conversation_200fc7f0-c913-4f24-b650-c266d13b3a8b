# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
from datetime import datetime
from unittest.mock import call, patch, MagicMock

from disk_healthd.tests.utils import init_config


class _MockPopenReturn:
    def __init__(self, output_lines, return_code):
        self.output_lines = output_lines
        self.return_code = return_code

    def communicate(self):
        return "\n".join(self.output_lines), self.return_code


class FakeObj:
    pass


def test_smartctl_loop():
    init_config()

    from disk_healthd.check_loops.smartctl_loop import SmartctlLoop

    loop = SmartctlLoop()
    loop.check_smartctl_status()
    assert not loop.smartctl_thread_list
    assert not loop.smartctl_list
    assert not loop.smartctl_hang_list

    loop.smartctl_list = ["D smartctl" for _ in range(15)]
    loop._check_smartctl_status()
    loop.check_smartctl_status()
    assert not loop.smartctl_list

    with patch("disk_healthd.check_loops.smartctl_loop.Popen") as mock_popen:
        ps = ["D 1234 smartctl -a sda", "D 1235 smartctl -a sdb", "D 1236 smartctl -a sdc"]
        mock_popen.return_value = _MockPopenReturn(ps, return_code=0)
        loop = SmartctlLoop()
        loop.load_smartctl_process_status()
        assert loop.smartctl_list == ps
        assert loop.smartctl_hang_list == {"1234": ps[0], "1235": ps[1], "1236": ps[2]}


def test_smartctl_loop_with_update_disks():
    init_config()

    from disk_healthd.check_loops.smartctl_loop import SmartctlLoop
    from disk_healthd.core.disk_map import disk_map

    disk_map.cumulative_update()

    with patch.object(SmartctlLoop, "load_smartctl_process_status") as mock_load_hang, patch.object(
        SmartctlLoop, "tick_sleep"
    ) as mock_tick_sleep, patch(
        "disk_healthd.check_loops.smartctl_loop.DiskStatusService.update_disks_with_fields"
    ) as mock_update_disks_with_fields:
        mock_tick_sleep.return_value = None
        mock_load_hang.return_value = None
        mock_update_disks_with_fields.return_value = None

        loop = SmartctlLoop()
        loop.smartctl_hang_list = {
            i: "D {} smartctl {}".format(i, disk_name) for i, disk_name in enumerate(disk_map.disk_names)
        }
        loop.SMARTCTL_HANG_ALERT = True
        loop.check_smartctl_status()
        assert not loop.smartctl_thread_list
        assert loop.smartctl_hang_list
        assert mock_update_disks_with_fields.called


def test_get_life_span():
    init_config()

    from disk_healthd.check_loops.smartctl_loop import SmartctlLoop

    loop = SmartctlLoop()

    fake_dev = FakeObj()
    fake_dev.available_spare = 99  # nvme
    assert loop._get_life_span(fake_dev) == 99

    fake_dev.available_spare = None
    fake_dev.name = "sda"
    fake_dev.attributes = []
    assert loop._get_life_span(fake_dev) is None

    fake_attr_177 = FakeObj()
    fake_attr_177.name = "smart 177"
    fake_attr_177.num = "177"
    fake_attr_177.value = "98"
    fake_dev.attributes = [fake_attr_177]
    assert loop._get_life_span(fake_dev) == 98

    fake_attr_233 = FakeObj()
    fake_attr_233.name = "smart 233"
    fake_attr_233.num = "233"
    fake_attr_233.value = "97"
    fake_dev.attributes = [fake_attr_177, fake_attr_233]
    assert loop._get_life_span(fake_dev) == 97


def test_is_healthy():
    init_config()

    from disk_healthd.check_loops.smartctl_loop import SmartctlLoop

    fake_dev = FakeObj()
    fake_dev.name = "sda"
    fake_dev.is_healthy = lambda: True
    loop = SmartctlLoop()

    smart_attrs = {
        "smart_5": {"name": "Reallocated_Sector_Ct", "raw": "501", "thresh": "010", "value": "100"},
        "smart_177": {"name": "Wear_Leveling_Count", "raw": "0", "thresh": "000", "value": "10"},
        "smart_187": {"name": "Reported_Uncorrect", "raw": "1", "thresh": "000", "value": "100"},
        "smart_188": {"name": "Command_Timeout", "raw": "0", "thresh": "000", "value": "9"},
        "smart_194": {"name": "Temperature_Celsius", "raw": "54", "thresh": "000", "value": "024"},
        "smart_197": {"name": "Current_Pending_Sector", "raw": "1", "thresh": "000", "value": "100"},
        "smart_198": {"name": "Offline_Uncorrectable", "raw": "1", "thresh": "000", "value": "100"},
        "smart_233": {"name": "Media_Wearout_Indicator", "raw": "10172962", "thresh": "000", "value": "10"},
    }

    loop.EXTRA_SMART_CHECK = True
    assert loop._check_and_record_healthy(fake_dev, smart_attrs) is False
    loop.EXTRA_SMART_CHECK = False
    assert loop._check_and_record_healthy(fake_dev, smart_attrs) is True

    expected_smart_attrs = {
        "self_assessment_error": False,
        "smart_5": {
            "name": "Reallocated_Sector_Ct",
            "raw": "501",
            "thresh": "010",
            "value": "100",
            "check_field": "raw",
            "check_threshold": 500,
            "check_ok": False,
        },
        "smart_177": {
            "name": "Wear_Leveling_Count",
            "raw": "0",
            "thresh": "000",
            "value": "10",
            "check_field": "value",
            "check_threshold": 20,
            "check_ok": False,
        },
        "smart_187": {
            "name": "Reported_Uncorrect",
            "raw": "1",
            "thresh": "000",
            "value": "100",
            "check_field": "raw",
            "check_threshold": 0,
            "check_ok": False,
        },
        "smart_188": {
            "name": "Command_Timeout",
            "raw": "0",
            "thresh": "000",
            "value": "9",
            "check_field": "value",
            "check_threshold": 10,
            "check_ok": False,
        },
        "smart_194": {
            "name": "Temperature_Celsius",
            "raw": "54",
            "thresh": "000",
            "value": "024",
            "check_field": "raw",
            "check_threshold": 45,
            "check_ok": False,
        },
        "smart_197": {
            "name": "Current_Pending_Sector",
            "raw": "1",
            "thresh": "000",
            "value": "100",
            "check_field": "raw",
            "check_threshold": 0,
            "check_ok": False,
        },
        "smart_198": {
            "name": "Offline_Uncorrectable",
            "raw": "1",
            "thresh": "000",
            "value": "100",
            "check_field": "raw",
            "check_threshold": 0,
            "check_ok": False,
        },
        "smart_233": {
            "name": "Media_Wearout_Indicator",
            "raw": "10172962",
            "thresh": "000",
            "value": "10",
            "check_field": "value",
            "check_threshold": 20,
            "check_ok": False,
        },
    }
    assert smart_attrs == expected_smart_attrs

    smart_attrs = {
        "smart_5": {"name": "Reallocated_Sector_Ct", "raw": "9", "thresh": "010", "value": "100"},
        "smart_177": {"name": "Wear_Leveling_Count", "raw": "0", "thresh": "000", "value": "21"},
        "smart_187": {"name": "Reported_Uncorrect", "raw": "0", "thresh": "000", "value": "100"},
        "smart_188": {"name": "Command_Timeout", "raw": "0", "thresh": "000", "value": "11"},
        "smart_194": {"name": "Temperature_Celsius", "raw": "44", "thresh": "000", "value": "024"},
        "smart_197": {"name": "Current_Pending_Sector", "raw": "0", "thresh": "000", "value": "100"},
        "smart_198": {"name": "Offline_Uncorrectable", "raw": "0", "thresh": "000", "value": "100"},
        "smart_233": {"name": "Media_Wearout_Indicator", "raw": "10172962", "thresh": "000", "value": "21"},
    }

    loop.EXTRA_SMART_CHECK = True
    assert loop._check_and_record_healthy(fake_dev, smart_attrs) is True
    loop.EXTRA_SMART_CHECK = False
    assert loop._check_and_record_healthy(fake_dev, smart_attrs) is True


def test_check_smartctl_status():
    init_config()

    from disk_healthd.check_loops import smartctl_loop
    from disk_healthd.check_loops.smartctl_loop import SmartctlLoop

    mock_disk_map = MagicMock(disk_names={
        "sda": {"name": "sda"},
        "sdb": {"name": "sdb"},
        "sdd": {"name": "sdd"},
    })

    with (
        patch.object(smartctl_loop, "disk_map", mock_disk_map),
        patch("disk_healthd.check_loops.smartctl_loop.Disk.disk_names") as mock_disk_names,
        patch("disk_healthd.check_loops.smartctl_loop.SmartctlLoop._too_many_smartctl") as mock_too_many_smartctl,
        patch("disk_healthd.check_loops.smartctl_loop.SmartctlLoop._safe_get_smart_info") as mock_safe_get_smart_info,
        patch("disk_healthd.check_loops.smartctl_loop.DiskStatusService") as MockDiskStatusService,
        patch("disk_healthd.check_loops.smartctl_loop.datetime") as mock_datetime,
    ):
        mock_disk_names.return_value = ["sda", "sdb", "sdc", "sdd"]
        mock_too_many_smartctl.side_effect = [False, False, False, False]
        mock_safe_get_smart_info.side_effect = [
            {
                "is_healthy": False,
                "smart_attrs": {"self_assessment_error": False},
                "reallocated_sectors_count": 10,
            },
            {
                "is_healthy": False,
                "smart_attrs": {"self_assessment_error": False},
                "reallocated_sectors_count": 20,
            },
            {
                "is_healthy": True,
                "smart_attrs": {"self_assessment_error": False},
                "reallocated_sectors_count": 500,
            },
        ]
        MockDiskStatusService._gen_disk_trace_id.side_effect = ["trace_id_of_sda", "trace_id_of_sdb", "trace_id_of_sdd"]
        mock_disk_status_service = MockDiskStatusService.return_value
        mock_disk_status_service.list_local_disk_status.return_value = [
            {
                "name": "sda",
                "trace_id": "trace_id_of_sda",
                "hints": {"smart_check": True},
                "detected_at": {},
            },
            {
                "name": "sdb",
                "trace_id": "trace_id_of_sdb",
                "hints": {"smart_check": True},
                "detected_at": {"smart_check": "2024"}
            },
            {
                "name": "sdd",
                "trace_id": "trace_id_of_sdd",
            },
        ]
        mock_now_date = datetime(2024, 10, 24, 12, 12, 12)
        mock_datetime.now.return_value = mock_now_date

        loop = SmartctlLoop()
        loop._check_smartctl_status()

        mock_disk_names.assert_called_once_with()
        mock_too_many_smartctl.assert_has_calls([call(), call(), call()])
        mock_safe_get_smart_info.assert_has_calls([call("sda"), call("sdb")])
        mock_datetime.now.assert_called_once_with()
        mock_disk_status_service.list_local_disk_status.assert_called_once_with()
        mock_disk_status_service.update_disks_with_fields.assert_has_calls([
            call([
                (
                    {"name": "sda"},
                    {"hints.smart_check": True, "healthy": False, "extra_info.self_assessment_error": False},
                ),
                (
                    {"name": "sdb"},
                    {"hints.smart_check": True, "healthy": False, "extra_info.self_assessment_error": False},
                ),
                (
                    {"name": "sdd"},
                    {
                        "hints.reallocated_sectors_count_overflow": True,
                        "extra_info.reallocated_sectors_count": 500,
                        "extra_info.reallocated_sectors_count_threshold": ">=400",
                        "extra_info.self_assessment_error": False,
                        "healthy": False,
                    }
                ),
            ]),
            call([
                (
                    {"name": "sda"},
                    {"detected_at.smart_check": mock_now_date},
                ),
                (
                    {"name": "sdd"},
                    {"detected_at.reallocated_sectors_count_overflow": mock_now_date},
                ),
            ]),
        ])


def test_get_smart_info():
    init_config()

    from disk_healthd.check_loops.smartctl_loop import SmartctlLoop

    class MockAttribute:
        def __init__(self, name, num, value, thresh, raw):
            self.name = name
            self.num = num
            self.value = value
            self.thresh = thresh
            self.raw = raw

        def __repr__(self):
            return f"{self.name}({self.num}, {self.value}, {self.thresh}, {self.raw})"

    # case 1: get smart info successfully
    with (
        patch("disk_healthd.check_loops.smartctl_loop.ZDevice") as MockZDevice,
        patch.object(SmartctlLoop, "_get_life_span") as mock_get_life_span,
        patch.object(SmartctlLoop, "_get_celsius_temperature") as mock_get_celsius_temperature,
        patch.object(SmartctlLoop, "_check_and_record_healthy") as mock_check_and_record_healthy,
    ):
        mock_dev = MockZDevice.return_value
        mock_dev.name = "sda"
        mock_dev.attributes = [
            MockAttribute(name="Reallocated_Sector_Ct", num="5", value="100", thresh="010", raw="200"),
            MockAttribute(name="Reported_Uncorrect", num="187", value="100", thresh="000", raw="0"),
            MockAttribute(name="Command_Timeout", num="188", value="100", thresh="000", raw="0"),
            MockAttribute(name="Temperature_Celsius", num="194", value="036", thresh="000", raw="36"),
            MockAttribute(name="Current_Pending_Sector ", num="197", value="100", thresh="000", raw="300"),
            MockAttribute(name="Offline_Uncorrectable", num="198", value="100", thresh="000", raw="0"),
        ]
        mock_get_life_span.return_value = 95
        mock_get_celsius_temperature.return_value = 36
        mock_check_and_record_healthy.return_value = True

        loop = SmartctlLoop()
        mock_res = {}
        loop._get_smart_info(mock_dev, mock_res)

        assert mock_res["return"] == {
            "is_healthy": True,
            "smart_attrs": {
                "smart_5": {
                    "name": "Reallocated_Sector_Ct",
                    "value": "100",
                    "thresh": "010",
                    "raw": "200",
                },
                "smart_187": {
                    "name": "Reported_Uncorrect",
                    "value": "100",
                    "thresh": "000",
                    "raw": "0",
                },
                "smart_188": {
                    "name": "Command_Timeout",
                    "value": "100",
                    "thresh": "000",
                    "raw": "0",
                },
                "smart_194": {
                    "name": "Temperature_Celsius",
                    "value": "036",
                    "thresh": "000",
                    "raw": "36",
                },
                "smart_197": {
                    "name": "Current_Pending_Sector ",
                    "value": "100",
                    "thresh": "000",
                    "raw": "300",
                },
                "smart_198": {
                    "name": "Offline_Uncorrectable",
                    "value": "100",
                    "thresh": "000",
                    "raw": "0",
                },
                "lifespan": 95,
                "celsius_temperature": 36,
            },
            "reallocated_sectors_count": 500,
        }
        mock_get_life_span.assert_called_once_with(mock_dev)
        mock_get_celsius_temperature.assert_called_once_with(mock_dev)
        mock_check_and_record_healthy.assert_called_once_with(
            mock_dev,
            {
                "smart_5": {
                    "name": "Reallocated_Sector_Ct",
                    "value": "100",
                    "thresh": "010",
                    "raw": "200",
                },
                "smart_187": {
                    "name": "Reported_Uncorrect",
                    "value": "100",
                    "thresh": "000",
                    "raw": "0",
                },
                "smart_188": {
                    "name": "Command_Timeout",
                    "value": "100",
                    "thresh": "000",
                    "raw": "0",
                },
                "smart_194": {
                    "name": "Temperature_Celsius",
                    "value": "036",
                    "thresh": "000",
                    "raw": "36",
                },
                "smart_197": {
                    "name": "Current_Pending_Sector ",
                    "value": "100",
                    "thresh": "000",
                    "raw": "300",
                },
                "smart_198": {
                    "name": "Offline_Uncorrectable",
                    "value": "100",
                    "thresh": "000",
                    "raw": "0",
                },
                "lifespan": 95,
                "celsius_temperature": 36,
            },
        )

    # case 2: get smart info failed with ValueError
    with (
        patch("disk_healthd.check_loops.smartctl_loop.ZDevice") as MockZDevice,
        patch.object(SmartctlLoop, "_get_life_span") as mock_get_life_span,
        patch.object(SmartctlLoop, "_get_celsius_temperature") as mock_get_celsius_temperature,
        patch.object(SmartctlLoop, "_check_and_record_healthy") as mock_check_and_record_healthy,
    ):
        mock_dev = MockZDevice.return_value
        mock_dev.name = "sda"
        mock_dev.attributes = [
            MockAttribute(name="Reallocated_Sector_Ct", num="5", value="100", thresh="010", raw="abc"),
            MockAttribute(name="Reported_Uncorrect", num="187", value="100", thresh="000", raw="0"),
            MockAttribute(name="Command_Timeout", num="188", value="100", thresh="000", raw="0"),
            MockAttribute(name="Temperature_Celsius", num="194", value="036", thresh="000", raw="36"),
            MockAttribute(name="Current_Pending_Sector ", num="197", value="100", thresh="000", raw="def"),
            MockAttribute(name="Offline_Uncorrectable", num="198", value="100", thresh="000", raw="0"),
        ]
        mock_get_life_span.return_value = 95
        mock_get_celsius_temperature.return_value = 36
        mock_check_and_record_healthy.return_value = True

        loop = SmartctlLoop()
        mock_res = {}
        loop._get_smart_info(mock_dev, mock_res)

        assert mock_res["return"] == {
            "is_healthy": True,
            "smart_attrs": {
                "smart_5": {
                    "name": "Reallocated_Sector_Ct",
                    "value": "100",
                    "thresh": "010",
                    "raw": "abc",
                },
                "smart_187": {
                    "name": "Reported_Uncorrect",
                    "value": "100",
                    "thresh": "000",
                    "raw": "0",
                },
                "smart_188": {
                    "name": "Command_Timeout",
                    "value": "100",
                    "thresh": "000",
                    "raw": "0",
                },
                "smart_194": {
                    "name": "Temperature_Celsius",
                    "value": "036",
                    "thresh": "000",
                    "raw": "36",
                },
                "smart_197": {
                    "name": "Current_Pending_Sector ",
                    "value": "100",
                    "thresh": "000",
                    "raw": "def",
                },
                "smart_198": {
                    "name": "Offline_Uncorrectable",
                    "value": "100",
                    "thresh": "000",
                    "raw": "0",
                },
                "lifespan": 95,
                "celsius_temperature": 36,
            },
            "reallocated_sectors_count": 0,
        }
        mock_get_life_span.assert_called_once_with(mock_dev)
        mock_get_celsius_temperature.assert_called_once_with(mock_dev)
        mock_check_and_record_healthy.assert_called_once_with(
            mock_dev,
            {
                "smart_5": {
                    "name": "Reallocated_Sector_Ct",
                    "value": "100",
                    "thresh": "010",
                    "raw": "abc",
                },
                "smart_187": {
                    "name": "Reported_Uncorrect",
                    "value": "100",
                    "thresh": "000",
                    "raw": "0",
                },
                "smart_188": {
                    "name": "Command_Timeout",
                    "value": "100",
                    "thresh": "000",
                    "raw": "0",
                },
                "smart_194": {
                    "name": "Temperature_Celsius",
                    "value": "036",
                    "thresh": "000",
                    "raw": "36",
                },
                "smart_197": {
                    "name": "Current_Pending_Sector ",
                    "value": "100",
                    "thresh": "000",
                    "raw": "def",
                },
                "smart_198": {
                    "name": "Offline_Uncorrectable",
                    "value": "100",
                    "thresh": "000",
                    "raw": "0",
                },
                "lifespan": 95,
                "celsius_temperature": 36,
            },
        )
