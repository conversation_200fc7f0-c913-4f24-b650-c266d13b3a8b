# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
from datetime import datetime
import logging
from unittest.mock import call, <PERSON><PERSON><PERSON>, Mo<PERSON>, patch

from disk_healthd.tests.utils import init_config
from zbs.proto import chunk_pb2 as chunk


def test_diskstats_loop():
    init_config()

    with patch(
        "disk_healthd.check_loops.diskstats_loop.DiskStatusService.update_disks_with_fields"
    ) as mock_update_disks_with_fields, patch("disk_healthd.check_loops.diskstats_loop.disk_map") as mock_disk_map:
        from disk_healthd.check_loops.diskstats_loop import Disk<PERSON>tat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DiskStatsLoop

        DiskStatsDeltaHelper._setup_logger = lambda item: logging.getLogger()
        mock_disk_map.disk_names.__contains__ = Mock(return_value=True)

        diskstats_loop = DiskStatsLoop(interval=0.01)
        diskstats_loop._stop = True
        diskstats_loop.loop()
        diskstats_loop.delta_counter.collect_one_point()
        diskstats_loop.delta_counter.collect_one_point()
        assert len(diskstats_loop.delta_counter.points) == 2
        assert len(diskstats_loop.delta_counter.raw_points) == 2
        assert len(diskstats_loop.delta_counter.iostat_points) == 1
        assert len(diskstats_loop.delta_counter.iostat_points[0]) == 2

        timestamp = datetime.strftime(datetime.now(), "%Y-%m-%d %H:%M:%S")

        mock_disk_map.disk_names = {"sda": {"name": "sda", "is_rotational": False}}
        iostat_p = {"sda": {"rw_await": 501, "rw_iops": 0.0, "rw_sector_ps": 307201}}
        diskstats_loop.delta_counter.iostat_points = [
            (timestamp, iostat_p) for _ in range(diskstats_loop.SAMPLING_WIN_SIZE)
        ]
        diskstats_loop._check_io_latency()
        assert not mock_update_disks_with_fields.called

        mock_disk_map.disk_names = {"sda": {"name": "sda", "is_rotational": True}}
        iostat_p = {"sda": {"rw_await": 5001, "rw_iops": 101, "rw_sector_ps": 0.0}}
        diskstats_loop.delta_counter.iostat_points = [
            (timestamp, iostat_p) for _ in range(diskstats_loop.SAMPLING_WIN_SIZE)
        ]
        diskstats_loop._check_io_latency()
        assert not mock_update_disks_with_fields.called

        iostat_p = {"sda": {"rw_await": 3001, "rw_iops": 0.0, "rw_sector_ps": 0.0}}
        diskstats_loop.delta_counter.iostat_points = [
            (timestamp, iostat_p) for _ in range(diskstats_loop.SAMPLING_WIN_SIZE)
        ]
        diskstats_loop._check_io_latency()
        assert mock_update_disks_with_fields.called


def test_check_io_latency():
    init_config()

    from disk_healthd.check_loops import diskstats_loop
    from disk_healthd.check_loops.diskstats_loop import DiskStatsLoop

    mock_disk_map = Mock(
        disk_names={
            "sda": {"name": "sda"},
            "sdb": {"name": "sdb"},
        }
    )
    mock_delta_counter = Mock()
    mock_delta_counter.get_raw_data_of_disk.side_effect = [
        "raw_data_of_sda",
        "raw_data_of_sdb",
    ]

    with (
        patch.object(diskstats_loop, "disk_map", mock_disk_map),
        patch("disk_healthd.check_loops.diskstats_loop.DiskStatsLoop._get_high_latency") as mock_get_high_latency,
        patch("disk_healthd.check_loops.diskstats_loop.DiskStatusService") as MockDiskStatusService,
        patch("disk_healthd.check_loops.diskstats_loop.datetime") as mock_datetime,
        patch("disk_healthd.check_loops.diskstats_loop.DiskStatsLoop._get_slow_disks") as mock_get_slow_disks,
    ):
        mock_get_high_latency.side_effect = [
            (
                {"hints.iostat_latency": True},
                ["detected_at.iostat_latency"],
            ),
            (
                {"hints.iostat_latency": True},
                ["detected_at.iostat_latency"],
            ),
        ]
        MockDiskStatusService._gen_disk_trace_id.side_effect = [
            "trace_id_of_sda", "trace_id_of_sdb",
        ]
        mock_disk_status_service = MockDiskStatusService.return_value
        mock_disk_status_service.list_local_disk_status.return_value = [
            {
                "trace_id": "trace_id_of_sda",
                "hints": {
                    "iostat_latency": True,
                },
                "detected_at": {
                    "iostat_latency": "2024",
                }
            },
            {
                "trace_id": "trace_id_of_sdb",
                "hints": {
                    "iostat_latency": True,
                },
            },
        ]
        mock_now_date = datetime(2024, 10, 24, 12, 12, 12)
        mock_datetime.now.return_value = mock_now_date
        mock_get_slow_disks.return_value = []

        disk_stats_loop = DiskStatsLoop()
        disk_stats_loop.delta_counter = mock_delta_counter
        disk_stats_loop._check_io_latency()

        mock_get_high_latency.assert_has_calls([call({"name": "sda"}), call({"name": "sdb"})])
        mock_delta_counter.flush_raw_log.assert_called_once_with()
        mock_disk_status_service.list_local_disk_status.assert_called_once_with()
        MockDiskStatusService._gen_disk_trace_id.assert_has_calls([call({"name": "sda"}), call({"name": "sdb"})])
        mock_disk_status_service.update_disks_with_fields.assert_has_calls([
            call([
                ({"name": "sda"}, {"hints.iostat_latency": True, "extra_info.raw_data": "raw_data_of_sda"}),
                ({"name": "sdb"}, {"hints.iostat_latency": True, "extra_info.raw_data": "raw_data_of_sdb"}),
            ]),
            call([
                ({"name": "sdb"}, {"detected_at.iostat_latency": mock_now_date}),
            ]),
        ])
        mock_get_slow_disks.assert_called_once_with([
            ({"name": "sda"}, {"hints.iostat_latency": True, "extra_info.raw_data": "raw_data_of_sda"}),
            ({"name": "sdb"}, {"hints.iostat_latency": True, "extra_info.raw_data": "raw_data_of_sdb"}),
        ])


def test_get_high_latency():
    init_config()

    from disk_healthd.check_loops.diskstats_loop import DiskStatsLoop

    mock_disk_info = {
        "name": "sda",
        "is_rotational": True,
    }
    mock_delta_counter = Mock(
        iostat_points=[
            (
                "2025-06-06 10:00:00",
                {
                    "sda": {
                        "rw_iops": 10,
                        "rw_sector_ps": 200,
                        "rw_await": 5000,
                    },
                },
            ),
        ],
    )

    disk_stats_loop = DiskStatsLoop()
    disk_stats_loop.delta_counter = mock_delta_counter

    hints, hints_detected = disk_stats_loop._get_high_latency(mock_disk_info)

    assert hints == {
        "hints.iostat_latency": True,
        "healthy": False,
        "extra_info.rw_await": 5000,
        "extra_info.latency_threshold": ">{}ms".format(disk_stats_loop.THRESHOLD_HDD_LATENCY_MS),
        "extra_info.iops": 10,
        "extra_info.iops_threshold": "<50",
        "extra_info.sector_ps": 200,
        "extra_info.sector_ps_threshold": "<102400setcor/s",
        "extra_info.bw_mib_ps": 0.098,
        "extra_info.bw_mib_ps_threshold": "<50.0MiB/s",
    }
    assert hints_detected == ["detected_at.iostat_latency"]


def test_get_slow_disks():
    init_config()

    from disk_healthd.check_loops.diskstats_loop import DiskStatsLoop

    mock_hint_disk_list = [
        (
            {"name": "sda", "is_rotational": True},
            {"hints.iostat_latency": True},
        ),
        (
            {"name": "sdb", "is_rotational": False},
            {"hints.iostat_latency": True, "extra_info.rw_await": 3000, "extra_info.iops": 1999, "extra_info.sector_ps": 102000},
        ),
        (
            {"name": "sdc", "is_rotational": False},
            {"hints.iostat_latency": True, "extra_info.rw_await": 3000, "extra_info.iops": 2001, "extra_info.sector_ps": 103000},
        )
    ]

    diskstats_loop = DiskStatsLoop()
    slow_disks = diskstats_loop._get_slow_disks(mock_hint_disk_list)

    assert slow_disks == [
        {"name": "sda", "is_rotational": True},
        {"name": "sdb", "is_rotational": False},
    ]


def test_collect_one_point():
    init_config()

    from disk_healthd.check_loops.diskstats_loop import DiskStatsDeltaHelper

    DiskStatsDeltaHelper._setup_logger = lambda item: logging.getLogger()

    with (
        patch.object(DiskStatsDeltaHelper, "_parse_disk_stats") as mock_parse_disk_stats,
        patch.object(DiskStatsDeltaHelper, "_cal_avg_iostat") as mock_cal_avg_iostat,
    ):
        mock_parse_disk_stats.return_value = (
            "2024-10-24 12:00:00",
            {
                "sda": {
                    "r_io": 100,
                    "w_io": 200,
                },
                "sdb": {
                    "r_io": 150,
                    "w_io": 250,
                },
            },
            "2024-10-24 12:00:00\n100 200\n150 250\n",
        )
        mock_cal_avg_iostat.return_value = {
            "sda": {
                "rw_await": 500,
                "rw_iops": 50.0,
                "rw_sector_ps": 102400,
            },
            "sdb": {
                "rw_await": 600,
                "rw_iops": 60.0,
                "rw_sector_ps": 204800,
            },
        }

        delta_helper = DiskStatsDeltaHelper()

        delta_helper.collect_one_point()

        assert len(delta_helper.points) == 1
        assert len(delta_helper.raw_points) == 1
        assert len(delta_helper.raw_diskstats_points) == 1

        mock_parse_disk_stats.assert_called_once_with()
        mock_cal_avg_iostat.assert_not_called()


def test_get_raw_data_of_disk():
    init_config()

    from disk_healthd.check_loops.diskstats_loop import DiskStatsDeltaHelper

    DiskStatsDeltaHelper._setup_logger = lambda item: logging.getLogger()

    mock_disk_name = "sda"
    mock_raw_diskstats_points = [
        "2024-10-24 12:00:00\n8 0 sda 565 0 75122 522 30 19 336 168 0 456 455 0 0 0 0\n",
        "2024-10-24 12:00:15\n8 0 sda 566 0 75123 523 31 20 337 169 0 457 456 0 0 0 0\n",
    ]
    delta_helper = DiskStatsDeltaHelper()
    delta_helper.raw_diskstats_points = mock_raw_diskstats_points

    raw_data = delta_helper.get_raw_data_of_disk(mock_disk_name)

    assert raw_data == "2024-10-24_12:00:00 565 30 595 - 75122 336 75458 - - 522 168 -\n2024-10-24_12:00:15 566 31 597 0.133 75123 337 75460 0.133 0.0 523 169 1.0"
