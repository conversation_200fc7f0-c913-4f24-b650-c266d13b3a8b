# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
from collections import namedtuple
from uuid import uuid4

from unittest.mock import call, <PERSON><PERSON><PERSON>, Mock, patch

from disk_healthd.check_loops.isolate_disk_loop import (
    DISK_OR_PARTITION_USAGE_CACHE,
    DISK_OR_PARTITION_USAGE_JOURNAL,
    DISK_OR_PARTITION_USAGE_PARTITION,
    IsolateDiskLoop,
    StorageDiskManager,
)
from disk_healthd.core import slow_disk
from zbs.chunk.client import ZbsChunk
from zbs.proto import chunk_pb2 as chunk


class _MockPart:
    def __init__(self):
        self.num_io_errors = 0
        self.num_checksum_errors = 0
        self.num_slow_io = 0
        self.path = "/dev/sda"
        self.device_id = "scsi-0QEMU_QEMU_HARDDISK_2604fb72-21ac-4781-b-part4"
        self.uuid = str(uuid4())
        self.part_uuid = "test_part_uuid"
        self.errflags = 0
        self.warnflags = 0
        self.used_size = 0
        self.total_size = 1024000
        self.num_used = 0
        self.status = 0


def test_get_chunk_partition_for_disk():
    with patch.object(StorageDiskManager, "get_disk_or_partition_info") as mock_get_disk_or_partition_info:
        mock_partition_info = {
            "path": "/dev/sda",
            "device_id": "scsi-0QEMU_QEMU_HARDDISK_2604fb72-21ac-4781-b-part4",
            "uuid": "test_uuid",
            "part_uuid": "test_part_uuid",
            "usage": "partition",
            "errflags": 0,
            "warnflags": 0,
            "used_size": 0,
            "total_size": 1024000,
            "num_used": 0,
            "status": 0,
            "num_io_errors": 0,
            "num_checksum_errors": 0,
            "num_slow_io": 0,
        }
        mock_partition = namedtuple("GenericDict", mock_partition_info.keys())(**mock_partition_info)
        mock_get_disk_or_partition_info.return_value = {"test_uuid": mock_partition}
        loop = IsolateDiskLoop()
        loop.chunk_client = ZbsChunk()

        chunk_parts = loop._list_chunk_partitions()
        assert chunk_parts is not None
        assert mock_get_disk_or_partition_info.called
        mock_get_disk_or_partition_info.assert_called_once_with(
            usages=[DISK_OR_PARTITION_USAGE_PARTITION],
            chunk_client=loop.chunk_client,
            raise_error=True,
            use_nt=True,
        )

        fake_disk = {
            "id": "sda-QEMU_HARDDISK-c51a5855_8c1b_4f8e_8be1_b07125f8166c",
            "is_rotational": True,
            "name": "sda",
            "path": "/dev/sda",
            "serial": "c51a5855-8c1b-4f8e-8be1-b07125f8166c",
            "type": "HDD",
            "partitions": [
                {
                    "part_uuid": "test_part_uuid",
                    "path": "/dev/sda1",
                }
            ],
            "udev_properties": {
                "device_links": [
                    "/dev/disk/by-id/scsi-0QEMU_QEMU_HARDDISK_2604fb72-21ac-4781-b",
                    "/dev/disk/by-path/virtio-pci-0000:00:09.0-scsi-0:0:0:0",
                ],
            },
        }

        assert loop._get_part_from_chunk_for_disk(fake_disk, chunk_parts) is not None


class _MockCache:
    def __init__(self):
        self.num_io_errors = 0
        self.num_checksum_errors = 0
        self.num_slow_io = 0
        self.path = "/dev/sdb"
        self.device_id = "scsi-0QEMU_QEMU_HARDDISK_2604fb72-21ac-4781-b-part4"
        self.uuid = str(uuid4())
        self.part_uuid = "test_part_uuid"
        self.errflags = 0
        self.warnflags = 0
        self.used_size = 0
        self.total_size = 1024000
        self.num_used = 0
        self.status = 0


def test_list_chunk_caches():
    with patch.object(StorageDiskManager, "get_disk_or_partition_info") as mock_get_disk_or_partition_info:
        mock_cache_info = {
            "path": "/dev/sdb",
            "usage": "cache",
            "device_id": "scsi-0QEMU_QEMU_HARDDISK_2604fb72-21ac-4781-b-part4",
            "uuid": "test_uuid",
            "part_uuid": "test_part_uuid",
            "errflags": 0,
            "warnflags": 0,
            "used_size": 0,
            "total_size": 1024000,
            "num_used": 0,
            "status": 0,
            "num_io_errors": 0,
            "num_checksum_errors": 0,
            "num_slow_io": 0,
        }
        mock_cache = namedtuple("GenericDict", mock_cache_info.keys())(**mock_cache_info)
        mock_get_disk_or_partition_info.return_value = {"test_uuid": mock_cache}

        loop = IsolateDiskLoop()
        loop.chunk_client = ZbsChunk()

        chunk_caches = loop._list_chunk_caches()

        assert chunk_caches is not None
        assert mock_get_disk_or_partition_info.called
        mock_get_disk_or_partition_info.assert_called_once_with(
            usages=[DISK_OR_PARTITION_USAGE_CACHE],
            chunk_client=loop.chunk_client,
            raise_error=True,
            use_nt=True,
        )

    with patch.object(StorageDiskManager, "get_disk_or_partition_info") as mock_get_disk_or_partition_info:
        mock_get_disk_or_partition_info.side_effect = Exception("error")

        loop = IsolateDiskLoop()
        loop.chunk_client = ZbsChunk()

        chunk_caches = loop._list_chunk_caches()

        assert chunk_caches is None
        assert mock_get_disk_or_partition_info.called


class _MockJournal:
    def __init__(self):
        self.num_io_errors = 0
        self.num_checksum_errors = 0
        self.num_slow_io = 0
        self.path = "/dev/sdc"
        self.device_id = "scsi-0QEMU_QEMU_HARDDISK_2604fb72-21ac-4781-b-part4"
        self.uuid = str(uuid4())
        self.part_uuid = "test_part_uuid"
        self.errflags = 0
        self.warnflags = 0
        self.used_size = 0
        self.num_used = 0
        self.status = 0


def test_list_chunk_journals():
    with patch.object(StorageDiskManager, "get_disk_or_partition_info") as mock_get_disk_or_partition_info:
        mock_journal_info = {
            "path": "/dev/sdc",
            "usage": "journal",
            "device_id": "scsi-0QEMU_QEMU_HARDDISK_2604fb72-21ac-4781-b-part4",
            "uuid": "test_uuid",
            "part_uuid": "test_part_uuid",
            "errflags": 0,
            "warnflags": 0,
            "used_size": 0,
            "num_used": 0,
            "status": 0,
            "num_io_errors": 0,
            "num_checksum_errors": 0,
            "num_slow_io": 0,
        }
        mock_journal = namedtuple("GenericDict", mock_journal_info.keys())(**mock_journal_info)
        mock_get_disk_or_partition_info.return_value = {"test_uuid": mock_journal}

        loop = IsolateDiskLoop()
        loop.chunk_client = ZbsChunk()

        chunk_journals = loop._list_chunk_journals()

        assert chunk_journals is not None
        assert mock_get_disk_or_partition_info.called
        mock_get_disk_or_partition_info.assert_called_once_with(
            usages=[DISK_OR_PARTITION_USAGE_JOURNAL],
            chunk_client=loop.chunk_client,
            raise_error=True,
            use_nt=True,
        )

    with patch.object(StorageDiskManager, "get_disk_or_partition_info") as mock_get_disk_or_partition_info:
        mock_get_disk_or_partition_info.side_effect = Exception("error")

        loop = IsolateDiskLoop()
        loop.chunk_client = ZbsChunk()

        chunk_journals = loop._list_chunk_journals()

        assert chunk_journals is None
        assert mock_get_disk_or_partition_info.called


def test_get_part_from_chunk_for_disk():
    with patch(
        "disk_healthd.check_loops.isolate_disk_loop.StorageDiskManager.is_chunk_part_belong_to_disk"
    ) as mock_is_chunk_part_belong_to_disk:
        mock_is_chunk_part_belong_to_disk.return_value = True

        loop = IsolateDiskLoop()
        mock_disk = {"name": "sda"}
        mock_part_01 = _MockPart()
        mock_part_02 = _MockPart()
        mock_partitions_in_chunk = {
            "1234-**************": mock_part_01,
            "**************-4567": mock_part_02,
        }
        part_in_chunk = loop._get_part_from_chunk_for_disk(mock_disk, mock_partitions_in_chunk)

        assert part_in_chunk is not None
        assert part_in_chunk.path == "/dev/sda"
        mock_chunk_part = {
            "usage": "partition",
            "path": mock_part_01.path,
            "device_id": mock_part_01.device_id,
            "uuid": mock_part_01.uuid,
            "part_uuid": mock_part_01.part_uuid,
        }
        mock_is_chunk_part_belong_to_disk.assert_called_once_with(mock_chunk_part, mock_disk)


def test_check_lsm_support_isolate():
    with patch("disk_healthd.check_loops.isolate_disk_loop.ZbsChunk") as MockZbsChunk:
        # case 1: lsm support isolate
        mock_chunk_client = MockZbsChunk.return_value
        mock_chunk_client.summary_info.return_value = Mock(
            instances_response=[
                Mock(lsm_capability=chunk.LSM_CAP_CACHE_ISOLATE)
            ]
        )

        loop = IsolateDiskLoop()
        loop.chunk_client = mock_chunk_client

        assert loop._check_lsm_support_isolate()
        mock_chunk_client.summary_info.assert_called_once_with(keep_v2_message_format=True)

        # case 2: lsm not support isolate
        MockZbsChunk.reset_mock()

        mock_chunk_client = MockZbsChunk.return_value
        mock_chunk_client.summary_info.return_value = Mock(instances_response=[Mock(lsm_capability=0)])

        loop = IsolateDiskLoop()
        loop.chunk_client = mock_chunk_client

        assert not loop._check_lsm_support_isolate()
        mock_chunk_client.summary_info.assert_called_once_with(keep_v2_message_format=True)

        # case 3: has exception
        MockZbsChunk.reset_mock()

        mock_chunk_client = MockZbsChunk.return_value
        mock_chunk_client.summary_info.side_effect = Exception("error")

        loop = IsolateDiskLoop()
        loop.chunk_client = mock_chunk_client

        assert loop._check_lsm_support_isolate() is None
        mock_chunk_client.summary_info.assert_called_once_with(keep_v2_message_format=True)


def test_check_meta_chunk_list_healthy():
    loop = IsolateDiskLoop()
    assert loop._check_meta_chunk_list_healthy() is True


def test_max_isolate_size():
    loop = IsolateDiskLoop()
    assert loop._max_isolate_size() is not None


def test_max_single_node_healthy_partition_num():
    with (
        patch("disk_healthd.check_loops.isolate_disk_loop.ZbsMeta") as MockZbsMeta,
        patch("disk_healthd.check_loops.isolate_disk_loop.ZbsChunk") as MockZbsChunk,
    ):
        mock_meta_client = MockZbsMeta.return_value
        # 184937482:*********** 201714698:***********
        mock_meta_client.chunk_list.return_value = Mock(
            chunks=[Mock(rpc_ip=184937482), Mock(rpc_ip=201714698)],
        )
        mock_zbs_chunk_1 = MagicMock(rpc_ip=184937482)
        mock_zbs_chunk_2 = MagicMock(rpc_ip=201714698)
        mock_zbs_chunk_1.partition_list.return_value = Mock(
            instances_response=[
                Mock(partitions=[
                    Mock(errflags=0, warnflags=0, status=chunk.PARTITION_MOUNTED),
                    Mock(errflags=0, warnflags=0, status=chunk.PARTITION_MOUNTED),
                    Mock(errflags=0, warnflags=0, status=chunk.PARTITION_MOUNTED),
                    Mock(errflags=0, warnflags=0, status=chunk.PARTITION_MOUNTED),
                    Mock(errflags=1, warnflags=0, status=chunk.PARTITION_MOUNTED),
                    Mock(errflags=0, warnflags=1, status=chunk.PARTITION_MOUNTED),
                ]),
            ],
        )
        mock_zbs_chunk_2.partition_list.return_value = Mock(
            instances_response=[
                Mock(partitions=[
                    Mock(errflags=0, warnflags=0, status=chunk.PARTITION_MOUNTED),
                    Mock(errflags=0, warnflags=0, status=chunk.PARTITION_MOUNTED),
                    Mock(errflags=0, warnflags=0, status=chunk.PARTITION_STAGING),
                    Mock(errflags=0, warnflags=1, status=chunk.PARTITION_MOUNTED),
                    Mock(errflags=1, warnflags=0, status=chunk.PARTITION_MOUNTED),
                ]),
            ],
        )
        MockZbsChunk.side_effect = [None, mock_zbs_chunk_1, mock_zbs_chunk_2]
        mock_sd_service = Mock(CURRENT_DATA_IP="***********")

        loop = IsolateDiskLoop()
        loop.sd_service = mock_sd_service

        max_healthy_part_num = loop._max_single_node_healthy_partition_num()

        assert max_healthy_part_num == 3
        mock_meta_client.chunk_list.assert_called_once_with()
        MockZbsChunk.assert_has_calls([
            call("***********"),
            call("***********"),
        ])
        mock_zbs_chunk_1.partition_list.assert_called_once_with(keep_v2_message_format=True)
        mock_zbs_chunk_2.partition_list.assert_called_once_with(keep_v2_message_format=True)


def test_max_single_node_healthy_cache_num():
    with (
        patch("disk_healthd.check_loops.isolate_disk_loop.ZbsMeta") as MockZbsMeta,
        patch("disk_healthd.check_loops.isolate_disk_loop.ZbsChunk") as MockZbsChunk,
    ):
        mock_meta_client = MockZbsMeta.return_value
        # 184937482:*********** 201714698:***********
        mock_meta_client.chunk_list.return_value = Mock(
            chunks=[Mock(rpc_ip=184937482), Mock(rpc_ip=201714698)],
        )
        mock_zbs_chunk_1 = MagicMock(rpc_ip=184937482)
        mock_zbs_chunk_2 = MagicMock(rpc_ip=201714698)
        mock_zbs_chunk_1.cache_list.return_value = Mock(
            instances_response=[
                Mock(caches=[
                    Mock(errflags=0, warnflags=0, status=chunk.CACHE_MOUNTED),
                    Mock(errflags=0, warnflags=0, status=chunk.CACHE_MOUNTED),
                    Mock(errflags=0, warnflags=0, status=chunk.CACHE_MOUNTED),
                    Mock(errflags=0, warnflags=0, status=chunk.CACHE_MIGRATING),
                    Mock(errflags=1, warnflags=0, status=chunk.CACHE_MOUNTED),
                    Mock(errflags=0, warnflags=1, status=chunk.CACHE_MOUNTED),
                ]),
            ],
        )
        mock_zbs_chunk_2.cache_list.return_value = Mock(
            instances_response=[
                Mock(caches=[
                    Mock(errflags=0, warnflags=0, status=chunk.CACHE_MOUNTED),
                    Mock(errflags=0, warnflags=0, status=chunk.CACHE_MOUNTED),
                    Mock(errflags=0, warnflags=0, status=chunk.CACHE_STAGING),
                    Mock(errflags=0, warnflags=1, status=chunk.CACHE_MOUNTED),
                    Mock(errflags=1, warnflags=0, status=chunk.CACHE_MOUNTED),
                ]),
            ],
        )
        MockZbsChunk.side_effect = [None, mock_zbs_chunk_1, mock_zbs_chunk_2]
        mock_sd_service = Mock(CURRENT_DATA_IP="***********")

        loop = IsolateDiskLoop()
        loop.sd_service = mock_sd_service

        max_healthy_part_num = loop._max_single_node_healthy_cache_num()

        assert max_healthy_part_num == 2
        mock_meta_client.chunk_list.assert_called_once_with()
        MockZbsChunk.assert_has_calls([
            call("***********"),
            call("***********"),
        ])
        mock_zbs_chunk_1.cache_list.assert_called_once_with(keep_v2_message_format=True)
        mock_zbs_chunk_2.cache_list.assert_called_once_with(keep_v2_message_format=True)


def test_get_cluster_isolate_partition_size():
    sd_views = {
        "resource_name": "slow_disks",
        "version": -2,
        "hosts": [
            {
                "data_ip": "*******",
                "slow_disks": [
                    {
                        "disk_name": "sda",
                        "disk_serial": "abc123",
                        "status": slow_disk.MARK_TO_ISOLATE,
                        "chunk_part_size": 1,
                    }
                ],
            },
            {
                "data_ip": "*******",
                "slow_disks": [
                    {
                        "disk_name": "sda",
                        "disk_serial": "abc123",
                        "status": slow_disk.CHUNK_INVOLVED,
                        "chunk_part_size": 2,
                    }
                ],
            },
            {
                "data_ip": "*******",
                "slow_disks": [
                    {
                        "disk_name": "sda",
                        "disk_serial": "abc123",
                        "status": slow_disk.CHUNK_INVOLVED,
                    }
                ],
            },
        ],
    }
    loop = IsolateDiskLoop()
    assert loop._get_cluster_isolate_partition_size(sd_views) == 3


def test_get_cluster_isolating_or_umounting_num():
    with (
        patch("disk_healthd.check_loops.isolate_disk_loop.ZbsMeta") as MockZbsMeta,
        patch("disk_healthd.check_loops.isolate_disk_loop.ZbsChunk") as MockZbsChunk,
    ):
        mock_meta_client = MockZbsMeta.return_value
        # 184937482:*********** 201714698:***********
        mock_meta_client.chunk_list.return_value = Mock(
            chunks=[Mock(rpc_ip=184937482), Mock(rpc_ip=201714698)],
        )
        mock_zbs_chunk_1 = MagicMock(rpc_ip=184937482)
        mock_zbs_chunk_2 = MagicMock(rpc_ip=201714698)
        mock_zbs_chunk_1.partition_list.return_value = Mock(
            instances_response=[
                Mock(partitions=[
                    Mock(errflags=0, warnflags=0, status=chunk.PARTITION_MOUNTED),
                    Mock(errflags=0, warnflags=0, status=chunk.PARTITION_STAGING),
                    Mock(errflags=0, warnflags=0, status=chunk.PARTITION_MIGRATING),
                    Mock(errflags=0, warnflags=1, status=chunk.PARTITION_MOUNTED),
                ]),
            ],
        )
        mock_zbs_chunk_1.cache_list.return_value = Mock(
            instances_response=[
                Mock(caches=[
                    Mock(errflags=0, warnflags=0, status=chunk.CACHE_MOUNTED),
                    Mock(errflags=0, warnflags=0, status=chunk.CACHE_STAGING),
                    Mock(errflags=0, warnflags=0, status=chunk.CACHE_MIGRATING),
                    Mock(errflags=0, warnflags=1, status=chunk.CACHE_MOUNTED),
                ]),
            ],
        )
        mock_zbs_chunk_1.journal_list.return_value = Mock(
            instances_response=[
                Mock(groups=[
                    Mock(errflags=0, warnflags=0, status=chunk.JOURNALGROUP_STAGING),
                    Mock(errflags=0, warnflags=0, status=chunk.JOURNALGROUP_MOUNTED),
                ]),
            ],
        )
        mock_zbs_chunk_2.partition_list.return_value = Mock(
            instances_response=[
                Mock(partitions=[
                    Mock(errflags=0, warnflags=0, status=chunk.PARTITION_MOUNTED),
                    Mock(errflags=0, warnflags=0, status=chunk.PARTITION_STAGING),
                    Mock(errflags=0, warnflags=0, status=chunk.PARTITION_MIGRATING),
                    Mock(errflags=0, warnflags=1, status=chunk.PARTITION_MOUNTED),
                ]),
            ],
        )
        mock_zbs_chunk_2.cache_list.return_value = Mock(
            instances_response=[
                Mock(caches=[
                    Mock(errflags=0, warnflags=0, status=chunk.CACHE_MOUNTED),
                    Mock(errflags=0, warnflags=0, status=chunk.CACHE_STAGING),
                    Mock(errflags=0, warnflags=0, status=chunk.CACHE_MIGRATING),
                    Mock(errflags=0, warnflags=1, status=chunk.CACHE_MOUNTED),
                ]),
            ],
        )
        mock_zbs_chunk_2.journal_list.return_value = Mock(
            instances_response=[
                Mock(groups=[
                    Mock(errflags=0, warnflags=0, status=chunk.JOURNALGROUP_STAGING),
                    Mock(errflags=0, warnflags=0, status=chunk.JOURNALGROUP_MOUNTED),
                ]),
            ],
        )

        MockZbsChunk.side_effect = [None, mock_zbs_chunk_1, mock_zbs_chunk_2]

        loop = IsolateDiskLoop()

        cluster_isolating_or_umounting_num = loop._get_cluster_isolating_or_umounting_num()

        assert cluster_isolating_or_umounting_num == 14


def test_get_cluster_isolate_partition_num():
    sd_views = {
        "resource_name": "slow_disks",
        "version": -2,
        "hosts": [
            {
                "data_ip": "*******",
                "slow_disks": [
                    {
                        "disk_name": "sda",
                        "disk_serial": "abc123",
                        "status": slow_disk.MARK_TO_ISOLATE,
                        "chunk_part_size": 1,
                    }
                ],
            },
            {
                "data_ip": "*******",
                "slow_disks": [
                    {
                        "disk_name": "sda",
                        "disk_serial": "abc123",
                        "status": slow_disk.CHUNK_INVOLVED,
                        "chunk_part_size": 2,
                    },
                    {
                        "disk_name": "sda",
                        "disk_serial": "abc123",
                        "status": slow_disk.CAP_EXCLUDED,
                    },
                ],
            },
        ],
    }
    loop = IsolateDiskLoop()
    assert loop._get_cluster_isolate_partition_num(sd_views) == 2


def test_get_cluster_isolate_cache_num():
    sd_views = {
        "resource_name": "slow_disks",
        "version": -2,
        "hosts": [
            {
                "data_ip": "*******",
                "slow_disks": [
                    {
                        "disk_name": "sda",
                        "disk_serial": "abc123",
                        "status": slow_disk.MARK_TO_ISOLATE,
                        "chunk_cache_size": 1,
                    }
                ],
            },
            {
                "data_ip": "*******",
                "slow_disks": [
                    {
                        "disk_name": "sda",
                        "disk_serial": "abc123",
                        "status": slow_disk.CHUNK_INVOLVED,
                        "chunk_part_size": 2,
                    },
                    {
                        "disk_name": "sda",
                        "disk_serial": "abc123",
                        "status": slow_disk.CAP_EXCLUDED,
                    },
                ],
            },
        ],
    }
    loop = IsolateDiskLoop()
    assert loop._get_cluster_isolate_cache_num(sd_views) == 1


def test_need_to_hint_chunk():
    loop = IsolateDiskLoop()
    sd_views = {
        "resource_name": "slow_disks",
        "version": -2,
        "hosts": [
            {
                "data_ip": loop.sd_service.CURRENT_DATA_IP,
                "slow_disks": [
                    {
                        "disk_name": "sda",
                        "disk_serial": "abc123",
                        "status": slow_disk.MARK_TO_ISOLATE,
                        "chunk_part_size": 1,
                    }
                ],
            }
        ],
    }

    assert loop._need_to_hint_chunk(sd_views) is True


def test_process_slow_disk():
    with (
        patch.object(IsolateDiskLoop, "_check_lsm_support_isolate") as mock_check_lsm_support_isolate,
        patch.object(IsolateDiskLoop, "_check_meta_chunk_list_healthy") as mock_check_meta_chunk_list_healthy,
        patch.object(IsolateDiskLoop, "_list_chunk_partitions") as mock_list_chunk_partitions,
        patch.object(IsolateDiskLoop, "_list_chunk_caches") as mock_list_chunk_caches,
        patch.object(IsolateDiskLoop, "_list_chunk_journals") as mock_list_chunk_journals,
        patch("disk_healthd.check_loops.isolate_disk_loop.HostService.get_current_host_info") as mock_get_host_info,
        patch.object(IsolateDiskLoop, "_update_normal_state") as mock_update_normal_state,
        patch.object(IsolateDiskLoop, "_try_mark_to_isolate") as mock_try_mark_to_isolate,
        patch.object(IsolateDiskLoop, "_need_to_hint_chunk") as mock_need_to_hint_chunk,
        patch.object(IsolateDiskLoop, "_hint_slow_disk_to_chunk") as mock_hint_slow_disk_to_chunk,
        patch("disk_healthd.check_loops.isolate_disk_loop.SlowDiskService.save_record") as mock_save_record,
    ):
        mock_check_lsm_support_isolate.return_value = True
        mock_check_meta_chunk_list_healthy.return_value = True
        mock_chunk_partition = {item.uuid: item for item in [_MockPart(), _MockPart()]}
        mock_list_chunk_partitions.return_value = mock_chunk_partition
        mock_chunk_cache = {item.uuid: item for item in [_MockCache(), _MockCache()]}
        mock_list_chunk_caches.return_value = mock_chunk_cache
        mock_chunk_journal = {item.uuid: item for item in [_MockJournal(), _MockJournal()]}
        mock_list_chunk_journals.return_value = mock_chunk_journal
        mock_partitions_in_chunk = {
            "partition": mock_chunk_partition,
            "cache": mock_chunk_cache,
            "journal": mock_chunk_journal,
        }
        mock_get_host_info.return_value = {}
        mock_update_normal_state.return_value = True
        mock_try_mark_to_isolate.return_value = True
        mock_need_to_hint_chunk.return_value = True
        mock_save_record.return_value = True

        loop = IsolateDiskLoop()
        loop.sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sda",
                            "disk_serial": "abc123",
                            "status": slow_disk.MARK_TO_ISOLATE,
                            "chunk_part_size": 1,
                        }
                    ],
                }
            ],
        }
        loop.process_slow_disk()

        mock_check_lsm_support_isolate.assert_called_once_with()
        mock_check_meta_chunk_list_healthy.assert_called_once_with()
        mock_list_chunk_partitions.assert_called_once_with()
        mock_list_chunk_caches.assert_called_once_with()
        mock_list_chunk_journals.assert_called_once_with()
        mock_get_host_info.assert_called_once_with()
        mock_update_normal_state.assert_called_once_with(
            loop.sd_views,
            mock_partitions_in_chunk,
            {},
        )
        mock_try_mark_to_isolate.assert_called_once_with(
            loop.sd_views,
            mock_partitions_in_chunk,
            {},
        )
        mock_need_to_hint_chunk.assert_called_once_with(loop.sd_views)
        mock_save_record.assert_called_once_with(loop.sd_views)
        mock_hint_slow_disk_to_chunk.assert_called_once_with(loop.sd_views)


def test_process_slow_disk_list_chunk_caches_failed():
    with (
        patch.object(IsolateDiskLoop, "_check_lsm_support_isolate") as mock_check_lsm_support_isolate,
        patch.object(IsolateDiskLoop, "_check_meta_chunk_list_healthy") as mock_check_meta_chunk_list_healthy,
        patch.object(IsolateDiskLoop, "_list_chunk_partitions") as mock_list_chunk_partitions,
        patch.object(IsolateDiskLoop, "_list_chunk_caches") as mock_list_chunk_caches,
    ):
        mock_check_lsm_support_isolate.return_value = True
        mock_check_meta_chunk_list_healthy.return_value = True
        mock_chunk_partition = {item.uuid: item for item in [_MockPart(), _MockPart()]}
        mock_list_chunk_partitions.return_value = mock_chunk_partition
        mock_list_chunk_caches.return_value = None

        loop = IsolateDiskLoop()
        loop.sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sda",
                            "disk_serial": "abc123",
                            "status": slow_disk.MARK_TO_ISOLATE,
                            "chunk_part_size": 1,
                        }
                    ],
                }
            ],
        }
        loop.process_slow_disk()

        mock_check_lsm_support_isolate.assert_called_once_with()
        mock_check_meta_chunk_list_healthy.assert_called_once_with()
        mock_list_chunk_partitions.assert_called_once_with()
        mock_list_chunk_caches.assert_called_once_with()


def test_process_slow_disk_list_chunk_journals_failed():
    with (
        patch.object(IsolateDiskLoop, "_check_lsm_support_isolate") as mock_check_lsm_support_isolate,
        patch.object(IsolateDiskLoop, "_check_meta_chunk_list_healthy") as mock_check_meta_chunk_list_healthy,
        patch.object(IsolateDiskLoop, "_list_chunk_partitions") as mock_list_chunk_partitions,
        patch.object(IsolateDiskLoop, "_list_chunk_caches") as mock_list_chunk_caches,
        patch.object(IsolateDiskLoop, "_list_chunk_journals") as mock_list_chunk_journals,
    ):
        mock_check_lsm_support_isolate.return_value = True
        mock_check_meta_chunk_list_healthy.return_value = True
        mock_chunk_partition = {item.uuid: item for item in [_MockPart(), _MockPart()]}
        mock_list_chunk_partitions.return_value = mock_chunk_partition
        mock_chunk_cache = {item.uuid: item for item in [_MockCache(), _MockCache()]}
        mock_list_chunk_caches.return_value = mock_chunk_cache
        mock_list_chunk_journals.return_value = None

        loop = IsolateDiskLoop()
        loop.sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sda",
                            "disk_serial": "abc123",
                            "status": slow_disk.MARK_TO_ISOLATE,
                            "chunk_part_size": 1,
                        }
                    ],
                }
            ],
        }
        loop.process_slow_disk()

        mock_check_lsm_support_isolate.assert_called_once_with()
        mock_check_meta_chunk_list_healthy.assert_called_once_with()
        mock_list_chunk_partitions.assert_called_once_with()
        mock_list_chunk_caches.assert_called_once_with()
        mock_list_chunk_journals.assert_called_once_with()


def test_update_normal_state():
    with (
        patch("disk_healthd.check_loops.isolate_disk_loop.disk_map") as mock_disk_map,
        patch.object(IsolateDiskLoop, "_get_part_from_chunk_for_disk") as mock_get_partition_from_chunk,
        patch.object(IsolateDiskLoop, "_get_chunk_part_map") as mock_get_chunk_part_map,
    ):
        mock_disk_map.disk_serials = {
            "sda123": {
                "name": "sda",
                "path": "/dev/sda",
            },
            "sdc123": {
                "name": "sdc",
                "path": "/dev/sdc",
                "is_rotational": True,
            },
            "sdd123": {
                "name": "sdd",
                "path": "/dev/sdd",
                "is_rotational": True,
            },
            "sde123": {
                "name": "sde",
                "path": "/dev/sde",
                "is_rotational": True,
            },
            "sdf123": {
                "name": "sdf",
                "path": "/dev/sdf",
                "is_rotational": False,
            },
            "sdg123": {
                "name": "sdg",
                "path": "/dev/sdg",
                "is_rotational": False,
            },
            "sdh123": {
                "name": "sdh",
                "path": "/dev/sdh",
                "is_rotational": False,
            },
            "sdj123": {
                "name": "sdj",
                "path": "/dev/sdj",
                "is_rotational": True,
            },
            "sdk123": {
                "name": "sdk",
                "path": "/dev/sdk",
                "is_rotational": True,
            },
            "sdl123": {
                "name": "sdl",
                "path": "/dev/sdl",
                "is_rotational": False,
            },
            "sdm123": {
                "name": "sdm",
                "path": "/dev/sdm",
                "is_rotational": False,
            },
            "sdn123": {
                "name": "sdn",
                "path": "/dev/sdn",
                "is_rotational": False,
            },
            "sdo123": {
                "name": "sdo",
                "path": "/dev/sdo",
                "is_rotational": False,
            },
        }
        mock_get_partition_from_chunk.side_effect = [
            None,
            Mock(path="/dev/sdd1", errflags=0, warnflags=0),
            Mock(path="/dev/sde1", errflags=1, warnflags=1),
            None,
            Mock(path="/dev/sdk1", errflags=1, warnflags=1),
        ]
        mock_get_chunk_part_map.side_effect = [
            {},
            {
                "partition": Mock(path="/dev/sdg1"),
                "journal": None,
            },
            {
                "partition": Mock(path="/dev/sdh1", errflags=0, warnflags=1),
                "journal": Mock(path="/dev/sdh2"),
            },
            {},
            {
                "partition": Mock(path="/dev/sdm1", errflags=0, warnflags=0),
                "journal": None,
            },
            {
                "partition": Mock(path="/dev/sdn1", errflags=0, warnflags=0),
                "cache": Mock(path="/dev/sdn2", errflags=0, warnflags=0),
                "journal": Mock(path="/dev/sdn3", errflags=1, warnflags=0),
            },
            {
                "partition": None,
                "cache": Mock(path="/dev/sdo2", errflags=0, warnflags=0),
                "journal": Mock(path="/dev/sdo3", errflags=0, warnflags=0),
            }
        ]

        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda123",
                            "status": slow_disk.CHUNK_INVOLVED,
                        },
                        {
                            "disk_name": "sdb",
                            "disk_serial": "sdb123",
                            "status": slow_disk.MARK_TO_ISOLATE,
                        },
                        {
                            "disk_name": "sdc",
                            "disk_serial": "sdc123",
                            "status": slow_disk.MARK_TO_ISOLATE,
                        },
                        {
                            "disk_name": "sdd",
                            "disk_serial": "sdd123",
                            "status": slow_disk.MARK_TO_ISOLATE,
                        },
                        {
                            "disk_name": "sde",
                            "disk_serial": "sde123",
                            "status": slow_disk.MARK_TO_ISOLATE,
                        },
                        {
                            "disk_name": "sdf",
                            "disk_serial": "sdf123",
                            "status": slow_disk.MARK_TO_ISOLATE,
                        },
                        {
                            "disk_name": "sdg",
                            "disk_serial": "sdg123",
                            "status": slow_disk.MARK_TO_ISOLATE,
                        },
                        {
                            "disk_name": "sdh",
                            "disk_serial": "sdh123",
                            "status": slow_disk.MARK_TO_ISOLATE,
                        },
                        {
                            "disk_name": "sdi",
                            "disk_serial": "sdi123",
                            "status": slow_disk.SLOW_DETECTED,
                        },
                        {
                            "disk_name": "sdj",
                            "disk_serial": "sdj123",
                            "status": slow_disk.SLOW_DETECTED,
                        },
                        {
                            "disk_name": "sdk",
                            "disk_serial": "sdk123",
                            "status": slow_disk.SLOW_DETECTED,
                        },
                        {
                            "disk_name": "sdl",
                            "disk_serial": "sdl123",
                            "status": slow_disk.SLOW_DETECTED,
                        },
                        {
                            "disk_name": "sdm",
                            "disk_serial": "sdm123",
                            "status": slow_disk.SLOW_DETECTED,
                        },
                        {
                            "disk_name": "sdn",
                            "disk_serial": "sdn123",
                            "status": slow_disk.SLOW_DETECTED,
                        },
                        {
                            "disk_name": "sdo",
                            "disk_serial": "sdo123",
                            "status": slow_disk.SLOW_DETECTED,
                        },
                    ],
                }
            ],
        }
        mock_partitions_in_chunk = {
            "partition": {},
            "cache": {},
            "journal": {},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._update_normal_state(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is True
        mock_get_partition_from_chunk.assert_has_calls([
            call(
                {
                    "name": "sdc",
                    "path": "/dev/sdc",
                    "is_rotational": True,
                },
                mock_partitions_in_chunk["partition"],
                usage="partition",
            ),
            call(
                {
                    "name": "sdd",
                    "path": "/dev/sdd",
                    "is_rotational": True,
                },
                mock_partitions_in_chunk["partition"],
                usage="partition",
            ),
            call(
                {
                    "name": "sde",
                    "path": "/dev/sde",
                    "is_rotational": True,
                },
                mock_partitions_in_chunk["partition"],
                usage="partition",
            ),
            call(
                {
                    "name": "sdj",
                    "path": "/dev/sdj",
                    "is_rotational": True,
                },
                mock_partitions_in_chunk["partition"],
                usage="partition",
            ),
            call(
                {
                    "name": "sdk",
                    "path": "/dev/sdk",
                    "is_rotational": True,
                },
                mock_partitions_in_chunk["partition"],
                usage="partition",
            ),
        ])
        mock_get_chunk_part_map.assert_has_calls([
            call(
                {
                    "name": "sdf",
                    "path": "/dev/sdf",
                    "is_rotational": False,
                },
                mock_host_info,
                mock_partitions_in_chunk,
            ),
            call(
                {
                    "name": "sdg",
                    "path": "/dev/sdg",
                    "is_rotational": False,
                },
                mock_host_info,
                mock_partitions_in_chunk,
            ),
            call(
                {
                    "name": "sdh",
                    "path": "/dev/sdh",
                    "is_rotational": False,
                },
                mock_host_info,
                mock_partitions_in_chunk,
            ),
            call(
                {
                    "name": "sdl",
                    "path": "/dev/sdl",
                    "is_rotational": False,
                },
                mock_host_info,
                mock_partitions_in_chunk,
            ),
            call(
                {
                    "name": "sdm",
                    "path": "/dev/sdm",
                    "is_rotational": False,
                },
                mock_host_info,
                mock_partitions_in_chunk,
            ),
            call(
                {
                    "name": "sdn",
                    "path": "/dev/sdn",
                    "is_rotational": False,
                },
                mock_host_info,
                mock_partitions_in_chunk,
            ),
            call(
                {
                    "name": "sdo",
                    "path": "/dev/sdo",
                    "is_rotational": False,
                },
                mock_host_info,
                mock_partitions_in_chunk,
            ),
        ])


def test_hint_slow_disk_to_chunk():
    with (
        patch.object(ZbsChunk, "partition_isolate") as mock_partition_isolate,
        patch.object(ZbsChunk, "cache_isolate") as mock_cache_isolate,
        patch.object(ZbsChunk, "journal_umount") as mock_journal_umount,
    ):
        loop = IsolateDiskLoop()
        loop.chunk_client = ZbsChunk()
        sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sda",
                            "disk_serial": "abc123",
                            "status": slow_disk.MARK_TO_ISOLATE,
                            "expect_status": slow_disk.MARK_TO_ISOLATE,
                            "chunk_part_size": 1,
                            "part_uuid_in_chunk": "123",
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "abc123",
                            "status": slow_disk.MARK_TO_ISOLATE,
                            "expect_status": slow_disk.MARK_TO_ISOLATE,
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "abc123",
                            "status": slow_disk.MARK_TO_ISOLATE,
                            "expect_status": slow_disk.MARK_TO_ISOLATE,
                            "chunk_cache_size": 1,
                            "cache_uuid_in_chunk": "123",
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "abc123",
                            "status": slow_disk.MARK_TO_ISOLATE,
                            "expect_status": slow_disk.MARK_TO_ISOLATE,
                            "journal_uuid_in_chunk": "123",
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "abc123",
                            "status": slow_disk.CAP_EXCLUDED,
                            "expect_status": slow_disk.CAP_EXCLUDED,
                            "chunk_part_size": 1,
                            "part_uuid_in_chunk": "123",
                        },
                    ],
                }
            ],
        }

        loop._hint_slow_disk_to_chunk(sd_views)
        assert mock_partition_isolate.call_count == 1
        assert mock_cache_isolate.call_count == 1
        assert mock_journal_umount.call_count == 1


def test_hint_slow_disk_to_chunk_failed_with_exception():
    with (
        patch.object(ZbsChunk, "partition_isolate") as mock_partition_isolate,
        patch.object(ZbsChunk, "cache_isolate") as mock_cache_isolate,
        patch.object(ZbsChunk, "journal_umount") as mock_journal_umount,
    ):
        mock_partition_isolate.side_effect = Exception("Partition isolate failed")
        mock_cache_isolate.side_effect = Exception("Cache isolate failed")
        mock_journal_umount.side_effect = Exception("Journal umount failed")

        loop = IsolateDiskLoop()
        loop.chunk_client = ZbsChunk()
        sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sda",
                            "disk_serial": "abc123",
                            "status": slow_disk.MARK_TO_ISOLATE,
                            "expect_status": slow_disk.MARK_TO_ISOLATE,
                            "chunk_part_size": 1,
                            "part_uuid_in_chunk": "123",
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "abc123",
                            "status": slow_disk.MARK_TO_ISOLATE,
                            "expect_status": slow_disk.MARK_TO_ISOLATE,
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "abc123",
                            "status": slow_disk.MARK_TO_ISOLATE,
                            "expect_status": slow_disk.MARK_TO_ISOLATE,
                            "chunk_cache_size": 1,
                            "cache_uuid_in_chunk": "123",
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "abc123",
                            "status": slow_disk.MARK_TO_ISOLATE,
                            "expect_status": slow_disk.MARK_TO_ISOLATE,
                            "journal_uuid_in_chunk": "123",
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "abc123",
                            "status": slow_disk.CAP_EXCLUDED,
                            "expect_status": slow_disk.CAP_EXCLUDED,
                            "chunk_part_size": 1,
                            "part_uuid_in_chunk": "123",
                        },
                    ],
                }
            ],
        }

        loop._hint_slow_disk_to_chunk(sd_views)
        assert mock_partition_isolate.call_count == 1
        assert mock_cache_isolate.call_count == 1
        assert mock_journal_umount.call_count == 1


def test_get_chunk_part_map():
    with (
        patch.object(IsolateDiskLoop, "_get_disk_function") as mock_get_disk_function,
        patch.object(IsolateDiskLoop, "_get_disk_usage_in_chunk") as mock_get_disk_usage_in_chunk,
        patch.object(IsolateDiskLoop, "_get_part_from_chunk_for_disk") as mock_get_partition_from_chunk,
    ):
        mock_disk_info = {"name": "sda"}
        mock_host_info = {"disks": [{"name": "sda", "serial": "test_serial", "function": "data"}]}
        mock_partitions_in_chunk = {
            "partition": [],
            "cache": [],
            "journal": [],
        }

        # case 1: disk function is None
        mock_get_disk_function.return_value = None

        loop = IsolateDiskLoop()

        chunk_part_map = loop._get_chunk_part_map(
            mock_disk_info,
            mock_host_info,
            mock_partitions_in_chunk,
        )

        assert chunk_part_map == {}

        # case 2: chunk_partition_map is None
        mock_get_disk_function.return_value = "data"
        mock_get_disk_usage_in_chunk.return_value = None

        loop = IsolateDiskLoop()

        chunk_part_map = loop._get_chunk_part_map(
            mock_disk_info,
            mock_host_info,
            mock_partitions_in_chunk,
        )

        assert chunk_part_map == {}

        # case 3: fill chunk_partition_map
        mock_get_disk_function.return_value = "data"
        mock_get_disk_usage_in_chunk.return_value = {
            "partition": None,
            "cache": None,
            "journal": None,
        }
        mock_part_01 = _MockPart()
        mock_cache_01 = _MockCache()
        mock_journal_01 = _MockJournal()
        mock_get_partition_from_chunk.side_effect = [
            mock_part_01,
            mock_cache_01,
            mock_journal_01,
        ]

        loop = IsolateDiskLoop()

        chunk_part_map = loop._get_chunk_part_map(
            mock_disk_info,
            mock_host_info,
            mock_partitions_in_chunk,
        )

        assert chunk_part_map == {
            "partition": mock_part_01,
            "cache": mock_cache_01,
            "journal": mock_journal_01,
        }


def test_get_disk_usage_in_chunk():
    with (
        patch("disk_healthd.check_loops.isolate_disk_loop.is_faster_ssd_as_cache_enabled") as mock_ssd_as_cache,
        patch("disk_healthd.check_loops.isolate_disk_loop.is_disk_data_with_cache") as mock_is_disk_data_with_cache,
    ):
        mock_ssd_as_cache.return_value = True
        mock_is_disk_data_with_cache.return_value = True

        loop = IsolateDiskLoop()

        disk_usage_map = loop._get_disk_usage_in_chunk("data")

        assert disk_usage_map == {"partition": None, "cache": None, "journal": None}

        mock_ssd_as_cache.return_value = True
        mock_is_disk_data_with_cache.return_value = False

        loop = IsolateDiskLoop()

        disk_usage_map_of_smtx_system = loop._get_disk_usage_in_chunk("smtx_system")
        disk_usage_map_of_data = loop._get_disk_usage_in_chunk("data")
        disk_usage_map_of_cache = loop._get_disk_usage_in_chunk("cache")

        assert disk_usage_map_of_smtx_system == {"cache": None, "journal": None}
        assert disk_usage_map_of_data == {"partition": None}
        assert disk_usage_map_of_cache == {"cache": None, "journal": None}

        mock_ssd_as_cache.return_value = False
        mock_is_disk_data_with_cache.return_value = False

        loop = IsolateDiskLoop()

        disk_usage_map_of_smtx_system = loop._get_disk_usage_in_chunk("smtx_system")
        disk_usage_map_of_data = loop._get_disk_usage_in_chunk("data")

        assert disk_usage_map_of_smtx_system == {"partition": None, "journal": None}
        assert disk_usage_map_of_data == {"partition": None, "journal": None}


def test_get_disk_function():
    # case 1: disk function is not None
    mock_disk_info = {
        "serial": "test_serial",
    }
    mock_host_info = {
        "disks": [
            {
                "name": "sda",
                "serial": "test_serial",
                "function": "data",
            }
        ]
    }

    loop = IsolateDiskLoop()

    disk_function = loop._get_disk_function(mock_disk_info, mock_host_info)

    assert disk_function == "data"

    # case 2: disk function is None
    mock_disk_info = {
        "serial": "test_serial",
    }
    mock_host_info = {
        "disks": [
            {
                "name": "sda",
                "serial": "test_serial",
            }
        ]
    }

    loop = IsolateDiskLoop()

    disk_function = loop._get_disk_function(mock_disk_info, mock_host_info)

    assert disk_function is None


def test_get_high_latency_disk_in_same_hba():
    with patch("disk_healthd.check_loops.isolate_disk_loop.DiskStatusService") as MockDiskStatusService:
        mock_disk_status_service = MockDiskStatusService.return_value
        mock_disk_status_service.list_local_unhealthy_disk_status_in_one_hour.return_value = []

        loop = IsolateDiskLoop()

        high_latency_disks = loop._get_high_latency_disk_in_same_hba("test_bus_location")

        assert high_latency_disks == []
        mock_disk_status_service.list_local_unhealthy_disk_status_in_one_hour.assert_called_once_with("test_bus_location")


def test_try_mark_to_isolate_ssd():
    with (
        patch("disk_healthd.check_loops.isolate_disk_loop.disk_map") as mock_disk_map,
        patch.object(IsolateDiskLoop, "_get_part_from_chunk_for_disk") as mock_get_part_from_chunk_for_disk,
        patch.object(IsolateDiskLoop, "_max_isolate_size") as mock_max_isolate_size,
        patch.object(IsolateDiskLoop, "_get_cluster_isolate_partition_size") as mock_get_cluster_isolate_partition_size,
        patch.object(IsolateDiskLoop, "_max_single_node_healthy_partition_num") as mock_max_single_node_healthy_partition_num,
        patch.object(IsolateDiskLoop, "_get_cluster_isolate_partition_num") as mock_get_cluster_isolate_partition_num,
        patch.object(IsolateDiskLoop, "_max_single_node_healthy_cache_num") as mock_max_single_node_healthy_cache_num,
        patch.object(IsolateDiskLoop, "_get_cluster_isolate_cache_num") as mock_get_cluster_isolate_cache_num,
        patch.object(IsolateDiskLoop, "_get_cluster_isolating_or_umounting_num")  as mock_get_cluster_isolating_or_umounting_num,
        patch.object(IsolateDiskLoop, "_get_high_latency_disk_in_same_hba") as mock_get_high_latency_disk_in_same_hba,
        patch.object(IsolateDiskLoop, "_get_chunk_part_map") as mock_get_chunk_part_map,
        patch.object(IsolateDiskLoop, "_is_last_part_in_chunk") as mock_is_last_part_in_chunk,
    ):
        # case 1: no MARK_TO_ISOLATE disk & no remain todo
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [],
                }
            ],
        }

        mock_partitions_in_chunk = {
            "partition": {},
            "cache": {},
            "journal": {},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False

        # case 2: has MARK_TO_ISOLATE disk but no disk_info & no remain todo
        mock_disk_map.disk_serials = {}
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.MARK_TO_ISOLATE,
                        },
                    ],
                },
            ],
        }

        mock_partitions_in_chunk = {
            "partition": {},
            "cache": {},
            "journal": {},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False

        # case 3: has MARK_TO_ISOLATE disk & has disk_info & no remain todo
        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": False,
            }
        }
        mock_get_part_from_chunk_for_disk.side_effect = [
            Mock(path="/dev/sda1", total_size=2, uuid="sda1-new"),
            Mock(path="/dev/sda2", total_size=2, uuid="sda2-new"),
            Mock(path="/dev/sda3", uuid="sda3-new"),
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.MARK_TO_ISOLATE,
                            "chunk_part_size": 1,
                            "part_uuid_in_chunk": "sda1",
                            "chunk_cache_size": 1,
                            "cache_uuid_in_chunk": "sda2",
                            "journal_uuid_in_chunk": "sda3",
                        },
                    ],
                },
            ],
        }

        mock_partitions_in_chunk = {
            "partition": {},
            "cache": {},
            "journal": {},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is True
        mock_get_part_from_chunk_for_disk.assert_has_calls([
            call(
                {
                    "name": "sda",
                    "path": "/dev/sda",
                    "is_rotational": False,
                },
                mock_partitions_in_chunk["partition"],
                usage="partition",
            ),
            call(
                {
                    "name": "sda",
                    "path": "/dev/sda",
                    "is_rotational": False,
                },
                mock_partitions_in_chunk["cache"],
                usage="cache",
            ),
            call(
                {
                    "name": "sda",
                    "path": "/dev/sda",
                    "is_rotational": False,
                },
                mock_partitions_in_chunk["journal"],
                usage="journal",
            ),
        ])

        # case 4: no MARK_TO_ISOLATE disk & no remain todo & has SLOW_DETECTED disk
        # case 4.1: there is isolating or umounting disk in cluster
        mock_get_part_from_chunk_for_disk.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 50
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 1

        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": False,
            }
        }
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.SLOW_DETECTED,
                            "chunk_part_size": 1,
                            "part_uuid_in_chunk": "sda1",
                            "chunk_cache_size": 1,
                            "cache_uuid_in_chunk": "sda2",
                            "journal_uuid_in_chunk": "sda3",
                        },
                    ],
                },
            ],
        }

        mock_partitions_in_chunk = {
            "partition": {},
            "cache": {},
            "journal": {},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()

        # case 4.2: there is more than 2 high latency disk in same HBA in one hour
        mock_get_part_from_chunk_for_disk.reset_mock()
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 50
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_get_high_latency_disk_in_same_hba.return_value = [Mock(), Mock()]

        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": False,
                "bus_location": "sda-bus-location",
            }
        }
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.SLOW_DETECTED,
                            "chunk_part_size": 1,
                            "part_uuid_in_chunk": "sda1",
                            "chunk_cache_size": 1,
                            "cache_uuid_in_chunk": "sda2",
                            "journal_uuid_in_chunk": "sda3",
                        },
                    ],
                },
            ],
        }

        mock_partitions_in_chunk = {
            "partition": {},
            "cache": {},
            "journal": {},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_high_latency_disk_in_same_hba.assert_called_once_with("sda-bus-location")

        # case 4.3: skip slow hint
        # when there is too many unhealthy chunk caches
        # when cluster remain space under 10%
        mock_get_part_from_chunk_for_disk.reset_mock()
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_high_latency_disk_in_same_hba.reset_mock()
        mock_get_chunk_part_map.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 100
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 4
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_get_high_latency_disk_in_same_hba.return_value = [Mock()]

        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": False,
                "bus_location": "sda-bus-location",
            },
            "sdb": {
                "name": "sdb",
                "path": "/dev/sdb",
                "is_rotational": False,
                "bus_location": "sdb-bus-location",
            },
        }
        mock_get_chunk_part_map.side_effect = [
            {"cache": Mock(path="/dev/sda1", uuid="sda1-uuid")},
            {"partition": Mock(path="/dev/sdb1", uuid="sdb1-uuid")},
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.SLOW_DETECTED,
                        },
                        {
                            "disk_name": "sdb",
                            "disk_serial": "sdb",
                            "status": slow_disk.SLOW_DETECTED,
                        },
                    ],
                },
            ],
        }

        mock_partitions_in_chunk = {
            "partition": {},
            "cache": {},
            "journal": {},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_high_latency_disk_in_same_hba.assert_has_calls([
            call("sda-bus-location"),
            call("sdb-bus-location"),
        ])

        # case 4.3: skip slow hint
        # when there is too many unhealthy chunk partitions
        mock_get_part_from_chunk_for_disk.reset_mock()
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_high_latency_disk_in_same_hba.reset_mock()
        mock_get_chunk_part_map.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 50
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 4
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 4
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_get_high_latency_disk_in_same_hba.return_value = [Mock()]

        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": False,
                "bus_location": "sda-bus-location",
            },
        }
        mock_get_chunk_part_map.side_effect = [
            {"partition": Mock(path="/dev/sda1", uuid="sda1-uuid")},
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.SLOW_DETECTED,
                        },
                    ],
                },
            ],
        }

        mock_partitions_in_chunk = {
            "partition": {},
            "cache": {},
            "journal": {},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_high_latency_disk_in_same_hba.assert_has_calls([
            call("sda-bus-location"),
        ])

        # case 4.3: skip slow hint
        # disk contains journal partition and only one journal in chunk
        # disk contains cache partition and only one cache in chunk
        # disk contains partition and only one partition in chunk
        mock_get_part_from_chunk_for_disk.reset_mock()
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_high_latency_disk_in_same_hba.reset_mock()
        mock_get_chunk_part_map.reset_mock()
        mock_is_last_part_in_chunk.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 50
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_get_high_latency_disk_in_same_hba.return_value = [Mock()]
        mock_is_last_part_in_chunk.return_value = True

        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": False,
                "bus_location": "sda-bus-location",
            },
            "sdb": {
                "name": "sdb",
                "path": "/dev/sdb",
                "is_rotational": False,
                "bus_location": "sdb-bus-location",
            },
            "sdc": {
                "name": "sdc",
                "path": "/dev/sdc",
                "is_rotational": False,
                "bus_location": "sdc-bus-location",
            },
        }
        mock_journal = Mock(uuid="sda1-journal")
        mock_partition = Mock(uuid="sdb1-partition")
        mock_cache = Mock(uuid="sdc1-cache")
        mock_get_chunk_part_map.side_effect = [
            {"journal": mock_journal},
            {"cache": mock_cache},
            {"partition": mock_partition},
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.SLOW_DETECTED,
                        },
                        {
                            "disk_name": "sdb",
                            "disk_serial": "sdb",
                            "status": slow_disk.SLOW_DETECTED,
                        },
                        {
                            "disk_name": "sdc",
                            "disk_serial": "sdc",
                            "status": slow_disk.SLOW_DETECTED,
                        },
                    ],
                },
            ],
        }

        mock_partitions_in_chunk = {
            "partition": {mock_partition.uuid: mock_partition},
            "cache": {mock_cache.uuid: mock_cache},
            "journal": {mock_journal.uuid: mock_journal},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_high_latency_disk_in_same_hba.assert_has_calls([
            call("sda-bus-location"),
            call("sdb-bus-location"),
            call("sdc-bus-location"),
        ])
        mock_is_last_part_in_chunk.assert_has_calls([
            call(mock_journal, mock_partitions_in_chunk["journal"]),
            call(mock_cache, mock_partitions_in_chunk["cache"]),
            call(mock_partition, mock_partitions_in_chunk["partition"]),
        ])

        # case 4.4: marked one disk to isolate and mark journal/partition/cache parts
        mock_get_part_from_chunk_for_disk.reset_mock()
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_high_latency_disk_in_same_hba.reset_mock()
        mock_get_chunk_part_map.reset_mock()
        mock_is_last_part_in_chunk.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 50
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_get_high_latency_disk_in_same_hba.return_value = []
        mock_is_last_part_in_chunk.return_value = False

        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": False,
                "bus_location": "sda-bus-location",
            },
        }
        mock_journal = Mock(
            path="/dev/sda1", errflags=0, warnflags=0, uuid="sda1-journal",
        )
        mock_partition = Mock(
            path="/dev/sda2", errflags=0, warnflags=0, total_size=1, uuid="sda2-partition",
        )
        mock_cache = Mock(
            path="/dev/sda3", errflags=0, warnflags=0, total_size=1, uuid="sda3-cache",
        )
        mock_get_chunk_part_map.side_effect = [
            {
                "journal": mock_journal,
                "partition": mock_partition,
                "cache": mock_cache,
            },
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.SLOW_DETECTED,
                        },
                    ],
                },
            ],
        }

        mock_partitions_in_chunk = {
            "partition": {},
            "cache": {},
            "journal": {},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is True
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_high_latency_disk_in_same_hba.assert_has_calls([
            call("sda-bus-location"),
        ])
        mock_get_chunk_part_map.assert_has_calls([
            call(
                {
                    "name": "sda",
                    "path": "/dev/sda",
                    "is_rotational": False,
                    "bus_location": "sda-bus-location",
                },
                mock_host_info,
                mock_partitions_in_chunk,
            ),
        ])

        # case 4.5: marked one disk to isolate
        # journal already been handled by chunk
        # partition size overflow
        # cache already been handled by chunk
        mock_get_part_from_chunk_for_disk.reset_mock()
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_high_latency_disk_in_same_hba.reset_mock()
        mock_get_chunk_part_map.reset_mock()
        mock_is_last_part_in_chunk.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 50
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_get_high_latency_disk_in_same_hba.return_value = []
        mock_is_last_part_in_chunk.return_value = False

        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": False,
                "bus_location": "sda-bus-location",
            },
        }
        mock_journal = Mock(
            path="/dev/sda1", errflags=1, warnflags=1, uuid="sda1-journal",
        )
        mock_partition = Mock(
            path="/dev/sda2", errflags=0, warnflags=0, total_size=50, uuid="sda2-partition",
        )
        mock_cache = Mock(
            path="/dev/sda3", errflags=1, warnflags=1, total_size=1, uuid="sda3-cache",
        )
        mock_get_chunk_part_map.side_effect = [
            {
                "journal": mock_journal,
                "partition": mock_partition,
                "cache": mock_cache,
            },
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.SLOW_DETECTED,
                        },
                    ],
                },
            ],
        }

        mock_partitions_in_chunk = {
            "partition": {},
            "cache": {},
            "journal": {},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_high_latency_disk_in_same_hba.assert_has_calls([
            call("sda-bus-location"),
        ])
        mock_get_chunk_part_map.assert_has_calls([
            call(
                {
                    "name": "sda",
                    "path": "/dev/sda",
                    "is_rotational": False,
                    "bus_location": "sda-bus-location",
                },
                mock_host_info,
                mock_partitions_in_chunk,
            ),
        ])

        # case 4.5: marked one disk to isolate
        # journal already been handled by chunk
        # partition already been handled by chunk
        # cache already been handled by chunk
        mock_get_part_from_chunk_for_disk.reset_mock()
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_high_latency_disk_in_same_hba.reset_mock()
        mock_get_chunk_part_map.reset_mock()
        mock_is_last_part_in_chunk.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 50
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_get_high_latency_disk_in_same_hba.return_value = []
        mock_is_last_part_in_chunk.return_value = False

        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": False,
                "bus_location": "sda-bus-location",
            },
        }
        mock_journal = Mock(
            path="/dev/sda1", errflags=1, warnflags=1, uuid="sda1-journal",
        )
        mock_partition = Mock(
            path="/dev/sda2", errflags=1, warnflags=1, total_size=1, uuid="sda2-partition",
        )
        mock_cache = Mock(
            path="/dev/sda3", errflags=1, warnflags=1, total_size=1, uuid="sda3-cache",
        )
        mock_get_chunk_part_map.side_effect = [
            {
                "journal": mock_journal,
                "partition": mock_partition,
                "cache": mock_cache,
            },
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.SLOW_DETECTED,
                        },
                    ],
                },
            ],
        }

        mock_partitions_in_chunk = {
            "partition": {},
            "cache": {},
            "journal": {},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_high_latency_disk_in_same_hba.assert_has_calls([
            call("sda-bus-location"),
        ])
        mock_get_chunk_part_map.assert_has_calls([
            call(
                {
                    "name": "sda",
                    "path": "/dev/sda",
                    "is_rotational": False,
                    "bus_location": "sda-bus-location",
                },
                mock_host_info,
                mock_partitions_in_chunk,
            ),
        ])

        # case 4.6: handle CAP_EXCLUDED disks
        # disk_info not exists
        mock_get_part_from_chunk_for_disk.reset_mock()
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_high_latency_disk_in_same_hba.reset_mock()
        mock_get_chunk_part_map.reset_mock()
        mock_is_last_part_in_chunk.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 50
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_get_high_latency_disk_in_same_hba.return_value = []
        mock_is_last_part_in_chunk.return_value = False

        mock_disk_map.disk_serials = {}
        mock_journal = Mock(
            path="/dev/sda1", errflags=1, warnflags=1, uuid="sda1-journal",
        )
        mock_partition = Mock(
            path="/dev/sda2", errflags=0, warnflags=0, total_size=50, uuid="sda2-partition",
        )
        mock_cache = Mock(
            path="/dev/sda3", errflags=1, warnflags=1, total_size=1, uuid="sda3-cache",
        )
        mock_get_chunk_part_map.side_effect = [
            {
                "journal": mock_journal,
                "partition": mock_partition,
                "cache": mock_cache,
            },
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.CAP_EXCLUDED,
                        },
                        {
                            "disk_name": "sdb",
                            "disk_serial": "sdb",
                            "status": slow_disk.IGNORED,
                        },
                    ],
                },
            ],
        }

        mock_partitions_in_chunk = {
            "partition": {},
            "cache": {},
            "journal": {},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_high_latency_disk_in_same_hba.assert_has_calls([])
        mock_get_chunk_part_map.assert_has_calls([])

        # case 4.6: handle CAP_EXCLUDED disks
        # there has isolating or umounting disks in cluster
        mock_get_part_from_chunk_for_disk.reset_mock()
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_high_latency_disk_in_same_hba.reset_mock()
        mock_get_chunk_part_map.reset_mock()
        mock_is_last_part_in_chunk.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 50
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 1
        mock_get_high_latency_disk_in_same_hba.return_value = [Mock(), Mock()]
        mock_is_last_part_in_chunk.return_value = False

        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": False,
                "bus_location": "sda-bus-location",
            },
            "sdc": {
                "name": "sdc",
                "path": "/dev/sdc",
                "is_rotational": False,
                "bus_location": "sdc-bus-location",
            },
        }
        mock_journal = Mock(
            path="/dev/sda1", errflags=1, warnflags=1, uuid="sda1-journal",
        )
        mock_partition = Mock(
            path="/dev/sda2", errflags=0, warnflags=0, total_size=50, uuid="sda2-partition",
        )
        mock_cache = Mock(
            path="/dev/sda3", errflags=1, warnflags=1, total_size=1, uuid="sda3-cache",
        )
        mock_get_chunk_part_map.side_effect = [
            {
                "journal": mock_journal,
                "partition": mock_partition,
                "cache": mock_cache,
            },
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sdb",
                            "disk_serial": "sdb",
                            "status": slow_disk.IGNORED,
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.CAP_EXCLUDED,
                        },
                        {
                            "disk_name": "sdc",
                            "disk_serial": "sdc",
                            "status": slow_disk.CAP_EXCLUDED,
                        }
                    ],
                },
            ],
        }

        mock_partitions_in_chunk = {
            "partition": {},
            "cache": {},
            "journal": {},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_high_latency_disk_in_same_hba.assert_has_calls([])
        mock_get_chunk_part_map.assert_has_calls([])

        # case 4.6: handle CAP_EXCLUDED disks
        # there may be HBA card issue
        mock_get_part_from_chunk_for_disk.reset_mock()
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_high_latency_disk_in_same_hba.reset_mock()
        mock_get_chunk_part_map.reset_mock()
        mock_is_last_part_in_chunk.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 50
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_get_high_latency_disk_in_same_hba.return_value = [Mock(), Mock()]
        mock_is_last_part_in_chunk.return_value = False

        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": False,
                "bus_location": "sda-bus-location",
            },
        }
        mock_journal = Mock(
            path="/dev/sda1", errflags=1, warnflags=1, uuid="sda1-journal",
        )
        mock_partition = Mock(
            path="/dev/sda2", errflags=0, warnflags=0, total_size=50, uuid="sda2-partition",
        )
        mock_cache = Mock(
            path="/dev/sda3", errflags=1, warnflags=1, total_size=1, uuid="sda3-cache",
        )
        mock_get_chunk_part_map.side_effect = [
            {
                "journal": mock_journal,
                "partition": mock_partition,
                "cache": mock_cache,
            },
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sdb",
                            "disk_serial": "sdb",
                            "status": slow_disk.IGNORED,
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.CAP_EXCLUDED,
                        },
                    ],
                },
            ],
        }

        mock_partitions_in_chunk = {
            "partition": {},
            "cache": {},
            "journal": {},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_high_latency_disk_in_same_hba.assert_has_calls([
            call("sda-bus-location"),
        ])
        mock_get_chunk_part_map.assert_has_calls([])

        # case 4.6: handle CAP_EXCLUDED disks
        # max isolate cache num overflow
        # max isolate partition size overflow
        mock_get_part_from_chunk_for_disk.reset_mock()
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_high_latency_disk_in_same_hba.reset_mock()
        mock_get_chunk_part_map.reset_mock()
        mock_is_last_part_in_chunk.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 100
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 4
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_get_high_latency_disk_in_same_hba.return_value = [Mock()]
        mock_is_last_part_in_chunk.return_value = False

        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": False,
                "bus_location": "sda-bus-location",
            },
        }
        mock_journal = Mock(
            path="/dev/sda1", errflags=1, warnflags=1, uuid="sda1-journal",
        )
        mock_partition = Mock(
            path="/dev/sda2", errflags=0, warnflags=0, total_size=50, uuid="sda2-partition",
        )
        mock_cache = Mock(
            path="/dev/sda3", errflags=1, warnflags=1, total_size=1, uuid="sda3-cache",
        )
        mock_get_chunk_part_map.side_effect = [
            {
                "journal": mock_journal,
                "partition": mock_partition,
                "cache": mock_cache,
            },
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sdb",
                            "disk_serial": "sdb",
                            "status": slow_disk.IGNORED,
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.CAP_EXCLUDED,
                        },
                    ],
                },
            ],
        }

        mock_partitions_in_chunk = {
            "partition": {},
            "cache": {},
            "journal": {},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_high_latency_disk_in_same_hba.assert_has_calls([
            call("sda-bus-location"),
        ])
        mock_get_chunk_part_map.assert_has_calls([
            call(
                {
                    "name": "sda",
                    "path": "/dev/sda",
                    "is_rotational": False,
                    "bus_location": "sda-bus-location",
                },
                mock_host_info,
                mock_partitions_in_chunk,
            )
        ])

        # case 4.6: handle CAP_EXCLUDED disks
        # max isolate partition num overflow
        mock_get_part_from_chunk_for_disk.reset_mock()
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_high_latency_disk_in_same_hba.reset_mock()
        mock_get_chunk_part_map.reset_mock()
        mock_is_last_part_in_chunk.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 100
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_get_high_latency_disk_in_same_hba.return_value = [Mock()]
        mock_is_last_part_in_chunk.return_value = False

        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": False,
                "bus_location": "sda-bus-location",
            },
        }
        mock_journal = Mock(
            path="/dev/sda1", errflags=1, warnflags=1, uuid="sda1-journal",
        )
        mock_partition = Mock(
            path="/dev/sda2", errflags=0, warnflags=0, total_size=50, uuid="sda2-partition",
        )
        mock_cache = Mock(
            path="/dev/sda3", errflags=1, warnflags=1, total_size=1, uuid="sda3-cache",
        )
        mock_get_chunk_part_map.side_effect = [
            {
                "journal": mock_journal,
                "partition": mock_partition,
                "cache": mock_cache,
            },
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sdb",
                            "disk_serial": "sdb",
                            "status": slow_disk.IGNORED,
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.CAP_EXCLUDED,
                        },
                    ],
                },
            ],
        }

        mock_partitions_in_chunk = {
            "partition": {mock_partition.uuid: mock_partition},
            "cache": {mock_cache.uuid: mock_cache},
            "journal": {mock_journal.uuid: mock_journal},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_high_latency_disk_in_same_hba.assert_has_calls([
            call("sda-bus-location"),
        ])
        mock_get_chunk_part_map.assert_has_calls([
            call(
                {
                    "name": "sda",
                    "path": "/dev/sda",
                    "is_rotational": False,
                    "bus_location": "sda-bus-location",
                },
                mock_host_info,
                mock_partitions_in_chunk,
            )
        ])

        # case 4.6: handle CAP_EXCLUDED disks
        # disk contains partition part and only one partition in chunk
        # disk contains cache part and only one cache in chunk
        mock_get_part_from_chunk_for_disk.reset_mock()
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_high_latency_disk_in_same_hba.reset_mock()
        mock_get_chunk_part_map.reset_mock()
        mock_is_last_part_in_chunk.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 50
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_get_high_latency_disk_in_same_hba.return_value = [Mock()]
        mock_is_last_part_in_chunk.return_value = True

        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": False,
                "bus_location": "sda-bus-location",
            },
            "sdc": {
                "name": "sdc",
                "path": "/dev/sdc",
                "is_rotational": False,
                "bus_location": "sdc-bus-location",
            },
        }
        mock_journal = Mock(
            path="/dev/sda1", errflags=0, warnflags=0, uuid="sda1-journal",
        )
        mock_partition = Mock(
            path="/dev/sda2", errflags=0, warnflags=0, total_size=1, uuid="sda2-partition",
        )
        mock_cache = Mock(
            path="/dev/sda3", errflags=0, warnflags=0, total_size=1, uuid="sda3-cache",
        )
        mock_get_chunk_part_map.side_effect = [
            {
                "partition": mock_partition,
            },
            {
                "cache": mock_cache,
            }
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sdb",
                            "disk_serial": "sdb",
                            "status": slow_disk.IGNORED,
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.CAP_EXCLUDED,
                        },
                        {
                            "disk_name": "sdc",
                            "disk_serial": "sdc",
                            "status": slow_disk.CAP_EXCLUDED,
                        }
                    ],
                },
            ],
        }

        mock_partitions_in_chunk = {
            "partition": {mock_partition.uuid: mock_partition},
            "cache": {mock_cache.uuid: mock_cache},
            "journal": {mock_journal.uuid: mock_journal},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_high_latency_disk_in_same_hba.assert_has_calls([
            call("sda-bus-location"),
        ])
        mock_get_chunk_part_map.assert_has_calls([
            call(
                {
                    "name": "sda",
                    "path": "/dev/sda",
                    "is_rotational": False,
                    "bus_location": "sda-bus-location",
                },
                mock_host_info,
                mock_partitions_in_chunk,
            ),
            call(
                {
                    "name": "sdc",
                    "path": "/dev/sdc",
                    "is_rotational": False,
                    "bus_location": "sdc-bus-location",
                },
                mock_host_info,
                mock_partitions_in_chunk,
            ),
        ])

        # case 4.6: handle CAP_EXCLUDED disks
        # disk contains partition part and only one partition in chunk
        mock_get_part_from_chunk_for_disk.reset_mock()
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_high_latency_disk_in_same_hba.reset_mock()
        mock_get_chunk_part_map.reset_mock()
        mock_is_last_part_in_chunk.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 50
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_get_high_latency_disk_in_same_hba.return_value = [Mock()]
        mock_is_last_part_in_chunk.return_value = True

        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": False,
                "bus_location": "sda-bus-location",
            },
        }
        mock_journal = Mock(
            path="/dev/sda1", errflags=1, warnflags=1, uuid="sda1-journal",
        )
        mock_partition = Mock(
            path="/dev/sda2", errflags=0, warnflags=0, total_size=50, uuid="sda2-partition",
        )
        mock_cache = Mock(
            path="/dev/sda3", errflags=1, warnflags=1, total_size=1, uuid="sda3-cache",
        )
        mock_get_chunk_part_map.side_effect = [
            {
                "journal": mock_journal,
                "partition": mock_partition,
                "cache": mock_cache,
            },
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sdb",
                            "disk_serial": "sdb",
                            "status": slow_disk.IGNORED,
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.CAP_EXCLUDED,
                        },
                    ],
                },
            ],
        }

        mock_partitions_in_chunk = {
            "partition": {mock_partition.uuid: mock_partition},
            "cache": {},
            "journal": {},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_high_latency_disk_in_same_hba.assert_has_calls([
            call("sda-bus-location"),
        ])
        mock_get_chunk_part_map.assert_has_calls([
            call(
                {
                    "name": "sda",
                    "path": "/dev/sda",
                    "is_rotational": False,
                    "bus_location": "sda-bus-location",
                },
                mock_host_info,
                mock_partitions_in_chunk,
            )
        ])

        # case 4.6: handle CAP_EXCLUDED disks
        # mark journal/partition/cache part to isolate
        mock_get_part_from_chunk_for_disk.reset_mock()
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_high_latency_disk_in_same_hba.reset_mock()
        mock_get_chunk_part_map.reset_mock()
        mock_is_last_part_in_chunk.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 50
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_get_high_latency_disk_in_same_hba.return_value = [Mock()]
        mock_is_last_part_in_chunk.return_value = False

        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": False,
                "bus_location": "sda-bus-location",
            },
        }
        mock_journal = Mock(
            path="/dev/sda1", errflags=0, warnflags=0, uuid="sda1-journal",
        )
        mock_partition = Mock(
            path="/dev/sda2", errflags=0, warnflags=0, total_size=1, uuid="sda2-partition",
        )
        mock_cache = Mock(
            path="/dev/sda3", errflags=0, warnflags=0, total_size=1, uuid="sda3-cache",
        )
        mock_get_chunk_part_map.side_effect = [
            {
                "journal": mock_journal,
                "partition": mock_partition,
                "cache": mock_cache,
            },
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sdb",
                            "disk_serial": "sdb",
                            "status": slow_disk.IGNORED,
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.CAP_EXCLUDED,
                        },
                    ],
                },
            ],
        }

        mock_partitions_in_chunk = {
            "partition": {},
            "cache": {},
            "journal": {},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is True
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_high_latency_disk_in_same_hba.assert_has_calls([
            call("sda-bus-location"),
        ])
        mock_get_chunk_part_map.assert_has_calls([
            call(
                {
                    "name": "sda",
                    "path": "/dev/sda",
                    "is_rotational": False,
                    "bus_location": "sda-bus-location",
                },
                mock_host_info,
                mock_partitions_in_chunk,
            )
        ])

        # case 4.6: handle CAP_EXCLUDED disks
        # journal/partition/cache part not exists in chunk
        mock_get_part_from_chunk_for_disk.reset_mock()
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_high_latency_disk_in_same_hba.reset_mock()
        mock_get_chunk_part_map.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 50
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_get_high_latency_disk_in_same_hba.return_value = [Mock()]

        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": False,
                "bus_location": "sda-bus-location",
            },
        }
        mock_journal = Mock(
            path="/dev/sda1", errflags=0, warnflags=0, uuid="sda1-journal",
        )
        mock_partition = Mock(
            path="/dev/sda2", errflags=0, warnflags=0, total_size=1, uuid="sda2-partition",
        )
        mock_cache = Mock(
            path="/dev/sda3", errflags=0, warnflags=0, total_size=1, uuid="sda3-cache",
        )
        mock_get_chunk_part_map.side_effect = [
            {
                "journal": None,
                "partition": None,
                "cache": None,
            },
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sdb",
                            "disk_serial": "sdb",
                            "status": slow_disk.IGNORED,
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.CAP_EXCLUDED,
                        },
                    ],
                },
            ],
        }

        mock_partitions_in_chunk = {
            "partition": {},
            "cache": {},
            "journal": {},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_high_latency_disk_in_same_hba.assert_has_calls([
            call("sda-bus-location"),
        ])
        mock_get_chunk_part_map.assert_has_calls([
            call(
                {
                    "name": "sda",
                    "path": "/dev/sda",
                    "is_rotational": False,
                    "bus_location": "sda-bus-location",
                },
                mock_host_info,
                mock_partitions_in_chunk,
            )
        ])

        # case 4.6: handle CAP_EXCLUDED disks
        # journal/partition/cache part already isolated by chunk
        mock_get_part_from_chunk_for_disk.reset_mock()
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_high_latency_disk_in_same_hba.reset_mock()
        mock_get_chunk_part_map.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 50
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_get_high_latency_disk_in_same_hba.return_value = [Mock()]

        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": False,
                "bus_location": "sda-bus-location",
            },
        }
        mock_journal = Mock(
            path="/dev/sda1", errflags=1, warnflags=1, uuid="sda1-journal",
        )
        mock_partition = Mock(
            path="/dev/sda2", errflags=1, warnflags=1, total_size=1, uuid="sda2-partition",
        )
        mock_cache = Mock(
            path="/dev/sda3", errflags=1, warnflags=1, total_size=1, uuid="sda3-cache",
        )
        mock_get_chunk_part_map.side_effect = [
            {
                "journal": mock_journal,
                "partition": mock_partition,
                "cache": mock_cache,
            },
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sdb",
                            "disk_serial": "sdb",
                            "status": slow_disk.IGNORED,
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.CAP_EXCLUDED,
                        },
                    ],
                },
            ],
        }

        mock_partitions_in_chunk = {
            "partition": {},
            "cache": {},
            "journal": {},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_high_latency_disk_in_same_hba.assert_has_calls([
            call("sda-bus-location"),
        ])
        mock_get_chunk_part_map.assert_has_calls([
            call(
                {
                    "name": "sda",
                    "path": "/dev/sda",
                    "is_rotational": False,
                    "bus_location": "sda-bus-location",
                },
                mock_host_info,
                mock_partitions_in_chunk,
            )
        ])

        # case 4.6: handle CAP_EXCLUDED disks
        # partition part size overflow
        mock_get_part_from_chunk_for_disk.reset_mock()
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_high_latency_disk_in_same_hba.reset_mock()
        mock_get_chunk_part_map.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 50
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_get_high_latency_disk_in_same_hba.return_value = [Mock()]

        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": False,
                "bus_location": "sda-bus-location",
            },
        }
        mock_journal = Mock(
            path="/dev/sda1", errflags=1, warnflags=1, uuid="sda1-journal",
        )
        mock_partition = Mock(
            path="/dev/sda2", errflags=0, warnflags=0, total_size=50, uuid="sda2-partition",
        )
        mock_cache = Mock(
            path="/dev/sda3", errflags=1, warnflags=1, total_size=1, uuid="sda3-cache",
        )
        mock_get_chunk_part_map.side_effect = [
            {
                "journal": None,
                "partition": mock_partition,
                "cache": None,
            },
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sdb",
                            "disk_serial": "sdb",
                            "status": slow_disk.IGNORED,
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.CAP_EXCLUDED,
                        },
                    ],
                },
            ],
        }

        mock_partitions_in_chunk = {
            "partition": {},
            "cache": {},
            "journal": {},
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_high_latency_disk_in_same_hba.assert_has_calls([
            call("sda-bus-location"),
        ])
        mock_get_chunk_part_map.assert_has_calls([
            call(
                {
                    "name": "sda",
                    "path": "/dev/sda",
                    "is_rotational": False,
                    "bus_location": "sda-bus-location",
                },
                mock_host_info,
                mock_partitions_in_chunk,
            )
        ])


def test_try_mark_to_isolate_hdd():
    with (
        patch("disk_healthd.check_loops.isolate_disk_loop.disk_map") as mock_disk_map,
        patch.object(IsolateDiskLoop, "_get_part_from_chunk_for_disk") as mock_get_part_from_chunk_for_disk,
        patch.object(IsolateDiskLoop, "_max_isolate_size") as mock_max_isolate_size,
        patch.object(IsolateDiskLoop, "_get_cluster_isolate_partition_size") as mock_get_cluster_isolate_partition_size,
        patch.object(IsolateDiskLoop, "_max_single_node_healthy_partition_num") as mock_max_single_node_healthy_partition_num,
        patch.object(IsolateDiskLoop, "_get_cluster_isolate_partition_num") as mock_get_cluster_isolate_partition_num,
        patch.object(IsolateDiskLoop, "_max_single_node_healthy_cache_num") as mock_max_single_node_healthy_cache_num,
        patch.object(IsolateDiskLoop, "_get_cluster_isolate_cache_num") as mock_get_cluster_isolate_cache_num,
        patch.object(IsolateDiskLoop, "_get_cluster_isolating_or_umounting_num")  as mock_get_cluster_isolating_or_umounting_num,
        patch.object(IsolateDiskLoop, "_get_high_latency_disk_in_same_hba") as mock_get_high_latency_disk_in_same_hba,
        patch.object(IsolateDiskLoop, "_get_chunk_part_map") as mock_get_chunk_part_map,
    ):
        # case 1: no MARK_TO_ISOLATE/SLOW_DETECTED/CAP_EXCLUDED disk exist
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.IGNORED,
                        },
                    ],
                }
            ],
        }

        mock_partitions_in_chunk = {
            "partition": [],
            "cache": [],
            "journal": [],
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False

        # case 2: has MARK_TO_ISOLATE disk but no disk_info
        mock_disk_map.disk_serials = {}
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.MARK_TO_ISOLATE,
                        },
                    ],
                }
            ],
        }

        mock_partitions_in_chunk = {
            "partition": [],
            "cache": [],
            "journal": [],
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False

        # case 3: has MARK_TO_ISOLATE disk & no chunk_partition
        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": True,
                "bus_location": "sda-bus-location",
            },
        }
        mock_get_part_from_chunk_for_disk.return_value = None
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.MARK_TO_ISOLATE,
                        },
                    ],
                }
            ],
        }

        mock_partitions_in_chunk = {
            "partition": [],
            "cache": [],
            "journal": [],
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_get_part_from_chunk_for_disk.assert_has_calls([
            call({
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": True,
                "bus_location": "sda-bus-location",
            },
            mock_partitions_in_chunk["partition"],
            usage="partition",
            ),
        ])

        # case 4: has MARK_TO_ISOLATE disk & chunk_partition total_size not equal
        mock_get_part_from_chunk_for_disk.reset_mock()

        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": True,
                "bus_location": "sda-bus-location",
            },
        }
        mock_partition = Mock(
            path="/dev/sda1", errflags=0, warnflags=0, total_size=2, uuid="sda1-new",
        )
        mock_get_part_from_chunk_for_disk.return_value = mock_partition
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.MARK_TO_ISOLATE,
                            "chunk_part_size": 1,
                            "part_uuid_in_chunk": "sda1",
                        },
                    ],
                }
            ],
        }

        mock_partitions_in_chunk = {
            "partition": [],
            "cache": [],
            "journal": [],
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is True
        mock_get_part_from_chunk_for_disk.assert_has_calls([
            call({
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": True,
                "bus_location": "sda-bus-location",
            },
            mock_partitions_in_chunk["partition"],
            usage="partition",
            ),
        ])

        # case 5: SLOW_DETECTED disk
        # disk_info not exists
        mock_get_part_from_chunk_for_disk.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 50
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_disk_map.disk_serials = {}
        mock_partition = Mock(
            path="/dev/sda1", errflags=0, warnflags=0, total_size=2, uuid="sda1-new",
        )
        mock_get_part_from_chunk_for_disk.return_value = mock_partition
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sdb",
                            "disk_serial": "sdb",
                            "status": slow_disk.IGNORED,
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.SLOW_DETECTED,
                        },
                    ],
                }
            ],
        }

        mock_partitions_in_chunk = {
            "partition": [],
            "cache": [],
            "journal": [],
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_part_from_chunk_for_disk.assert_has_calls([])

        # case 5: SLOW_DETECTED disk
        # cluster isolate partition size overflow
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_part_from_chunk_for_disk.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 100
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": True,
                "bus_location": "sda-bus-location",
            },
        }
        mock_partition = Mock(
            path="/dev/sda1", errflags=0, warnflags=0, total_size=2, uuid="sda1-new",
        )
        mock_get_part_from_chunk_for_disk.return_value = mock_partition
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sdb",
                            "disk_serial": "sdb",
                            "status": slow_disk.IGNORED,
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.SLOW_DETECTED,
                        },
                    ],
                }
            ],
        }

        mock_partitions_in_chunk = {
            "partition": [],
            "cache": [],
            "journal": [],
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_part_from_chunk_for_disk.assert_has_calls([])

        # case 5: SLOW_DETECTED disk
        # cluster max isolate partition num overflow
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_part_from_chunk_for_disk.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 50
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 4
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": True,
                "bus_location": "sda-bus-location",
            },
        }
        mock_partition = Mock(
            path="/dev/sda1", errflags=0, warnflags=0, total_size=2, uuid="sda1-new",
        )
        mock_get_part_from_chunk_for_disk.return_value = mock_partition
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sdb",
                            "disk_serial": "sdb",
                            "status": slow_disk.IGNORED,
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.SLOW_DETECTED,
                        },
                    ],
                }
            ],
        }

        mock_partitions_in_chunk = {
            "partition": [],
            "cache": [],
            "journal": [],
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_part_from_chunk_for_disk.assert_has_calls([])

        # case 5: SLOW_DETECTED disk
        # chunk partition not exists
        # chunk partition already handled by chunk
        # chunk partition size overflow
        # chunk partition is marked to isolate
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_part_from_chunk_for_disk.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 50
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": True,
                "bus_location": "sda-bus-location",
            },
            "sdc": {
                "name": "sdc",
                "path": "/dev/sdc",
                "is_rotational": True,
                "bus_location": "sdc-bus-location",
            },
            "sdd": {
                "name": "sdd",
                "path": "/dev/sdd",
                "is_rotational": True,
                "bus_location": "sdd-bus-location",
            },
            "sde": {
                "name": "sde",
                "path": "/dev/sde",
                "is_rotational": True,
                "bus_location": "sde-bus-location",
            },
        }
        mock_get_part_from_chunk_for_disk.side_effect = [
            # chunk partition not exists
            None,
            # chunk partition already handled by chunk
            Mock(path="/dev/sdc1", errflags=1, warnflags=1),
            # chunk partition size overflow
            Mock(path="/dev/sdd1", errflags=0, warnflags=0, total_size=50, uuid="sdd1-new"),
            # chunk partition is marked to isolate
            Mock(path="/dev/sde1", errflags=0, warnflags=0, total_size=1, uuid="sde1-new"),
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sdb",
                            "disk_serial": "sdb",
                            "status": slow_disk.IGNORED,
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.SLOW_DETECTED,
                        },
                        {
                            "disk_name": "sdc",
                            "disk_serial": "sdc",
                            "status": slow_disk.SLOW_DETECTED,
                        },
                        {
                            "disk_name": "sdd",
                            "disk_serial": "sdd",
                            "status": slow_disk.SLOW_DETECTED,
                        },
                        {
                            "disk_name": "sde",
                            "disk_serial": "sde",
                            "status": slow_disk.SLOW_DETECTED,
                        }
                    ],
                }
            ],
        }

        mock_partitions_in_chunk = {
            "partition": [],
            "cache": [],
            "journal": [],
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is True
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_part_from_chunk_for_disk.assert_has_calls([
            call(
                {
                    "name": "sda",
                    "path": "/dev/sda",
                    "is_rotational": True,
                    "bus_location": "sda-bus-location",
                },
                mock_partitions_in_chunk["partition"],
                usage="partition",
            ),
            call(
                {
                    "name": "sdc",
                    "path": "/dev/sdc",
                    "is_rotational": True,
                    "bus_location": "sdc-bus-location",
                },
                mock_partitions_in_chunk["partition"],
                usage="partition",
            ),
            call(
                {
                    "name": "sdd",
                    "path": "/dev/sdd",
                    "is_rotational": True,
                    "bus_location": "sdd-bus-location",
                },
                mock_partitions_in_chunk["partition"],
                usage="partition",
            ),
            call(
                {
                    "name": "sde",
                    "path": "/dev/sde",
                    "is_rotational": True,
                    "bus_location": "sde-bus-location",
                },
                mock_partitions_in_chunk["partition"],
                usage="partition",
            ),
        ])

        # case 6: CAP_EXCLUDED disk
        # not CAP_EXCLUDED disk
        # disk_info not exists
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_part_from_chunk_for_disk.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 50
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_disk_map.disk_serials = {}
        mock_get_part_from_chunk_for_disk.side_effect = [
            # chunk partition not exists
            None,
            # chunk partition already handled by chunk
            Mock(path="/dev/sdc1", errflags=1, warnflags=1),
            # chunk partition size overflow
            Mock(path="/dev/sdd1", errflags=0, warnflags=0, total_size=50, uuid="sdd1-new"),
            # chunk partition is marked to isolate
            Mock(path="/dev/sde1", errflags=0, warnflags=0, total_size=1, uuid="sde1-new"),
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sdb",
                            "disk_serial": "sdb",
                            "status": slow_disk.IGNORED,
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.CAP_EXCLUDED,
                        },
                    ],
                }
            ],
        }

        mock_partitions_in_chunk = {
            "partition": [],
            "cache": [],
            "journal": [],
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_part_from_chunk_for_disk.assert_has_calls([])

        # case 6: CAP_EXCLUDED disk
        # cluster isolate partition size overflow
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_part_from_chunk_for_disk.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 100
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": True,
                "bus_location": "sda-bus-location",
            },
        }
        mock_get_part_from_chunk_for_disk.side_effect = [
            # chunk partition not exists
            None,
            # chunk partition already handled by chunk
            Mock(path="/dev/sdc1", errflags=1, warnflags=1),
            # chunk partition size overflow
            Mock(path="/dev/sdd1", errflags=0, warnflags=0, total_size=50, uuid="sdd1-new"),
            # chunk partition is marked to isolate
            Mock(path="/dev/sde1", errflags=0, warnflags=0, total_size=1, uuid="sde1-new"),
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sdb",
                            "disk_serial": "sdb",
                            "status": slow_disk.IGNORED,
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.CAP_EXCLUDED,
                        },
                    ],
                }
            ],
        }

        mock_partitions_in_chunk = {
            "partition": [],
            "cache": [],
            "journal": [],
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_part_from_chunk_for_disk.assert_has_calls([])

        # case 6: CAP_EXCLUDED disk
        # max isolate partition num overflow
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_part_from_chunk_for_disk.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 50
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 4
        mock_max_single_node_healthy_cache_num.return_value = 4
        mock_get_cluster_isolate_cache_num.return_value = 2
        mock_get_cluster_isolating_or_umounting_num.return_value = 0
        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": True,
                "bus_location": "sda-bus-location",
            },
        }
        mock_get_part_from_chunk_for_disk.side_effect = [
            # chunk partition not exists
            None,
            # chunk partition already handled by chunk
            Mock(path="/dev/sdc1", errflags=1, warnflags=1),
            # chunk partition size overflow
            Mock(path="/dev/sdd1", errflags=0, warnflags=0, total_size=50, uuid="sdd1-new"),
            # chunk partition is marked to isolate
            Mock(path="/dev/sde1", errflags=0, warnflags=0, total_size=1, uuid="sde1-new"),
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sdb",
                            "disk_serial": "sdb",
                            "status": slow_disk.IGNORED,
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.CAP_EXCLUDED,
                        },
                    ],
                }
            ],
        }

        mock_partitions_in_chunk = {
            "partition": [],
            "cache": [],
            "journal": [],
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is False
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_part_from_chunk_for_disk.assert_has_calls([])

        # case 6: CAP_EXCLUDED disk
        # chunk partition not exists
        # chunk partition already handled by chunk
        # chunk partition size overflow
        # chunk partition is marked isolated
        mock_max_isolate_size.reset_mock()
        mock_get_cluster_isolate_partition_size.reset_mock()
        mock_max_single_node_healthy_partition_num.reset_mock()
        mock_get_cluster_isolate_partition_num.reset_mock()
        mock_max_single_node_healthy_cache_num.reset_mock()
        mock_get_cluster_isolate_cache_num.reset_mock()
        mock_get_cluster_isolating_or_umounting_num.reset_mock()
        mock_get_part_from_chunk_for_disk.reset_mock()

        mock_max_isolate_size.return_value = 100
        mock_get_cluster_isolate_partition_size.return_value = 50
        mock_max_single_node_healthy_partition_num.return_value = 4
        mock_get_cluster_isolate_partition_num.return_value = 2
        mock_disk_map.disk_serials = {
            "sda": {
                "name": "sda",
                "path": "/dev/sda",
                "is_rotational": True,
                "bus_location": "sda-bus-location",
            },
            "sdc": {
                "name": "sdc",
                "path": "/dev/sdc",
                "is_rotational": True,
                "bus_location": "sdc-bus-location",
            },
            "sdd": {
                "name": "sdd",
                "path": "/dev/sdd",
                "is_rotational": True,
                "bus_location": "sdd-bus-location",
            },
            "sde": {
                "name": "sde",
                "path": "/dev/sde",
                "is_rotational": True,
                "bus_location": "sde-bus-location",
            },
        }
        mock_get_part_from_chunk_for_disk.side_effect = [
            # chunk partition not exists
            None,
            # chunk partition already handled by chunk
            Mock(path="/dev/sdc1", errflags=1, warnflags=1),
            # chunk partition size overflow
            Mock(path="/dev/sdd1", errflags=0, warnflags=0, total_size=50, uuid="sdd1-new"),
            # chunk partition is marked to isolate
            Mock(path="/dev/sde1", errflags=0, warnflags=0, total_size=1, uuid="sde1-new"),
        ]
        loop = IsolateDiskLoop()
        mock_sd_views = {
            "resource_name": "slow_disks",
            "version": -2,
            "hosts": [
                {
                    "data_ip": loop.sd_service.CURRENT_DATA_IP,
                    "slow_disks": [
                        {
                            "disk_name": "sdb",
                            "disk_serial": "sdb",
                            "status": slow_disk.IGNORED,
                        },
                        {
                            "disk_name": "sda",
                            "disk_serial": "sda",
                            "status": slow_disk.CAP_EXCLUDED,
                        },
                        {
                            "disk_name": "sdc",
                            "disk_serial": "sdc",
                            "status": slow_disk.CAP_EXCLUDED,
                        },
                        {
                            "disk_name": "sdd",
                            "disk_serial": "sdd",
                            "status": slow_disk.CAP_EXCLUDED,
                        },
                        {
                            "disk_name": "sde",
                            "disk_serial": "sde",
                            "status": slow_disk.CAP_EXCLUDED,
                        },
                    ],
                }
            ],
        }

        mock_partitions_in_chunk = {
            "partition": [],
            "cache": [],
            "journal": [],
        }
        mock_host_info = {"hostname": "mock_hostname"}

        view_changed = loop._try_mark_to_isolate(
            mock_sd_views,
            mock_partitions_in_chunk,
            mock_host_info,
        )

        assert view_changed is True
        mock_max_isolate_size.assert_called_once_with()
        mock_get_cluster_isolate_partition_size.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_partition_num.assert_called_once_with()
        mock_get_cluster_isolate_partition_num.assert_called_once_with(mock_sd_views)
        mock_max_single_node_healthy_cache_num.assert_called_once_with()
        mock_get_cluster_isolate_cache_num.assert_called_once_with(mock_sd_views)
        mock_get_cluster_isolating_or_umounting_num.assert_called_once_with()
        mock_get_part_from_chunk_for_disk.assert_has_calls([
            call(
                {
                    "name": "sda",
                    "path": "/dev/sda",
                    "is_rotational": True,
                    "bus_location": "sda-bus-location",
                },
                mock_partitions_in_chunk["partition"],
                usage="partition",
            ),
            call(
                {
                    "name": "sdc",
                    "path": "/dev/sdc",
                    "is_rotational": True,
                    "bus_location": "sdc-bus-location",
                },
                mock_partitions_in_chunk["partition"],
                usage="partition",
            ),
            call(
                {
                    "name": "sdd",
                    "path": "/dev/sdd",
                    "is_rotational": True,
                    "bus_location": "sdd-bus-location",
                },
                mock_partitions_in_chunk["partition"],
                usage="partition",
            ),
            call(
                {
                    "name": "sde",
                    "path": "/dev/sde",
                    "is_rotational": True,
                    "bus_location": "sde-bus-location",
                },
                mock_partitions_in_chunk["partition"],
                usage="partition",
            )
        ])


def test_is_last_part_in_chunk():
    loop = IsolateDiskLoop()

    # case 1: chunk_part is None
    res = loop._is_last_part_in_chunk(None, {})

    assert res is False

    # case 2: more than one part in chunk
    mock_chunk_part = Mock(chunk_ins_id=1, uuid="test_uuid_1")
    mock_parts_in_node = {
        "test_uuid_1": Mock(chunk_ins_id=1, uuid="test_uuid_1"),
        "test_uuid_2": Mock(chunk_ins_id=1, uuid="test_uuid_2"),
    }

    res = loop._is_last_part_in_chunk(mock_chunk_part, mock_parts_in_node)

    assert res is False

    # case 3: only one part in chunk
    mock_chunk_part = Mock(chunk_ins_id=1, uuid="test_uuid_1")
    mock_parts_in_node = {
        "test_uuid_1": Mock(chunk_ins_id=1, uuid="test_uuid_1"),
        "test_uuid_2": Mock(chunk_ins_id=2, uuid="test_uuid_2"),
    }

    res = loop._is_last_part_in_chunk(mock_chunk_part, mock_parts_in_node)

    assert res is True
