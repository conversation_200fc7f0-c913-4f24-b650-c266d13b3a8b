[loggers]
keys=root, gunicorn.error, gunicorn.access

[handlers]
keys=console, access_file

[formatters]
keys=generic, access

[logger_root]
level=INFO
handlers=console

[logger_gunicorn.error]
level=INFO
handlers=console
propagate=0
qualname=gunicorn.error

[logger_gunicorn.access]
level=INFO
handlers=access_file
propagate=0
qualname=gunicorn.access

[handler_console]
class=logging.FileHandler
formatter=generic
args=('/var/log/zbs/disk-healthd/disk-healthd.log',)

[handler_access_file]
class=logging.FileHandler
formatter=access
args=('/var/log/zbs/disk-healthd/rest-access.log',)

[formatter_generic]
format=%(asctime)s [%(process)d] [%(levelname)s] %(message)s
datefmt=%Y-%m-%d %H:%M:%S
class=logging.Formatter

[formatter_access]
format=%(message)s
class=logging.Formatter
