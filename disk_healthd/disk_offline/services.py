# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
from datetime import datetime
import logging
import time

from disk_healthd.core.udp import UdpRequestContext
from disk_healthd.disk_offline.messages import (
    HeartbeatRequest,
    HeartbeatResponse,
    MsgType,
    OfflineAckRequest,
    OfflineAckResponse,
    OfflineRequest,
    OfflineResponse,
    WarningRequest,
    WarningResponse,
)
from tuna.config.constant import (
    DISK_OR_PARTITION_USAGE_CACHE,
    DISK_OR_PARTITION_USAGE_JOURNAL,
    DISK_OR_PARTITION_USAGE_PARTITION,
)
from tuna.node.storage_manager import StorageDiskManager
from tuna.service.disk_status_service import DiskStatusService
from tuna.service.host_service import HostService
from tuna.service.slow_disk_service import OfflineRecordService
from zbs.chunk.client import ZbsChunk
from zbs.proto.chunk_pb2 import DiskErrFlags, DiskNameScheme

MARK_TO_OFFLINE = "MARK_TO_OFFLINE"
OFFLINE_ACKED = "OFFLINE_ACKED"


class DiskNotUnique(Exception):
    pass


class DiskNotFound(Exception):
    pass


class OverOfflineNum(Exception):
    pass


class UpdateRecordDbError(Exception):
    pass


class RpcToChunkError(Exception):
    pass


class DiskOfflineService:
    register_msg_type = MsgType

    @classmethod
    def handle_request(cls, context: UdpRequestContext) -> bytes:
        match context.msg_type_id:
            case MsgType.HEART_BEAT:
                req = HeartbeatRequest.from_bytes(context.msg_content)
                return cls.handle_heartbeat(req).to_bytes()
            case MsgType.WARNING:
                req = WarningRequest.from_bytes(context.msg_content)
                return cls.handle_warning(req).to_bytes()
            case MsgType.OFFLINE:
                req = OfflineRequest.from_bytes(context.msg_content)
                return cls.handle_offline(req).to_bytes()
            case MsgType.OFFLINE_ACK:
                req = OfflineAckRequest.from_bytes(context.msg_content)
                return cls.handle_offline_ack(req).to_bytes()
            case _:
                logging.warning(f"DiskOffline not support msg type: {context.msg_type_id}")
                req = HeartbeatRequest.from_bytes(context.msg_content)
                return OfflineResponse(
                    MsgType.OFFLINE,
                    req.request_id,
                    int(time.time()),
                    req.node_uuid,
                    granted=0,
                    disk_wwid=b"",
                    reason=b"unknown msg type",
                ).to_bytes()

    @classmethod
    def _get_disk_record_by_wwid(cls, host_uuid, disk_wwid, raise_error=False):
        disk_record = None

        try:
            if not (host_uuid and disk_wwid):
                raise DiskNotFound(f"Invalid disk record query {host_uuid=} {disk_wwid=}")

            disks = DiskStatusService().list_disk_status_by_filter(
                {"host_uuid": host_uuid, "disk_wwid": disk_wwid},
                projection={
                    "_id": False,
                    "trace_id": True,
                    "disk_path": True,
                    "disk_serial": True,
                    "disk_wwid": True,
                    "host_uuid": True,
                    "data_ip": True,
                    "healthy": True,
                },
            )
            disk_record = disks[0] if disks else None

            if not disk_record:
                raise DiskNotFound(f"Record not found for {disk_wwid=}")
            if len(disks) > 1:
                raise DiskNotUnique(f"Found more than one record for {disk_wwid=}")
        except (DiskNotFound, DiskNotUnique, Exception) as e:
            logging.warning(f"DiskOffline: An error occurred during query disk record for {disk_wwid=} {e=}")
            if raise_error:
                raise e

        return disk_record

    @classmethod
    def handle_heartbeat(cls, req: HeartbeatRequest) -> HeartbeatResponse:
        return HeartbeatResponse(
            MsgType.HEART_BEAT,
            req.request_id,
            int(time.time()),
            req.node_uuid,
        )

    @classmethod
    def handle_warning(cls, req: WarningRequest) -> WarningResponse:
        request_id, host_uuid, disk_wwid, warn_msg = (
            req.request_id,
            req.get_host_uuid(),
            req.get_disk_wwid(),
            req.get_msg(),
        )

        disk_record = None
        if host_uuid and disk_wwid:
            disk_record = cls._get_disk_record_by_wwid(host_uuid, disk_wwid)

        logging.info(f"DiskOffline Warning: {request_id=} {host_uuid=} {disk_wwid=} {warn_msg=} {disk_record=}")

        return WarningResponse(
            MsgType.WARNING,
            request_id,
            int(time.time()),
            req.node_uuid,
        )

    @classmethod
    def _retry_func(cls, func, retry_num, exp: type[Exception]):
        count = 1
        while count <= retry_num:
            try:
                return func()
            except exp as e:
                if count >= retry_num:
                    raise e
                else:
                    logging.info(f"retry for {type(e).__name__}: {e}")
            count += 1

    @classmethod
    def handle_offline(cls, req: OfflineRequest) -> OfflineResponse:
        request_id, host_uuid, disk_wwid = (
            req.request_id,
            req.get_host_uuid(),
            req.get_disk_wwid(),
        )
        logging.info(f"DiskOffline receive {request_id=} {host_uuid=} {disk_wwid=}")

        try:
            return cls._retry_func(func=lambda: cls._handle_offline(req), retry_num=3, exp=UpdateRecordDbError)
        except DiskNotUnique:
            reason = "disk not unique"
        except DiskNotFound:
            reason = "disk not found"
        except OverOfflineNum:
            reason = "more than one offline"
        except Exception as e:
            logging.exception(f"An error occurred during handle offline request: {e}")
            reason = "internal error"

        return OfflineResponse(
            MsgType.OFFLINE,
            req.request_id,
            int(time.time()),
            req.node_uuid,
            granted=0,
            disk_wwid=req.disk_wwid,
            reason=reason.encode(),
        )

    @classmethod
    def _handle_offline(cls, req: OfflineRequest) -> OfflineResponse:
        host_uuid, disk_wwid = req.get_host_uuid(), req.get_disk_wwid()

        # get disk info from disk_status
        disk_record = cls._get_disk_record_by_wwid(host_uuid, disk_wwid, raise_error=True)

        # get offline disk record views
        off_service = OfflineRecordService()
        record_views = off_service.get_record_views()
        target_record = off_service.get_node_record(record_views, host_uuid)
        if not (record_views and target_record):
            raise Exception("offline_disks records not init")

        # check whether offline disk record exist
        add_new_record = True
        for host_record in record_views["hosts"]:
            if host_record["offline_disks"]:
                # Only allowed to mark at most one offline disk
                add_new_record = False
                offline_disk = host_record["offline_disks"][0]
                if host_record["host_uuid"] == host_uuid and offline_disk["disk_wwid"] == disk_wwid:
                    logging.info(f"the offline disk already recorded: {host_record=}")
                else:
                    msg = f"another offline disk already recorded: {host_record=}"
                    logging.info(msg)
                    raise OverOfflineNum(msg)

        # ensure adding offline disk into node record
        if add_new_record:
            data_ip = target_record.get("data_ip")
            offline_record = {
                "trace_id": disk_record.get("trace_id", ""),
                "disk_path": disk_record.get("disk_path", ""),
                "disk_serial": disk_record.get("disk_serial", ""),
                "disk_wwid": disk_wwid,
                "record_by": f"{off_service.CURRENT_HOST_UUID}@{off_service.CURRENT_DATA_IP}",
                "status": MARK_TO_OFFLINE,
            }
            target_record["offline_disks"].append(offline_record)
            if off_service.save_record(record_views):
                logging.info(f"DiskOffline granted: {host_uuid=} {data_ip=} {offline_record=}")
            else:
                raise UpdateRecordDbError(f"Update record failed for {host_uuid=} {data_ip=} {offline_record=}")

        # update disk status
        hints_and_detail = {}
        hints_detected_detail = []
        if req.has_cmd_abort():
            hints_and_detail["hints.cmd_abort_offline"] = True
            hints_detected_detail.append("detected_at.cmd_abort_offline")
        if req.has_io_timeout():
            hints_and_detail["hints.io_timeout_offline"] = True
            hints_detected_detail.append("detected_at.io_timeout_offline")
        if req.has_io_eh_queue():
            hints_and_detail["hints.io_eh_queue_offline"] = True
            hints_detected_detail.append("detected_at.io_eh_queue_offline")
        if hints_and_detail:
            hints_and_detail["healthy"] = False
            query_key = {"trace_id": disk_record["trace_id"]}
            logging.info(f"update disk status {disk_record=} {hints_and_detail=}")
            DiskStatusService().update_disks_fields_with_query([(query_key, hints_and_detail)])

            now = datetime.now()

            final_update_disk_detected_list = []
            disk_status_list = DiskStatusService().list_disk_status_by_filter(id_filter=query_key)
            local_disk_status_map = {status["trace_id"]: status for status in disk_status_list}

            final_hints_detected_detail = {}
            if query_key["trace_id"] in local_disk_status_map:
                disk_status = local_disk_status_map[query_key["trace_id"]]
                for hints_detected_key in hints_detected_detail:
                    detected_key = hints_detected_key.split(".")[1]
                    if detected_key not in disk_status.get("detected_at", {}):
                        final_hints_detected_detail[hints_detected_key] = now
            if final_hints_detected_detail:
                final_update_disk_detected_list.append((query_key, final_hints_detected_detail))

            if final_update_disk_detected_list:
                DiskStatusService().update_disks_fields_with_query(final_update_disk_detected_list)

        # rpc to zbs chunk
        try:
            cls._retry_func(lambda: cls._rpc_to_zbs_chunk(target_record), retry_num=3, exp=RpcToChunkError)
        except Exception as e:
            logging.info(f"failed to notify {target_record=} to chunk: {e}")

        return OfflineResponse(
            MsgType.OFFLINE,
            req.request_id,
            int(time.time()),
            req.node_uuid,
            granted=1,
            disk_wwid=req.disk_wwid,
            reason=b"looks good to me",
        )

    @classmethod
    def _rpc_to_zbs_chunk(cls, node_record):
        data_ip, host_uuid, offline_disk = (
            node_record["data_ip"],
            node_record["host_uuid"],
            node_record["offline_disks"][0],
        )
        host_info = HostService().get_host_partial_info(host_uuid, projection=["disks"])
        if not host_info:
            raise Exception(f"host info not found for {host_uuid=} {data_ip=}")

        target_disk = None
        for disk in host_info.get("disks", []):
            if (
                disk["path"] == offline_disk["disk_path"]
                and disk["serial"] == offline_disk["disk_serial"]
                and disk.get("wwid") == offline_disk["disk_wwid"]
            ):
                target_disk = disk
                break
        if not target_disk:
            raise Exception(f"disk info not found for {host_uuid=} {data_ip=} {offline_disk=}")

        try:
            chunk_client = ZbsChunk(chunk_ip=data_ip, socket_timeout=3)
            chunk_parts = StorageDiskManager.get_disk_or_partition_info(chunk_client=chunk_client, raise_error=True)
            unhealthy_fn_map = {
                DISK_OR_PARTITION_USAGE_JOURNAL: chunk_client.journal_set_unhealthy,
                DISK_OR_PARTITION_USAGE_CACHE: chunk_client.cache_set_unhealthy,
                DISK_OR_PARTITION_USAGE_PARTITION: chunk_client.partition_set_unhealthy,
            }
            for uuid_in_chunk, chunk_part in chunk_parts.items():
                if StorageDiskManager.is_chunk_part_belong_to_disk(chunk_part, target_disk):
                    chunk_part_usage = chunk_part["usage"]
                    if chunk_part_usage in unhealthy_fn_map:
                        logging.info(f"DiskOffline notify {chunk_part_usage} unhealthy to chunk: {chunk_part=}")
                        unhealthy_fn_map[chunk_part_usage](
                            disk_name=uuid_in_chunk,
                            errflag=DiskErrFlags.DISK_ERR_IO,
                            scheme=DiskNameScheme.DISK_NAME_BY_UUID,
                        )
        except Exception as e:
            msg = f"failed to notify {node_record=} to chunk: {e}"
            logging.info(msg)
            raise RpcToChunkError(msg)

        return True

    @classmethod
    def handle_offline_ack(cls, req: OfflineAckRequest) -> OfflineAckResponse:
        request_id, host_uuid, disk_wwid = (
            req.request_id,
            req.get_host_uuid(),
            req.get_disk_wwid(),
        )

        disk_record = cls._get_disk_record_by_wwid(host_uuid, disk_wwid)
        logging.info(f"DiskOffline ACK: {request_id=} {host_uuid=} {disk_wwid=} {disk_record=}")

        try:
            cls._retry_func(lambda: cls._mark_offline_acked(host_uuid, disk_wwid), retry_num=3, exp=UpdateRecordDbError)
        except Exception as e:
            logging.warning(f"DiskOffline update ACK failed: {e}")

        return OfflineAckResponse(
            MsgType.OFFLINE_ACK,
            request_id,
            int(time.time()),
            req.node_uuid,
        )

    @classmethod
    def _mark_offline_acked(cls, host_uuid, disk_wwid):
        view_changed = False
        off_service = OfflineRecordService()
        record_views = off_service.get_record_views()
        target_record = off_service.get_node_record(record_views, host_uuid)
        if record_views and target_record and target_record["offline_disks"]:
            for offline_disk in target_record["offline_disks"]:
                if offline_disk["disk_wwid"] == disk_wwid:
                    offline_disk["status"] = OFFLINE_ACKED
                    view_changed = True

        if not view_changed:
            logging.warning(f"DiskOffline ACK not find offline disk record of {host_uuid=} {disk_wwid=} in db")
            return False

        if off_service.save_record(record_views):
            logging.info(f"DiskOffline updated ACK to {target_record=}")
        else:
            raise UpdateRecordDbError(f"Update record failed for {target_record=}")

        return True
