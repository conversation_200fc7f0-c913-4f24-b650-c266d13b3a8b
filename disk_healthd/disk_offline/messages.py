# Copyright (c) 2013-2024, SMARTX
# All rights reserved.

import dataclasses
from enum import IntEnum
import struct


class MsgType(IntEnum):
    HEART_BEAT = 0
    WARNING = 1
    OFFLINE = 2
    OFFLINE_ACK = 3


@dataclasses.dataclass
class MsgCommon:
    type_id: MsgType
    request_id: int
    timestamp: int
    node_uuid: bytes

    @classmethod
    def struct_fmt(cls):
        return "@3Q40s"

    @classmethod
    def from_bytes(cls, content: bytes):
        fmt = cls.struct_fmt()
        fmt_len = struct.calcsize(fmt)
        return cls(*struct.unpack(fmt, content[:fmt_len]))

    def to_bytes(self) -> bytes:
        fmt = self.struct_fmt()
        return struct.pack(fmt, *dataclasses.astuple(self))

    def get_host_uuid(self):
        return self.node_uuid.decode("ascii").strip("\x00").strip()


@dataclasses.dataclass
class HeartbeatRequest(MsgCommon):
    pass


@dataclasses.dataclass
class HeartbeatResponse(MsgCommon):
    pass


@dataclasses.dataclass
class WarningRequest(MsgCommon):
    disk_wwid: bytes
    msg: bytes

    @classmethod
    def struct_fmt(cls):
        return MsgCommon.struct_fmt() + "96s256s"

    def get_disk_wwid(self):
        return self.disk_wwid.decode("ascii").strip("\x00").strip()

    def get_msg(self):
        return self.msg.decode("ascii").strip("\x00").strip()


@dataclasses.dataclass
class WarningResponse(MsgCommon):
    pass


@dataclasses.dataclass
class OfflineRequest(MsgCommon):
    disk_wwid: bytes
    reason: int

    @classmethod
    def struct_fmt(cls):
        return MsgCommon.struct_fmt() + "96s1Q"

    def get_disk_wwid(self):
        return self.disk_wwid.decode("ascii").strip("\x00").strip()

    def has_io_eh_queue(self):
        return bool(self.reason & (1 << 0))

    def has_cmd_abort(self):
        return bool(self.reason & (1 << 1))

    def has_io_timeout(self):
        return bool(self.reason & (1 << 2))


@dataclasses.dataclass
class OfflineResponse(MsgCommon):
    granted: int
    disk_wwid: bytes
    reason: bytes

    @classmethod
    def struct_fmt(cls):
        return MsgCommon.struct_fmt() + "1Q96s256s"

    def get_disk_wwid(self):
        return self.disk_wwid.decode("ascii").strip("\x00").strip()

    def get_reason(self):
        return self.reason.decode("ascii").strip("\x00").strip()


@dataclasses.dataclass
class OfflineAckRequest(MsgCommon):
    disk_wwid: bytes

    @classmethod
    def struct_fmt(cls):
        return MsgCommon.struct_fmt() + "96s"

    def get_disk_wwid(self):
        return self.disk_wwid.decode("ascii").strip("\x00").strip()


@dataclasses.dataclass
class OfflineAckResponse(MsgCommon):
    pass
