[main]
# CHUNK_STAT_LOOP_INTERVAL_S = 10
# DISKSTATS_LOOP_INTERVAL_S = 15
# ISOLATE_LOOP_INTERVAL_S = 30
# 6h = 3600s * 6 = 21600s
# SMARTCTL_LOOP_INTERVAL_S = 21600
# RAID_LOOP_INTERVAL_S = 30

[diskstats_loop]
# ENABLE_LATENCY_HINT = true
# SAMPLING_WIN_SIZE = 6

# THRESHOLD_SSD_LATENCY_MS = 500
# THRESHOLD_SSD_LATENCY_MS_ISOLATE = 2000
# THRESHOLD_SSD_IOPS = 5000
# THRESHOLD_SSD_IOPS_ISOLATE = 2000
# 150MiB = 150 * 1024 * 1024 / 512 = 307200
# THRESHOLD_SSD_SECTOR_PS = 307200
# 50MiB = 50 * 1024 * 1024 / 512 = 102400
# THRESHOLD_SSD_SECTOR_PS_ISOLATE = 102400

# THRESHOLD_HDD_LATENCY_MS = 3000
# THRESHOLD_HDD_IOPS = 50
# 50MiB = 50 * 1024 * 1024 / 512 = 102400
# THRESHOLD_HDD_SECTOR_PS = 102400

# for debug purpose
# THRESHOLD_LOG_LATENCY_MS = 500
# ENABLE_LATENCY_DEBUG_LOG = false

[smartctl_loop]
# SMARTCTL_TIMEOUT_S = 90
# SMARTCTL_HANG_ALERT = false
# EXTRA_SMART_CHECK = false

# ID    ATTRIBUTE_NAME                        CHECK_FIELD    TRIGGER    DEFAULT
# 5     Reallocated Sectors Count             raw            no         <= 500
# 177   Wear Leveling Count     @ Samsung     value          no         >= 20
# 187   Reported Uncorrectable Errors         raw            yes        <= 0
# 188   Command Timeout                       value          no         >= 10
# 194   Temperature_Celsius                   raw            no         <= 45
# 197   Current Pending Sector Count          raw            no         <= 0
# 198   Uncorrectable Sector Count            raw            yes        <= 0
# 233   Media Wearout Indicator @ Intel       value          no         >= 20

# THRESHOLD_SMART_5 = 500
# THRESHOLD_SMART_177 = 20
# THRESHOLD_SMART_187 = 0
# THRESHOLD_SMART_188 = 10
# THRESHOLD_SMART_194 = 45
# THRESHOLD_SMART_197 = 0
# THRESHOLD_SMART_198 = 0
# THRESHOLD_SMART_233 = 20

# THRESHOLD_OF_REALLOCATED_SECTORS_COUNT = 400

[chunk_status_loop]
# THRESHOLD_NUM_IO_ERRORS = 300
# THRESHOLD_NUM_CHECKSUM_ERRORS = 100
