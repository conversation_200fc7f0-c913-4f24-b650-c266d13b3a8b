name: disk_status
path: /api/v2/exporter/disk_healthd/disk_status
priority: VERY_HIGH
timeout: 25s
interval: 30s
metric_alert_rules:
  - alerting_rules:
      - causes:
          - locale: zh-CN
            str: 物理盘 S.M.A.R.T. 检测异常。
          - locale: en-US
            str: The physical disk S.M.A.R.T. detects abnormalities.
        impacts:
          - locale: zh-CN
            str: 物理盘故障风险显著提高。
          - locale: en-US
            str: The risk of physical disk failure is significantly increased.
        messages:
          - locale: zh-CN
            str: ${TARGET_CN} { .labels.hostname } 的物理盘 { .labels._serial_number }({ .labels._device }) S.M.A.R.T. 检测不通过。
          - locale: en-US
            str: '${TARGET_US_HEAD} { .labels.hostname }: Physical disk { .labels._serial_number }({ .labels._device }) S.M.A.R.T. check failed.'
        metric_name: ${target}_disk_smart_test_failed
        name: ${target}_disk_smart_test_failed
        operator: EQ
        solutions:
          - locale: zh-CN
            str: 请及时更换物理盘。
          - locale: en-US
            str: Please replace the physical disk in time.
        thresholds:
          - severity: CRITICAL
            value: 1
      - causes:
          - locale: zh-CN
            str: 检测到物理盘预期寿命不足。
          - locale: en-US
            str: An insufficient life expectancy of the physical disk was detected.
        impacts:
          - locale: zh-CN
            str: 物理盘寿命到期后，故障风险和性能下降的概率将显著提高。
          - locale: en-US
            str: After the life of the physical disk expires, the risk of failure and the probability of performance degradation will increase significantly.
        messages:
          - locale: zh-CN
            str: ${TARGET_CN} { .labels.hostname } 的物理盘 { .labels._serial_number }({ .labels._device }) 寿命不足 { .threshold }。
          - locale: en-US
            str: '${TARGET_US_HEAD} { .labels.hostname }: The remaining lifetime of the physical disk { .labels._serial_number }({ .labels._device }) is less than { .threshold }.'
        metric_name: ${target}_disk_remaining_life_percent
        name: ${target}_disk_remaining_life_percent
        operator: LT
        solutions:
          - locale: zh-CN
            str: 请及时更换物理盘。
          - locale: en-US
            str: Please replace the physical disk in time.
        thresholds:
          - severity: NOTICE
            value: 20
    labels:
      - _${target}
      - _device
      - _serial_number
    metric_descriptors:
      - is_boolean: true
        metric_name: ${target}_disk_smart_test_failed
      - is_boolean: false
        metric_name: ${target}_disk_remaining_life_percent
        unit: PERCENT
      - is_boolean: false
        metric_name: ${target}_disk_temperature_celsius
        unit: CELSIUS
  - alerting_rules:
      - causes:
          - locale: zh-CN
            str: 物理盘频繁出现 I/O 超时或终止，或物理盘上的 I/O 请求进入异常 I/O 队列。
          - locale: en-US
            str: The physical disk frequently encountered I/O timeouts or terminations, or its I/O requests entered the abnormal I/O queue.
        impacts:
          - locale: zh-CN
            str: 该物理盘已停止处理任何 I/O 请求，其提供的存储容量将计入失效空间。
          - locale: en-US
            str: The physical disk stopped processing any I/O requests, and its storage capacity will be identified as invalid.
        messages:
          - locale: zh-CN
            str: ${TARGET_CN} { .labels.hostname } 的物理盘 { .labels._serial_number }({ .labels._device }) 发生 I/O 阻塞，已将该物理盘下线。
          - locale: en-US
            str: 'The physical disk { .labels._serial_number }({ .labels._device }) on the ${TARGET_US_INLINE} { .labels.hostname } experienced I/O block and has been taken offline.'
        metric_name: ${target}_io_block_disk_offline
        name: ${target}_io_block_disk_offline
        operator: EQ
        solutions:
          - locale: zh-CN
            str: 联系售后技术支持，更换物理盘。
          - locale: en-US
            str: Contact technical support to replace the physical disk.
        thresholds:
          - severity: CRITICAL
            value: 1
      - causes:
          - locale: zh-CN
            str: 检测到物理盘出现错误。
          - locale: en-US
            str: An error has been detected on the physical disk.
        impacts:
          - locale: zh-CN
            str: 系统会自动隔离该盘，同时触发数据恢复。该盘提供的存储容量会计入失效空间。
          - locale: en-US
            str: The system will automatically isolate the disk and trigger data recovery. The storage capacity provided by the disk is included in the invalidation space.
        messages:
          - locale: zh-CN
            str: ${TARGET_CN} { .labels.hostname } 的物理盘 { .labels._serial_number }({ .labels._device }) 处于不健康状态，系统会自动隔离该盘，并将数据恢复至其他健康物理盘。请勿拔盘。
          - locale: en-US
            str: '${TARGET_US_HEAD} { .labels.hostname }: The physical disk { .labels._serial_number }({ .labels._device }) is unhealthy. The system will automatically isolate the disk and restore the data to other healthy physical disks. Do not pull the disk.'
        metric_name: ${target}_unhealthy_disk_in_isolate
        name: ${target}_unhealthy_disk_in_isolate
        operator: EQ
        solutions:
          - locale: zh-CN
            str: 等待系统自动完成该盘的隔离。
          - locale: en-US
            str: Wait for the system to automatically complete the isolation of the disk.
        thresholds:
          - severity: NOTICE
            value: 1
      - causes:
          - locale: zh-CN
            str: 检测到物理盘性能下降。
          - locale: en-US
            str: The performance of the physical disk is degraded.
        impacts:
          - locale: zh-CN
            str: 系统会自动隔离该盘，同时触发数据恢复。该盘提供的存储容量会计入失效空间。
          - locale: en-US
            str: The system will automatically isolate the disk and trigger data recovery. The storage capacity provided by the disk is included in the invalidation space.
        messages:
          - locale: zh-CN
            str: ${TARGET_CN} { .labels.hostname } 的物理盘 { .labels._serial_number }({ .labels._device }) 处于亚健康状态，系统会自动隔离该盘，并将数据恢复至其他健康物理盘。请勿拔盘。
          - locale: en-US
            str: '${TARGET_US_HEAD} { .labels.hostname }: The physical disk { .labels._serial_number }({ .labels._device }) is in sub-health state. The system will automatically isolate the disk and restore the data to other healthy physical disks. Do not pull the disk.'
        metric_name: ${target}_sub_healthy_disk_in_isolate
        name: ${target}_sub_healthy_disk_in_isolate
        operator: EQ
        solutions:
          - locale: zh-CN
            str: 等待系统自动完成该盘的隔离。
          - locale: en-US
            str: Wait for the system to automatically complete the isolation of the disk.
        thresholds:
          - severity: NOTICE
            value: 1
      - causes:
          - locale: zh-CN
            str: 检测到物理盘性能下降。
          - locale: en-US
            str: The performance of the physical disk is degraded.
        impacts:
          - locale: zh-CN
            str: 物理盘有损坏风险，可能会影响业务或虚拟机性能。
          - locale: en-US
            str: There is a risk of damage to the physical disk, which may affect business or virtual machine performance.
        messages:
          - locale: zh-CN
            str: ${TARGET_CN} { .labels.hostname } 的物理盘 { .labels._serial_number }({ .labels._device }) 处于亚健康状态，请卸载此盘。
          - locale: en-US
            str: '${TARGET_US_HEAD} { .labels.hostname }: The physical disk { .labels._serial_number }({ .labels._device }) is in sub-healthy state. Please remove the disk.'
        metric_name: ${target}_sub_healthy_disk_without_isolate
        name: ${target}_sub_healthy_disk_without_isolate
        operator: EQ
        solutions:
          - locale: zh-CN
            str: 请及时更换物理盘。
          - locale: en-US
            str: Please replace the physical disk in time.
        thresholds:
          - severity: CRITICAL
            value: 1
      - causes:
          - locale: zh-CN
            str: 无法自动隔离该物理盘上剩余的分区。
          - locale: en-US
            str: The remaining partitions on the physical disk cannot be automatically isolated.
        impacts:
          - locale: zh-CN
            str: 无法完全隔离物理盘。
          - locale: en-US
            str: The physical disk cannot be completely isolated.
        messages:
          - locale: zh-CN
            str: ${TARGET_CN} { .labels.hostname } 的物理盘 { .labels._serial_number }({ .labels._device }) 出现故障，已完成隔离。请卸载其上剩余的分区。
          - locale: en-US
            str: '${TARGET_US_HEAD} { .labels.hostname }: Disk { .labels._serial_number }({ .labels._device }) failure and has been isolated. Please unmount the remaining partitions on it.'
        metric_name: ${target}_unhealthy_disk_with_left_partition
        name: ${target}_unhealthy_disk_with_left_partition
        operator: EQ
        solutions:
          - locale: zh-CN
            str: 卸载物理盘。若物理盘已经处于卸载中，只需等待卸载完成即可。
          - locale: en-US
            str: Unmount the physical disk. If the physical disk is already being uninstalled, just wait for the uninstallation to complete.
        thresholds:
          - severity: CRITICAL
            value: 1
      - causes:
          - locale: zh-CN
            str: 物理盘包含${PADDING}${TARGET_CN}${PADDING}上最后一个系统分区或元数据分区。
          - locale: en-US
            str: The physical disk contains the last system or meta partition on the ${TARGET_US_INLINE}.
        impacts:
          - locale: zh-CN
            str: 无法完全隔离物理盘。
          - locale: en-US
            str: The physical disk cannot be completely isolated.
        messages:
          - locale: zh-CN
            str: ${TARGET_CN} { .labels.hostname } 的物理盘 { .labels._serial_number }({ .labels._device }) 出现故障，且物理盘上存在${PADDING}${TARGET_CN}${PADDING}最后一个系统分区或元数据分区。
          - locale: en-US
            str: ${TARGET_US_HEAD} { .labels.hostname }： Disk { .labels._serial_number }({ .labels._device }) failure, and the last system or meta partition of the ${TARGET_US_INLINE} exists on the physical disk.
        metric_name: ${target}_unhealthy_disk_with_last_smtx_system
        name: ${target}_unhealthy_disk_with_last_smtx_system
        operator: EQ
        solutions:
          - locale: zh-CN
            str: 联系售后。
          - locale: en-US
            str: Please contact customer support.
        thresholds:
          - severity: CRITICAL
            value: 1
      - causes:
          - locale: zh-CN
            str: 物理盘出现故障或有故障风险，且未被使用。
          - locale: en-US
            str: The physical disk is faulty or has a risk of failure and is not used.
        impacts:
          - locale: zh-CN
            str: 无影响。
          - locale: en-US
            str: No impact.
        messages:
          - locale: zh-CN
            str: ${TARGET_CN} { .labels.hostname } 的物理盘 { .labels._serial_number }({ .labels._device }) 出现故障，现处于未挂载状态，可安全拔出。
          - locale: en-US
            str: '${TARGET_US_HEAD} { .labels.hostname }: The physical disk { .labels._serial_number }({ .labels._device }) failure and is now unmounted and can be removed safely.'
        metric_name: ${target}_unhealthy_disk_offline
        name: ${target}_unhealthy_disk_offline
        operator: EQ
        solutions:
          - locale: zh-CN
            str: 当集群没有数据恢复时，可安全拔出该物理盘。
          - locale: en-US
            str: When there is no data recovery underway, this physical disk can be safely unplugged.
        thresholds:
          - severity: INFO
            value: 1
    labels:
      - _${target}
      - _device
      - _serial_number
    metric_descriptors:
      - is_boolean: true
        metric_name: ${target}_unhealthy_disk_in_isolate
      - is_boolean: true
        metric_name: ${target}_sub_healthy_disk_in_isolate
      - is_boolean: true
        metric_name: ${target}_sub_healthy_disk_without_isolate
      - is_boolean: true
        metric_name: ${target}_unhealthy_disk_with_left_partition
      - is_boolean: true
        metric_name: ${target}_unhealthy_disk_with_last_smtx_system
      - is_boolean: true
        metric_name: ${target}_unhealthy_disk_offline
      - is_boolean: true
        metric_name: ${target}_io_block_disk_offline
    platform: [kvm, vmware, san]
