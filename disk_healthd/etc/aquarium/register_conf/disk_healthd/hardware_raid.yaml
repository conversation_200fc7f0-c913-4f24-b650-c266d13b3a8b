name: hardware_raid
path: /api/v2/exporter/disk_healthd/hardware_raid
priority: VERY_HIGH
timeout: 25s
interval: 30s
metric_alert_rules:
  - alerting_rules:
      - causes:
          - locale: zh-CN
            str: 硬件 RAID 下的虚拟盘状态异常。
          - locale: en-US
            str: The hardware RAID virtual disk is abnormal.
        impacts:
          - locale: zh-CN
            str: 硬件 RAID 下的虚拟盘可能无法被系统正常使用。
          - locale: en-US
            str: The hardware RAID virtual disk might not function properly.
        messages:
          - locale: zh-CN
            str: ${TARGET_CN} { .labels.hostname } 硬件 RAID 下的虚拟盘 { .labels._vd_name } 状态异常：{ .labels.raw_status }。
          - locale: en-US
            str: 'The hardware RAID virtual disk { .labels._vd_name } on the ${TARGET_US_INLINE} { .labels.hostname } is abnormal: { .labels.raw_status }.'
        metric_name: ${target}_hardware_raid_vd_status_error
        name: ${target}_hardware_raid_vd_status_error
        operator: EQ
        solutions:
          - locale: zh-CN
            str: 重新配置硬件 RAID 下的虚拟盘，纠正错误状态。
          - locale: en-US
            str: Reconfigure the hardware RAID virtual disk to correct its error state.
        thresholds:
          - severity: CRITICAL
            value: 1
      - causes:
          - locale: zh-CN
            str: 硬件 RAID 下组成虚拟盘的物理盘数量少于二。
          - locale: en-US
            str: The number of physical disks constituting the hardware RAID virtual disk is less than two.
        impacts:
          - locale: zh-CN
            str: 当前无影响。如果硬件 RAID 下虚拟盘中的最后一块物理盘损坏，可能导致服务器无法正常启动，或启动后系统运行异常。
          - locale: en-US
            str: Currently no impact. However, if the last physical disk constituting the hardware RAID virtual disk is damaged, the server might not be able to boot properly; if the server can be booted, the system might encounter exceptions.
        messages:
          - locale: zh-CN
            str: ${TARGET_CN} { .labels.hostname } 硬件 RAID 下的虚拟盘 { .labels._vd_name } 冗余度不足。
          - locale: en-US
            str: 'The hardware RAID virtual disk { .labels._vd_name } on the ${TARGET_US_INLINE} { .labels.hostname } has insufficient redundancy.'
        metric_name: ${target}_hardware_raid_vd_num_of_pd
        name: ${target}_hardware_raid_vd_num_of_pd
        operator: LT
        solutions:
          - locale: zh-CN
            str: 按需增加物理盘数量，重新配置硬件 RAID 下的虚拟盘，纠正错误状态。
          - locale: en-US
            str: Increase the number of physical disks as needed, and reconfigure the hardware RAID virtual disk to correct its error state.
        thresholds:
          - severity: NOTICE
            value: 2
      - causes:
          - locale: zh-CN
            str: 硬件 RAID 下的物理盘 S.M.A.R.T. 检测异常。
          - locale: en-US
            str: The physical disk in hardware RAID failed the S.M.A.R.T. test.
        impacts:
          - locale: zh-CN
            str: 硬件 RAID 下的物理盘故障风险显著提高。
          - locale: en-US
            str: The physical disk has a significant risk of failure.
        messages:
          - locale: zh-CN
            str: ${TARGET_CN} { .labels.hostname } 硬件 RAID 下的物理盘 { .labels._serial } S.M.A.R.T. 检测不通过。
          - locale: en-US
            str: 'The physical disk { .labels._serial } in hardware RAID on the ${TARGET_US_INLINE} { .labels.hostname } failed the S.M.A.R.T. test.'
        metric_name: ${target}_hardware_raid_pd_smart_error
        name: ${target}_hardware_raid_pd_smart_error
        operator: EQ
        solutions:
          - locale: zh-CN
            str: 联系售后技术支持，更换物理盘。
          - locale: en-US
            str: Contact technical support to replace the physical disk.
        thresholds:
          - severity: CRITICAL
            value: 1
      - causes:
          - locale: zh-CN
            str: 检测到硬件 RAID 下的物理盘预期寿命不足。
          - locale: en-US
            str: The physical disk in hardware RAID has an insufficient lifespan.
        impacts:
          - locale: zh-CN
            str: 物理盘寿命到期后，故障风险和性能下降的概率将显著提高。
          - locale: en-US
            str: After the physical disk reaches the end of its lifespan, its risk of failure and possibility of performance degradation will increase significantly.
        messages:
          - locale: zh-CN
            str: ${TARGET_CN} { .labels.hostname } 硬件 RAID 下的物理盘 { .labels._serial } 寿命不足 { .threshold }。
          - locale: en-US
            str: 'The physical disk { .labels._serial } in hardware RAID on the ${TARGET_US_INLINE} { .labels.hostname } has a remaining lifespan below { .threshold }. '
        metric_name: ${target}_hardware_raid_pd_lifespan
        name: ${target}_hardware_raid_pd_lifespan
        operator: LT
        solutions:
          - locale: zh-CN
            str: 联系售后技术支持，更换物理盘。
          - locale: en-US
            str: Contact technical support to replace the physical disk.
        thresholds:
          - severity: NOTICE
            value: 20
      - causes:
          - locale: zh-CN
            str: 硬件 RAID 下的虚拟盘的 Write cache 策略非预期的 Write through 策略。
          - locale: en-US
            str: The Write cache policy of hardware RAID virtual disk is not Write through.
        impacts:
          - locale: zh-CN
            str: 使用了硬件 RAID 下的虚拟盘在遇上断电场景时可能会导致数据丢失。
          - locale: en-US
            str: Using the hardware RAID virtual disk can lead to data loss in the event of a power outage.
        messages:
          - locale: zh-CN
            str: ${TARGET_CN} { .labels.hostname } 系统盘所在的硬件 RAID 下的虚拟盘 { .labels._vd_name } 的 Write cache 策略非  Write through，请尽快进行调整，以便正常完成后续操作。
          - locale: en-US
            str: 'The Write cache policy of hardware RAID virtual disk { .labels._vd_name } on which the system disk of the ${TARGET_US_INLINE} { .labels.hostname } resides is not Write through, please reconfigure it soon.'
        metric_name: ${target}_hardware_raid_write_cache_policy_incorrect
        name: ${target}_hardware_raid_write_cache_policy_incorrect
        operator: EQ
        solutions:
          - locale: zh-CN
            str: 重新配置硬件 RAID 下的虚拟盘的 Write cache 策略为 Write through 策略。
          - locale: en-US
            str: Reconfigure the Write cache policy of the hardware RAID virtual disk to Write through.
        thresholds:
          - severity: CRITICAL
            value: 1
    labels:
      - _${target}
      - _vd_id
      - _vd_name
    metric_descriptors:
      - is_boolean: true
        metric_name: ${target}_hardware_raid_vd_status_error
      - is_boolean: false
        metric_name: ${target}_hardware_raid_vd_num_of_pd
      - is_boolean: true
        metric_name: ${target}_hardware_raid_pd_smart_error
        labels:
          - _${target}
          - _pd_id
          - _serial
      - is_boolean: false
        metric_name: ${target}_hardware_raid_pd_lifespan
        unit: PERCENT
        labels:
          - _${target}
          - _pd_id
          - _serial
      - is_boolean: true
        metric_name: ${target}_hardware_raid_write_cache_policy_incorrect
