name: disk_stats
path: /api/v2/exporter/disk_healthd/disk_stats
priority: HIGH
timeout: 25s
interval: 30s
metric_alert_rules:
  - alerting_rules: []
    labels: [_${target}, _device]
    metric_descriptors:
    - metric_name: ${target}_disk_read_iops
    - metric_name: ${target}_disk_write_iops
    - metric_name: ${target}_disk_readwrite_iops
    - metric_name: ${target}_disk_read_speed_bps
      unit: BIT_PER_SECOND
    - metric_name: ${target}_disk_write_speed_bps
      unit: BIT_PER_SECOND
    - metric_name: ${target}_disk_readwrite_speed_bps
      unit: BIT_PER_SECOND
    - metric_name: ${target}_disk_avg_read_latency_ns
      unit: NANOSECOND
    - metric_name: ${target}_disk_avg_write_latency_ns
      unit: NANOSECOND
    - metric_name: ${target}_disk_avg_readwrite_latency_ns
      unit: NANOSECOND
    - metric_name: ${target}_disk_avg_read_size_bytes
      unit: BYTE
    - metric_name: ${target}_disk_avg_write_size_bytes
      unit: BYTE
    - metric_name: ${target}_disk_avg_readwrite_size_bytes
      unit: BYTE
    - metric_name: ${target}_disk_utilization_percent
      unit: PERCENT
    - metric_name: ${target}_disk_avg_readwrite_queue_length
