name: disk_event
path: /api/v2/exporter/disk_healthd/disk_event
priority: VERY_HIGH
timeout: 8s
interval: 10s
metric_alert_rules:
  - alerting_rules:
    - causes:
      - locale: zh-CN
        str: 有物理盘从${PADDING}${TARGET_CN}${PADDING}拔出。
      - locale: en-US
        str: A disk has been unplugged from the ${TARGET_US_INLINE}.
      impacts:
      - locale: zh-CN
        str: 意外地拔盘将会触发数据恢复。
      - locale: en-US
        str: Unexpected disk unplugging will trigger data recovery.
      messages:
      - locale: zh-CN
        str: '于 { .labels._created_time } 侦测到物理盘 { .labels.disk_id_serial }({ .labels._device }) 从${PADDING}${TARGET_CN} { .labels._hostname } 拔出。'
      - locale: en-US
        str: '${TARGET_US_HEAD} { .labels._hostname }: The disk { .labels.disk_id_serial }({ .labels._device }) was unplugged at { .labels._created_time }.'
      metric_name: event_disk_remove
      name: event_disk_remove
      operator: EQ
      query_tmpl: max without(instance) (event_disk_remove) == { .threshold }
      solutions:
      - locale: zh-CN
        str: 如果是意外拔盘或物理盘已损坏，请联系售后。
      - locale: en-US
        str: If the physical disk was unplugged unexpectedly or is damaged, please contact customer support.
      thresholds:
      - severity: CRITICAL
        value: 1
    - causes:
      - locale: zh-CN
        str: 有物理盘从${PADDING}${TARGET_CN}${PADDING}拔出。
      - locale: en-US
        str: A disk has been unplugged from the ${TARGET_US_INLINE}.
      impacts:
      - locale: zh-CN
        str: 意外地拔盘将会触发数据恢复。
      - locale: en-US
        str: Unexpected disk unplugging will trigger data recovery.
      messages:
      - locale: zh-CN
        str: '于 { .labels._created_time } 侦测到物理盘 { .labels.disk_id_serial }({ .labels._device }) 从${PADDING}${TARGET_CN} { .labels._hostname }
          拔出，该盘原位于机箱 { .labels._brick_name } 的插槽位置 { .labels._physical_slot }。'
      - locale: en-US
        str: '${TARGET_US_HEAD} { .labels._hostname }: The disk { .labels.disk_id_serial }({ .labels._device }) was unplugged at { .labels._created_time
          }. The disk was located on the slot { .labels._physical_slot } of brick { .labels._brick_name }.'
      metric_name: event_slot_disk_remove
      name: event_slot_disk_remove
      operator: EQ
      query_tmpl: max without(instance) (event_slot_disk_remove) == { .threshold }
      solutions:
      - locale: zh-CN
        str: 如果是意外拔盘或物理盘已损坏，请联系售后。
      - locale: en-US
        str: If the physical disk was unplugged unexpectedly or is damaged, please contact customer support.
      thresholds:
      - severity: CRITICAL
        value: 1
    - causes:
      - locale: zh-CN
        str: ${TARGET_CN}${PADDING}插入物理盘。
      - locale: en-US
        str: A physical disk has been plugged into the ${TARGET_US_INLINE}.
      impacts:
      - locale: zh-CN
        str: 无影响。
      - locale: en-US
        str: No impact.
      messages:
      - locale: zh-CN
        str: '于 { .labels._created_time } 侦测到物理盘 { .labels.disk_id_serial }({ .labels._device }) 从${PADDING}${TARGET_CN} { .labels._hostname } 插入。'
      - locale: en-US
        str: '${TARGET_US_HEAD} { .labels._hostname }: The disk { .labels.disk_id_serial }({ .labels._device }) was plugged into host at { .labels._created_time }.'
      metric_name: event_disk_add
      name: event_disk_add
      operator: EQ
      query_tmpl: max without(instance) (event_disk_add) == { .threshold }
      solutions:
      - locale: zh-CN
        str: 挂载该物理盘，或按需拔出。
      - locale: en-US
        str: Mount this physical disk or unplug it when necessary.
      thresholds:
      - severity: INFO
        value: 1
    labels:
    - _hostname
    - _device
    - _event_uuid
    metric_descriptors:
    - is_boolean: true
      metric_name: event_disk_add
    - is_boolean: true
      labels:
      - _hostname
      - _device
      - _event_uuid
      - _physical_slot
      - _brick_name
      metric_name: event_slot_disk_add
    - is_boolean: true
      metric_name: event_disk_remove
    - is_boolean: true
      labels:
      - _hostname
      - _device
      - _event_uuid
      - _physical_slot
      - _brick_name
      metric_name: event_slot_disk_remove
