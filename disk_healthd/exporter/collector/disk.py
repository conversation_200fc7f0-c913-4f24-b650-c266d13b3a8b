# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
import logging

from common.exporter.metric_family import TARGET_SCVM, get_metric_class
from disk_healthd.exporter.metrics.disk import DiskMetric, DiskStatusMetric
from tuna.service.disk_status_service import DiskStatusService
from tuna.service.host_service import HostService


class DiskCollector:
    def __init__(self, target=TARGET_SCVM):
        self.__target = target

    def collect(self):
        metrics = []

        metric_strs = [
            # (metric_name, metric_desc, metric_key)
            (self.__target + "_disk_read_iops", "Read IOPS", "r_iops"),
            (self.__target + "_disk_write_iops", "Write IOPS", "w_iops"),
            (self.__target + "_disk_readwrite_iops", "Total IOPS", "rw_iops"),
            (self.__target + "_disk_read_speed_bps", "Read Bandwidth", "r_bw"),
            (self.__target + "_disk_write_speed_bps", "Write Bandwidth", "w_bw"),
            (self.__target + "_disk_readwrite_speed_bps", "Total Bandwidth", "rw_bw"),
            (self.__target + "_disk_avg_read_latency_ns", "Read Average Latency", "r_lat"),
            (self.__target + "_disk_avg_write_latency_ns", "Write Average Latency", "w_lat"),
            (self.__target + "_disk_avg_readwrite_latency_ns", "Total Average Latency", "rw_lat"),
            (self.__target + "_disk_avg_read_size_bytes", "Read Average Block Size", "r_bs"),
            (self.__target + "_disk_avg_write_size_bytes", "Write Average Block Size", "w_bs"),
            (self.__target + "_disk_avg_readwrite_size_bytes", "Total Average Block Size", "rw_bs"),
            (self.__target + "_disk_utilization_percent", "Utilization", "util"),
            (self.__target + "_disk_avg_readwrite_queue_length", "Length of IO Queue", "io_queue_length"),
        ]

        metric_objs = []
        metric_class = get_metric_class(self.__target)
        labels = ["_device", "disk_model", "disk_type", "firmware_version", "serial_number", "capacity"]
        for ms in metric_strs:
            metric_objs.append(metric_class(ms[0], ms[1], labels=labels))

        disk_metrics = DiskMetric.get_io_stats_metric()
        for idx, obj in enumerate(metric_objs):
            for metric in disk_metrics:
                if metric.get(metric_strs[idx][2]) is not None:
                    label_values = [
                        metric.get("name", ""),
                        metric.get("model", ""),
                        metric.get("type", ""),
                        metric.get("firmware", ""),
                        metric.get("serial", ""),
                        metric.get("size", ""),
                    ]
                    obj.add_metric(label_values, float(metric.get(metric_strs[idx][2])))
            metrics.append(obj)
        return metrics


class DiskStatusCollector:
    def __init__(self, target=TARGET_SCVM):
        self.__target = target

    def collect(self):
        metrics = []

        host_info = HostService().get_current_host_info()
        if not host_info:
            logging.warning("get host info failed during collecting disk status metrics")
            return metrics

        DiskStatusService().attach_disk_status(
            [host_info],
            attach_mode=DiskStatusService.ATTACH_LOCAL,
            include_status_record=True,
        )
        disk_metrics = DiskStatusMetric(host_info.get("disks", []))

        metric_entries = [
            (
                "_io_block_disk_offline",
                "bool: io blocked disk offline",
                disk_metrics.io_block_offline,
            ),
            (
                "_unhealthy_disk_in_isolate",
                "bool: unhealthy disk in isolate",
                disk_metrics.unhealthy_in_isolate,
            ),
            (
                "_sub_healthy_disk_in_isolate",
                "bool: sub healthy disk in isolate",
                disk_metrics.sub_healthy_in_isolate,
            ),
            (
                "_sub_healthy_disk_without_isolate",
                "bool: sub healthy disk without isolate",
                disk_metrics.sub_healthy_without_isolate,
            ),
            (
                "_unhealthy_disk_with_left_partition",
                "bool: unhealthy disk with left partition",
                disk_metrics.unhealthy_disk_with_left_partition,
            ),
            (
                "_unhealthy_disk_with_last_smtx_system",
                "bool: unhealthy disk with last smtx system",
                disk_metrics.unhealthy_disk_with_last_smtx_system,
            ),
            (
                "_disk_smart_test_failed",
                "bool: S.M.A.R.T. test",
                disk_metrics.smart_test_failed_disk,
            ),
            (
                "_unhealthy_disk_offline",
                "bool: unhealthy disk not in use",
                disk_metrics.unhealthy_disk_not_in_use,
            ),
        ]

        metric_class = get_metric_class(self.__target)
        labels = ["_device", "_serial_number"]
        for ms in metric_entries:
            metric_obj = metric_class(self.__target + ms[0], ms[1], labels=labels)
            for disk in ms[2]:
                # if disk is offline, fallback to disk status record
                status_record = disk.get("disk_status") or {}
                disk_name = disk.get("name", "")
                disk_serial = disk.get("serial") or status_record.get("disk_serial") or ""
                metric_obj.add_metric([disk_name, disk_serial], 1)
            metrics.append(metric_obj)

        extra_metrics = [
            ("_disk_remaining_life_percent", "Lifetime Remained %", "lifespan"),
            ("_disk_temperature_celsius", "Temperature in Centimetre", "celsius_temperature"),
        ]
        metric_objs = []
        metric_class = get_metric_class(self.__target)
        labels = ["_device", "_serial_number", "_disk_model"]
        for ms in extra_metrics:
            metric_objs.append(metric_class(self.__target + ms[0], ms[1], labels=labels))

        info_disks = host_info.get("disks", [])
        for idx, obj in enumerate(metric_objs):
            for disk in info_disks:
                value = disk.get("extra_info", {}).get(extra_metrics[idx][2])
                if value is not None:
                    # if disk is offline, fallback to disk status record
                    status_record = disk.get("disk_status") or {}
                    disk_name = disk.get("name", "")
                    disk_serial = disk.get("serial") or status_record.get("disk_serial") or ""
                    disk_model = disk.get("model") or status_record.get("udev_properties", {}).get("ID_MODEL") or ""
                    obj.add_metric([disk_name, disk_serial, disk_model], float(value))
            metrics.append(obj)
        return metrics
