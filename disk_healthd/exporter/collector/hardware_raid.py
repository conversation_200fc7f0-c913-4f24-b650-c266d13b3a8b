# Copyright (c) 2013-2024, SMARTX
# All rights reserved.

from common.exporter.metric_family import TARGET_SCVM, get_metric_class
from disk_healthd.exporter.metrics.hardware_raid import HardwareRaidMetric


class HardwareRaidCollector:
    def __init__(self, target=TARGET_SCVM):
        self.__target = target

    def collect(self):
        vds = HardwareRaidMetric.get_vd_metrics()

        metric_class = get_metric_class(self.__target)
        vd_status_metrics = metric_class(
            self.__target + "_hardware_raid_vd_status_error",
            "Hardware RAID Virtual Disk Status",
            labels=["_vd_id", "_vd_name", "raid_mode", "raw_status", "cmd_tool"],
        )
        vd_num_of_pd_metrics = metric_class(
            self.__target + "_hardware_raid_vd_num_of_pd",
            "Number of PDs in Hardware RAID VD",
            labels=["_vd_id", "_vd_name"],
        )
        vd_write_cache_policy_metrics = metric_class(
            self.__target + "_hardware_raid_write_cache_policy_incorrect",
            "Hardware RAID Write Cache Policy Incorrect",
            labels=["_vd_id", "_vd_name"],
        )
        pd_smart_test_metrics = metric_class(
            self.__target + "_hardware_raid_pd_smart_error",
            "Hardware RAID Physical Disk S.M.A.R.T. test failed",
            labels=["_pd_id", "_serial", "vd_id", "vd_name"],
        )
        pd_lifespan_metrics = metric_class(
            self.__target + "_hardware_raid_pd_lifespan",
            "Hardware RAID Physical Disk Lifespan",
            labels=["_pd_id", "_serial", "vd_id", "vd_name"],
        )
        for vd in vds:
            vd_status_metrics.add_metric(
                [vd["vd_id"], vd["name"], vd["raid_mode"], vd["raw_status"], vd["cmd_tool"]],
                int(vd["hints"]["vd_status_error"]),
            )
            vd_num_of_pd_metrics.add_metric(
                [vd["vd_id"], vd["name"]],
                len(vd["pds"]),
            )
            vd_write_cache_policy_metrics.add_metric(
                [vd["vd_id"], vd["name"]],
                int(vd["hints"]["write_cache_policy_incorrect"]),
            )
            for pd in vd.get("pds", []):
                pd_smart_test_metrics.add_metric(
                    [pd["pd_id"], pd["serial"], vd["vd_id"], vd["name"]],
                    int(pd["smart_error"]),
                )
                if pd["lifespan"] >= 0:
                    pd_lifespan_metrics.add_metric(
                        [pd["pd_id"], pd["serial"], vd["vd_id"], vd["name"]],
                        int(pd["lifespan"]),
                    )

        return [
            vd_status_metrics,
            vd_num_of_pd_metrics,
            pd_smart_test_metrics,
            pd_lifespan_metrics,
            vd_write_cache_policy_metrics,
        ]
