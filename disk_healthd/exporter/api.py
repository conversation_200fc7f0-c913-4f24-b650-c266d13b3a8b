# Copyright (c) 2013-2024, SMARTX
# All rights reserved.

from flask import Blueprint, make_response
import prometheus_client
from prometheus_client import CollectorRegistry

from disk_healthd.exporter import cached_collector

exporter_bp = Blueprint(
    name="api_v2_disk_exporter",
    import_name=__name__,
    url_prefix="/api/v2/exporter/disk_healthd",
)


class DiskCollectorRegistry(CollectorRegistry):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def register(self, collector):
        if collector:
            super().register(collector)


@exporter_bp.route("/disk_stats")
def disk_metrics():
    disk_registry = DiskCollectorRegistry()
    disk_registry.register(cached_collector.disk)
    res = make_response(prometheus_client.generate_latest(disk_registry))
    res.headers["Content-Type"] = "text/plain"
    return res


@exporter_bp.route("/disk_status")
def disk_status_metrics():
    disk_status_registry = DiskCollectorRegistry()
    disk_status_registry.register(cached_collector.disk_status)
    res = make_response(prometheus_client.generate_latest(disk_status_registry))
    res.headers["Content-Type"] = "text/plain"
    return res


@exporter_bp.route("/hardware_raid")
def hardware_raid_metrics():
    hardware_raid_registry = DiskCollectorRegistry()
    hardware_raid_registry.register(cached_collector.hardware_raid)
    res = make_response(prometheus_client.generate_latest(hardware_raid_registry))
    res.headers["Content-Type"] = "text/plain"
    return res


@exporter_bp.route("/disk_event")
def event_metrics():
    event_registry = DiskCollectorRegistry()
    res = make_response(prometheus_client.generate_latest(event_registry))
    res.headers["Content-Type"] = "text/plain"
    return res
