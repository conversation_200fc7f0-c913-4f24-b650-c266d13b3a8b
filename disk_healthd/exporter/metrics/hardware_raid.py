# Copyright (c) 2013-2024, SMARTX
# All rights reserved.

from tuna.node.node_manager import NodeSoftwareManager
from tuna.service.hardware_raid_service import HardwareRaidService


class HardwareRaidMetric:
    @staticmethod
    def get_vd_metrics():
        host_uuid = NodeSoftwareManager().get_node_uuid()
        vds = HardwareRaidService().list_vd_from_db_by_host_uuid(host_uuid)
        return vds
