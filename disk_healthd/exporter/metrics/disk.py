# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
import logging

from disk_healthd.core.config import config
from tuna.config.constant import (
    DISK_FUNC_BOOT,
    DISK_FUNC_CACHE,
    DISK_FUNC_DATA,
    DISK_MOUNT_STATUS_UNMOUNTED,
    DISK_OR_PARTITION_USAGE_CACHE,
    DISK_OR_PARTITION_USAGE_JOURNAL,
    DISK_OR_PARTITION_USAGE_PARTITION,
    PARTITION_USAGE_BOOT,
)
from tuna.hardware.disk import Disk
from tuna.hardware.raid import Raid
from tuna.node import is_elf_only
from tuna.service.host_service import HostService


class DiskMetric:
    @staticmethod
    def get_io_stats_metric():
        disk_io_stats = Disk.io_stats()
        results = []
        disk_name_2_info = DiskMetric._get_current_host_disks_info()

        for disk_name in Disk.disk_names():
            disk_io_stat = disk_io_stats.get(disk_name)
            if disk_io_stat is not None:
                disk_info = disk_name_2_info.get(disk_name)
                if disk_info is not None:
                    disk_io_stat.update(disk_info)
                    results.append(disk_io_stat)
        return results

    @staticmethod
    def _get_current_host_disks_info():
        disk_name_2_info = {}
        host_info = HostService().get_current_host_info()
        if host_info is None:
            return disk_name_2_info

        for disk in host_info.get("disks"):
            disk_info = {}
            if disk.get("type") is not None:
                disk_info["type"] = disk.get("type")
            if disk.get("model") is not None:
                disk_info["model"] = disk.get("model")
            if disk.get("size") is not None:
                disk_info["size"] = str(disk.get("size"))
            if disk.get("serial") is not None:
                disk_info["serial"] = disk.get("serial")
            if disk.get("firmware") is not None:
                disk_info["firmware"] = disk.get("firmware")

            disk_name_2_info[disk.get("name")] = disk_info

        return disk_name_2_info


class DiskStatusMetric:
    THRESHOLD_SSD_LATENCY_MS_ISOLATE = config.diskstats_loop.THRESHOLD_SSD_LATENCY_MS_ISOLATE
    THRESHOLD_SSD_IOPS_ISOLATE = config.diskstats_loop.THRESHOLD_SSD_IOPS_ISOLATE
    THRESHOLD_SSD_SECTOR_PS_ISOLATE = config.diskstats_loop.THRESHOLD_SSD_SECTOR_PS_ISOLATE

    def __init__(self, info_disks):
        self.info_disks = info_disks

        # io-block-offline
        self.io_block_offline = []
        # used-in-chunk-isolate
        self.unhealthy_in_isolate = []
        self.sub_healthy_in_isolate = []
        # left-healthy-partition-after-isolate
        self.unhealthy_disk_with_left_partition = []
        self.unhealthy_disk_with_last_smtx_system = []
        # will-not-isolate
        self.sub_healthy_without_isolate = []
        self.smart_test_failed_disk = []
        # unhealthy-and-not-used
        self.unhealthy_disk_not_in_use = []

        self.classify_current_node_disk_alert()

    @staticmethod
    def _check_lsm_isolate_support():
        if is_elf_only():
            return False

        try:
            from zbs.chunk.client import ZbsChunk
            from zbs.proto import chunk_pb2 as chunk

            summary_info_res = ZbsChunk().summary_info(keep_v2_message_format=True)
            # lsm_capability is same in all chunk instances
            for instance_response in summary_info_res.instances_response:
                # As LSM_CAP_CACHE_ISOLATE capability is supported after LSM_CAP_PARTITION_ISOLATE capability,
                # we can check LSM_CAP_CACHE_ISOLATE directly without checking LSM_CAP_PARTITION_ISOLATE.
                return bool(instance_response.lsm_capability & chunk.LSM_CAP_CACHE_ISOLATE)
        except Exception as e:
            logging.exception("check lsm isolate support failed: {}".format(e))
            return None

    def classify_current_node_disk_alert(self):
        disk_names = Disk.disk_names()
        for disk in self.info_disks:
            if disk.get("name") not in disk_names:
                continue
            self.classify_one_disk(disk)

    def classify_one_disk(self, disk):
        # marker from chunk by node_info_collect (almost in real-time)
        exist_errflags_currently = disk.get("exist_errflags", False)
        not_considered_unhealthy_by_chunk_currently = disk.get("is_healthy_in_chunk", True)

        # marker from mongo disk-status by disk-healthd (historical persistent)
        disk_hints = disk.get("hints", {})
        chunk_errflag_detected = disk_hints.get("chunk_errflag", False)
        iostat_latency_detected = disk_hints.get("iostat_latency", False) or disk_hints.get("chunk_warnflag", False)
        chunk_error_detected = disk_hints.get("chunk_io_error", False) or disk_hints.get("chunk_checksum_error", False)
        smart_error_detected = disk_hints.get("smart_check", False)
        raid_faulty_detected = disk_hints.get("faulty_in_raid", False)
        io_block_mark_offline = (
            disk_hints.get("io_timeout_offline", False)
            or disk_hints.get("cmd_abort_offline", False)
            or disk_hints.get("io_eh_queue_offline", False)
        )
        reallocated_sectors_count_overflow = disk_hints.get("reallocated_sectors_count_overflow", False)

        if not any(
            [
                chunk_errflag_detected,
                iostat_latency_detected,
                smart_error_detected,
                raid_faulty_detected,
                chunk_error_detected,
                io_block_mark_offline,
                reallocated_sectors_count_overflow,
            ]
        ):
            return

        if io_block_mark_offline:
            self.io_block_offline.append(disk)

        mount_status = disk.get("mount_status")
        if mount_status == DISK_MOUNT_STATUS_UNMOUNTED:
            self.unhealthy_disk_not_in_use.append(disk)
            return

        if self._is_used_in_chunk(disk):
            # errflag detected in the past by disk-healthd & exist errflag currently
            if chunk_errflag_detected and exist_errflags_currently:
                self.unhealthy_in_isolate.append(disk)
                return

            extra_info = disk.get("extra_info", {})
            if (
                iostat_latency_detected
                and disk.get("is_rotational")  # HDD
                and self._exist_chunk_partition(disk)  # used as chunk partition
                and self._check_lsm_isolate_support()  # lsm will isolate
            ):
                self.sub_healthy_in_isolate.append(disk)
                return
            elif (
                iostat_latency_detected
                and not disk.get("is_rotational")  # SSD
                and self._check_lsm_isolate_support()  # lsm will isolate
                # SSD latency detected and meet the isolate conditions
                and extra_info.get("rw_await", 0) > self.THRESHOLD_SSD_LATENCY_MS_ISOLATE
                and extra_info.get("iops", 0) < self.THRESHOLD_SSD_IOPS_ISOLATE
                and extra_info.get("sector_ps", 0) < self.THRESHOLD_SSD_SECTOR_PS_ISOLATE
            ):
                self.sub_healthy_in_isolate.append(disk)
                return

        # if disk offline alert fired, skip following alert
        if io_block_mark_offline:
            return

        # unhealthy partition gone & only healthy partition left
        # the disk isolated by chunk in the past (errflags or warnflags detected)
        chunk_flags_detected = disk_hints.get("chunk_errflag", False) or disk_hints.get("chunk_warnflag", False)
        # corner case fine:
        # disk without soft raid only has boot and root partition,
        # and never gets chunk flags, should not trigger this rule
        if chunk_flags_detected and not_considered_unhealthy_by_chunk_currently:
            if self._contains_last_raid_dev(disk) or self._is_boot_disk(disk):
                self.unhealthy_disk_with_last_smtx_system.append(disk)
            else:
                self.unhealthy_disk_with_left_partition.append(disk)
            return

        if chunk_error_detected or iostat_latency_detected or reallocated_sectors_count_overflow:
            if self._contains_last_raid_dev(disk) or self._is_boot_disk(disk):
                self.unhealthy_disk_with_last_smtx_system.append(disk)
            else:
                self.sub_healthy_without_isolate.append(disk)
            return

        if smart_error_detected:
            if self._contains_last_raid_dev(disk) or self._is_boot_disk(disk):
                self.unhealthy_disk_with_last_smtx_system.append(disk)
            else:
                self.smart_test_failed_disk.append(disk)
            return

    def _is_used_in_chunk(self, disk):
        if disk.get("function") in [DISK_FUNC_CACHE, DISK_FUNC_DATA]:
            return True
        for partition in disk.get("partitions", []):
            if partition.get("usage") in [
                DISK_OR_PARTITION_USAGE_JOURNAL,
                DISK_OR_PARTITION_USAGE_CACHE,
                DISK_OR_PARTITION_USAGE_PARTITION,
            ]:
                return True
        return False

    def _exist_chunk_partition(self, disk):
        partitions = disk.get("partitions", [])
        partition_num = len(partitions)
        for partition in partitions:
            if partition.get("usage") == DISK_OR_PARTITION_USAGE_PARTITION:
                return True

        if disk.get("function") == DISK_FUNC_DATA:
            if partition_num == 0 or (partition_num == 1 and partitions[0]["usage"] == "unparted"):
                return True

        return False

    def _contains_last_raid_dev(self, disk):
        disk_partitions = []
        for part in disk.get("partitions", []):
            if part.get("name"):
                disk_partitions.append(part["name"])

        for md_name in Raid.raids():
            active_devs = Raid(md_name).list_active_devices()
            if len(active_devs) == 1 and active_devs[0] in disk_partitions:
                return True
        return False

    def _is_boot_disk(self, disk):
        if disk.get("function") == DISK_FUNC_BOOT:
            return True
        partitions = disk.get("partitions", [])
        for partition in partitions:
            if partition.get("usage") == PARTITION_USAGE_BOOT:
                return True
        return False
