# Copyright (c) 2013-2024, SMARTX
# All rights reserved.

from common.exporter.cache_utils import CachedCollectorWrapper
from common.exporter.metric_family import TARGET_HOST, TARGET_SCVM
from disk_healthd.exporter.collector.disk import Disk<PERSON><PERSON>ector, DiskStatusCollector
from disk_healthd.exporter.collector.hardware_raid import HardwareRaidCollector
from tuna.node import is_scvm_deploy, is_witness


def disk_collector():
    if is_witness():
        return None
    if is_scvm_deploy():
        target = TARGET_SCVM
    else:
        target = TARGET_HOST
    return CachedCollectorWrapper(DiskCollector, interval=25, target=target)


def disk_status_collector():
    if is_witness():
        return None
    if is_scvm_deploy():
        target = TARGET_SCVM
    else:
        target = TARGET_HOST
    return CachedCollectorWrapper(DiskStatusCollector, interval=25, target=target)


def hardware_raid_collector():
    if is_witness():
        return None
    if is_scvm_deploy():
        target = TARGET_SCVM
    else:
        target = TARGET_HOST
    return Cached<PERSON>ollectorWrapper(HardwareRaidCollector, interval=25, target=target)


# None if on witness
disk = disk_collector()
disk_status = disk_status_collector()
hardware_raid = hardware_raid_collector()
