# Copyright (c) 2013-2020, SMARTX
# All rights reserved.

from datetime import datetime
import logging

from disk_healthd.core import monotonic_time
from disk_healthd.core.base_loop import Base<PERSON>oop
from disk_healthd.core.config import config
from disk_healthd.core.disk_map import disk_map
from tuna.node.storage_manager import StorageDiskManager
from tuna.service.disk_status_service import DiskStatusService


class ChunkStatLoop(BaseLoop):
    THRESHOLD_NUM_IO_ERRORS = config.chunk_status_loop.THRESHOLD_NUM_IO_ERRORS
    THRESHOLD_NUM_CHECKSUM_ERRORS = config.chunk_status_loop.THRESHOLD_NUM_CHECKSUM_ERRORS

    def __init__(self, interval=10):
        super().__init__(interval)

    def loop(self):
        while not self._stop:
            start = monotonic_time.time()

            try:
                self.check_chunk_status()
            except Exception as e:
                logging.exception("{}: an error occurred during chunk_status_loop: {}".format(self.name, e))

            time_used = monotonic_time.time() - start
            logging.info("{}: one loop finished, cost {:.3f}s.".format(self.name, time_used))

            time_to_wait = max(self.interval - time_used, 0)
            self.tick_sleep(time_to_wait)

    def check_chunk_status(self):
        update_disk_list = []
        update_disk_detected_list = []
        part_infos = StorageDiskManager.get_disk_or_partition_info()
        for _, part in list(part_infos.items()):
            hints_and_detail = {}
            hints_detected_detail = []
            if part["num_io_errors"] > self.THRESHOLD_NUM_IO_ERRORS:
                hints_and_detail["extra_info.chunk_num_io_errors"] = part["num_io_errors"]
                hints_and_detail["extra_info.num_io_errors_threshold"] = ">{}".format(self.THRESHOLD_NUM_IO_ERRORS)
                hints_and_detail["hints.chunk_io_error"] = True
                hints_and_detail["healthy"] = False
                hints_detected_detail.append("detected_at.chunk_io_error")
            if "num_checksum_errors" in part and part["num_checksum_errors"] > self.THRESHOLD_NUM_CHECKSUM_ERRORS:
                hints_and_detail["extra_info.chunk_num_checksum_errors"] = part["num_checksum_errors"]
                hints_and_detail["extra_info.num_checksum_errors_threshold"] = ">{}".format(
                    self.THRESHOLD_NUM_CHECKSUM_ERRORS
                )
                hints_and_detail["hints.chunk_checksum_error"] = True
                hints_and_detail["healthy"] = False
                hints_detected_detail.append("detected_at.chunk_checksum_error")
            if part["errflags"] > 0:
                hints_and_detail["hints.chunk_errflag"] = True
                hints_and_detail["healthy"] = False
                hints_detected_detail.append("detected_at.chunk_errflag")
            if part["warnflags"] > 0:
                hints_and_detail["hints.chunk_warnflag"] = True
                hints_and_detail["healthy"] = False
                hints_detected_detail.append("detected_at.chunk_warnflag")

            if hints_and_detail:
                msg = "{}: unhealthy flag detected on chunk {} {}: {} ".format(
                    self.name, part["chunk_ins_id"], part["usage"], part["path"]
                )
                logging.info(msg)
                msg = (
                    "{}: part info, "
                    "uuid: {} "
                    "device_id: {} "
                    "num_io_errors: {} "
                    "num_slow_io: {} "
                    "num_checksum_errors: {} "
                    "errflags: {} "
                    "warnflags: {}"
                ).format(
                    self.name,
                    part["uuid"],
                    part["device_id"],
                    part["num_io_errors"],
                    part.get("num_slow_io"),
                    part.get("num_checksum_errors"),
                    part["errflags"],
                    part["warnflags"],
                )
                logging.info(msg)
                disk_info = disk_map.match_one_disk_for_chunk_part(part)
                if not disk_info:
                    logging.warning("{}: disk not found: {} {}".format(self.name, part["path"], part["device_id"]))
                    continue
                update_disk_list.append((disk_info, hints_and_detail))
                update_disk_detected_list.append((disk_info, hints_detected_detail))

        if update_disk_list:
            DiskStatusService().update_disks_with_fields(update_disk_list)

            now = datetime.now()

            final_update_disk_detected_list = []
            local_disk_status_list = DiskStatusService().list_local_disk_status()
            local_disk_status_map = {status["trace_id"]: status for status in local_disk_status_list}

            for disk_info, hints_detected_detail in update_disk_detected_list:
                trace_id = DiskStatusService._gen_disk_trace_id(disk_info)
                final_hints_detected_detail = {}
                if trace_id in local_disk_status_map:
                    local_disk_status = local_disk_status_map[trace_id]
                    for hints_detected_key in hints_detected_detail:
                        detected_key = hints_detected_key.split(".")[1]
                        if detected_key not in local_disk_status.get("detected_at", {}):
                            final_hints_detected_detail[hints_detected_key] = now

                if final_hints_detected_detail:
                    final_update_disk_detected_list.append((disk_info, final_hints_detected_detail))

            if final_update_disk_detected_list:
                DiskStatusService().update_disks_with_fields(final_update_disk_detected_list)
