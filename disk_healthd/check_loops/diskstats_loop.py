# Copyright (c) 2013-2021, SMARTX
# All rights reserved.

from datetime import datetime
import logging
from logging.handlers import RotatingFileHandler

from disk_healthd.core import monotonic_time
from disk_healthd.core.base_loop import BaseLoop
from disk_healthd.core.config import config
from disk_healthd.core.disk_map import disk_map
from disk_healthd.core.slow_disk import SlowDiskHelper
from tuna.service.disk_status_service import DiskStatusService


class DiskStatsDeltaHelper:
    LOG_FILE = "/var/log/zbs/disk-healthd/diskstats.log"
    DISKSTATS_FILE = "/proc/diskstats"
    DISK_STAT_KEYS = [
        "major",
        "minor",
        "name",
        "r_io",
        "r_merge",
        "r_sector",
        "r_ms",
        "w_io",
        "w_merge",
        "w_sector",
        "w_ms",
        "rw_waiting",
        "rw_ms",
        "rw_wms",
    ]

    def __init__(self, interval=15, win_size=6):
        self.points = []
        self.raw_points = []
        self.raw_diskstats_points = []
        self.iostat_points = []

        self.interval = interval
        self.win_size = win_size
        self.raw_buf_size = 2 * win_size
        self.logger = self._setup_logger()

    def _setup_logger(self):
        formatter = logging.Formatter("[%(asctime)s] %(message)s")
        handler = RotatingFileHandler(self.LOG_FILE, maxBytes=30000000, backupCount=1)  # max 30mb
        handler.setFormatter(formatter)

        logger = logging.getLogger("diskstats")
        logger.setLevel(logging.getLevelName(logging.INFO))
        list(map(logger.removeHandler, logger.handlers))
        logger.addHandler(handler)
        logger.propagate = 0  # not print to root logger
        return logger

    def _parse_disk_stats(self):
        # https://www.kernel.org/doc/Documentation/ABI/testing/procfs-diskstats
        res = {}
        with open(self.DISKSTATS_FILE) as f:
            ls = f.readlines()
        timestamp = datetime.strftime(datetime.now(), "%Y-%m-%d %H:%M:%S")

        for line in ls:
            tmp = dict(list(zip(self.DISK_STAT_KEYS, line.strip().split())))
            name = tmp.pop("name")
            if name in disk_map.disk_names:
                res[name] = tmp
        raw_stats = "".join(["{}\n".format(timestamp), *ls])
        return timestamp, res, raw_stats

    def _cal_avg_iostat(self):
        # https://github.com/open-falcon-archive/agent/blob/master/funcs/diskstats.go
        delta = {}
        pre_point, current_point = self.points[0], self.points[-1]

        for disk_name, cur_values in list(current_point.items()):
            # new disk found / old disk no longer exists
            if disk_name not in pre_point:
                continue

            delta[disk_name] = {}
            pre_values = pre_point.get(disk_name)

            r_io = int(cur_values["r_io"]) - int(pre_values["r_io"])
            w_io = int(cur_values["w_io"]) - int(pre_values["w_io"])
            r_ms = int(cur_values["r_ms"]) - int(pre_values["r_ms"])
            w_ms = int(cur_values["w_ms"]) - int(pre_values["w_ms"])
            r_sector = int(cur_values["r_sector"]) - int(pre_values["r_sector"])
            w_sector = int(cur_values["w_sector"]) - int(pre_values["w_sector"])

            delta[disk_name]["rw_iops"] = float(r_io + w_io) / self.interval
            delta[disk_name]["rw_sector_ps"] = float(r_sector + w_sector) / self.interval
            if r_io >= 0 and w_io >= 0 and r_io + w_io > 0:
                delta[disk_name]["rw_await"] = float(r_ms + w_ms) / float(r_io + w_io)
            else:
                delta[disk_name]["rw_await"] = 0

        return delta

    def collect_one_point(self):
        timestamp, stats, raw_stats = self._parse_disk_stats()

        self.raw_points.append(raw_stats)
        if len(self.raw_points) > self.raw_buf_size:
            self.raw_points.pop(0)

        # When disk is detected as a high latency disk, we need to keep the raw diskstats points.
        # We use a separate list to store these points.
        # The points number is limited to raw_buf_size.
        self.raw_diskstats_points.append(raw_stats)
        if len(self.raw_diskstats_points) > self.raw_buf_size:
            self.raw_diskstats_points.pop(0)

        self.points.append(stats)
        if len(self.points) > 2:
            self.points.pop(0)
        if len(self.points) == 2:
            iostat_value = self._cal_avg_iostat()
            self.iostat_points.append((timestamp, iostat_value))

        if len(self.iostat_points) > self.win_size:
            self.iostat_points.pop(0)

    def ready(self):
        return len(self.iostat_points) >= self.win_size

    def get_raw_data_of_disk(self, disk_name):
        # Raw data content is used to be saved to disk status collection.
        raw_data_content = ""
        raw_point_format = "{time} {r_io} {w_io} {rw_io} {rw_iops} {r_setcor} {w_sector} {rw_sector} {rw_sector_ps} {bw_mib_ps} {r_ms} {w_ms} {rw_await}\n"  # noqa: E501

        # Collect raw diskstats points for the specified disk name.
        raw_points = []
        for raw_diskstats_point in self.raw_diskstats_points:
            timestamp, raw_stats = raw_diskstats_point.split("\n", 1)
            timestamp = timestamp.replace(" ", "_")
            for raw_stat in raw_stats.split("\n"):
                raw_stat = raw_stat.strip()
                if raw_stat:
                    point_dict = dict(list(zip(self.DISK_STAT_KEYS, raw_stat.split())))
                    if point_dict.get("name") == disk_name:
                        point_dict["time"] = timestamp
                        raw_points.append(point_dict)

        # Aggregate raw points and calculate additional fields (rw_iops/sector_ps/bw_mib_ps/rw_await).
        for idx, raw_point in enumerate(raw_points):
            # The first point is no need to calculate rw_iops/rw_sector_ps/bw_mib_ps/rw_await
            if idx == 0:
                raw_point["rw_io"] = int(raw_point["r_io"]) + int(raw_point["w_io"])
                raw_point["rw_sector"] = int(raw_point["r_sector"]) + int(raw_point["w_sector"])
                raw_point["rw_iops"] = "-"
                raw_point["rw_sector_ps"] = "-"
                raw_point["bw_mib_ps"] = "-"
                raw_point["rw_await"] = "-"
                raw_data_content += raw_point_format.format(
                    time=raw_point["time"],
                    r_io=raw_point["r_io"],
                    w_io=raw_point["w_io"],
                    rw_io=raw_point["rw_io"],
                    rw_iops=raw_point["rw_iops"],
                    r_setcor=raw_point["r_sector"],
                    w_sector=raw_point["w_sector"],
                    rw_sector=raw_point["rw_sector"],
                    rw_sector_ps=raw_point["rw_sector_ps"],
                    bw_mib_ps=raw_point["bw_mib_ps"],
                    r_ms=raw_point["r_ms"],
                    w_ms=raw_point["w_ms"],
                    rw_await=raw_point["rw_await"],
                )
            else:
                pre_raw_point = raw_points[idx - 1]

                raw_point["rw_io"] = int(raw_point["r_io"]) + int(raw_point["w_io"])
                raw_point["rw_sector"] = int(raw_point["r_sector"]) + int(raw_point["w_sector"])

                raw_point["rw_iops"] = round(float(raw_point["rw_io"] - pre_raw_point["rw_io"]) / self.interval, 3)
                raw_point["rw_sector_ps"] = round(
                    float(raw_point["rw_sector"] - pre_raw_point["rw_sector"]) / self.interval, 3
                )
                # convert setcors/s to MiB/s for human readability
                raw_point["bw_mib_ps"] = round(raw_point["rw_sector_ps"] / 2048.0, 3)

                r_io = int(raw_point["r_io"]) - int(pre_raw_point["r_io"])
                w_io = int(raw_point["w_io"]) - int(pre_raw_point["w_io"])
                r_ms = int(raw_point["r_ms"]) - int(pre_raw_point["r_ms"])
                w_ms = int(raw_point["w_ms"]) - int(pre_raw_point["w_ms"])
                if r_io >= 0 and w_io >= 0 and r_io + w_io > 0:
                    raw_point["rw_await"] = round(float(r_ms + w_ms) / float(r_io + w_io), 3)
                else:
                    raw_point["rw_await"] = 0

                raw_data_content += raw_point_format.format(
                    time=raw_point["time"],
                    r_io=raw_point["r_io"],
                    w_io=raw_point["w_io"],
                    rw_io=raw_point["rw_io"],
                    rw_iops=raw_point["rw_iops"],
                    r_setcor=raw_point["r_sector"],
                    w_sector=raw_point["w_sector"],
                    rw_sector=raw_point["rw_sector"],
                    rw_sector_ps=raw_point["rw_sector_ps"],
                    bw_mib_ps=raw_point["bw_mib_ps"],
                    r_ms=raw_point["r_ms"],
                    w_ms=raw_point["w_ms"],
                    rw_await=raw_point["rw_await"],
                )

        return raw_data_content.strip()

    def flush_raw_log(self):
        while self.raw_points:
            raw_point = self.raw_points.pop(0)
            self.logger.info(raw_point)


class DiskStatsLoop(BaseLoop):
    ENABLE_LATENCY_HINT = config.diskstats_loop.ENABLE_LATENCY_HINT
    SAMPLING_WIN_SIZE = config.diskstats_loop.SAMPLING_WIN_SIZE

    THRESHOLD_SSD_LATENCY_MS = config.diskstats_loop.THRESHOLD_SSD_LATENCY_MS
    THRESHOLD_SSD_LATENCY_MS_ISOLATE = config.diskstats_loop.THRESHOLD_SSD_LATENCY_MS_ISOLATE
    THRESHOLD_SSD_IOPS = config.diskstats_loop.THRESHOLD_SSD_IOPS
    THRESHOLD_SSD_IOPS_ISOLATE = config.diskstats_loop.THRESHOLD_SSD_IOPS_ISOLATE
    THRESHOLD_SSD_SECTOR_PS = config.diskstats_loop.THRESHOLD_SSD_SECTOR_PS
    THRESHOLD_SSD_SECTOR_PS_ISOLATE = config.diskstats_loop.THRESHOLD_SSD_SECTOR_PS_ISOLATE

    THRESHOLD_HDD_LATENCY_MS = config.diskstats_loop.THRESHOLD_HDD_LATENCY_MS
    THRESHOLD_HDD_IOPS = config.diskstats_loop.THRESHOLD_HDD_IOPS
    THRESHOLD_HDD_SECTOR_PS = config.diskstats_loop.THRESHOLD_HDD_SECTOR_PS

    THRESHOLD_LOG_LATENCY_MS = config.diskstats_loop.THRESHOLD_LOG_LATENCY_MS
    ENABLE_LATENCY_DEBUG_LOG = config.diskstats_loop.ENABLE_LATENCY_DEBUG_LOG

    def __init__(self, interval=15):
        super().__init__(interval)
        self.slow_disk_handler = SlowDiskHelper()
        self.delta_counter = None

    def loop(self):
        self.delta_counter = DiskStatsDeltaHelper(self.interval, self.SAMPLING_WIN_SIZE)

        while not self._stop:
            start = monotonic_time.time()

            try:
                self.delta_counter.collect_one_point()
                if self.delta_counter.ready():
                    self._check_io_latency()
            except Exception as e:
                logging.exception("{}: an error occurred during diskstats_loop: {}".format(self.name, e))

            time_used = monotonic_time.time() - start
            logging.info("{}: one loop finished, cost {:.3f}s.".format(self.name, time_used))

            time_to_wait = max(self.interval - time_used, 0)
            if time_to_wait < 3:
                logging.warning("{}: slow loop detected.".format(self.name))
            self.tick_sleep(time_to_wait)

    def _check_io_latency(self):
        hint_disk_list = []
        hint_disk_detected_list = []
        for disk_info in list(disk_map.disk_names.values()):
            hints, hints_detected = self._get_high_latency(disk_info)
            if hints:
                # Collect raw data for the disk if it is detected as high latency.
                raw_data = self.delta_counter.get_raw_data_of_disk(disk_info["name"])
                if raw_data:
                    hints["extra_info.raw_data"] = raw_data

                hint_disk_list.append((disk_info, hints))
                hint_disk_detected_list.append((disk_info, hints_detected))

        if hint_disk_list:
            self.delta_counter.flush_raw_log()
            DiskStatusService().update_disks_with_fields(hint_disk_list)

            now = datetime.now()

            final_update_disk_detected_list = []
            local_disk_status_list = DiskStatusService().list_local_disk_status()
            local_disk_status_map = {status["trace_id"]: status for status in local_disk_status_list}

            for disk_info, hints_detected_detail in hint_disk_detected_list:
                trace_id = DiskStatusService._gen_disk_trace_id(disk_info)
                final_hints_detected_detail = {}
                if trace_id in local_disk_status_map:
                    local_disk_status = local_disk_status_map[trace_id]
                    for hints_detected_key in hints_detected_detail:
                        detected_key = hints_detected_key.split(".")[1]
                        if detected_key not in local_disk_status.get("detected_at", {}):
                            final_hints_detected_detail[hints_detected_key] = now

                if final_hints_detected_detail:
                    final_update_disk_detected_list.append((disk_info, final_hints_detected_detail))

            if final_update_disk_detected_list:
                DiskStatusService().update_disks_with_fields(final_update_disk_detected_list)

            if self.ENABLE_LATENCY_HINT:
                slow_disks = self._get_slow_disks(hint_disk_list)
                self.slow_disk_handler.add_slow_disks(slow_disks)

    def _get_high_latency(self, disk_info):
        disk_name = disk_info["name"]
        if disk_info.get("is_rotational"):  # HDD
            disk_type = "HDD"
            latency_threshold = self.THRESHOLD_HDD_LATENCY_MS
            exempt_iops = self.THRESHOLD_HDD_IOPS
            exempt_sector_ps = self.THRESHOLD_HDD_SECTOR_PS
        else:  # SSD
            disk_type = "SSD"
            latency_threshold = self.THRESHOLD_SSD_LATENCY_MS
            exempt_iops = self.THRESHOLD_SSD_IOPS
            exempt_sector_ps = self.THRESHOLD_SSD_SECTOR_PS

        ts_points = []
        latency_points = []
        iops_points = []
        bw_points = []

        for iostat_point in self.delta_counter.iostat_points:
            timestamp, iostat_value = iostat_point
            ts_points.append(timestamp)
            latency_points.append(iostat_value.get(disk_name, {}).get("rw_await", 0))
            iops_points.append(iostat_value.get(disk_name, {}).get("rw_iops", 0))
            bw_points.append(iostat_value.get(disk_name, {}).get("rw_sector_ps", 0))

        min_latency = min(latency_points)
        max_latency = max(latency_points)
        max_iops = max(iops_points)
        max_bw = max(bw_points)

        if min_latency > latency_threshold and max_iops < exempt_iops and max_bw < exempt_sector_ps:
            # convert setcors/s to MiB/s
            max_bw_mib = round(max_bw / 2048.0, 3)  # 1 MiB = 2048 sectors (512 bytes per sector)
            exempt_sector_ps_mib = round(exempt_sector_ps / 2048.0, 3)

            report_lines = ["{:<20} {:>15} {:>15} {:>15}".format("timestamp", "latency(ms)", "iops", "sector_ps")]
            for timestamp, latency, iops, sector_ps in zip(ts_points, latency_points, iops_points, bw_points):
                report_lines.append("{:<20} {:>15.2f} {:>15.2f} {:>15.2f}".format(timestamp, latency, iops, sector_ps))

            logging.info(
                "{}: disk {} {} io latency detected: ".format(self.name, disk_type, disk_name)
                + "min_latency {:.2f}ms > {}ms, max_iops {:.2f} < {}, max_sector_ps {:.2f} < {}, max_bw_mib {:.2f} < {}\n".format(  # noqa: E501
                    min_latency,
                    latency_threshold,
                    max_iops,
                    exempt_iops,
                    max_bw,
                    exempt_sector_ps,
                    max_bw_mib,
                    exempt_sector_ps_mib,
                )
                + "\n".join(report_lines)
            )

            return {
                "hints.iostat_latency": True,
                "healthy": False,
                "extra_info.rw_await": max_latency,
                "extra_info.latency_threshold": ">{}ms".format(latency_threshold),
                "extra_info.iops": max_iops,
                "extra_info.iops_threshold": "<{}".format(exempt_iops),
                "extra_info.sector_ps": max_bw,
                "extra_info.sector_ps_threshold": "<{}setcor/s".format(exempt_sector_ps),
                "extra_info.bw_mib_ps": max_bw_mib,
                "extra_info.bw_mib_ps_threshold": "<{}MiB/s".format(exempt_sector_ps_mib),
            }, ["detected_at.iostat_latency"]

        # logging abnormal disk latency
        if self.ENABLE_LATENCY_DEBUG_LOG or max_latency > self.THRESHOLD_LOG_LATENCY_MS:
            report_lines = ["{:<20} {:>15} {:>15} {:>15}".format("timestamp", "latency(ms)", "iops", "sector_ps")]
            for timestamp, latency, iops, sector_ps in zip(ts_points, latency_points, iops_points, bw_points):
                report_lines.append("{:<20} {:>15.2f} {:>15.2f} {:>15.2f}".format(timestamp, latency, iops, sector_ps))

            logging.info(
                "{}: disk {} {} debug: ".format(self.name, disk_type, disk_name)
                + "min_latency {:.2f}ms vs {}ms, max_iops {:.2f} vs {}, max_sector_ps {:.2f} vs {}\n".format(
                    min_latency,
                    latency_threshold,
                    max_iops,
                    exempt_iops,
                    max_bw,
                    exempt_sector_ps,
                )
                + "\n".join(report_lines)
            )

        return {}, []

    def _get_slow_disks(self, hint_disk_list):
        slow_disks = []
        for item in hint_disk_list:
            disk_info, hints = item
            if disk_info.get("is_rotational"):
                slow_disks.append(disk_info)
            else:
                if (
                    hints.get("hints.iostat_latency")
                    and hints.get("extra_info.rw_await") > self.THRESHOLD_SSD_LATENCY_MS_ISOLATE
                    and hints.get("extra_info.iops") < self.THRESHOLD_SSD_IOPS_ISOLATE
                    and hints.get("extra_info.sector_ps") < self.THRESHOLD_SSD_SECTOR_PS_ISOLATE
                ):
                    slow_disks.append(disk_info)

        return slow_disks
