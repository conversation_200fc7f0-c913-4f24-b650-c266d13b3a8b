# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
from copy import deepcopy
import logging
import socket
import struct

from disk_healthd.core import monotonic_time, slow_disk
from disk_healthd.core.base_loop import BaseLoop
from disk_healthd.core.disk_map import disk_map
from tuna.config.constant import (
    DISK_OR_PARTITION_USAGE_CACHE,
    DISK_OR_PARTITION_USAGE_JOURNAL,
    DISK_OR_PARTITION_USAGE_PARTITION,
)
from tuna.node import (
    is_disk_data_with_cache,
    is_faster_ssd_as_cache_enabled,
)
from tuna.node.storage_manager import StorageDiskManager
from tuna.service.disk_status_service import DiskStatusService
from tuna.service.host_service import HostService
from tuna.service.slow_disk_service import SlowDiskService
from zbs.chunk.client import ZbsChunk
from zbs.meta.client import ZbsMeta
from zbs.proto import chunk_pb2 as chunk
from zbs.proto import common_pb2 as zbs_common


class IsolateDiskLoop(BaseLoop):
    def __init__(self, interval=60):
        super().__init__(interval)
        self.sd_service = SlowDiskService()
        self.sd_views = None

        self.chunk_client = ZbsChunk()

        self.host_service = HostService()

        self.ssd_as_cache = is_faster_ssd_as_cache_enabled()
        self.disk_data_with_cache = is_disk_data_with_cache()

    def _list_chunk_partitions(self):
        try:
            return StorageDiskManager.get_disk_or_partition_info(
                usages=[DISK_OR_PARTITION_USAGE_PARTITION],
                chunk_client=self.chunk_client,
                raise_error=True,
                use_nt=True,
            )
        except Exception as e:
            logging.exception("{}: get partition list failed: {}".format(self.name, e))
            return None

    def _list_chunk_caches(self):
        try:
            return StorageDiskManager.get_disk_or_partition_info(
                usages=[DISK_OR_PARTITION_USAGE_CACHE],
                chunk_client=self.chunk_client,
                raise_error=True,
                use_nt=True,
            )
        except Exception as e:
            logging.exception("{}: get cache list failed: {}".format(self.name, e))
            return None

    def _list_chunk_journals(self):
        try:
            return StorageDiskManager.get_disk_or_partition_info(
                usages=[DISK_OR_PARTITION_USAGE_JOURNAL],
                chunk_client=self.chunk_client,
                raise_error=True,
                use_nt=True,
            )
        except Exception as e:
            logging.exception("{}: get journal list failed: {}".format(self.name, e))
            return None

    def _get_part_from_chunk_for_disk(self, disk, parts_in_chunk, usage="partition"):
        for _, part in parts_in_chunk.items():
            chunk_part = {
                "usage": usage,
                "path": part.path,
                "device_id": part.device_id,
                "uuid": part.uuid,
                "part_uuid": part.part_uuid,
            }
            if StorageDiskManager.is_chunk_part_belong_to_disk(chunk_part, disk):
                return part
        return None

    def _check_lsm_support_isolate(self):
        try:
            summary_info_res = self.chunk_client.summary_info(keep_v2_message_format=True)
            # lsm_capability is same in all chunk instances
            for instance_response in summary_info_res.instances_response:
                # As LSM_CAP_CACHE_ISOLATE capability is supported after LSM_CAP_PARTITION_ISOLATE capability,
                # we can check LSM_CAP_CACHE_ISOLATE directly without checking LSM_CAP_PARTITION_ISOLATE.
                return bool(instance_response.lsm_capability & chunk.LSM_CAP_CACHE_ISOLATE)
        except Exception as e:
            logging.exception("{}: check lsm isolate support failed: {}".format(self.name, e))
            return None

    def _check_meta_chunk_list_healthy(self):
        meta_client = ZbsMeta()
        chunk_list = meta_client.chunk_list()
        for item in chunk_list.chunks:
            if item.status != zbs_common.CHUNK_STATUS_CONNECTED_HEALTHY:
                chunk_ip = socket.inet_ntoa(struct.pack("I", item.rpc_ip))
                status_name = zbs_common.ChunkStatus.Name(item.status)
                logging.warning("{}: chunk {} status: {}".format(self.name, chunk_ip, status_name))
                return False
        return True

    def _max_isolate_size(self):
        summary = ZbsMeta().cluster_summary()
        allocated_data_space = summary.space_info.allocated_data_space
        valid_data_space = summary.space_info.valid_data_space

        used_size = allocated_data_space
        return valid_data_space * 0.9 - used_size

    def _get_cluster_isolate_partition_size(self, sd_views):
        isolate_size = 0
        for host in sd_views["hosts"]:
            for disk in host["slow_disks"]:
                if "chunk_part_size" not in disk:
                    continue
                if disk["status"] in [slow_disk.MARK_TO_ISOLATE, slow_disk.CHUNK_INVOLVED]:
                    isolate_size += disk.get("chunk_part_size", 0)
        return isolate_size

    def _max_single_node_healthy_partition_num(self):
        part_nums = []
        meta_client = ZbsMeta()
        chunk_list = meta_client.chunk_list()
        for item in chunk_list.chunks:
            chunk_ip = socket.inet_ntoa(struct.pack("I", item.rpc_ip))
            chunk_client = ZbsChunk(chunk_ip)

            healthy_part_count = 0
            partition_res = chunk_client.partition_list(keep_v2_message_format=True)
            for instance_response in partition_res.instances_response:
                for p in instance_response.partitions:
                    if p.errflags == 0 and p.warnflags == 0 and p.status == chunk.PARTITION_MOUNTED:
                        healthy_part_count += 1

            if chunk_ip == self.sd_service.CURRENT_DATA_IP:
                # pre-allocated possible one for MARK_TO_ISOLATE in this cycle
                healthy_part_count -= 1

            part_nums.append(healthy_part_count)

        return max(part_nums) if part_nums else 0

    def _max_single_node_healthy_cache_num(self):
        cache_nums = []
        meta_client = ZbsMeta()
        chunk_list = meta_client.chunk_list()
        for item in chunk_list.chunks:
            chunk_ip = socket.inet_ntoa(struct.pack("I", item.rpc_ip))
            chunk_client = ZbsChunk(chunk_ip)

            healthy_cache_count = 0
            cache_res = chunk_client.cache_list(keep_v2_message_format=True)
            for instance_response in cache_res.instances_response:
                for c in instance_response.caches:
                    if c.errflags == 0 and c.warnflags == 0 and c.status == chunk.CACHE_MOUNTED:
                        healthy_cache_count += 1

            if chunk_ip == self.sd_service.CURRENT_DATA_IP:
                # pre-allocated possible one for MARK_TO_ISOLATE in this cycle
                healthy_cache_count -= 1

            cache_nums.append(healthy_cache_count)

        return max(cache_nums) if cache_nums else 0

    def _get_cluster_isolating_or_umounting_num(self):
        meta_client = ZbsMeta()
        chunk_list = meta_client.chunk_list()
        isolating_or_umounting_count = 0
        for item in chunk_list.chunks:
            chunk_ip = socket.inet_ntoa(struct.pack("I", item.rpc_ip))
            chunk_client = ZbsChunk(chunk_ip)

            # partition
            partition_res = chunk_client.partition_list(keep_v2_message_format=True)
            for instance_response in partition_res.instances_response:
                for p in instance_response.partitions:
                    if p.status in [chunk.PARTITION_STAGING, chunk.PARTITION_MIGRATING]:
                        isolating_or_umounting_count += 1
                    if p.status == chunk.PARTITION_MOUNTED and p.warnflags == 1:
                        isolating_or_umounting_count += 1

            # cache
            cache_res = chunk_client.cache_list(keep_v2_message_format=True)
            for instance_response in cache_res.instances_response:
                for c in instance_response.caches:
                    if c.status in [chunk.CACHE_STAGING, chunk.CACHE_MIGRATING]:
                        isolating_or_umounting_count += 1
                    if c.status == chunk.CACHE_MOUNTED and c.warnflags == 1:
                        isolating_or_umounting_count += 1

            # journal
            journal_res = chunk_client.journal_list(keep_v2_message_format=True)
            for instance_response in journal_res.instances_response:
                for j in instance_response.groups:
                    if j.status == chunk.JOURNALGROUP_STAGING:
                        isolating_or_umounting_count += 1

        return isolating_or_umounting_count

    def _get_cluster_isolate_partition_num(self, sd_views):
        isolate_num = 0
        for host in sd_views["hosts"]:
            for disk in host["slow_disks"]:
                if "chunk_part_size" not in disk:
                    continue
                if disk["status"] in [slow_disk.MARK_TO_ISOLATE, slow_disk.CHUNK_INVOLVED, slow_disk.CAP_EXCLUDED]:
                    isolate_num += 1
        return isolate_num

    def _get_cluster_isolate_cache_num(self, sd_views):
        isolate_num = 0
        for host in sd_views["hosts"]:
            for disk in host["slow_disks"]:
                if "chunk_cache_size" not in disk:
                    continue
                if disk["status"] in [slow_disk.MARK_TO_ISOLATE, slow_disk.CHUNK_INVOLVED, slow_disk.CAP_EXCLUDED]:
                    isolate_num += 1
        return isolate_num

    def _need_to_hint_chunk(self, sd_views):
        record = self.sd_service.get_current_node_record(sd_views)
        for disk in record["slow_disks"]:
            if disk["status"] == slow_disk.MARK_TO_ISOLATE:
                return True
        return False

    def _load_sd_views(self):
        try:
            self.sd_views = self.sd_service.get_record_views()
        except Exception as e:
            logging.exception("{}: load slow disk views for next loop failed: {}".format(self.name, e))
            self.sd_views = None

    def loop(self):
        self.sd_views = None
        while not self._stop:
            start = monotonic_time.time()

            try:
                self.process_slow_disk()
            except Exception as e:
                logging.exception("{}: an error occurred during isolate_disk_loop: {}".format(self.name, e))
            finally:
                self._load_sd_views()

            time_used = monotonic_time.time() - start
            logging.info("{}: one loop finished, cost {:.3f}s.".format(self.name, time_used))

            time_to_wait = max(self.interval - time_used, 0)
            self.tick_sleep(time_to_wait)

    def process_slow_disk(self):
        # pre-check before process slow disk
        if not self._check_lsm_support_isolate():
            logging.info("{}: lsm not support partition isolate, skip slow hint.".format(self.name))
            return

        if not self.sd_views:
            logging.info("{}: views not loaded, delay to next loop.".format(self.name))
            return

        record = self.sd_service.get_current_node_record(self.sd_views)
        if not record:
            logging.warning("{}: current node record not found.".format(self.name))
            return

        if not [disk for disk in record["slow_disks"] if disk["status"] != slow_disk.IGNORED]:
            return

        if not self._check_meta_chunk_list_healthy():
            logging.warning("{}: not all chunks in the cluster are healthy, skip slow hint.")
            return

        chunk_partitions = self._list_chunk_partitions()
        if chunk_partitions is None:
            logging.warning("{}: get chunk partition list failed, skip slow hint.")
            return

        chunk_caches = self._list_chunk_caches()
        if chunk_caches is None:
            logging.warning("{}: get chunk cache list failed, skip slow hint.")
            return

        chunk_journals = self._list_chunk_journals()
        if chunk_journals is None:
            logging.warning("{}: get chunk journal list failed, skip slow hint.")
            return

        parts_in_chunk = {
            "partition": chunk_partitions,
            "cache": chunk_caches,
            "journal": chunk_journals,
        }

        host_info = self.host_service.get_current_host_info()

        # process view state machine
        view_changed = False
        sd_views = deepcopy(self.sd_views)
        if self._update_normal_state(sd_views, parts_in_chunk, host_info):
            view_changed = True
        if self._try_mark_to_isolate(sd_views, parts_in_chunk, host_info):
            view_changed = True

        # save record_views and emit chunk isolate rpc if needed
        need_hint = self._need_to_hint_chunk(sd_views)
        if view_changed or need_hint:
            view_saved = self.sd_service.save_record(sd_views)
            if view_saved and need_hint:
                self._hint_slow_disk_to_chunk(sd_views)

    def _update_normal_state(self, sd_views, parts_in_chunk, host_info):
        view_changed = False
        record = self.sd_service.get_current_node_record(sd_views)

        # step1: CHUNK_INVOLVED -> CAP_EXCLUDED
        # CHUNK_INVOLVED from last loop can be marked as CAP_EXCLUDED at this loop
        for disk in record["slow_disks"]:
            if disk["status"] == slow_disk.CHUNK_INVOLVED:
                msg = "{}: update disk {} {} status: CHUNK_INVOLVED -> CAP_EXCLUDED"
                logging.info(msg.format(self.name, disk["disk_name"], disk["disk_serial"]))
                disk["status"] = slow_disk.CAP_EXCLUDED
                view_changed = True

        # step2: MARK_TO_ISOLATE -> CHUNK_INVOLVED
        # check whether MARK_TO_ISOLATE can be marked as CHUNK_INVOLVED
        for disk in record["slow_disks"]:
            if disk["status"] != slow_disk.MARK_TO_ISOLATE:
                continue

            disk_info = disk_map.disk_serials.get(disk["disk_serial"])
            if not disk_info:
                msg = "{}: disk_info not found for {} {}, update status: MARK_TO_ISOLATE -> CHUNK_INVOLVED."
                logging.warning(msg.format(self.name, disk["disk_name"], disk["disk_serial"]))
                disk["status"] = slow_disk.CHUNK_INVOLVED
                view_changed = True
                continue

            # HDD(keep the same logic as before)
            if disk_info.get("is_rotational"):
                chunk_part = self._get_part_from_chunk_for_disk(
                    disk_info, parts_in_chunk["partition"], usage="partition"
                )
                # chunk has remove the disk or mark it as unhealthy
                if not chunk_part:
                    cause = "disk disappeared from chunk partition list"
                    msg = "{}: update disk {} {} status: MARK_TO_ISOLATE -> CHUNK_INVOLVED: {}"
                    logging.info(msg.format(self.name, disk["disk_name"], disk["disk_serial"], cause))
                    disk["status"] = slow_disk.CHUNK_INVOLVED
                    view_changed = True
                elif chunk_part.errflags > 0 or chunk_part.warnflags > 0:
                    cause = "errflags:{} warnflags: {}".format(chunk_part.errflags, chunk_part.warnflags)
                    msg = "{}: update disk {} {} status: MARK_TO_ISOLATE -> CHUNK_INVOLVED: {}"
                    logging.info(msg.format(self.name, disk["disk_name"], disk["disk_serial"], cause))
                    disk["status"] = slow_disk.CHUNK_INVOLVED
                    view_changed = True
            # SSD
            else:
                chunk_part_map = self._get_chunk_part_map(disk_info, host_info, parts_in_chunk)

                # all parts in disk has been removed from chunk
                if not any(chunk_part_map.values()):
                    cause = "all parts in disk has disappeared from chunk"
                    msg = "{}: update disk {} {} status: MARK_TO_ISOLATE -> CHUNK_INVOLVED: {}"
                    logging.info(msg.format(self.name, disk["disk_name"], disk["disk_serial"], cause))
                    disk["status"] = slow_disk.CHUNK_INVOLVED
                    view_changed = True
                # some parts in disk has been removed from chunk
                elif not all(chunk_part_map.values()):
                    cause = "some parts in disk has disappeared from chunk"
                    msg = "{}: update disk {} {} status: MARK_TO_ISOLATE -> CHUNK_INVOLVED: {}"
                    logging.info(msg.format(self.name, disk["disk_name"], disk["disk_serial"], cause))
                    disk["status"] = slow_disk.CHUNK_INVOLVED
                    view_changed = True
                # all parts in disk are still in chunk
                else:
                    for _, partition_in_chunk in chunk_part_map.items():
                        # If partition which is one of the disk error or warn flag is set, we should mark it as CHUNK_INVOLVED  # noqa: E501
                        if partition_in_chunk.errflags > 0 or partition_in_chunk.warnflags > 0:
                            cause = "errflags:{} warnflags: {}".format(
                                partition_in_chunk.errflags, partition_in_chunk.warnflags
                            )
                            msg = "{}: update disk {} {} status: MARK_TO_ISOLATE -> CHUNK_INVOLVED: {}"
                            logging.info(msg.format(self.name, disk["disk_name"], disk["disk_serial"], cause))
                            disk["status"] = slow_disk.CHUNK_INVOLVED
                            view_changed = True
                            break

        # step3: SLOW_DETECTED -> CHUNK_INVOLVED/IGNORED
        # check whether SLOW_DETECTED can be marked as CHUNK_INVOLVED
        for disk in record["slow_disks"]:
            if disk["status"] != slow_disk.SLOW_DETECTED:
                continue

            disk_info = disk_map.disk_serials.get(disk["disk_serial"])
            if not disk_info:
                continue

            # HDD(keep the same logic as before)
            if disk_info.get("is_rotational"):
                chunk_part = self._get_part_from_chunk_for_disk(
                    disk_info, parts_in_chunk["partition"], usage="partition"
                )
                if not chunk_part:  # not be used as data by chunk, no need to issue a rpc call
                    msg = "{}: update disk {} {} status: SLOW_DETECTED -> IGNORED"
                    logging.info(msg.format(self.name, disk["disk_name"], disk["disk_serial"]))
                    disk["status"] = slow_disk.IGNORED
                    view_changed = True
                elif chunk_part.errflags > 0 or chunk_part.warnflags > 0:
                    # chunk has marked it as unhealthy
                    cause = "errflags:{} warnflags: {}".format(chunk_part.errflags, chunk_part.warnflags)
                    msg = "{}: update disk {} {} status: SLOW_DETECTED -> CHUNK_INVOLVED: {}"
                    logging.info(msg.format(self.name, disk["disk_name"], disk["disk_serial"], cause))
                    disk["chunk_part_size"] = chunk_part.total_size
                    disk["part_uuid_in_chunk"] = chunk_part.uuid
                    disk["status"] = slow_disk.CHUNK_INVOLVED
                    view_changed = True
            # SSD
            else:
                chunk_part_map = self._get_chunk_part_map(disk_info, host_info, parts_in_chunk)

                # all parts in disk not be used by chunk, no need to issue a rpc call
                if not any(chunk_part_map.values()):
                    msg = "{}: update disk {} {} status: SLOW_DETECTED -> IGNORED"
                    logging.info(msg.format(self.name, disk["disk_name"], disk["disk_serial"]))
                    disk["status"] = slow_disk.IGNORED
                    view_changed = True
                # some parts in disk has been removed from chunk
                elif not all(chunk_part_map.values()):
                    cause = "some parts in disk has disappeared from chunk"
                    msg = "{}: update disk {} {} status: SLOW_DETECTED -> CHUNK_INVOLVED: {}"
                    logging.info(msg.format(self.name, disk["disk_name"], disk["disk_serial"], cause))
                    if chunk_part_map.get("partition") is not None:
                        disk["chunk_part_size"] = chunk_part_map["partition"].total_size
                        disk["part_uuid_in_chunk"] = chunk_part_map["partition"].uuid
                    if chunk_part_map.get("cache") is not None:
                        disk["chunk_cache_size"] = chunk_part_map["cache"].total_size
                        disk["cache_uuid_in_chunk"] = chunk_part_map["cache"].uuid
                    if chunk_part_map.get("journal") is not None:
                        disk["journal_uuid_in_chunk"] = chunk_part_map["journal"].uuid
                    disk["status"] = slow_disk.CHUNK_INVOLVED
                    view_changed = True
                # all parts in disk are still in chunk
                else:
                    for _, chunk_part in chunk_part_map.items():
                        if chunk_part.errflags > 0 or chunk_part.warnflags > 0:
                            cause = "errflags:{} warnflags: {}".format(chunk_part.errflags, chunk_part.warnflags)
                            msg = "{}: update disk {} {} status: SLOW_DETECTED -> CHUNK_INVOLVED: {}"
                            logging.info(msg.format(self.name, disk["disk_name"], disk["disk_serial"], cause))
                            disk["status"] = slow_disk.CHUNK_INVOLVED
                            view_changed = True
                            break
                    if chunk_part_map.get("partition") is not None:
                        disk["chunk_part_size"] = chunk_part_map["partition"].total_size
                        disk["part_uuid_in_chunk"] = chunk_part_map["partition"].uuid
                    if chunk_part_map.get("cache") is not None:
                        disk["chunk_cache_size"] = chunk_part_map["cache"].total_size
                        disk["cache_uuid_in_chunk"] = chunk_part_map["cache"].uuid
                    if chunk_part_map.get("journal") is not None:
                        disk["journal_uuid_in_chunk"] = chunk_part_map["journal"].uuid

        return view_changed

    def _try_mark_to_isolate(self, sd_views, parts_in_chunk, host_info):
        view_changed = False
        record = self.sd_service.get_current_node_record(sd_views)

        # step1: update exist MARK_TO_ISOLATE chunk_part_size/uuid_in_chunk
        for disk in record["slow_disks"]:
            if disk["status"] != slow_disk.MARK_TO_ISOLATE:
                continue

            disk_info = disk_map.disk_serials.get(disk["disk_serial"])
            if not disk_info:
                msg = "{}: disk_info not found for {} {} during update chunk_size/uuid_in_chunk."
                logging.warning(msg.format(self.name, disk["disk_name"], disk["disk_serial"]))
                continue

            # HDD(keep the same logic as before)
            if disk_info.get("is_rotational"):
                chunk_partition = self._get_part_from_chunk_for_disk(
                    disk_info, parts_in_chunk["partition"], usage="partition"
                )
                if not chunk_partition:
                    msg = "{}: chunk_partition not found for {} {} during update chunk_part_size/part_uuid_in_chunk."
                    logging.warning(msg.format(self.name, disk["disk_name"], disk["disk_serial"]))
                    continue
                if chunk_partition.total_size != disk["chunk_part_size"]:
                    logging.warning(
                        "{}: update chunk_part_size for {} {}: from {} to {}".format(
                            self.name,
                            disk["disk_name"],
                            disk["disk_serial"],
                            disk["chunk_part_size"],
                            chunk_partition.total_size,
                        )
                    )
                    disk["chunk_part_size"] = chunk_partition.total_size
                    view_changed = True
                if chunk_partition.uuid != disk["part_uuid_in_chunk"]:
                    logging.warning(
                        "{}: update part_uuid_in_chunk for {} {}: from {} to {}".format(
                            self.name,
                            disk["disk_name"],
                            disk["disk_serial"],
                            disk["part_uuid_in_chunk"],
                            chunk_partition.uuid,
                        )
                    )
                    disk["part_uuid_in_chunk"] = chunk_partition.uuid
                    view_changed = True
            # SSD
            else:
                chunk_partition = self._get_part_from_chunk_for_disk(
                    disk_info, parts_in_chunk["partition"], usage="partition"
                )
                if chunk_partition:
                    if chunk_partition.total_size != disk["chunk_part_size"]:
                        logging.warning(
                            "{}: update chunk_part_size for {} {}: from {} to {}".format(
                                self.name,
                                disk["disk_name"],
                                disk["disk_serial"],
                                disk["chunk_part_size"],
                                chunk_partition.total_size,
                            )
                        )
                        disk["chunk_part_size"] = chunk_partition.total_size
                        view_changed = True
                    if chunk_partition.uuid != disk["part_uuid_in_chunk"]:
                        logging.warning(
                            "{}: update part_uuid_in_chunk for {} {}: from {} to {}".format(
                                self.name,
                                disk["disk_name"],
                                disk["disk_serial"],
                                disk["part_uuid_in_chunk"],
                                chunk_partition.uuid,
                            )
                        )
                        disk["part_uuid_in_chunk"] = chunk_partition.uuid
                        view_changed = True

                chunk_cache = self._get_part_from_chunk_for_disk(disk_info, parts_in_chunk["cache"], usage="cache")
                if chunk_cache:
                    if chunk_cache.total_size != disk["chunk_cache_size"]:
                        logging.warning(
                            "{}: update chunk_cache_size for {} {}: from {} to {}".format(
                                self.name,
                                disk["disk_name"],
                                disk["disk_serial"],
                                disk["chunk_cache_size"],
                                chunk_cache.total_size,
                            )
                        )
                        disk["chunk_cache_size"] = chunk_cache.total_size
                        view_changed = True
                    if chunk_cache.uuid != disk["cache_uuid_in_chunk"]:
                        logging.warning(
                            "{}: update cache_uuid_in_chunk for {} {}: from {} to {}".format(
                                self.name,
                                disk["disk_name"],
                                disk["disk_serial"],
                                disk["cache_uuid_in_chunk"],
                                chunk_cache.uuid,
                            )
                        )
                        disk["cache_uuid_in_chunk"] = chunk_cache.uuid
                        view_changed = True

                chunk_journal = self._get_part_from_chunk_for_disk(
                    disk_info, parts_in_chunk["journal"], usage="journal"
                )
                if chunk_journal:
                    if chunk_journal.uuid != disk["journal_uuid_in_chunk"]:
                        logging.warning(
                            "{}: update journal_uuid_in_chunk for {} {}: from {} to {}".format(
                                self.name,
                                disk["disk_name"],
                                disk["disk_serial"],
                                disk["journal_uuid_in_chunk"],
                                chunk_journal.uuid,
                            )
                        )
                        disk["journal_uuid_in_chunk"] = chunk_journal.uuid
                        view_changed = True

        # step2: SLOW_DETECTED/CAP_EXCLUDED -> MARK_TO_ISOLATE
        remain_todo = False
        for disk in record["slow_disks"]:
            if disk["status"] in [slow_disk.SLOW_DETECTED, slow_disk.CAP_EXCLUDED]:
                remain_todo = True
        if not remain_todo:
            return view_changed

        # Check whether isolated partition size overflow
        is_isolate_partition_size_overflow = False
        max_isolate_size = self._max_isolate_size()
        cluster_isolate_size = self._get_cluster_isolate_partition_size(sd_views)
        if cluster_isolate_size >= max_isolate_size:
            is_isolate_partition_size_overflow = True

        # Check whether max isolated partition num overflow
        is_max_isolate_partition_num_overflow = False
        max_isolate_partition_num = self._max_single_node_healthy_partition_num()
        cluster_isolate_partition_num = self._get_cluster_isolate_partition_num(sd_views)
        if cluster_isolate_partition_num >= max_isolate_partition_num:
            is_max_isolate_partition_num_overflow = True

        # Check whether max isolated cache num overflow
        is_max_isolate_cache_num_overflow = False
        max_isolate_cache_num = self._max_single_node_healthy_cache_num()
        cluster_isolate_cache_num = self._get_cluster_isolate_cache_num(sd_views)
        if cluster_isolate_cache_num >= max_isolate_cache_num:
            is_max_isolate_cache_num_overflow = True

        # Check whether there is isolating or umounting disk exists
        is_isolating_or_umounting_disk_exists = False
        cluster_isolating_or_umounting_num = self._get_cluster_isolating_or_umounting_num()
        if cluster_isolating_or_umounting_num > 0:
            is_isolating_or_umounting_disk_exists = True

        marked_one = False
        # step2-1: SLOW_DETECTED -> MARK_TO_ISOLATE
        # check whether SLOW_DETECTED can be marked as MARK_TO_ISOLATE
        for disk in record["slow_disks"]:
            if disk["status"] != slow_disk.SLOW_DETECTED:
                continue

            disk_info = disk_map.disk_serials.get(disk["disk_serial"])
            if not disk_info:
                continue

            # HDD(keep the same logic as before)
            if disk_info.get("is_rotational"):
                if is_isolate_partition_size_overflow:
                    logging.info("{}: cluster remain space would be under 10%, skip slow hint.".format(self.name))
                    continue

                if is_max_isolate_partition_num_overflow:
                    logging.info("{}: too many unhealthy chunk partitions, skip slow hint.".format(self.name))
                    continue

                chunk_partition = self._get_part_from_chunk_for_disk(
                    disk_info, parts_in_chunk["partition"], usage="partition"
                )
                if not (chunk_partition and chunk_partition.errflags == 0 and chunk_partition.warnflags == 0):
                    # chunk already involved, no further step required
                    continue
                if (chunk_partition.total_size + cluster_isolate_size) >= max_isolate_size:
                    msg = "{}: skip MARK_TO_ISOLATE {} {} due to not enough remaining storage capacity of the cluster."
                    logging.info(msg.format(self.name, chunk_partition.path, chunk_partition.device_id))
                else:
                    msg = "{}: update disk {} {} status: SLOW_DETECTED -> MARK_TO_ISOLATE"
                    logging.info(msg.format(self.name, chunk_partition.path, chunk_partition.device_id))
                    disk["chunk_part_size"] = chunk_partition.total_size
                    disk["part_uuid_in_chunk"] = chunk_partition.uuid
                    disk["status"] = slow_disk.MARK_TO_ISOLATE
                    view_changed = True
                    marked_one = True
                    break
            # SSD
            else:
                if is_isolating_or_umounting_disk_exists:
                    logging.info(
                        "{}: there is isolating or umounting disk in cluster, skip slow hint.".format(self.name)
                    )
                    continue

                high_latency_disks = self._get_high_latency_disk_in_same_hba(disk_info["bus_location"])
                if len(high_latency_disks) >= 2:
                    logging.warning("{}: there may be HBA card issue, skip slow hint.".format(self.name))
                    continue

                chunk_part_map = self._get_chunk_part_map(disk_info, host_info, parts_in_chunk)

                if (
                    "cache" in chunk_part_map
                    and chunk_part_map["cache"] is not None
                    and is_max_isolate_cache_num_overflow
                ):
                    logging.info("{}: too many unhealthy chunk caches, skip slow hint.".format(self.name))
                    continue

                if (
                    "partition" in chunk_part_map
                    and chunk_part_map["partition"] is not None
                    and is_isolate_partition_size_overflow
                ):
                    logging.info("{}: cluster remain space would be under 10%, skip slow hint.".format(self.name))
                    continue

                if (
                    "partition" in chunk_part_map
                    and chunk_part_map["partition"] is not None
                    and is_max_isolate_partition_num_overflow
                ):
                    logging.info("{}: too many unhealthy chunk partitions, skip slow hint.".format(self.name))
                    continue

                if "journal" in chunk_part_map and self._is_last_part_in_chunk(
                    chunk_part_map["journal"], parts_in_chunk["journal"]
                ):
                    logging.info(
                        "{}: disk {} contains journal partition and only one journal in chunk, skip slow hint.".format(
                            self.name, disk["disk_name"]
                        )
                    )
                    continue

                if "cache" in chunk_part_map and self._is_last_part_in_chunk(
                    chunk_part_map["cache"], parts_in_chunk["cache"]
                ):
                    logging.info(
                        "{}: disk {} contains cache partition and only one cache in chunk, skip slow hint.".format(
                            self.name, disk["disk_name"]
                        )
                    )
                    continue

                if "partition" in chunk_part_map and self._is_last_part_in_chunk(
                    chunk_part_map["partition"], parts_in_chunk["partition"]
                ):
                    logging.info(
                        "{}: disk {} contains partition and only one partition in chunk, skip slow hint.".format(
                            self.name, disk["disk_name"]
                        )
                    )
                    continue

                for function, part_in_chunk in chunk_part_map.items():
                    if function == "journal":
                        if not (part_in_chunk and part_in_chunk.errflags == 0 and part_in_chunk.warnflags == 0):
                            continue
                        msg = "{}: update disk {} {} status: SLOW_DETECTED -> MARK_TO_ISOLATE"
                        logging.info(msg.format(self.name, part_in_chunk.path, part_in_chunk.device_id))
                        disk["status"] = slow_disk.MARK_TO_ISOLATE
                        disk["journal_uuid_in_chunk"] = part_in_chunk.uuid
                        view_changed = True
                        marked_one = True
                    elif function == "partition":
                        if not (part_in_chunk and part_in_chunk.errflags == 0 and part_in_chunk.warnflags == 0):
                            continue
                        if (part_in_chunk.total_size + cluster_isolate_size) >= max_isolate_size:
                            msg = "{}: skip MARK_TO_ISOLATE {} {} due to not enough remaining storage capacity of the cluster."  # noqa: E501
                            logging.info(msg.format(self.name, part_in_chunk.path, part_in_chunk.device_id))
                        else:
                            msg = "{}: update disk {} {} status: SLOW_DETECTED -> MARK_TO_ISOLATE"
                            logging.info(msg.format(self.name, part_in_chunk.path, part_in_chunk.device_id))
                            disk["chunk_part_size"] = part_in_chunk.total_size
                            disk["part_uuid_in_chunk"] = part_in_chunk.uuid
                            disk["status"] = slow_disk.MARK_TO_ISOLATE
                            view_changed = True
                            marked_one = True
                    elif function == "cache":
                        if not (part_in_chunk and part_in_chunk.errflags == 0 and part_in_chunk.warnflags == 0):
                            continue
                        msg = "{}: update disk {} {} status: SLOW_DETECTED -> MARK_TO_ISOLATE"
                        logging.info(msg.format(self.name, part_in_chunk.path, part_in_chunk.device_id))
                        disk["chunk_cache_size"] = part_in_chunk.total_size
                        disk["cache_uuid_in_chunk"] = part_in_chunk.uuid
                        disk["status"] = slow_disk.MARK_TO_ISOLATE
                        view_changed = True
                        marked_one = True

                if marked_one:
                    break

        if marked_one:
            return view_changed

        # step2-2: CAP_EXCLUDED -> MARK_TO_ISOLATE
        # check whether CAP_EXCLUDED can be marked as MARK_TO_ISOLATE
        for disk in record["slow_disks"]:
            if disk["status"] != slow_disk.CAP_EXCLUDED:
                continue

            disk_info = disk_map.disk_serials.get(disk["disk_serial"])
            if not disk_info:
                continue

            # HDD(keep the same logic as before)
            if disk_info.get("is_rotational"):
                if is_isolate_partition_size_overflow:
                    logging.info("{}: cluster remain space would be under 10%, skip slow hint.".format(self.name))
                    continue

                if is_max_isolate_partition_num_overflow:
                    logging.info("{}: too many unhealthy chunk partitions, skip slow hint.".format(self.name))
                    continue

                chunk_partition = self._get_part_from_chunk_for_disk(
                    disk_info, parts_in_chunk["partition"], usage="partition"
                )
                if not (chunk_partition and chunk_partition.errflags == 0 and chunk_partition.warnflags == 0):
                    # chunk already involved, no further step required
                    continue
                if (chunk_partition.total_size + cluster_isolate_size) >= max_isolate_size:
                    msg = "{}: skip MARK_TO_ISOLATE {} {} due to not enough remaining storage capacity of the cluster."
                    logging.info(msg.format(self.name, chunk_partition.path, chunk_partition.device_id))
                else:
                    msg = "{}: update disk {} {} status: CAP_EXCLUDED -> MARK_TO_ISOLATE"
                    logging.info(msg.format(self.name, chunk_partition.path, chunk_partition.device_id))
                    disk["chunk_part_size"] = chunk_partition.total_size
                    disk["part_uuid_in_chunk"] = chunk_partition.uuid
                    disk["status"] = slow_disk.MARK_TO_ISOLATE
                    view_changed = True
                    marked_one = True
                    break
            # SSD
            else:
                if is_isolating_or_umounting_disk_exists:
                    logging.info(
                        "{}: there is isolating or umounting disk in cluster, skip slow hint.".format(self.name)
                    )
                    continue

                high_latency_disks = self._get_high_latency_disk_in_same_hba(disk_info["bus_location"])
                if len(high_latency_disks) >= 2:
                    logging.warning("{}: there may be HBA card issue, skip slow hint.".format(self.name))
                    continue

                chunk_part_map = self._get_chunk_part_map(disk_info, host_info, parts_in_chunk)

                if (
                    "cache" in chunk_part_map
                    and chunk_part_map["cache"] is not None
                    and is_max_isolate_cache_num_overflow
                ):
                    logging.info("{}: too many unhealthy chunk caches, skip slow hint.".format(self.name))
                    continue

                if (
                    "partition" in chunk_part_map
                    and chunk_part_map["partition"] is not None
                    and is_isolate_partition_size_overflow
                ):
                    logging.info("{}: cluster remain space would be under 10%, skip slow hint.".format(self.name))
                    continue

                if (
                    "partition" in chunk_part_map
                    and chunk_part_map["partition"] is not None
                    and is_max_isolate_partition_num_overflow
                ):
                    logging.info("{}: too many unhealthy chunk partitions, skip slow hint.".format(self.name))
                    continue

                if "journal" in chunk_part_map and self._is_last_part_in_chunk(
                    chunk_part_map["journal"], parts_in_chunk["journal"]
                ):
                    logging.info(
                        "{}: disk {} contains journal partition and only one journal in chunk, skip slow hint.".format(
                            self.name, disk["disk_name"]
                        )
                    )
                    continue

                if "cache" in chunk_part_map and self._is_last_part_in_chunk(
                    chunk_part_map["cache"], parts_in_chunk["cache"]
                ):
                    logging.info(
                        "{}: disk {} contains cache partition and only one cache in chunk, skip slow hint.".format(
                            self.name, disk["disk_name"]
                        )
                    )
                    continue

                if "partition" in chunk_part_map and self._is_last_part_in_chunk(
                    chunk_part_map["partition"], parts_in_chunk["partition"]
                ):
                    logging.info(
                        "{}: disk {} contains partition and only one partition in chunk, skip slow hint.".format(
                            self.name, disk["disk_name"]
                        )
                    )
                    continue

                for function, part_in_chunk in chunk_part_map.items():
                    if function == "journal":
                        if not (part_in_chunk and part_in_chunk.errflags == 0 and part_in_chunk.warnflags == 0):
                            continue
                        msg = "{}: update disk {} {} status: CAP_EXCLUDED -> MARK_TO_ISOLATE"
                        logging.info(msg.format(self.name, part_in_chunk.path, part_in_chunk.device_id))
                        disk["status"] = slow_disk.MARK_TO_ISOLATE
                        disk["journal_uuid_in_chunk"] = part_in_chunk.uuid
                        view_changed = True
                        marked_one = True
                    elif function == "partition":
                        if not (part_in_chunk and part_in_chunk.errflags == 0 and part_in_chunk.warnflags == 0):
                            continue
                        if (part_in_chunk.total_size + cluster_isolate_size) >= max_isolate_size:
                            msg = "{}: skip MARK_TO_ISOLATE {} {} due to not enough remaining storage capacity of the cluster."  # noqa: E501
                            logging.info(msg.format(self.name, part_in_chunk.path, part_in_chunk.device_id))
                        else:
                            msg = "{}: update disk {} {} status: CAP_EXCLUDED -> MARK_TO_ISOLATE"
                            logging.info(msg.format(self.name, part_in_chunk.path, part_in_chunk.device_id))
                            disk["chunk_part_size"] = part_in_chunk.total_size
                            disk["part_uuid_in_chunk"] = part_in_chunk.uuid
                            disk["status"] = slow_disk.MARK_TO_ISOLATE
                            view_changed = True
                            marked_one = True
                    elif function == "cache":
                        if not (part_in_chunk and part_in_chunk.errflags == 0 and part_in_chunk.warnflags == 0):
                            continue
                        msg = "{}: update disk {} {} status: CAP_EXCLUDED -> MARK_TO_ISOLATE"
                        logging.info(msg.format(self.name, part_in_chunk.path, part_in_chunk.device_id))
                        disk["chunk_cache_size"] = part_in_chunk.total_size
                        disk["cache_uuid_in_chunk"] = part_in_chunk.uuid
                        disk["status"] = slow_disk.MARK_TO_ISOLATE
                        view_changed = True
                        marked_one = True

                if marked_one:
                    break

        if marked_one:
            return view_changed

        return view_changed

    def _is_last_part_in_chunk(self, chunk_part, parts_in_node):
        if chunk_part is None:
            return False

        parts_in_chunk = []
        for _, part in parts_in_node.items():
            if part.chunk_ins_id == chunk_part.chunk_ins_id:
                parts_in_chunk.append(part)

        if len(parts_in_chunk) == 1 and parts_in_chunk[0].uuid == chunk_part.uuid:
            return True

        return False

    def _hint_slow_disk_to_chunk(self, sd_views):
        record = self.sd_service.get_current_node_record(sd_views)
        for disk in record["slow_disks"]:
            if disk["status"] != slow_disk.MARK_TO_ISOLATE:
                continue

            if not any(
                [disk.get("part_uuid_in_chunk"), disk.get("cache_uuid_in_chunk"), disk.get("journal_uuid_in_chunk")]
            ):
                continue

            disk_name = disk["disk_name"]

            if disk.get("part_uuid_in_chunk") is not None:
                part_uuid_in_chunk = disk["part_uuid_in_chunk"]

                try:
                    logging.info("{}: hint one slow chunk part: {} {}".format(self.name, disk_name, part_uuid_in_chunk))
                    self.chunk_client.partition_isolate(part_uuid_in_chunk, scheme=chunk.DISK_NAME_BY_UUID)
                    logging.info(
                        "{}: hint one slow chunk part {} {} success".format(self.name, disk_name, part_uuid_in_chunk)
                    )
                except Exception as e:
                    logging.exception(
                        "{}: hint one slow chunk part {} {} failed: {}".format(
                            self.name, disk_name, part_uuid_in_chunk, e
                        )
                    )

            if disk.get("cache_uuid_in_chunk") is not None:
                cache_uuid_in_chunk = disk["cache_uuid_in_chunk"]

                try:
                    logging.info(
                        "{}: hint one slow chunk cache: {} {}".format(self.name, disk_name, cache_uuid_in_chunk)
                    )
                    self.chunk_client.cache_isolate(cache_uuid_in_chunk, scheme=chunk.DISK_NAME_BY_UUID)
                    logging.info(
                        "{}: hint one slow chunk cache {} {} success".format(self.name, disk_name, cache_uuid_in_chunk)
                    )
                except Exception as e:
                    logging.exception(
                        "{}: hint one slow chunk cache {} {} failed: {}".format(
                            self.name, disk_name, cache_uuid_in_chunk, e
                        )
                    )

            if disk.get("journal_uuid_in_chunk") is not None:
                journal_uuid_in_chunk = disk["journal_uuid_in_chunk"]

                try:
                    logging.info(
                        "{}: hint one slow chunk journal: {} {}".format(self.name, disk_name, journal_uuid_in_chunk)
                    )
                    self.chunk_client.journal_umount(journal_uuid_in_chunk, scheme=chunk.DISK_NAME_BY_UUID)
                    logging.info(
                        "{}: hint one slow chunk journal {} {} success".format(
                            self.name, disk_name, journal_uuid_in_chunk
                        )
                    )
                except Exception as e:
                    logging.exception(
                        "{}: hint one slow chunk journal {} {} failed: {}".format(
                            self.name, disk_name, journal_uuid_in_chunk, e
                        )
                    )

    def _get_chunk_part_map(self, disk_info, host_info, parts_in_chunk):
        disk_function = self._get_disk_function(disk_info, host_info)
        if disk_function is None:
            return {}

        chunk_part_map = self._get_disk_usage_in_chunk(disk_function)
        if chunk_part_map is None:
            return {}

        chunk_partition = self._get_part_from_chunk_for_disk(disk_info, parts_in_chunk["partition"], usage="partition")
        chunk_cache = self._get_part_from_chunk_for_disk(disk_info, parts_in_chunk["cache"], usage="cache")
        chunk_journal = self._get_part_from_chunk_for_disk(disk_info, parts_in_chunk["journal"], usage="journal")

        # fill the chunk_part_map with partition/cache/journal if they are exist
        if chunk_partition is not None:
            chunk_part_map["partition"] = chunk_partition
        if chunk_cache is not None:
            chunk_part_map["cache"] = chunk_cache
        if chunk_journal is not None:
            chunk_part_map["journal"] = chunk_journal

        return chunk_part_map

    def _get_disk_usage_in_chunk(self, disk_function):
        usage_map = None
        if self.ssd_as_cache:
            if self.disk_data_with_cache:
                usage_map = {
                    "partition": None,
                    "cache": None,
                    "journal": None,
                }
            else:
                if disk_function == "data":
                    usage_map = {
                        "partition": None,
                    }
                else:
                    usage_map = {
                        "cache": None,
                        "journal": None,
                    }
        else:
            usage_map = {
                "partition": None,
                "journal": None,
            }

        return usage_map

    def _get_disk_function(self, disk_info, host_info):
        disk_function = None
        for disk in host_info["disks"]:
            if disk["serial"] == disk_info["serial"]:
                disk_function = disk.get("function")
                break

        return disk_function

    def _get_high_latency_disk_in_same_hba(self, bus_location):
        disk_status_service = DiskStatusService()
        return disk_status_service.list_local_unhealthy_disk_status_in_one_hour(bus_location)
