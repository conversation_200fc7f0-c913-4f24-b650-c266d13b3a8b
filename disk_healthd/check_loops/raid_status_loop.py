# Copyright (c) 2013-2023, SMARTX
# All rights reserved.
from datetime import datetime
import logging
import os

from disk_healthd.core import monotonic_time
from disk_healthd.core.base_loop import Base<PERSON>oop
from disk_healthd.core.disk_map import disk_map
from tuna.hardware.partition import Partition
from tuna.hardware.raid import Raid
from tuna.service.disk_status_service import DiskStatusService


class RaidStatusLoop(BaseLoop):
    def __init__(self, interval=30):
        super().__init__(interval)
        self._latest_detected_count = {}

    def loop(self):
        while not self._stop:
            start = monotonic_time.time()

            try:
                self.check_raid_faulty()
            except Exception as e:
                logging.exception(f"{self.name}: an error occurred during raid_loop: {e}")

            time_used = monotonic_time.time() - start
            logging.info(f"{self.name}: one loop finished, cost {time_used:.3f}s.")

            time_to_wait = max(self.interval - time_used, 0)
            if time_to_wait < 3:
                logging.warning(f"{self.name}: slow loop detected.")
            self.tick_sleep(time_to_wait)

    def check_raid_faulty(self):
        update_disk_list = []
        update_disk_detected_list = []
        raids = Raid.list_all_raid_details()
        update_detected_count = {}
        for raid in raids:
            for part in raid.get("device_table", []):
                if not part.get("device"):
                    continue
                if "faulty" not in part.get("state", []):
                    continue

                raid_path = raid.get("device", "")
                partition_path = part.get("device", "")
                partition_name = os.path.basename(partition_path)
                disk_name = Partition(partition_name).get_disk_name()

                detected_count = self._latest_detected_count.get(partition_path, 0) + 1
                update_detected_count[partition_path] = detected_count
                logging.info(f"{self.name}: {partition_path} faulty detected in {raid_path}, {detected_count=}")

                if disk_name not in disk_map.disk_names:
                    logging.warning(f"{self.name}: disk {disk_name} not found in disk_map")
                    continue

                # Partition is also marked as faulty when removing from raid during umount disk,
                # The faulty state will last for 10s. In this case, the faulty should not be record.
                if detected_count < 5:
                    continue

                logging.info(f"{self.name}: record {disk_name} partition {partition_name} faulty in {raid_path}")
                disk_info = disk_map.disk_names[disk_name]
                hints_and_detail = {"healthy": False, "hints.faulty_in_raid": True}
                hints_detected_detail = ["detected_at.faulty_in_raid"]
                update_disk_list.append((disk_info, hints_and_detail))
                update_disk_detected_list.append((disk_info, hints_detected_detail))

        self._latest_detected_count = update_detected_count
        if update_disk_list:
            DiskStatusService().update_disks_with_fields(update_disk_list)

            now = datetime.now()

            final_update_disk_detected_list = []
            local_disk_status_list = DiskStatusService().list_local_disk_status()
            local_disk_status_map = {status["trace_id"]: status for status in local_disk_status_list}

            for disk_info, hints_detected_detail in update_disk_detected_list:
                trace_id = DiskStatusService._gen_disk_trace_id(disk_info)
                final_hints_detected_detail = {}
                if trace_id in local_disk_status_map:
                    local_disk_status = local_disk_status_map[trace_id]
                    for hints_detected_key in hints_detected_detail:
                        detected_key = hints_detected_key.split(".")[1]
                        if detected_key not in local_disk_status.get("detected_at", {}):
                            final_hints_detected_detail[hints_detected_key] = now

                if final_hints_detected_detail:
                    final_update_disk_detected_list.append((disk_info, final_hints_detected_detail))

            if final_update_disk_detected_list:
                DiskStatusService().update_disks_with_fields(final_update_disk_detected_list)
