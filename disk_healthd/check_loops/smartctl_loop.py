# Copyright (c) 2013-2020, SMARTX
# All rights reserved.
from datetime import datetime
import logging
from subprocess import PIPE, Popen
from threading import Thread

from common.lib.utils.smartctl_tools import ZDevice, get_disk_temperature
from disk_healthd.core import monotonic_time
from disk_healthd.core.base_loop import Base<PERSON>oop
from disk_healthd.core.config import config
from disk_healthd.core.disk_map import disk_map
from tuna.hardware.disk import Disk
from tuna.service.disk_status_service import DiskStatusService


class SmartctlLoop(BaseLoop):
    SMARTCTL_TIMEOUT_S = config.smartctl_loop.SMARTCTL_TIMEOUT_S
    SMARTCTL_HANG_ALERT = config.smartctl_loop.SMARTCTL_HANG_ALERT
    EXTRA_SMART_CHECK = config.smartctl_loop.EXTRA_SMART_CHECK

    THRESHOLD_SMART_5 = config.smartctl_loop.THRESHOLD_SMART_5
    THRESHOLD_SMART_177 = config.smartctl_loop.THRESHOLD_SMART_177
    THRESHOLD_SMART_187 = config.smartctl_loop.THRESHOLD_SMART_187
    THRESHOLD_SMART_188 = config.smartctl_loop.THRESHOLD_SMART_188
    THRESHOLD_SMART_194 = config.smartctl_loop.THRESHOLD_SMART_194
    THRESHOLD_SMART_197 = config.smartctl_loop.THRESHOLD_SMART_197
    THRESHOLD_SMART_198 = config.smartctl_loop.THRESHOLD_SMART_198
    THRESHOLD_SMART_233 = config.smartctl_loop.THRESHOLD_SMART_233

    THRESHOLD_OF_REALLOCATED_SECTORS_COUNT = config.smartctl_loop.THRESHOLD_OF_REALLOCATED_SECTORS_COUNT

    def __init__(self, interval=3600 * 6):
        super().__init__(interval)
        self.smartctl_list = []
        self.smartctl_hang_list = {}
        self.smartctl_thread_list = []

    def loop(self):
        while not self._stop:
            start = monotonic_time.time()

            try:
                self.check_smartctl_status()
            except Exception as e:
                logging.exception("{}: an error occurred during smartctl_loop: {}".format(self.name, e))

            time_used = monotonic_time.time() - start
            logging.info("{}: one loop finished, cost {:.3f}s.".format(self.name, time_used))

            time_to_wait = max(self.interval - time_used, 0)
            self.tick_sleep(time_to_wait)

    def check_smartctl_status(self):
        self.load_smartctl_thread_list()
        too_many_thread = len(self.smartctl_thread_list) >= 10
        if too_many_thread:
            logging.warning("{}: too many smartctl thread hang found, skip loop.".format(self.name))

        self.load_smartctl_process_status()
        too_many_smartctl = len(self.smartctl_list) >= 10
        if too_many_smartctl:
            logging.warning("{}: too many smartctl process found, skip loop.".format(self.name))

        if not (too_many_thread or too_many_smartctl):
            self._check_smartctl_status()

    def load_smartctl_thread_list(self):
        self.smartctl_thread_list = [item for item in self.smartctl_thread_list if item.is_alive()]

    def load_smartctl_process_status(self):
        stdout, _ = Popen(
            "ps axo stat,pid,cmd | grep smartctl | grep -v grep", stdout=PIPE, stderr=PIPE, shell=True, text=True
        ).communicate()

        self.smartctl_list = []
        self.smartctl_hang_list = {}
        for line in stdout.split("\n"):
            line = line.strip()
            if not line:
                continue
            self.smartctl_list.append(line)
            if line.startswith("D"):
                items = line.split()
                if len(items) < 3:
                    logging.info("{}: ps output: {}".format(self.name, line))
                    continue
                hang_pid = items[1]
                self.smartctl_hang_list[hang_pid] = line
        if self.smartctl_hang_list:
            hang_list = "\n".join(list(self.smartctl_hang_list.values()))
            logging.warning("{}: smartctl hang found:\n{}".format(self.name, hang_list))

    def _too_many_smartctl(self):
        if len(self.smartctl_thread_list) >= 10 or len(self.smartctl_list) >= 10:
            return True
        return False

    def _check_smartctl_status(self):
        update_disk_list = []
        update_disk_detected_list = []
        disk_names = Disk.disk_names()
        for disk_name in disk_names:
            if self._stop:
                break

            if self._too_many_smartctl():
                logging.warning("{}: too many smartctl process found, no further checking.".format(self.name))
                break

            if disk_name not in disk_map.disk_names:
                logging.warning("{}: disk {} not found in disk_map".format(self.name, disk_name))
                continue

            disk_info = disk_map.disk_names.get(disk_name)
            smart_info = self._safe_get_smart_info(disk_name)
            if not smart_info:
                continue

            hints_and_detail = {}
            hints_detected_detail = []
            is_healthy = smart_info.get("is_healthy")
            # As origin is_healthy field is used to determine the smart_check hint, to keep the logic
            # simple, we will keep using it here.
            if is_healthy is False:
                hints_and_detail["hints.smart_check"] = True
                hints_detected_detail.append("detected_at.smart_check")
                hints_and_detail["healthy"] = False

            # Handle reallocated sectors count overflow when reallocated_sectors_count overflow
            # This is a special unhealthy case that we want to record, and we use reallocated_sectors_count_overflow
            # to indicate this case to avoid confusion with the is_healthy field.
            reallocated_sectors_count = smart_info.get("reallocated_sectors_count", 0)
            if reallocated_sectors_count >= self.THRESHOLD_OF_REALLOCATED_SECTORS_COUNT:
                msg = "{}: reallocated sectors count overflow detected on {}: reallocated_sectors_count: {} check_threshold: {}".format(  # noqa: E501
                    self.name,
                    disk_name,
                    reallocated_sectors_count,
                    self.THRESHOLD_OF_REALLOCATED_SECTORS_COUNT,
                )
                logging.info(msg)

                hints_and_detail["hints.reallocated_sectors_count_overflow"] = True
                hints_and_detail["extra_info.reallocated_sectors_count"] = reallocated_sectors_count
                hints_and_detail["extra_info.reallocated_sectors_count_threshold"] = ">={}".format(
                    self.THRESHOLD_OF_REALLOCATED_SECTORS_COUNT
                )
                hints_detected_detail.append("detected_at.reallocated_sectors_count_overflow")
                # Record unhealthy status here
                hints_and_detail["healthy"] = False

            smart_attrs = smart_info.get("smart_attrs") or {}
            for key, value in list(smart_attrs.items()):
                if value is not None:
                    hints_and_detail["extra_info.{}".format(key)] = value

            if hints_and_detail:
                update_disk_list.append((disk_info, hints_and_detail))
                update_disk_detected_list.append((disk_info, hints_detected_detail))

        if update_disk_list:
            DiskStatusService().update_disks_with_fields(update_disk_list)

            now = datetime.now()

            final_update_disk_detected_list = []
            local_disk_status_list = DiskStatusService().list_local_disk_status()
            local_disk_status_map = {status["trace_id"]: status for status in local_disk_status_list}

            for disk_info, hints_detected_detail in update_disk_detected_list:
                trace_id = DiskStatusService._gen_disk_trace_id(disk_info)
                final_hints_detected_detail = {}
                if trace_id in local_disk_status_map:
                    local_disk_status = local_disk_status_map[trace_id]
                    for hints_detected_key in hints_detected_detail:
                        detected_key = hints_detected_key.split(".")[1]
                        if detected_key not in local_disk_status.get("detected_at", {}):
                            final_hints_detected_detail[hints_detected_key] = now

                if final_hints_detected_detail:
                    final_update_disk_detected_list.append((disk_info, final_hints_detected_detail))

            if final_update_disk_detected_list:
                DiskStatusService().update_disks_with_fields(final_update_disk_detected_list)

    def _double_check_smartctl_hang(self, disk_name):
        check_1 = set()
        for hang_pid, line in list(self.smartctl_hang_list.items()):
            if disk_name in line:
                logging.warning("{}: check_1: smartctl D found on {}: {}".format(self.name, disk_name, line))
                check_1.add(hang_pid)

        if not check_1:
            return False

        logging.info(
            "{}: double check smartctl D for {} after {}s.".format(self.name, disk_name, self.SMARTCTL_TIMEOUT_S)
        )
        self.tick_sleep(self.SMARTCTL_TIMEOUT_S)

        check_2 = set()
        self.load_smartctl_process_status()
        for hang_pid, line in list(self.smartctl_hang_list.items()):
            if disk_name in line:
                logging.warning("{}: check_2: smartctl D found on {} again: {}".format(self.name, disk_name, line))
                check_2.add(hang_pid)

        check_res = check_1 & check_2
        if not check_res:
            logging.info("{}: smartctl D of check_1 on {} has slipped away.".format(self.name, disk_name))
            return False

        return self.smartctl_hang_list[check_res.pop()]

    def _safe_get_smart_info(self, disk_name):
        hang_line = self._double_check_smartctl_hang(disk_name)
        if hang_line:
            logging.warning("{}: smartctl hang detected on {}: {}".format(self.name, disk_name, hang_line))
            if self.SMARTCTL_HANG_ALERT:
                return {
                    "is_healthy": False,
                    "smart_attrs": {"smartctl_hang": hang_line},
                }
            else:
                logging.info("{}: skip get smart_info for disk {}".format(self.name, disk_name))
                return {}

        smart_info = {"return": {}}
        thread = Thread(target=self._get_smart_info, args=(disk_name, smart_info))
        thread.daemon = True
        thread.start()
        thread.join(timeout=self.SMARTCTL_TIMEOUT_S)
        if not thread.is_alive():
            return smart_info["return"]

        logging.warning("{}: smartctl thread timeout detected on {}".format(self.name, disk_name))
        self.smartctl_thread_list.append(thread)
        self.load_smartctl_process_status()
        hang_line = self._double_check_smartctl_hang(disk_name)
        if not hang_line:
            return {}
        logging.warning("{}: smartctl hang detected on {}: {}".format(self.name, disk_name, hang_line))
        if self.SMARTCTL_HANG_ALERT:
            return {
                "is_healthy": False,
                "smart_attrs": {"smartctl_hang": hang_line},
            }
        else:
            logging.info("{}: skip get smart_info for disk {}".format(self.name, disk_name))
            return {}

    def _get_smart_info(self, disk_name, res):
        try:
            dev_path = "/dev/{}".format(disk_name)
            dev = ZDevice(dev_path)
        except Exception as e:
            logging.exception("{}: Get {} smart info failed: {}".format(self.name, disk_name, e))
            return

        # smartctl -A /dev/sdx
        smart_attrs = {}
        for attr in dev.attributes:
            if attr and attr.num in ["5", "177", "187", "188", "194", "197", "198", "233"]:
                smart_attrs["smart_{}".format(attr.num)] = {
                    "name": attr.name,
                    "value": attr.value,
                    "thresh": attr.thresh,
                    "raw": attr.raw,
                }

        smart_attrs["lifespan"] = self._get_life_span(dev)
        smart_attrs["celsius_temperature"] = self._get_celsius_temperature(dev)
        is_healthy = self._check_and_record_healthy(dev, smart_attrs)

        # Get total reallocated sectors count
        # Sum of:
        #   smart_5: Reallocated Sectors Count
        #   smart_197: Current Pending Sector Count
        try:
            smart_5_raw = smart_attrs.get("smart_5", {}).get("raw", "0")
            smart_197_raw = smart_attrs.get("smart_197", {}).get("raw", "0")
            reallocated_sectors_count = int(smart_5_raw) + int(smart_197_raw)
        except (ValueError, TypeError) as e:
            logging.warning(
                "{}: Failed to parse reallocated sectors count for {}: smart_5={}, smart_197={}, error={}".format(
                    self.name, disk_name, smart_5_raw, smart_197_raw, str(e)
                )
            )
            reallocated_sectors_count = 0

        res["return"] = {
            "is_healthy": is_healthy,
            "smart_attrs": smart_attrs,
            "reallocated_sectors_count": reallocated_sectors_count,
        }

    def _get_celsius_temperature(self, dev):
        if dev.temperature is not None:  # for nvme
            return dev.temperature

        # 194 Temperature_Celsius
        temperature = None
        for attr in dev.attributes:
            if attr and attr.num == "194":
                try:
                    temperature = int(attr.raw)
                except (ValueError, TypeError):
                    msg = "{}: get temperature for {} failed: {}-{}".format(self.name, dev.name, attr.name, attr.raw)
                    logging.warning(msg)
                break

        if not temperature:
            temperature = get_disk_temperature("/dev/{}".format(dev.name))
        return temperature

    def _get_life_span(self, dev):
        if dev.available_spare is not None:  # for nvme
            return dev.available_spare

        # 177 Wear Leveling Count     @ Samsung
        # 233 Media Wearout Indicator @ Intel
        lifespan_dict = {}
        for attr in dev.attributes:
            if attr and attr.num in ["177", "233"]:
                try:
                    lifespan_dict[attr.num] = float(attr.value)
                except (ValueError, TypeError):
                    msg = "{}: get lifespan for {} failed: {}-{}".format(self.name, dev.name, attr.name, attr.value)
                    logging.warning(msg)

        if lifespan_dict.get("233"):
            return lifespan_dict["233"]
        if lifespan_dict.get("177"):
            return lifespan_dict["177"]
        return None

    def _check_and_record_healthy(self, dev, smart_attrs):
        res, extra_res = True, True

        assessment_error = not dev.is_healthy()
        smart_attrs["self_assessment_error"] = assessment_error
        if assessment_error:
            logging.info("{}: S.M.A.R.T. self-assessment failed on {}".format(self.name, dev.name))
            res = False

        # ID    ATTRIBUTE_NAME                        CHECK_FIELD    TRIGGER    DEFAULT
        # 5     Reallocated Sectors Count             raw            no         <= 500
        # 177   Wear Leveling Count     @ Samsung     value          no         >= 20
        # 187   Reported Uncorrectable Errors         raw            yes        <= 0
        # 188   Command Timeout                       value          no         >= 10
        # 194   Temperature_Celsius                   raw            no         <= 45
        # 197   Current Pending Sector Count          raw            no         <= 0
        # 198   Uncorrectable Sector Count            raw            yes        <= 0
        # 233   Media Wearout Indicator @ Intel       value          no         >= 20

        thresholds = [
            ("smart_5", self.THRESHOLD_SMART_5, "raw", False),
            ("smart_177", self.THRESHOLD_SMART_177, "value", False),
            ("smart_187", self.THRESHOLD_SMART_187, "raw", True),
            ("smart_188", self.THRESHOLD_SMART_188, "value", False),
            ("smart_194", self.THRESHOLD_SMART_194, "raw", False),
            ("smart_197", self.THRESHOLD_SMART_197, "raw", False),
            ("smart_198", self.THRESHOLD_SMART_198, "raw", True),
            ("smart_233", self.THRESHOLD_SMART_233, "value", False),
        ]
        for key, check_threshold, check_field, trigger in thresholds:
            if key not in smart_attrs:
                continue

            smart_attr = smart_attrs[key]
            smart_attr["check_field"] = check_field
            smart_attr["check_threshold"] = check_threshold
            smart_attr["check_ok"] = True

            try:
                check_value = int(smart_attr.get(check_field))
            except (ValueError, TypeError):
                msg = "{}: get smart attribute failed on {}: {} {}: {}".format(
                    self.name, dev.name, smart_attr.get("name"), check_field, smart_attr.get(check_field)
                )
                logging.warning(msg)
                continue

            if check_field == "raw":
                smart_attr["check_ok"] = check_value <= check_threshold
            else:
                smart_attr["check_ok"] = check_value >= check_threshold

            if not smart_attr["check_ok"]:
                msg = "{}: S.M.A.R.T. abnormality detected on {}: {} {}: {} check_threshold: {}".format(
                    self.name, dev.name, smart_attr.get("name"), check_field, check_value, check_threshold
                )
                logging.info(msg)
                if trigger:
                    extra_res = False

        if self.EXTRA_SMART_CHECK:
            res &= extra_res

        return res
