# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
from threading import Thread

from disk_healthd.core.config import config
from disk_healthd.core.disk_map import disk_map
from disk_healthd.core.inotify_update import DiskEventWatcher
from disk_healthd.core.slow_disk import Off<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SlowDiskHelper
from tuna.node import is_elf_only


class DiskMonitor:
    CHUNK_STAT_LOOP_INTERVAL_S = config.main.CHUNK_STAT_LOOP_INTERVAL_S
    DISKSTATS_LOOP_INTERVAL_S = config.main.DISKSTATS_LOOP_INTERVAL_S
    ISOLATE_LOOP_INTERVAL_S = config.main.ISOLATE_LOOP_INTERVAL_S
    SMARTCTL_LOOP_INTERVAL_S = config.main.SMARTCTL_LOOP_INTERVAL_S
    RAID_LOOP_INTERVAL_S = config.main.RAID_LOOP_INTERVAL_S

    def __init__(self):
        self.thread = None
        if is_elf_only():
            self._load_elf_only_loop()
        else:
            self._load_loop()

    def _load_loop(self):
        from disk_healthd.check_loops.chunk_status_loop import ChunkStatLoop
        from disk_healthd.check_loops.diskstats_loop import DiskStatsLoop
        from disk_healthd.check_loops.isolate_disk_loop import IsolateDiskLoop
        from disk_healthd.check_loops.raid_status_loop import RaidStatusLoop
        from disk_healthd.check_loops.smartctl_loop import SmartctlLoop

        self.loops = [
            ChunkStatLoop(self.CHUNK_STAT_LOOP_INTERVAL_S),
            DiskStatsLoop(self.DISKSTATS_LOOP_INTERVAL_S),
            IsolateDiskLoop(self.ISOLATE_LOOP_INTERVAL_S),
            SmartctlLoop(self.SMARTCTL_LOOP_INTERVAL_S),
            RaidStatusLoop(self.RAID_LOOP_INTERVAL_S),
        ]

    def _load_elf_only_loop(self):
        from disk_healthd.check_loops.smartctl_loop import SmartctlLoop

        self.loops = [SmartctlLoop(self.SMARTCTL_LOOP_INTERVAL_S)]

    def run(self):
        for loop in self.loops:
            loop.start()

        watcher = DiskEventWatcher()
        watcher.add_callback(watcher.DISK_ADD, self.graceful_restart)
        watcher.watch()

    def start(self):
        self.thread = Thread(target=self.run)
        self.thread.daemon = True
        self.thread.start()

    def graceful_restart(self):
        for loop in self.loops:
            loop.stop()
        for loop in self.loops:
            loop.join()

        disk_map.cumulative_update()
        disk_map.ensure_disk_records()

        SlowDiskHelper().remove_absent_disks()
        OfflineDiskHelper().remove_absent_disks()

        for loop in self.loops:
            loop.start()
