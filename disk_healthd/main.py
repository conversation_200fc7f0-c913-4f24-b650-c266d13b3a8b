# Copyright (c) 2013-2024, SMARTX
# All rights reserved.

import logging
from logging.handlers import RotatingFileHandler
import os
import sys

LOG_FILE = "/var/log/zbs/disk-healthd/disk-healthd.log"


def setup_logger():
    if not os.path.exists(os.path.dirname(LOG_FILE)):
        os.makedirs(os.path.dirname(LOG_FILE))

    formatter = logging.Formatter("[%(asctime)s: %(levelname)s] %(message)s")
    handler = RotatingFileHandler(LOG_FILE, maxBytes=30000000, backupCount=1)  # max 30mb
    handler.setFormatter(formatter)

    root_logger = logging.getLogger()
    root_logger.setLevel(logging.getLevelName(logging.INFO))
    list(map(root_logger.removeHandler, root_logger.handlers))
    root_logger.addHandler(handler)


def init():
    from disk_healthd.core.config import config
    from disk_healthd.core.disk_map import disk_map
    from disk_healthd.core.slow_disk import OfflineDiskHelper, SlowDiskHelper

    try:
        config.load()

        SlowDiskHelper().ensure_schema_init()
        OfflineDiskHelper().ensure_schema_init()

        disk_map.cumulative_update()
        disk_map.upgrade_disk_records()
        disk_map.ensure_disk_records()

        SlowDiskHelper().remove_absent_disks()
        OfflineDiskHelper().remove_absent_disks()
    except Exception as e:
        logging.exception(e)
        sys.exit(1)


def start_disk_monitor():
    from disk_healthd.disk_monitor import DiskMonitor

    try:
        DiskMonitor().start()
    except Exception as e:
        logging.exception(e)
        sys.exit(1)


def start_udp_server():
    from disk_healthd.udp_server import udp_server
    from tuna.config.config_manager import ZBSConfig

    try:
        data_ip = ZBSConfig().get_data_ip()
        udp_server.start(host=data_ip, port=10480)
    except Exception as e:
        logging.exception(e)
        sys.exit(1)


def run_rest_server():
    from disk_healthd.rest_server import flask_app
    from tuna.config.config_manager import ZBSConfig

    try:
        data_ip = ZBSConfig().get_data_ip()
        flask_app.run(host=data_ip, port=10415)
    except Exception as e:
        logging.exception(e)
        sys.exit(1)


def main():
    """
    Since disk-healthd is invoked by gunicorn now,
    this main function would NEVER be used.
    """
    setup_logger()
    init()
    start_disk_monitor()
    start_udp_server()
    run_rest_server()


def gunicorn_entry():
    """
    The main entry invoked by gunicorn.
    gunicorn setup logging via /usr/share/disk_healthd/gunicorn_logging.conf
    there is no need to call setup_logger() again here,
    and similarly, there is no need to call run_rest_server().
    """
    init()
    start_disk_monitor()
    start_udp_server()

    from disk_healthd.rest_server import flask_app

    return flask_app


if __name__ == "__main__":
    main()
