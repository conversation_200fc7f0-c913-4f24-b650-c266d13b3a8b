- hosts: 127.0.0.1
  connection: local
  gather_facts: no
  no_log: True
  vars:
  tasks:
  - name: clean up log_path
    shell: "rm -rf {{ log_path }}/*"
    when: clean_all_logs == "yes" and include_controller == "yes"

  - name: clean up temp_path
    shell: "rm -rf {{ temp_path }}/*"
    when: clean_all_logs == "yes" and include_controller == "yes"

  - name: clean up metadata
    shell: "rm -rf {{ metadata_path }}/*"
    when: clean_all_logs == "yes" and include_controller == "yes"

  - name: clean up logs
    shell: "{{ python_path }} {{ shell_path }}/clean_local_log.py {{ task_uuid }}"
    when: clean_all_logs == "no"

- hosts: cluster
  vars:
  become: yes
  gather_facts: no
  become_user: root
  become_method: sudo
  tasks:
  - name: clean up log_path
    shell: "rm -rf {{ log_path }}/*"
    when: clean_all_logs == "yes"

  - name: clean up temp_path
    shell: "rm -rf {{ temp_path }}/*"
    when: clean_all_logs == "yes"

  - name: clean up metadata
    shell: "rm -rf {{ metadata_path }}/*"
    when: clean_all_logs == "yes"

  - name: clean up logs
    shell: "{{ python_path }} {{ shell_path }}/clean_local_log.py {{ task_uuid }}"
    when: clean_all_logs == "no"
