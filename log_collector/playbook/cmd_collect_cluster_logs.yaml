- hosts: 127.0.0.1
  gather_facts: no
  connection: local
  tasks:
  - name: make task_uuid dir
    file:
      path: "{{ temp_path }}/{{ task_uuid }}"
      state: directory


- hosts: cluster
  gather_facts: no
  become: yes
  become_user: root
  become_method: sudo
  vars:
    tmp_file: /tmp/tmp_log_local_collector_{{ task_uuid }}.txt
    start_time: "{{ start_time }}"
    end_time: "{{ end_time }}"
    task_uuid: "{{ task_uuid }}"
    log_path: "{{ log_path }}"
    log_groups: "{{ log_groups }}"
    log_services: "{{ log_services }}"
    log_file_name: "{{ log_file_name }}"

  tasks:
  - name: clean up temporary and log files
    shell: |
      rm -rf /tmp/tmp_log_local_collector_*.txt
      rm -rf {{ temp_path }}/*{{ task_uuid }}*

  - name: execute clean_collector_path
    shell: "{{ python_path }} {{ shell_path }}/clean_collector_path.py"
    register: clean_output

  - name: output debug info to log
    debug: var=clean_output verbosity=0

  - name: collect node log and save
    shell: "nohup unbuffer {{ python_path }} {{ shell_path }}/collect_local_log.py {{ start_time }} {{ end_time }} {{ task_uuid }} {{ log_size }} {{ group_service_pattern | to_json | string }} > {{ tmp_file }}"
    async: 1000
    poll: 0
    register: async_result

  - name: output progress
    shell: grep 'LOG_COLLECTOR_PROGRESS#SUCCESS' {{ tmp_file }}
    register: grep_output
    retries: 120
    until: grep_output.rc == 0

  - name: Cleanup async task
    async_status:
      jid: "{{ async_result.ansible_job_id }}"
      mode: cleanup
    when: grep_output.rc == 0 or grep_output.attempts == 120

  - name: find zipped log file
    shell: "ls -t {{ log_path }} | grep {{ task_uuid }}.tar.gz | head -1"
    register: zip_file_name

  - name: transfer zipped log file to controller node
    fetch:
      src: "{{ log_path }}/{{ zip_file_name.stdout }}"
      dest: "{{ temp_path }}/{{ task_uuid }}/{{ zip_file_name.stdout }}"
      flat: yes

  - name: find summary file
    shell: "ls -t {{ temp_path }} | grep summary | grep {{ task_uuid }} | head -1"
    register: summary_file_name

  - name: transfer summary file to controller node
    fetch:
      src: "{{ temp_path }}/{{ summary_file_name.stdout }}"
      dest: "{{ temp_path }}/{{ task_uuid }}/{{ summary_file_name.stdout }}"
      flat: yes

  - name: delete log files
    shell: "rm -rf {{ log_path }}/*{{ task_uuid }}.tar.gz"

  - name: delete summary files
    shell: "rm -rf {{ temp_path }}/summary*{{ task_uuid }}*"

  - name: get log file size
    shell: ls -l {{ log_path }}/*{{ task_uuid }}.tar.gz | head -1 | awk '{print $5}'
    register: log_file_size

- hosts: 127.0.0.1
  connection: local
  gather_facts: no
  vars:
  tasks:
  - name: Combine all summary files to single file
    shell: "head -12 -q {{ temp_path }}/{{ task_uuid }}/summary_* > {{ temp_path }}/{{ task_uuid }}/summary.txt"

  - name: Delete summary file
    shell: "rm -rf {{ temp_path }}/{{ task_uuid }}/summary_*"

  - name: Rename task_uuid dir
    shell: "mv {{ temp_path }}/{{ task_uuid }} {{ temp_path }}/cluster-logs-{{ task_uuid }}"

  - name: Compress log dir
    archive:
      path: "{{ temp_path }}/cluster-logs-{{ task_uuid }}"
      dest: "{{ log_file_name }}"
      format: gz
