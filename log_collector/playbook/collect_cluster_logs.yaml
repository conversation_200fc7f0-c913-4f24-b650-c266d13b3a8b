- hosts: 127.0.0.1
  gather_facts: no
  connection: local
  tasks:
  - name: clean up temp_path
    shell: "rm -rf {{ temp_path }}/*{{ task_uuid }}*"

  - name: execute clean_collector_path
    shell: "{{ python_path }} {{ shell_path }}/clean_collector_path.py"
    register: clean_output

  - name: output debug info to log
    debug: var=clean_output verbosity=0

  - name: make task_uuid dir
    file:
      path: "{{ temp_path }}/{{ task_uuid }}"
      state: directory

- hosts: cluster
  gather_facts: no
  become: yes
  become_user: root
  become_method: sudo
  vars:
    tmp_file: /tmp/tmp_log_local_collector_{{ task_uuid }}.txt
    start_time: "{{ start_time }}"
    end_time: "{{ end_time }}"
    task_uuid: "{{ task_uuid }}"
    log_path: "{{ log_path }}"
    group_service_pattern: "{{ group_service_pattern }}"
    send_log_to_controller: "yes"
  tasks:
  - name: clean up temporary and log files
    shell: |
      rm -rf /tmp/tmp_log_local_collector_*.txt
      rm -rf {{ temp_path }}/*

  - name: execute clean_collector_path
    shell: "{{ python_path }} {{ shell_path }}/clean_collector_path.py"
    register: clean_output

  - name: output debug info to log
    debug: var=clean_output verbosity=0

  - name: collect node log and save
    shell: "nohup unbuffer {{ python_path }} {{ shell_path }}/collect_local_log.py {{ start_time }} {{ end_time }} {{ task_uuid }} {{ log_size }} {{ group_service_pattern | to_json | string }} > {{ tmp_file }}"
    async: 1000
    poll: 0
    register: async_result

  - name: output progress
    shell: grep 'LOG_COLLECTOR_PROGRESS#SUCCESS' {{ tmp_file }}
    register: grep_output
    retries: 120
    until: grep_output.rc == 0

  - name: Cleanup async task
    async_status:
      jid: "{{ async_result.ansible_job_id }}"
      mode: cleanup
    when: grep_output.rc == 0 or grep_output.attempts == 120

  - name: find zipped log file
    shell: "ls -t {{ log_path }} | grep {{ task_uuid }}.tar.gz | head -1"
    register: zip_file_name

  - name: transfer zipped log file to controller node
    fetch:
      src: "{{ log_path }}/{{ zip_file_name.stdout }}"
      dest: "{{ temp_path }}/{{ task_uuid }}/{{ zip_file_name.stdout }}"
      flat: yes
    when: send_log_to_controller == "yes"

  - name: find summary file
    shell: "ls -t {{ temp_path }} | grep summary | grep {{ task_uuid }} | head -1"
    register: summary_file_name

  - name: transfer summary file to controller node
    fetch:
      src: "{{ temp_path }}/{{ summary_file_name.stdout }}"
      dest: "{{ temp_path }}/{{ task_uuid }}/{{ summary_file_name.stdout }}"
      flat: yes
    when: send_log_to_controller == "yes"

  - name: delete log files
    shell: "rm -rf {{ log_path }}/*{{ task_uuid }}.tar.gz"
    when: send_log_to_controller == "yes"

  - name: delete summary files
    shell: "rm -rf {{ temp_path }}/summary*{{ task_uuid }}*"

  - name: get log file size
    shell: ls -l {{ log_path }}/*{{ task_uuid }}.tar.gz | head -1 | awk '{print $5}'
    register: log_file_size
    when: send_log_to_controller == "no"

  - name: write log size to file
    shell: echo {{ log_file_size.stdout }} > {{ temp_path }}/log_size_{{ task_uuid }}_{{ log_file_size.stdout }}
    when: send_log_to_controller == "no"

  - name: transfer log size file to controller node
    fetch:
      src: "{{ temp_path }}/log_size_{{ task_uuid }}_{{ log_file_size.stdout }}"
      dest: "{{ temp_path }}/{{ task_uuid }}/log_size_{{ task_uuid }}_{{ log_file_size.stdout }}"
      flat: yes
    when: send_log_to_controller == "no"

- hosts: 127.0.0.1
  connection: local
  gather_facts: no
  tasks:
  - name: Find the specific controller log file
    find:
      paths: "{{ log_path }}"
      patterns: "{{ task_uuid }}.tar.gz"
      recurse: no
    register: zip_file
    when: send_log_to_controller == "yes"

  - name: Copy the controller log file to task_uuid directory
    copy:
      src: "{{ zip_file.files[0].path }}"
      dest: "{{ temp_path }}/{{ task_uuid }}/"
    when: include_controller == "yes" and send_log_to_controller == "yes" and zip_file.matched > 0

  - name: Delete the original controller log
    file:
      path: "{{ zip_file.files[0].path }}"
      state: absent
    when: include_controller == "yes" and send_log_to_controller == "yes" and zip_file.matched > 0

  - name: Find the controller summary file
    find:
      paths: "{{ temp_path }}"
      patterns: "summary*{{ task_uuid }}*"
      recurse: no
    register: summary_file
    when: send_log_to_controller == "yes"

  - name: Copy the summary file to task_uuid directory
    copy:
      src: "{{ summary_file.files[0].path }}"
      dest: "{{ temp_path }}/{{ task_uuid }}/"
    when: include_controller == "yes" and send_log_to_controller == "yes" and summary_file.matched > 0

  - name: Combine all summary files into a single file
    shell: "cat {{ temp_path }}/{{ task_uuid }}/summary_* > {{ temp_path }}/{{ task_uuid }}/summary_combined.txt"
    when: send_log_to_controller == "yes" and summary_file.matched > 0

  - name: Delete individual summary files
    file:
      path: "{{ item.path }}"
      state: absent
    with_items: "{{ summary_file.files }}"
    when: send_log_to_controller == "yes" and summary_file.matched > 0

  - name: Rename task_uuid directory for logs
    command: "mv {{ temp_path }}/{{ task_uuid }} {{ temp_path }}/cluster-logs-{{ task_uuid }}"
    when: send_log_to_controller == "yes"

  - name: Compress the log directory
    archive:
      path: "{{ temp_path }}/cluster-logs-{{ task_uuid }}"
      dest: "{{ log_path }}/cluster-logs-{{ task_uuid }}.tar.gz"
      format: gz
    when: send_log_to_controller == "yes"
