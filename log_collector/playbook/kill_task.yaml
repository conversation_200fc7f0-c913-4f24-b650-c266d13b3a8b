- hosts: 127.0.0.1
  connection: local
  gather_facts: no
  no_log: True
  vars:
  tasks:
  - name: clean up log_path
    shell: ps -ef|grep {{ task_uuid }}|grep -v grep|grep -v {{ kill_symbol }}|grep -v PPID|awk '{print $2}'
    register: running_processes

  - name: Kill running processes
    shell: "kill -9 {{ item }}"
    with_items: "{{ running_processes.stdout_lines }}"
    ignore_errors: yes

  - name: sleep 3s to wait process exit
    shell: "sleep 3"

  - name: clean up tmp_file
    shell: "rm -rf {{ log_path }}/*{{ task_uuid }}*"

  - name: clean up log_path
    shell: "rm -rf {{ log_path }}/*{{ task_uuid }}*"

  - name: clean up temp_path
    shell: "rm -rf {{ temp_path }}/*{{ task_uuid }}*"

  - name: clean up metadata
    shell: "rm -rf {{ metadata_path }}/*{{ task_uuid }}*"

- hosts: cluster
  vars:
  become: yes
  gather_facts: no
  become_user: root
  become_method: sudo
  tasks:
  - name: clean up log_path
    shell: ps -ef|grep {{ task_uuid }}|grep -v grep|grep -v {{ kill_symbol }}|grep -v PPID|awk '{print $2}'
    register: running_processes

  - name: Kill running processes
    shell: "kill -9 {{ item }}"
    with_items: "{{ running_processes.stdout_lines }}"
    ignore_errors: yes

  - name: sleep 3s to wait process exit
    shell: "sleep 3"

  - name: clean up tmp_file
    shell: "rm -rf {{ log_path }}/*{{ task_uuid }}*"

  - name: clean up log_path
    shell: "rm -rf {{ log_path }}/*{{ task_uuid }}*"

  - name: clean up temp_path
    shell: "rm -rf {{ temp_path }}/*{{ task_uuid }}*"

  - name: clean up metadata
    shell: "rm -rf {{ metadata_path }}/*{{ task_uuid }}*"
