# Copyright (c) 2013-2024, SMARTX
# All rights reserved.


import os
import shutil
import time

import requests

from log_collector.config.constants import (
    CLUSTER_ZIP_LOG_PREFIX,
    LOG_LOG_PATH,
    LOG_METADATA_PATH,
    LOG_TEMP_PATH,
)
from log_collector.config.logger import logger

DIR_SIZE_THRESHOLD = 3 * 1024 * 1024 * 1024


def _get_metadata_list():
    try:
        resp = requests.get("http://localhost/api/v2/logs")

        return resp.json().get("data", [])
    except Exception as err:
        logger.exception("clean_collector_path: get metadata_list failed: %s" % err)
        return []


def _delete_log(task_uuid):
    try:
        requests.delete("http://localhost/api/v2/logs/{}".format(task_uuid))
    except (IndexError, KeyError) as err:
        logger.exception("clean_collector_path: delete {} log failed: {}".format(task_uuid, err))


def _delete_file(path, pattern):
    try:
        for f in os.listdir(path):
            if os.path.isfile(path + os.sep + f) and pattern in f:
                os.remove(path + os.sep + f)
            elif os.path.isdir(path + os.sep + f) and pattern in f:
                shutil.rmtree(path + os.sep + f)
    except OSError as err:
        logger.exception("clean_collector_path: _delete_file failed: %s" % err)


def find_latest_metadata():
    try:
        metadata_list = _get_metadata_list()
        valid_metadata_list = [item for item in metadata_list if item.get("collect_start_timestamp")]

        metadata_sort_list = sorted(valid_metadata_list, key=lambda metadata: int(metadata["collect_start_timestamp"]))

        return [x["task_uuid"] for x in metadata_sort_list][-3:]
    except (IndexError, KeyError) as err:
        logger.exception("clean_collector_path: find latest metadata failed: %s" % err)
        return []


def delete_metadata_and_log_file(uuid_list):
    metadata_list = _get_metadata_list()

    for metadata in metadata_list:
        try:
            if metadata["task_uuid"] not in uuid_list and metadata["status"] != "running":
                _delete_file(LOG_METADATA_PATH, metadata["task_uuid"])
                _delete_file(LOG_LOG_PATH, metadata["task_uuid"])
        except (TypeError, OSError, KeyError):
            logger.exception("clean_collector_path: delete_metadata_and_log_file failed: uuid_list=%s" % uuid_list)


def delete_log_files_without_metadata():
    def _check_task_uuid_in_filename(uuid_list, filename):
        for task_uuid in uuid_list:
            if task_uuid in filename:
                return True
        return False

    def _check_is_top_2_zipped_log_file(filename):
        zipped_log_files = _get_zipped_log_files()
        if filename in zipped_log_files[-2:]:
            return True
        return False

    metadata_list = _get_metadata_list()
    task_uuid_list = [x["task_uuid"] for x in metadata_list]

    path = LOG_LOG_PATH
    for f in os.listdir(path):
        try:
            if (
                os.path.isfile(path + os.sep + f)
                and not _check_task_uuid_in_filename(task_uuid_list, f)
                and not _check_is_top_2_zipped_log_file(f)
            ):
                os.remove(path + os.sep + f)
            elif os.path.isdir(path + os.sep + f):
                shutil.rmtree(path + os.sep + f)
        except (TypeError, OSError, KeyError) as err:
            logger.exception("clean_collector_path: delete_log_files_without_metadata failed: ({}, {})".format(f, err))


def _get_zipped_log_files():
    log_files = []

    for f in os.listdir(LOG_LOG_PATH):
        if f.startswith(CLUSTER_ZIP_LOG_PREFIX) and os.path.isfile(LOG_LOG_PATH + os.sep + f):
            log_files.append((f, os.path.getmtime(LOG_LOG_PATH + os.sep + f)))
    return [x[0] for x in sorted(log_files, key=lambda y: y[1])]


def ensure_log_size(log_size):
    """
    ensure dir size smaller than log_size
    :param log_size:
    :return:
    """

    def _get_dir_size(dir_path):
        dir_size_total = 0
        for f in os.listdir(dir_path):
            dir_size_total += os.path.getsize(dir_path + os.sep + f)
        return dir_size_total

    def _delete_oldest_zipped_log_file(dir_path):
        log_files = []
        for i in os.listdir(dir_path):
            if i.startswith(CLUSTER_ZIP_LOG_PREFIX):
                log_files.append((i, os.path.getmtime(LOG_LOG_PATH + os.sep + i)))
        log_files_sorted = sorted(log_files, key=lambda y: y[1])
        os.remove(dir_path + os.sep + log_files_sorted[0][0])

    def _delete_oldest_task_log_file():
        metadata_list = _get_metadata_list()

        metadata_sort_list = sorted(metadata_list, key=lambda metedata: metedata["collect_start_timestamp"])

        oldest_task = next(x["task_uuid"] for x in metadata_sort_list)
        _delete_log(oldest_task)

    def _is_zipped_log_file_exist(dir_path):
        for i in os.listdir(dir_path):
            if i.startswith(CLUSTER_ZIP_LOG_PREFIX):
                return True
        return False

    while _get_dir_size(LOG_LOG_PATH) > log_size:
        if _is_zipped_log_file_exist(LOG_LOG_PATH):
            _delete_oldest_zipped_log_file(LOG_LOG_PATH)
        else:
            _delete_oldest_task_log_file()


def clean_tmp_dir():
    time_now = time.time()
    for f in os.listdir(LOG_TEMP_PATH):
        try:
            file_mtime = os.path.getmtime(LOG_TEMP_PATH + os.sep + f)
            if time_now - file_mtime > 3600:
                if os.path.isfile(LOG_TEMP_PATH + os.sep + f):
                    os.remove(LOG_TEMP_PATH + os.sep + f)
                elif os.path.isdir(LOG_TEMP_PATH + os.sep + f):
                    shutil.rmtree(LOG_TEMP_PATH + os.sep + f)
        except OSError as err:
            logger.exception("clean_collector_path: clean tmp dir failed, {} get error: {}".format(f, err))


if __name__ == "__main__":
    try:
        metadata_uuid_list = find_latest_metadata()
        delete_metadata_and_log_file(metadata_uuid_list)
        delete_log_files_without_metadata()
        ensure_log_size(DIR_SIZE_THRESHOLD)
        clean_tmp_dir()
    except (OSError, IndexError, KeyError) as e:
        print(e)
