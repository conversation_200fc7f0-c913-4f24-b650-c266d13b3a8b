# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
import concurrent.futures
from configparser import NoOptionError, SafeConfigParser
import importlib
import logging
import os
import shutil
import socket
import sys
import time
from uuid import uuid4

import click
import ethtool

from log_collector.config.constants import (
    EXECUTE_TIMOUT,
    PLATFORM_VMWARE,
    PROCESS_DISK_STATE_CHECK_CMD,
    SOS_EXECUTE_TIMEOUT,
    ZBS_CONFIG_FILE,
)
from log_collector.config.logger import node_logger
from log_collector.utils.cmdline import run_shell_script_with_return_output
from log_collector.utils.hardware import get_storage_controller, get_vmware_physical_nic_info
from log_collector.utils.log_config import LogConf

DEFAULT_LOG_PATH = "/usr/share/tuna/smartx_logs/"
importlib.reload(sys)


class CollectInfo:
    def __init__(self, uuid, dst_path="", split_file=False, disk_check=True, node_info=True, sos=True, sos_plugin=None):
        self._uuid = uuid
        self._dst_path = dst_path
        self._start_time = time.strftime("%Y%m%d%H%M", time.localtime())
        self._log_path = self._dst_path + "node_info_" + self._uuid + "/"
        self._local_ip = self._get_local_ip()
        self._collect_file = self._log_path + "report_node_info_" + self._local_ip
        self._split_file = split_file
        self._disk_check = disk_check
        self.node_info_collect = node_info
        self.sos_collect = sos
        self.sos_future = None
        self.sos_plugin = sos_plugin
        self._config_dir()
        self._logger = self._create_rotating_log()

    def _get_local_ip(self):
        parser = SafeConfigParser()
        try:
            parser.read(ZBS_CONFIG_FILE)
            local_ip = str(parser.get("network", "web_ip")).replace(".", "_")
        except NoOptionError:
            local_ip = str(socket.gethostbyname(socket.gethostname())).replace(",", "_")
        finally:
            return local_ip

    def _create_rotating_log(self):
        formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - " + self._local_ip + " - %(message)s")
        logger = logging.getLogger("node_info")
        logger.setLevel(logging.DEBUG)

        # add console handler
        ch = logging.StreamHandler()
        ch.setLevel(logging.INFO)
        ch.setFormatter(formatter)
        logger.addHandler(ch)
        return logger

    def _config_dir(self):
        if not os.path.exists(self._log_path):
            os.makedirs(self._log_path)

    def _format_save(self, section, inputs, file):
        if os.path.exists(file):
            action = "a"
        else:
            action = "w"
        if isinstance(inputs, bytes):
            inputs = inputs.decode("utf-8")
        with open(file, action) as f:
            content = "################# cmd: {} #################\n".format(section)
            content += inputs.strip()
            content += "\n\n"
            f.write(content)

    def _split(self, function_name):
        if self._split_file:
            return self._log_path + self._local_ip + "_" + str(function_name)
        return self._collect_file

    def _copy_dir(self, src_path):
        """
        :param src_path: source path where need to copy
        :type src_path: list
        :return:
        """
        for path in src_path:
            if os.path.exists(path):
                dst_path = self._log_path + path[1:]
                node_logger.info("Copy file to {} from {}".format(dst_path, path))
                try:
                    shutil.copytree(path, dst_path)
                except Exception as e:
                    node_logger.error("Copy file failed: {}".format(e))

    def execute_cmd(self, cmd, timeout=EXECUTE_TIMOUT):
        node_logger.info("excute cmd is: %s" % cmd)
        return run_shell_script_with_return_output(cmd, timeout=timeout, logger=node_logger)

    def collect_installed_rpm(self):
        def return_rpm_cmd(query_fmt, filter_cmd):
            rpmq_cmd = "rpm --nodigest -qa --qf=%s" % query_fmt
            shell_cmd = rpmq_cmd
            if filter_cmd:
                shell_cmd = "sh -c '%s'" % (rpmq_cmd + "|" + filter_cmd)
            return shell_cmd

        query_fmt = '"%{NAME}-%{VERSION}-%{RELEASE}.%{ARCH}~~'
        query_fmt = query_fmt + '%{INSTALLTIME:date}\\t%{VENDOR}\n"'
        filter_cmd = 'awk -F "~~" ' r'"{printf \"%-80s %s\n\",\$1,\$2}"|sort -V'
        cmd = return_rpm_cmd(query_fmt, filter_cmd)
        self._logger.info("Collect Installed rpm list.")
        node_logger.info("Collect Installed rpm list.")
        file = self._split(sys._getframe().f_code.co_name)
        rc, output = self.execute_cmd(cmd)
        if rc == 0:
            self._format_save("collect_installed_rpm", output, file=file)

    def collect_kernel_info(self):
        cmd = "uname -a"
        self._logger.info("Collect kernel info")
        node_logger.info("Collect kernel info")
        file = self._split(sys._getframe().f_code.co_name)
        rc, output = self.execute_cmd(cmd)
        if rc == 0:
            self._format_save("collect_kernel_info", output, file=file)

    def collect_services_config(self):
        services_log_path = ["/etc/tuna/", "/etc/sysconfig/", "/etc/default/", "/etc/zookeeper/"]
        self._logger.info("Collect services config file.")
        node_logger.info("Collect services config file.")
        self._copy_dir(services_log_path)

    def collect_deploy_config(self):
        deploy_config_path = ["/etc/zbs/", "/usr/share/zbs_deploy/"]
        self._logger.info("Collect deploy config file.")
        node_logger.info("Collect deploy config file.")
        self._copy_dir(deploy_config_path)

    def _disk_process_hang(self):
        _, output = self.execute_cmd(PROCESS_DISK_STATE_CHECK_CMD)
        for line in output.split("\n"):
            line = line.strip()
            if not line:
                continue
            if line.startswith("D"):
                return True
        return False

    def collect_disk_info(self):
        if not self._disk_check or self._disk_check == "False":
            self._logger.info("Not set disk_check, skip collect disk_info")
            return
        if self._disk_process_hang():
            self._logger.info("There is disk process hang, skip collect disk info")
            return
        self._logger.info("Collect disk info.")
        file = self._split(sys._getframe().f_code.co_name)

        disk_cmds = "lsblk;"
        disk_cmds = disk_cmds + "lsblk -t;"
        disk_cmds = disk_cmds + "lsblk -D;"
        disk_cmds = disk_cmds + "blkid -c /dev/null;"
        disk_cmds = disk_cmds + "blockdev --report;"

        if os.path.isdir("/sys/block"):
            for disk in os.listdir("/sys/block"):
                if disk.startswith("ram"):
                    continue
                disk_path = os.path.join("/dev/", disk)
                disk_cmds = disk_cmds + "smartctl -a %s;" % disk_path
                disk_cmds = disk_cmds + "parted -s %s unit s print;" % disk_path
                disk_cmds = disk_cmds + "fdisk -l %s;" % disk_path

        for cmd in disk_cmds.split(";"):
            rc, output = self.execute_cmd(cmd)
            if rc is None:
                self._logger.warning("log_collector: disk info has hang process, please check it")
                break
            if rc == 0 and output:
                self._format_save(cmd, output, file=file)

    def collect_nic_info(self):
        self._logger.info("Collect nic info.")
        file = self._split(sys._getframe().f_code.co_name)
        nic_cmds = "ip -o addr;"
        nic_cmds = nic_cmds + "route -n;"

        for nic in ethtool.get_devices():
            nic_cmds = nic_cmds + "ethtool %s;" % nic
            nic_cmds = nic_cmds + "ethtool -i %s;" % nic

        for cmd in nic_cmds.split(";"):
            rc, output = self.execute_cmd(cmd)
            if rc == 0 and output:
                self._format_save(cmd, output, file=file)

    def collect_services_status(self):
        self._logger.info("Collect SmartX Services status.")
        node_logger.info("Collect SmartX Services status.")
        file = self._split(sys._getframe().f_code.co_name)

        cmd = "/usr/share/tuna/script/control_all_services.sh --action=status --group=role"
        rc, output = self.execute_cmd(cmd)
        if rc == 0:
            self._format_save("collect services status", output, file=file)

    def collect_system_status(self):
        self._logger.info("Collect system status.")
        node_logger.info("Collect system status.")
        file = self._split(sys._getframe().f_code.co_name)
        cmds = "sar -u 2 5;"
        cmds = cmds + "mpstat -P ALL;"
        cmds = cmds + "ps -eo pcpu,rss,pid,user,args |sort -k 1 -r |head -10;"
        cmds = cmds + "free -h;"
        _, output = self.execute_cmd("rpm -qa | grep smartx-ntpm")
        if output:
            cmds = cmds + "ntpm server show"
        else:
            cmds = cmds + "zbs-tool ntp show"
        for cmd in cmds.split(";"):
            rc, output = self.execute_cmd(cmd)
            if rc == 0:
                self._format_save(cmd, output, file=file)

    def collect_zbs_info(self):
        self._logger.info("Collect zbs info.")
        node_logger.info("Collect zbs info.")
        file = self._split(sys._getframe().f_code.co_name)
        cmds = "zbs-chunk journal list;"
        cmds = cmds + "zbs-chunk cache list;"
        cmds = cmds + "zbs-chunk partition list;"
        cmds = cmds + "zbs-meta chunk list;"
        cmds = cmds + "zbs-meta topo list;"  # pragma: no cover
        cmds = cmds + "zbs-meta cluster summary;"  # pragma: no cover
        cmds = cmds + "zbs-tool service list;"
        cmds = cmds + "zbs-tool zk list;"
        cmds = cmds + "zbs-node mongo list;"
        cmds = cmds + "zbs-meta pextent find need_recover;"
        cmds = cmds + "zbs-meta pextent find may_recover;"
        cmds = cmds + "zbs-meta pextent find dead"

        for cmd in cmds.split(";"):
            rc, output = self.execute_cmd(cmd)
            if rc == 0:
                self._format_save(cmd, output, file=file)

    def collect_hardware_info(self):
        try:
            self._logger.info("Collect hardware info.")
            node_logger.info("Collect hardware info.")
            log_conf_obj = LogConf()
            output = "Storage Controller:\n{}"
            storage_controller = get_storage_controller()
            file = self._split(sys._getframe().f_code.co_name)
            if storage_controller:
                storage_controller = "\n".join(storage_controller)
                output = output.format(storage_controller)
                output = output + "\n"
            platform = log_conf_obj.platform
            vmware_nic_info = None
            if platform == PLATFORM_VMWARE:
                vmware_nic_info = get_vmware_physical_nic_info()
                if vmware_nic_info:
                    output = output + "\nVMware NIC info:\n{}".format(vmware_nic_info)
            if storage_controller or vmware_nic_info:
                self._format_save("Collect hardware info", output, file=file)
        except Exception as e:
            self._logger.warning(f"Collect hardware info failed: {e}")

    def collect_sos_report(self):
        if not self.sos_collect:
            return
        self._logger.info("Start collecting sosreport asynchronously.")
        node_logger.info("Start collecting sosreport asynchronously.")
        if self.sos_plugin:
            cmd = "sos report --batch -o {} --tmp-dir {} --case-id {} --quiet -t 8 --compression-type gzip".format(
                self.sos_plugin,
                self._log_path,
                self._uuid,
            )
        else:
            cmd = "sos report --batch --tmp-dir {} --case-id {} --quiet -t 8 --compression-type gzip".format(
                self._log_path, self._uuid
            )
        executor = concurrent.futures.ThreadPoolExecutor()
        self.sos_future = executor.submit(self.execute_cmd, cmd, SOS_EXECUTE_TIMEOUT)  # sos report default timeout 300s
        executor.shutdown(wait=False)

    def collect_all(self):
        if self.sos_collect:
            self.collect_sos_report()
        if self.node_info_collect:
            self.collect_installed_rpm()
            self.collect_kernel_info()
            self.collect_services_config()
            self.collect_deploy_config()
            self.collect_disk_info()
            self.collect_nic_info()
            self.collect_services_status()
            self.collect_system_status()
            self.collect_zbs_info()
            self.collect_hardware_info()


@click.command()
@click.option("--uuid", default=str(uuid4()), help="Task uuid, auto generate new uuid if not.")
@click.option("--dst_path", default=DEFAULT_LOG_PATH, help="Dst path where to save collect node report.")
@click.option("--split_file", is_flag=True, help="Split report file.")
@click.option("--disk_check", is_flag=True, help="collect disk info.")
@click.option("--node_info_collect", is_flag=True, help="collect disk info.")
@click.option("--sos_collect", is_flag=True, help="collect sosreport.")
def collect_info(uuid, dst_path, split_file, disk_check, node_info_collect, sos_collect):
    """Collect node & cluter info."""
    collector = CollectInfo(uuid, dst_path, split_file, disk_check, node_info_collect, sos_collect)
    collector.collect_all()


if __name__ == "__main__":
    collect_info()
