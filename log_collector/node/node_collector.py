# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
import datetime
import os

from log_collector.config.constants import LOG_TEMP_PATH
from log_collector.config.logger import logger
from log_collector.node.node_info import CollectInfo
from log_collector.node.node_service_log import CollectNodeService
from log_collector.utils.metro_availability import get_witness_ip
from log_collector.utils.zbs_conf import ZbsConf

task_start_info = """
#########################################
# TASK: {task_name}
# TIME: {time}
#########################################
"""


class NodeCollector:
    def __init__(self, uuid, log_size, group_service_pattern=[], collect_type="cluster", include_disk=True):
        self.uuid = uuid.decode() if isinstance(uuid, bytes) else uuid
        self.log_size = log_size
        self.group_service_pattern = group_service_pattern
        self.ip = self._get_current_storage_ip()
        self.collect_type = collect_type
        self.include_disk = include_disk
        self.dir_name = "{}/{}_{}".format(LOG_TEMP_PATH, str(self.ip).replace(".", "_"), self.uuid)

    @staticmethod
    def _get_current_storage_ip():
        if ZbsConf.get_role() == "witness":
            current_ip = get_witness_ip()
        else:
            current_ip = ZbsConf.get_current_storage_ip()
        return current_ip

    def collect_log(self, start_time, end_time):
        print(task_start_info.format(task_name="COLLECT LOGS", time=str(datetime.datetime.now())))

        os.makedirs(self.dir_name)

        # service_config saved the service should collect list
        service_config = []
        # node_info_config saved the node_info collect config: node_info, sos_report
        node_info_config = {}
        for group in self.group_service_pattern:
            if group["group_name"] == "node_info":
                node_info_config = group
            else:
                service_config.append(group)

        node_info_collect = False
        sos_report_collect = False
        sos_plugin = None
        node_info_service_list = node_info_config.get("service_list", {})
        for item in node_info_service_list:
            name = item.get("name")
            if name == "node_info":
                node_info_collect = True
            if name == "sos_report":
                sos_report_collect = True
                sos_plugin = ",".join(item.get("plugin"))
        try:
            collect_node_info = CollectInfo(
                self.uuid,
                str(self.dir_name) + "/",
                disk_check=self.include_disk,
                node_info=node_info_collect,
                sos=sos_report_collect,
                sos_plugin=sos_plugin,
            )
            collect_node_info.collect_all()
            sos_future = collect_node_info.sos_future
        except Exception as e:
            sos_future = None
            print("Failed to collect node info: %s" % e)
            logger.exception("Failed to collect node info: %s" % e)
        try:
            node_service_log = CollectNodeService(
                self.ip,
                self.dir_name,
                self.log_size,
                service_config,
                self.uuid,
                self.collect_type,
                sos_future=sos_future,
            )
            node_service_log.collect_services_log(start_time, end_time)
        except Exception as e:
            print("Failed to collect node service log: %s" % e)
            logger.exception("Failed to collect node service log: %s" % e)
