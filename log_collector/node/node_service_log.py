# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
from collections import OrderedDict
from datetime import datetime
import glob
import os
import shutil
from subprocess import PIPE, Popen

import progressbar

from log_collector.config.constants import (
    CHECK_TIME_RANGE,
    COMPRESS_RANGE,
    COPY_CMD_RANGE,
    COPY_RANGE,
    LOG_LOG_PATH,
    LOG_TEMP_PATH,
    MAXIMUM_LOG_SIZE,
    MINIMUM_JOURNALD_AVAILABLE_SIZE,
    NODE_PROCESS_PREFIX,
    NODE_PROCESS_STR,
    PROCESS_STAGE_COLLECT_NODE,
    SOS_EXECUTE_TIMEOUT,
)
from log_collector.config.logger import node_logger
from log_collector.node.summary import Summary
from log_collector.utils.cmdline import run_shell_script_with_return_output
from log_collector.utils.file_time import File
from log_collector.utils.log_size import check_capacity_safe, local_log_threshold

task_end_info = """
collect task done
state: {task_state}
"""

log_position_info = """
collected log file: {log_file}
"""


class CollectNodeService:
    def __init__(self, local_ip, dir_name, log_size, log_config, task_uuid, collect_type="cluster", sos_future=None):
        self.ip = local_ip
        self.dir_name = dir_name
        self.progress_bar = None
        self.task_uuid = task_uuid
        self.collect_type = collect_type
        self.sos_future = sos_future
        self.log_size = log_size
        self.log_config = log_config
        self.progress_dict = {}

    @staticmethod
    def _extract_log_from_pattern(paths):
        log_list = []
        for path in paths:
            try:
                expanded_paths = glob.glob(path)
                for expanded_path in expanded_paths:
                    if os.path.isfile(expanded_path) and not os.path.islink(expanded_path):
                        log_list.append(expanded_path)
                    # when path contains dir, get its subdirs log files recursively
                    elif os.path.isdir(expanded_path):
                        for root, dirs, files in os.walk(expanded_path):
                            for file in files:
                                log_file = os.path.join(root, file)
                                if os.path.isfile(log_file) and not os.path.islink(log_file):
                                    log_list.append(log_file)
            except Exception as e:
                node_logger.exception("Failed extract {} error: {}".format(path, e))
        return log_list

    def collect_services_log(self, start_time, end_time):
        log_pattern_dict = self._get_log_pattern(self.log_config)
        cmd_log_dict = self._get_cmd_log_info(self.log_config)

        logs_checked = self._check_time(log_pattern_dict, start_time, end_time)

        saved_logs, cmd_saved_logs = self._copy_logs(logs_checked, start_time, end_time, cmd_log_dict)

        all_logs = []
        for file_logs in list(log_pattern_dict.values()):
            all_logs.extend(file_logs)
        all_logs.extend(cmd_saved_logs)
        summary = Summary(self.task_uuid, self.ip, all_logs, start_time, end_time)

        node_logger.info("start to write summary file")
        summary.write_summary(saved_logs)
        node_logger.info("write summary file completed")

        self._compress()

        self._write_process("success", 1)
        node_logger.info(
            log_position_info.format(
                log_file=LOG_LOG_PATH + "/" + str(self.ip).replace(".", "_") + "_" + self.task_uuid + ".tar.gz"
            )
        )
        node_logger.info(task_end_info.format(task_state="successful"))

    def execute_cmd_collect(self, cmd, save_path) -> bool:
        try:
            node_logger.info(f"collect command log: {cmd} to {save_path}")
            rc, output = run_shell_script_with_return_output(cmd)
            if rc != 0:
                node_logger.warning(f"{rc=} returned from {cmd=}")
            if not output:
                output = f"{rc=}: No output from {cmd=}"

            file_size = len(output)
            if not check_capacity_safe(file_size):
                node_logger.warning("system capacity is not enough, skip saving cmd log")
                return False
            with open(save_path, "w") as f:
                f.write(output)
            node_logger.info(f"save command log to {save_path} success")
            return True
        except Exception as e:
            node_logger.warning(f"Failed to execute command {cmd} for {save_path} error: {e}")
            return False

    def _get_log_pattern(self, log_config):
        log_pattern_list = OrderedDict()
        for group in log_config:
            for service in group["service_list"]:
                save_path = "/" + group["group_name"] + "/" + service["name"]
                log_pattern_list[save_path] = self._extract_log_from_pattern(service["path"])

        return log_pattern_list

    def _get_cmd_log_info(self, log_config):
        log_pattern_list = OrderedDict()
        for group in log_config:
            for service in group["service_list"]:
                save_path = "/" + group["group_name"] + "/" + service["name"]
                if "cmd" in service:
                    log_pattern_list[save_path] = service["cmd"]

        return log_pattern_list

    def _check_time(self, log_dict, start_time, end_time):
        node_logger.info("start to check logs time")
        checked_log_dict = OrderedDict()
        total_logs = sum([len(log_files) for log_files in list(log_dict.values())])
        count = 0
        # we collect modify_time in [start_time~end_time] period directly,
        # then to check modify_time > end_time and crt_time<end_time, if
        # satisfy this condition, we also collect it
        for save_path, log_file_list in list(log_dict.items()):
            checked_logs = []
            for log_file in log_file_list:
                try:
                    count += 1
                    self._write_process("_check_time", count * 1.0 / total_logs)
                    modify_time = os.path.getmtime(log_file)
                    if int(start_time) <= int(modify_time) <= int(end_time):
                        checked_logs.append(log_file)
                    if int(modify_time) > int(end_time):
                        file_time = File.get_create_time(log_file)
                        create_time = int(file_time) if file_time is not None else 0
                        if int(create_time) < int(end_time):
                            checked_logs.append(log_file)
                except OSError as e:
                    node_logger.exception("check file {} time error: {}".format(log_file, e))
            checked_log_dict[save_path] = checked_logs
        node_logger.info("check logs time completed")
        return checked_log_dict

    def _copy_cmd_logs(self, cmd_log_dict) -> list:
        node_logger.info("start to copy cmd log files")
        count = 0
        saved_logs = []

        total_logs = sum([len(cmd_list) for cmd_list in list(cmd_log_dict.values())])
        for save_name, cmd_list in cmd_log_dict.items():
            for cmd_info in cmd_list:
                count += 1
                service_name = save_name.split("/")[-1]
                identifier = cmd_info["identifier"]
                log_file = f"{service_name}_{identifier}.log"
                save_dir = self.dir_name + save_name
                if not os.path.isdir(save_dir):
                    os.makedirs(save_dir)
                save_path = save_dir + "/" + log_file
                self._write_process("copy_cmd_log", count * 1.0 / total_logs)
                if self.execute_cmd_collect(cmd_info["command"], save_path):
                    saved_logs.append(log_file)
        return saved_logs

    def _copy_path_logs(self, log_file_dict, start_time, end_time):
        node_logger.info("start to copy time checked log files")
        size_sum = 0
        saved_logs = []

        local_log_thres = local_log_threshold()

        total_logs = sum([len(log_list) for log_list in list(log_file_dict.values())])
        count = 0

        for save_name, log_list in list(log_file_dict.items()):
            node_logger.info("copy_log: start to copy %s" % log_list)
            log_dir = self.dir_name + save_name
            if not os.path.isdir(log_dir):
                os.makedirs(log_dir)
            for log_file in log_list:
                try:
                    count += 1
                    self._write_process("copy_log", count * 1.0 / total_logs)
                    file_size = os.path.getsize(log_file)
                    size_sum += file_size
                    if size_sum >= min(MAXIMUM_LOG_SIZE, self.log_size, local_log_thres) or not check_capacity_safe(
                        file_size
                    ):
                        node_logger.info("stop save log file because capacity usage reach 90% of total capacity")
                        return saved_logs

                    shutil.copy2(log_file, log_dir)
                    saved_logs.append(log_file)
                except OSError as er:
                    node_logger.exception("Failed to copy {}: {}".format(log_file, er))
                    continue

            if local_log_thres > MINIMUM_JOURNALD_AVAILABLE_SIZE:
                self._copy_journald_log(save_name, start_time, end_time)
        return saved_logs

    def _copy_logs(self, log_file_dict, start_time, end_time, cmd_log_dict) -> list:
        node_logger.info("start to copy logs")
        saved_logs = self._copy_path_logs(log_file_dict, start_time, end_time)
        cmd_saved_logs = self._copy_cmd_logs(cmd_log_dict)
        saved_logs.extend(cmd_saved_logs)
        return saved_logs, cmd_saved_logs

    def _clean_incomplete_sos_files(self, node_info_path):
        # Clean up incomplete sos report directories
        sos_pattern = os.path.join(node_info_path, "sos*")
        dirs = glob.glob(sos_pattern)
        for dir_path in dirs:
            try:
                if os.path.isdir(dir_path):
                    shutil.rmtree(dir_path)
                    node_logger.info(f"Removed incomplete sos report directory: {dir_path}")
            except Exception as e:
                node_logger.warning(f"Failed to remove incomplete sos report directory: {dir_path}, error: {e}")

    def _check_sos_done(self, future):
        node_info_path = self.dir_name + "/node_info_" + self.task_uuid + "/"
        try:
            rc, output = future.result(timeout=SOS_EXECUTE_TIMEOUT)
            if rc == 0 and self._check_sos_report(node_info_path):
                node_logger.info("Successfully collected sosreport.")
            else:
                node_logger.warning("Failed to collect sosreport or report file is missing.")
        except Exception as exc:
            node_logger.warning(f"sos report collection failed with an error: {exc}")
            self._clean_incomplete_sos_files(node_info_path)

    def _check_sos_report(self, node_info_path):
        # check if collected sos report compressed file exists
        pattern = os.path.join(node_info_path, f"sosreport*{self.task_uuid}*.tar.gz")
        node_logger.info(f"Checking if sos report exists: {pattern}")
        files = glob.glob(pattern)
        return len(files) > 0

    def _compress(self):
        try:
            if self.sos_future:
                node_logger.info("check sos done before compress file")
                self._check_sos_done(self.sos_future)

            node_logger.info("start to compress file")
            dest_ext = ".gz"
            arcname = os.path.basename(self.dir_name)
            dest_name = "{}.tar{}".format(arcname, dest_ext)
            dest_path = os.path.join(LOG_LOG_PATH, dest_name)

            directory_name = self.dir_name.split("/")[-1]
            cmd = "cd {}; tar czf {} ./{}".format(LOG_TEMP_PATH, dest_path, directory_name)
            proc = Popen(cmd, shell=True, close_fds=True, stdout=PIPE, stderr=PIPE)
            out, err = proc.communicate()
            if proc.returncode != 0:
                output = out + err
                node_logger.info("use tar command compress files error: {}".format(output))
                raise Exception("command compress files error")
            # self.dir_name has been compress,so delete it
            shutil.rmtree(self.dir_name)
            node_logger.info("compress file completed")
        except OSError as e:
            node_logger.exception("Failed to compress file: %s" % e)

    def _write_process(self, stage, progress):
        if self.collect_type != "cluster":
            if self.progress_bar is None:
                self.progress_bar = progressbar.ProgressBar(max_value=1).start()

        difference = 0.1

        if progress < 0 or progress > 1:
            return

        if stage not in self.progress_dict:
            self.progress_dict[stage] = progress
        else:
            if abs(progress - self.progress_dict[stage]) < difference:
                return

        l_range = 0
        r_range = 0

        if stage == "_check_time":
            l_range = CHECK_TIME_RANGE[0]
            r_range = CHECK_TIME_RANGE[1]
        elif stage == "copy_log":
            l_range = COPY_RANGE[0]
            r_range = COPY_RANGE[1]
        elif stage == "copy_cmd_log":
            l_range = COPY_CMD_RANGE[0]
            r_range = COPY_CMD_RANGE[1]
        elif stage == "compress":
            l_range = COMPRESS_RANGE[0]
            r_range = COMPRESS_RANGE[1]
        elif stage == "success":
            l_range = 0
            r_range = 1
            progress = 1
            if self.collect_type != "cluster":
                self.progress_bar.update(1)
                self.progress_bar.finish()
            else:
                print(NODE_PROCESS_PREFIX + "#SUCCESSFUL")
        current_process = l_range + (r_range - l_range) * progress
        if self.collect_type != "cluster":
            node_logger.info("write process: {}: {}".format(stage, current_process))
            self.progress_bar.update(current_process)
        else:
            print(NODE_PROCESS_STR % (PROCESS_STAGE_COLLECT_NODE, self.ip, current_process))

        self.progress_dict[stage] = current_process

    def _copy_journald_log(self, save_name, start_time, end_time):
        start_time_str = datetime.fromtimestamp(start_time).strftime("%Y-%m-%d %H:%M:%S")
        end_time_str = datetime.fromtimestamp(end_time).strftime("%Y-%m-%d %H:%M:%S")
        service_name = save_name.split("/")[-1]
        service_dir = self.dir_name + save_name
        journal_log_file = "{}/{}.journald.log".format(service_dir, service_name)
        if service_name == "dmesg":
            cmd = "journalctl -k -S '{}' -U '{}' >> {}".format(start_time_str, end_time_str, journal_log_file)

        else:
            cmd = "journalctl -u {} -S '{}' -U '{}' >> {}".format(
                service_name, start_time_str, end_time_str, journal_log_file
            )
        node_logger.info("journald cmd: {}".format(cmd))
        process = Popen(cmd, shell=True)
        process.wait()
        if os.path.exists(journal_log_file):
            with open(journal_log_file) as f:
                output = f.readline().strip()
            if not output or "No entries" in output:
                os.remove(journal_log_file)
        else:
            node_logger.warning("copy {} log from journald failed".format(service_name))

        if len(os.listdir(service_dir)) == 0:
            os.rmdir(service_dir)
