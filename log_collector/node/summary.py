# Copyright (c) 2013-2018, SMARTX
# All rights reserved.

import os
import socket
import time

from log_collector.config.constants import LOG_TEMP_PATH


class Summary:
    SUMMARY = """
-------------------------------------------------------------------

task uuid: {task_uuid}
host name: {host_name}
ip: {ip}
start time: {start_time}
end time: {end_time}
logs sum: {logs_sum}
saved logs count: {exported_count}
not saved logs count: {unexported_count}



**************** SAVED LOGS: ****************
{exported_logs}



**************** NOT SAVED LOGS: ****************
Warning: The following logs are not saved cause they are out of the selected collect date range.
{unexported_logs}



**************** ALL SCAN LOGS: ****************
{all_logs}

"""

    def __init__(self, task_uuid, ip, logs, start_time, end_time):
        self.task_uuid = task_uuid
        self.ip = ip
        self.logs = logs
        self.start_time = start_time
        self.end_time = end_time

    @staticmethod
    def _get_hostname():
        """Return current node's name"""
        if not os.path.exists("/etc/hostname"):
            return socket.gethostname()
        with open("/etc/hostname") as f:
            return f.read().strip() or socket.gethostname()

    def write_summary(self, exported):
        extract_logs = self.logs

        un_exported = [x for x in extract_logs if x not in exported]

        exported_logs_str = ""
        un_exported_logs_str = ""
        for x in exported:
            exported_logs_str += str(x) + "\n"
        for y in un_exported:
            un_exported_logs_str += str(y) + "\n"

        summary = {
            "task_uuid": self.task_uuid,
            "host_name": self._get_hostname(),
            "ip": self.ip,
            "logs_sum": len(extract_logs),
            "exported_count": len(exported),
            "unexported_count": len(un_exported),
            "exported_logs": exported_logs_str,
            "unexported_logs": un_exported_logs_str,
            "all_logs": "\n".join(extract_logs),
            "start_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(self.start_time)),
            "end_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(self.end_time)),
        }
        with open(
            "{}/{}_{}/summary_{}_{}".format(
                LOG_TEMP_PATH,
                str(self.ip).replace(".", "_"),
                self.task_uuid,
                str(self.ip).replace(".", "_"),
                self.task_uuid,
            ),
            "w",
        ) as f:
            f.write(Summary.SUMMARY.format(**summary))

        with open("{}/summary_{}_{}".format(LOG_TEMP_PATH, str(self.ip).replace(".", "_"), self.task_uuid), "w") as f:
            f.write(Summary.SUMMARY.format(**summary))
