{"log_config": [{"group_name": "kernel_logs", "service_list": [{"name": "messages", "path": ["/var/log/messages*"]}, {"name": "audit", "path": ["/var/log/audit/*"]}, {"name": "dmesg", "path": ["/var/log/dmesg*"]}, {"name": "hugepage-manager", "path": ["/var/log/hugepage-manager*"]}]}, {"group_name": "zbs_logs", "service_list": [{"name": "zbs-metad", "path": ["/var/log/zbs/zbs-meta*"]}, {"name": "zbs-chunkd", "path": ["/var/log/zbs/zbs-chunk*", "/var/log/zbs/zbs-lsm2db*"]}, {"name": "zbs-taskd", "path": ["/var/log/zbs/zbs-taskd*"]}, {"name": "zbs-inspectord", "path": ["/var/log/zbs/zbs-inspector*"]}, {"name": "zbs-aurorad", "path": ["/var/log/zbs/zbs-aurorad*"]}, {"name": "zbs-aurora-monitord", "path": ["/var/log/zbs/zbs-aurora-monitor*"]}, {"name": "zbs-scavenger", "path": ["/var/log/zbs/zbs-scavenger*"]}, {"name": "zbs-lsm-tool", "path": ["/var/log/zbs/zbs-lsm-tool*"]}, {"name": "zbs-iscsi-redirectord", "path": ["/var/log/zbs/zbs-iscsi-redirectord*"]}, {"name": "procurator", "path": ["/var/log/zbs/procurator/procurator/*", "/var/log/zbs/procurator/upgrade/*"]}, {"name": "procurator-qemu", "path": ["/var/log/zbs/procurator/qemu/*"]}, {"name": "l2ping@storage", "path": ["/var/log/zbs/netbouncer/l2ping@storage*"]}, {"name": "l2ping@access", "path": ["/var/log/zbs/netbouncer/l2ping@access*"]}, {"name": "netguard", "path": ["/var/log/zbs/netbouncer/netguard*"]}, {"name": "zbs-watchdogd", "path": [], "cmd": [{"command": "curl 127.0.0.1:10300/api/v1/watchdog/log", "identifier": "log"}, {"command": "curl 127.0.0.1:10300/api/v1/watchdog/log_warn", "identifier": "log_warn"}, {"command": "curl 127.0.0.1:10300/api/v1/watchdog/log_error", "identifier": "log_error"}]}], "supported_platform": ["san", "kvm", "vmware", "xense<PERSON>r"]}, {"group_name": "base_component_logs", "service_list": [{"name": "zookeeper", "path": ["/var/log/zookeeper/*"]}, {"name": "mongodb", "path": ["/var/log/mongodb/*"]}, {"name": "job-center-worker", "path": ["/var/log/zbs/job-center-worker*", "/var/log/zbs/archive/job-center-worker*", "/var/log/zbs/operate-disk*", "/var/log/zbs/restart_job_center_if_hang*"]}, {"name": "job-center-scheduler", "path": ["/var/log/zbs/job-center-scheduler*"]}, {"name": "openvswitch", "path": ["/var/log/openvswitch/*"]}, {"name": "vipservice", "path": ["/var/log/zbs/vipservice/*"]}, {"name": "network-firewall", "path": ["/var/log/zbs/network-firewall*"]}, {"name": "net-health-check", "path": ["/var/log/zbs/net-health-check*"]}, {"name": "bash", "path": ["/var/log/bash*"]}, {"name": "cron", "path": ["/var/log/cron*", "/var/log/zbs/service_resource_check*", "/var/log/zbs/generate_raid_check_crontable*", "/var/log/zbs/witness_sync_cluster_ips*"]}, {"name": "nginx", "path": ["/var/log/nginx/*"]}, {"name": "dhcp", "path": ["/var/log/zbs/dhcp*"]}, {"name": "yum", "path": ["/var/log/yum.log"]}, {"name": "redis", "path": ["/var/log/zbs/redis*"]}, {"name": "chrony", "path": ["/var/log/chrony/*"]}, {"name": "netreactor", "path": ["/var/log/zbs/netbouncer/netreactor*"]}, {"name": "containerd", "path": []}]}, {"group_name": "tuna_logs", "service_list": [{"name": "disk-healthd", "path": ["/var/log/zbs/disk-healthd/*"], "cmd": [{"command": "/usr/Arcconf/arcconf GETCONFIG 1 nologs", "identifier": "arcconf.getconfig"}, {"command": "/usr/Arcconf/arcconf GETEXCEPTION 1 nologs", "identifier": "arcconf.getexception"}, {"command": "/usr/Arcconf/arcconf GETLOGS 1 EVENT tabular nologs noprompt", "identifier": "arcconf.getlogs_event"}, {"command": "/usr/Arcconf/arcconf GETSMARTSTATS 1 tabular nologs", "identifier": "arcconf.getsmartstats"}, {"command": "/usr/Arcconf/arcconf GETVERSION nologs", "identifier": "arcconf.getversion"}, {"command": "/usr/Arcconf/arcconf LIST nologs", "identifier": "arcconf.list"}, {"command": "mvcli info -o hba", "identifier": "mvcli.info_hba"}, {"command": "mvcli info -o vd", "identifier": "mvcli.info_vd"}, {"command": "mvcli info -o pd", "identifier": "mvcli.info_pd"}, {"command": "mvcli smart -p 0", "identifier": "mvcli.smart_p0"}, {"command": "mvcli smart -p 1", "identifier": "mvcli.smart_p1"}, {"command": "/opt/marvell/mnvcli/dell/mnv_cli info -o hba", "identifier": "mnvcli.dell.info_hba"}, {"command": "/opt/marvell/mnvcli/dell/mnv_cli info -o vd", "identifier": "mnvcli.dell.info_vd"}, {"command": "/opt/marvell/mnvcli/dell/mnv_cli info -o pd", "identifier": "mnvcli.dell.info_pd"}, {"command": "/opt/marvell/mnvcli/dell/mnv_cli smart -i 0", "identifier": "mnvcli.dell.smart_p0"}, {"command": "/opt/marvell/mnvcli/dell/mnv_cli smart -i 1", "identifier": "mnvcli.dell.smart_p1"}, {"command": "/opt/marvell/mnvcli/dell/mnv_cli event -c 0", "identifier": "mnvcli.dell.event"}, {"command": "/opt/marvell/mnvcli/lenovo/mnv_cli info -o hba", "identifier": "mnvcli.lenovo.info_hba"}, {"command": "/opt/marvell/mnvcli/lenovo/mnv_cli info -o vd", "identifier": "mnvcli.lenovo.info_vd"}, {"command": "/opt/marvell/mnvcli/lenovo/mnv_cli info -o pd", "identifier": "mnvcli.lenovo.info_pd"}, {"command": "/opt/marvell/mnvcli/lenovo/mnv_cli smart -i 0", "identifier": "mnvcli.lenovo.smart_p0"}, {"command": "/opt/marvell/mnvcli/lenovo/mnv_cli smart -i 1", "identifier": "mnvcli.lenovo.smart_p1"}, {"command": "/opt/marvell/mnvcli/lenovo/mnv_cli event -c 0", "identifier": "mnvcli.lenovo.event"}]}, {"name": "usbredir-manager", "path": ["/var/log/zbs/usbredir-manager/*"]}, {"name": "cluster-upgrader", "path": ["/var/log/zbs/*upgrade*", "/var/log/zbs/archive/*upgrade*"]}, {"name": "tuna-exporter", "path": ["/var/log/zbs/tuna-exporter*", "/var/log/zbs/archive/tuna-exporter*"]}, {"name": "svcresctld", "path": ["/var/log/zbs/svcresctl/*"]}, {"name": "ntpm", "path": ["/var/log/zbs/ntpm/*"]}, {"name": "network-monitor", "path": ["/var/log/zbs/network-monitor*", "/var/log/zbs/archive/network-monitor*", "/var/log/zbs/network-high-latencies*", "/var/log/zbs/archive/network-high-latencies*"]}, {"name": "zbs-rest", "path": ["/var/log/zbs/zbs-rest*", "/var/log/zbs/archive/zbs-rest*", "/var/log/zbs/zbs-zk-client*"]}, {"name": "tuna-rest", "path": ["/var/log/zbs/tuna-rest*", "/var/log/zbs/archive/tuna-rest*"]}, {"name": "zbs-deploy", "path": ["/var/log/zbs-deploy*", "/var/log/zbs/deploy-manage*"]}, {"name": "log-collector", "path": ["/var/log/zbs/log-collector*", "/var/log/zbs/archive/log-collector*"]}, {"name": "tuna-cmd", "path": ["/var/log/zbs/tuna_cmd*", "/var/log/zbs/cmd_zbs_node*", "/var/log/zbs/cmd_zbs_cluster*"]}, {"name": "inspector", "path": ["/var/log/inspector*", "/var/log/inspector-agent/*", "/var/log/zbs/turbot*"]}, {"name": "sd-offline", "path": [], "cmd": [{"command": "sdm -l", "identifier": "log"}]}, {"name": "master-monitor", "path": ["/var/log/zbs/master-monitor*"]}, {"name": "tuna-init-cgconfig", "path": []}, {"name": "tuna-config-hugepage", "path": ["/var/log/zbs/tuna-config-hugepage*"]}, {"name": "fisheye-pre-boot", "path": ["/var/log/zbs/fisheye-pre-boot*"]}, {"name": "fisheye-os-shutdown", "path": []}, {"name": "tuna-umount-nfs-export", "path": []}]}, {"group_name": "elf_logs", "service_list": [{"name": "elf-vm-monitor", "path": ["/var/log/zbs/elf-vm-monitor*"]}, {"name": "elf-vm-watchdog", "path": ["/var/log/zbs/elf-vm-watchdog*"]}, {"name": "elf-vm-scheduler", "path": ["/var/log/zbs/elf-vm-scheduler*"]}, {"name": "vmtools-agent", "path": ["/var/log/zbs/vmtools_agent*"]}, {"name": "elf-exporter", "path": ["/var/log/zbs/elf-exporter*", "/var/log/zbs/archive/elf-exporter*"]}, {"name": "libvirtd", "path": ["/var/log/zbs/libvirtd*", "/var/log/zbs/archive/libvirtd*"]}, {"name": "qemu", "path": ["/var/log/libvirt/qemu/*"]}, {"name": "vnc-proxy", "path": ["/var/log/zbs/vnc-proxy/*"]}, {"name": "goose", "path": ["/var/log/zbs/goose*"]}, {"name": "elf-fs", "path": ["/var/log/zbs/elf-fs*"]}, {"name": "elf-rest", "path": ["/var/log/zbs/elf-rest*", "/var/log/zbs/archive/elf-rest*"]}, {"name": "vm-security-controller", "path": ["/var/log/zbs/vsc*"]}]}, {"group_name": "octopus_logs", "service_list": [{"name": "octopus", "path": ["/var/log/zbs/octopus/*"]}, {"name": "prometheus", "path": ["/var/log/zbs/prometheus/prometheus*"]}, {"name": "dolphin", "path": ["/var/log/zbs/dolphin/*"]}, {"name": "siren", "path": ["/var/log/zbs/siren/*"]}, {"name": "crab", "path": ["/var/log/zbs/crab/*"]}, {"name": "consul", "path": ["/var/log/zbs/consul/*"]}, {"name": "consul-server", "path": ["/var/log/zbs/consul-server/*"]}, {"name": "harbor", "path": ["/var/log/zbs/harbor/*"]}, {"name": "aquarium", "path": ["/var/log/zbs/aquarium/*"]}, {"name": "envoy", "path": ["/var/log/zbs/envoy/*"]}, {"name": "envoy-xds", "path": ["/var/log/zbs/envoy-xds/*"]}, {"name": "oscar", "path": ["/var/log/zbs/oscar/*"]}, {"name": "seal", "path": ["/var/log/zbs/seal/*"]}, {"name": "fluent-bit", "path": ["/var/log/zbs/fluent-bit/fluent-bit*"]}, {"name": "node-exporter", "path": ["/var/log/zbs/node-exporter/node-exporter*"]}]}, {"group_name": "coredump_logs", "service_list": [{"name": "coredump_logs", "path": ["/var/crash/*"]}]}, {"group_name": "timemachine_logs", "service_list": [{"name": "timemachine", "path": ["/var/log/zbs/timemachine*"]}]}, {"group_name": "node_info", "service_list": [{"name": "node_info", "path": []}, {"name": "sos_report", "path": [], "plugin": ["abrt", "ansible", "auditd", "cgroups", "cgroups", "containerd", "containerd", "cron", "dbus", "d<PERSON><PERSON><PERSON>", "dracut", "dracut", "filesys", "grub2", "hardware", "host", "hts", "ipmitool", "iscsi", "kdump", "kvm", "libvirt", "login", "logrotate", "lvm2", "md", "megacli", "networking", "nginx", "sar", "openvswitch", "pci", "perccli", "process", "processor", "sas3ircu", "services", "snmp", "storcli", "sudo", "system", "systemd", "udev", "virsh", "usb", "vmware"]}]}]}