# Copyright (c) 2013-2018, SMARTX
# All rights reserved.


import logging
from logging.handlers import RotatingFileHandler

from log_collector.config.constants import APP_NAME, LOG_FORMAT, NODE_LOG_NAME, NODE_LOG_PATH, TOOL_LOG_PATH


def get_logger():
    file_handler = logging.FileHandler(TOOL_LOG_PATH)
    formatter = logging.Formatter(LOG_FORMAT)
    file_handler.setFormatter(formatter)

    app_logger = logging.getLogger(APP_NAME)
    app_logger.setLevel(logging.INFO)
    app_logger.addHandler(file_handler)

    return app_logger


def setup_node_logger():
    formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    # add a rotating handler
    rh = RotatingFileHandler(NODE_LOG_PATH, maxBytes=1024 * 1024 * 10, backupCount=2)
    rh.setLevel(logging.INFO)
    rh.setFormatter(formatter)

    node_app_logger = logging.getLogger(NODE_LOG_NAME)
    node_app_logger.setLevel(logging.INFO)
    node_app_logger.addHandler(rh)
    return node_app_logger


logger = get_logger()
node_logger = setup_node_logger()
