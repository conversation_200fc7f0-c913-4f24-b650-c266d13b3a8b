# Copyright (c) 2013-2023, SMARTX
# All rights reserved.


APP_NAME = "log_collector"
NODE_LOG_NAME = "node_collector"
LOG_CONFIG_PATH = "/etc/log_collector/log_config.json"
TOOL_LOG_PATH = "/var/log/zbs/log-collector.INFO"
NODE_LOG_PATH = "/var/log/zbs/node_log_collector.log"
LOG_TEMP_PATH = "/usr/share/tuna/log_collector/tmp"
LOG_LOG_PATH = "/usr/share/tuna/log_collector/logs"
LOG_METADATA_PATH = "/usr/share/tuna/log_collector/metadata"
SHELL_PATH = "/usr/share/tuna/log_collector/shell"
ZBS_CONF_FILE = "/etc/zbs/zbs.conf"
INVENTORY_FILE = "/etc/log_collector/data/inventory"
COLLECT_CLUSTER_LOG_PLAYBOOK = "/etc/log_collector/data/collect_cluster_logs.yaml"
CMD_COLLECT_CLUSTER_LOG_PLAYBOOK = "/etc/log_collector/data/cmd_collect_cluster_logs.yaml"
CLEAN_CLUSTER_LOG_PLAYBOOK = "/etc/log_collector/data/clean_cluster_logs.yaml"
INVENTORY_IP_ATTACHMENT = " ansible_ssh_user=admin ansible_ssh_private_key_file=/home/<USER>/.ssh/admin_id_rsa"
MAXIMUM_LOG_SIZE = 20 * 1024 * 1024 * 1024
MINIMUM_JOURNALD_AVAILABLE_SIZE = 5 * 1024 * 1024 * 1024
LOG_COMPRESS_RATE = 10
LAUNCH_PORT = 10406
KILL_TASK_PLAYBOOK = "/etc/log_collector/data/kill_task.yaml"
KILL_TASK_SYMBOL = "LOG_COLLECTOR_KILL_SYMBOL"
LOG_FORMAT = "%(asctime)s, %(levelname)s, %(filename)s:%(lineno)d, %(message)s"
NODE_PROCESS_PREFIX = "LOG_COLLECTOR_PROGRESS"
NODE_INFO_PREFIX = "node_info - INFO -"
NODE_PROCESS_STR = "#" + NODE_PROCESS_PREFIX + "#stage=%s#host_ip=%s#progress=%s#"
PROCESS_STAGE_COLLECT_NODE = "COLLECT_NODE"
PROCESS_STAGE_COLLECT_CONTROLLER = "COLLECT_CONTROLLER"
PROCESS_STAGE_COLLECT_SLAVE = "COLLECT_SLAVE"
CHECK_TIME_RANGE = (0.3, 0.65)
NODE_INFO_RANGE = (0, 0.3)
COPY_RANGE = (0.65, 0.90)
COPY_CMD_RANGE = (0.90, 0.95)
COMPRESS_RANGE = (0.95, 1)
CLUSTER_ZIP_LOG_PREFIX = "cluster-logs"
HOST_METRO_AVAILABILITY_PATH = "/etc/zbs/metro_availability"
HOST_WITNESS_PATH = "/etc/zbs/witness"
EXECUTE_TIMOUT = 60  # 60s
TERMINATE_WAIT_TIMEOUT = 10  # 10s
SOS_EXECUTE_TIMEOUT = 600  # 600s
ZBS_CONFIG_FILE = "/etc/zbs/zbs.conf"
PROCESS_DISK_STATE_CHECK_CMD = "ps axo stat,cmd | egrep 'lsblk|blkid|blockdev|smartctl|parted|fdisk'"
DEFAULT_ANSIBLE_PATH = "/usr/local/venv/ansible/bin/ansible-playbook"
PYTHON_PATH = "/usr/local/venv/tuna/bin/python"
HOST_PLATFORM_PATH = "/etc/zbs/platform"
PLATFORM_KVM = "kvm"
PLATFORM_ELF = "elf"
PLATFORM_VMWARE = "vmware"
