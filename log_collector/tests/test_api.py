# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
import json
import time
import uuid

from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch
import pytest

from pyfakefs import fake_filesystem

from log_collector.utils import collect_progress


zbs_conf = """[network]
data_ip=***********
heartbeat_ip=***********
vm_ip=**************
web_ip=**************

[cluster]
role=master
members=***********,***********,***********
zookeeper=***********:2181,***********:2181,***********:2181
mongo=***********:27017,***********:27017,***********:27017
cluster_mgt_ips = **************,**************,**************,**************
cluster_storage_ips = ***********,***********,***********,***********
"""


fs = fake_filesystem.FakeFilesystem()
fs.create_file(
    "/usr/share/tuna/log_collector/metadata/1-0-0-1_test_uuid",
    contents=json.dumps({"status": "running", "task_uuid": "test123"}),
)
fs.create_file("/usr/share/tuna/log_collector/tmp/tmp_process_" + str(uuid.uuid4()), contents="{}")
fs.create_file("/etc/zbs/zbs.conf", contents=zbs_conf)
fs.create_file("/var/log/zbs/log-collector.INFO")

file_open = fake_filesystem.FakeFileOpen(fs)

with patch("tuna.server.common.utils.get_user_by_token"), patch("os.listdir") as mock_listdir, patch(
    "builtins.open", file_open
):
    mock_listdir.return_value = ["1-0-0-1_test_uuid"]

    from log_collector.rest.app import app as flask_app


class MockClusterInotify:
    @property
    def data(self):
        return {
            "cluster_hosts": {"mgt": ["*******", "*******"]},
            "local_ip": {"storage": "*******", "mgt": "*******"},
        }

    def cluster_up_to_date(self):
        return None


class MockTask:
    def isAlive(self):
        return True

    @property
    def collect_status(self):
        return "successful"

    def start(self):
        return None


@pytest.fixture
def client():
    return flask_app.test_client()


def test_post_log_collect(client):
    with patch("os.listdir") as mock_listdir, patch("builtins.open", file_open):
        mock_listdir.return_value = ["1-0-0-1_test_uuid"]

        from log_collector.cmd import cluster_logs
        from log_collector.rest import api

        with patch.object(collect_progress, "open", file_open) as mock_open, patch(
            "log_collector.cluster.metadata.MetaData.init_metadata"
        ) as mock_init_meta, patch.object(cluster_logs.CollectTask, "start") as mock_start, patch.object(
            cluster_logs, "CollectTask"
        ) as mock_task, patch.object(
            api, "CollectLogEventWrapper"
        ) as mock_event:
            api.cluster_inotify.cluster_hosts = {"mgt": ["*******", "*******"]}
            api.cluster_inotify.local_ip = {"storage": "*******", "mgt": "*******"}
            mock_task.return_value = MockTask()
            mock_open.return_value = None
            mock_init_meta.return_value = None
            mock_start.return_value = None
            mock_event.return_value = Mock()
            mock_request_data = {"hosts": ["*******"], "start_timestamp": 1621210916, "end_timestamp": 1621225316}
            client.post("/api/v2/logs", data=json.dumps(mock_request_data))


def test_collect_when_has_running_job(client):
    with patch("os.listdir") as mock_listdir, patch("builtins.open", file_open):
        mock_listdir.return_value = ["1-0-0-1_test_uuid"]

        from log_collector.cmd import cluster_logs
        from log_collector.rest import api

        with patch.object(collect_progress, "open", file_open) as mock_open, patch(
            "log_collector.cluster.metadata.MetaData.init_metadata"
        ) as mock_init_meta, patch.object(cluster_logs.CollectTask, "start") as mock_start, patch.object(
            api, "CollectLogEventWrapper"
        ) as mock_event:
            api.cluster_inotify.cluster_hosts = {"mgt": ["*******", "*******"]}
            api.cluster_inotify.local_ip = {"storage": "*******", "mgt": "*******"}
            mock_open.return_value = None
            mock_init_meta.return_value = None
            mock_start.return_value = None
            mock_event.return_value = Mock()
            mock_request_data = {"hosts": ["*******"], "start_timestamp": 1621210916, "end_timestamp": 1621225316}
            client.post("/api/v2/logs", data=json.dumps(mock_request_data))
            time.sleep(1)
            client.post("/api/v2/logs", data=json.dumps(mock_request_data))


def test_get_cluster_log_config(client):
    with patch("os.listdir") as mock_listdir:
        mock_listdir.return_value = ["1-0-0-1_test_uuid"]

        from log_collector.rest import api

        with patch.object(api, "ClusterInotify", return_value=None):
            r = client.get("/api/v2/logs/log_config")

            assert r.status_code == 200


def test_get_metro_cluster_logs(client):
    with patch("os.listdir") as mock_listdir, patch("builtins.open", file_open):
        mock_listdir.return_value = ["1-0-0-1_test_uuid"]

        from log_collector.rest import api

        with patch.object(collect_progress, "open", file_open) as mock_open, patch(
            "log_collector.cluster.metadata.MetaData.init_metadata"
        ) as mock_init_meta, patch.object(api.requests, "get") as mock_get:
            mock_open.return_value = None
            mock_init_meta.return_value = None
            api.cluster_inotify.cluster_hosts = {
                "mgt": ["*********", "*********"],
                "storage": ["*******", "*******"],
                "witness": ["*******"],
            }
            api.cluster_inotify.local_ip = {"mgt": "*********", "storage": "1*******"}
            mock_get.return_value.status_code.return_value = 200
            mock_get.return_value.json.return_value = {"data": [{"task_uuid": "uuid-test-1234"}]}
            r = client.get("/api/v2/logs")
            assert r.status_code == 200


def test_get_cluster_logs(client):
    with patch("os.listdir") as mock_listdir, patch("builtins.open", file_open):
        mock_listdir.return_value = ["1-0-0-1_test_uuid"]

        from log_collector.rest import api

        with patch.object(collect_progress, "open", file_open) as mock_open, patch(
            "log_collector.cluster.metadata.MetaData.init_metadata"
        ) as mock_init_meta, patch.object(api.requests, "get") as mock_get:
            mock_open.return_value = None
            mock_init_meta.return_value = None
            api.cluster_inotify.cluster_hosts = {
                "mgt": ["*********", "*********"],
                "storage": ["*******", "*******"],
            }
            api.cluster_inotify.local_ip = {"mgt": "*********", "storage": "*******"}
            mock_get.return_value.status_code.return_value = 200
            mock_get.return_value.json.return_value = {"data": [{"task_uuid": "uuid-test-1234"}]}
            r = client.get("/api/v2/logs")
            assert r.status_code == 200


def test_get_current_node_log(client):
    with patch("os.listdir") as mock_listdir, patch("builtins.open", file_open):
        mock_listdir.return_value = ["1-0-0-1_test_uuid"]

        from log_collector.rest import api

        with patch.object(collect_progress, "open", file_open) as mock_open, patch(
            "log_collector.cluster.metadata.MetaData.init_metadata"
        ) as mock_init_meta:
            mock_open.return_value = None
            mock_init_meta.return_value = None
            api.cluster_inotify.cluster_hosts = {
                "mgt": ["*********", "*********"],
                "storage": ["*******", "*******"],
            }
            api.cluster_inotify.local_ip = {"mgt": "*********", "storage": "*******"}
            r = client.get("/api/v2/logs/node/get_current_log")
            assert r.status_code == 200


def test_get_log_status(client):
    with patch("os.listdir") as mock_listdir:
        mock_listdir.return_value = ["1-0-0-1_test_uuid"]

        from common.config.constant import AUTH_TOKEN_NAME, AUTH_TOKEN_NAME_NEW
        from log_collector.rest import api

        with flask_app.test_request_context():
            with patch.object(api, "request") as mock_request:
                mock_request.headers = {AUTH_TOKEN_NAME_NEW: "test_token"}
                mock_request.method = "GET"

                with patch.object(collect_progress, "open", file_open) as mock_open, patch(
                    "log_collector.cluster.metadata.MetaData.init_metadata"
                ) as mock_init_meta, patch.object(api.requests, "get") as mock_get:
                    mock_open.return_value = None
                    mock_init_meta.return_value = None
                    api.cluster_inotify.cluster_hosts = {
                        "mgt": ["*********", "*********"],
                        "storage": ["*******", "*******"],
                    }
                    api.cluster_inotify.local_ip = {"mgt": "*********", "storage": "*******"}
                    mock_uuid = "uuid-test-1234"

                    mock_get.return_value.json.return_value = {"data": [{"task_uuid": "uuid-test-1234"}]}
                    r = client.get("/api/v2/logs/{}".format(mock_uuid))
                    assert r.status_code == 200


def test_old_get_logs_api_call(client):
    with patch("os.listdir") as mock_listdir, patch("builtins.open", file_open):
        mock_listdir.return_value = ["1-0-0-1_test_uuid"]

        from log_collector.rest import api

        with patch.object(collect_progress, "open", file_open) as mock_open, patch(
            "log_collector.cluster.metadata.MetaData.init_metadata"
        ) as mock_init_meta:
            mock_open.return_value = None
            mock_init_meta.return_value = None
            api.cluster_inotify.cluster_hosts = {
                "mgt": ["*********", "*********"],
                "storage": ["*******", "*******"],
            }
            api.cluster_inotify.local_ip = {"mgt": "*********", "storage": "*******"}
            r = client.get("/api/v2/logs?current_node_only=true")
            assert r.status_code == 200


def test_cluster_download(client):
    with patch("os.listdir") as mock_listdir, patch("builtins.open", file_open):
        mock_listdir.return_value = ["1-0-0-1_test_uuid"]

        from log_collector.rest import api
        with flask_app.test_request_context():
            with patch.object(collect_progress, "open", file_open) as mock_open, patch(
                "log_collector.cluster.metadata.MetaData.init_metadata"
            ) as mock_init_meta, patch.object(api, "request") as mock_request, patch.object(
                api.requests, "get") as mock_get, patch.object(
                api, "Response"
            ) as mock_response:
                mock_open.return_value = None
                mock_init_meta.return_value = {"metadata": "test"}
                api.cluster_inotify.cluster_hosts = {
                    "mgt": ["*********", "*********"],
                    "storage": ["*******", "*******"],
                }
                api.cluster_inotify.local_ip = {"mgt": "*********", "storage": "*******"}
                mock_request.args = {"log_owner": "*********"}
                response = mock_get.return_value = Mock()
                mock_uuid = "uuid-test-1234"
                response.json.return_value = {
                    "data": {
                        "task_uuid": mock_uuid,
                        "status": "successful",
                        "node_list": ["*********"],
                        "path": "/tmp/uuid-test-1234.tar.gz",
                    }
                }
                response.status_code = 200
                response.url = "http://*********/api/v2/logs/{}/cluster_download".format(mock_uuid)
                response.headers = {"content-length": 1000}
                response.raw.decode_content = True
                mock_response = Mock(return_value={})
                r = client.get("/api/v2/logs/{}/cluster_download".format(mock_uuid))
                assert r.status_code == 200


def test_node_download(client):
    with patch("os.listdir") as mock_dir, patch("builtins.open", file_open):
        mock_dir.return_value = ["1-0-0-1_test_uuid"]

        from common.config.constant import AUTH_TOKEN_NAME_NEW
        from log_collector.rest import api

        with patch.object(collect_progress, "open", file_open) as mock_open, patch(
            "log_collector.cluster.metadata.MetaData.init_metadata"
        ) as mock_init_meta, patch.object(api, "Response") as mock_response:
            mock_open.return_value = None
            mock_init_meta.return_value = None
            mock_response = Mock()
            mock_response.headers = {AUTH_TOKEN_NAME_NEW: "test_token"}
            api.cluster_inotify.cluster_hosts = {
                "mgt": ["*********", "*********"],
                "storage": ["*******", "*******"],
            }
            api.cluster_inotify.local_ip = {"mgt": "*********", "storage": "*******"}
            client.get("/api/v2/logs/test_uuid/node_download")


def test_tail_read():
    with patch("os.listdir") as mock_dir, patch("builtins.open", file_open):
        mock_dir.return_value = ["1-0-0-1_test_uuid"]

        from log_collector.rest import api

        with patch.object(api.subprocess, "Popen") as mock_popen:
            mock_process = MagicMock()
            mock_popen.return_value = mock_process
            mock_process.communicate.return_value = (b"line1\nline2\nline3\nline4\nline5", b"")
            mock_process.returncode = 0

            result = api.tail_read("dummy_file.log", 5)

            assert result == ['line1', 'line2', 'line3', 'line4', 'line5']


def test_get_local_progress():
    with patch("os.listdir") as mock_dir, patch("builtins.open", file_open):
        mock_dir.return_value = ["1-0-0-1_test_uuid"]

        from log_collector.rest import api

        with patch("os.path.exists") as mock_exists, patch.object(api, "tail_read") as mock_tail_read:
            api.cluster_inotify.local_ip = {"mgt": "*********", "storage": "*******"}
            mock_exists.return_value = True
            mock_tail_read.return_value = ["#LOG_COLLECTOR_PROGRESS#stage=COLLECT_NODE#host_ip=*******#progress=0.528#"]

            result = api._get_local_progress("uuid1234")

            mock_tail_read.assert_called_once_with("/tmp/tmp_log_local_collector_uuid1234.txt", n=1)
            assert result["progress"] == "#LOG_COLLECTOR_PROGRESS#stage=COLLECT_NODE#host_ip=*******#progress=0.528#"
            assert result["host_ip"] == "*******"


def test_get_cluster_progress(client):
    with patch("os.listdir") as mock_dir, patch("builtins.open", file_open):
        mock_dir.return_value = ["1-0-0-1_test_uuid"]

        from log_collector.rest import api

        with patch.object(api, "_get_local_progress") as mock_process, patch("requests.get") as mock_get:
            api.cluster_inotify.cluster_hosts = {
                "storage": ["*******", "*******"],
                "mgt": ["*********", "*********"],
                "witness": "*******54",
            }
            api.cluster_inotify.local_ip = {"storage": "*******"}
            mock_process.return_value = {
                "host_ip": "*******", "progress": "node_info - INFO - ******* - Collect zbs info."
            }
            response = mock_get.return_value = Mock()
            response.json.return_value = {
                "data": {"host_ip": "*******", "progress": "node_info - INFO - ******* - Collect disk info."}
            }
            r = client.get("/api/v2/logs/uuid1234/progress")
            assert r.status_code == 200
            assert json.loads(r.data)["data"][0]["host_ip"] == "*******"
