# -*- coding: utf-8 -*-
# Copyright (c) 2013-2023, SMARTX
# All rights reserved.
import json

from unittest.mock import patch

from pyfakefs import fake_filesystem

from log_collector.utils.log_config import LogConf


fs = fake_filesystem.FakeFilesystem()
mock_config = {
    "log_config": [
        {
          "group_name": "kernel_logs",
          "service_list": [
              {"name": "messages", "path": ["/var/log/messages*"]},
              {"name": "audit", "path": ["/var/log/audit/*"]},
          ],
        },
        {
          "group_name": "zbs_logs",
          "service_list": [
              {"name": "zbs-chunkd", "path": ["/var/log/zbs/zbs-chunkd*"]},
              {"name": "zbs-metad", "path": ["/var/log/zbs/zbs-metad*"]},
          ],
          "supported_platform": ["san", "kvm", "vmware"],
        },
    ]
}
fs.create_file("/etc/log_collector/log_config.json", contents=json.dumps(mock_config))
file_open = fake_filesystem.FakeFileOpen(fs)


def test_load_log_config():
    with patch("builtins.open", file_open), patch.object(LogConf, "get_platform", return_value="elf"):
        res = LogConf().load_log_config()
        assert len(res) == 1
        assert res[0]["group_name"] == "kernel_logs"


def test_build_log_config_pattern_all_groups():
    with patch("builtins.open", file_open), patch.object(LogConf, "get_platform", return_value="kvm"):
        result = LogConf().build_log_config_pattern(log_groups=None, log_services=None)

        assert result[0]["group_name"] == "kernel_logs"
        assert result[0]["service_list"][1]["name"] == "audit"
        assert result[1]["group_name"] == "zbs_logs"
        assert result[1]["service_list"][0]["name"] == "zbs-chunkd"


def test_build_log_config_pattern_specific_group_and_service():
    with patch("builtins.open", file_open), patch.object(LogConf, "get_platform", return_value="kvm"):
        result = LogConf().build_log_config_pattern("zbs_logs", "messages")

        assert result[0]["group_name"] == "zbs_logs"
        assert result[0]["service_list"][0]["name"] == "zbs-chunkd"
        assert result[1]["group_name"] == "kernel_logs"
        assert result[1]["service_list"][0]["name"] == "messages"
        assert len(result[1]["service_list"]) == 1
