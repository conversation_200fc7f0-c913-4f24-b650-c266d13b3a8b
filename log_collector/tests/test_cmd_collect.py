# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
import uuid

from unittest.mock import patch

import log_collector.cluster.cmd_collect as cmd_collect
from log_collector.cluster.cmd_collect import CollectLog


metadata = {
    "task_uuid": str(uuid.uuid4()),
    "node_list": ["**************"],
    "groups": ["zbs_logs", "kernel_logs"],
    "services": ["elf-exporter", "tuna-exporter"],
    "collect_start_timestamp": 1621210916,
    "collect_end_timestamp": None,
    "start_timestamp": 1621210916,
    "end_timestamp": 1621225316,
    "path": "/log_path",
    "size": 0,
    "status": "running",
    "owner": "*******",
    "progress": "0%",
}


@patch.object(cmd_collect, "get_cluster_name", return_value="test_cluster")
@patch.object(cmd_collect.Executor, "get_status", return_value="successful")
@patch.object(cmd_collect.Executor, "execute", return_value=None)
@patch.object(cmd_collect.Inventory, "generate_inventory", return_value=None)
@patch.object(cmd_collect.MetaData, "init_metadata", return_value=metadata)
@patch.object(cmd_collect.ZbsConf, "check_local_ip", return_value=False)
def test_collect_log_by_cmd(*args):
    with patch.object(cmd_collect.Executor, "write_stdout_to_log") as mock_stout_log:
        mock_stout_log.return_value = None
        cl = CollectLog(["**************"], 1621210916, 1621225316, None, None, "test-uuid-123")
        cl.collect()
