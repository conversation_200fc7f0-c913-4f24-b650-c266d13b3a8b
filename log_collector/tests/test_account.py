# Copyright (c) 2013-2024, SMARTX
# All rights reserved.

import scrypt
import unittest
from unittest.mock import MagicMock, patch

from log_collector.utils import account


class TestVsphereAccountService(unittest.TestCase):
    def setUp(self):
        self.service = account.VsphereAccountService()
        self.host_uuid = "test-uuid"
        self.account_data = {
            "host_uuid": self.host_uuid,
            "password": "encrypted_password",
            "host": "test-host",
            "user": "test-user",
            "port": 1234,
        }

    @patch.object(account, "mongodb")
    def test_get_by_node_uuid_with_no_password(self, mock_db):
        mock_find_one = MagicMock(return_value=self.account_data)
        mock_db[self.service.DEFAULT_DB_NAME].vsphere_account.find_one.return_value = mock_find_one

        result = self.service.get_by_node_uuid(self.host_uuid, no_password=True)

        self.assertNotIn("password", result)
        mock_db[self.service.DEFAULT_DB_NAME].vsphere_account.find_one.assert_called_once()

    @patch.object(account, "mongodb")
    @patch.object(account.VsphereAccountService, "decode_password")
    def test_get_by_node_uuid_with_password(self, mock_decode_password, mock_db):
        mock_decode_password.return_value = "decrypted_password"
        mock_db[self.service.DEFAULT_DB_NAME].vsphere_account.find_one.return_value = self.account_data

        result = self.service.get_by_node_uuid(self.host_uuid, no_password=False)

        mock_decode_password.assert_called_once_with("encrypted_password")

        self.assertEqual(result["password"], "decrypted_password")

    @patch.object(account, "mongodb")
    @patch.object(account.VsphereAccountService, "decode_password")
    def test_get_by_node_uuid_password_decrypt_failure(self, mock_decode_password, mock_db):
        mock_decode_password.side_effect = scrypt.error("test error")
        mock_db[self.service.DEFAULT_DB_NAME].vsphere_account.find_one.return_value = self.account_data

        result = self.service.get_by_node_uuid(self.host_uuid, no_password=False)

        self.assertIsNone(result)


if __name__ == "__main__":
    unittest.main()
