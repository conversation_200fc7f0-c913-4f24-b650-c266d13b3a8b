# Copyright (c) 2013-2021, SMARTX
# All rights reserved.

import uuid

from unittest.mock import patch

import log_collector.node.node_collector as node_collector
from log_collector.node.node_collector import NodeCollector


log_config = [
    {
        "group_name": "kernel_logs",
        "service_list": [
            {"name": "messages", "path": ["/var/log/messages*"]},
            {"name": "audit", "path": ["/var/log/audit/*"]},
            {"name": "dmesg", "path": ["/var/log/dmesg*"]},
        ],
    },
    {
        "group_name": "zbs_logs",
        "service_list": [
            {"name": "zbs-metad", "path": ["/var/log/zbs/zbs-meta*"]},
            {"name": "zbs-chunkd", "path": ["/var/log/zbs/zbs-chunk*"]},
            {"name": "zbs-ntpd", "path": ["/var/log/zbs/zbs-ntpd*"]},
            {"name": "zbs-taskd", "path": ["/var/log/zbs/zbs-taskd*"]},
            {"name": "zbs-inspectord", "path": ["/var/log/zbs/zbs-inspector*"]},
            {"name": "timemachine", "path": ["/var/log/zbs/timemachine*"]},
            {"name": "zbs-aurorad", "path": ["/var/log/zbs/zbs-aurorad*"]},
            {"name": "zbs-aurora-monitord", "path": ["/var/log/zbs/zbs-aurora-monitor*"]},
            {"name": "zbs-scavenger", "path": ["/var/log/zbs/zbs-scavenger*"]},
            {"name": "zbs-lsm-tool", "path": ["/var/log/zbs/zbs-lsm-tool*"]},
            {"name": "zbs-iscsi-redirectord", "path": ["/var/log/zbs/zbs-iscsi-redirectord*"]},
            {"name": "procurator", "path": ["/var/log/zbs/procurator/procurator/*"]},
            {"name": "procurator-qemu", "path": ["/var/log/zbs/procurator/qemu/*"]},
        ],
    },
    {
        "group_name": "base_component_logs",
        "service_list": [
            {"name": "zookeeper", "path": ["/var/log/zookeeper/*"]},
            {"name": "mongodb", "path": ["/var/log/mongodb/*"]},
            {
                "name": "job-center-worker",
                "path": ["/var/log/zbs/job-center-worker*", "/var/log/zbs/archive/job-center-worker*"],
            },
            {"name": "job-center-scheduler", "path": ["/var/log/zbs/job-center-scheduler*"]},
            {"name": "openvswitch", "path": ["/var/log/openvswitch/*"]},
            {"name": "bash", "path": ["/var/log/bash*"]},
            {"name": "cron", "path": ["/var/log/cron*"]},
            {"name": "nginx", "path": ["/var/log/nginx/*"]},
            {"name": "dhcp", "path": ["/var/log/zbs/dhcp*"]},
            {"name": "yum", "path": ["/var/log/yum.log"]},
            {"name": "redis", "path": ["/var/log/zbs/redis*"]},
            {"name": "containerd", "path": []},
        ],
    },
    {
        "group_name": "tuna_logs",
        "service_list": [
            {"name": "disk-healthd", "path": ["/var/log/zbs/disk-healthd/*"]},
            {"name": "cluster-upgrader", "path": ["/var/log/zbs/*upgrade*", "/var/log/zbs/archive/*upgrade*"]},
            {"name": "tuna-exporter", "path": ["/var/log/zbs/tuna-exporter*", "/var/log/zbs/archive/tuna-exporter*"]},
            {
                "name": "network-monitor",
                "path": [
                    "/var/log/zbs/network-monitor*",
                    "/var/log/zbs/archive/network-monitor*",
                    "/var/log/zbs/archive/network-high-latencies*",
                ],
            },
            {"name": "zbs-rest", "path": ["/var/log/zbs/zbs-rest*", "/var/log/zbs/archive/zbs-rest*"]},
            {"name": "zbs-deploy", "path": ["/var/log/zbs-deploy*"]},
            {"name": "fisheye-collected", "path": ["/var/log/zbs/collectd*"]},
            {"name": "log-collector", "path": ["/var/log/zbs/log-collector*", "/var/log/zbs/archive/log-collector*"]},
        ],
    },
    {
        "group_name": "elf_logs",
        "service_list": [
            {"name": "elf-vm-monitor", "path": ["/var/log/zbs/elf-vm-monitor*"]},
            {"name": "elf-vm-scheduler", "path": ["/var/log/zbs/elf-vm-scheduler*"]},
            {"name": "vmtools-agent", "path": ["/var/log/zbs/vmtools_agent*"]},
            {"name": "elf-exporter", "path": ["/var/log/zbs/elf-exporter*", "/var/log/zbs/archive/elf-exporter*"]},
            {"name": "libvirtd", "path": ["/var/log/zbs/libvirtd*", "/var/log/zbs/archive/libvirtd*"]},
            {"name": "qemu", "path": ["/var/log/libvirt/qemu/*"]},
            {"name": "vnc-proxy", "path": ["/var/log/zbs/vnc-proxy/*"]},
            {"name": "goose", "path": ["/var/log/zbs/goose*"]},
        ],
    },
    {
        "group_name": "octopus_logs",
        "service_list": [
            {"name": "octopus", "path": ["/var/log/zbs/octopus/*"]},
            {"name": "prometheus", "path": ["/var/log/zbs/prometheus/prometheus*"]},
            {"name": "dolphin", "path": ["/var/log/zbs/dolphin/*"]},
            {"name": "siren", "path": ["/var/log/zbs/siren/*"]},
            {"name": "crab", "path": ["/var/log/zbs/crab/*"]},
            {"name": "consul", "path": ["/var/log/zbs/consul/*"]},
            {"name": "consul-server", "path": ["/var/log/zbs/consul-server/*"]},
            {"name": "harbor", "path": ["/var/log/zbs/harbor/*"]},
            {"name": "aquarium", "path": ["/var/log/zbs/aquarium/*"]},
            {"name": "envoy", "path": ["/var/log/zbs/envoy/*"]},
            {"name": "envoy-xds", "path": ["/var/log/zbs/envoy-xds/*"]},
            {"name": "oscar", "path": ["/var/log/zbs/oscar/*"]},
            {"name": "seal", "path": ["/var/log/zbs/seal/*"]},
            {"name": "fluent-bit", "path": ["/var/log/zbs/fluent-bit/fluent-bit*"]},
        ],
    },
    {"group_name": "coredump_logs", "service_list": [{"name": "coredump_logs", "path": ["/var/crash/*"]}]},
    {"group_name": "node_info", "service_list": [{"name": "node_info", "path": []}]},
]


@patch.object(node_collector.CollectNodeService, "collect_services_log", return_value=None)
@patch.object(node_collector.CollectInfo, "collect_all", return_value=None)
@patch("log_collector.utils.zbs_conf.ZbsConf.get_current_storage_ip", return_value="*******")
@patch("log_collector.utils.zbs_conf.ZbsConf.get_role", return_value="master")
def test_collect_log(*args):
    mock_uuid = str(uuid.uuid4())
    collect_config = [
        {"group_name": "kernel", "service_list": [{"name": "kernel", "path": ["/var/log/kern*"]}]},
        {"group_name": "node_info", "service_list": [{"name": "node_info", "path": []}]},
    ]
    node_c = NodeCollector(mock_uuid, 9000000, group_service_pattern=collect_config)
    node_c.collect_log(1621210916, 1621225316)


def test_collect_services_log_failed():
    with (
        patch.object(node_collector.CollectInfo, "collect_all") as node_info_mock,
        patch.object(node_collector.CollectNodeService, "collect_services_log") as services_mock,
    ):
        node_info_mock.return_value = None
        services_mock.side_effect = Exception("error test")
        mock_uuid = str(uuid.uuid4())
        node_c = NodeCollector(mock_uuid, 9000000)
        node_c.collect_log(1621210916, 1621225316)


def test_collect_node_info_failed():
    with (
        patch.object(node_collector.CollectInfo, "collect_all") as node_info_mock,
        patch.object(node_collector.CollectNodeService, "collect_services_log") as services_mock,
    ):
        node_info_mock.side_effect = Exception("node info error")
        services_mock.side_effect = Exception("services log error")
        mock_uuid = str(uuid.uuid4())
        node_c = NodeCollector(mock_uuid, 9000000)
        node_c.collect_log(1621210916, 1621225316)
        assert services_mock.call_count == 1
