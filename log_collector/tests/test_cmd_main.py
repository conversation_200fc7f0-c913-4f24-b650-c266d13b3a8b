# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from unittest.mock import patch
import pytest
import unittest

import log_collector.cluster.cmd_collect as cmd_collect
from log_collector.utils.exceptions import ParamException


log_config = [
    {
        "group_name": "kernel_logs",
        "service_list": [
            {"name": "messages", "path": ["/var/log/messages*"]},
            {"name": "audit", "path": ["/var/log/audit/*"]},
            {"name": "dmesg", "path": ["/var/log/dmesg*"]},
        ],
    },
    {
        "group_name": "zbs_logs",
        "service_list": [
            {"name": "zbs-metad", "path": ["/var/log/zbs/zbs-meta*"]},
            {"name": "zbs-chunkd", "path": ["/var/log/zbs/zbs-chunk*"]},
            {"name": "zbs-ntpd", "path": ["/var/log/zbs/zbs-ntpd*"]},
            {"name": "zbs-taskd", "path": ["/var/log/zbs/zbs-taskd*"]},
            {"name": "zbs-inspectord", "path": ["/var/log/zbs/zbs-inspector*"]},
            {"name": "timemachine", "path": ["/var/log/zbs/timemachine*"]},
            {"name": "zbs-aurorad", "path": ["/var/log/zbs/zbs-aurorad*"]},
            {"name": "zbs-aurora-monitord", "path": ["/var/log/zbs/zbs-aurora-monitor*"]},
            {"name": "zbs-scavenger", "path": ["/var/log/zbs/zbs-scavenger*"]},
            {"name": "zbs-lsm-tool", "path": ["/var/log/zbs/zbs-lsm-tool*"]},
            {"name": "zbs-iscsi-redirectord", "path": ["/var/log/zbs/zbs-iscsi-redirectord*"]},
            {"name": "procurator", "path": ["/var/log/zbs/procurator/procurator/*"]},
            {"name": "procurator-qemu", "path": ["/var/log/zbs/procurator/qemu/*"]},
        ],
    },
    {
        "group_name": "base_component_logs",
        "service_list": [
            {"name": "zookeeper", "path": ["/var/log/zookeeper/*"]},
            {"name": "mongodb", "path": ["/var/log/mongodb/*"]},
            {
                "name": "job-center-worker",
                "path": ["/var/log/zbs/job-center-worker*", "/var/log/zbs/archive/job-center-worker*"],
            },
            {"name": "job-center-scheduler", "path": ["/var/log/zbs/job-center-scheduler*"]},
            {"name": "openvswitch", "path": ["/var/log/openvswitch/*"]},
            {"name": "bash", "path": ["/var/log/bash*"]},
            {"name": "cron", "path": ["/var/log/cron*"]},
            {"name": "nginx", "path": ["/var/log/nginx/*"]},
            {"name": "dhcp", "path": ["/var/log/zbs/dhcp*"]},
            {"name": "yum", "path": ["/var/log/yum.log"]},
            {"name": "redis", "path": ["/var/log/zbs/redis*"]},
            {"name": "containerd", "path": []},
        ],
    },
    {
        "group_name": "tuna_logs",
        "service_list": [
            {"name": "disk-healthd", "path": ["/var/log/zbs/disk-healthd/*"]},
            {"name": "cluster-upgrader", "path": ["/var/log/zbs/*upgrade*", "/var/log/zbs/archive/*upgrade*"]},
            {"name": "tuna-exporter", "path": ["/var/log/zbs/tuna-exporter*", "/var/log/zbs/archive/tuna-exporter*"]},
            {
                "name": "network-monitor",
                "path": [
                    "/var/log/zbs/network-monitor*",
                    "/var/log/zbs/archive/network-monitor*",
                    "/var/log/zbs/archive/network-high-latencies*",
                ],
            },
            {"name": "zbs-rest", "path": ["/var/log/zbs/zbs-rest*", "/var/log/zbs/archive/zbs-rest*"]},
            {"name": "zbs-deploy", "path": ["/var/log/zbs-deploy*"]},
            {"name": "fisheye-collected", "path": ["/var/log/zbs/collectd*"]},
            {"name": "log-collector", "path": ["/var/log/zbs/log-collector*", "/var/log/zbs/archive/log-collector*"]},
        ],
    },
    {
        "group_name": "elf_logs",
        "service_list": [
            {"name": "elf-vm-monitor", "path": ["/var/log/zbs/elf-vm-monitor*"]},
            {"name": "elf-vm-scheduler", "path": ["/var/log/zbs/elf-vm-scheduler*"]},
            {"name": "vmtools-agent", "path": ["/var/log/zbs/vmtools_agent*"]},
            {"name": "elf-exporter", "path": ["/var/log/zbs/elf-exporter*", "/var/log/zbs/archive/elf-exporter*"]},
            {"name": "libvirtd", "path": ["/var/log/zbs/libvirtd*", "/var/log/zbs/archive/libvirtd*"]},
            {"name": "qemu", "path": ["/var/log/libvirt/qemu/*"]},
            {"name": "vnc-proxy", "path": ["/var/log/zbs/vnc-proxy/*"]},
            {"name": "goose", "path": ["/var/log/zbs/goose*"]},
        ],
    },
    {
        "group_name": "octopus_logs",
        "service_list": [
            {"name": "octopus", "path": ["/var/log/zbs/octopus/*"]},
            {"name": "prometheus", "path": ["/var/log/zbs/prometheus/prometheus*"]},
            {"name": "dolphin", "path": ["/var/log/zbs/dolphin/*"]},
            {"name": "siren", "path": ["/var/log/zbs/siren/*"]},
            {"name": "crab", "path": ["/var/log/zbs/crab/*"]},
            {"name": "consul", "path": ["/var/log/zbs/consul/*"]},
            {"name": "consul-server", "path": ["/var/log/zbs/consul-server/*"]},
            {"name": "harbor", "path": ["/var/log/zbs/harbor/*"]},
            {"name": "aquarium", "path": ["/var/log/zbs/aquarium/*"]},
            {"name": "envoy", "path": ["/var/log/zbs/envoy/*"]},
            {"name": "envoy-xds", "path": ["/var/log/zbs/envoy-xds/*"]},
            {"name": "oscar", "path": ["/var/log/zbs/oscar/*"]},
            {"name": "seal", "path": ["/var/log/zbs/seal/*"]},
            {"name": "fluent-bit", "path": ["/var/log/zbs/fluent-bit/fluent-bit*"]},
        ],
    },
    {"group_name": "coredump_logs", "service_list": [{"name": "coredump_logs", "path": ["/var/crash/*"]}]},
    {"group_name": "node_info", "service_list": [{"name": "node_info", "path": []}]},
]


class TestCmdMain(unittest.TestCase):
    def test_check_log_services(self):
        with patch("os.listdir") as mock_listdir:
            mock_listdir.return_value = []

            import log_collector.cmd.main as cmd_main

            with patch.object(cmd_main.LogConf, "load_log_config", return_value=log_config):
                log_services = "tuna-exporter,elf-vm-monitor"
                cmd_main.check_log_services(log_services)

    def test_check_log_groups_with_invalid_params(self):
        with patch("os.listdir") as mock_listdir:
            mock_listdir.return_value = []
            from log_collector.cmd.main import check_log_groups

            groups = "tuna1,elftest"
            with self.assertRaises(ParamException):
                check_log_groups(groups)

    def test_check_log_services_with_invalid_params(self):
        with patch("os.listdir") as mock_listdir:
            mock_listdir.return_value = []
            from log_collector.cmd.main import check_log_services

            services = "tuna-test,elf-test"
            with self.assertRaises(ParamException):
                check_log_services(services)

    def test_start_time_eq_end_time_collect(self):
        with patch("os.listdir") as mock_listdir:
            mock_listdir.return_value = []
            from log_collector.cmd.main import collect

            runner = CliRunner()
            runner.invoke(collect, ["--start_time", "2021/12/03-10:10:10", "--end_time", "2021/12/03-10:10:10"])

    def test_start_time_eq_end_time_cluster_collect(self):
        with patch("os.listdir") as mock_listdir:
            mock_listdir.return_value = []
            from log_collector.cmd.main import cluster

            runner = CliRunner()
            runner.invoke(
                cluster, ["collect", "--start_time", "2021/12/03-10:10:10", "--end_time", "2021/12/03-10:10:10"]
            )

    def test_cmd_collect_log(self):
        with patch("os.listdir") as mock_listdir:
            mock_listdir.return_value = []

            import log_collector.cmd.cluster_logs as cluster_logs
            import log_collector.cmd.main as cmd_main
            from log_collector.cmd.main import collect

            with (
                patch.object(cmd_collect.Executor, "write_stdout_to_log") as mock_stout_log,
                patch.object(cluster_logs.CollectTask, "start") as mock_start,
                patch.object(cmd_main, "parse_node_list") as mock_node_list,
            ):
                mock_stout_log.return_value = None
                mock_start.return_value = None
                mock_node_list.return_value = ["192.168.31.116"]
                runner = CliRunner()
                result = runner.invoke(collect, ["--groups", "tuna_logs,zbs_logs"])

                assert result.exit_code == 1

    def test_config_show(self):
        with patch("os.listdir") as mock_listdir:
            mock_listdir.return_value = []
            import log_collector.cmd.main as cmd_main
            from log_collector.cmd.main import show

            with patch.object(cmd_main.LogConf, "load_log_config", return_value=log_config):
                runner = CliRunner()
                result = runner.invoke(show)

                assert result.exit_code == 0

    def test_cmd_clean_logs(self):
        from log_collector.cluster import task_clean
        from log_collector.utils.exceptions import ZbsConfException

        with (
            patch.object(task_clean.ZbsConf, "check_local_ip") as mock_local_ip,
            patch.object(task_clean.Executor, "execute") as mock_execute,
            patch.object(task_clean.Executor, "write_stdout_to_log") as mock_stdout,
            patch.object(task_clean.Executor, "get_status") as mock_status,
        ):
            with pytest.raises(ZbsConfException):
                mock_local_ip.return_value = False
                mock_execute.return_value = None
                mock_stdout.return_value = None
                mock_status.return_value = "successful"
                clean_task = task_clean.Task(["*******", "*******"])
                clean_task.run()
