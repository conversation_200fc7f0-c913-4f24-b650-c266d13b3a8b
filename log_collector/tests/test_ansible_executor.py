# Copyright (c) 2013-2024, SMARTX
# All rights reserved.

import subprocess

import unittest
from unittest.mock import MagicMock, patch
from log_collector.utils import ansible_executor


class TestAnsibleExecutor(unittest.TestCase):
    @patch.object(ansible_executor, "Popen")
    def test_terminate(self, mock_popen):
        process_mock = MagicMock()
        mock_popen.return_value = process_mock
        process_mock.poll.return_value = None  # Process is still running
        process_mock.wait.side_effect = subprocess.TimeoutExpired(cmd="cmd", timeout=10)

        instance = ansible_executor.Executor("test_playbook.yml", "test-uuid-1")
        instance.process = process_mock

        instance.terminate()

        process_mock.terminate.assert_called_once()
        process_mock.kill.assert_called_once()


if __name__ == "__main__":
    unittest.main()
