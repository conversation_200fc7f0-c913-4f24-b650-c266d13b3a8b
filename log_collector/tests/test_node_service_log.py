# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
import uuid

from unittest.mock import call, MagicM<PERSON>, Mock, mock_open, patch
import pytest

import log_collector.node.node_service_log as node_service_log
from log_collector.node.node_service_log import CollectNodeService


class MockPopen:
    def __init__(self, cmd, shell=True):
        self.cmd = cmd

    @classmethod
    def wait(cls):
        return None

    @classmethod
    def communicate(cls, timeout=10):
        return "success", None

    @property
    def returncode(self):
        return 0


class MockTimeout:
    def __init__(self, cmd, shell=True):
        self.cmd = cmd

    @classmethod
    def communicate(cls):
        return "success", ""

    @classmethod
    def kill(cls):
        return None

    @classmethod
    def wait(cls):
        return None

    @property
    def returncode(self):
        return 1


@patch.object(node_service_log, "Popen", return_value=MockPopen(""))
@patch("shutil.rmtree", return_value=None)
@patch("os.path.basename", return_value="tmp")
@patch("shutil.copy2", return_value=None)
@patch("os.path.isdir", return_value=True)
@patch("log_collector.node.summary.open", mock_open())
@patch.object(node_service_log, "local_log_threshold", return_value=19000000)
@patch("os.path.getsize", return_value=2000)
@patch.object(node_service_log.File, "get_create_time", return_value=1621225300)
@patch("os.path.getmtime", return_value=1621210920)
@patch("log_collector.node.node_service_log.CollectNodeService._extract_log_from_pattern", return_value=["test.log"])
@patch("glob.glob", return_value=["/tmp/test.log"])
def test_collect_services_log(*args):
    log_config = [{"group_name": "kernel_logs", "service_list": [{"name": "messages", "path": ["/var/log/messages*"]}]}]
    mock_uuid = str(uuid.uuid4())
    collect_node_service = CollectNodeService("127.0.0.1", "/tmp/test/", 90000000, log_config, mock_uuid)

    collect_node_service.collect_services_log(1621210916, 1621225316)


@patch.object(node_service_log, "Popen", return_value=MockPopen(""))
@patch("shutil.rmtree", return_value=None)
@patch("tarfile.TarFile.open.close", return_value=None)
@patch("tarfile.TarFile.open.add", return_value=None)
@patch("tarfile.TarFile.open", return_value=Mock())
@patch("os.path.basename", return_value="tmp")
@patch.object(node_service_log.Summary, "write_summary", return_value=None)
@patch("shutil.copy2", return_value=None)
@patch("os.path.isdir", return_value=True)
@patch.object(node_service_log, "local_log_threshold", return_value=19000000)
@patch("os.path.getsize", return_value=2000)
@patch.object(node_service_log.File, "get_create_time", return_value=1621225300)
@patch("os.path.getmtime", return_value=1621225320)
@patch("log_collector.node.node_service_log.CollectNodeService._extract_log_from_pattern", return_value=["test.log"])
@patch("glob.glob", return_value=["/tmp/test.log"])
def test_collect_services_log_with_modify_time_greater_than_end_time(*args):
    log_config = [{"group_name": "kernel_logs", "service_list": [{"name": "messages", "path": ["/var/log/messages*"]}]}]
    mock_uuid = str(uuid.uuid4())
    collect_node_service = CollectNodeService("127.0.0.1", "/tmp/test/", 90000000, log_config, mock_uuid)
    collect_node_service.collect_services_log(1621210916, 1621225316)


@patch.object(node_service_log, "Popen", return_value=MockTimeout(""))
@patch("os.path.basename", return_value="tmp")
def test_compress(*args):
    with patch("shutil.rmtree") as mock_rmtree:
        log_config = [
            {"group_name": "kernel_logs", "service_list": [{"name": "messages", "path": ["/var/log/messages*"]}]}
        ]
        mock_uuid = str(uuid.uuid4())
        collect_node_service = CollectNodeService("127.0.0.1", "/tmp/test/", 90000000, log_config, mock_uuid)
        mock_rmtree.return_value = None
        with pytest.raises(Exception):
            collect_node_service._compress()


@patch("os.path.islink", return_value=False)
@patch("os.path.isfile", return_value=True)
@patch("glob.glob", return_value=["/tmp/test.log"])
def test_extract_log_from_pattern(*args):
    log_config = [{"group_name": "kernel_logs", "service_list": [{"name": "messages", "path": ["/var/log/messages*"]}]}]
    mock_uuid = str(uuid.uuid4())
    node_c = CollectNodeService("127.0.0.1", "/tmp/test/", log_config, 90000000, mock_uuid)
    res = node_c._extract_log_from_pattern(["/tmp/"])
    assert res == ["/tmp/test.log"]


@patch("os.walk", return_value=[("/var/crash/test-time", [], ["testcore"])])
@patch("os.path.isdir", return_value=True)
@patch("os.path.islink", side_effect=[False, False])
@patch("os.path.isfile", side_effect=[False, True])
@patch("glob.glob", return_value=["/var/crash/test-time"])
def test_extract_log_from_path_contain_dir(*args):
    log_config = [
        {"group_name": "coredump_logs", "service_list": [{"name": "coredump_logs", "path": ["/var/crash/*"]}]}
    ]
    mock_uuid = str(uuid.uuid4())
    node_c = CollectNodeService("127.0.0.1", "/tmp/test", log_config, 90000000, mock_uuid)
    res = node_c._extract_log_from_pattern(["/var/crash/*"])
    assert res == ["/var/crash/test-time/testcore"]


@patch("os.makedirs", return_value=None)
@patch("os.path.isdir", return_value=False)
@patch("os.path.getsize", return_value=12000)
@patch.object(node_service_log.File, "get_create_time", return_value=1621225300)
@patch("os.path.getmtime", return_value=1621210920)
def test_collect_path_logs_oversize(*args):
    log_config = [{"group_name": "kernel_logs", "service_list": [{"name": "messages", "path": ["/var/log/messages*"]}]}]
    mock_uuid = str(uuid.uuid4())
    collect_node_service = CollectNodeService("127.0.0.1", "/tmp/test/", 200, log_config, mock_uuid)
    res = collect_node_service._copy_path_logs({"/tuna_logs/": ["tuna-exporter"]}, 1621210916, 1621225316)
    assert res == []


@patch.object(node_service_log, "check_capacity_safe", return_value=True)
@patch("builtins.open", new_callable=mock_open)
def test_collect_cmd_logs(*args):
    with patch.object(node_service_log, "run_shell_script_with_return_output") as mock_shell_op:
        log_dict = [
            {
                "group_name": "zbs_logs",
                "service_list": [
                    {
                        "name": "zbs-watchdogd",
                        "patch": [],
                        "cmd": [
                            {"command": "curl 127.0.0.1:10300/api/v1/watchdog/log", "identifier": "log"},
                            {"command": "curl 127.0.0.1:10300/api/v1/watchdog/log_warn", "identifier": "log_warn"},
                            {"command": "curl 127.0.0.1:10300/api/v1/watchdog/log_error", "identifier": "log_error"},
                        ],
                    }
                ]
            },
            {
                "group_name": "tuna_logs",
                "service_list": [
                    {"name": "sd-offline", "path": [], "cmd": [{"command": "sdm -l", "identifier": "log"}]}
                ],
            },
        ]
        mock_uuid = "uuid-123"
        collect_node_service = CollectNodeService("127.0.0.1", "/tmp/test/", 20000, log_dict, mock_uuid)
        cmd_log_dict = collect_node_service._get_cmd_log_info(log_dict)

        mock_cmd_log_dict = {
            "/zbs_logs/zbs-watchdogd": [
                {"command": "curl 127.0.0.1:10300/api/v1/watchdog/log", "identifier": "log"},
                {"command": "curl 127.0.0.1:10300/api/v1/watchdog/log_warn", "identifier": "log_warn"},
                {"command": "curl 127.0.0.1:10300/api/v1/watchdog/log_error", "identifier": "log_error"},
            ],
            "/tuna_logs/sd-offline": [{"command": "sdm -l", "identifier": "log"}],
        }

        assert cmd_log_dict["/zbs_logs/zbs-watchdogd"] == mock_cmd_log_dict["/zbs_logs/zbs-watchdogd"]
        assert cmd_log_dict["/tuna_logs/sd-offline"][0]["command"] \
               == mock_cmd_log_dict["/tuna_logs/sd-offline"][0]["command"]

        mock_shell_op.side_effect = [
            (0, "test watchdog logs"),
            (1, "test warn watchdog logs"),
            (0, "test error watchdog logs"),
            (1, ""),  # test empty output from sdm -l
        ]

        res = collect_node_service._copy_cmd_logs(mock_cmd_log_dict)
        assert len(res) == 4
        assert "zbs-watchdogd_log_warn.log" in res


def test_check_time_error():
    with patch.object(node_service_log.File, "get_create_time") as mock_get_time:
        mock_get_time.side_effect = OSError
        log_config = [
            {"group_name": "kernel_logs", "service_list": [{"name": "messages", "path": ["/var/log/messages*"]}]}
        ]
        mock_uuid = str(uuid.uuid4())
        collect_node_service = CollectNodeService("127.0.0.1", "/tmp/test/", 200000, log_config, mock_uuid)
        res = collect_node_service._check_time({"/tuna_logs/": ["tuna-exporter"]}, 1621210916, 1621225316)
        assert len(res) == 1


@patch.object(node_service_log, "Popen", return_value=MockPopen(""))
@patch("shutil.rmtree", return_value=None)
@patch("tarfile.TarFile.open.close", return_value=None)
@patch("tarfile.TarFile.open.add", return_value=None)
@patch("tarfile.TarFile.open", return_value=Mock())
@patch("os.path.basename", return_value="tmp")
@patch.object(node_service_log.Summary, "write_summary", return_value=None)
@patch("shutil.copy2", return_value=None)
@patch("os.path.isdir", return_value=True)
@patch.object(node_service_log, "local_log_threshold", return_value=19000000)
@patch("os.path.getsize", return_value=2000)
@patch.object(node_service_log.File, "get_create_time", return_value=1621225300)
@patch("os.path.getmtime", return_value=1621210920)
@patch("log_collector.node.node_service_log.CollectNodeService._extract_log_from_pattern", return_value=["test.log"])
@patch("glob.glob", return_value=["/tmp/test.log"])
def test_collect_with_node(*args):
    log_config = [{"group_name": "kernel_logs", "service_list": [{"name": "messages", "path": ["/var/log/messages*"]}]}]
    mock_uuid = str(uuid.uuid4())
    collect_node_service = CollectNodeService(
        "127.0.0.1", "/tmp/test/", 90000000, log_config, mock_uuid, collect_type="node"
    )
    collect_node_service.collect_services_log(1621210916, 1621225316)


@patch("log_collector.node.node_service_log.CollectNodeService._extract_log_from_pattern", return_value=["test.log"])
def test_get_log_pattern(*args):
    log_config = [{"group_name": "kernel_logs", "service_list": [{"name": "messages", "path": ["/var/log/messages*"]}]}]
    mock_uuid = str(uuid.uuid4())
    node_c = CollectNodeService("127.0.0.1", "/tmp/test/", 90000000, log_config, mock_uuid)
    res = node_c._get_log_pattern(log_config)
    assert res["/kernel_logs/messages"] == ["test.log"]


def test_copy_journald_log():
    content = """
    -- No entries --
    """
    with patch("os.path.exists") as mock_exist, patch("os.remove") as mock_remove, patch(
        "os.listdir"
    ) as mock_listdir, patch("os.rmdir") as mock_rmdir, patch.object(node_service_log, "Popen") as mock_popen:
        with patch("log_collector.node.node_service_log.open", mock_open(read_data=content)):
            log_config = [
                {"group_name": "kernel_logs", "service_list": [{"name": "dmesg", "path": ["/var/log/messages*"]}]},
                {"group_name": "tuna_logs", "service_list": [{"name": "zbs-deploy", "path": ["/var/log/zbs-deploy*"]}]},
            ]
            mock_popen.return_value = MockPopen("ls")
            mock_exist.return_value = True
            mock_rmdir.return_value = None
            mock_remove.return_value = None
            mock_listdir.return_value = []
            mock_uuid = str(uuid.uuid4())
            node_collect = CollectNodeService("127.0.0.1", "/tmp/test/", 90000000, log_config, mock_uuid)
            service_list = ["dmesg", "zbs-deploy"]
            for service_name in service_list:
                node_collect._copy_journald_log(service_name, 1621210916, 1621225316)


@patch("glob.glob", return_value=["/tmp/test.log"])
def test_check_sos_done(*args):
    log_config = [{"group_name": "kernel_logs", "service_list": [{"name": "messages", "path": ["/var/log/messages*"]}]}]
    mock_uuid = str(uuid.uuid4())
    node_c = CollectNodeService("127.0.0.1", "/tmp/test/", 90000000, log_config, mock_uuid)
    node_c.sos_future = MagicMock()
    node_c.sos_future.result.return_value = (0, "Done")
    node_c._check_sos_done(node_c.sos_future)


@patch("glob.glob", return_value=["/tmp/test_sos1", "/tmp/test_sos2"])
@patch("shutil.rmtree")
@patch("os.path.isdir", return_value=True)
def test_clean_incomplete_sos_files(mock_isdir, mock_rmtree, mock_glob):
    log_config = [{"group_name": "kernel_logs", "service_list": [{"name": "messages", "path": ["/var/log/messages*"]}]}]
    mock_uuid = str(uuid.uuid4())
    node_c = CollectNodeService("127.0.0.1", "/tmp/test/", 90000000, log_config, mock_uuid)

    node_c._clean_incomplete_sos_files("/tmp")

    # Check if glob was called with the correct pattern
    mock_glob.assert_called_once_with("/tmp/sos*")

    # Check if rmtree was called twice for each directory
    assert mock_rmtree.call_count == 2
    mock_rmtree.assert_any_call("/tmp/test_sos1")
    mock_rmtree.assert_any_call("/tmp/test_sos2")


@patch("glob.glob", return_value=["/tmp/test_sos1", "/tmp/test_sos2"])
@patch("shutil.rmtree", side_effect=Exception("Error removing directory"))
@patch("os.path.isdir", return_value=True)
@patch.object(node_service_log.node_logger, "warning")
def test_clean_incomplete_sos_files_with_exception(mock_warning, mock_isdir, mock_rmtree, mock_glob):
    log_config = [{"group_name": "kernel_logs", "service_list": [{"name": "messages", "path": ["/var/log/messages*"]}]}]
    mock_uuid = str(uuid.uuid4())
    node_c = CollectNodeService("127.0.0.1", "/tmp/test/", 90000000, log_config, mock_uuid)

    node_c._clean_incomplete_sos_files("/tmp")

    # Check if glob was called with the correct pattern
    mock_glob.assert_called_once_with("/tmp/sos*")

    # Check if rmtree was called twice for each directory
    assert mock_rmtree.call_count == 2
    mock_rmtree.assert_any_call("/tmp/test_sos1")
    mock_rmtree.assert_any_call("/tmp/test_sos2")

    # Check if the warning logger was called twice
    expected_calls = [
        call("Failed to remove incomplete sos report directory: /tmp/test_sos1, error: Error removing directory"),
        call("Failed to remove incomplete sos report directory: /tmp/test_sos2, error: Error removing directory"),
    ]
    mock_warning.assert_has_calls(expected_calls, any_order=True)


@patch("os.path.isdir", return_value=True)
@patch("shutil.copy2", return_value=None)
@patch.object(node_service_log.File, "get_create_time", return_value=1621225300)
@patch("os.path.getmtime", return_value=1621210920)
def test_copy_logs_return_values(*args):
    """Test that _copy_logs returns both saved_logs and cmd_saved_logs"""
    log_config = [{"group_name": "kernel_logs", "service_list": [{"name": "messages", "path": ["/var/log/messages*"]}]}]
    mock_uuid = str(uuid.uuid4())
    collect_node_service = CollectNodeService("127.0.0.1", "/tmp/test/", 90000000, log_config, mock_uuid)

    # Mock the internal methods
    with patch.object(collect_node_service, '_copy_path_logs', return_value=['path_log1.log', 'path_log2.log']), \
         patch.object(collect_node_service, '_copy_cmd_logs', return_value=['cmd_log1.log', 'cmd_log2.log']):

        saved_logs, cmd_saved_logs = collect_node_service._copy_logs(
            {'/kernel_logs/messages': ['test.log']},
            1621210916,
            1621225316,
            {'/cmd_logs/test': [{'command': 'test', 'identifier': 'log'}]}
        )

        assert saved_logs == ['path_log1.log', 'path_log2.log', 'cmd_log1.log', 'cmd_log2.log']
        assert cmd_saved_logs == ['cmd_log1.log', 'cmd_log2.log']
