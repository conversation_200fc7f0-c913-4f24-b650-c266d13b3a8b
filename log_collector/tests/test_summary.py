# Copyright (c) 2013-2024, SMARTX
# All rights reserved.

import unittest
from unittest.mock import patch, mock_open

from log_collector.node.summary import Summary


class TestSummary(unittest.TestCase):
    @patch("builtins.open", new_callable=mock_open)
    @patch("os.path.exists")
    @patch("socket.gethostname")
    def test_write_summary_with_flat_logs(self, mock_gethostname, mock_exists, mock_file):
        """Test write_summary with flat logs list instead of dict"""
        mock_gethostname.return_value = "testhost"
        mock_exists.return_value = True
        open_handle = mock_file()

        task_uuid = "12345"
        ip = "***********"
        logs = ['info1', 'info2', 'info3', 'info4']  # Flat list of logs
        start_time = 1609459200
        end_time = 1609545600
        exported = ['info1', 'info3']

        summary_instance = Summary(task_uuid, ip, logs, start_time, end_time)

        summary_instance.write_summary(exported)

        written_content = open_handle.write.call_args[0][0]
        self.assertIn("task uuid: 12345", written_content)
        self.assertIn("host name: testhost", written_content)
        self.assertIn("ip: ***********", written_content)
        self.assertIn("logs sum: 4", written_content)
        self.assertIn("saved logs count: 2", written_content)
        self.assertIn("not saved logs count: 2", written_content)
        self.assertIn("info1", written_content)
        self.assertIn("info3", written_content)
        self.assertIn("info2", written_content)
        self.assertIn("info4", written_content)


if __name__ == "__main__":
    unittest.main()
