# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
from datetime import datetime
import time
import uuid

from unittest.mock import patch

import log_collector.cluster.metadata as metadata
from log_collector.cluster.metadata import MetaData
from log_collector.config.constants import LOG_LOG_PATH


@patch.object(metadata, "get_cluster_name", return_value="test_name")
@patch.object(metadata.ZbsConf, "check_local_ip", return_value=True)
@patch.object(metadata.ZbsConf, "get_mgt_ips", return_value=["**************"])
@patch("log_collector.cluster.metadata.MetaData.write_metadata_file", return_value=None)
def test_init_metadata(*args):
    node_list = ["**************", "**************"]
    task_uuid = str(uuid.uuid4())
    start_timestamp, end_timestamp = 1621210916, 1621225316
    send_log_to_controller = False
    groups = ["tuna_logs"]
    services = ["elf-exporter", "zbs-metad"]
    res = MetaData.init_metadata(
        node_list, task_uuid, groups, services, start_timestamp, end_timestamp, send_log_to_controller
    )
    suffix_name = task_uuid.split("-")[0]
    expected_log_path = LOG_LOG_PATH + "/cluster-logs-{}-{}-{}-{}.zip".format(
        "test_name",
        datetime.fromtimestamp(start_timestamp).strftime("%Y%m%d%H"),
        datetime.fromtimestamp(end_timestamp).strftime("%Y%m%d%H"),
        suffix_name,
    )
    expected = {
        "task_uuid": task_uuid,
        "node_list": node_list,
        "groups": groups,
        "services": services,
        "collect_start_timestamp": int(time.time()),
        "collect_end_timestamp": None,
        "start_timestamp": start_timestamp,
        "end_timestamp": end_timestamp,
        "path": expected_log_path,
        "size": 0,
        "status": "running",
        "owner": "**************",
        "progress": "0%",
    }
    assert res["task_uuid"] == expected["task_uuid"]
    assert res["owner"] == expected["owner"]
    assert res["path"] == expected_log_path
