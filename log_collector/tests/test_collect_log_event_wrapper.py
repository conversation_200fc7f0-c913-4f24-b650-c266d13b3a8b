# Copyright (c) 2013-2019, SMARTX
# All rights reserved.

from unittest.mock import patch

from log_collector.events.collect_log_events_wrapper import CollectLogEventWrapper  # noqa: I100,I202
from tuna.cluster.cluster_manager import ClusterManager
from log_collector.events.constants import EVENT_COLLECT_ALL_NODES_LOG, EVENT_COLLECT_SELECTED_NODES_LOG  # noqa: I100

user = {"username": "root", "Role": "ROOT"}
cluster_uuid = "2fc94de8-0219-455c-8a14-0d5d46857f26"


def test_event_collect_all_nodes_log():
    with patch.object(ClusterManager, "get_cluster_uuid") as get_cluster_uuid_mock:
        get_cluster_uuid_mock.return_value = cluster_uuid

        event = CollectLogEventWrapper(user=user).event_collect_all_nodes_log()
        assert event["resources"] == {cluster_uuid: "CLUSTER"}
        assert event["event_name"] == EVENT_COLLECT_ALL_NODES_LOG
        assert event["user_name"] == user["username"]


def test_event_collect_selected_nodes_log():
    with patch.object(ClusterManager, "get_cluster_uuid") as get_cluster_uuid_mock:
        get_cluster_uuid_mock.return_value = cluster_uuid

        event = CollectLogEventWrapper(user=user).event_collect_selected_nodes_log()
        assert event["resources"] == {cluster_uuid: "CLUSTER"}
        assert event["event_name"] == EVENT_COLLECT_SELECTED_NODES_LOG
        assert event["user_name"] == user["username"]
