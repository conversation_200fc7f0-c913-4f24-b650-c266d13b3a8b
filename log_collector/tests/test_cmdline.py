# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
import errno
from subprocess import PIPE, Popen
import uuid

import pexpect
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, patch

from log_collector.utils import ansible_executor
from log_collector.utils import cmdline
from log_collector.utils.ansible_executor import Executor
from log_collector.utils.cmdline import LogTool


class MockProcess:
    def __init__(self, *args, **kwargs):
        self.daemon = "test"

    @classmethod
    def poll(cls):
        return None

    @classmethod
    def kill(cls):
        return None

    @property
    def stdout(self):
        return Popen("ls", stdout=PIPE, stderr=PIPE, bufsize=1, shell=True)

    def start(self):
        return None


class MockPopen:
    @classmethod
    def poll(cls):
        return True

    @classmethod
    def kill(cls):
        return None

    @classmethod
    def stdout(cls):
        return Mock()

    @classmethod
    def returncode(cls):
        return 200


class MockSubPopen:
    def __init__(self, return_code, stdout, stderr):
        self.return_code = return_code
        self.stdout = stdout
        self.stderr = stderr

    def communicate(self):
        return self.stdout, self.stderr

    @property
    def returncode(self):
        return self.return_code

    def poll(self):
        return None

    def terminate(self):
        return None


class MockThread:
    def __init__(self, alive_status):
        self.alive_status = alive_status

    def start(self):
        return None

    def join(self, timeout=60):
        return None

    def is_alive(self):
        return self.alive_status


class MockNonBlockingStreamReader:
    @property
    def readline(self):
        return "test"


def test_no_blocking_output_to_log():
    with patch.object(ansible_executor, "Popen") as mock_popen, patch("threading.Thread") as mock_thread:
        mock_playbook = "playbook.yaml"
        mock_popen.return_value = MockProcess()
        mock_thread.return_value = MockProcess()
        execr = Executor(mock_playbook)
        execr.execute({"test": "test"})
        execr.write_stdout_to_log(progress=True)


@patch.object(cmdline.LogTool, "loop_write_timeout")
@patch.object(cmdline, "NonBlockingStreamReader")
def test_logtool_save(mock_stream_reader, mock_timeout):
    mock_process = MockPopen()
    mock_stream_reader.return_value = MockNonBlockingStreamReader()
    mock_timeout.return_value = True
    LogTool(mock_process, str(uuid.uuid4())).save(progress=True)
    

@patch("pexpect.spawn")
def test_ssh_timeout(mock_spawn):
    mock_child = MagicMock()
    mock_child.expect.return_value = 0
    mock_spawn.return_value = mock_child

    stdout = cmdline.remote_ssh("192.168.1.1", "user", "password", "ls -l")

    assert stdout is None


@patch("pexpect.spawn")
def test_ssh_new_key_and_password_failed(mock_spawn):
    mock_child = MagicMock()
    mock_child.expect.side_effect = [1, 0]  # simulate new key and password prompt
    mock_spawn.return_value = mock_child

    res = cmdline.remote_ssh("192.168.1.1", "user", "password", "ls -l")

    assert res is None


def test_run_cmd():
    with patch.object(cmdline.subprocess, "Popen") as mock_popen:
        mock_popen.return_value = MockSubPopen(0, "success", "")
        return_info = {"res": (None, "")}
        process_info = {"process": None}
        res = cmdline._run_cmd("lsblk -t", return_info=return_info, process_info=process_info)
        assert res["res"][0] == 0


def test_run_cmd_failed():
    with patch.object(cmdline.subprocess, "Popen") as mock_popen:
        mock_popen.return_value = MockSubPopen(1, "", "error")
        mock_uuid = str(uuid.uuid4())
        return_info = {"res": (None, "")}
        process_info = {"process": None}
        res = cmdline._run_cmd("lsblk -t", return_info=return_info, process_info=process_info)
        assert res["res"][0] == 1


def test_run_cmd_error():
    with patch.object(cmdline.subprocess, "Popen") as mock_popen:
        mock_popen.side_effect = OSError(errno.EPIPE, "piple error")
        return_info = {"res": (None, "")}
        res = cmdline._run_cmd("lsblk -t", return_info=return_info, process_info={"process": None})
        assert res["res"][0] == errno.EPIPE


def test_run_shell_script_with_return_output_timeout():
    with patch.object(cmdline.threading, "Thread") as mock_thread:
        mock_thread.return_value = MockThread(alive_status=True)
        res = cmdline.run_shell_script_with_return_output("lsblk -t")
        assert res[0] is None
