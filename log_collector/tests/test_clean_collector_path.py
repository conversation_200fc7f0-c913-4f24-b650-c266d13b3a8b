# Copyright (c) 2013-2024, SMARTX
# All rights reserved.


import time
import shutil  # noqa: F401,I100
import sys  # noqa: F401
from unittest.mock import patch  # noqa: I201

from pyfakefs import fake_filesystem

from log_collector.config.constants import (  # noqa: I101,I100,I202
    LOG_LOG_PATH,
    LOG_TEMP_PATH,
    LOG_METADATA_PATH,
    CLUSTER_ZIP_LOG_PREFIX,
)  # noqa: I101,I100,I202
from log_collector.shell.clean_collector_path import os, requests, DIR_SIZE_THRESHOLD  # noqa: I101
import log_collector.shell.clean_collector_path as clean_collector_path  # noqa: I100


response_1 = {
    "data": [
        {
            "task_uuid": "uuid_1",
            "collect_start_timestamp": "1",
            "status": "successful",
        },
        {
            "task_uuid": "uuid_4",
            "collect_start_timestamp": "4",
            "status": "successful",
        },
        {
            "task_uuid": "uuid_2",
            "collect_start_timestamp": "2",
            "status": "successful",
        },
        {
            "task_uuid": "uuid_5",
            "collect_start_timestamp": "5",
            "status": "successful",
        },
        {
            "task_uuid": "uuid_3",
            "collect_start_timestamp": "3",
            "status": "running",
        },
        {
            "task_uuid": "uuid_6",
            "collect_start_timestamp": "6",
            "status": "running",
        }
    ]
}


response_2 = {
    "data": [
        {
            "task_uuid": "uuid_1",
            "collect_start_timestamp": "1",
            "status": "successful",
        },
        {
            "task_uuid": "uuid_2",
            "collect_start_timestamp": "2",
            "status": "successful",
        },
        {
            "task_uuid": "uuid_3",
            "collect_start_timestamp": "3",
            "status": "running",
        }
    ]
}


response_3 = {"data": [{"task_uuid": "uuid_1", "collect_start_timestamp": "1", "status": "successful"}]}


def test_delete_file():
    fs = fake_filesystem.FakeFilesystem()
    fs.create_file(LOG_LOG_PATH + "/test_pattern.tar.gz")
    fs.create_file(LOG_LOG_PATH + "/pattern.tar.gz")
    fs.create_file(LOG_LOG_PATH + "/test.tar.gz")
    fs.create_dir(LOG_LOG_PATH + "/test_pattern_dir")
    fs.create_dir(LOG_LOG_PATH + "/pattern_dir")
    fs.create_dir(LOG_LOG_PATH + "/test_dir")

    os_module = fake_filesystem.FakeOsModule(fs)
    with patch("os.listdir", os_module.listdir), patch("os.path", os_module.path), patch(
        "os.remove", os_module.remove
    ), patch("shutil.rmtree", os_module.removedirs):

        clean_collector_path._delete_file(LOG_LOG_PATH, "pattern")
        assert not os.path.exists(LOG_LOG_PATH + "/test_pattern.tar.gz")
        assert not os.path.exists(LOG_LOG_PATH + "/pattern.tar.gz")
        assert os.path.exists(LOG_LOG_PATH + "/test.tar.gz")
        assert not os.path.exists(LOG_LOG_PATH + "/un_exist_test.tar.gz")

        assert not os.path.exists(LOG_LOG_PATH + "/test_pattern_dir")
        assert not os.path.exists(LOG_LOG_PATH + "/pattern_dir")
        assert os.path.exists(LOG_LOG_PATH + "/test_dir")
        assert not os.path.exists(LOG_LOG_PATH + "/un_exist_test_dir")


@patch.object(requests, "get")
def test_find_latest_metadata(request_get):

    request_get.return_value.json.return_value = response_1

    resp = clean_collector_path.find_latest_metadata()
    assert len(resp) == 3
    assert resp[0] == "uuid_4"
    assert resp[1] == "uuid_5"
    assert resp[2] == "uuid_6"


@patch.object(requests, "get")
def test_find_latest_metadata_failed(request_get):

    request_get.return_value.json.return_value = response_3
    resp = clean_collector_path.find_latest_metadata()
    assert resp == ["uuid_1"]


@patch.object(requests, "get")
def test_find_latest_metadata_error(request_get):

    request_get.side_effect = requests.exceptions.ConnectionError("Connection failed")
    resp = clean_collector_path.find_latest_metadata()
    assert resp == []


@patch.object(requests, "get")
def test_delete_metadata_and_log_file(request_get):
    request_get.return_value.json.return_value = response_1
    fs = fake_filesystem.FakeFilesystem()

    fs.create_file(LOG_LOG_PATH + "/uuid_1.tar.gz")
    fs.create_file(LOG_LOG_PATH + "/uuid_2.tar.gz")
    fs.create_file(LOG_LOG_PATH + "/uuid_3.tar.gz")
    fs.create_file(LOG_LOG_PATH + "/uuid_4.tar.gz")
    fs.create_file(LOG_LOG_PATH + "/uuid_5.tar.gz")
    fs.create_file(LOG_LOG_PATH + "/uuid_6.tar.gz")

    fs.create_file(LOG_METADATA_PATH + "/uuid_1")
    fs.create_file(LOG_METADATA_PATH + "/uuid_2")
    fs.create_file(LOG_METADATA_PATH + "/uuid_3")
    fs.create_file(LOG_METADATA_PATH + "/uuid_4")
    fs.create_file(LOG_METADATA_PATH + "/uuid_5")
    fs.create_file(LOG_METADATA_PATH + "/uuid_6")

    os_module = fake_filesystem.FakeOsModule(fs)
    with patch("os.listdir", os_module.listdir), patch("os.path", os_module.path), patch(
        "os.remove", os_module.remove
    ), patch("shutil.rmtree", os_module.removedirs):

        resp = clean_collector_path.find_latest_metadata()
        clean_collector_path.delete_metadata_and_log_file(resp)

        assert not os.path.exists(LOG_LOG_PATH + "/uuid_1.tar.gz")
        assert not os.path.exists(LOG_METADATA_PATH + "/uuid_1")
        assert not os.path.exists(LOG_LOG_PATH + "/uuid_2.tar.gz")
        assert not os.path.exists(LOG_METADATA_PATH + "/uuid_2")
        assert os.path.exists(LOG_LOG_PATH + "/uuid_3.tar.gz")
        assert os.path.exists(LOG_METADATA_PATH + "/uuid_3")
        assert os.path.exists(LOG_LOG_PATH + "/uuid_4.tar.gz")
        assert os.path.exists(LOG_METADATA_PATH + "/uuid_4")
        assert os.path.exists(LOG_LOG_PATH + "/uuid_5.tar.gz")
        assert os.path.exists(LOG_METADATA_PATH + "/uuid_5")
        assert os.path.exists(LOG_LOG_PATH + "/uuid_6.tar.gz")
        assert os.path.exists(LOG_METADATA_PATH + "/uuid_6")


@patch.object(requests, "get")
def test_delete_log_files_without_metadata(request_get):
    request_get.return_value.json.return_value = response_1
    fs = fake_filesystem.FakeFilesystem()

    fs.create_file(LOG_LOG_PATH + "/uuid_1.tar.gz")
    fs.create_file(LOG_LOG_PATH + "/uuid_2.tar.gz")
    fs.create_file(LOG_LOG_PATH + "/test_1.tar.gz")
    fs.create_file(LOG_LOG_PATH + "/test2.tar.gz")
    fs.create_file(LOG_LOG_PATH + "/{}_test1".format(CLUSTER_ZIP_LOG_PREFIX))
    fs.create_file(LOG_LOG_PATH + "/{}_test2".format(CLUSTER_ZIP_LOG_PREFIX))
    fs.create_file(LOG_LOG_PATH + "/{}_test3".format(CLUSTER_ZIP_LOG_PREFIX))
    fs.create_file(LOG_LOG_PATH + "/{}_test4".format(CLUSTER_ZIP_LOG_PREFIX))

    fs.create_dir(LOG_LOG_PATH + "/uuid_1_dir")
    fs.create_dir(LOG_LOG_PATH + "/test_dir")
    fs.create_dir(LOG_LOG_PATH + "/{}_test1_dir".format(CLUSTER_ZIP_LOG_PREFIX))
    fs.create_dir(LOG_LOG_PATH + "/{}_test2_dir".format(CLUSTER_ZIP_LOG_PREFIX))

    os_module = fake_filesystem.FakeOsModule(fs)
    with patch("os.listdir", os_module.listdir), patch("os.path", os_module.path), patch(
        "os.remove", os_module.remove
    ), patch("shutil.rmtree", os_module.removedirs):

        clean_collector_path.delete_log_files_without_metadata()

        assert os.path.exists(LOG_LOG_PATH + "/uuid_1.tar.gz")
        assert os.path.exists(LOG_LOG_PATH + "/uuid_2.tar.gz")
        assert not os.path.exists(LOG_LOG_PATH + "/test_1.tar.gz")
        assert not os.path.exists(LOG_LOG_PATH + "/test2.tar.gz")
        assert not os.path.exists(LOG_LOG_PATH + "/{}_test1".format(CLUSTER_ZIP_LOG_PREFIX))
        assert not os.path.exists(LOG_LOG_PATH + "/{}_test2".format(CLUSTER_ZIP_LOG_PREFIX))
        assert os.path.exists(LOG_LOG_PATH + "/{}_test3".format(CLUSTER_ZIP_LOG_PREFIX))
        assert os.path.exists(LOG_LOG_PATH + "/{}_test4".format(CLUSTER_ZIP_LOG_PREFIX))

        assert not os.path.exists(LOG_LOG_PATH + "/uuid_1_dir")
        assert not os.path.exists(LOG_LOG_PATH + "/test_dir")
        assert not os.path.exists(LOG_LOG_PATH + "/{}_test1_dir".format(CLUSTER_ZIP_LOG_PREFIX))
        assert not os.path.exists(LOG_LOG_PATH + "/{}_test2_dir".format(CLUSTER_ZIP_LOG_PREFIX))


def test_get_zipped_log_files():
    fs = fake_filesystem.FakeFilesystem()
    time_now = time.time()

    fs.create_file(LOG_LOG_PATH + "/uuid_1.tar.gz")
    fs.create_file(LOG_LOG_PATH + "/uuid_2.tar.gz")
    fs.create_file(LOG_LOG_PATH + "/{}_test1".format(CLUSTER_ZIP_LOG_PREFIX))
    fs.create_file(LOG_LOG_PATH + "/{}_test2".format(CLUSTER_ZIP_LOG_PREFIX))
    fs.create_file(LOG_LOG_PATH + "/{}_test3".format(CLUSTER_ZIP_LOG_PREFIX))
    fs.create_file(LOG_LOG_PATH + "/{}_test4".format(CLUSTER_ZIP_LOG_PREFIX))
    fs.create_dir(LOG_LOG_PATH + "/{}_test3_dir".format(CLUSTER_ZIP_LOG_PREFIX))
    fs.create_dir(LOG_LOG_PATH + "/{}_test4_dir".format(CLUSTER_ZIP_LOG_PREFIX))

    os_module = fake_filesystem.FakeOsModule(fs)
    file_open = fake_filesystem.FakeFileOpen(fs)  # noqa: F841
    with patch("os.listdir", os_module.listdir), patch("os.path", os_module.path), patch(
        "os.remove", os_module.remove
    ), patch("os.utime", os_module.utime), patch("shutil.rmtree", os_module.removedirs):

        os.utime(LOG_LOG_PATH + "/{}_test2".format(CLUSTER_ZIP_LOG_PREFIX), (time_now, time_now))

        resp = clean_collector_path._get_zipped_log_files()

        assert len(resp) == 4
        assert "{}_test1".format(CLUSTER_ZIP_LOG_PREFIX) in resp
        assert "{}_test3".format(CLUSTER_ZIP_LOG_PREFIX) in resp
        assert "{}_test4".format(CLUSTER_ZIP_LOG_PREFIX) in resp
        assert "{}_test2".format(CLUSTER_ZIP_LOG_PREFIX) in resp


@patch.object(requests, "delete")
@patch.object(requests, "get")
def test_ensure_log_size_1(request_get, delete_mock):
    def delete_side_effect(args_1):
        r_pos = args_1.rfind("/")
        uuid_delete = args_1[r_pos + 1 :]
        for f in os.listdir(LOG_LOG_PATH):
            if f.find(uuid_delete) != -1:
                os.remove(LOG_LOG_PATH + os.sep + f)

    request_get.return_value.json.return_value = response_2
    fs = fake_filesystem.FakeFilesystem()
    delete_mock.side_effect = delete_side_effect

    fs.create_file(LOG_LOG_PATH + "/uuid_1.tar.gz", st_size=1 * 1024 * 1024 * 1024)
    fs.create_file(LOG_LOG_PATH + "/uuid_2.tar.gz", st_size=1 * 1024 * 1024 * 1024 + 1000)
    fs.create_file(LOG_LOG_PATH + "/uuid_3.tar.gz", st_size=1 * 1024 * 1024 * 1024)
    fs.create_file(LOG_LOG_PATH + "/{}_test1".format(CLUSTER_ZIP_LOG_PREFIX), st_size=1000)
    fs.create_file(LOG_LOG_PATH + "/{}_test2".format(CLUSTER_ZIP_LOG_PREFIX), st_size=1000)
    fs.create_file(LOG_LOG_PATH + "/{}_test3".format(CLUSTER_ZIP_LOG_PREFIX), st_size=1000)
    os_module = fake_filesystem.FakeOsModule(fs)
    with patch("os.listdir", os_module.listdir), patch("os.path", os_module.path), patch(
        "os.remove", os_module.remove
    ), patch("shutil.rmtree", os_module.removedirs):

        clean_collector_path.ensure_log_size(DIR_SIZE_THRESHOLD)
        assert os.path.exists(LOG_LOG_PATH + "/uuid_2.tar.gz")
        assert os.path.exists(LOG_LOG_PATH + "/uuid_3.tar.gz")
        assert not os.path.exists(LOG_LOG_PATH + "/uuid_1.tar.gz")
        assert not os.path.exists(LOG_LOG_PATH + "/{}_test1".format(CLUSTER_ZIP_LOG_PREFIX))
        assert not os.path.exists(LOG_LOG_PATH + "/{}_test2".format(CLUSTER_ZIP_LOG_PREFIX))
        assert not os.path.exists(LOG_LOG_PATH + "/{}_test3".format(CLUSTER_ZIP_LOG_PREFIX))


@patch.object(requests, "delete")
@patch.object(requests, "get")
def test_ensure_log_size_2(request_get, delete_mock):
    def delete_side_effect(args_1):
        r_pos = args_1.rfind("/")
        uuid_delete = args_1[r_pos + 1 :]
        for f in os.listdir(LOG_LOG_PATH):
            if f.find(uuid_delete) != -1:
                os.remove(LOG_LOG_PATH + os.sep + f)

    request_get.return_value.json.return_value = response_2
    fs = fake_filesystem.FakeFilesystem()
    delete_mock.side_effect = delete_side_effect

    fs.create_file(LOG_LOG_PATH + "/uuid_1.tar.gz", st_size=1 * 1024 * 1024 * 1024)
    fs.create_file(LOG_LOG_PATH + "/uuid_2.tar.gz", st_size=1000)
    fs.create_file(LOG_LOG_PATH + "/uuid_3.tar.gz", st_size=1000)
    fs.create_file(LOG_LOG_PATH + "/{}_test1".format(CLUSTER_ZIP_LOG_PREFIX), st_size=1 * 1024 * 1024 * 1024)
    fs.create_file(LOG_LOG_PATH + "/{}_test2".format(CLUSTER_ZIP_LOG_PREFIX), st_size=1 * 1024 * 1024 * 1024)
    fs.create_file(LOG_LOG_PATH + "/{}_test3".format(CLUSTER_ZIP_LOG_PREFIX), st_size=1 * 1024 * 1024 * 1024)

    os_module = fake_filesystem.FakeOsModule(fs)
    with patch("os.listdir", os_module.listdir), patch("os.path", os_module.path), patch(
        "os.remove", os_module.remove
    ), patch("shutil.rmtree", os_module.removedirs):

        clean_collector_path.ensure_log_size(DIR_SIZE_THRESHOLD)
        assert os.path.exists(LOG_LOG_PATH + "/uuid_2.tar.gz")
        assert os.path.exists(LOG_LOG_PATH + "/uuid_3.tar.gz")
        assert os.path.exists(LOG_LOG_PATH + "/uuid_1.tar.gz")
        assert not os.path.exists(LOG_LOG_PATH + "/{}_test1".format(CLUSTER_ZIP_LOG_PREFIX))
        assert not os.path.exists(LOG_LOG_PATH + "/{}_test2".format(CLUSTER_ZIP_LOG_PREFIX))
        assert os.path.exists(LOG_LOG_PATH + "/{}_test3".format(CLUSTER_ZIP_LOG_PREFIX))


def test_clean_tmp_dir():
    fs = fake_filesystem.FakeFilesystem()
    time_now = time.time()
    fs.create_file(LOG_TEMP_PATH + "/test_1.py", contents="test")
    fs.create_dir(LOG_TEMP_PATH + "/test_dir1")
    fs.create_file(LOG_TEMP_PATH + "/test_dir1/file", contents="test")

    fs.create_file(LOG_TEMP_PATH + "/test_2.py", contents="test")
    fs.create_dir(LOG_TEMP_PATH + "/test_dir2")

    fs.create_file(LOG_TEMP_PATH + "/test_3.py", contents="test")
    fs.create_dir(LOG_TEMP_PATH + "/test_dir3")
    fs.create_file(LOG_TEMP_PATH + "/test_dir3/file", contents="test")
    os_module = fake_filesystem.FakeOsModule(fs)
    with patch("os.listdir", os_module.listdir), patch("os.path", os_module.path), patch(
        "os.remove", os_module.remove
    ), patch("shutil.rmtree", os_module.removedirs), patch("os.utime", os_module.utime):

        os.utime(LOG_TEMP_PATH + "/test_1.py", (time_now - 3800, time_now - 3800))
        os.utime(LOG_TEMP_PATH + "/test_dir1", (time_now - 3800, time_now - 3800))
        os.utime(LOG_TEMP_PATH + "/test_3.py", (time_now - 3500, time_now - 3500))
        os.utime(LOG_TEMP_PATH + "/test_dir3", (time_now - 3500, time_now - 3500))

        clean_collector_path.clean_tmp_dir()

        assert not os.path.exists(LOG_TEMP_PATH + "/test_1.py")
        # assert not os.path.exists(LOG_TEMP_PATH + '/test_dir1')
        # assert not os.path.exists(LOG_TEMP_PATH + '/test_dir1/file')

        assert os.path.exists(LOG_TEMP_PATH + "/test_2.py")
        assert os.path.exists(LOG_TEMP_PATH + "/test_dir2")

        assert os.path.exists(LOG_TEMP_PATH + "/test_3.py")
        assert os.path.exists(LOG_TEMP_PATH + "/test_dir3")
