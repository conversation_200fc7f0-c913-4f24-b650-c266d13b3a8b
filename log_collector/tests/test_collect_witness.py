# Copyright (c) 2013-2018, SMARTX
# All rights reserved.
from unittest.mock import patch  # noqa: E402

import log_collector.utils.metro_availability as metro_availability  # noqa: E402,I202
import log_collector.utils.inventory as inventory  # noqa: E402,I100
from log_collector.utils.metro_availability import get_witness_ip, os  # noqa: E402,F401
from log_collector.config.constants import INVENTORY_FILE  # noqa: E402,I100
from log_collector.utils.inventory import Inventory, ZbsConf  # noqa: E402

from pyfakefs import fake_filesystem

zbs_conf = """[network]
data_ip=***********
heartbeat_ip=***********
vm_ip=**************
web_ip=**************

[cluster]
role=master
members=***********,***********,***********
zookeeper=***********:2181,***********:2181,***********:2181
mongo=***********:27017,***********:27017,***********:27017
cluster_mgt_ips = **************,**************,**************,**************
cluster_storage_ips = ***********,***********,***********,***********
"""


def test_get_witness_ip():
    fs = fake_filesystem.FakeFilesystem()
    fs.create_file("/etc/zbs/metro_availability", contents="true")
    fs.create_file("/etc/zbs/witness", contents="***********:27017")

    os_module = fake_filesystem.FakeOsModule(fs)
    file_open = fake_filesystem.FakeFileOpen(fs)
    with patch("os.listdir", os_module.listdir), patch("os.path", os_module.path), patch(
        "os.remove", os_module.remove
    ), patch("shutil.rmtree", os_module.removedirs), patch.object(metro_availability, "open", file_open):

        witness_ip = get_witness_ip()
        assert witness_ip == "***********"


def test_get_witness_ip_not_exist():
    fs = fake_filesystem.FakeFilesystem()
    fs.create_file("/etc/zbs/metro_availability", contents="false")
    fs.create_file("/etc/zbs/witness", contents="***********:27017")

    os_module = fake_filesystem.FakeOsModule(fs)
    file_open = fake_filesystem.FakeFileOpen(fs)
    with patch("os.listdir", os_module.listdir), patch("os.path", os_module.path), patch(
        "os.remove", os_module.remove
    ), patch("shutil.rmtree", os_module.removedirs), patch.object(metro_availability, "open", file_open):

        witness_ip = get_witness_ip()
        assert witness_ip is None


def test_parse_node_list():
    fs = fake_filesystem.FakeFilesystem()
    fs.create_file("/etc/zbs/metro_availability", contents="true")
    fs.create_file("/etc/zbs/witness", contents="***********:27017")
    fs.create_file("/etc/zbs/zbs.conf", contents="")

    os_module = fake_filesystem.FakeOsModule(fs)
    file_open = fake_filesystem.FakeFileOpen(fs)
    with patch("os.listdir") as mock_listdir:
        mock_listdir.return_value = []
        from log_collector.cmd.main import parse_node_list

        with patch("os.listdir", os_module.listdir), patch("os.path", os_module.path), patch(
            "os.remove", os_module.remove
        ), patch("shutil.rmtree", os_module.removedirs), patch.object(metro_availability, "open", file_open):

            node_list = parse_node_list(None)
            assert node_list == ["***********"]

            with file_open("/etc/zbs/metro_availability", "w") as f:
                f.write("false")
            node_list = parse_node_list(None)
            assert node_list == []


def test_generate_inventory():
    fs = fake_filesystem.FakeFilesystem()
    fs.create_file("/etc/zbs/metro_availability", contents="true")
    fs.create_file("/etc/zbs/witness", contents="***********:27017")
    fs.create_file("/etc/zbs/zbs.conf", contents=zbs_conf)
    fs.create_file(INVENTORY_FILE)

    os_module = fake_filesystem.FakeOsModule(fs)
    file_open = fake_filesystem.FakeFileOpen(fs)
    with patch("os.listdir", os_module.listdir), patch("os.path", os_module.path), patch(
        "os.remove", os_module.remove
    ), patch("shutil.rmtree", os_module.removedirs), patch.object(metro_availability, "open", file_open), patch.object(
        inventory, "open", file_open
    ), patch.object(
        ZbsConf, "get_storage_ips", return_value=["***********"]
    ), patch.object(
        ZbsConf, "get_mgt_ips", return_value=["**************"]
    ), patch.object(
        inventory, "can_ping", return_value=True
    ):

        Inventory.generate_inventory(["**************", "***********"])
        with file_open(INVENTORY_FILE, "r") as f:
            file_str = f.read()
            assert (
                "*********** ansible_ssh_user=admin ansible_ssh_private_key_"
                "file=/home/<USER>/.ssh/admin_id_rsa" in file_str
            )
            assert (
                "*********** ansible_ssh_user=admin ansible_ssh_private_"
                "key_file=/home/<USER>/.ssh/admin_id_rsa" in file_str
            )
