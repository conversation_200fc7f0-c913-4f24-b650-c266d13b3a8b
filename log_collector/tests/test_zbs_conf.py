# -*- coding: utf-8 -*-
# Copyright (c) 2013-2023, SMARTX
# All rights reserved.

from unittest.mock import patch

from log_collector.utils.zbs_conf import ZbsConf


def test_get_current_storage_ip():
    with patch("configparser.ConfigParser.get") as mock_get:
        mock_get.side_effect = AttributeError("No option network in section")
        res = ZbsConf.get_current_storage_ip()
        assert res is None
