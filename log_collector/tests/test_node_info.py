# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
import uuid

from pyfakefs import fake_filesystem
from unittest.mock import MagicMock, patch

import log_collector.node.node_info as node_info
from log_collector.node.node_info import CollectInfo


@patch.object(node_info, "run_shell_script_with_return_output")
@patch("log_collector.node.node_info.CollectInfo._format_save")
@patch("log_collector.node.node_info.CollectInfo._split")
def test_collect_disk_info(mock_file, mock_save, mock_execute):
    mock_file.return_value = "test.txt"
    mock_save.return_value = None
    mock_execute.return_value = (0, "R+   grep -E --color=auto lsblk|blkid|blockdev|smartctl|parted|fdisk")
    mock_uuid = str(uuid.uuid4())
    collect_info = CollectInfo(mock_uuid, disk_check=True)
    collect_info.collect_disk_info()
    assert mock_file.call_count == 1


@patch.object(node_info.CollectInfo, "_format_save", return_value=None)
@patch.object(node_info.CollectInfo, "execute_cmd", return_value=(0, "success"))
@patch.object(node_info.ethtool, "get_devices", return_value=["test1"])
@patch("log_collector.node.node_info.CollectInfo._split", return_value="test.txt")
def test_collect_nic_info(*args):
    mock_uuid = str(uuid.uuid4())
    ci = CollectInfo(mock_uuid)
    ci.collect_nic_info()


def test_collect_disk_info_without_disk_check():
    mock_uuid = str(uuid.uuid4())
    collect_info = CollectInfo(mock_uuid)
    collect_info.collect_disk_info()


@patch.object(node_info.CollectInfo, "_disk_process_hang", return_value=True)
def test_collect_disk_info_without_disk_process_hang(*args):
    mock_uuid = str(uuid.uuid4())
    collect_info = CollectInfo(mock_uuid, disk_check=True)
    collect_info.collect_disk_info()


@patch("os.path.exists", return_value=True)
@patch.object(node_info.shutil, "copytree")
def test_collect_services_config(*args):
    mock_uuid = str(uuid.uuid4())
    collect_info = CollectInfo(mock_uuid)
    collect_info.collect_services_config()


@patch("log_collector.node.node_info.CollectInfo._format_save")
@patch.object(node_info.CollectInfo, "execute_cmd")
@patch("os.path.exists")
def test_collect_services_status(mock_path_exists, mock_execute_cmd, mock_format_save):
    mock_uuid = str(uuid.uuid4())
    mock_path_exists.return_value = True
    mock_execute_cmd.return_value = (0, "success")
    mock_format_save.return_value = None
    collect_info = CollectInfo(mock_uuid)
    collect_info.collect_services_status()

    mock_path_exists.return_value = False
    collect_info.collect_services_status()


@patch("os.path.exists")
def test_format_save(mock_exists, ):
    fs = fake_filesystem.FakeFilesystem()
    fs.create_file("/tmp/testfile")
    file_open = fake_filesystem.FakeFileOpen(fs)

    with patch.object(node_info, "open", file_open):
        mock_uuid = str(uuid.uuid4())
        collect_info = CollectInfo(mock_uuid)
        collect_info._format_save("test_command", "test command result", "/tmp/testfile")


@patch.object(node_info.CollectInfo, "_format_save", return_value=None)
@patch.object(node_info.CollectInfo, "execute_cmd", return_value=(0, "smartx-ntpm"))
@patch("log_collector.node.node_info.CollectInfo._split", return_value="test.txt")
def test_collect_system_status_with_ntpm(*args):
    mock_uuid = str(uuid.uuid4())
    ci = CollectInfo(mock_uuid)
    ci.collect_system_status()


@patch.object(node_info.CollectInfo, "_format_save", return_value=None)
@patch.object(node_info.CollectInfo, "execute_cmd", return_value=(0, ""))
@patch("log_collector.node.node_info.CollectInfo._split", return_value="test.txt")
def test_collect_system_status_with_ntpd(*args):
    mock_uuid = str(uuid.uuid4())
    ci = CollectInfo(mock_uuid)
    ci.collect_system_status()


@patch("concurrent.futures.ThreadPoolExecutor")
def test_sos_collect(*args):
    mock_executor = MagicMock()
    mock_executor.submit.return_value = None
    mock_executor.shutdown.return_value = None

    mock_uuid = str(uuid.uuid4())
    collect_info = CollectInfo(mock_uuid, sos_plugin="hardware,nginx")
    collect_info.collect_sos_report()


@patch("log_collector.node.node_info.CollectInfo.collect_sos_report")
@patch("log_collector.node.node_info.CollectInfo.collect_installed_rpm")
@patch("log_collector.node.node_info.CollectInfo.collect_kernel_info")
@patch("log_collector.node.node_info.CollectInfo.collect_services_config")
@patch("log_collector.node.node_info.CollectInfo.collect_deploy_config")
@patch("log_collector.node.node_info.CollectInfo.collect_disk_info")
@patch("log_collector.node.node_info.CollectInfo.collect_nic_info")
@patch("log_collector.node.node_info.CollectInfo.collect_services_status")
@patch("log_collector.node.node_info.CollectInfo.collect_system_status")
@patch("log_collector.node.node_info.CollectInfo.collect_zbs_info")
def test_collect_all(mock_collect_zbs_info, mock_collect_system_status, mock_collect_services_status,
                     mock_collect_nic_info, mock_collect_disk_info, mock_collect_deploy_config, 
                     mock_collect_services_config, mock_collect_kernel_info, mock_collect_installed_rpm, 
                     mock_collect_sos_report):
    collector = CollectInfo(uuid="dummy_uuid", node_info=True, sos=True)

    collector.collect_all()

    mock_collect_sos_report.assert_called_once()
    mock_collect_installed_rpm.assert_called_once()
    mock_collect_kernel_info.assert_called_once()
    mock_collect_services_config.assert_called_once()
    mock_collect_deploy_config.assert_called_once()
    mock_collect_disk_info.assert_called_once()
    mock_collect_nic_info.assert_called_once()
    mock_collect_services_status.assert_called_once()
    mock_collect_system_status.assert_called_once()
    mock_collect_zbs_info.assert_called_once()
