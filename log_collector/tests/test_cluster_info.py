# -*- coding: utf-8 -*-
# Copyright (c) 2013-2024, SMARTX
# All rights reserved.

from unittest.mock import Mock, patch

from log_collector.utils import cluster_info


def test_get_cluster_name():
    with patch("log_collector.utils.cluster_info.tuna.Client") as MockClient:
        mock_rest_client = Mock()
        mock_rest_client.get.return_value = {"data": {"cluster_name": "test name"}}

        MockClient.return_value._rest_client = mock_rest_client

        assert cluster_info.get_cluster_name() == "test_name"
