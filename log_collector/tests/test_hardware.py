# Copyright (c) 2013-2024, SMARTX
# All rights reserved.

from unittest.mock import patch

from log_collector.utils import hardware


def test_get_vmware_physical_nic_info():
    with patch.object(
        hardware.VsphereAccountService, "get_by_node_uuid"
    ) as mock_account, patch.object(hardware, "remote_ssh") as mock_remote_output:
        mock_account.return_value = {
            "host": "test-host",
            "port": 1234,
            "user": "test-username",
            "password": "test-password",
        }
        mock_remote_output.side_effect = [
            (
                "Name    PCI          Driver      Link Speed      Duplex MAC Address     "
                "  MTU    Description                   \nvmnic0  0000:03:00.0 igbn      "
                "  Up   1000Mbps   Full   00:25:90:fc:fb:42 1500   Intel Corporation "
                "I350 Gigabit Network Connection\n"
            ),
            "Driver: igbn\nFirmware Version: 1.63.0:0x80000a05\n",
        ]
        result = hardware.get_vmware_physical_nic_info()
        model = "Intel Corporation I350 Gigabit Network Connection"
        detail_info = "Driver: igbn\nFirmware Version: 1.63.0:0x80000a05\n"
        assert result == f"model: {model}:\n{detail_info}\n"


def test_get_storage_controller():
    with patch.object(hardware, "run_shell_script_with_return_output") as mock_shell_output:
        mock_shell_output.side_effect = [
            (1, "test_error"),
            (0, "Controller Count = 1"),
            (0, "Model = test_model\nFirmware Version = test_version"),
        ]
        res = hardware.get_storage_controller()
        assert len(res) == 1
        assert res[0] == "name: test_model, version: test_version"
