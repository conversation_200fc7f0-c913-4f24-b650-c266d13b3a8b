# Copyright (c) 2013-2024, SMARTX
# All rights reserved.

import unittest
from unittest.mock import patch
from log_collector.cmd import collect_local_log


class TestCollectLogs(unittest.TestCase):
    @patch.object(collect_local_log.NodeCollector, "collect_log")
    def test_collect_logs_valid_json(self, mock_node_collector):
        group_service_pattern_json = '{"service": "example"}'

        collect_local_log.collect_logs("1609459200", "1609545600", "uuid123", "100", group_service_pattern_json)

        mock_node_collector.assert_called_once_with(1609459200, 1609545600)

    @patch.object(collect_local_log.NodeCollector, "collect_log")
    def test_collect_logs_no_json_string(self, mock_node_collector):
        group_service_pattern = {"service": "example"}

        collect_local_log.collect_logs("1609459200", "1609545600", "uuid123", "100", group_service_pattern)

        mock_node_collector.assert_called_once_with(1609459200, 1609545600)


if __name__ == "__main__":
    unittest.main()
