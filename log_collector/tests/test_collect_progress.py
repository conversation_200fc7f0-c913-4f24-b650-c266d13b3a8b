# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
import uuid

from unittest.mock import patch

from pyfakefs import fake_filesystem

from log_collector.utils.ansible_executor import Executor
import log_collector.utils.collect_progress as collect_progress
from log_collector.utils.collect_progress import (
    get_process,
    get_process_status,
    init_process_status,
    update_process_status,
    write_process,
)


mock_metadata = {
    "task_uuid": str(uuid.uuid4()),
    "node_list": ["192.168.31.116"],
    "collect_start_timestamp": 1621210916,
    "collect_end_timestamp": None,
    "start_timestamp": 1621210916,
    "end_timestamp": 1621225316,
    "path": "/log_path",
    "size": 0,
    "status": "running",
    "owner": "0.0.0.1",
    "progress": "10%",
}


mock_process = {"COLLECT_SLAVE": {"192.168.31.116": 0.3}}


class MockPopen:
    def poll(self):
        return None

    def kill(self):
        return None


@patch.object(collect_progress, "get_process", return_value=mock_process)
@patch.object(collect_progress.MetaData, "write_metadata_file", return_value=None)
def test_write_process(*args):
    mock_uuid = str(uuid.uuid4())
    write_process(
        process=0.1,
        progress_bar=None,
        task_uuid=mock_uuid,
        status="running",
        metadata=mock_metadata,
        include_controller=True,
        send_log_to_controller=True,
    )


def test_get_process_with_init_time():
    with patch.object(collect_progress, "get_process_status") as mock_process_status, patch(
            "requests.get") as mock_get, patch.object(
        collect_progress.ZbsConf, "check_local_ip", return_value=True), patch.object(
        collect_progress, "update_process_status"
    ) as mock_update_process_status:
        mock_process_status.return_value = {}
        mock_get.return_value.status_code = 200
        mock_get.return_value.json.return_value = {
            "data": [
                {
                    "host_ip": "*******",
                    "progress": "2021-06-29 17:53:24,802 - node_info - INFO - 192_168_27_71 - Collect zbs info.",
                }
            ]
        }
        mock_update_process_status.return_value = None
        res = get_process("mock-uuid-1")
        res = get_process("mock-uuid-1")
        assert res["COLLECT_CONTROLLER"]["*******"] < 0.3


def test_get_process_with_collect_services():
    with patch.object(collect_progress, "get_process_status") as mock_process_status, patch(
        "requests.get") as mock_get, patch.object(
        collect_progress.ZbsConf, "check_local_ip", return_value=True), patch.object(
        collect_progress, "update_process_status"
    ) as mock_update_process_status:
        mock_process_status.return_value = {"COLLECT_CONTROLLER": {"*******": 0.31}}
        mock_get.return_value.status_code = 200
        mock_get.return_value.json.return_value = {
            "data": [
                {
                    "host_ip": "*******",
                    "progress": "#LOG_COLLECTOR_PROGRESS#stage=COLLECT_NODE#host_ip=*******#progress=0.328571428#",
                }
            ]
        }
        mock_update_process_status.return_value = None
        res = get_process("mock-uuid-1")
        assert res["COLLECT_CONTROLLER"]["*******"] == 0.328571428


@patch("log_collector.utils.collect_progress.os.path.exists", return_value=True)
def test_get_process_status(*args):
    task_uuid = str(uuid.uuid4())
    fs = fake_filesystem.FakeFilesystem()
    path = "/usr/share/tuna/log_collector/tmp/tmp_process_{}".format(task_uuid)
    fs.create_file(path, contents="test")

    file_open = fake_filesystem.FakeFileOpen(fs)
    with patch.object(collect_progress, "open", file_open):
        res = get_process_status(task_uuid)
        assert res == {}


def test_update_process_status():
    task_uuid = str(uuid.uuid4())
    fs = fake_filesystem.FakeFilesystem()
    path = "/usr/share/tuna/log_collector/tmp/tmp_process_{}".format(task_uuid)
    fs.create_file(path, contents="test")

    file_open = fake_filesystem.FakeFileOpen(fs)
    with patch.object(collect_progress, "open", file_open):
        update_process_status("testtest", task_uuid)


@patch("os.makedirs", return_value=None)
@patch("os.path.exists", return_value=False)
def test_init_process_status(*args):
    task_uuid = str(uuid.uuid4())
    fs = fake_filesystem.FakeFilesystem()
    path = "/usr/share/tuna/log_collector/tmp/tmp_process_{}".format(task_uuid)
    fs.create_file(path, contents="{}")
    file_open = fake_filesystem.FakeFileOpen(fs)
    with patch.object(collect_progress, "open", file_open):
        init_process_status(task_uuid)


def test_get_status():
    exect = Executor("test.yaml")
    mp = MockPopen()
    exect.process = mp
    exect.task_start_time = 0
    exect.get_status()
