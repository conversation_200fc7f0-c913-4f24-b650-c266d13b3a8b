# Copyright (c) 2013-2021, SMARTX
# All rights reserved.

from unittest.mock import Mock, patch

from inotify_simple import flags
from pyfakefs import fake_filesystem

from log_collector.config.constants import ZBS_CONF_FILE
from log_collector.utils import cluster_monitor
from log_collector.utils.cluster_monitor import ClusterInotify

zbs_conf = """[network]
data_ip=***********
heartbeat_ip=***********
vm_ip=**************
web_ip=**************

[cluster]
role=master
members=***********,***********,***********
zookeeper=***********:2181,***********:2181,***********:2181
mongo=***********:27017,***********:27017,***********:27017
cluster_mgt_ips = **************,**************,**************,**************
cluster_storage_ips = ***********,***********,***********,***********
"""


class MockINotify:
    def add_watch(self, path, mask):
        return None

    def read(self):
        event_mock = Mock()
        event_mock.mask.return_value = [flags.MODIFY]
        return [event_mock]


def test_cluster_inotify():
    with patch.object(cluster_monitor.ClusterInotify, "_active") as mock_active, patch.object(
        cluster_monitor, "INotify"
    ) as mock_inotify:
        mock_active.return_value = None
        mock_inotify.return_value = MockINotify()
        cluster_monitor.ClusterInotify()


def test_cluster_up_to_date():
    fs = fake_filesystem.FakeFilesystem()
    fs.create_file(ZBS_CONF_FILE, contents=zbs_conf)

    with patch.object(cluster_monitor, "get_witness_ip") as witness_mock, patch.object(
        cluster_monitor.configparser.SafeConfigParser, "get"
    ) as mock_zbs_config, patch.object(cluster_monitor.ClusterInotify, "_active") as mock_active, patch.object(
        cluster_monitor, "INotify"
    ) as mock_inotify:
        witness_mock.return_value = None
        mock_zbs_config.return_value = "**************"
        mock_active.return_value = None
        mock_inotify.return_value = MockINotify()

        ClusterInotify().cluster_up_to_date()
