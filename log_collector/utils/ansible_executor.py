# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
import os.path
import subprocess
from subprocess import PIPE, Popen
import threading
import time

from log_collector.config.constants import DEFAULT_ANSIBLE_PATH, INVENTORY_FILE
from log_collector.config.logger import logger
from log_collector.utils.cmdline import no_blocking_output_to_log


class Executor:
    execute_ansible_timeout = 1200  # depend on collect ansible execute retry 120 times

    def __init__(self, playbook, task_uuid=None):
        self.process = None
        self.playbook = playbook
        self.task_uuid = task_uuid
        self.inventory = INVENTORY_FILE
        self.task_start_time = int(time.time())

        logger.info(
            "log_collector.utils.ansible_executor.__init__, playbook = {}, inventory = {}".format(
                playbook, INVENTORY_FILE
            )  # E501
        )

    def execute(self, args_str):
        cmd = "cd {}; {} -vvv -i {} {} --extra-vars '{}'".format(
            os.path.dirname(INVENTORY_FILE), DEFAULT_ANSIBLE_PATH, self.inventory, self.playbook, args_str
        )
        logger.info("log_collector.utils.ansible_executor.execute, cmd = %s" % cmd)
        self.process = Popen(cmd, stdout=PIPE, stderr=PIPE, bufsize=1, shell=True, text=True)

    def terminate(self):
        if self.process and self.process.poll() is None:  # Check if the process is still running
            self.process.terminate()
            logger.info(f"Terminated ansible process for task {self.task_uuid}")
            try:
                self.process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                print("Process didn't exit gracefully, forcing kill it.")
                self.process.kill()

    def write_stdout_to_log(self, progress=False):
        th = threading.Thread(target=no_blocking_output_to_log, args=(self.process, self.task_uuid, logger, progress))
        th.daemon = True
        th.start()

    def get_status(self):
        timetokill = int(time.time()) - self.task_start_time > self.execute_ansible_timeout
        if self.process is not None:
            return_code = self.process.poll()
            if return_code is None:
                if not timetokill:
                    return "running"
                else:
                    try:
                        self.process.kill()
                        return "failed"
                    except Exception as e:
                        logger.exception("try to kill log-collector job failed: {}".format(e))
            elif return_code != 0:
                return "failed"
            else:
                return "successful"
        else:
            return None
