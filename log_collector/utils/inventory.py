# Copyright (c) 2013-2018, SMARTX
# All rights reserved.


from log_collector.config.constants import INVENTORY_FILE, INVENTORY_IP_ATTACHMENT
from log_collector.config.logger import logger
from log_collector.utils.cmdline import can_ping
from log_collector.utils.exceptions import ZbsConfException
from log_collector.utils.metro_availability import get_witness_ip
from log_collector.utils.zbs_conf import ZbsConf

connected_host_info = """
host connected
    host management ip: {host_mgt_ip}
    host storage ip:    {host_storage_ip}"""


connected_witness_info = """
witness connected
    witness ip: {witness_ip}"""


failed_connected_host_info = """
host failed to connect:
    host management ip: {host_mgt_ip}
    host storage ip:    {host_storage_ip}"""


class Inventory:
    def __init__(self):
        pass

    @classmethod
    def write_inventory_file(cls, ip_list):
        conf_str = "[cluster]\n"
        for ip in ip_list:
            host_info = str(ip) + INVENTORY_IP_ATTACHMENT
            conf_str += host_info + "\n"

        with open(INVENTORY_FILE, "w") as f:
            f.write(conf_str)

    @classmethod
    def generate_inventory(cls, node_list):
        ip_list = []

        witness_ip = get_witness_ip()
        if witness_ip in node_list:
            if can_ping(witness_ip):
                logger.info(connected_witness_info.format(witness_ip=witness_ip))
                print(connected_witness_info.format(witness_ip=witness_ip))
                ip_list.append(witness_ip)
                node_list.remove(witness_ip)

        # ips = [x for x in node_list if not ZbsConf.check_local_ip(x)]
        storage_ips = ZbsConf.get_storage_ips(node_list)
        mgt_ips = ZbsConf.get_mgt_ips(node_list)

        if len(node_list) != len(storage_ips):
            raise ZbsConfException("cannot find ip conf, node_list=%s" % node_list)

        for i in range(len(node_list)):
            if can_ping(storage_ips[i]):
                ip_list.append(storage_ips[i])
                logger.info(connected_host_info.format(host_mgt_ip=mgt_ips[i], host_storage_ip=storage_ips[i]))
                print(connected_host_info.format(host_mgt_ip=mgt_ips[i], host_storage_ip=storage_ips[i]))
            elif can_ping(mgt_ips[i]):
                ip_list.append(mgt_ips[i])
                logger.info(connected_host_info.format(host_mgt_ip=mgt_ips[i], host_storage_ip=storage_ips[i]))
                print(connected_host_info.format(host_mgt_ip=mgt_ips[i], host_storage_ip=storage_ips[i]))
            else:
                logger.info(failed_connected_host_info.format(host_mgt_ip=mgt_ips[i], host_storage_ip=storage_ips[i]))
                print(failed_connected_host_info.format(host_mgt_ip=mgt_ips[i], host_storage_ip=storage_ips[i]))

        Inventory.write_inventory_file(ip_list)
