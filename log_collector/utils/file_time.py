# Copyright (c) 2013-2018, SMARTX
# All rights reserved.


import re
import subprocess


class File:
    def __init_(self):
        pass

    @classmethod
    def get_create_time(cls, filename):
        inode_num = File._get_inode_num(filename)
        return File._get_time(inode_num)

    @classmethod
    def _execute(cls, cmd):
        try:
            process = subprocess.Popen(
                cmd, shell=True, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
            )
            cmd_out = process.stdout.read()
            return cmd_out
        except (OSError, ValueError, ChildProcessError):
            return None

    @classmethod
    def _get_root_disk(cls):
        try:
            cmd_out = File._execute("df -h /")
            if cmd_out.find("dev") != -1:
                split_str = cmd_out.split(" ")
                disks = [x.strip() for x in split_str if x.find("/dev") != -1]
                return disks[0].split("\n")[1]
        except IndexError:
            return None

    @classmethod
    def _get_inode_num(cls, filename):
        try:
            cmd_out = File._execute("ls -i %s" % filename)
            if re.match(r"[\d]+[\s\S]+%s" % filename, cmd_out):
                return int(cmd_out.split(" ")[0])
            else:
                return None
        except IndexError:
            return None

    @classmethod
    def _get_time(cls, inode_num):
        try:
            disk_name = File._get_root_disk()
            cmd = "debugfs -R 'stat <{}>' {} | grep crtime ".format(inode_num, disk_name)
            cmd_out = File._execute(cmd)

            if cmd_out.find("crtime") != -1:
                time_str = cmd_out.split("--")[1].strip()
                return File._execute("date --date=\"%s\" '+%%s'" % time_str)
            else:
                return None
        except (AttributeError, IndexError):
            return None
