# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
import configparser
import logging

from log_collector.config.constants import ZBS_CONF_FILE

logger = logging.getLogger(__name__)


class ZbsConf:
    def __init__(self):
        pass

    @staticmethod
    def _get_ip_index(ips):
        mgt_ip_list = ZbsConf.get_mgt_ips()
        storage_ip_list = ZbsConf.get_storage_ips()
        index_list = []

        for x in ips:
            if x in storage_ip_list:
                index_list.append(storage_ip_list.index(x))
            elif x in mgt_ip_list:
                index_list.append(mgt_ip_list.index(x))
            else:
                raise Exception("ip %s cannot find in zbs.conf" % x)

        return index_list

    @staticmethod
    def get_storage_ips(ips=None):
        try:
            cf = configparser.ConfigParser()
            cf.read(ZBS_CONF_FILE)

            ip_list = cf.get("cluster", "cluster_storage_ips").split(",")

            if ips is None:
                return ip_list
            else:
                index_list = ZbsConf._get_ip_index(ips)

                return [ip_list[x] for x in index_list]

        except (configparser.Error, AttributeError):
            return []

    @staticmethod
    def get_mgt_ips(ips=None):
        try:
            cf = configparser.ConfigParser()
            cf.read(ZBS_CONF_FILE)

            ip_list = cf.get("cluster", "cluster_mgt_ips").split(",")

            if ips is None:
                return ip_list
            else:
                index_list = ZbsConf._get_ip_index(ips)

                return [ip_list[x] for x in index_list]

        except (configparser.Error, AttributeError):
            return []

    @staticmethod
    def check_local_ip(ip):
        try:
            cf = configparser.ConfigParser()
            cf.read(ZBS_CONF_FILE)

            data_ip = cf.get("network", "data_ip")
            vm_ip = cf.get("network", "vm_ip")
            web_ip = cf.get("network", "web_ip")

            return ip in [data_ip, vm_ip, web_ip]

        except (configparser.Error, AttributeError):
            return None

    @staticmethod
    def get_role():
        try:
            cf = configparser.ConfigParser()
            cf.read(ZBS_CONF_FILE)

            role = cf.get("cluster", "role")

            return role
        except (configparser.Error, AttributeError):
            return None

    @staticmethod
    def get_current_storage_ip():
        try:
            cf = configparser.ConfigParser()
            cf.read(ZBS_CONF_FILE)
            return cf.get("network", "data_ip")
        except (configparser.Error, AttributeError) as e:
            logger.error("Fail to get the current mgt ip: %s" % e)

            return None
