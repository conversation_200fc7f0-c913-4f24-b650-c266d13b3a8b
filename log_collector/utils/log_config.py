# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
import copy
import json
import os

from log_collector.config.constants import HOST_PLATFORM_PATH, LOG_CONFIG_PATH, PLATFORM_KVM
from log_collector.config.logger import logger


class LogConf:
    def __init__(self):
        self.platform = self.get_platform() or PLATFORM_KVM
        self.default_log_config = self.load_log_config()

    def get_platform(self):
        platform = ""
        if os.path.exists(HOST_PLATFORM_PATH):
            with open(HOST_PLATFORM_PATH) as f:
                platform = f.read().strip()
        return platform

    def is_supported_platform(self, item, platform):
        # if supported_platform is not set, it means that the log config is supported by all platforms
        if "supported_platform" in item and platform not in item["supported_platform"]:
            return False
        return True

    def load_log_config(self):
        log_config = []
        try:
            with open(LOG_CONFIG_PATH) as f:
                config_list = json.loads(f.read()).get("log_config")
                log_config = [item for item in config_list if self.is_supported_platform(item, self.platform)]
        except Exception as e:
            logger.exception("Failed to load the log_config default config file({}): {}.".format(LOG_CONFIG_PATH, e))
        return log_config

    @property
    def default_log_groups(self):
        groups = []
        for item in self.default_log_config:
            groups.append(item["group_name"])
        return groups

    @property
    def node_info_log_config(self):
        for item in self.default_log_config:
            if item["group_name"] == "node_info":
                return item
        return

    def build_log_config_pattern(self, log_groups, log_services):
        log_config_pattern = []
        if not (log_groups or log_services):
            log_config_pattern = copy.deepcopy(self.default_log_config)
            return log_config_pattern

        if log_groups:
            groups = log_groups.split(",")
            for log_pattern in self.default_log_config:
                if log_pattern["group_name"] in groups:
                    log_config_pattern.append(log_pattern)

        if log_services:
            services = log_services.split(",")
            for log_pattern in self.default_log_config:
                group_name = log_pattern["group_name"]
                if group_name in log_config_pattern:
                    continue

                build_service_list = []
                for service_pattern in log_pattern["service_list"]:
                    if service_pattern["name"] in services:
                        build_service_list.append(service_pattern)
                if build_service_list:
                    build_service_config = {"group_name": group_name, "service_list": build_service_list}
                    log_config_pattern.append(build_service_config)

        return log_config_pattern
