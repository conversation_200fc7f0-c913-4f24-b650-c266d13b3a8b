# Copyright (c) 2013-2024, SMARTX
# All rights reserved.

import json
import os
import time

import requests

from log_collector.cluster.metadata import MetaData
from log_collector.config.constants import (
    LOG_TEMP_PATH,
    NODE_INFO_PREFIX,
    NODE_INFO_RANGE,
    NODE_PROCESS_PREFIX,
    PROCESS_STAGE_COLLECT_CONTROLLER,
    PROCESS_STAGE_COLLECT_SLAVE,
)
from log_collector.config.logger import logger
from log_collector.utils.collected_log_size import sum_log_size
from log_collector.utils.zbs_conf import ZbsConf


def init_process_status(task_uuid):
    process_status = {}
    file_name = LOG_TEMP_PATH + "/tmp_process_" + str(task_uuid)
    if not os.path.exists(LOG_TEMP_PATH):
        os.makedirs(LOG_TEMP_PATH)
    try:
        with open(file_name, "w") as f:
            json.dump(process_status, f)
    except OSError as e:
        logger.exception("init tmp_process_status failed: {}".format(e))


def get_process_status(task_uuid):
    file_path = LOG_TEMP_PATH + "/tmp_process_" + str(task_uuid)
    output = {}
    if os.path.exists(file_path):
        try:
            with open(file_path) as f:
                output = json.load(f)
        except Exception as e:
            logger.warning("get task process status failed: {}".format(e))
    return output


def update_process_status(task_status, task_uuid):
    file_path = LOG_TEMP_PATH + "/tmp_process_" + str(task_uuid)
    with open(file_path, "w") as f:
        json.dump(task_status, f)


def clean_tmp_process_file(task_uuid):
    file_path = LOG_TEMP_PATH + "/tmp_process_" + str(task_uuid)
    if os.path.isfile(file_path):
        os.remove(file_path)


def write_process(process, progress_bar, task_uuid, status, metadata, include_controller, send_log_to_controller):
    metadata["collect_end_timestamp"] = int(time.time())

    log_path = metadata["path"]
    if status in ["successful", "failed"]:
        # executor.write_stdout_to_log()
        if send_log_to_controller is True and progress_bar is not None:
            progress_bar.update(1)
            progress_bar.finish()

        if send_log_to_controller is False:
            metadata["size"] = sum_log_size(task_uuid)
        else:
            log_file_size = int(os.path.getsize(log_path)) if os.path.exists(log_path) else 0
            metadata["size"] = log_file_size

        metadata["progress"] = "100%"
    else:
        current_process = calculate_process(task_uuid, process, include_controller)
        if send_log_to_controller is True and progress_bar is not None:
            progress_bar.update(current_process)
        metadata["progress"] = str(int(current_process * 100)) + "%"

    MetaData.write_metadata_file(metadata, send_log_to_controller)


def calculate_process(task_uuid, process, include_controller):
    current_process = get_process(task_uuid)
    if include_controller == "yes":
        controller_range = (0, 0.4)
        node_range = (0.4, 0.95)
    else:
        controller_range = (0, 0)
        node_range = (0, 0.95)

    if PROCESS_STAGE_COLLECT_SLAVE in current_process:
        l_range = node_range[0]
        r_range = node_range[1]
        progress_list = [progress for _, progress in current_process[PROCESS_STAGE_COLLECT_SLAVE].items()]
        progress = min(progress_list)
    elif PROCESS_STAGE_COLLECT_CONTROLLER in current_process:
        l_range = controller_range[0]
        r_range = controller_range[1]
        progress_list = [progress for _, progress in current_process[PROCESS_STAGE_COLLECT_CONTROLLER].items()]
        progress = max(progress_list)
    else:
        return process

    current = l_range + (r_range - l_range) * float(progress)
    process = max(current, process)

    return process


def get_process(task_uuid):
    def _get_node_info_process(node_info_str):
        node_info_stages = [
            "Collect Installed rpm list",
            "Collect kernel info",
            "Collect services config file",
            "Collect deploy config file",
            "Collect disk info",
            "Collect nic info",
            "Collect SmartX Services status",
            "Collect system status",
            "Collect zbs info",
            "Collect hardware info",
        ]
        for index, node_info_stage in enumerate(node_info_stages):
            if node_info_stage in node_info_str:
                return NODE_INFO_RANGE[0] + (NODE_INFO_RANGE[1] - NODE_INFO_RANGE[0]) * (index + 1) / len(
                    node_info_stages
                )

        return None

    process_status = {}
    try:
        process_status = get_process_status(task_uuid)
        latest_nodes_progress = get_latest_nodes_progress(task_uuid)
        for node_data in latest_nodes_progress:
            host_ip = node_data.get("host_ip")
            node_progress = node_data.get("progress", "")
            if not node_progress:
                continue
            if "SUCCESSFUL" not in node_progress:
                progress = None
                if NODE_PROCESS_PREFIX in node_progress:
                    progress_str = node_progress.split("=")[-1].strip("#")
                    progress = float(progress_str)
                elif NODE_INFO_PREFIX in node_progress:
                    progress = _get_node_info_process(node_progress)

                if host_ip and progress:
                    stage = (
                        PROCESS_STAGE_COLLECT_CONTROLLER
                        if ZbsConf.check_local_ip(host_ip)
                        else PROCESS_STAGE_COLLECT_SLAVE
                    )

                    if host_ip not in process_status.setdefault(stage, {}):
                        process_status[stage][host_ip] = progress
                    elif process_status[stage][host_ip] < progress:
                        process_status[stage][host_ip] = progress
        if process_status:
            update_process_status(process_status, task_uuid)

    except Exception as e:
        logger.warning("Failed to get progress: %s" % e)
    return process_status


def get_latest_nodes_progress(task_uuid):
    resp = requests.get(f"http://localhost:10406/api/v2/logs/{task_uuid}/progress")
    if resp.status_code == 200:
        return resp.json().get("data", {})
    else:
        return None
