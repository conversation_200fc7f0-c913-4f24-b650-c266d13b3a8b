# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
import logging

from common.http import tuna


def get_cluster_name():
    try:
        tuna_client = tuna.Client()._rest_client
        result = tuna_client.get("http://127.0.0.1/api/v2/settings/cluster")
        cluster_name = result.get("data", {}).get("cluster_name")
        # To prevent compressed file generation failure due to whitespace in the
        # cluster name, replace spaces with underscores.
        return cluster_name.replace(" ", "_") if cluster_name else ""
    except Exception as e:
        logging.warning("log-collector failed to get cluster name: {}".format(e))
        return ""
