# Copyright (c) 2013-2024, SMARTX
# All rights reserved.

import logging
from queue import Empty, Queue
import re
import subprocess
import sys
import threading
import time

import pexpect

from log_collector.config.constants import EXECUTE_TIMOUT, LOG_FORMAT, TERMINATE_WAIT_TIMEOUT

SCRIPT_EXECUTE_TIMEOUT = 3600  # 60 mins


def can_ping(ip):
    process = subprocess.Popen(
        "ping -W 2 -c 2 %s" % ip,
        shell=True,
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
    )
    cmd_out = process.stdout.read()
    result = re.match(r"[\S\s]*bytes from[\S\s]*icmp_seq=[\S\s]*ttl=", cmd_out)
    return result is not None


def no_blocking_output_to_log(
    process, uuid, log=logging, progress=False, timeout=SCRIPT_EXECUTE_TIMEOUT, return_output=False
):
    handler = log.handlers[0]
    handler.setFormatter(logging.Formatter("[%(asctime)s] %(message)s"))
    command = LogTool(process, uuid)
    rc, output = command.save(log=log, progress=progress, timeout=timeout, return_output=return_output)
    handler.setFormatter(logging.Formatter(LOG_FORMAT))
    return rc, output


def _run_cmd(cmd, return_info, process_info):
    try:
        process_info["process"] = subprocess.Popen(
            cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
        )

        stdout, stderr = process_info["process"].communicate()
        output = stdout + stderr
        process_info["process"].poll()
        return_info["res"] = (process_info["process"].returncode, output)

        return return_info

    except OSError as e:
        output = "Fail to run command: %s" % e
        return_info["res"] = (e.errno, output)
        return return_info


# Help to terminate when Popen run process in D status
def run_shell_script_with_return_output(cmd, timeout=EXECUTE_TIMOUT, logger=logging):
    process_info = {"process": None}

    return_res = {"res": (None, "")}
    thread = threading.Thread(target=_run_cmd, args=(cmd, return_res, process_info))
    thread.start()
    thread.join(timeout=timeout)
    if thread.is_alive():
        if process_info["process"] is not None:
            logger.warning("cmd: %s run timeout!" % cmd)
            return_res["res"] = (None, "timeout")
            logger.info("pid {} will be terminated".format(process_info["process"].pid))  # pragma: no cover
            process_info["process"].terminate()
        thread.join(timeout=TERMINATE_WAIT_TIMEOUT)

    return return_res["res"]


def remote_ssh(host, user, password, command, logger=logging):
    ssh_newkey = "Are you sure you want to continue connecting"
    command = 'ssh  {}@{} "{}"'.format(user, host, command)
    logger.info("command: %s" % command)
    child = pexpect.spawn(command, encoding="utf-8")
    child.logfile_read = sys.stdout  # pragma: no cover
    index = child.expect([pexpect.TIMEOUT, ssh_newkey, "[pP]assword:( )*", pexpect.EOF])
    if index == 0:  # Timeout
        logger.error("ERROR!")
        logger.error("SSH could not login. Here is what SSH said:")
        logger.error("{}, {}".format(child.before, child.after))
        return None
    if index == 1:  # SSH does not have the public key. Just accept it.
        child.sendline("yes")
        index = child.expect([pexpect.TIMEOUT, "[pP]assword:( )*", pexpect.EOF])
        if index == 0:
            logger.error("ERROR!")
            logger.error("SSH could not login. Here is what SSH said:")
            logger.error("{}, {}".format(child.before, child.after))
            return None
        elif index == 1:
            child.sendline(password)
            child.expect(pexpect.EOF)
    if index == 2:
        child.sendline(password)
        child.expect(pexpect.EOF)
    stdout = child.before
    return stdout.strip()


class LogTool:
    def __init__(self, process, uuid):
        self.process = process
        self.task_uuid = uuid
        self.error_message = None

    def save(self, log=logging, progress=False, timeout=SCRIPT_EXECUTE_TIMEOUT, return_output=False):
        try:

            def kill_process():
                if self.process.poll() is None:
                    log.error("Kill process")
                    self.process.kill()
                    self.error_message = "shell process killed by timeout, timeout=%s" % timeout
                    log.error(self.error_message)

            # start timer
            t = threading.Timer(timeout, kill_process)
            t.daemon = True
            t.start()

            # wrap self.process.stdout with a NonBlockingStreamReader object:
            nbsr = NonBlockingStreamReader(self.process.stdout)

            shell_output = ""
            start_time = time.time()
            # print the output in real-time
            while self.process.poll() is None:
                try:
                    line = nbsr.readline(0.1)  # 0.1 secs to let the shell output the result
                    if progress:
                        if line and not line.startswith("<"):
                            shell_output += line
                            log.info(line.rstrip())
                    else:
                        if line and not line.startswith("<"):
                            shell_output += line
                            log.info(line.rstrip())

                    if self.loop_write_timeout(start_time):
                        break
                except KeyboardInterrupt:
                    # ctrl + c in terminal will send SIGINT to both parent and child processes
                    # catch the signal and wait for defunct child in next loop.
                    continue

            # When the subprocess terminates there might be unconsumed output that still needs to be processed.
            content = nbsr.readline(1)
            if content:
                shell_output += content
                log.info(content)

            # cancel timer
            t.cancel()
            if return_output:
                output = shell_output if self.process.returncode == 0 else self.error_message
                return self.process.returncode, output
            else:
                return self.process.returncode, self.error_message
        except OSError as e:
            error_message = "Fail to run command: %s" % e
            log.error(error_message)
            return e.errno, error_message
        except TypeError as e:
            error_message = "Fail to run command: %s" % e
            log.error(error_message)
            return None, error_message

    @staticmethod
    def loop_write_timeout(start_time, timeout=SCRIPT_EXECUTE_TIMEOUT):
        return True if time.time() - start_time >= timeout else False


class NonBlockingStreamReader:
    def __init__(self, stream):
        """
        stream: the stream to read from.
                Usually a process' stdout or stderr.
        """

        self._s = stream
        self._q = Queue()

        def _populateQueue(stream, queue):
            """
            Collect lines from 'stream' and put them in 'quque'.
            """

            while True:
                line = stream.readline()
                if line:
                    queue.put(line)
                else:
                    break
                    # raise UnexpectedEndOfStream

        self._t = threading.Thread(target=_populateQueue, args=(self._s, self._q))
        self._t.daemon = True
        self._t.start()  # start collecting lines from the stream

    def readline(self, timeout=None):
        try:
            return self._q.get(block=timeout is not None, timeout=timeout)
        except Empty:
            return None
