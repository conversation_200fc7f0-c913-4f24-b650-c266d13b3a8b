# Copyright (c) 2013-2024, SMARTX
# All rights reserved.


import os

from log_collector.config.constants import LOG_TEMP_PATH
from log_collector.config.logger import logger


def sum_log_size(uuid):
    try:
        log_size_sum = 0

        for root, dirs, files in os.walk(LOG_TEMP_PATH + "/" + uuid, topdown=False):
            for name in files:
                if name.startswith("log_size_"):
                    with open(root + "/" + name) as f:
                        log_size_sum += int(f.read())
        return log_size_sum
    except Exception as e:
        logger.exception("can not get the logs total size: %s" % e)
        return 0
