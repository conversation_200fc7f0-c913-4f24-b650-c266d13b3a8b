# Copyright (c) 2013-2024, SMARTX
# All rights reserved.

import base64
import logging

import pymongo
import scrypt

from common.mongo.db import mongodb

ENCRYPT_SALT = "SmartX-Encrypt-Salt"


class VsphereAccountService:
    DEFAULT_DB_NAME = "smartx"

    def decode_password(self, password):
        res = scrypt.decrypt(base64.b64decode(password), ENCRYPT_SALT)
        return res

    def _adjust_pwd(self, account, no_password):
        if no_password:
            del account["password"]
        else:
            try:
                account["password"] = self.decode_password(account["password"])
            except scrypt.error:
                logging.warning("Decrypt account password failed ({}:{}).".format(account["host"], account["port"]))
                return None
        return account

    def get_by_node_uuid(self, host_uuid, no_password=True):
        """
        Get a server account by node_uuid.

        :param host_uuid:           node uuid
        :param no_password:         If ``True``, returns the account
                                    without password field. Default
                                    is ``True``.
        :return:
        """
        account = mongodb[self.DEFAULT_DB_NAME].vsphere_account.find_one(
            {"host_uuid": host_uuid}, {"_id": 0}, sort=[("_id", pymongo.DESCENDING)]
        )
        if account:
            return self._adjust_pwd(account, no_password)
        return None
