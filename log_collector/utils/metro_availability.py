# Copyright (c) 2013-2018, SMARTX
# All rights reserved.


import os

from log_collector.config.constants import HOST_METRO_AVAILABILITY_PATH, HOST_WITNESS_PATH


def get_witness_ip():
    try:
        with open(HOST_METRO_AVAILABILITY_PATH) as f:
            is_metro = f.read().strip()
        if is_metro in ["true", "True"] and os.path.exists(HOST_WITNESS_PATH):
            with open(HOST_WITNESS_PATH) as f:
                witness_storage_ip = f.read().strip().split(":", 1)
            return witness_storage_ip[0]
    except (<PERSON><PERSON><PERSON><PERSON>, KeyError, IndexError):
        return None
