# Copyright (c) 2013-2024, SMARTX
# All rights reserved.

import logging

from log_collector.utils.account import VsphereAccountService
from log_collector.utils.cmdline import remote_ssh, run_shell_script_with_return_output

PERCCLI_CMD = "/opt/MegaRAID/perccli/perccli64"
STORCLI_CMD = "/opt/MegaRAID/storcli/storcli64"


def get_node_uuid():
    host_uuid_path = "/etc/zbs/uuid"
    host_config_uuid = None
    try:
        with open(host_uuid_path) as f:
            host_config_uuid = f.read().strip()
    except OSError:
        logging.exception("Failed to access node uuid file({}).".format(host_uuid_path))
    return host_config_uuid


def get_vmware_physical_nic_info():
    """
    esxcfg-nics -l example:
    Name    PCI          Driver      Link Speed      Duplex MAC Address       MTU    Description
    vmnic0  0000:03:00.0 igbn        Up   1000Mbps   Full   00:25:90:fc:fb:42 1500   Intel Corporation I350 Gigabit

    esxcli network nic get -n vmnic0 example:
    Advertised Link Modes: Auto, 1000BaseT/Full, 100BaseT/Full, 100BaseT/Half, 10BaseT/Full, 10BaseT/Half
    Auto Negotiation: true
    Backing DPUId: N/A
    Cable Type: Twisted Pair
    Current Message Level: 0
    Driver Info:
         Bus Info: 0000:03:00:0
         Driver: igbn
         Firmware Version: 1.63.0:0x80000a05
         Version: ********
    Link Detected: true
    Link Status: Up
    Name: vmnic0
    """
    try:
        account = VsphereAccountService().get_by_node_uuid(get_node_uuid(), no_password=False)
        nic_list_cmd = "esxcfg-nics -l"
        stdout = remote_ssh(account["host"], account["user"], account["password"], nic_list_cmd)
        result = ""
        for line in stdout.split("\n")[1:]:
            if not line:
                continue
            info = line.strip().split()

            model = " ".join(info[8:])
            detail_cmd = "esxcli network nic get -n {}".format(info[0])
            detail_info = remote_ssh(account["host"], account["user"], account["password"], detail_cmd)
            result += f"model: {model}:\n{detail_info}\n"

    except Exception as e:
        logging.warning("get esxi nic info failed: {}".format(e))
        result = {}
    return result


def _get_storage_controller_by_megacli(cmd, controller_count):
    res = []
    for i in range(controller_count):
        rc, output = run_shell_script_with_return_output(
            "%s /c%d show all nolog | grep -E 'Model =|Firmware Version ='" % (cmd, i)
        )
        if not (rc == 0 and output):
            logging.info("test {} cmd failed, rc: {} output: {}".format(cmd, rc, output))
            continue
        model = firmware_version = None
        for line in output.strip().split("\n"):
            if line.startswith("Model ="):
                model = line.strip().split("=")[-1].strip()
            if line.startswith("Firmware Version ="):
                firmware_version = line.strip().split("=")[-1].strip()
        if model and firmware_version:
            res.append(f"name: {model}, version: {firmware_version}")
    return res


def _get_storage_controller_count(cmd):
    """
    CLI Version = 007.1623.0000.0000 May 17, 2021
    Operating system = Linux 3.10.0-1160.11.1.el7.smartx.1.x86_64
    Status Code = 0
    Status = Success
    Description = None
    Controller Count = 1
    """
    rc, output = run_shell_script_with_return_output("%s show ctrlcount nolog | grep 'Controller Count'" % cmd)
    if not (rc == 0 and output):
        logging.info("test {} cmd failed, rc: {} output: {}".format(cmd, rc, output))
        return False, 0

    ctrl_count = 0
    if output.startswith("Controller Count ="):
        value = output.strip().split("=")[-1].strip()
        ctrl_count = int(value) if value.isdigit() else 0
    return ctrl_count > 0, ctrl_count


def get_storage_controller():
    mega_clis = [PERCCLI_CMD, STORCLI_CMD]
    for cmd in mega_clis:
        run_status, ctrl_cnt = _get_storage_controller_count(cmd)
        if run_status:
            return _get_storage_controller_by_megacli(cmd, ctrl_cnt)
    return None
