# Copyright (c) 2013-2018, SMARTX
# All rights reserved.


import os

from log_collector.config.constants import LOG_COMPRESS_RATE


def every_node_log_threshold(node_num):
    free_capacity = get_available_capacity()
    return int(free_capacity / int(node_num) * LOG_COMPRESS_RATE)


def local_log_threshold():
    free_capacity = get_available_capacity()
    return int(free_capacity / (1.0 + 1.0 / LOG_COMPRESS_RATE))


def get_available_capacity():
    st = os.statvfs("/")
    avail = int(st.f_blocks * st.f_frsize * 0.9 - (st.f_blocks - st.f_bfree) * st.f_frsize)
    return avail if avail > 0 else 0


def check_capacity_safe(add_file_sum):
    st = os.statvfs("/")
    return st.f_bfree * st.f_frsize - add_file_sum > st.f_blocks * 0.1 * st.f_frsize
