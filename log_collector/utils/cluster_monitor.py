# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
import configparser
import threading

from inotify_simple import INotify, flags

from log_collector.config.constants import ZBS_CONF_FILE
from log_collector.config.logger import logger
from log_collector.utils.metro_availability import get_witness_ip


class ClusterInotify:
    def __init__(self):
        self.cluster_hosts = {}
        self.local_ip = {}
        self.cluster_up_to_date()
        self._inotify = INotify()
        self._active()

    def _active(self):
        t = threading.Thread(target=self._run)
        t.daemon = True
        t.start()

    def _run(self):  # pragma: no cover
        watch_flags = flags.MODIFY | flags.IGNORED | flags.DELETE_SELF
        self._inotify.add_watch(ZBS_CONF_FILE, watch_flags)
        while True:
            try:
                for event in self._inotify.read(read_delay=2000):
                    event_flags = [str(f) for f in flags.from_mask(event.mask)]
                    logger.info("zbs.conf changed: {} {}, will start to sync it".format(event, "|".join(event_flags)))
                    if event.mask & flags.MODIFY or event.mask & flags.DELETE_SELF:
                        self.cluster_up_to_date()
                    if event.mask & flags.IGNORED:
                        logger.info("re-watch new /etc/zbs/zbs.conf")
                        self._inotify.add_watch(ZBS_CONF_FILE, watch_flags)

            except Exception as e:
                logger.exception(e)

    def cluster_up_to_date(self):
        cluster_hosts = self.cluster_hosts
        local_ip = self.local_ip
        logger.info("sync cluster info ...")
        parser = configparser.SafeConfigParser()
        try:
            parser.read(ZBS_CONF_FILE)
            curr_cluster_hosts = {
                "mgt": parser.get("cluster", "cluster_mgt_ips").split(","),
                "storage": parser.get("cluster", "cluster_storage_ips").split(","),
            }
            witness_ip = get_witness_ip()
            if witness_ip is not None:
                curr_cluster_hosts["witness"] = [witness_ip]
            elif "witness" in cluster_hosts:
                cluster_hosts.pop("witness")
            cluster_hosts.update(curr_cluster_hosts)
        except Exception as e:
            logger.error("sync cluster info failed: {}".format(e))

        logger.info("cluster_hosts: {}".format(cluster_hosts))

        try:
            curr_local_ip = {
                "mgt": parser.get("network", "web_ip"),
                "storage": parser.get("network", "data_ip"),
            }
            local_ip.update(curr_local_ip)
        except Exception as e:
            logger.error("sync local info failed: {}".format(e))

        logger.info("local_ip: {}".format(local_ip))
