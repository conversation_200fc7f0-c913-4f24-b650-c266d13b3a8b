# Copyright (c) 2013-2024, SMARTX
# All rights reserved.
import datetime
from io import BytesIO
import json
import os
import subprocess
import time
from urllib.parse import urlparse
import uuid

from flask import Blueprint, Response, abort, request
import requests

from common.event.event_message import generate_events, record_event
from log_collector.cluster.metadata import MetaData
from log_collector.cmd.cluster_logs import api_collect_logs, clean_logs, kill_task, task_holder
from log_collector.config.logger import logger
from log_collector.events.collect_log_events_wrapper import CollectLogEventWrapper
from log_collector.utils.cluster_monitor import ClusterInotify
from log_collector.utils.zip_stream import ZipFile, ZipStream
from tuna.server.common.utils import get_user_by_token

log_bp = Blueprint("api_v2_logs", "api_v2_logs", url_prefix="/api/v2")

LOG_PATH = "/usr/share/tuna/log_collector/logs/"
METADATA_PATH = "/usr/share/tuna/log_collector/metadata/"
TMP_LOCAL_PROGRESS = "/tmp/tmp_log_local_collector_{}.txt"


cluster_inotify = ClusterInotify()


def wrap_response(data=None, ec="EOK", error=None):
    if error is None:
        error = {}
    if data is None:
        data = {}
    return {"data": data, "ec": ec, "error": error}


def _get_current_node_log():
    metadata = []
    for metadata_file in os.listdir(METADATA_PATH):
        with open(METADATA_PATH + str(metadata_file)) as f:
            try:
                json_obj = json.load(f)
            except ValueError:
                pass
            else:
                metadata.append(json_obj)
    return metadata


def tail_read(filename, n=5):
    """Returns the last n lines of a file f using the 'tail' system command."""
    command = ["tail", "-n", str(n), filename]
    process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    stdout, stderr = process.communicate()
    if process.returncode == 0:
        return stdout.decode().splitlines()
    else:
        logger.warning(f"tail command failed with error: {stderr.decode()}")
        return []


def _get_local_progress(task_uuid):
    tmp_local_progress = TMP_LOCAL_PROGRESS.format(task_uuid)
    local_data_ip = cluster_inotify.local_ip.get("storage")
    progress_data = {"host_ip": local_data_ip}
    if os.path.exists(tmp_local_progress):
        latest_content = tail_read(tmp_local_progress, n=1)
        progress_data["progress"] = latest_content[0] if latest_content else ""
    return progress_data


@log_bp.route("/logs/node/get_current_log", methods=["GET"])
def get_current_node_log():
    metadata_info = _get_current_node_log()
    return json.dumps(wrap_response(data=metadata_info))


@log_bp.route("/logs/node/<task_uuid>/progress", methods=["GET"])
def get_current_node_progress(task_uuid):
    progress_data = _get_local_progress(task_uuid)
    return json.dumps(wrap_response(data=progress_data))


@log_bp.route("/logs/<task_uuid>/progress", methods=["GET"])
def cluster_collect_progress(task_uuid):
    cluster_hosts = cluster_inotify.cluster_hosts
    local_ip = cluster_inotify.local_ip
    cluster_ips = cluster_hosts["storage"]
    cluster_progress = []
    if "witness" in cluster_hosts:
        cluster_ips += cluster_hosts["witness"]

    for data_ip in cluster_ips:
        if local_ip.get("storage") == data_ip:
            latest_progress_info = _get_local_progress(task_uuid)
            cluster_progress.append(latest_progress_info)
            continue
        try:
            response = requests.get(f"http://{data_ip}/api/v2/logs/node/{task_uuid}/progress")
            if response.status_code == 200:
                node_latest_progress = response.json().get("data", {})
                cluster_progress.append(node_latest_progress)
        except Exception as e:
            logger.warning("Failed to get progress from node: %s, error: %s", data_ip, e)
            pass

    return json.dumps(wrap_response(data=cluster_progress))


@log_bp.route("/logs", methods=["GET"])
def cluster_logs_status():
    """Return cluster tasks list. If no tasks, return []."""
    metadata = []
    # Inorder to satisfy old api code GET: /logs?current_node_only
    # may cause too many api calls in a short time when upgrading OS,
    # it may lead log-collector crash at that time.
    if request.args.get("current_node_only"):
        return json.dumps(wrap_response(data=metadata))
    cluster_hosts = cluster_inotify.cluster_hosts
    local_ip = cluster_inotify.local_ip
    if "witness" in cluster_hosts:
        cluster_ips = cluster_hosts["storage"] + cluster_hosts["witness"]
    else:
        cluster_ips = cluster_hosts["storage"]
    for host in cluster_ips:
        if local_ip.get("storage") == host:
            metadata_info = _get_current_node_log()
            metadata.extend(metadata_info)
            continue
        try:
            response = requests.get("http://%s/api/v2/logs/node/get_current_log" % host)
            if response.status_code == 200 and len(response.json()) != 0:
                metadata.extend(response.json().get("data"))
        except Exception as e:
            logger.warning(f"Failed to get logs from host {host}: {e}")
            pass

    return json.dumps(wrap_response(data=metadata))


@log_bp.route("/logs", methods=["POST"])
def collect_log():
    """
    Collect host logs which host in cluster.
    """

    if task_holder.is_task_running():
        return json.dumps(
            wrap_response(
                ec="LOG_COLLECT_NOT_SUPPORT_RUN_PARALLEL",
                error={"msg": "please make sure there is no running collect job in this node"},
            )
        )

    data = json.loads(request.get_data())
    collect_hosts = data.get("hosts")
    start_timestamp = data.get("start_timestamp")
    end_timestamp = data.get("end_timestamp")
    groups = data.get("groups")
    services = data.get("services")

    task_uuid = str(uuid.uuid4())
    cluster_ips = []

    cluster_hosts = cluster_inotify.cluster_hosts
    for _, value in list(cluster_hosts.items()):
        cluster_ips.extend(value)
    count = [host for host in collect_hosts if str(host) not in cluster_ips]
    if len(count) > 0:
        logger.error("collected host {} not in cluster hosts {}.".format(count, cluster_ips))
        return json.dumps(
            wrap_response(
                ec="REST_MGT_IP_NOT_FOUND",
                error={"msg": "collected host {} not in cluster hosts {}.".format(count, cluster_ips)},
            )
        )
    log_groups = ",".join(groups) if groups else None
    log_services = ",".join(services) if services else None
    MetaData.init_metadata(
        collect_hosts, task_uuid, groups, services, start_timestamp, end_timestamp, send_log_to_controller=False
    )

    api_collect_logs(
        start_timestamp,
        end_timestamp,
        collect_hosts,
        groups=log_groups,
        services=log_services,
        task_uuid=task_uuid,
        send_log_to_controller=False,
    )

    # event auditlog start
    event_wrapper = CollectLogEventWrapper(user=get_user_by_token(request))
    local_ip = cluster_inotify.local_ip
    if len(collect_hosts) == len(cluster_hosts.get("storage")):
        event_arg = event_wrapper.event_collect_all_nodes_log()
    else:
        event_arg = event_wrapper.event_collect_selected_nodes_log()
    record_event(generate_events(event_state="DONE", **event_arg))
    # event auditlog end

    result = {"task_uuid": task_uuid, "log_owner": local_ip.get("mgt")}
    return json.dumps(wrap_response(data=result))


@log_bp.route("/logs/log_config", methods=["GET"])
def cluster_log_config():
    from log_collector.utils.log_config import LogConf

    log_config = LogConf().load_log_config()

    return json.dumps(wrap_response(data=log_config))


@log_bp.route("/logs/<task_uuid>", methods=["GET", "DELETE"])
def collect_task_status(task_uuid):
    """
    Return task status if method is GET,
    if method is DELETE, delete task relevant logs.
    """
    tasks = requests.get("http://localhost:10406/api/v2/logs").json().get("data")
    tasks = [task for task in tasks if len(task) != 0]
    for task in tasks:
        if task.get("task_uuid") == task_uuid:
            if request.method == "GET":
                result = wrap_response(data=task)
                return json.dumps(result)
            elif request.method == "DELETE":
                clean_logs(task_uuid=task_uuid)
                result = wrap_response(data="Task %s will be delete." % task_uuid)
                return json.dumps(result)
    abort(404)


@log_bp.route("/logs/<task_uuid>/stop", methods=["POST"])
def stop_task(task_uuid):
    tasks = requests.get("http://localhost:10406/api/v2/logs").json().get("data")
    tasks = [task for task in tasks if len(task) != 0]
    for task in tasks:
        if task.get("task_uuid") == task_uuid:
            kill_task(str(task_uuid))
            result = wrap_response(data="Task %s will be stop and delete." % task_uuid)
            return json.dumps(result)
    abort(404)


@log_bp.route("/logs/<task_uuid>/cluster_download", methods=["GET"])
def cluster_download(task_uuid):
    """Download one task collect all logs."""

    def get_remote_size(req):
        return int(req.headers.get("content-length"))

    def remote_file_gen_func(req):
        def create_fp():
            raw = req.raw
            raw.decode_content = True
            return raw

        return create_fp

    def generate_zipfile(reqs, metadata):
        metadata_bytes = json.dumps(metadata, indent=4, separators=(",", ": ")).encode("utf-8")
        modify_time_utc = datetime.datetime.utcfromtimestamp(time.time())
        files = [ZipFile("metadata.json", len(metadata_bytes), lambda: BytesIO(metadata_bytes), modify_time_utc, None)]
        for req in reqs:
            host = urlparse(req.url).hostname
            files.append(
                ZipFile(
                    "{}.tar.gz".format(host.replace(".", "_")),
                    get_remote_size(req),
                    remote_file_gen_func(req),
                    modify_time_utc,
                    None,
                )
            )
        return ZipStream(files=files)

    def get_log_metadata(task_uuid):
        for file in os.listdir(METADATA_PATH):
            if str(file) == task_uuid:
                with open(METADATA_PATH + file) as f:
                    try:
                        json_obj = json.load(f)
                    except ValueError:
                        pass
                    else:
                        return json_obj

    log_owner = request.args.get("log_owner")

    local_ip = cluster_inotify.local_ip
    if str(log_owner) == str(local_ip.get("mgt")):
        metadata = get_log_metadata(task_uuid)
    else:
        response = requests.get("http://{}:10406/api/v2/logs/{}".format("localhost", task_uuid))
        metadata = response.json().get("data")

    if not metadata:
        data = {"messages": "Task log({}) has been removed. Can not download.".format(task_uuid)}
        result = wrap_response(data=data)
        return json.dumps(result)

    if not metadata.get("task_uuid"):
        abort(404)
    elif metadata and metadata.get("status") != "successful":
        data = {"messages": "Task status is not successful. Can not download."}
        result = wrap_response(data=data)
        return json.dumps(result)

    reqs = []
    hosts = metadata.get("node_list")
    for host in hosts:
        try:
            req = requests.get(
                "http://{}:10406/api/v2/logs/{}/node_download".format(host, task_uuid), stream=True, timeout=5
            )
            if req.status_code == 200:
                reqs.append(req)
        except Exception:
            pass

    z = generate_zipfile(reqs, metadata)
    response = Response(z.generate(), mimetype="application/zip")
    filename = metadata.get("path").split("/")[-1]
    response.headers["Content-Disposition"] = "attachment; filename=%s" % filename
    response.headers["content-length"] = str(z.size())
    return response


@log_bp.route("/logs/<task_uuid>/node_download", methods=["GET"])
def node_download(task_uuid):
    local_ip = cluster_inotify.local_ip

    def get_file_from_uuid(task_uuid):
        for file in os.listdir(LOG_PATH):
            if task_uuid in str(file):
                return LOG_PATH + str(file)

    def send_chunk(filename):
        with open(filename, "rb") as target_file:
            while True:
                chunk = target_file.read(500 * 1024)
                if not chunk:
                    break
                yield chunk

    filename = get_file_from_uuid(task_uuid)
    response = Response(send_chunk(filename))
    response.headers["Content-Type"] = "application/zip"
    response.headers["Content-Disposition"] = "attachment; filename={}.tar.gz".format(local_ip.get("storage"))
    response.headers["content-length"] = os.stat(str(filename)).st_size
    return response
