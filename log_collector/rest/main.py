# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
import logging
from logging import DEB<PERSON><PERSON>, ERROR, INFO
from logging.handlers import RotatingFileHandler
import sys

import click

from log_collector.config.constants import LAUNCH_PORT
from log_collector.rest.app import app

LOG_LEVEL_MAP = {logging.getLevelName(level): level for level in (DEBUG, INFO, ERROR)}
LOG_FORMAT = "%(asctime)s, %(levelname)s, %(filename)s:%(lineno)d, %(message)s"

LOG_COLLECTOR_LOGFILE = "/var/log/zbs/log_collector.INFO"


def setup_logger(log_file, log_level):
    formatter = logging.Formatter("[%(asctime)s: %(levelname)s/%(processName)s] %(message)s")
    handler = RotatingFileHandler(log_file, maxBytes=20000000, backupCount=1)  # default 20mb
    handler.setLevel(logging.getLevelName(log_level))
    handler.setFormatter(formatter)

    logger = logging.getLogger()
    logger.setLevel(logging.getLevelName(log_level))
    list(map(logger.removeHandler, logger.handlers))
    logger.addHandler(handler)


@click.command()
@click.option("--debug", default=False, is_flag=True, help="enable flask debug mode")
@click.option("--log-level", type=click.Choice(list(LOG_LEVEL_MAP.keys())), default="INFO")
@click.option("--log-file", default=LOG_COLLECTOR_LOGFILE)
def run_server(log_level, log_file, debug):
    level = LOG_LEVEL_MAP[log_level]
    setup_logger(log_file, level)
    app.run(host="0.0.0.0", debug=debug, port=LAUNCH_PORT)


if __name__ == "__main__":
    level = LOG_LEVEL_MAP["DEBUG"]
    logging.basicConfig(
        format=LOG_FORMAT,
        level=level,
        stream=sys.stdout,
    )
    app.run(host="0.0.0.0", port=LAUNCH_PORT)
