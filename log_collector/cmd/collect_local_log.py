# Copyright (c) 2013-2024, SMARTX
# All rights reserved.

import json
import sys

from log_collector.node.node_collector import NodeCollector


def collect_logs(
    start_time,
    end_time,
    uuid,
    log_size,
    group_service_pattern=None,
    include_disk=True,
    collect_type="cluster",
):
    try:
        if isinstance(group_service_pattern, str):
            group_service_pattern = json.loads(group_service_pattern)  # Convert JSON string back into Python object
    except json.JSONDecodeError as e:
        print(f"Error decoding JSON: {e}")
        sys.exit(1)

    node_ins = NodeCollector(uuid, int(log_size), group_service_pattern, collect_type, include_disk)
    node_ins.collect_log(int(start_time), int(end_time))


if __name__ == "__main__":
    if len(sys.argv) != 6:
        print("Usage: collect_local_log.py <start_time> <end_time> <uuid> <log_size> <group_service_pattern_json>")
        print(f"sys.argv: {sys.argv}")
        sys.exit(1)

    collect_logs(*sys.argv[1:])
