# Copyright (c) 2013-2018, SMARTX
# All rights reserved.


import os
import sys
import traceback

from log_collector.config.constants import LOG_LOG_PATH, LOG_METADATA_PATH
from log_collector.config.logger import logger


def _clean_metadata(task_uuid_list):
    for root, dirs, files in os.walk(LOG_METADATA_PATH):
        names = [x for x in files if len([y for y in task_uuid_list if x.find(y) != -1]) > 0]
        for name in names:
            os.remove(os.path.join(root, name))


def _clean_zipped_log(task_uuid_list):
    for root, dirs, files in os.walk(LOG_LOG_PATH):
        names = [x for x in files if len([y for y in task_uuid_list if x.find(y) != -1]) > 0]
        for name in names:
            os.remove(os.path.join(root, name))


def node_clean_logs(task_uuid_list):
    try:
        logger.info("log_collector.cmd.clean_local_logs.clean_logs, task_uuid_list = %s" % task_uuid_list)
        _clean_zipped_log(task_uuid_list)
        _clean_metadata(task_uuid_list)
        return True
    except OSError:
        logger.info("log_collector.cmd.clean_cluster_logs.clean_logs, failed. exception is %s" % traceback.format_exc())
        return False


if __name__ == "__main__":
    try:
        uuid_list = [str(sys.argv[1])]
        node_clean_logs(uuid_list)
    except ValueError:
        logger.error("log_collector.cmd.clean_local_logs._main__, failed. exception is %s" % traceback.format_exc())
