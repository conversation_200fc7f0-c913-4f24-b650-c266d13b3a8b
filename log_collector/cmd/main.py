# Copyright (c) 2013-2018, SMARTX
# All rights reserved.
import sys
import time
import uuid

import click

from log_collector.cmd.cluster_logs import clean_logs, cmd_collect_logs
from log_collector.cmd.collect_local_log import collect_logs as collect_local_logs
from log_collector.config.constants import MAXIMUM_LOG_SIZE
from log_collector.config.logger import logger
from log_collector.utils.exceptions import ParamException
from log_collector.utils.log_config import LogConf
from log_collector.utils.metro_availability import get_witness_ip
from log_collector.utils.zbs_conf import ZbsConf

param_error_str = """
------------- param error -------------
param {param_name}'s value {param_value} not supported
{extra}
"""

DEFAULT_GROUPS = LogConf().default_log_groups


def parse_time(time_str, time_name):
    try:
        time_array = time.strptime(time_str, "%Y/%m/%d-%H:%M:%S")
        return int(time.mktime(time_array))
    except ValueError:
        print(
            param_error_str.format(
                param_name=time_name, param_value=time_str, extra="Time format like : %Y/%m/%d-%H:%M:%S"
            )
        )
        raise ParamException()


def parse_last_days(days, use_default):
    try:
        if days is None:
            if use_default:
                days = 1
            else:
                days = 100 * 365
        days = int(days)
        assert days > 0
        now = int(time.time())
        delta = now - days * 24 * 3600
        return delta if delta > 0 else 0
    except ValueError:
        print(
            param_error_str.format(
                param_name="last_no_of_days", param_value=days, extra="last_no_of_days must be number"
            )
        )
        raise ParamException()
    except AssertionError:
        print(param_error_str.format(param_name="last_no_of_days", param_value=days, extra="last_no_of_days must > 0"))
        raise ParamException()


def parse_last_hours(hours, use_default):
    try:
        if hours is None:
            if use_default:
                hours = 4
            else:
                hours = 100 * 365 * 24
        hours = int(hours)
        assert hours > 0
        now = int(time.time())
        delta = now - hours * 3600
        return delta if delta > 0 else 0
    except ValueError:
        print(
            param_error_str.format(
                param_name="last_no_of_hours", param_value=hours, extra="last_no_of_days must be number"
            )
        )
        raise ParamException()
    except AssertionError:
        print(
            param_error_str.format(param_name="last_no_of_hours", param_value=hours, extra="last_no_of_days must > 0")
        )
        raise ParamException()


def parse_node_list(node_list_str):
    nodes = []
    mgt_ips = ZbsConf.get_mgt_ips()
    storage_ips = ZbsConf.get_storage_ips()

    if node_list_str is None:
        witness_ip = get_witness_ip()
        if witness_ip is not None:
            mgt_ips.append(witness_ip)
        return mgt_ips

    node_list = node_list_str.split(",")
    for i in node_list:
        if i not in mgt_ips + storage_ips:
            print(
                param_error_str.format(
                    param_name="node_list", param_value=node_list_str, extra="node_ip %s not in cluster" % i
                )
            )
            raise ParamException("cannot find node %s" % i)
        nodes.append(i)
    return nodes


def check_log_groups(groups):
    if not groups:
        return groups
    log_groups = set(groups.split(","))
    default_groups = set(DEFAULT_GROUPS)
    for group in log_groups:
        if group not in default_groups:
            print(
                param_error_str.format(
                    param_name="groups", param_value=groups, extra="specified groups should in %s" % default_groups
                )
            )
            raise ParamException()


def check_log_services(services):
    if not services:
        return services

    log_services = set(services.split(","))

    services_list = []
    for log_pattern in LogConf().load_log_config():
        service_list = log_pattern["service_list"]
        for item in service_list:
            if item["name"] in log_services:
                services_list.append(item["name"])

    if len(services_list) != len(log_services):
        print(
            param_error_str.format(
                param_name="services",
                param_value=services,
                extra="specified services please check /etc/log_collector/log_config.json",
            )
        )
        raise ParamException()


@click.group()
def run():
    """
    log-collector is a tool to collect and clean logs
    """
    pass


@run.group()
def cluster():
    """
    log-collector support collect and clean log in cluster
    """
    pass


@run.group()
def node():
    """
    log-collector support collect and clean log in node
    """
    pass


@run.group()
def config():
    """
    log-collector support show config info
    """
    pass


@cluster.command(name="collect")
@click.option(
    "--start_time",
    default=None,
    help="Start time from when logs should be collected. Time format like : %Y/%m/%d-%H:%M:%S",
)
@click.option(
    "--end_time",
    default="2037/12/30-00:00:00",
    help="End time from till logs should be collected. Time format like : %Y/%m/%d-%H:%M:%S",
)
@click.option(
    "--last_no_of_days",
    default=None,
    help="Number of days for which logs should be collected. E.g. collect 3 days of logs:",
)
@click.option(
    "--last_no_of_hours",
    default=None,
    help="Number of hours for which logs should be collected. By default, 4 hrs of logs are collected. "
    "E.g. collect 3 hours of logs",
)
@click.option(
    "--node_list",
    default=None,
    help="which node should collect logs. nodes should split by , .E.g. ***********,***********",
)
@click.option(
    "--groups",
    default=None,
    help="collect the specified groups log, groups name should in %s. E.g. kernel_logs,zbs_logs,tuna_logs,node_info"
    % DEFAULT_GROUPS,
)
@click.option(
    "--services",
    default=None,
    help="collect the specified services log, default services name please check /etc/log_collector/log_config.json"
    " E.g. tuna-exporter,elf-vm-monitor,zbs-chunk",
)
@click.option("--include_disk", default=True, is_flag=True, help="collect nodes hardware info include disk.")
def cluster_collect(start_time, end_time, last_no_of_days, last_no_of_hours, node_list, groups, services, include_disk):
    logger.info(
        "log_collector.cmd.main.collect, collect cluster logs. start_time = {}, end_time={}, last_no_of_days = {}, "
        "last_no_of_hours = {}, node_list = {}, groups = {}, services = {}".format(
            start_time, end_time, last_no_of_days, last_no_of_hours, node_list, groups, services
        )
    )

    try:
        temp_start_time = start_time
        if start_time is None:
            start_time = "1970/01/01-00:00:00"
        start_timestamp = max(
            parse_time(start_time, "start_time"),
            parse_last_days(last_no_of_days, last_no_of_hours is None and temp_start_time is None),
            parse_last_hours(last_no_of_hours, last_no_of_days is None and temp_start_time is None),
        )
        end_timestamp = parse_time(end_time, "end_time")

        if int(start_timestamp) >= int(end_timestamp):
            print("invalid time scope, log end time must be greater than start time")
            raise ParamException("invalid time scope")

        node_ips = parse_node_list(node_list)
        check_log_groups(groups)
        check_log_services(services)
    except ParamException:
        sys.exit(1)

    cmd_collect_logs(start_timestamp, end_timestamp, node_ips, groups, services, include_disk=include_disk)

    logger.info("log_collector.cmd.main.cmd_collect, collect cluster logs done")


@cluster.command(name="clean")
@click.option(
    "--node_list",
    default=None,
    help="which node should clean logs. nodes should split by , .E.g. ***********,***********",
)
def cluster_clean(node_list):
    try:
        logger.info("log_collector.cmd.main.clean, clean cluster logs.")

        node_ips = parse_node_list(node_list)
    except ParamException:
        return
    clean_logs(node_ips)

    logger.info("log_collector.cmd.main.clean, clean cluster logs done.")


@node.command(name="collect")
@click.option(
    "--start_time",
    default=None,
    help="Start time from when logs should be collected. Time format like : %Y/%m/%d-%H:%M:%S",
)
@click.option(
    "--end_time",
    default="2037/12/30-00:00:00",
    help="End time from till logs should be collected. Time format like : %Y/%m/%d-%H:%M:%S",
)
@click.option(
    "--last_no_of_days",
    default=None,
    help="Number of days for which logs should be collected. E.g. collect 3 days of logs:",
)
@click.option(
    "--last_no_of_hours",
    default=None,
    help="Number of hours for which logs should be collected. By default, 4 hrs of logs are collected. "
    "E.g. collect 3 hours of logs",
)
@click.option(
    "--groups",
    default=None,
    help="collect the specified groups log, groups name should in %s. E.g. kernel_logs,zbs_logs,tuna_logs,node_info"
    % DEFAULT_GROUPS,
)
@click.option(
    "--services",
    default=None,
    help="collect the specified services log, default services name please check log_collector/config/log_config.json"
    " E.g. tuna-exporter,elf-vm-monitor,zbs-chunk",
)
@click.option("--include_disk", default=True, is_flag=True, help="collect node hardware info include disk")
def collect(start_time, end_time, last_no_of_days, last_no_of_hours, groups, services, include_disk):
    logger.info(
        "log_collector.cmd.main.collect, collect node logs. start_time = {}, end_time={}, last_no_of_days = {}, "
        "last_no_of_hours = {}, groups = {}, services = {}".format(
            start_time, end_time, last_no_of_days, last_no_of_hours, groups, services
        )
    )

    try:
        temp_start_time = start_time
        if start_time is None:
            start_time = "1970/01/01-00:00:00"
        start_timestamp = max(
            parse_time(start_time, "start_time"),
            parse_last_days(last_no_of_days, last_no_of_hours is None and temp_start_time is None),
            parse_last_hours(last_no_of_hours, last_no_of_days is None and temp_start_time is None),
        )
        end_timestamp = parse_time(end_time, "end_time")

        if int(start_timestamp) >= int(end_timestamp):
            print("invalid time scope, log end time must be greater than start time")
            raise ParamException("invalid time scope")

        check_log_groups(groups)
        check_log_services(services)
    except ParamException:
        sys.exit(1)
    group_service_pattern = LogConf().build_log_config_pattern(groups, services)
    collect_local_logs(
        start_timestamp,
        end_timestamp,
        str(uuid.uuid1()),
        MAXIMUM_LOG_SIZE,
        group_service_pattern,
        include_disk=include_disk,
        collect_type="node",
    )

    logger.info("log_collector.cmd.main.collect, collect node logs done")


@node.command()
def clean():
    logger.info("log_collector.cmd.main.clean, clean node logs.")

    mgt_ips = ZbsConf.get_mgt_ips()
    clean_logs([x for x in mgt_ips if ZbsConf.check_local_ip(x)])

    logger.info("log_collector.cmd.main.clean, clean node logs done.")


@config.command()
def show():
    from log_collector.utils.log_config import LogConf

    log_config = LogConf().load_log_config()
    for item in log_config:
        group_name = click.style(item["group_name"], fg="red")
        click.echo(group_name)
        service_name = ""
        cnt = 0
        for service in item["service_list"]:
            if cnt > 5:
                service_name += "\n{}".format(" " * 4)
                cnt = 0
            service_name += service["name"] + " "
            cnt += 1
        service_name = click.style(service_name, fg="green")
        click.echo("{}{}".format(" " * 4, service_name))
