# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
import json
import os

from log_collector.cluster.cmd_collect import CollectLog
from log_collector.cluster.task_clean import Task as CleanTask
from log_collector.cluster.task_collect import Task as CollectTask
from log_collector.cluster.task_kill import Task as KillTask
from log_collector.config.constants import LOG_METADATA_PATH
from log_collector.config.logger import logger
from log_collector.utils.zbs_conf import ZbsConf


class CollectTaskHolder:
    def __init__(self):
        self.cur_task = None
        if not self.cur_task:
            self.clean_pre_running_metadata()

    def set_task(self, task):
        self.cur_task = task

    def is_task_running(self):
        if not self.cur_task:
            return False
        if self.cur_task.is_alive() and self.cur_task.collect_status == "running":
            return True
        else:
            return False

    def clean_pre_running_metadata(self):
        running_metadata = []
        for file_name in os.listdir(LOG_METADATA_PATH):
            with open(LOG_METADATA_PATH + "/" + str(file_name), "r+") as f:
                try:
                    json_obj = json.load(f)
                    if json_obj["status"] == "running":
                        json_obj["status"] = "failed"
                        f.seek(0)
                        json.dump(json_obj, f, indent=4, separators=(",", ": "))
                        f.truncate()
                except ValueError as e:
                    logger.warning("set previous running metadata failed: {}".format(e))
        return running_metadata


task_holder = CollectTaskHolder()


def api_collect_logs(
    start_time, end_time, node_list, groups=None, services=None, task_uuid=None, send_log_to_controller=True
):
    logger.info(
        "log_collector.cmd.cluster_logs.collect_logs, start_time = {}, end_time={}, node_list = {}, "
        "groups = {}, services = {}, task_uuid = {}, send_log_to_controller = {}".format(
            start_time, end_time, node_list, groups, services, task_uuid, send_log_to_controller
        )
    )

    task = CollectTask(
        node_list,
        start_time=int(start_time),
        end_time=int(end_time),
        groups=groups,
        services=services,
        task_uuid=task_uuid,
        send_log_to_controller=send_log_to_controller,
    )
    task.start()
    task_holder.set_task(task)


def cmd_collect_logs(
    start_time,
    end_time,
    node_list,
    groups=None,
    services=None,
    task_uuid=None,
    send_log_to_controller=True,
    include_disk=False,
):
    logger.info(
        "log_collector.cmd.cluster_logs.cmd_collect_logs, start_time = {}, end_time={}, node_list = {}, "
        "groups = {}, services = {}, task_uuid = {}, send_log_to_controller = {}".format(
            start_time, end_time, node_list, groups, services, task_uuid, send_log_to_controller
        )
    )

    task = CollectLog(
        node_list,
        start_time=int(start_time),
        end_time=int(end_time),
        groups=groups,
        services=services,
        task_uuid=task_uuid,
        include_disk=include_disk,
    )
    task.collect()


def clean_logs(node_list=None, task_uuid=None):
    logger.info("log_collector.cmd.api.clean_logs, node_list = {}, task_uuid = {}".format(node_list, task_uuid))

    if task_uuid is not None:
        node_list = ZbsConf.get_mgt_ips()

    task = CleanTask(node_list, task_uuid)
    task.start()

    logger.info("log_collector.cmd.collect_cluster_log.clean_logs, clean done.")


def kill_task(task_uuid):
    logger.info("log_collector.cmd.api.kill_task, task_uuid = %s" % task_uuid)
    task = KillTask(task_uuid)
    task.start()
