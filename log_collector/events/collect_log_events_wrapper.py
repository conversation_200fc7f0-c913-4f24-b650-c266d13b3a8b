# Copyright (c) 2013-2019, SMARTX
# All rights reserved.

from common.event.event_message import build_event, event_enclosure
from log_collector.events.constants import (
    CLUSTER,
    EVENT_COLLECT_ALL_NODES_LOG,
    EVENT_COLLECT_SELECTED_NODES_LOG,
)
import log_collector.events.message_i18n_tuna as i18n
from tuna.cluster.cluster_manager import ClusterManager


class CollectLogEventWrapper:
    def __init__(self, user):
        self.user_name = user.get("username", "-")
        self.user_role = user.get("Role")
        self.cluster_id = ClusterManager.get_cluster_uuid()

    @event_enclosure
    def collect_log_event_splicing(self, event_name, detail=None):
        if not detail:
            detail = {"zh_CN": "None", "en_US": "None"}

        if isinstance(self.cluster_id, bytes):
            self.cluster_id = self.cluster_id.decode()
        resources = {self.cluster_id: CLUSTER}

        data = {"cluster_id": self.cluster_id}

        return build_event(
            event_name=event_name,
            user=self.user_name,
            user_role=self.user_role,
            data=data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(event_name),
        )

    @event_enclosure
    def event_collect_all_nodes_log(self):
        return self.collect_log_event_splicing(EVENT_COLLECT_ALL_NODES_LOG)

    @event_enclosure
    def event_collect_selected_nodes_log(self):
        return self.collect_log_event_splicing(EVENT_COLLECT_SELECTED_NODES_LOG)
