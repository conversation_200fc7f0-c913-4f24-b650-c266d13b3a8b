# Copyright (c) 2013-2018, SMARTX
# All rights reserved.


import datetime
import json
import threading
import time

from log_collector.config.constants import (
    CLEAN_CLUSTER_LOG_PLAYBOOK,
    LOG_LOG_PATH,
    LOG_METADATA_PATH,
    LOG_TEMP_PATH,
    <PERSON><PERSON><PERSON><PERSON>_PATH,
    <PERSON><PERSON><PERSON>_PATH,
)
from log_collector.utils.ansible_executor import Executor
from log_collector.utils.inventory import Inventory
from log_collector.utils.zbs_conf import ZbsConf

task_start_info = """
#########################################
# TASK: {task_name}
# TIME: {time}
#########################################
"""


task_end_info = """
clean task done
state: {task_state}
"""


class Task(threading.Thread):
    def __init__(self, node_list, task_uuid=None):
        threading.Thread.__init__(self)
        self.node_list = node_list
        self.task_uuid = task_uuid

    def run(self):
        print(task_start_info.format(task_name="CLEAN LOGS", time=str(datetime.datetime.now())))
        Inventory.generate_inventory(self.node_list)

        playbook = CLEAN_CLUSTER_LOG_PLAYBOOK
        args_json = {
            "python_path": PYTHON_PATH,
            "log_path": LOG_LOG_PATH,
            "temp_path": LOG_TEMP_PATH,
            "metadata_path": LOG_METADATA_PATH,
            "task_uuid": self.task_uuid,
            "shell_path": SHELL_PATH,
            "clean_all_logs": "yes" if self.task_uuid is None else "no",
            "include_controller": "yes" if any(ZbsConf.check_local_ip(x) for x in self.node_list) else "no",
        }

        executor = Executor(playbook)
        executor.execute(args_str=json.dumps(args_json))
        executor.write_stdout_to_log()
        while True:
            time.sleep(1)
            status = executor.get_status()

            if status in ["successful", "failed"]:
                print(task_end_info.format(task_state=status))
                return
