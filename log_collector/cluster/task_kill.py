# Copyright (c) 2013-2021, SMARTX
# All rights reserved.


import json
import threading

from log_collector.config.constants import (
    KILL_TASK_PLAYBOOK,
    KILL_TASK_SYMBOL,
    LOG_LOG_PATH,
    LOG_METADATA_PATH,
    LOG_TEMP_PATH,
)
from log_collector.utils.ansible_executor import Executor
from log_collector.utils.inventory import Inventory
from log_collector.utils.zbs_conf import ZbsConf


class Task(threading.Thread):
    def __init__(self, task_uuid):
        threading.Thread.__init__(self)
        self.task_uuid = task_uuid

    def run(self):
        node_list = ZbsConf.get_mgt_ips()
        Inventory.generate_inventory(node_list)

        playbook = KILL_TASK_PLAYBOOK
        args_json = {
            "log_path": LOG_LOG_PATH,
            "temp_path": LOG_TEMP_PATH,
            "metadata_path": LOG_METADATA_PATH,
            "task_uuid": self.task_uuid,
            "kill_symbol": <PERSON><PERSON><PERSON>_TASK_SYMBOL,
        }

        executor = Executor(playbook)
        executor.execute(args_str=json.dumps(args_json))
        executor.write_stdout_to_log()
