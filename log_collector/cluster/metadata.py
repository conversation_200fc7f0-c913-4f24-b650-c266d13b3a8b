# Copyright (c) 2013-2018, SMARTX
# All rights reserved.


from datetime import datetime
import json
import time

from log_collector.config.constants import LOG_LOG_PATH, LOG_METADATA_PATH
from log_collector.utils.cluster_info import get_cluster_name
from log_collector.utils.zbs_conf import ZbsConf


class MetaData:
    """
    METADATA = {{
      "task_uuid": "{task_uuid}",
      "node_list": {node_list},
      "groups": "{groups}",
      "services": "{services}",
      "collect_start_timestamp": "{collect_start_timestamp}",
      "collect_end_timestamp": "{collect_end_timestamp}",
      "start_timestamp": "{start_timestamp}",
      "end_timestamp": "{end_timestamp}",
      "path": "{path}",
      "size": "{size}",
      "status": "{status}",
      "owner": "{owner}",
      "progress": "{progress}"
    }}
    """

    def __init__(self):
        pass

    @classmethod
    def read_metadata_file(cls, task_uuid):
        file_name = LOG_METADATA_PATH + "/" + task_uuid
        with open(file_name) as f:
            meta_str = f.read()
        return json.loads(meta_str)

    @classmethod
    def init_metadata(
        cls,
        node_list,
        task_uuid,
        groups,
        services,
        start_timestamp,
        end_timestamp,
        send_log_to_controller,
        status="running",
    ):
        cluster_name = get_cluster_name()
        ip = next(x for x in ZbsConf.get_mgt_ips() if ZbsConf.check_local_ip(x))
        suffix_name = task_uuid.split("-")[0]
        log_path = LOG_LOG_PATH + "/cluster-logs-{}-{}-{}-{}.zip".format(
            cluster_name,
            datetime.fromtimestamp(start_timestamp).strftime("%Y%m%d%H"),
            datetime.fromtimestamp(end_timestamp).strftime("%Y%m%d%H"),
            suffix_name,
        )

        metadata = {
            "task_uuid": task_uuid,
            "node_list": node_list,
            "groups": groups,
            "services": services,
            "collect_start_timestamp": int(time.time()),
            "collect_end_timestamp": None,
            "start_timestamp": start_timestamp,
            "end_timestamp": end_timestamp,
            "path": log_path,
            "size": 0,
            "status": status,
            "owner": ip,
            "progress": "0%",
        }
        cls.write_metadata_file(metadata, send_log_to_controller)
        return metadata

    @classmethod
    def write_metadata_file(cls, metadata, send_log_to_controller):
        if send_log_to_controller:
            return

        file_name = LOG_METADATA_PATH + "/" + metadata["task_uuid"]

        with open(file_name, "w") as f:
            f.write(json.dumps(metadata, indent=4, separators=(",", ": ")))
