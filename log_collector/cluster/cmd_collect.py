# Copyright (c) 2013-2024, SMARTX
# All rights reserved.

import copy
import datetime
import json
import time
import uuid

import progressbar

from log_collector.cluster.metadata import MetaData
from log_collector.config.constants import (
    CMD_COLLECT_CLUSTER_LOG_PLAYBOOK,
    LOG_LOG_PATH,
    LOG_TEMP_PATH,
    NODE_LOG_PATH,
    PYTHON_PATH,
    SHELL_PATH,
)
from log_collector.config.logger import logger
from log_collector.utils.ansible_executor import Executor
from log_collector.utils.cluster_info import get_cluster_name
from log_collector.utils.collect_progress import clean_tmp_process_file, init_process_status, write_process
from log_collector.utils.inventory import Inventory
from log_collector.utils.log_config import LogConf
from log_collector.utils.log_size import every_node_log_threshold
from log_collector.utils.zbs_conf import ZbsConf

task_start_info = """
#########################################
# TASK: {task_name}
# TIME: {time}
#########################################
"""


task_end_info = """
collect task done
state: {task_state}
"""


log_position_info = """
collected log file: {log_file}
"""


class CollectLog:
    def __init__(
        self,
        node_list,
        start_time=None,
        end_time=None,
        groups=None,
        services=None,
        task_uuid=None,
        include_disk=True,
    ):
        self.start_time = start_time
        self.end_time = end_time
        self.log_groups = groups
        self.log_services = services
        self.send_log_to_controller = True
        self.task_uuid = str(uuid.uuid1()) if task_uuid is None else task_uuid
        self.node_list = node_list
        self.include_controller = (
            "yes" if len([x for x in node_list if not ZbsConf.check_local_ip(x)]) < len(node_list) else "no"
        )
        self.include_disk = include_disk
        self.process = 0
        self.progress_bar = None
        self.group_service_pattern = LogConf().build_log_config_pattern(self.log_groups, self.log_services)

        self.metadata = MetaData.init_metadata(
            self.node_list,
            self.task_uuid,
            self.log_groups,
            self.log_services,
            self.start_time,
            self.end_time,
            self.send_log_to_controller,
        )
        init_process_status(self.task_uuid)

    def collect(self):
        print(task_start_info.format(task_name="COLLECT LOGS", time=str(datetime.datetime.now())))
        Inventory.generate_inventory(copy.deepcopy(self.node_list))

        playbook = CMD_COLLECT_CLUSTER_LOG_PLAYBOOK
        cluster_name = get_cluster_name()
        log_file = "{}/cluster-logs-{}-{}.tar.gz".format(LOG_LOG_PATH, cluster_name, self.task_uuid)
        args_json = {
            "python_path": PYTHON_PATH,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "task_uuid": self.task_uuid,
            "log_path": LOG_LOG_PATH,
            "shell_path": SHELL_PATH,
            "temp_path": LOG_TEMP_PATH,
            "node_collector_log_path": NODE_LOG_PATH,
            "log_size": every_node_log_threshold(len(self.node_list)),
            "group_service_pattern": json.dumps(self.group_service_pattern),
            "include_disk": self.include_disk,
            "include_controller": self.include_controller,
            "log_file_name": log_file,
        }

        executor = Executor(playbook, self.task_uuid)
        executor.execute(args_str=json.dumps(args_json))
        executor.write_stdout_to_log(progress=True)
        if not self.progress_bar and self.send_log_to_controller is True:
            self.progress_bar = progressbar.ProgressBar(max_value=1)
        while True:
            time.sleep(1)
            status = executor.get_status()

            self.metadata["status"] = status

            write_process(
                self.process,
                self.progress_bar,
                self.task_uuid,
                status,
                self.metadata,
                self.include_controller,
                self.send_log_to_controller,
            )
            if status in ["successful", "failed"]:
                clean_tmp_process_file(self.task_uuid)
                log_file = "{}/cluster-logs-{}-{}.tar.gz".format(LOG_LOG_PATH, get_cluster_name(), self.task_uuid)
                print(log_position_info.format(log_file=log_file))
                print(task_end_info.format(task_state=status))
                logger.info(log_position_info.format(log_file=log_file))
                logger.info(task_end_info.format(task_state=status))
                return
