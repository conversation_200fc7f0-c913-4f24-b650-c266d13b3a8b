# directory structure

We prefers to create new package as top-level package now.    
All package can denpend on zbs, but zbs would better not have deps in other package.


## Summary

```
.
├── collectd_supports
├── collector
├── docs
├── job_center
├── integration
├── jenkins
├── job_center
├── provisioning
├── rpm
├── schema_sugar
├── smartx_app
├── zbs
├── zbs_deploy
└── zbs_rest
```

## zbs

```
├── chunk - zbs-chunk cli and rpc client
├── config - config file of pyzbs
├── deps - some non-python-package dependencies
├── dr - data recover rpc client and cli
├── l10n
├── lib - common lib
├── meta - meta client, cmd line tools
├── nfs - nfs rpc client and command line tools
├── node - some functions about single-node operations
├── proto - protobuf definition files
├── cdp - cdp job management client and command line
├── root - rpc service base(rpc channel implementation, etc)
├── test - all of zbs package's tests placed here
├── tool - zbs-tool command line tools(mongo, zk, kv, etc)
└── zservice - some service implements
```

## collectd_supports

Collectd plugins and common libs.

## smartx_app

### coral_fish
coral_fish is the metric server(r/w, aggregation).

It works as a zbs_rest plugin now.

### gazer
`ocean_gazer` is an monitoring service and event trigger for metric and status data.

It checks data from coral_fish and create events to user.

It works as zbs_rest plugin(event query) and job-center plugin(cron job to check given
status or value then trigger/auto-expire events).

### elf
`elf` is our kvm hypervisor management interface , it works as plugin of job-center
and zbs_rest.

We manage our virtual machines via `elf` module via rest-api.

## integration

functional test, etc.

## jenkins

jenkins test script

## rpm

rpm build script.

## schema_sugar

Base lib for zbs_rest's schema-base api generating.

## zbs_rest

FishEye api services.

## job_center

Job center is a celery-based task queue for our background task executor.