# Setup Environment

## Install OS

All SMARTX softwares are now based on CentOS 7.x, so please install a CnetOS 7.x
on your machine (with memory >= 4G) or vm based on
[dogfood cluster](dog.smartx.com).

You could download a dvd iso from:

+ [CentOS 7.1 dvd iso](http://192.168.17.2/distros/isostore/CentOS-7.1-x86_64-DVD-1503-01.iso)
+ [CentOS 7.2 dvd iso](http://192.168.17.2/distros/isostore/CentOS-7.2-x86_64-Everything-1511.iso)
+ [SmartX OS iso](http://192.168.17.2/distros/isostore/release_smartxos/2.3.0/SMARTXOS-2.3.0-rc6-el7-1606281540.iso)

## Setup Repo

epel.repo

```
[epel]
name=Extra Packages for Enterprise Linux 7 - $basearch
baseurl=*************************************/pub/epel/$releasever/$basearch/
#mirrorlist=https://mirrors.fedoraproject.org/metalink?repo=epel-6&arch=$basearch
failovermethod=priority
enabled=1
gpgcheck=0
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-EPEL-6

[epel-debuginfo]
name=Extra Packages for Enterprise Linux 7 - $basearch - Debug
baseurl=*************************************/pub/epel/$releasever/$basearch/debug
#mirrorlist=https://mirrors.fedoraproject.org/metalink?repo=epel-debug-6&arch=$basearch
failovermethod=priority
enabled=0
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-EPEL-6
gpgcheck=0

[epel-source]
name=Extra Packages for Enterprise Linux 7 - $basearch - Source
baseurl=*************************************/pub/epel/$releasever/SRPMS
#mirrorlist=https://mirrors.fedoraproject.org/metalink?repo=epel-source-6&arch=$basearch
failovermethod=priority
enabled=0
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-EPEL-6
gpgcheck=0
```

smartx-dev.repo

```
[smartx-dev]
name=SMARTX OS - dev
baseurl=*************************************/pub/smartxos/el$releasever/2/
gpgcheck=0
enabled=1
```

smartx-rc.repo

```
[smartx-rc]
name=SMARTX OS - rc
baseurl=*************************************/pub/smartxos/el$releasever/2-rc/
gpgcheck=0
enabled=1
```

smartx-common.repo

```
[smartx-common]
name=SMARTX OS-$releasever - common
baseurl=*************************************/pub/smartxos/el$releasever/common/
gpgcheck=0
enabled=1
```

NOTICE: You should disable smartx-dev.repo if you want to install stable
version only.


## Install ZBS and Dependencies

```
yum install zbs libzbs pyzbs
```

## Setup ZBS

### Buggy step for creating env files which should be removed later

```
touch /etc/sysconfig/zbs-metad
touch /etc/sysconfig/zbs-chunkd
```

### Start ZBS Services

```
# start zookeeper
cp /etc/zookeeper/zoo_zbs.cfg /etc/zookeeper/zoo.cfg
systemctl start zookeeper

# start meta server
systemctl start zbs-metad
zbs-meta enable

# start chunk server
systemctl start zbs-chunkd

# register chunk server
zbs-meta chunk register 127.0.0.1 10200

# verify
zbs-meta chunk list
```

### Setup ZBS Storage

```
# allocate partition space to zbs
mkdir /zbs
touch /zbs/partition-1
truncate -s 10g /zbs/partition-1
zbs-chunk partition format --force /zbs/partition-1
zbs-chunk partition mount /zbs/partition-1

# verify
zbs-chunk partiiton list

# allocate journal space to zbs
mkdir /zbs
touch /zbs/journal-1
truncate -s 10g /zbs/journal-1
zbs-chunk journal format --force /zbs/journal-1
zbs-chunk journal mount /zbs/journal-1

# verify
zbs-chunk journal list
```

### Verify ZBS

```
zbs-meta volume create default test 1
zbs-chunk volume write default test -i /dev/zero
zbs-meta volume delete default test
```

## Start pyzbs related services

### Prepare /etc/zbs/zbs.conf

Add following content to /etc/zbs/zbs.conf

```
[network]
data_interface=127.0.0.1  # or the local ip
heartbeat_interface=127.0.0.1
vm_interface=127.0.0.1
web_interface=127.0.0.1

[cluster]
role=single
zookeeper=127.0.0.1:2181
mongo=127.0.0.1:27017
```

### start pyzbs services

```
# start mongo
echo "replSet=zbs" >> /etc/mongod.conf
systemctl start mongod
zbs-node mongo create

# start zbs_rest
systemctl start zbs-rest-server
```

Other services listed in `code_structure/function.md`

Use the same method to start them(just call `systemctl`)

### Create first user

```
zbs-rest create-root
```

### Optional Services in 2.0

To start some optional service for development, for example web and rpc services.

```
# 2.x web server
python zbs_rest/main.py
```

### Optional Configuration for starting VM in KVM environment

To start VM in KVM environment, we should update VM config before starting it.

```
# Find the vm uuid
virsh list --all

# Edit its config
virsh edit [UUID]

# then do it
# 1. Update *domain*  of config from `kvm` to `qemu`
# 2. Comment interface node of this XML file
# after, try to start it
virsh start [UUID]
```
