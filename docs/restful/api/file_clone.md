# FileClone
ZBS file clone api

###/api/v2/file_clones

CURL example:

```
curl -i http://localhost:10402/api/v2/file_clones \
     -H 'Content-Type: application/json' \
     -d '{"src_export_name": "default", \
          "src_path": "file", \
          "snapshot_name": "snapshot", \
          "dst_export_name": "default", \
          "dst_path": "file_cloned"}'
```

### Parameters

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| src_export_name  | String  |  export name of source file   |
| src_path         | String  |  file path of source file     |
| snapshot_name    | String  |  snapshot name of source file |
| dst_export_name  | String  |  export name of target file   |
| dst_path         | String  |  file path of target file     |

### Success Response

```
   HTTP/1.1 200 OK
   {
        "data": {
            "preallocate": false,
            "attr": {
                "ctime": {
                    "seconds": 1452722778,
                    "nseconds": 668000390
                },
                "mode": 432,
                "mtime": {
                    "seconds": 1452722778,
                    "nseconds": 668000390
                },
                "atime": {
                    "seconds": 1452722778,
                    "nseconds": 668000390
                },
                "type": 1,
                "size": 0
            },
            "pool_id": 12,
            "parent_id": 31,
            "volume_id": 22,
            "id": 33,
            "name": "file_cloned"
        },
        "ec": "UOK",
        "error": {}
   }
```
