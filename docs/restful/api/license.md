# License
ZBS license tools
###/api/v2/tools/license

CURL example:

```
   curl -i -X GET http://localhost:10402/api/v2/tools/license
```

### Success Response

```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": {
            "max_chunk_num": 255,
            "max_physical_space_size": 3377699720527872,
            "max_pool_num": 1000000,
            "max_snap_num": 512,
            "max_volume_num": 1000000,
            "max_volume_size": 70368744177664,
            "period": 2592000,
            "serial": "b3fb6244-9989-4fd2-a805-74cbbbdde8da",
            "sign_date": **********
        },
        "error": {}
   }
```

## Update license

    PUT /api/v2/tools/license

### Parameters

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| license    | String    |  License string                   |

## Show license

    GET /api/v2/tools/license


## Parse license

	GET /api/v2/tools/parse_license?license=abcdef

### Success Response

```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": {
            "max_chunk_num": 255,
            "max_physical_space_size": 3377699720527872,
            "max_pool_num": 1000000,
            "max_snap_num": 512,
            "max_volume_num": 1000000,
            "max_volume_size": 70368744177664,
            "period": 2592000,
            "serial": "b3fb6244-9989-4fd2-a805-74cbbbdde8da",
            "sign_date": **********
        },
        "error": {}
   }
```
