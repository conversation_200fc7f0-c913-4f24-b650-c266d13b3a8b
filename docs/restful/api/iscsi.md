# Target

iscsi target api

## Query

    GET /api/v2/iscsi/targets

### Success Response

```
HTTP/1.1 200 OK
{
  "data": [
    {
      "_luns": [
        {
          "_snapshots_count": 0, 
          "bps_burst": 0, 
          "created_time": {
            "nseconds": 544451310, 
            "seconds": 1469759600
          }, 
          "description": "", 
          "iops_burst": 0, 
          "lun_id": 1, 
          "name": "90ae776c-7f8b-4cd6-88c9-6ac30a1ea6ba", 
          "pool_id": "9be0eee9-f4b8-4701-8e17-4193fe2b240b", 
          "replica_num": 2, 
          "size": 107374182400, 
          "stripe_num": 1, 
          "stripe_size": 262144, 
          "thin_provision": true, 
          "volume_id": "90ae776c-7f8b-4cd6-88c9-6ac30a1ea6ba"
        }, 
        {
          "_snapshots_count": 0, 
          "bps_burst": 0, 
          "created_time": {
            "nseconds": 126485023, 
            "seconds": 1469759602
          }, 
          "description": "", 
          "iops_burst": 0, 
          "lun_id": 2, 
          "name": "b094c1f3-efd1-45b9-82c3-101f72a503d6", 
          "pool_id": "9be0eee9-f4b8-4701-8e17-4193fe2b240b", 
          "replica_num": 2, 
          "size": 107374182400, 
          "stripe_num": 1, 
          "stripe_size": 262144, 
          "thin_provision": true, 
          "volume_id": "b094c1f3-efd1-45b9-82c3-101f72a503d6"
        }, 
        {
          "_snapshots_count": 0, 
          "bps_burst": 0, 
          "created_time": {
            "nseconds": 776678178, 
            "seconds": 1469759602
          }, 
          "description": "", 
          "iops_burst": 0, 
          "lun_id": 3, 
          "name": "bbcfd3bf-4a04-4352-988b-98e67f1aca07", 
          "pool_id": "9be0eee9-f4b8-4701-8e17-4193fe2b240b", 
          "replica_num": 2, 
          "size": 107374182400, 
          "stripe_num": 1, 
          "stripe_size": 262144, 
          "thin_provision": true, 
          "volume_id": "bbcfd3bf-4a04-4352-988b-98e67f1aca07"
        }, 
        {
          "_snapshots_count": 0, 
          "bps_burst": 0, 
          "created_time": {
            "nseconds": 866498520, 
            "seconds": 1469759604
          }, 
          "description": "", 
          "iops_burst": 0, 
          "lun_id": 4, 
          "name": "2cca7345-ef55-444d-babe-19dae76ac707", 
          "pool_id": "9be0eee9-f4b8-4701-8e17-4193fe2b240b", 
          "replica_num": 2, 
          "size": 107374182400, 
          "stripe_num": 1, 
          "stripe_size": 262144, 
          "thin_provision": true, 
          "volume_id": "2cca7345-ef55-444d-babe-19dae76ac707"
        }, 
        {
          "_snapshots_count": 0, 
          "bps_burst": 0, 
          "created_time": {
            "nseconds": 340390336, 
            "seconds": 1469786778
          }, 
          "description": "", 
          "iops_burst": 0, 
          "lun_id": 5, 
          "name": "APP_Hopper_3.1.2", 
          "pool_id": "9be0eee9-f4b8-4701-8e17-4193fe2b240b", 
          "replica_num": 2, 
          "size": 268435456, 
          "stripe_num": 1, 
          "stripe_size": 262144, 
          "thin_provision": true, 
          "volume_id": "b73b3dd0-6202-4848-b89a-fc47b6f0e711"
        }
      ], 
      "created_time": {
        "nseconds": 133214575, 
        "seconds": 1469759513
      }, 
      "id": "9be0eee9-f4b8-4701-8e17-4193fe2b240b", 
      "iqn_name": "iqn.2016-02.com.smartx:system:hyperv", 
      "name": "hyperv", 
      "pool": {
        "created_time": {
          "nseconds": 133214575, 
          "seconds": 1469759513
        }, 
        "id": "9be0eee9-f4b8-4701-8e17-4193fe2b240b", 
        "mod_verf": 4081795745, 
        "name": "hyperv", 
        "replica_num": 2, 
        "storage_pool_id": "system", 
        "thin_provision": true
      }
    },  
    {
      "_luns": [
        {
          "_snapshots_count": 0, 
          "bps_burst": 0, 
          "created_time": {
            "nseconds": 803513675, 
            "seconds": 1469862028
          }, 
          "description": "", 
          "iops_burst": 0, 
          "lun_id": 1, 
          "name": "test", 
          "pool_id": "6073be8f-a882-46c5-82f7-04d2ce692f0d", 
          "replica_num": 2, 
          "size": 268435456, 
          "stripe_num": 1, 
          "stripe_size": 262144, 
          "thin_provision": true, 
          "volume_id": "63cec47f-d1eb-4b2b-a7eb-b1194ebdaa16"
        }
      ], 
      "created_time": {
        "nseconds": 647007056, 
        "seconds": 1469431028
      }, 
      "description": "zhihao-test", 
      "id": "6073be8f-a882-46c5-82f7-04d2ce692f0d", 
      "iqn_name": "iqn.2016-02.com.smartx:system:zhihao-test-1", 
      "name": "zhihao-test-1", 
      "pool": {
        "created_time": {
          "nseconds": 647007056, 
          "seconds": 1469431028
        }, 
        "description": "zhihao-test", 
        "id": "6073be8f-a882-46c5-82f7-04d2ce692f0d", 
        "mod_verf": 4081795745, 
        "name": "zhihao-test-1", 
        "replica_num": 1, 
        "storage_pool_id": "system", 
        "thin_provision": true
      }
    }
  ], 
  "ec": "EOK", 
  "error": {}
}
```

	 GET /api/v2/iscsi/targets/<target_id>

### Success Response

```
HTTP/1.1 200 OK
{
  "data": {
    "_luns": [
      {
        "_snapshots_count": 0, 
        "bps_burst": 0, 
        "created_time": {
          "nseconds": 544451310, 
          "seconds": 1469759600
        }, 
        "description": "", 
        "iops_burst": 0, 
        "lun_id": 1, 
        "name": "90ae776c-7f8b-4cd6-88c9-6ac30a1ea6ba", 
        "pool_id": "9be0eee9-f4b8-4701-8e17-4193fe2b240b", 
        "replica_num": 2, 
        "size": 107374182400, 
        "stripe_num": 1, 
        "stripe_size": 262144, 
        "thin_provision": true, 
        "volume_id": "90ae776c-7f8b-4cd6-88c9-6ac30a1ea6ba"
      }, 
      {
        "_snapshots_count": 0, 
        "bps_burst": 0, 
        "created_time": {
          "nseconds": 126485023, 
          "seconds": 1469759602
        }, 
        "description": "", 
        "iops_burst": 0, 
        "lun_id": 2, 
        "name": "b094c1f3-efd1-45b9-82c3-101f72a503d6", 
        "pool_id": "9be0eee9-f4b8-4701-8e17-4193fe2b240b", 
        "replica_num": 2, 
        "size": 107374182400, 
        "stripe_num": 1, 
        "stripe_size": 262144, 
        "thin_provision": true, 
        "volume_id": "b094c1f3-efd1-45b9-82c3-101f72a503d6"
      }, 
      {
        "_snapshots_count": 0, 
        "bps_burst": 0, 
        "created_time": {
          "nseconds": 776678178, 
          "seconds": 1469759602
        }, 
        "description": "", 
        "iops_burst": 0, 
        "lun_id": 3, 
        "name": "bbcfd3bf-4a04-4352-988b-98e67f1aca07", 
        "pool_id": "9be0eee9-f4b8-4701-8e17-4193fe2b240b", 
        "replica_num": 2, 
        "size": 107374182400, 
        "stripe_num": 1, 
        "stripe_size": 262144, 
        "thin_provision": true, 
        "volume_id": "bbcfd3bf-4a04-4352-988b-98e67f1aca07"
      }, 
      {
        "_snapshots_count": 0, 
        "bps_burst": 0, 
        "created_time": {
          "nseconds": 866498520, 
          "seconds": 1469759604
        }, 
        "description": "", 
        "iops_burst": 0, 
        "lun_id": 4, 
        "name": "2cca7345-ef55-444d-babe-19dae76ac707", 
        "pool_id": "9be0eee9-f4b8-4701-8e17-4193fe2b240b", 
        "replica_num": 2, 
        "size": 107374182400, 
        "stripe_num": 1, 
        "stripe_size": 262144, 
        "thin_provision": true, 
        "volume_id": "2cca7345-ef55-444d-babe-19dae76ac707"
      }, 
      {
        "_snapshots_count": 0, 
        "bps_burst": 0, 
        "created_time": {
          "nseconds": 340390336, 
          "seconds": 1469786778
        }, 
        "description": "", 
        "iops_burst": 0, 
        "lun_id": 5, 
        "name": "APP_Hopper_3.1.2", 
        "pool_id": "9be0eee9-f4b8-4701-8e17-4193fe2b240b", 
        "replica_num": 2, 
        "size": 268435456, 
        "stripe_num": 1, 
        "stripe_size": 262144, 
        "thin_provision": true, 
        "volume_id": "b73b3dd0-6202-4848-b89a-fc47b6f0e711"
      }
    ], 
    "created_time": {
      "nseconds": 133214575, 
      "seconds": 1469759513
    }, 
    "id": "9be0eee9-f4b8-4701-8e17-4193fe2b240b", 
    "iqn_name": "iqn.2016-02.com.smartx:system:hyperv", 
    "name": "hyperv", 
    "pool": {
      "created_time": {
        "nseconds": 133214575, 
        "seconds": 1469759513
      }, 
      "id": "9be0eee9-f4b8-4701-8e17-4193fe2b240b", 
      "mod_verf": 4081795745, 
      "name": "hyperv", 
      "replica_num": 2, 
      "storage_pool_id": "system", 
      "thin_provision": true
    }
  }, 
  "ec": "EOK", 
  "error": {}
}
```

## Create

	POST /api/v2/iscsi/targets

|        Name        |  Type   |         Description          |
| ------------------ | ------- | ---------------------------- |
| name               | String  | export name                  |
| replica_num        | Number  | replica number               |
| thin_provision     | Boolean | thin provision               |
| iops               | Number  | iops limit of total io       |
| iops_rd            | Number  | iops limit of read io        |
| iops_wr            | Number  | iops limit of write io       |
| bps                | Number  | bps limit of total io        |
| bps_rd             | Number  | bps limit of read io         |
| bps_wr             | Number  | bps limit of write io        |
| description        | String  | target description           |
| storage_pool_id    | String  | storage pool id              |
| whitelist          | String  | target whitelist             |
| iqn_date           | String  | iqn date                     |
| iqn_naming_auth    | String  | iqn naming auth              |
| external_use       | Boolean | allow access outside cluster |
| iqn_whitelist      | String  | allowd initiator iqn list    |
| target_chap_name   | String  | target chap name             |
| target_chap_secret | String  | target chap secret           |
| initiator_chaps    | Object  | initiator chap list          |

## Delete

	DELETE /api/v2/iscsi/targets/<target_id>

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| recursive  | Boolean    | delete all luns if set to true, otherwise only allow to delete empty target	|

## Update

	PUT /api/v2/iscsi/targets/<target_id>

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| name  | String    |  export name	|
| replica_num    |	Number    |  replica number	|
| thin_provision    | Boolean    |  thin provision	|
| description    | String    |  target description	|
| whitelist    | String    |  target whitelist	|
| iqn_date    | String    |  iqn date	|
| iqn_naming_auth    | String    | iqn naming auth	|

# Lun

iscsi lun api

## Query

	GET /api/v2/iscsi/targets/<target_id>/luns

### Success Response

```
HTTP/1.1 200 OK
{
  "data": [
    {
      "_snapshots_count": 0, 
      "bps_burst": 0, 
      "created_time": {
        "nseconds": 544451310, 
        "seconds": 1469759600
      }, 
      "description": "", 
      "iops_burst": 0, 
      "lun_id": 1, 
      "name": "90ae776c-7f8b-4cd6-88c9-6ac30a1ea6ba", 
      "pool_id": "9be0eee9-f4b8-4701-8e17-4193fe2b240b", 
      "replica_num": 2, 
      "size": 107374182400, 
      "stripe_num": 1, 
      "stripe_size": 262144, 
      "thin_provision": true, 
      "volume_id": "90ae776c-7f8b-4cd6-88c9-6ac30a1ea6ba"
    }, 
    {
      "_snapshots_count": 0, 
      "bps_burst": 0, 
      "created_time": {
        "nseconds": 126485023, 
        "seconds": 1469759602
      }, 
      "description": "", 
      "iops_burst": 0, 
      "lun_id": 2, 
      "name": "b094c1f3-efd1-45b9-82c3-101f72a503d6", 
      "pool_id": "9be0eee9-f4b8-4701-8e17-4193fe2b240b", 
      "replica_num": 2, 
      "size": 107374182400, 
      "stripe_num": 1, 
      "stripe_size": 262144, 
      "thin_provision": true, 
      "volume_id": "b094c1f3-efd1-45b9-82c3-101f72a503d6"
    }
  ], 
  "ec": "EOK", 
  "error": {}
}
```

	GET /api/v2/iscsi/targets/<target_id>/luns/<lun_id>

### Success Response

```
HTTP/1.1 200 OK
{
  "data": {
    "_snapshots_count": 0, 
    "bps_burst": 0, 
    "created_time": {
      "nseconds": 544451310, 
      "seconds": 1469759600
    }, 
    "description": "", 
    "iops_burst": 0, 
    "lun_id": 1, 
    "name": "90ae776c-7f8b-4cd6-88c9-6ac30a1ea6ba", 
    "pool_id": "9be0eee9-f4b8-4701-8e17-4193fe2b240b", 
    "replica_num": 2, 
    "size": 107374182400, 
    "stripe_num": 1, 
    "stripe_size": 262144, 
    "thin_provision": true, 
    "volume_id": "90ae776c-7f8b-4cd6-88c9-6ac30a1ea6ba"
  }, 
  "ec": "EOK", 
  "error": {}
}
```

## Create

	POST /api/v2/iscsi/targets/<target_id>/luns

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| lun_name    | String    |  lun name	|
| lun_id    | Number    | lun id	|
| description    | String    |  lun description	|
| replica_num    |	Number    |  replica number	|
| thin_provision    | Boolean    |  thin provision	|
| src_lun_id    | Number    |  src lun id	|
| iops_burst    | Number    |  iops burst	|
| bps_burst    | Number    | bps burst	|
| bps  | Number    |  bps	|
| iops    | Number    | iops	|
| size    | Number    | size	|
| stripe_num  | Number    |  stripe number	|
| stripe_size  | Number    |  stripe size	|
| src_snapshot_id  | String    |  src snapshot id	|
| src_target_id  | String    |  src target id	|

## Delete

	DELETE /api/v2/iscsi/targets/<target_id>/luns/<lun_id>

## Update
	PUT /api/v2/iscsi/targets/<target_id>/luns/<lun_id>

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| lun_name    | String    |  lun name	|
| description    | String    |  lun description	|
| replica_num    |	Number    |  replica number	|
| thin_provision    | Boolean    |  thin provision	|
| iops_burst    | Number    |  iops burst	|
| bps_burst    | Number    | bps burst	|
| bps  | Number    |  bps	|
| iops    | Number    | iops	|
| size    | Number    | size	|

## Move

	POST /api/v2/iscsi/targets/<target_id>/luns/<lun_id>/move

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| target_id    | String    |  destination target id	|
| lun_id    |	Number    |  destination lun id		|
| lun_name    | String    |  destination lun name	|

## Upload

	POST /api/v2/volume_upload?description=<lun_description>&device=iscsi&name=<lun_name>&target_name=<target_name>

# Snapshot

## Query

	GET /api/v2/iscsi/targets/<target_id>/snapshots

### Success Response

```
HTTP/1.1 200 OK
{
  "data": [
    {
      "alloced_physical_bytes": 536870912, 
      "alloced_virtual_bytes": 268435456, 
      "bps_burst": 0, 
      "created_time": {
        "nseconds": 998816251, 
        "seconds": 1469873567
      }, 
      "description": "", 
      "diff_size": 268435456, 
      "id": "c94ff9d9-6914-4e9b-bbd7-dda2ca69c33a", 
      "iops_burst": 0, 
      "is_snapshot": true, 
      "name": "PinDesign46-snapshot-0", 
      "nfs_meta": {}, 
      "origin_id": "", 
      "parent_id": "b8e01fae-240a-4d47-b566-a0c608bfc01d", 
      "replica_num": 2, 
      "size": 268435456, 
      "snapshot_pool_id": "66017b45-9701-49ed-a2de-39d725e0f342", 
      "thin_provision": true
    }, 
    {
      "alloced_physical_bytes": 536870912, 
      "alloced_virtual_bytes": 268435456, 
      "bps_burst": 0, 
      "created_time": {
        "nseconds": 877837060, 
        "seconds": 1469873563
      }, 
      "description": "", 
      "diff_size": 268435456, 
      "id": "f6ffac3f-f066-4f1a-a68d-f40cc10493e3", 
      "iops_burst": 0, 
      "is_snapshot": true, 
      "name": "lun-upload-test-snapshot-0", 
      "nfs_meta": {}, 
      "origin_id": "", 
      "parent_id": "70755298-0172-4d14-9a12-31410d2a88cb", 
      "replica_num": 2, 
      "size": 268435456, 
      "snapshot_pool_id": "66017b45-9701-49ed-a2de-39d725e0f342", 
      "thin_provision": true
    }
  ], 
  "ec": "EOK", 
  "error": {}
}
```
	GET /api/v2/iscsi/targets/<target_id>/snapshots/<snapshot_id>

```
HTTP/1.1 200 OK
{
  "data": {
    "alloced_physical_bytes": 536870912, 
    "alloced_virtual_bytes": 268435456, 
    "bps_burst": 0, 
    "created_time": {
      "nseconds": 877837060, 
      "seconds": 1469873563
    }, 
    "description": "", 
    "diff_size": 268435456, 
    "id": "f6ffac3f-f066-4f1a-a68d-f40cc10493e3", 
    "iops_burst": 0, 
    "is_snapshot": true, 
    "name": "lun-upload-test-snapshot-0", 
    "nfs_meta": {}, 
    "origin_id": "", 
    "parent_id": "70755298-0172-4d14-9a12-31410d2a88cb", 
    "replica_num": 2, 
    "size": 268435456, 
    "snapshot_pool_id": "66017b45-9701-49ed-a2de-39d725e0f342", 
    "thin_provision": true
  }, 
  "ec": "EOK", 
  "error": {}
}
```

## Create

   	POST /api/v2/iscsi/targets/<target_id>/snapshots

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| lun_id    | Number   |  src lun id	|
| name    |	String    |  snapshot name		|
| desc    | String    |  snapshot description	|

## Delete

	DELETE /api/v2/iscsi/targets/<target_id>/snapshots/<snapshot_id>

## Update

	PUT /api/v2/iscsi/targets/<target_id>/snapshots/<snapshot_id>

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| name    |	String    |  snapshot name		|
| desc    | String    |  snapshot description	|

## Rollback

    POST /api/v2/iscsi/targets/<target_id>/snapshots/<snapshot_id>/rollback

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| lun_id    |	String    |  lun id		|

## Move

    POST /api/v2/iscsi/targets/<target_id>/snapshots/<snapshot_id>/move

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| dst_target_id    |	String    |  destination target id		|

# initiator_chap

iSCSI Target Initiator CHAP API
## List

    GET /api/v2/iscsi/targets/<target_id>/initiator_chaps/

## Create

    POST /api/v2/iscsi/targets/<target_id>/initiator_chaps/

|     Name      |  Type  |     Description      |
| ------------- | ------ | -------------------- |
| initiator_iqn | string | the iqn of initiator |
| name          | string | the chap name        |
| secret        | string | the chap secret      |
| enabled       | bool   | whether to enable    |

## Query

    GET /api/v2/iscsi/targets/<target_id>/initiator_chaps/<initiator_iqn>

### Success Response

```
HTTP/1.1 200 OK
{
  "data": {
    "chap_name": "ichap_1",
    "enable": true,
    "iqn": "iqn.2016-02.com.smartx:system:target-1",
    "secret": "abc123abc123"
  },
  "ec": "EOK",
  "error": {}
}
```

## Update

    PUT /api/v2/iscsi/targets/<target_id>/initiator_chaps/<initiator_iqn>

|  Name   |  Type  |     Description      |
| ------- | ------ | -------------------- |
| name    | string | the iqn of initiator |
| secret  | string | the chap secret      |
| enabled | bool   | whether to enable    |

## Delete

    DELETE /api/v2/iscsi/targets/<target_id>/initiator_chaps/<initiator_iqn>
