# Export

nfs export api

### Query

    GET /api/v2/nfs/exports

### Success Response

```
 HTTP/1.1 200 OK
{
	"data" : [{
			"_inode" : {
				"attr" : {
					"atime" : {
						"nseconds" : 554300213,
						"seconds" : 1468927853
					},
					"ctime" : {
						"nseconds" : 47278181,
						"seconds" : 1468937153
					},
					"mode" : 511,
					"mtime" : {
						"nseconds" : 47278181,
						"seconds" : 1468937153
					},
					"nlink" : 3,
					"size" : 4096,
					"type" : 2
				},
				"id" : "171d9934-e689-41f1",
				"name" : "171d9934-e689-41f1",
				"parent_id" : "zbs",
				"pool_id" : "171d9934-e689-41f1-9f4b-1a0b5ac4bcd8"
			},
			"created_time" : {
				"nseconds" : 554300213,
				"seconds" : 1468927853
			},
			"id" : "171d9934-e689-41f1-9f4b-1a0b5ac4bcd8",
			"name" : "paula-test",
			"nfs_export" : true,
			"replica_num" : 2,
			"storage_pool_id" : "system",
			"thin_provision" : true
		}, {
			"_inode" : {
				"attr" : {
					"atime" : {
						"nseconds" : 488738906,
						"seconds" : 1469102540
					},
					"ctime" : {
						"nseconds" : 488738906,
						"seconds" : 1469102540
					},
					"mode" : 511,
					"mtime" : {
						"nseconds" : 488738906,
						"seconds" : 1469102540
					},
					"nlink" : 2,
					"size" : 4096,
					"type" : 2
				},
				"id" : "190ef87c-6ef8-4624",
				"name" : "190ef87c-6ef8-4624",
				"parent_id" : "zbs",
				"pool_id" : "190ef87c-6ef8-4624-a8b4-12c0db97eabd"
			},
			"created_time" : {
				"nseconds" : 488738906,
				"seconds" : 1469102540
			},
			"id" : "190ef87c-6ef8-4624-a8b4-12c0db97eabd",
			"name" : "kyle1",
			"nfs_export" : true,
			"replica_num" : 2,
			"storage_pool_id" : "a2c5d898-a6a4-4369-afaf-da5cc7e0b023",
			"thin_provision" : true
		}
	],
	"ec" : "EOK",
	"error" : {}
}

```

    GET /api/v2/nfs/exports/<export_id>

### Success Response

```
HTTP/1.1 200 OK
{
	"data" : {
		"_inode" : {
			"attr" : {
				"atime" : {
					"nseconds" : 554300213,
					"seconds" : 1468927853
				},
				"ctime" : {
					"nseconds" : 47278181,
					"seconds" : 1468937153
				},
				"mode" : 511,
				"mtime" : {
					"nseconds" : 47278181,
					"seconds" : 1468937153
				},
				"nlink" : 3,
				"size" : 4096,
				"type" : 2
			},
			"id" : "171d9934-e689-41f1",
			"name" : "171d9934-e689-41f1",
			"parent_id" : "zbs",
			"pool_id" : "171d9934-e689-41f1-9f4b-1a0b5ac4bcd8"
		},
		"created_time" : {
			"nseconds" : 554300213,
			"seconds" : 1468927853
		},
		"id" : "171d9934-e689-41f1-9f4b-1a0b5ac4bcd8",
		"name" : "paula-test",
		"nfs_export" : true,
		"replica_num" : 2,
		"storage_pool_id" : "system",
		"thin_provision" : true
	},
	"ec" : "EOK",
	"error" : {}
}
```

## Create

	POST /api/v2/nfs/exports

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| name  | String    |  export name	|
| replica_num    |	Number    |  replica number	|
| thin_provision    | Boolean    |  thin provision	|
| description    | String    |  export description	|
| storage_pool_id    | String    |  storage pool id	|
| whitelist    | String    |  export whitelist	|

## Delete

	DELTE /api/v2/nfs/exports/<export_id>

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| force  | Boolean    |  delete all inodes if set to true, otherwise only allow to delete empty export	|

## Update

	PUT /api/v2/nfs/exports/<export_id>


| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| name  | String    |  export name	|
| replica_num    |	Number    |  replica number	|
| thin_provision    | Boolean    |  thin provision	|
| description    | String    |  export description	|
| whitelist    | String    |  export whitelist	|

# Inode

nfs inode api

## Query

	GET /api/v2/nfs/exports/<export_id>/inodes/<inode_id>

### Success Response

```
HTTP/1.1 200 OK
{
	"data" : {
		"inodes" : [{
				"attr" : {
					"atime" : {
						"nseconds" : 572429939,
						"seconds" : 1468927934
					},
					"ctime" : {
						"nseconds" : 695179366,
						"seconds" : 1469093916
					},
					"mode" : 504,
					"mtime" : {
						"nseconds" : 695179366,
						"seconds" : 1469093916
					},
					"nlink" : 3,
					"size" : 4096,
					"type" : 2
				},
				"id" : "7006e95e-4b6c-48f3",
				"name" : "test",
				"parent_id" : "171d9934-e689-41f1",
				"pool_id" : "171d9934-e689-41f1-9f4b-1a0b5ac4bcd8"
			}
		],
		"parent_hierarchy_inodes" : [{
				"id" : "171d9934-e689-41f1",
				"name" : "171d9934-e689-41f1",
				"parent_id" : "zbs"
			}
		]
	},
	"ec" : "EOK",
	"error" : {}
}
```

	GET /api/v2/nfs/exports/<export_id>/inodes?parent_id=xxxx&nfs_type=1

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| parent_id  | String    |  dir id	|
| nfs_type    |	String    |  nfs type, default to all. NFS_TYPE_FILE = 1, NFS_TYPE_DIR = 2	|

## Create

	POST /api/v2/nfs/exports/<export_id>/inodes

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| name  | String    |  inode name	|
| nfs_type    |	String    |  nfs type, NFS_TYPE_FILE = 1, NFS_TYPE_DIR = 2	|
| parent_id    |	String    |  dir id	|
| create_how    |	String    |  create how	|
| mode    |	Number    |  mode	|
| uid    |	Number    |  user id	|
| gid    |	Number    |  user group id|
| size    |	Number    |  size	|
| atime_how    |	String    |  atime how	|
| mtime_how    |	String    |  mtime how	|
| atime_sec    |	Number    |  atime sec	|
| atime_nsec    |	Number    |  atime nsec |
| mtime_sec    |	Number    |  mtime sec	|
| mtime_nsec    |	Number    |  mtime nsec	|
| src_inode_id    |	String    |  src inode id for clone	|
| src_snapshot_id    |	String    |  src snapshot id for rollback	|
| preallocate    |	Boolean    |  preallocate	|

## Delete
	DELETE /api/v2/nfs/exports/<export_id>/inodes/<inode_id>

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| recursive  | Boolean    |   recursive deletion	|

## Update

	PUT /api/v2/nfs/exports/<export_id>/inodes/<inode_id>

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| mode    |	Number    |  mode	|
| uid    |	Number    |  user id	|
| gid    |	Number    |  user group id|
| size    |	Number    |  size	|
| atime_how    |	String    |  atime how	|
| mtime_how    |	String    |  mtime how	|
| atime_sec    |	Number    |  atime sec	|
| atime_nsec    |	Number    |  atime nsec |
| mtime_sec    |	Number    |  mtime sec	|
| mtime_nsec    |	Number    |  mtime nsec	|

## Rename

	POST /api/v2/nfs/exports/<export_id>/inodes/<inode_id>/rename

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| to_parent_id    |	String    |  destination dir id, it must be in same export with src inode.	|
| to_name    |	String    |  name	|

## Move

	POST /api/v2/nfs/exports/<export_id>/inodes/<inode_id>/move

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| dst_export_id    |	String    |  destination export id, it must be different with src export	|
| dst_parent_id    |	String    |  destination dir id	|
| dst_file_name    |	String    |  name	|

## Upload
	POST /api/v2/volume_upload?description=<nfs_file_description>&device=nfs&name=<nfs_file_name>&export_id=<export_id>&parent_id=<dir_id>

# Snapshot

nfs file snapshot api

## Query

	GET /api/v2/nfs/exports/<export_id>/snapshots/<snapshot_id>

### Success Response

```
HTTP/1.1 200 OK
{
	"data" : {
		"_related_inode_id" : "b6287564-8aa9-49c1",
		"alloced_physical_bytes" : 536870912,
		"alloced_virtual_bytes" : 268435456,
		"bps_burst" : 0,
		"created_time" : {
			"nseconds" : 116438273,
			"seconds" : 1469096679
		},
		"description" : "",
		"diff_size" : 0,
		"id" : "1ba853b8-fc64-4d41-809a-047364598de5",
		"iops_burst" : 0,
		"is_snapshot" : true,
		"name" : "nfs_rest_api_rename-snapshot-0",
		"nfs_meta" : {
			"atime" : {
				"nseconds" : 881531550,
				"seconds" : 1469096412
			},
			"ctime" : {
				"nseconds" : 123432713,
				"seconds" : 1469096497
			},
			"gid" : 999,
			"mode" : 384,
			"mtime" : {
				"nseconds" : 881531550,
				"seconds" : 1469096412
			},
			"size" : 4946,
			"type" : 1,
			"uid" : 999
		},
		"origin_id" : "",
		"parent_id" : "b6287564-8aa9-49c1-8967-746da806a3fa",
		"replica_num" : 2,
		"size" : 268435456,
		"snapshot_pool_id" : "7b9621c5-264c-4c68-a10e-e6c1289e0ba5",
		"thin_provision" : true
	},
	"ec" : "EOK",
	"error" : {}
}
```

    GET /api/v2/nfs/exports/<export_id>/snapshots

### Success Response

```
HTTP/1.1 200 OK
{
	"data" : [{
			"_related_inode_id" : "b6287564-8aa9-49c1",
			"alloced_physical_bytes" : 536870912,
			"alloced_virtual_bytes" : 268435456,
			"bps_burst" : 0,
			"created_time" : {
				"nseconds" : 116438273,
				"seconds" : 1469096679
			},
			"description" : "",
			"diff_size" : 0,
			"id" : "1ba853b8-fc64-4d41-809a-047364598de5",
			"iops_burst" : 0,
			"is_snapshot" : true,
			"name" : "nfs_rest_api_rename-snapshot-0",
			"nfs_meta" : {
				"atime" : {
					"nseconds" : 881531550,
					"seconds" : 1469096412
				},
				"ctime" : {
					"nseconds" : 123432713,
					"seconds" : 1469096497
				},
				"gid" : 999,
				"mode" : 384,
				"mtime" : {
					"nseconds" : 881531550,
					"seconds" : 1469096412
				},
				"size" : 4946,
				"type" : 1,
				"uid" : 999
			},
			"origin_id" : "",
			"parent_id" : "b6287564-8aa9-49c1-8967-746da806a3fa",
			"replica_num" : 2,
			"size" : 268435456,
			"snapshot_pool_id" : "7b9621c5-264c-4c68-a10e-e6c1289e0ba5",
			"thin_provision" : true
		}, {
			"_related_inode_id" : "c05237aa-75b2-47de",
			"alloced_physical_bytes" : 1073741824,
			"alloced_virtual_bytes" : 536870912,
			"bps_burst" : 0,
			"created_time" : {
				"nseconds" : 832124058,
				"seconds" : 1469098629
			},
			"description" : "",
			"diff_size" : 0,
			"id" : "495dddac-39c6-4af7-9eb7-4ca675567c61",
			"iops_burst" : 0,
			"is_snapshot" : true,
			"name" : "VMware-workstation-full-12.1.1-3770994-snapshot-1",
			"nfs_meta" : {
				"atime" : {
					"nseconds" : 702895945,
					"seconds" : 1469096376
				},
				"ctime" : {
					"nseconds" : 790943658,
					"seconds" : 1469096876
				},
				"gid" : 999,
				"mode" : 384,
				"mtime" : {
					"nseconds" : 790943658,
					"seconds" : 1469096876
				},
				"size" : 307937320,
				"type" : 1,
				"uid" : 999
			},
			"origin_id" : "631ebda0-**************-d0ea8861742b",
			"parent_id" : "c05237aa-75b2-47de-aba0-0a2300169b25",
			"replica_num" : 2,
			"size" : 536870912,
			"snapshot_pool_id" : "7b9621c5-264c-4c68-a10e-e6c1289e0ba5",
			"thin_provision" : true
		}
	],
	"ec" : "EOK",
	"error" : {}
}
```

## Creat

	POST /api/v2/nfs/exports/<export_id>/snapshots

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| name    |	String    |  snapshot name	|
| desc    |	String    |  snapshot description	|
| inode_id    |	String    |  inode id to create snapshot	|

## Delete

	DELETE /api/v2/nfs/exports/<export_id>/snapshots/<snapshot_id>

## Update

	PUT /api/v2/nfs/exports/<export_id>/snapshots/<snapshot_id>

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| name    |	String    |  snapshot name	|
| desc    |	String    |  snapshot description	|

## Rollback

    POST /api/v2/nfs/exports/<export_id>/snapshots/<snapshot_id>/rollback

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| inode_id    |	String    |  inode id to rollback	|

## Move

	POST /api/v2/nfs/exports/<export_id>/snapshots/<snapshot_id>/move

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| dst_export_id    |	String    |  dst export id	|