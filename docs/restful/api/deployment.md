# Deployment
Deployment related api, including access host hardware info and deploy host

## API List

| Operation                                       |     HTTP Request                        |
| ----                                            |      -----------                        |
| [cluster info](#get-cluster-info)               | GET  /api/v2/deployment/cluster         |
| [cluster deploy](#deploy-cluster)               | POST /api/v2/deployment/cluster         |
| [Add host](#add-host)                           | POST /api/v2/deployment/cluster/add_host    |
| [host info](#get-host-info)                     | GET /api/v2/deployment/host             |
| [host deploy](#deploy-host)                     | POST /api/v2/deployment/host           |
| [host deploy status](#get-host-deploy-status)   | GET /api/v2/deployment/host/deploy\_status   |       
| [host deploy log](#get-host-deploy-log)         | GET /api/v2/deployment/host/deploy\_log      |
| [host heartbeat](#get-host-heartbeat)           | GET /api/v2/deployment/host/heartbeat        |
| [ping ip](#can-ping)                            | GET /api/v2/deployment/can_ping           |
| [deploy config](#get-deploy-config)             | GET /api/v2/deployment/deploy_config      |
| [deploy version](#get-deploy-version)           | GET /api/v2/deployment/deploy_version      |


## API Detail


### <a name="get-cluster-info"></a> Get Cluster Info

Get all no deployed hosts information in cluster

#### HTTP request
      GET /api/v2/deployment/cluster

#### Success Response
```
{
    "data": {
        "hosts": [
            {
                "host_ip": "fe80::250:56ff:fe9f:495e%eth0",
                "disks": [
                    {
                        "drive": "sda",
                        "type": "HDD",   # should be "HDD" or "SSD"
                        "model": "VBOX HARDDISK",
                        "serial": "VB66c75745-aac27c80",
                        "size": 53687091200
                    },
                    {
                        "drive": "sdb",
                        "type": "SSD",
                        "model": "VBOX HARDDISK",
                        "serial": "VB66c75745-aac27c80",
                        "size": 42949672960
                    }
                ],
                "ifaces": [
                    {
                        "name": "eth0",
                        "speed": "1000Mb/s"
                    },
                    {
                        "name": "eth1",
                        "speed": "10000Mb/s"
                    }
                ]
            }
        ]
    }
    "ec": "EOK",
    "error": {}
}
```
-------------

### <a name="get-host-info"></a> Get Host Info

Get host info.

#### HTTP request
      GET /api/v2/deployment/host

#### URL path parameter
| Parameter Name    | Type      | Optional  | Description    |
| ------    | ----      | ----  | ----      |
| host_ip   |  string    |       | host ip  |


#### Response
```
{
    "data": {
        {
            "host_ip": "fe80::250:56ff:fe9f:495e%eth0",
            "sn": "023341343143",
            "disks": [
                {
                    "drive": "sda",
                    "type": "HDD",   # should be "HDD" or "SSD"
                    "model": "VBOX HARDDISK",
                    "serial": "VB66c75745-aac27c80",
                    "size": 53687091200
                },
                {
                    "drive": "sdb",
                    "model": "SSD",
                    "size": 42949672960
                }
            ],
            "ifaces": [
                {
                    "name": "eth0",
                    "speed": "1000Mb/s"
                },
                {
                    "name": "eth1",
                    "speed": "10000Mb/s"
                }
            ]
        }    
    }
    "ec": "EOK",
    "error": {}
}
```
-------------

### <a name="deploy-cluster"></a>  Deploy Cluster

Deploy cluster

#### HTTP request
      POST /api/v2/deployment/cluster

#### Body parameter
```
{
    "platform": "kvm",    # kvm or vmware
    "elfcompute": False,
    "cluster_name" : "cluster",
    "gateway": "************",
    "dns_server": ["***************", "*******"],
    "ntp": {
    	"mode": "internal" # enum ["external","internal"].
    	"current_time": "Tue, 12 Jan 2016 12:41:04 +0800" # needed if mode is internal
    	"ntp_server": "ntp_server": ["0.centos.pool.ntp.org"] # needed if mode is external  
    },   
    "hosts": [
        {
            "host_ip": "fe80::250:56ff:fe9f:589f%eth1",
            "hostname": "node1",
            "tags": ["master, zbs"],
            "ifaces": [
                {
                    "name" : "eth0",
                    "ip": "************",
                    "netmask": "*************",
                    "function": "manage"  #should be any of [manage, data, vm]
                },
                {
                    "name": "eth0",
                    "function": "vm"
                },
                {
                    "name" : "eth1",
                    "ip": "**************",
                    "netmask": "*************",
                    "function": "data"
                }
             ],
            "disks": [
                {
                    "drive": "sdb",
                    "function": "extent"
                },
                {
                    "drive": "sdc",
                    "function": "cache"
                }
            ]
        }
    ]
}
```

#### Success Response
```
{
	"ec": "EOK",
	"data": {
   	},
   	"error": {}
}
```

-------------

### <a name="add-host"></a>  Add Host

Add new host to cluster

#### HTTP request
      POST /api/v2/deployment/cluster/add_host

#### Body parameter
```
{
    "host_ip": "fe80::250:56ff:fe9f:495e%eth0",
    "hostname": "node1",
    "tags": ["master, zbs"],
    "ifaces": [
        {
            "name" : "eth0",
            "ip": "**************",
            "netmask": "*************",
            "function": "manage"   # should be any of [manage, data, vm]
        },
        {
            "name": "eth0",
            "function": "vm"
        },
        {
            "name" : "eth1",
            "ip": "**********",
            "netmask": "*************",
            "function": "data"
        }
    ],
    "disks": [
        {
            "drive": "sdb",
            "function": "extent"
        },
        {
            "drive": "sdc",
            "function": "extent"
        },
        {
            "drive": "sdd",
            "function": "cache"
        }
    ]
}
```

#### Response
```
{
    "ec": "EOK",
    "data": {
   	},
    "error": {}
}
```

-------------


### <a name="deploy-host"></a>  Deploy Host

Deploy one host

#### HTTP request
      POST /api/v2/deployment/host

#### Body parameter
```
{
    "platform": "kvm",    # kvm or vmware
    "elfcompute": false
    "host_ip": "fe80::250:56ff:fe9f:495e%eth0",
    "cluster_name": "cluster",
    "hostname": "node1",
    "tags": ["master, zbs"],
    "gateway": "************",
    "dns_server": ["***************", "*******"],
    "ntp": {
    	"mode": "internal" # enum ["external","internal"].
    	"current_time": "Tue, 12 Jan 2016 12:41:04 +0800" # needed if mode is internal
    	"ntp_server": "ntp_server": ["0.centos.pool.ntp.org"] # needed if mode is external  
    },
    "master_ips": ["**********"],
    "ifaces": [
        {
            "name" : "eth0",
            "ip": "**************",
            "netmask": "*************",
            "function": "manage"   # should be any of [manage, data, vm]
        },
        {
            "name": "eth0",
            "function": "vm"
        },
        {
            "name" : "eth1",
            "ip": "**********",
            "netmask": "*************",
            "function": "data"
        }
    ],
    "disks": [
        {
            "drive": "sdb",
            "function": "extent"
        },
        {
            "drive": "sdc",
            "function": "extent"
        },
        {
            "drive": "sdd",
            "function": "cache"
        }
    ]
}
```

#### Response
```
{
    "ec": "EOK",
    "data": {
   	},
    "error": {}
}
```

-------------
### <a name="get-host-deploy-status"></a>  Get Host Deploy Status

Get host deploy status.

#### HTTP request
      GET /api/v2/deployment/host/deploy_status

#### URL path parameter
| Parameter Name    | Type      | Optional  | Description    |
| ------    | ----      | ----  | ----      |
| host_ip  |  string    |       | host ip   |

#### Response
```
{
    "ec": "EOK",
    "data": {
        "host_ip": "fe80::250:56ff:fe9f:495e%eth0",
   	    "current_stage": 2,
        "total_stages": 16,
        "stage_info": "config system",
        "state": "running"   #should be "running" or "finish" or "free"
        "msg": "msg"
    },
    "error": {}
}
```

-------------

### <a name="get-host-deploy-log"></a>  Get Host Deploy Log

Get host deploy log.

#### HTTP request
      GET /api/v2/management/hosts/:host_id/deploy_log

#### URL path parameter
| Parameter Name    | Type      | Optional  | Description    |
| ------    | ----      | ----  | ----      |
| host_ip  |  string    |       | host ip  |

#### Response
```
{
	"ec": "EOK",
	"data": {
        "host_ip": "fe80::250:56ff:fe9f:495e%eth0",
        "log": "log"   # maybe string or file content.
    },
    "error": {}
}
```

-------------

### <a name="get-host-heartbeat"></a>  Get Host Heartbeat

Get host heartbeat.

#### HTTP request
      GET /api/v2/deployment/host/heartbeat

#### URL path parameter
| Parameter Name    | Type      | Optional  | Description    |
| ------    | ----      | ----  | ----      |
| host_ip  |  string    |       | host ip  |

#### Response
```
{
	"ec": "EOK",
	"data": {
        "status": "disabled"   # should be "enabled" (host has been deployed) or "disabled" (no deploy)
    },
    "error": {}
}
```

-------------
### <a name="can-ping"></a>  Can ping ip

ping ip

#### HTTP request
      GET /api/v2/deployment/can_ping

#### URL path parameter
| Parameter Name    | Type      | Optional  | Description    |
| ------    | ----      | ----  | ----      |
| ip  |  string    |       | ip  |

#### Response
```
{
	"ec": "EOK",
	"data": {
        "result": true   # should be true / false
    },
    "error": {}
}
```

-------------
### <a name="get-deploy-config"></a> Get Deploy Config

get deploy config

#### HTTP request
      GET /api/v2/deployment/deploy_config

#### Response
```
{
	"ec": "EOK",
    "data": {
    	"data_iface": {
    	   "ip": "*************",
    	   "netmask": "*************"
    	}, 
    	"manage_iface": {
    	   "ip": "**********",
    	   "netmask": "*************"
    	}, 
    	"platform": "kvm",   # kvm or vmware
    	"elfcompute": false, 
    	"gateway": "************", 
  }, 
    "error": {}
}
```

-------------

### <a name="get-deploy-version"></a> Get Deploy Version

get deploy version

#### HTTP request
      GET /api/v2/deployment/deploy_version

#### Response
```
{
data: {
	os: "Linux-3.10.0-229.el7.x86_64-x86_64-with-centos-7.1.1503-Core",
	rpms: {
		fisheye: {
			changelog: [ ],
			description: "SMARTX",
			name: "fisheye",
			release: "rc3.11.git.3cd5c70.el7.centos",
			version: "2.2.3"
		},
		libzbs: {
			changelog: [{
				author: "ZBS developers <<EMAIL>> - 1.0.0",
				commit: "- Release zbs-1.0.0",
				timestamp: 1425643200
			},
			{
				author: "ZBS developers <<EMAIL>> - 0.3-1",
				commit: "- initial packaging",
				timestamp: 1388491200
			}],
			description: "This package provides APIs for applications to use zbs.",
			name: "libzbs",
			release: "dev.128.release.git.g661dda5.el7.centos",
			version: "2.3.0"
		}
	}
}
```
-------------
