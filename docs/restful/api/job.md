  
#Job-Center Job

###/api/v2/jobs

##Create a job

	POST /api/v2/jobs/
	
	This is a general job-center job api

### Example

### Create vm with volumes
	POST /api/v2/jobs/

    {
        "description": "VM_CREATE",
        "resources": [
            {
                "type": "KVM_VOLUME",
                "status": "created",
                "name": "vdisk-1",
                "size": 100,
                "uuid": "8b3fa9aa-d0c7-4d66-9232-b2aeb6929992"
            },
            {
                "vm_name": "test",
                "vcpu": 1,
                "memory": 8589934592,
                "ha": false,
                "node_ip": "*********",
                "description": "test_vm",
                "nics": [
                    {
                        "vlans": [
                            {
                                "vlan_id": 0
                            }
                        ]
                    }
                ],
                "disks": [
                    {
                        "boot": 1,
                        "type": "disk",
                        "bus": "virtio",
                        "path": "@{8b3fa9aa-d0c7-4d66-
                        9232-b2aeb6929992/path}"
                    }
                ],
                "type": "KVM_VM",
                "status": "running"
            }
        ]
    }
    
### Delete vm with volumes
	POST /api/v2/jobs/

    {
        "description": "VM_DELETE",
        "resources": [
            {
                "status": "deleted",
                "type": "KVM_VM",
                "uuid": "91197baf-c985-40ca-973c-e1d68dced718"
            },
            {
                "status": "deleted",
                "type": "KVM_VOLUME",
                "uuid": "8f1a3f6e-a93c-48e1-bd72-28676fd60066"
            }
        ]
    }
    
### Update vm with new iso appended
	POST /api/v2/jobs/
    {
        "description": "VM_EDIT_VOL",
        "resources": [
            {
                "type": "KVM_VM",
                "uuid": "89e08128-edea-4c0b-bd1e-a9f22ff134ba",
                "disks": [
                    {
                        "boot": 1,
                        "type": "disk",
                        "bus": "virtio",
                        "path": "/usr/share/smartx/volumes/
                        55680dd0-b08a-455c-8c63-122dcbb44194"
                    },
                    {
                        "boot": 2,
                        "type": "cdrom",
                        "bus": "ide",
                        "path": "/usr/share/smartx/images/
                        3842a62b-a17f-428a-ac75-6f94c8c262ca"
                    }
                ]
            }
        ]
    }


### Success Response

```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": {
        	"job_id": "xxxxx-xxxxxxx-xxxxxx-xxxxx"
        },
        "error": {}
   }
```


##List all jobs


	GET /api/v2/jobs

##  Show one job

	GET /api/v2/jobs/<id>

```
   HTTP/1.1 200 OK
    {
      "data": {
        "job": {
          "ctime": 1464855351, 
          "description": "VM_CREATE", 
          "error_code": "JOB_LIBVIRT_ERROR", 
          "error_msg": "", 
          "handler": "34a21537-c3c4-35c2-97e7-2177b80f41dd", 
          "job_id": "002cddbd-6300-411d-9b45-6d64fb4f5c36", 
          "life_cycle": "run", 
          "resources": {
            "8b3fa9aa-d0c7-4d66-9232-b2aeb6929992": {
              "name": "vdisk-1", 
              "size": 100, 
              "status": "created", 
              "type": "KVM_VOLUME", 
              "uuid": "8b3fa9aa-d0c7-4d66-9232-b2aeb6929992"
            }, 
            "f36f7b23-f75d-4aff-acf0-c64753fbdd7c": {
              "description": "test_vm", 
              "disks": [
                {
                  "boot": 1, 
                  "bus": "virtio", 
                  "path": "@{8b3fa9aa-d0c7-4d66-9232-
                  			b2aeb6929992/path}", 
                  "type": "disk"
                }
              ], 
              "ha": false, 
              "memory": 8589934592, 
              "nics": [
                {
                  "vlans": [
                    {
                      "vlan_id": 0
                    }
                  ]
                }
              ], 
              "node_ip": "*********", 
              "status": "running", 
              "type": "KVM_VM", 
              "uuid": "f36f7b23-f75d-4aff-acf0-c64753fbdd7c", 
              "vcpu": 1, 
              "vm_name": "test"
            }
          }, 
          "schedule_task": null, 
          "state": "failed", 
          "task_list": [
            {
              "data": null, 
              "error_code": "JOB_LIBVIRT_ERROR", 
              "error_msg": "", 
              "follower_name": "Task.Kvm.Pool.Create", 
              "handler": "34a21537-c3c4-35c2-97e7-2177b80f41dd", 
              "msg": "", 
              "queue": "*********", 
              "reference": "8b3fa9aa-d0c7-4d66-9232-
              				b2aeb6929992", 
              "state": "failed", 
              "time": 1464855362, 
              "uuid": "38ab7dc7-c094-469d-817f-47174fded47e"
            }, 
            {
              "data": "", 
              "follower_name": "Task.Kvm.Volume.Create", 
              "handler": null, 
              "msg": "", 
              "queue": "*********", 
              "reference": "8b3fa9aa-d0c7-4d66-9232-
              				b2aeb6929992", 
              "state": "pending", 
              "time": 1464855352, 
              "uuid": "7db270a6-fe0f-481a-a2bf-fd9a220aeac5"
            }, 
            {
              "data": "", 
              "follower_name": "Task.VM.Create", 
              "handler": null, 
              "msg": "", 
              "queue": "*********", 
              "reference": "f36f7b23-f75d-4aff-acf0-
              				c64753fbdd7c", 
              "state": "pending", 
              "time": 1464855352, 
              "uuid": "12832941-5a7e-4581-9694-805b10339eac"
            }, 
            {
              "data": "", 
              "follower_name": "Task.VM.Start", 
              "handler": null, 
              "msg": "", 
              "queue": "*********", 
              "reference": "f36f7b23-f75d-4aff-acf0-
              				c64753fbdd7c", 
              "state": "pending", 
              "time": 1464855352, 
              "uuid": "c64a10e6-a2ef-443b-b6f1-7b3e044dcef9"
            }, 
            {
              "data": "", 
              "follower_name": "Task.VM.VNC.Token.Create", 
              "handler": null, 
              "msg": "", 
              "queue": "*********", 
              "reference": "f36f7b23-f75d-4aff-acf0-
              				c64753fbdd7c", 
              "state": "pending", 
              "time": 1464855352, 
              "uuid": "b60aa78f-54ef-432a-a153-e0c5ba19cb68"
            }
          ], 
          "type": "action", 
          "user": ""
        }
      }, 
      "ec": "EOK", 
      "error": {}
    }
```