# Task
list all ZBS current tasks
###/api/v2/tasks

CURL example:

```
   curl -i -X GET http://localhost:10402/api/v2/tasks
```

### Success Response

```
   HTTP/1.1 200 OK
   {
       "ec": "UOK",
       "data": {
            "recovery": {
                "recover_size": 0,
                "recover_speed": 0,
            },
            "node_status": {
                "removing": 0,
                "connecting": 0
            },
            "migration": {
                "migrate_size": 0,
                "migrate_speed": 0,
            }
       },
       "error": {}
   }
```

## list zbs tasks

    GET /api/v2/tasks