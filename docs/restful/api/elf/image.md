  
#Elf Image

###/api/v2/images


## Delete one image

	DELETE /api/v2/images/<id>

## List all images

	GET /api/v2/images

```
   HTTP/1.1 200 OK
    {
        "data": {
            "images": [
                {
                    "description": null,
                    "file_name": "3842a62b-a17f-428a-
                    	ac75-6f94c8c262ca",
                    "name": "winser2008",
                    "os": null,
                    "path": "/usr/share/smartx/images/
                    3842a62b-a17f-428a-ac75-6f94c8c262ca",
                    "resource_state": "in-use",
                    "size": 3166840832,
                    "time": 1463040538,
                    "type": "ISO_IMAGE",
                    "uuid": "3842a62b-a17f-428a-ac75-
                    	6f94c8c262ca"
                },            
            ]
        }
    }
```


## Show one image

	GET /api/v2/images/<id>
	
## Upload one imgae
	POST /api/v2/image_upload?name=test&description=test
		 &file_name=test
	
	Body is file content
	
