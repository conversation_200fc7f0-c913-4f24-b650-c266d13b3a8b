
#VM snapshots

###/api/v2/vm_snapshots

##Create a vm snapshot

	POST /api/v2/vm_snapshots/

### Parameters


| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| vm_uuid    | String    |  VM uuid                  |
| name    | String    |  VM snapshot name	                  |
| description    | String    |  VM snapshot description                               |

### Success Response

```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": {
        	"job_id": "xxxxx-xxxxxxx-xxxxxx-xxxxx"
        },
        "error": {}
   }
```


##Delete vm snapshot


	DELETE /api/v2/vm_snapshots/<id>

## Bulk delete vm snapshots

### Request

DELETE /api/v2/vm_snapshots/

|-------+-------+-----------------------+----------|
| name  | type  | description           | required |
|-------+-------+-----------------------+----------|
| uuids | Array | vm snapshot uuid list | True     |
|-------+-------+-----------------------+----------|

#### Response

```json
{
  "ec": "EOK",
  "data": {
    "job_id": "d3373927-85a2-4d3d-a891-8939accb4342"
  },
  "error": {}
}
```

## List all vm snapshot

	GET /api/v2/vm_snapshots

```
   HTTP/1.1 200 OK
    {
        "data": [
            {
                "create_time": 1463048279,
                "description": "",
                "disks": [
                    {
                        "boot": 1,
                        "bus": "virtio",
                        "path": "/usr/share/smartx/volumes/
                          e52a0488-6e9e-4d94-a8c9-8085a4100dfb",
                        "snapshot_uuid": "a511b2c5-2e18-
                        	479f-a2dd-4ee26257e912",
                        "type": "disk",
                        "volume_uuid": "e52a0488-6e9e-4d94-
                        	a8c9-8085a4100dfb"
                    }
                ],
                "ha": false,
                "memory": 4294967296,
                "name": "YiKeSaiTing-snapshot-0",
                "nics": [
                    {
                        "mac_address": "52:54:00:b7:cb:d2",
                        "vlans": [
                            {
                                "vlan_id": 0
                            }
                        ]
                    }
                ],
                "resource_state": "in-use",
                "status": "created",
                "type": "KVM_VM_SNAPSHOT",
                "uuid": "4eea62b3-10f1-42e9-93b0-4f297dff1831",
                "vcpu": 1,
                "vm_description": "",
                "vm_name": "YiKeSaiTing",
                "vm_snapshot": null,
                "vm_uuid": "16ed70e6-7177-4e4b-9337-
                		1878836e0a1e"
            },
        ]
    }

```


## Show one vm snapshot

	GET /api/v2/vm_snapshots/<id>

## Rollback one vm snapshot

	POST /api/v2/vm_snapshots/<id>/rollback


### Response

```
   HTTP/1.1 200 OK
    {
        "data": {
            "job_id": "xxxxxxx-xxxxxxx-xxxx-xxx"
        }
    }

```

## Rebuild one vm snapshot

	POST /api/v2/vm_snapshots/<id>/rebuild

### Parameters
    {
        "vm_name": "wqp-win-2008-clone",    # new clone vm name
        "vcpu": 6,                          # new vcpu number
        "memory": 17179869184,              # new memory size
        "ha": true,                         # new ha value
        "node_ip": "*********",             # new vm node ip
        "description": "克隆自 wqp-win-2008",# new vm description
        "nics": [                           # new vm nics
            {
                "vlans": [
                    {
                        "vlan_id": 0
                    }
                ]
            }
        ],
        "disks": [                          # new append disk
            {
                "type": "disk",             # append new empty disk
                "boot": 1,
                "bus": "virtio",
                "size": 10,
                "name": "vdisk-4"
            },
            {
                "path": "/usr/share/smartx/volumes/bda8f691-bfbc-
                		47e4-bb48-dc12e4e8ce7d",
                "type": "disk",             # append exist disk
                "boot": 1,
                "bus": "virtio",
                "size": 11
            }
        ],
        "status": "stopped"                 # vm expected state
    }

### Response

```
   HTTP/1.1 200 OK
    {
        "data": {
            "job_id": "xxxxxxx-xxxxxxx-xxxx-xxx"
        }
    }

```
