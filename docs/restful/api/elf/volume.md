  
#Elf Volume

###/api/v2/volumes

##Create a volumes

	POST /api/v2/volumes/

### Parameters


| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| size    | int    |  Volume size in GB              |
| name    | String    |  Volume name	                  |
| description    | String    |  volume description                               |

### Success Response

```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": {
        	"job_id": "xxxxx-xxxxxxx-xxxxxx-xxxxx"
        },
        "error": {}
   }
```


##Delete volume

	DELETE /api/v2/volumes/<id>

## List all volumes

	GET /api/v2/volumes

```
   HTTP/1.1 200 OK
    {
        "data": [
            {
                "create_time": 1463552151,
                "description": "",
                "name": "vdisk-3-snapshot-0",
                "resource_state": "in-use",
                "size": 0,
                "status": "created",
                "type": "KVM_VOLUME",
                "uuid": "09257a02-f017-4399-a2d7-6a3baf62a0ab"
            }
        ]
    }
```


## Show one volume

	GET /api/v2/volumes/<id>

## Clone one volume

	POST /api/v2/volumes/<id>/clone

### Parameter
| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| name    | String    |  Volume name	                  |
| description    | String    |  volume description                               |
	

### Response

```
   HTTP/1.1 200 OK
    {
        "data": {
            "job_id": "xxxxxxx-xxxxxxx-xxxx-xxx"
        }
    }
    
```
