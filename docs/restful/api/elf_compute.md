##Elf compute

---

####概要
Elf compute是SmartX的云计算平台解决方案。依托于SmartX Job Center框架，能提供一站式管理虚拟机服务，包括创建，修改，删除，迁移，灾备等功能。更兼容多种虚拟机，包括KVM，VMware，Xen等

####优势

- 高可用，分布式架构，自动感知虚拟机状态并自动作出迁移或者重建
- 高性能，单机创建100个虚拟机只需要30秒
- 兼容KVM和VMWARE
- 能根据用户设置规则，自动备份虚拟磁盘
- 能根据集群状态，自动选择最优机器进行虚拟机部署



---

Job Center

Job Center是SmartX的分布式异步工作系统，通过分布式消息队列，能高效的并发处理大量Job。组件包含分布式消息队列如RabbitMQ，MongoDB，Redis，分布式Worker（Celery）。用户只需要提交一份描述json到消息队列，Worker便会根据描述进行处理，没有指定具体机器的任务，能自动调度到合适的机器，假如机器在中途当机，能迅速分配给其他机器接收处理。

---

术语表：

- Job center：SmartX异步工作系统
- Job：异步工作， 和前端的主要交互部分，每一个Job会描述包含1个或多个Task
- Task：每一个异步工作中的小任务
- Worker：工作中心系统工作者，分布在各个机器中，负责处理Job和Task
- Leader：领导者，这里指获取到Job的worker，进行任务拆分，控制任务调度等
- Follower：跟从者，这里指获取到Task得worker，无须感知其他任务，只需要完成当前任务并更新Task的状态和Job中Task得状态
- Scheduler：调度器，指定时任务发起者，功能包含Job center集群健康状态自检测（如有必要，则迁移或者重建虚拟机），所有虚拟机健康状态检测。如果发现需要机器重建或者迁移，则会自动向集群发起迁移或者重建的Job

---


##Job Center 设计细节

####概要

Job center由消息队列系统，Worker，Scheduler组成，消息队列对应可以使用MongoDB，Redis和RabbitMQ，Worker使用开源框架Celery（3.1.19），Scheduler使用开源框架Celery Beat。

每台服务器需要部署一个Worker，Scheduler则根据实际需求部署（建议是一个集群部署3台Scheduler），集群共享一个分布式消息队列。

Worker使用Gevent模式启动，能承受1000个Job或者Task的并发操作（非阻塞式任务）。这1000个并发处理单元里面可以是处理Job的Leader，也可以是处理Task的Follower。Leader和Follower的任务函数都必须是等幂的。

Scheduler使用celery beat模式启动，能触发定时任务处理一些与时间相关的任务，例如虚拟机监控检查，集群监控检查，监控数据收集等，发起的Job会由Worker接收并处理，因为Scheduler只是发起端，所以压力不大，只需要保持高可用就可以，所以建议是一个集群启动3台做灾备，就算有2台当机，最后一台还是可以运作的

####Leader
接收到Job的Worker叫Leader，它会分析Job的描述，根据描述进行任务拆分，然后再分配给其他Follower进行处理，在处理图中，Leader并不会退出，而是监控进度，假如Follower超时或者失败，则Leader也会标记Job失败。

其中，Leader里面还会包含Splitter负责任务拆分，例如Job描述里面包含一个类型是KVM_VM的资源，则这个资源的拆分交由负责KVM_VM的Splitter进行拆分。Leader只负责分配对应的资源给对应的Splitter，然后收集拆分后的Task，并根据Dependency进行排序。

Dependency是一个依赖关系表，标记着各个Task之间的静态依赖关系，例如VM_START需要在VM_CREATE之后，则关系表会有一条记录列明 `VM_CREATE -> VM_START`。这规则用于处理Task之间的先后次序，如果是没有依赖关系的Task，则会按随机顺序执行(将来会并行)。

####Follower

接收到Task的Worker叫Follower，接收到Task后，会根据分配到Task的资源的描述进行处理。例如分配的Task是VM_CREATE，那么Follower会根据资源的描述，例如CPU，Memory去创建VM。Task完成后，会更新数据库中Job下对应的Task的状态和数据，不会影响到其他Follower的数据和状态。也就是说，在并行的环境下，多个业务不相关Task的Follower进行同时处理并不会引起冲突。当所有Follower完成后，会通知正在等待的Leader，进行最终Job成功标记。

#### Scheduler

定时调度器，会根据设置的时间定时提交一些Job进行处理，例如每10秒检查所有VM状态一次。
目前有的定时器有：

- 检查所有VM状态是否和数据库一致，假如不一致尝试恢复一致，失败后，重建机器
- 检查Job center集群状态是否和数据库一致，假如集群中某个Worker失联，则尝试连接该机器，查看该机器中虚拟机状态，假如无法连接到该机器，则重建所有在该机器上的虚拟机

---tu








功能点
- 创建虚拟机（包含新建和镜像创建）
- 开机
- 关机
- reset
- pause
- console
- 列出所有vm
- 冷启动多网卡
- 停机编辑虚拟机（开机状态下自动关机修改然后再启动？）
- 运行中编辑虚拟机（插拔盘）
- 热迁移
- 冷迁移
- HA(定时任务)
- auto_check_vm(v1版本监控任务，定时任务)
- 创建镜像，从已有vm
- 创建镜像，从网页上传到nfs
- 删除镜像


Job
- 创建虚拟机（包含空白和镜像）
- 更新虚拟机（包含开关机，reset， pause，冷热修改，多网卡，插拔盘）
- 删除虚拟机
- 创建镜像（包含已有vm和nfs文件）
- 删除镜像
- 创建虚拟机快照
- 虚拟机回滚
- VM定时任务（包含HA和已有的auto check vm逻辑）
- 定时任务更新vm状态到数据库


Worker
- 创建虚拟机
- start
- stop
- config(property like cpu mem)
- reset
- reboot
- pause
- rebuild
- migrate
- delete vm
- detach cdrom(replace iso or replace)
- attach cdrom
- detach disk
- attach disk
- snapshot create from vm
- snapshot create from nfs( upload iso? )
- snapshot delete
- create vlan
- delete vlan
- create nic
- delete nic


Dependency

create  vm→ create nic, create vlan, snapshot create, snapshot create
start → stop, config, attach cdrom, detach cdrom, attach disk, detach disk
stop →
config → create, stop
reset →
reboot →
pause →
rebuild → config?
migrate →
delete vm → detach cdrom, detach disk
detach cdrom → 
attach cdrom → snapshot create ?  upload iso?, detach cdrom
detach disk →
attach disk → snapshot create ?  upload iso?, detach disk
snapshot create from vm → snapshot delete
snapshot create from nfs → snapshot delete
create vlan → delete vlan
delete vlan → delete vm
create nic → delete nic
delete nic → delete vm


Example

Job create vm → rest server(json including vm, vlan, 2 nic) → celery leader →
leader split task → subtasks = [create_vm, create_vlan, create_nic, create_nic] → sort_according_dependency(subtasks) → sorted_subtasks = [create_vlan, create_nic, create_nic, create_vm] → worker(sorted_subtasks) → done → leader → mongo(json with new nic id, new vlan id, new vm id) → UI


简单的 HA 策略
触发事件
HA 行为
VM 所在 Host 挂了
在某个正常的 Host 上重建
VM 自身 crash
在本机重建
VM本地重建3次失败
在某个正常的host上重建




第一版本的范围和实现细节

API URL:

  POST /api/v2/jobs
  GET /api/v2/jobs
  GET /api/v2/jobs/:job_id

Job type:

  single # one time action like reboot, reset, not affect json
  multi

Job JSON format:



    {
          "type": "multi",
          "description": "",
          "job_id": "", # generate by rest server
          "state": "pending" # pending, processing, done or failed,
          "life_cycle": "run", # run, rollback,
          "user": "",
          "resources": { # 前端提交的资源描述
                "uuid-123": {
                  "type": "kvm_vm",
                  "uuid": "uuid-123",
                  "user": request.user,
                  "vm_name": request.vm_name, # required
                  "vcpu": request.vcpu, # required?
                  "memory": request.memory, # required?
                  "node_ip": get_config_ip(DATA_IP), # required
                  "vm_ip": get_config_ip(WEB_IP),
                  "vnc_port": -1,
                  "status": STATE_NAMES["Creating"],
                  "token": "",
                  "vlan": int(request.vlan),
                  "desc": str(request.desc),
                  "mac_addr": request.mac_addr,
                  "create_date": int(time.time()),
                  'boot_path': request.boot_path, # required
                  'disks': [],
                  #"depend_on": [    # second version, 
                  #    "uuid-1234" # same operation dependency
                  #]
                }
          },
          "task_list": [ # 由后端leader动态生成任务列表
              {
                  "uuid": "",
                  "follower_name":"",
                  "reference": "", # uuid in details
                  "queue": "",
                  "state": "", # pending, running, done, failed
                  "msg": "",
                  "data": "",
                  "time": ""
              }
          ]
    }


Resource Json:

Common:

    {
          "type": "kvm_vm",
          "uuid": request.uuid,
          "user": request.user,
          "ctime": "",
          "mtime": "",
          'resource_state': "" # creating, in-use, removing, removed
    }


Resource reference:
    {
          "type": "kvm_nic",
          "uuid": "nic_uuid_xxxx",
          "resource_id": "",
          "user": "",
          "ctime": "",
          "mtime": "",
          "host": "@{kvm_vm_uuid_xxxx.resource_id}",  # reference vm id name, if host not exist
          'resource_state': "" # creating, in-use, removing, removed
    }
    
    {
          "type": "kvm_vm",
          "uuid": "kvm_vm_uuid_xxxx",
          "resource_id": "",
          "user": request.user,
          "ctime": "",
          "mtime": "",
          'resource_state': "" # creating, in-use, removing, removed
    }



术语表

Job center： 工作中心
job： 前后端交互单位，一个job包含一个或者多个任务，任务通过json进行描述
task： job细分下的单位，内部使用，不对前端开放，或者前端只看到任务名称和状态，无法控制
leader：领导者，这里指获取到job的celery worker，进行任务拆分，控制任务调度等
follwer：跟从者，这里指获取到task得celery worker，无须感知其他任务，只需要完成当前任务并更新task的状态和job中task得状态

主要模块和控制的部分
leader_worker：负责拆分任务，控制job流程，包含job.state, job.life_cycle
FollowerBase.handle_follower_process: 更新job.details中对应的资源信息，job.task_list中对应的task的状态信息，mongo resource表的更新，celery chain的中断控制
Splitter： 负责拆分任务的模块，一个资源对应一个splitter，例如vm有一个vm splitter。leader集成所有splitter模块



流程：
- UI 提交job
  POST /api/v2/jobs
    {
          "type": "multi",
          "description": "",
          "resources": { # 前端提交的资源描述
                "uuid-123": {
                  "type": "kvm_vm",
                  "uuid": "uuid-123",
                  "user": request.user,
                  "vm_name": request.vm_name, # required
                  "vcpu": request.vcpu, # required?
                  "memory": request.memory, # required?
                  "node_ip": get_config_ip(DATA_IP), # required
                  "vm_ip": get_config_ip(WEB_IP),
                  "vnc_port": -1,
                  "status": STATE_NAMES["Creating"],
                  "token": "",
                  "vlan": int(request.vlan),
                  "desc": str(request.desc),
                  "mac_addr": request.mac_addr,
                  "create_date": int(time.time()),
                  'boot_path': request.boot_path, # required
                  'disks': []
                }
          }
    }


- rest server 接收后提交job到celery
    def job_submit(user, description, type, resources):
        """submit job worker
    
        :param user: username
        :param description: job description
        :param type: multi or single
        :param resources: resources
        :return: None
        """


- celery接收后在leader使用对应的resource splitter拆分任务
    class KvmVMSplitter(SplitterBase):
    
        def split(self, vm_json): # 未完成
            task_list = []
            #if self.create_new(vm_json):
            task_list.append(
                self.generate_task(TASK_VM_CREATE, vm_json['uuid']) # 生成一个vm create task
            )
            return task_list


- task会放到job的task_list，并通过DAG排序
- leader会组合完整job json并保存数据库，并开始job，更新state成processing并更新到数据库
    {
          "type": "multi",
          "description": "",
          "job_id": "uuid-generated", # generate by rest server
          "state": "processing" # pending, processing, done or failed,
          "life_cycle": "run", # run, rollback,
          "user": "",
          "resources": { # 前端提交的资源描述
                "uuid-123": {
                  "type": "kvm_vm",
                  "uuid": "uuid-123",
                  "user": request.user,
                  "vm_name": request.vm_name, # required
                  "vcpu": request.vcpu, # required?
                  "memory": request.memory, # required?
                  "node_ip": get_config_ip(DATA_IP), # required
                  "vm_ip": get_config_ip(WEB_IP),
                  "vnc_port": -1,
                  "status": STATE_NAMES["Creating"],
                  "token": "",
                  "vlan": int(request.vlan),
                  "desc": str(request.desc),
                  "mac_addr": request.mac_addr,
                  "create_date": int(time.time()),
                  'boot_path': request.boot_path, # required
                  'disks': [],
                  #"depend_on": [    # second version, 
                  #    "uuid-1234" # same operation dependency
                  #]
                }
          },
          "task_list": [ # 由后端leader动态生成任务列表
              {
                  "uuid": "uuid-generated",
                  "follower_name":"TASK_VM_CREATE",
                  "reference": "uuid-123", # uuid in details
                  "queue": "",
                  "state": "pending", # pending, running, done, failed
                  "msg": "",
                  "data": "",
                  "time": 12345678
              }
          ]
    }


- 往celery投放任务，串行执行，follower 接收并更新数据库，更新task，开始任务会在task_list对应的task更新为running


- 任务执行中
    def kvm_vm_create(self, vm_json): # 未完成，这里应该是复制v1版本创建vm的逻辑进来，
        logging.info("kvm_vm_create")
        logging.info(vm_json)
        return vm_json


- 任务完成follwer会更新对应task的状态为done，失败failed，假如资源有更新的字段，会更新到resources任务对应的json，例如生成的数据
    {
          "type": "multi",
          "description": "",
          "job_id": "uuid-generated", # generate by rest server
          "state": "processing" # pending, processing, done or failed,
          "life_cycle": "run", # run, rollback,
          "user": "",
          "resources": { # 前端提交的资源描述
                "uuid-123": {
                  "type": "kvm_vm",
                  "uuid": "uuid-123",
                  "user": request.user,
                  "vm_name": "my_vm_name_uuid", # generated after task success
                  "vcpu": request.vcpu, # required?
                  "memory": request.memory, # required?
                  "node_ip": get_config_ip(DATA_IP), # required
                  "vm_ip": get_config_ip(WEB_IP),
                  "vnc_port": -1,
                  "status": STATE_NAMES["Creating"],
                  "token": "",
                  "vlan": int(request.vlan),
                  "desc": str(request.desc),
                  "mac_addr": request.mac_addr,
                  "create_date": int(time.time()),
                  'boot_path': request.boot_path, # required
                  'disks': [],
                  #"depend_on": [    # second version, 
                  #    "uuid-1234" # same operation dependency
                  #]
                }
          },
          "task_list": [ # 由后端leader动态生成任务列表
              {
                  "uuid": "uuid-generated",
                  "follower_name":"TASK_VM_CREATE",
                  "reference": "uuid-123", # uuid in details
                  "queue": "",
                  "state": "done", # pending, running, done, failed
                  "msg": "",
                  "data": "",
                  "time": 12345678 # latest modify time
              }
          ]
    }


- 所有任务完成，或者某个任务失败后会停止串行任务，最后leader会更新job状态，到这里整个job会完成
    {
          "type": "multi",
          "description": "",
          "job_id": "uuid-generated", # generate by rest server
          "state": "done" # pending, processing, done or failed,
          "life_cycle": "run", # run, rollback,
          "user": "",
          "resources": { # 前端提交的资源描述
                "uuid-123": {
                  "type": "kvm_vm",
                  "uuid": "uuid-123",
                  "user": request.user,
                  "vm_name": "my_vm_name_uuid", # generated after task success
                  "vcpu": request.vcpu, # required?
                  "memory": request.memory, # required?
                  "node_ip": get_config_ip(DATA_IP), # required
                  "vm_ip": get_config_ip(WEB_IP),
                  "vnc_port": -1,
                  "status": STATE_NAMES["Creating"],
                  "token": "",
                  "vlan": int(request.vlan),
                  "desc": str(request.desc),
                  "mac_addr": request.mac_addr,
                  "create_date": int(time.time()),
                  'boot_path': request.boot_path, # required
                  'disks': [],
                  'bus':''
                  #"depend_on": [    # second version, 
                  #    "uuid-1234" # same operation dependency
                  #]
                }
          },
          "task_list": [ # 由后端leader动态生成任务列表
              {
                  "uuid": "uuid-generated",
                  "follower_name":"TASK_VM_CREATE",
                  "reference": "uuid-123", # uuid in details
                  "queue": "",
                  "state": "done", # pending, running, done, failed
                  "msg": "",
                  "data": "",
                  "time": 12345678 # latest modify time
              }
          ]
    }


Resources:
- VM(KVM)
    {
        "type": "KVM_VM",
        "uuid": "uuid-123",
        "user": "",
        "vm_name": "my_vm_name", # user input, required
        "vcpu": 1, # required
        "memory": 1024, # required
        "node_ip": management ip, # required
        "vnc_port": -1, # may be changed by migration, need by start vm
        "status": "", # kvm state running, stopped, suspended, unknown
        "token": "", # need by start vm
        "nics": [
            {
                "vlans": [ # one or multi vlans?
                    {
                        "vlan_id": "42"
                    }
                ],
                "mac_address": "" # generated
            }
        ],
        "description": "", # optional
        "create_date": int(time.time()),
        'disks': [ # required at least one
            {
                "path": "/xxx/xx/xx/xx/x",
                "type": "cdrom", # "floppy", "disk", "cdrom", and "lun", defaulting to "disk"
                "boot": 1, # sequence boot number
                "bus": "ide" # ide, sata, virtio, scsi, usb, sd, xen
            }
        ],
    }
    # frontend required: vm_name, vcpu, memory, node_ip, status, nics, disks, description
    # dynamic update when start stop: vnc_port, token
    # reboot required: vcpu, memory
    
- Volume（KVM）
    {
        "type": "KVM_VOLUME",
        "size": "", # required
        "pool_name": "", # not supported yet, only support default right now
        "uuid": "",
        "path": "",
        "status": "" # created, deleted
    }
- ISO
    {
        "type": "IMAGE",
        "uuid": "",
        "path": "",
        "status": "" # created, deleted
    }

VM ha scheduler

- schedule job
- 
    {
          "type": "JOB_TYPE_SCHEDULE",
          "description": "",
          "job_id": "uuid-generated", # generate by rest server
          "state": "done" # pending, processing, done or failed,
          "life_cycle": "run", # run, rollback,
          "user": "",
          "resources": {},
          "schedule_task": {
              "name": "Schedule.Task.VM.Check",
              "hosts": [
                  "*************"
              ],
              "args": [
              ]
              
          },
          "task_list": [
          ]
    }
