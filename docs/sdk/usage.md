#SMARTXSDK
####导语
> *SmartxSDK是利用SchemaSugar中的Meta信息动态解析出来的，可以用作集成测试和持续测试的内部使用工具。
> 它包含了所有API接口，当使用时候按照给定的规则调用就能获取返回值。*

* 使用方法会在下面[API](#jump1)的文档中给出简要的使用方式。
* 每个接口所需要的参数都有可能改变，所以本文档中的参数有些并没有给出，需要自己联系API Owner或者前端。
* 由于SDK是由Schemasugar给出的Meta数据动态生成的，所以如果某个接口没有定义schema就没法使用SDK，同样的上传方法目前也暂时不支持。


#Usage
***

> ####conn = SmartxSDK(server, username, password)
> ####获取连接了SDK的对象之后，就可以完成一些操作

* 当有需要传参的时候，参数都需要使用kwargs，也就是传字典参数。


#<span id="jump1">API</span>
***

##Storage Pool

&emsp;
####api:/api/v2/datastores
####resources:datastores
####resouece:/api/v2/datastores/\<id>
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      index    |conn.datastores.index( )
| put     |      update   |conn.datastores.update(id=_id, name=_name, replica_num=_num, thin_provision=_bool)

&emsp;
####api:/api/v2/cluster/storage
####resouece:storage
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      show     |conn.cluster.storage.show( )

&emsp;
####api:/api/v2/storage/storage_pools
####resources:storage_pools
####resource:/api/v2/storage/storage_pools/\<id>
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      show     |conn.storage.storage_pools.show(id=\_id)
| get     |      index    |conn.storage.storage_pools.index( )
| delete  |      delete   |conn.storage.storage_pools.delete(id=\_id)
| put     |      update   |conn.storage.storage_pools.update(id=\_id, name=\_name)
| post    |      create   |conn.storage.storage_pools.create(chunk_ids=\_chunk_ids, name=\_name)
| post    |    add_chunk  |conn.storage.storage_pools.add_chunk(id=\_id, chunk_id=\_chunk_id)
| post    |  remove_chunk |conn.storage.storage_pools.remove_chunk(id=\_id, chunk_id=\_chunk_id)

&emsp;
***

##User / Admin

&emsp;
####api:/api/v2/auth/session
####resource:session
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:| :-----|
| get     |      show     |conn.auth.session.show( )
| post    |      create   |conn.auth.session.create(username="root", password="111111")
| delete  |      delete   |conn.auth.session.delete( )

&emsp;
####api:/api/v2/auth/superuser
####resource:superuser
####operation:

| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:----- |
| get     |      show     |conn.auth.superuser.show( ) 
| post    |      create   |conn.auth.superuser.create(username=username, password=password, repeat=repeat)

&emsp;
####api:/api/v2/auth/password
####resource:password
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| put     |   update      |conn.auth.password.update(new_password=new_password, repeat=repeat)

&emsp;
####api:/api/v2/tools/keys/locale
####resoueces:keys
####resouece:/api/v2/tools/keys/\<id>
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| get     |   index       |conn.tools.keys.index( )
| put     |   update      |conn.tools.keys.update(id="locale", value="zhihao")
| get     |   show        |conn.tools.keys.show(id="locale")
| delete  |   delete      |conn.tools.keys.delete(id="locale")

&emsp;
####api:/api/v2/auth/users
####resoueces:users
####resouece:/api/v2/auth/users/\<id>
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| get     |   index       |conn.auth.users.index( )
| put     |   update      |conn.auth.users.update(id=_id, value="zhihao")
| get     |   show        |conn.auth.users.show(id=_id)
| delete  |   delete      |conn.auth.users.delete(id=_id)
| post    |   create      |conn.auth.users.create(id=_id)

&emsp;
***

##Fisheye Info

&emsp;
####api:/api/v2/deployment/deploy_config
####resouece:deploy_config
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| get     |   show       |conn.deployment.deploy_config.show( )

&emsp;
####api:/api/v2/tools/license
####resource:license
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| get     |   show        |conn.tools.license.show( )
| put     |   update      |conn.tools.license.update(license=_license)

&emsp;
####api:/api/v2/vmware/account
####resource:account
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| get     |   show        |conn.vmware.account.show( )
| put     |   update      |conn.vmware.account.update(host=\_host, port=\_port, user=\_user, password=\_password)

&emsp;
####api:/api/v2/cluster/software
####resource:software
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| get     |   show        |conn.cluster.software.show( )

&emsp;
####api:/api/v2/vsphere/accounts
####resources:accounts
####resource:/api/v2/vsphere/accounts/\<id>
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| get     |   index       |conn.vsphere.accounts.index( )
| get     |   show        |conn.vsphere.accounts.show(id=_id)
| put     |   update      |conn.vsphere.accounts.update(id=_id, host=\_host, user=\_user)
| delete  |   delete      |conn.vsphere.accounts.delete(id=_id)
| post    |   create      |conn.vsphere.accounts.create(host_uuid=\_host_uuid, host_name=\_host_name, host=\_host, user=\_user, passwrod=\_password)

&emsp;
####api:/api/v2/xenserver/accounts
####resources:accounts
####resource:/api/v2/xenserver/accounts/\<id>
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| get     |   index       |conn.xenserver.accounts.index( )
| get     |   show        |conn.xenserver.accounts.show(id=_id)
| put     |   update      |conn.xenserver.accounts.update(id=_id, host=\_host, user=\_user)
| delete  |   delete      |conn.xenserver.accounts.delete(id=_id)
| post    |   create      |conn.xenserver.accounts.create(host_uuid=\_host_uuid, host_name=\_host_name, host=\_host, user=\_user, passwrod=\_password)

&emsp;
####api:/api/v2/vsphere/update_many_accounts
####resource:update_many_accounts
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| put     |   update      |conn.vsphere.update_many_accounts.update( )

&emsp;
####api:/api/v2/xenserver/update_many_accounts
####resource:update_many_accounts
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| put     |   update      |conn.xenserver.update_many_accounts.update( )

&emsp;
####api:/api/v2/management/management_ip
####resource:management_ip
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| put     |   update      |conn.management.management_ip.update( )

&emsp;
***

##Hardware

&emsp;
####api:/api/v2/management/caches
####resources:caches
####resource:/api/v2/management/caches/\<id>
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| post    |   create      |conn.management.caches.create( )
| delete  |   delete      |conn.management.caches.delete( )
| get     |   index       |conn.management.caches.index( )
| get     |   show        |conn.management.caches.show(id=_id)

&emsp;
####api:/api/v2/management/partitions
####resources:partitions
####resource:/api/v2/management/partitions/\<id>
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| post    |   create      |conn.management.partitions.create( )
| delete  |   delete      |conn.management.cache.delete( )
| get     |   index       |conn.management.partitions.index( )
| get     |   show        |conn.management.partitions.show(id=_id)

&emsp;
####api:/api/v2/management/hosts
####resources:hosts
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| get     |   index       |conn.management.hosts.index( )

&emsp;
####api:/api/v2/deployment/cluster
####resource:cluster
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| get     |   index       |conn.deployment.cluster.index( )
| post    |   create      |conn.deployment.cluster.create( )

&emsp;
####api:/api/v2/deployment/cluster/add_host
####resource:add_host
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| post    |   create      |conn.deployment.cluster.add_host.create( )

&emsp;
####api:/api/v2/deployment/host/deploy_status
####resource:deploy_status
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| get     |   show        |conn.deployment.host.deploy_status.show(host_ip=\_host_ip)

&emsp;
####api:/api/v2/deployment/host/deploy_log
####resource:deploy_log
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| get     |   show        |conn.deployment.host.deploy_log.show(host_ip=\_host_ip)

&emsp;
####api:/api/v2/deployment/host/heart_beat
####resource:heart_beat
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| get     |   show        |conn.deployment.host.heart_beat.show(host_ip=\_host_ip)

&emsp;
***

##Services
&emsp;

####api:/api/v2/cluster/services
####resource:services
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| get     |   index       |conn.cluster.services.index( )

&emsp;
***

##Job Center

&emsp;
####api:/api/v2/jobs
####resources:jobs
####resource:/api/v2/jobs/\<id>
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| get     |   index       |conn.jobs.index( )
| get     |   show        |conn.jobs.show(id=\_id)
| post    |   create      |conn.jobs.create(resource=\_resource)

&emsp;

####api:/api/v2/job_center
####resource:job_center
####operation:
| Method  |   Operation   |SDK   |
|:-------:|:-------------:|:-----|
| get     |   show        |conn.job_center.show( )

&emsp;
***


##Iscsi

&emsp;
####api:/api/v2/iscsi/targets
####resoueces:targets
####resouece:/api/v2/iscsi/targets/\<id>
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      show     |conn.iscsi.targets.show(id=\_id)
| get     |      index    |conn.iscsi.targets.index( )
| delete  |      delete   |conn.iscsi.targets.delete(id=\_id)
| put     |      update   |conn.iscsi.targets.update(id=\_id, name=\_name)
| post    |      create   |conn.iscsi.targets.create(name=\_name)

&emsp;
####api:/api/v2/iscsi/targets/\<target_id>/luns
####resoueces:luns
####resouece:/api/v2/iscsi/targets/\<target_id>/luns/\<id>
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      show     |conn.iscsi.targets.luns.show(id=\_id, target_id=\_target_id)
| get     |      index    |conn.iscsi.targets.luns.index(target_id=\_target_id)
| delete  |      delete   |conn.iscsi.targets.luns.delete(id=\_id, target_id=\_target_id)
| put     |      update   |conn.iscsi.targets.luns.update(id=\_id,  target_id=\_target_id)
| post    |      create   |conn.iscsi.targets.luns.create(target_id=\_target_id)
| post    |      move     |conn.iscsi.targets.luns.move(target_id=\_target_id, id=\_id)

&emsp;
####api:/api/v2/iscsi/targets/\<target_id>/snapshots
####resoueces:snapshots
####resouece:/api/v2/iscsi/targets/\<target_id>/snapshots/\<id>
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      show     |conn.iscsi.targets.snapshots.show(id=\_id, target_id=\_target_id)
| get     |      index    |conn.iscsi.targets.snapshots.index(target_id=\_target_id)
| delete  |      delete   |conn.iscsi.targets.snapshots.delete(id=\_id, target_id=\_target_id)
| put     |      update   |conn.iscsi.targets.snapshots.update(id=\_id,  target_id=\_target_id)
| post    |      create   |conn.iscsi.targets.snapshots.create(target_id=\_target_id)

&emsp;
***


##NFS

&emsp;
####api:/api/v2/nfs/exports
####resoueces:exports
####resource:/api/v2/nfs/exports/\<id>
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      show     |conn.nfs.exports.show(id=\_id)
| get     |      index    |conn.nfs.exports.index( )
| delete  |      delete   |conn.nfs.exports.delete(id=\_id)
| put     |      update   |conn.nfs.exports.update(id=\_id)
| post    |      create   |conn.nfs.exports.create(name=\_name)

&emsp;
####api:/api/v2/nfs/exports/\<export_id>/inodes
####resoueces:inodes
####resouece:/api/v2/nfs/exports/\<export_id>/inodes/\<id>
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      show     |conn.nfs.exports.inodes.show(export_id=\_export_id, id=\_id)
| get     |      index    |conn.nfs.exports.inodes.index(export_id=\_export_id)
| delete  |      delete   |conn.nfs.exports.inodes.delete(export_id=\_export_id, id=\_id)
| put     |      update   |conn.nfs.exports.inodes.update(export_id=\_export_id, id=\_id)
| post    |      create   |conn.nfs.exports.inodes.create(export_id=\_export_id, name=\_name)
| post    |      rename   |conn.nfs.exports.inodes.rename(export_id=\_export_id, id=\_id)
| post    |      move     |conn.nfs.exports.inodes.move(export_id=\_export_id, id=\_id)

&emsp;
####api:/api/v2/nfs/exports/\<export_id>/snapshots
####resoueces:snapshots
####resouece:/api/v2/nfs/exports/\<export_id>/snapshots/\<id>
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      show     |conn.nfs.exports.snapshots.show(export_id=\_export_id, id=\_id)
| get     |      index    |conn.nfs.exports.snapshots.index(export_id=\_export_id)
| delete  |      delete   |conn.nfs.exports.snapshots.delete(export_id=\_export_id, id=\_id)
| put     |      update   |conn.nfs.exports.snapshots.update(export_id=\_export_id, id=\_id, name=\_name)
| post    |      create   |conn.nfs.exports.snapshots.create(export_id=\_export_id, name=\_name)
| post    |      rollback |conn.nfs.exports.snapshots.rollback(export_id=\_export_id, id=\_id)
| post    |      move     |conn.nfs.exports.snapshots.move(export_id=\_export_id, id=\_id)

&emsp;
####api:/api/v2/nfs/query_inode_names
####resouece:query_inode_names
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| post    |      create   |conn.nfs.query_inode_names.create( )

&emsp;
***


##Elf Network

&emsp;
####api:/api/v2/network/vlans
####resoueces:vlans
####resouece:/api/v2/network/vlans/\<id>
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      index    |conn.network.vlans.index( )
| get     |      show     |conn.network.vlans.show(id=\_id)
| delete  |      delete   |conn.network.vlans.delete(id=\_id)
| post    |      create   |conn.network.vlans.create( )
| put     |      update   |conn.network.vlans.update(id=\_id)

&emsp;
***

##Elf Resources

&emsp;
####api:/api/v2/vms
####resoueces:vms
####resouece:/api/v2/vms/\<id>
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      index    |conn.vms.index( )
| get     |      show     |conn.vms.show(id=\_id)
| post    |      clone    |conn.vms.clone(id=\_id)

&emsp;
####api:/api/v2/vm_snapshots
####resoueces:vm_snapshots
####resouece:/api/v2/vm_snapshots/\<id>
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      index    |conn.vm_snapshots.index( )
| get     |      show     |conn.vm_snapshots.show(id=\_id)
| delete  |      delete   |conn.vm_snapshots.delete(id=\_id)
| post    |      rollback |conn.vm_snapshots.rollback(id=\_id)
| post    |      rebuild  |conn.vm_snapshots.rebuild(id=\_id)
| post    |      create   |conn.vm_snapshots.create( )

&emsp;
####api:/api/v2/volumes
####resoueces:volumes
####resouece:/api/v2/volumes/\<id>
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      index    |conn.volumes.index( )
| get     |      show     |conn.volumes.show(id=\_id)
| delete  |      delete   |conn.volumes.delete(id=\_id)
| put     |      update   |conn.volumes.update(id=\_id)
| post    |      create   |conn.volumes.create( )

&emsp;
####api:/api/v2/volume_snapshots
####resoueces:volume_snapshots
####resouece:/api/v2/volume_snapshots/\<id>
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      index    |conn.volume_snapshots.index( )
| get     |      show     |conn.volume_snapshots.show(id=\_id)
| delete  |      delete   |conn.volume_snapshots.delete(id=\_id)
| post    |      rollback |conn.volume_snapshots.rollback(id=\_id)
| post    |      rebuild  |conn.volume_snapshots.rebuild(id=\_id)
| post    |      create   |conn.volume_snapshots.create( )

&emsp;
####api:/api/v2/vm_templates
####resoueces:vm_templates
####resouece:/api/v2/vm_templates/\<id>
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      index    |conn.vm_templates.index( )
| get     |      show     |conn.vm_templates.show(id=\_id)
| delete  |      delete   |conn.vm_templates.delete(id=\_id)
| put     |      update   |conn.vm_templates.update(id=\_id)
| post    |      create   |conn.vm_templates.create( )

&emsp;
####api:/api/v2/images
####resoueces:images
####resouece:/api/v2/images/\<id>
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      index    |conn.images.index( )
| get     |      show     |conn.images.show(id=\_id)
| delete  |      delete   |conn.images.delete(id=\_id)
| put     |      update   |conn.images.update(id=\_id)

&emsp;
***

##Metric / Dashboard metrics

&emsp;
####api:/api/v2/metric
####resouece:metric
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      show     |conn.metric.show( )

&emsp;
####api:/api/v2/cluster/node_summary
####resouece:nodes_summary
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      show     |conn.cluster.nodes_summary.show( )

&emsp;
####api:/api/v2/tasks
####resouece:tasks
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      show     |conn.tasks.show( )

&emsp;
####api:/api/v2/compute/summary
####resouece:summary
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      show     |conn.compute.summary.show( )

&emsp;
####api:/api/v2/vsphere/summary
####resouece:summary
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      show     |conn.vsphere.summary.show( )

&emsp;
####api:/api/v2/xenserver/summary
####resouece:summary
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      show     |conn.xenserver.summary.show( )

&emsp;
***

##Notification

&emsp;
####api:/api/v2/gazer/events
####resoueces:events
####resouece:/api/v2/gazer/events/\<id>
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      index    |conn.gazer.events.index( )
| get     |      show     |conn.gazer.events.show(id=\_id)
| put     |      update   |conn.gazer.events.update(id=\_id)
| post    |      create   |conn.gazer.events.create( )

&emsp;
####api:/api/v2/gazer/user_alerts/email_provider
####resouece:email_provider
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      show     |conn.gazer.user_alerts.email_provider.show( )
| delete  |      delete   |conn.gazer.user_alerts.email_provider.delete( )
| post    |      create   |conn.gazer.user_alerts.email.provider.create( )

&emsp;
####api:/api/v2/gazer/user_alerts/snmps
####resoueces:snmps
####resouece:/api/v2/gazer/user_alerts/snmps/\<id>
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      index    |conn.gazer.user_alerts.snmps.index( )
| get     |      show     |conn.gazer.user_alerts.snmps.show(id=\_id)
| put     |      update   |conn.gazer.user_alerts.snmps.update(id=\_id)
| delete  |      delete   |conn.gazer.user_alerts.snmps.delete(id=\_id)
| post    |      create   |conn.gazer.user_alerts.snmps.create( )

&emsp;
***

##Network

&emsp;
####api:/api/v2/network/vds
####resoueces:vds
####resouece:/api/v2/network/vds/\<id>
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      index    |conn.network.vds.index( )
| get     |      show     |conn.network.vds.show(id=\_id)
| put     |      update   |conn.network.vds.update(id=\_id)
| delete  |      delete   |conn.network.vds.delete(id=\_id)
| post    |      create   |conn.network.vds.create( )

&emsp;
####api:/api/v2/network/vds_vlans
####resoueces:vds_vlans
####resouece:/api/v2/network/vds_vlans/\<id>
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      index    |conn.network.vds_vlans.index( )
| get     |      show     |conn.network.vds_vlans.show(id=\_id)
| put     |      update   |conn.network.vds_vlans.update(id=\_id)
| delete  |      delete   |conn.network.vds_vlans.delete(id=\_id)
| post    |      create   |conn.network.vds_vlans.create( )

&emsp;
####api:/api/v2/network/available_nics
####resouece:available_nics
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      show     |conn.network.available_nics.show( )

&emsp;
***

##Others

&emsp;
####api:/api/v2/gazer/user_alerts/trap.mib
####resouece:trap.mib
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      show     |conn.gazer.user_alerts.trap_mib.show( )

&emsp;
####api:/api/v2/management/hosts/export.csv
####resouece:export.csv
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      show     |conn.management.host.export_csv.show( )

&emsp;
####api:/api/v2/management/hosts/disks.csv
####resouece:disks.csv
####operation:
| Method  |   Operation   | SDK   |
|:-------:|:-------------:|:-----|
| get     |      show     |conn.management.host.disks_csv.show( )