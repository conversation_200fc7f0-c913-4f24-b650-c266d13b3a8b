# Cluster
ZBS cluster summary
###/api/v2/cluster

CURL example:

```
    curl -i -X GET http://localhost:10402/api/v2/cluster/storage
```

### Success Response

```
    HTTP/1.1 200 OK
    {
        "ec": "UOK",
        "data": {
             "used_data_space": 0,
             "total_data_capacity": 0,
             "total_pools": 0, 
             "total_volumes": 0,
             "provisioned_data_space": 0,
             "max_pool_num": 0,
             "max_volume_num": 0,
        },
        "error": {}
    }
```

## Show cluster storage information

	GET /api/v2/cluster/storage


## Show cluster node summary
  
	GET /api/v2/cluster/nodes_summary

### Success Response

```
    HTTP/1.1 200 OK
    {
        "ec": "UOK",
        "data": {
             "total_nodes": 0,
        	 "healthy_nodes": 0, 
        	 "connecting_nodes": 0,
        	 "warning_nodes": 0, 
        	 "error_nodes": 0
        },
        "error": {}
    }
```