# Account
Vmware account related operations
###/api/v2/vmware/account

CURL example:

```
   curl -i -X POST http://localhost:10402/api/v2/vmware/account
        -H 'Content-Type: application/json' \
        -d '{"host": "***********", "port": 443, "user":"abc", "password":"123"}'
```

### Success Response

```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": {},
        "error": {}
   }
```
## Create or update vcenter account

	POST /api/v2/vmware/account

### Parameters

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| host    | String    |  vcenter hostname or ip		|
| port    | Int       |  vcenter port		|
| user    | String    |  vcenter username		|
| password | String    |  vcenter password		|

## Show vcenter account without password

	GET /api/v2/vmware/account

### Success Response

```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": {
            "host": "***********",
            "port": 443,
            "user": "abc"
        },
        "error": {}
   }
```