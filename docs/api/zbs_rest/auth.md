# Auth
ZBS rest auth related operations
###/api/v2/auth

CURL example:

```
   curl -i -X POST http://localhost:10402/api/v2/auth/session
        -H 'Content-Type: application/json' \
        -d '{"username": "abc","password": "123", "remember": true}'

```

### Success Response

```
   HTTP/1.1 200 OK
   {
     "ec": "UOK",
     "data": {
     	"token": "xxxxx"
     },
     "error": {}
   }

```
## Create a session when login

    POST /api/v2/auth/session

### Parameters

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| username    | String    |  Name of user.		              |
| password    | String    |  User password		              |
| remember    | Boolean    |  Remeber login	              |

## Delete a session when logout

    DELETE /api/v2/auth/session

## Update password

    PUT /api/v2/auth/password

### Parameters

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| old_password    | String    |  Old password		              |
| new_password    | String    |  New password		              |
| repeat    | String    |  Repeat new password	              |