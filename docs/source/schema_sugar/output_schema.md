#OutputSchema Format
-------------------

* Please Read This In Your Markdown Tool 

# Summary
* About output_schema

* About output_schema validator

* About using output_schema validate output data

* Any Question

## Output_Schema

### 1.	According to make return format clear and specific, so here is a sample

		"output_schema": {
        	"index": {
             	"type": "object"
               	"properties": {
                	"name": {"type": "string"},
                	"girlfriends": {
                   		"type": "array",
                       	"items": [
                       		{
                        		"type": "string"
                         	}
                    	]
                 	},
                	"phone_number": {"type": "string"},
                 	"is_male": {"type": "boolean"},
                   	"age": {"type": "number"} # integer belong to number, so number range is bigger
                  	"family": {
                    	"type": "array",
                    	"items": [
                        	{
                        		"type": "object",
                           		"properties": {
                                	"name": {"type": "string"},
                                  	"nickname": {"type": "string"}
                             	},
                             	"required": ["name", "nickname"]
                     		}
                    	]
                    }
              	}
   			}
    	}

### 2.output_schema should contained in config_dict

		"config_dict": {
			"schema": {
				# some action in schema
			},
			"output_schema": {
				# some action in output_schema
			}
			# some other in config_dict
		}

## Output_Schema Validator

### 1. There are few rule about output_schema

1. the most outside type only can be "object", "null" and "array"
	
			"output_schema": {	
				"index": {
					"type": "object" # or "null" or "array"
					# ... something else
				},
				"show": {
					"type": "null",
					# ... something else
				},
				"update": {
					"type": "array",
					# ... something else
				},
				# ...other action
			}

2. where type is "object", there should be "properties", "patternProperties" after or just include nothing 

			"index": {
				"type": "object"，
				"properties": {
					"girlfriend": {"type": "string"}
				}
			},
			"show": {
				"type": "object",
				"patternProperties": {
					"^[0-9]{4}" : {
						"type": "object",
						"properties": {
							"some_object": {"type": "object"}
						}
					}
				} 
			}
		this sample above should pass the validator

3. where type is "array", there should be "items" after

			"index": {
				"type": "array",
				"items": [
					{
						"type" : "string"
					}
				]
			},

			"show": {
				"type": "array",
				"items": [
					{
						"type": "object",
						"properties": {
							"date": {
								"type": "string"
							}
						}
					}
				]
			} 

		this sample above should pass the validator

### 2. About APISugar

#### If you inherit APISugar and complete your config_dict, you have two option

1. ignore output_schema if you write output_schema later, you should define nothing
	
		"config_dict": {
				"schema": {
					"index": {
						# ...
					}
				},
				# do not define output_schema	
			}
			
2. if you want to complete your output_schema, you should obey the rule just mention above
	
## Valiate output using output_schema

1. When using output_schema you should not only obey the rule about output_schema just mention above, but also the output_schema will fit your HTTP response result

2. As we all to know, the response may look like that:
	
		"result": {
			"data": {},
			"ec": "E_OK",
			"error": {}
		}
		
3. output_schema only focus on the data in result, so maybe this sample will record you of something
		
		def out_schema_filter(output_schema, result, operation):
		    '''
		    :type output_schema: dict, contain your action and output data format
		    :type result: dict, response result
		    :type operation: str or unicode, like "index", "show", and other action
		    '''
		    
		    # if output_schema is not define and result is None should pass
		    if not (output_schema and result):
		        return 
		    
		    if operation not in output_schema:
		        raise OutputSchemaError(
		            "Checkout Your output_schema," 
		            "which do not have this operation"
		        )
		        
			form_validator = Draft4Validator(output_schema)
		
		    # data is None match the {"type": "null"}
		    data = result['data'] if result['data'] != {} else None
		    
		    # IF this all going right, will not raise any Exception,
		    # Or it will raise ValidationError if your output_schema not match the 
		    # output result data
		    form_validator.validate(data)

4. when define your output_schema, you should tell specifically some datas which will always return in result, at that time you should define `required`.Have a look at this sample, you should make sure `boot`, `bus`, `path`, `type` will always show  at `disks` array, if any of that will be `None`, you should define your keyword like that `"type":["string", "null"]`, this is an option you can choose `"string"` or `"null"` 

		"show": {
            "type": "object",
            "properties": {
                "create_time": {"type": "number"},
                "disks": {
                    "type": "array",
                    "items": [
                        {
                            "type": "object",
                            "properties": {
                                "boot": {"type": "number"},
                                "bus": {"type": "string"},
                                "path": {"type": "string"},
                                "type": {"type": "string"},
                            },
                            "required": ["boot", "bus", "path", "type"]
                        }
                    ]
                },
                "ha": {"type": "boolean"},
                "memory": {"type": "number"},
                "nics": {
                    "type": "array",
                    "items": [
                        {
                            "type": "object",
                            "properties": {
                                "mac_address": MAC_ADDRESS_PATTERN,
                                "vlans": {
                                    "type": "array",
                                    "items": [
                                        {
                                            "type": "object",
                                            "properties": {
                                                "vlan_id": {"type": "number"}
                                            },
                                            "required": ["vlan_id"]
                                        }
                                    ]
                                }
                            },
                            "required": ["mac_address", "vlans"]
                        }
                    ]
                },
                "node_ip": {"type": "string"},
                "resource_state": {"type": "string"},
                "status": {"type": "string"},
                "type": {"type": "string"},
                "uuid": UUID_TYPE_PATTERN,
                "vcpu": {"type": "number"},
                "vm_name": {"type": "string"}
            },
            "required": ["create_time", "disks", "ha", "memory",
                         "nics", "node_ip", "resource_state",
                         "status", "type", "uuid", "vm_name"]
        }		    

# Any Question

1. [Json-Schema](http://json-schema.org)  you can get more information from this website

2. [Contract Me](<EMAIL>)