# 架构设计文档

## 功能需求

在虚拟机内部安装SVT工具，用来支持下面的功能：

- 实时收集VM Guest OS的配置信息
- 实时收集VM Guest OS的性能信息
- ELF直接修改VM Guest OS的IP/用户名密码/Hostname/NTP server
- ELF生成VM的文件系统一致性快照

## 整体设计

```{eval-rst}
.. graphviz::
  :caption: SVT Architecture
  :align: center

  digraph svt {
    rankdir=TB
    labelloc=b
    splines=true
    style=filled
    fontsize=30
    node [shape=ellipse fixedsize=true width=2 fontsize=15 style=filled fillcolor=skyblue]
    edge [fontsize=10]

    n00 [label="Fisheye" shape=rectangle fillcolor=gold]

    subgraph cluster_0 {
      label="ELF Cluster"
      fillcolor="gray96"

      n10 [label="MongoDB" shape=cylinder fillcolor=wheat]
      n11 [label="ELF API/JC"]
      n12 [label="Octopus"]
      n13 [label="Prometheus" shape=cylinder fillcolor=wheat]

      {rank=same n13 n12 n11 n10}

      n13 -> n12 -> n11 -> n10 [style=invis]

      subgraph cluster_1 {
        label="ELF Node"
        fillcolor="ivory"

        n20 [label="SVT Agent"]
        n21 [label="Libvirtd"]
        n22 [label="SVT Scheduler"]
        n23 [label="ELF Exporter"]

        {rank=same n20 n22 n23}

        subgraph cluster_2 {
          label="VM"
          fillcolor="azure"

          n30 [label="SVT"]
        }
      }
    }

    n21 -> n30 [label="qapi" color="orange"]
    n20 -> n21 [label="qga cmd" color="orange"]
    n00:e-> n11 [label="query static" color="blue"]
    n00:s -> n11:w [label="set property" color="red"]
    n00:w-> n12 [label="query performance" color="green"]
    n12 -> n13 [label="query prometheus" color="green"]
    n13 -> n23 [label="pull performance" color="green"]
    n23 -> n20 [label="svt cmd" color="green"]
    n11 -> n20 [label="svt cmd" color="red"]
    n11 -> n10 [label="query mongo" color="blue"]
    n22 -> n10 [label="period push" color="blue"]
    n22 -> n20 [label="svt command" color="blue"]
    n23 -> n30 [weight=10 style=invis]
  }
```

根据上个章节功能需求的要求，虚拟机工具需要和VM内部的Guest OS进行交互，所以在集群节点和VM的Guest OS之间需要建立交互的通道，常见的通道有两种:

- **网络通道**

  功能强大，但是VM和主机节点如果处于两个隔离的网络的话就无法通信了，虽然可以使用locallink的结束打通隔离的网络通道，但是OVS的配置和维护都比较复杂。

- **串口通道**

  基于virtio-serial建立点对点的通道，配置简单而且非常稳定，虽然在windows上需要额外的驱动，但是可以做到一键安装和配置

因为客户的生产环境中业务网络和管理网络是隔离的，我们最终选择了串口通道，整理虚拟机工具系统包含两个独立部署的部分：

- **虚拟机部分**

  包含一个virtio-serial的驱动和以一个以service运行的guest agent，这部分我们使用开源的qemu-ga，因为它支持guest-file和guest-exec的接口，我们可以通过qemu-ga上传powershell和bash脚本然后远程执行，所以guest agent本身没有额外的开发工作，主要是对qemu-ga的重新编译打包，以及在不同Guest OS上的兼容性测试

- **节点部分**

  虚拟机工具的大部分功实现在Agent里面，它是一个部署在节点上的服务，在这个服务里面定义了gRPC接口实现了：

  - 封装了qemu-ga命令的发送和结果解析
  - 封装了使用qemu-ga远程执行bash和powershell脚本的接口
  - 周期性的检测和更新每个VM的SVT状态
  - 周期性的收集VM的静态信息存储到数据库里面
  - 接收ELF Exporter的请求收集和返回VM的性能信息（CPU和内存）
  - 接收ELF API的请求更新Guest OS的配置(密码，主机名，DNS和NTP)
  - 接口Job Center的请求更新Guest OS的网络配置和在创建一致性快照的时候冻结Guest OS的文件系统

  使用这个Agent的gRPC接口，API&JC和Octopus就可以实现产品需求中虚拟机工具的功能

## 生命周期管理

我们定义了四个虚拟机工具的状态：

```{eval-rst}
.. list-table:: SVT Status Definitions
  :widths: 30 70
  :header-rows: 1

  * - 状态
    - 定义

  * - Restrict
    - 没有virtio-serial且状态不是Stop，这时VM不能自动添加 virtio-serial 设备，禁止挂载SVT ISO，需要执行一次关机后才可以挂载SVT ISO，不展示Guest OS信息，不收集Guest OS性能信息

  * - NonInstalled
    - 初始的SVT状态，VM上从未安装和启动过SVT，可以挂载SVT ISO，不展示Guest OS信息，不收集Guest OS性能信息

  * - Inactive
    - VM上曾经启动过SVT，但是现在VM的SVT不可访问，可以挂载SVT ISO，展示最后一次收集到的Guest OS信息，不收集Guest OS性能信息

  * - Active
    - 能够访问VM的SVT服务，展示最新收集到的Guest OS信息，收集Guest OS性能信息
```

这里定义了一个特殊的状态 {code}`Restrict` ，这是因为在使用串口通道的时候需要在VM上添加一个virtio-serial设备，但是Libvirt和QEMU不支持热插拔virtio-serial的controller，这个限制会导致：

- 如果一个非Stop的VM没有virtio-serial，那么添加virtio-serial后需要关机才可以生效
- 如果一个非Stop的VM有virtio-serial，那么删除virtio-serial后需要关机才可以生效

为了简化整个virtio-serial的生命周期管理，我们只在VM开机的时候自动添加virtio-serial设备，而且不支持删除virtio-serial设备，这样在VM上启用virtio-serial时可以分为两种情况：

- 已经存在的非Stop状态的VM，我们定义为它的SVT状态为 {code}`Restrict` ，需要先关闭VM，等待下次开机的时候自动启用virtio-serial
- 已经存在的Stop的VM，或者新创建的VM，我们定义它的SVT状态为 ::`NonInstalled` ，开机的时候会自动启用virtio-serial

为了让VM的操作和SVT状态的迁移彻底解耦，我们定义所有VM的初始SVT状态都是NonInstalled，用户的操作不会改变SVT的状态，只有SVT Agent的SVT状态检测服务可以改变SVT的状态，这个服务会定期检查VM的相关信息，然后根据下图中定义的规则来改变VM的SVT状态:

```{eval-rst}
.. graphviz::
  :caption: SVT Status Definition
  :align: center

  digraph svt_state {
    rankdir=LR
    splines=ortho
    style=dotted
    nodesep=0.5
    node [shape=box fixedsize=true width=1 fontsize=10 style=filled fillcolor=skyblue]
    edge [fontsize=10]

    subgraph cluster_0 {
      n0 [label="Virtio Serial" shape=plaintext style=solid fontsize=15 fontcolor=navy]
      n00 [label="Exist"]
      n02 [shape=none fillcolor=invis label=""]
      n03 [shape=none fillcolor=invis label=""]
      n01 [label="Non-Exist"]
    }

    subgraph cluster_1 {
      n1 [label="VM Status" shape=plaintext style=solid fontsize=15 fontcolor=navy]
      n10 [label="Stopped"]
      n12 [label="Running"]
      n13 [shape=none fillcolor=invis label=""]
      n11 [label="Non Stopped"]
    }

    subgraph cluster_2 {
      n2 [label="SVT Service" shape=plaintext style=solid fontsize=15 fontcolor=navy]
      n22 [shape=none fillcolor=invis label=""]
      n21 [label="Stopped"]
      n20 [label="Running"]
      n23 [shape=none fillcolor=invis label=""]
    }

    subgraph cluster_3 {
      n3 [label="Guest Info" shape=plaintext style=solid fontsize=15 fontcolor=navy]
      n31 [label="Non-Exist"]
      n30 [label="Exist"]
      n32 [shape=none fillcolor=invis label=""]
      n33 [shape=none fillcolor=invis label=""]
    }

    subgraph cluster_4 {
      node [shape=box3d style=filled fillcolor=wheat]

      n4 [label="SVT State" shape=plaintext style=solid fontsize=15 fontcolor=navy]
      n41 [label="NonInstalled"]
      n43 [label="InActive"]
      n42 [label="Active"]
      n40 [label="Restrict"]
    }


    n0 -> n1 -> n2 -> n3 -> n4 [style=invis]

    n01 -> n11 -> n40 [color=red constraint=false]
    n01 -> n10 -> n41 [color=blue constraint=false]
    n00 -> n10 -> n31 -> n41 [color=green constraint=false]
    n00 -> n10 -> n30 -> n43 [color=gold constraint=false]
    n00 -> n12 -> n20 -> n42 [color=magenta constraint=false]
    n00 -> n12 -> n21 -> n31 -> n41 [color=deeppink constraint=false]
    n00 -> n12 -> n21 -> n30 -> n43 [color=cyan constraint=false]
  }
```

从VM操作的角度来看，SVT状态之间的转换过程如下：

```{eval-rst}
.. graphviz::
  :caption: SVT State Transition
  :align: center

  digraph svt {
    rankdir=TB
    splines=true
    style=filled
    fontsize=30
    node [shape=circle fixedsize=true width=1.5 fontsize=15 style=filled color=gray]
    edge [fontsize=10 color=blue]

    n0 [label=Restrict shape=Mcircle fillcolor=snow]
    n1 [label=NonInstalled shape=Mcircle fillcolor=wheat]
    n2 [label=Inactive fillcolor=skyblue]
    n3 [label=Active fillcolor=palegreen]

    {rank=same n0 n1}
    {rank=same n3 n2}

    n0 -> n1 [label="shutdown"]
    n1 -> n3 [label="install\nqga"]
    n3 -> n1 [label="rollback"]
    n3 -> n2 [label="stop qga"]
    n2 -> n3 [label="start qga"]
    n2 -> n1 [label="rollback"]
  }

```

## 功能模块设计

### 模块结构

SVT的功能涉及到三个代码repo:

- pyzbs: <ssh://<EMAIL>:29518/pyzbs>
- harbor: <https://github.smartx.com/smartx/harbor.git>
- qemu: <https://github.smartx.com/smartx/qemu.git>

模块的目录结构如下:

```shell
pyzbs/smartx_app/elf/common/
├── events
├── resource_query
├── resources
├── resource_wrappers
├── tests
│   ├── resource_query
│   └── utils
└── utils
pyzbs/smartx_app/elf/rest/handler/v2/
├── attribute
├── compute
├── cpu_capabilities
├── folder
├── image
├── migration_capabilities
├── pass_through
├── placement_group
├── resources
├── storage_policy
├── vm
├── vm_snapshot
├── vm_templates
├── volume
└── volume_snapshot
pyzbs/smartx_app/elf/job_center/
├── follower
│   └── kvm
├── hook
├── leader
│   └── splitter
│       └── kvm
├── lib
│   ├── converter
│   └── zbs
├── scheduler
└── tests
  ├── follower
  │   └── kvm
  ├── leader
  │   └── splitter
  ├── lib
  └── scheduler
pyzbs/smartx_app/elf/cmd
pyzbs/smartx_app/elf/exporter/
├── collector
├── metric
└── tests
pyzbs/smartx_app/vmtools/rest/
├── handler
│   └── v2
│       └── svt_image
└── service
pyzbs/gRPC_client/
├── common
├── crab
├── salmon
├── timemachine
└── vmtools
pyzbs/smartx_vmtools/
├── agent
├── common
├── config
├── dao
├── fluent-bit
├── svt
│   ├── linux
│   └── windows
└── tests
  ├── agent
  ├── common
  ├── config
  └── svt
pyzbs/rpm
├── data
│   ├── etc
│   │   ├── cloudcenter
│   │   │   └── permissions
│   │   ├── consul.d
│   │   ├── cron.d
│   │   ├── cron.daily
│   │   ├── cron.hourly
│   │   ├── init
│   │   ├── logrotate.smartx.d
│   │   ├── nginx
│   │   │   ├── conf.d
│   │   │   └── default.d
│   │   ├── profile.d
│   │   ├── rsyslog.d
│   │   ├── smartx
│   │   ├── sysconfig
│   │   │   └── modules
│   │   ├── systemd
│   │   │   ├── journald.conf.d
│   │   │   └── system
│   │   └── udev
│   │       └── rules.d
│   ├── python
│   ├── systemd
│   └── usr
│       └── share
│           └── smartx
│               ├── config
│               │   ├── elf-exporter
│               │   ├── log_collector
│               │   ├── salmon-exporter
│               │   ├── tuna-exporter
│               │   └── zbs_rest
│               └── script
├── repo
└── x86_64
qemu/qga
├── installer
└── vss-win32
qemu/svt
├── linux
│   ├── selinux_policy
│   └── service
└── windows
  └── library
harbor/proto/smartx/smartx_vmtools_agent/
└── v3
harbor/swagger/yaml/elf
├── en
│   ├── attribute
│   ├── common
│   ├── cpu_compatibility
│   ├── folder
│   ├── image
│   ├── network
│   ├── passthrough
│   ├── placement_groups
│   ├── storage_policy
│   ├── svt
│   ├── vm
│   ├── vm_snapshot
│   ├── vm_template
│   ├── volume
│   └── volume_snapshot
└── zh
  ├── attribute
  ├── common
  ├── cpu_compatibility
  ├── folder
  ├── image
  ├── network
  ├── passthrough
  ├── placement_groups
  ├── storage_policy
  ├── svt
  ├── vm
  ├── vm_snapshot
  ├── vm_template
  ├── volume
  └── volume_snapshot
```

### 模块介绍

- **ELF Common**

  公共资源模块，位于pyzbs/smartx_app/elf/common/，定义了ELF中的资源查询,操作和事件记录的公共模块。模块里面的resource_wrappers/vmtools_wrapper.py封装了对SVT image，guest info和VM内的文件系统的操作

- **ELF REST API**

  定义ELF的 RESTful API，位于pyzbs/smartx_app/elf/rest/handler/v2/，这个模块是API服务的入口，它接收schema sugar解析后的API参数，执行API请求返回API结果，或者创建一个异步job并返回job id。SVT修改的逻辑包括：

  - 修改查询VM的接口，返回结果增加了VM的guest info，网卡IP和SVT ISO名称的信息
  - 修改VM Snapshot和VM template的查询接口，返回结果增加了SVT ISO名称的信息
  - 修改VM Snapshot和VM template重建VM的接口，支持新的VM继承snapshot和template中的SVT ISO
  - 修改VM Snapshot创建接口，支持创建文件系统一致性快照
  - 修改VM更新的操作，支持设置网卡的静态IP/子网掩码/网关
  - 修改快照回滚的API，回滚时重置VM的guest info信息

- **ELF JC**

  定义ELF异步任务的拆分子任务的规则，以及子任务的执行逻辑，位于pyzbs/smartx_app/elf/job_center/，SVT的修改的逻辑包括:

  - 修改kvm_vm_config子任务，支持通过SVT设置静态IP/子网掩码和网关
  - 增加kvm_vm_fs_consistent_snapshot_create子任务，用于创建VM的文件系统一致性快照
  - 修改快照任务拆分的规则，支持生成创建文件系统一致性快照的子任务

- **ELF CMD**

  定义管理ELF集群的CLI命令，主要给售后维护和检测集群使用。位于pyzbs/smartx_app/elf/cmd，SVT在这里实现了 {code}`elf-tool elf_vm remove_vm_tools` 命令来清理VM上残留的guest info信息

- **ELF Exporter**

  定义了Prometheus采集性能数据的collector，位于pyzbs/smartx_app/elf/exporter/，SVT修改的逻辑包括：

  - 优先使用SVT采集到的VM CPU使用率
  - 优先使用SVT采集到的VM内存使用率

- **SVT REST API**

  定义了SVT ISO的上传和查询接口，位于pyzbs/smartx_app/vmtools/rest/

- **gRPC Client**

  封装了访问gRPC服务的client代码，位于pyzbs/gRPC_client/，主要给Prometheus Exporter使用，访问SVT Agent的gRPC服务的client定义在gRPC_client/vmtools模块下面

- **SVT Agent**

  SVT Agent定义了SVT支持的所有操作，以独立的服务运行在集群的每个节点上面，服务名称为vmtools-agent，对外提供：

  - gRPC接口：用于集群内部服务之间的通信
  - REST API V3接口：用于支持外部系统的访问

  SVT Agent由下面的模块组成:

  - **接口定义**

    定义在harbor/proto/smartx/smartx_vmtools_agent/，现在已经定义的gRPC接口包括:

    ```
    rpc BatchCreateStatus(BatchCreateStatusRequest) returns (VmIdListInfo);

    rpc BatchUpdateStatus(BatchUpdateStatusRequest) returns (VmIdListInfo);

    rpc BatchThawFileSystem(BatchThawFileSystemRequest) returns (VmIdListInfo);

    rpc BatchUpdateStaticData(BatchUpdateStaticDataRequest) returns (VmIdListInfo);

    rpc BatchGetPerformanceData(BatchGetPerformanceDataRequest) returns (SvtPerformanceDataListInfo);

    rpc FreezeFileSystem(FreezeFileSystemRequest) returns (FreezeFileSystemResponse);

    rpc ThawFileSystem(ThawFileSystemRequest) returns (ThawFileSystemResponse);

    rpc GetFileSystemStatus(GetFileSystemStatusRequest) returns (FileSystemStatus);

    rpc UpdateProperties(UpdatePropertiesRequest) returns (SvtProperties) {
      option (google.api.http) = {
        put: "/v3/svt/{id=*}/properties"
        body: "*"
      };
    }
    ```

    其中UpdateProperties由options参数，所以它还是一个v3 restful api接接口，它的openapi规范定义在harbor/swagger/yaml/elf/\[zh|en\]/svt/openapi.yml配置文件里面

  - **接口实现**

    gRPC接口实现的模块，位于pyzbs/smartx_vmtools/agent/

  - **Client**

    封装了访问SVT gRPC接口的client模块

  - **common模块**

    公共定义模块，包括：

    - 异常和Error Code定义
    - lbvirt connection封装
    - 事件公共定义模块
    - 定义定时任务调度器

  - **DAO**

    封装了对于mongodb的范围，包括：

    - SVTReportsDAO
    - SVTImageDAO
    - ResourceLockDAO
    - ResourceDAO

  - **SVT**

    SVT模块基于qemu-ga的guest-file/guest-exec相关接口进行了扩展，因为qemu-ga内置的QMP命令无法满足我们SVT的所有需求，扩展后能够支持使用qemu-ga执行bash和powershell脚本，所以SVT可以同时支持执行脚本命令和QMP命令，它们的执行优先级如下:

    1. 如果命令有对应的powershell和bash脚本，那么直接执行相应的脚本并返回结果
    2. 如果命令是一个合法的QMP命令，那么执行QMP命令并返回结果
    3. 如果命令不存在，那么抛出命令不存在的异常

    在SVT中使用到的SVT命令如下：

  ```{eval-rst}
  ..  list-table:: SVT Commands
    :widths: 50 20 30
    :header-rows: 1

    * - 命令名
      - 类型
      - 操作系统

    * - guest-get-static-data
      - Script
      - Windows/Linux

    * - guest-set-network
      - Script
      - Windows/Linux

    * - guest-check-svt-process
      - Script
      - Windows/Linux

    * - guest-set-dns-servers
      - Script
      - Windows/Linux

    * - guest-set-ntp-servers
      - Script
      - Windows/Linux

    * - guest-get-perf-data
      - Script
      - Windows/Linux

    * - guest-set-hostname
      - Script
      - Windows/Linux

    * - guest-fsfreeze-freeze
      - QMP
      - Windows/Linux

    * - guest-fsfreeze-status
      - QMP
      - Windows/Linux

    * - guest-fsfreeze-thaw
      - QMP
      - Windows/Linux

    * - guest-network-get-interfaces
      - QMP
      - Windows/Linux

    * - guest-get-osinfo
      - QMP
      - Windows/Linux

    * - guest-info
      - QMP
      - Windows/Linux

    * - guest-get-hostname
      - QMP
      - Windows/Linux

    * - guest-ping
      - QMP
      - Windows/Linux

    * - guest-file-open
      - QMP
      - Windows/Linux

    * - guest-file-read
      - QMP
      - Windows/Linux

    * - guest-file-write
      - QMP
      - Windows/Linux

    * - guest-file-flush
      - QMP
      - Windows/Linux

    * - guest-file-close
      - QMP
      - Windows/Linux

    * - guest-exec
      - QMP
      - Windows/Linux

    * - guest-exec-status
      - QMP
      - Windows/Linux
  ```

  - **vmtools-agent service**

    vmtools-agent服务的启动入口位于smartx_vmtools/main.py，在它的子线程中分别运行了：

    - **gRPC service**

      运行在大小为10的线程池中，接收和处理gRPC请求和v3 API请求，调用接口实现模块返回请求结果，

    - **Scheduler service**

      读取配置文件里面的任务列表，周期性触发任务执行，执行任务的过程比较简单，主要是根据任务名调用grpc接口，现在支持的任务包括：

      - batch_init_svt_status：默认周期5秒，初始化VM SVT的状态为NonInstalled
      - batch_transition_svt_status : 默认周期为15秒，根据规则设置SVT的状态，实现VM的SVT状态切换
      - batch_thaw_svt_file_system: 默认周期为60秒，这是一个兜底的动作，清理非操作中的VM的freeze状态
      - batch_update_svt_static_data: 默认周期600秒，采集VM的静态信息

- **RPM 打包**

  定义了如何打包ELF项目所有服务的RPM，SVT Agent的打包和服务配置文件也定义这个模块里面

- **Guest Agent**

  直接使用了开源的QEMU Guest Agent，位于qemu/qga，我们没有修改它的程序代码，只是进行了重新编译和打包，打包脚本位于qemu/svt，我们打包的qga的特点包括：

  - 通过ISO格式分发
  - 集成了windows virtio的驱动，能够在安装qga的时候自动安装驱动
  - 静态编译，不依赖Guest OS的系统共享库，只需要打包一个Windows和一个Linux的安装包
  - 一键安装包，不依赖系统包管理软件
  - qmp服务中打开guest-exec接口的支持

### 功能流程

- **集群升级**

  如果升级前的集群不支持SVT，那么升级完vmtools-agent服务启动后会初始化所有VM的SVT状态, 先将所有的VM初始化为noninstalled状态，然后将非stop状态的VM初始化为restricted状态

```{eval-rst}
.. graphviz::
  :align: center

  digraph G {
    rankdir=LR
    splines=true
    style=dotted
    nodesep=0.5
    node [shape=box width=1.5 fontsize=10 style=filled fillcolor=snow fontsize=8 color=gray]
    edge [fontsize=10 color=darkgreen constraint=true]

    subgraph cluster_1 {
      label = "SVT Agent";

      n1 [shape=plain label="" fixedsize=false]
      n10 [label="Scheduler Service" fillcolor=skyblue]
      n11 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>gRPC Client</TD></TR>
          <TR><TD PORT="f1">batch_transition_svt_status</TD></TR>
          <TR><TD PORT="f0">batch_init_svt_status</TD></TR>
        </TABLE>
      >]
      n12 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>gRPC Service</TD></TR>
          <TR><TD PORT="f1">BatchUpdateStatusRequest</TD></TR>
          <TR><TD PORT="f0">BatchCreateStatus</TD></TR>

        </TABLE>
      >]
      n13 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>SVT</TD></TR>
          <TR><TD PORT="f0">  guest-ping  </TD></TR>
        </TABLE>
      >]
      n14 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>DAO</TD></TR>
          <TR><TD PORT="f0">SVTReportsDAO</TD></TR>
          <TR><TD PORT="f1">ResourceDAO</TD></TR>
        </TABLE>
      >]
      n15 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>Agent</TD></TR>
          <TR><TD PORT="f0">SVTStatusWrapper</TD></TR>
        </TABLE>
      >]

      {rank=same n12 n14 n15}
      {rank=same n11 n13}
    }

    subgraph cluster_3 {
      label = "Storage";
      node[shape=cylinder]

      n3 [shape=plain label="" fixedsize=false]
      n31 [label="Mongo DB"]
    }

    n10 -> n11:f0 [label="set noninstalled"]
    n11:f0 -> n12:f0
    n12:f0:e-> n15:f0:e -> n14:f0:e
    n14:f0 -> n31 [label="set svt noninstalled"]

    n10 -> n11:f1 [label="transit to restrict" color=orange]
    n11:f1 -> n12:f1  [color=orange]
    n12:f1 -> n15:f0 -> n13:f0:s [color=orange]
    n13:f0 -> n14:f0 [color=orange]
    n14:f0 -> n31 [label="transition to restrict" color=orange]
  }
```

- **集群启用SVT**

  ELF通过查询SVT ISO来判断集群是否启用SVT，集群上传完SVT ISO后就会自动启用SVT，在虚拟卷详情页面就会出现 {code}`安装虚拟机工具` 的按钮，查询和上传SVT ISO的模块流程如下:

```{eval-rst}
.. graphviz::
  :align: center

  digraph G {
    rankdir=LR
    splines=true
    style=dotted
    nodesep=0.5
    node [shape=box fixedsize=true width=1 fontsize=10 style=filled fillcolor=snow fontsize=8 color=gray]
    edge [fontsize=8 color=darkgreen constraint=true]

    subgraph cluster_0 {
      label = "ELF";

      n0 [shape=plain label="" fixedsize=false]
      n00 [label="SVT RESTFul API"]
    }

    subgraph cluster_1 {
      label = "SVT Agent";

      n1 [shape=plain label="" fixedsize=false]
      n10 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>SVT</TD></TR>
          <TR><TD PORT="f0">SVTImageWrapper</TD></TR>
        </TABLE>
      >]
      n11 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>DAO</TD></TR>
          <TR><TD PORT="f0">SVTImageDAO</TD></TR>
        </TABLE>
      >]

      {rank=same n10 n11}
    }

    subgraph cluster_3 {
      label = "Storage";
      node[shape=cylinder]

      n3 [shape=plain label="" fixedsize=false]
      n32 [label="Mongo DB"]
      n31 [label="ZBS NFS"]

    }

    n4 [label="Client" shape=Mcircle]

    n4 -> n00 [label="upload iso" weight=3]
    n00 -> n31 [label="upload file"]
    n00 -> n10:f0 [weight=3]
    n10:f0:e -> n11:f0:e [weight=3]
    n11:f0 -> n32 [label="save iso info" weight=3]

    n11:f0 -> n32 [label="query iso info" color=orange]
    n10:f0:w -> n11:f0:w [color=orange]
    n00 -> n10:f0 [color=orange]
    n4 -> n00 [label="get iso info" color=orange]
  }

```

- **VM启用SVT**

  在VM内安装Guest Agent可以对一个VM启用SVT，vmtools-agent会周期性检测VM，当发现有VM已经安装Guest Agent后将它的SVT状态设置为Active

```{eval-rst}
.. graphviz::
  :align: center

  digraph G {
    rankdir=LR
    splines=true
    style=dotted
    nodesep=0.5
    node [shape=box fixedsize=true width=1.5 fontsize=10 style=filled fillcolor=snow fontsize=8 color=gray]
    edge [fontsize=10 color=darkgreen constraint=true]

    subgraph cluster_1 {
      label = "SVT Agent";

      n1 [shape=plain label="" fixedsize=false]
      n10 [label="Scheduler Service" fillcolor=skyblue]
      n11 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>gRPC Client</TD></TR>
          <TR><TD PORT="f1">batch_transition_svt_status</TD></TR>
        </TABLE>
      >]
      n12 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>gRPC Service</TD></TR>
          <TR><TD PORT="f1">BatchUpdateStatusRequest</TD></TR>

        </TABLE>
      >]
      n13 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>SVT</TD></TR>
          <TR><TD PORT="f0">guest-ping</TD></TR>
          <TR><TD PORT="f1">guest-check-svt-process</TD></TR>
        </TABLE>
      >]
      n14 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>DAO</TD></TR>
          <TR><TD PORT="f0">SVTReportsDAO</TD></TR>
          <TR><TD PORT="f1">ResourceDAO</TD></TR>
        </TABLE>
      >]
      n15 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>Agent</TD></TR>
          <TR><TD PORT="f0">SVTStatusWrapper</TD></TR>
        </TABLE>
      >]

      {rank=same n10 n11}
      {rank=same n13 n12}
      {rank=same n14 n15}
      n11 -> n10 [style=invis]
    }

    subgraph cluster_3 {
      label = "Storage";
      node[shape=cylinder]

      n3 [shape=plain label="" fixedsize=false]
      n31 [label="Mongo DB"]
    }

    n10 -> n11 [label="transit to active"]
    n11:f1:e -> n12:f1
    n12:f1 -> n15:f0
    n15:f0:s -> n13:f0
    n13:f0:w -> n13:f1:w
    n13:f1:e -> n14:f0
    n14:f0 -> n31 [label="transition to active"]
  }
```

- **VM Guest Info 静态信息的收集和展示**

  Guest Info静态信息的收集由vmtools-agent的周期任务进行，并将最新的数据存储到mongodb中，当ELF API查询VM的Guest Info信息时就可以直接从mongo里面读取了

```{eval-rst}
.. graphviz::
  :align: center

  digraph G {
    rankdir=LR
    splines=true
    style=dotted
    nodesep=0.5
    node [shape=box fixedsize=true width=1.5 fontsize=10 style=filled fillcolor=snow fontsize=8 color=gray]
    edge [fontsize=10 color=darkgreen constraint=true]

    subgraph cluster_0 {
      label = "ELF";

      n0 [shape=plain label="" fixedsize=false]
      n00 [label="RESTFul API"]
      n01 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>Common</TD></TR>
          <TR><TD PORT="f1">VMToolsWrapper</TD></TR>
          <TR><TD PORT="f0">VMWrapper</TD></TR>
        </TABLE>
      >]

      {rank=same n00 n01}
    }

    subgraph cluster_1 {
      label = "SVT Agent";

      n1 [shape=plain label="" fixedsize=false]
      n10 [label="Scheduler Service" fillcolor=skyblue]
      n11 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>gRPC Client</TD></TR>
          <TR><TD PORT="f1">batch_update_svt_static_data</TD></TR>
        </TABLE>
      >]
      n12 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>gRPC Service</TD></TR>
          <TR><TD PORT="f1">BatchUpdateStaticData</TD></TR>

        </TABLE>
      >]
      n13 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>SVT</TD></TR>
          <TR><TD PORT="f0">guest-fsfreeze-status</TD></TR>
          <TR><TD PORT="f1">guest-info</TD></TR>
          <TR><TD PORT="f2">guest-get-osinfo</TD></TR>
          <TR><TD PORT="f3">guest-network-get-interfaces</TD></TR>
          <TR><TD PORT="f4">guest-get-host-name</TD></TR>
          <TR><TD PORT="f5">guest-get-static-data</TD></TR>
        </TABLE>
      >]
      n14 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>DAO</TD></TR>
          <TR><TD PORT="f0">SVTReportsDAO</TD></TR>
        </TABLE>
      >]
      n15 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>Agent</TD></TR>
          <TR><TD PORT="f0">SVTStaticDataWrapper</TD></TR>
        </TABLE>
      >]

      {rank=same n10 n11}
      {rank=same n15 n12}
      {rank=same n14 n13}

      n11 -> n10 [style=invis]
      n15 -> n11 [style=invis]
      n14 -> n13 -> n12 [style=invis]

    }

    subgraph cluster_3 {
      label = "Storage";
      node[shape=cylinder]

      n3 [shape=plain label="" fixedsize=false]
      n31 [label="Mongo DB"]
    }

    n4 [label="Client" shape=Mcircle fillcolor=skyblue]

    n10 -> n11 [label="collect static data"]
    n11:f1 -> n12:f1
    n12 -> n15:f0
    n15:f0 -> n13:f0
    n13:f0:e -> n13:f1:e -> n13:f2:e -> n13:f3:e -> n13:f4:e -> n13:f5:e
    n13:f5:w -> n14:f0
    n14:f0 -> n31 [label="save guest info"]

    n4 -> n00 [label="Query VM info" color=orange]
    n00 -> n01:f0 [color=orange]
    n01:f0:w -> n01:f1:w [color=orange label="add guest info to vm"]
    n01:f1 -> n14:f0 [color=orange]
    n14:f0 -> n31 [color=orange label="query guest info"]
  }
```

- **VM CPU和内存使用率信息的收集和展示**

  VM CPU和内存使用率的信息的收集由ELF exporter周期性触发，访问vmtools-agent的性能数据收集接口获取数据，并将数据存储到Prometheus里面

```{eval-rst}
.. graphviz::
  :align: center

  digraph G {
    rankdir=LR
    splines=true
    style=dotted
    nodesep=0.5
    node [shape=box fixedsize=true width=1.5 fontsize=10 style=filled fillcolor=snow fontsize=8 color=gray]
    edge [fontsize=10 color=darkgreen constraint=true]

    subgraph cluster_0 {
      label = "ELF";

      n0 [shape=plain label="" fixedsize=false]
      n00 [label="ELF Exporter"]
      n01 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>gRPC Client</TD></TR>
          <TR><TD PORT="f0">batch_get_vm_perf_stats</TD></TR>
        </TABLE>
      >]

      {rank=same n00 n01}
    }

    subgraph cluster_1 {
      label = "SVT Agent";

      n1 [shape=plain label="" fixedsize=false]
      n12 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>gRPC Service</TD></TR>
          <TR><TD PORT="f1">BatchGetPerformanceData</TD></TR>

        </TABLE>
      >]
      n13 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>SVT</TD></TR>
          <TR><TD PORT="f0">guest-get-perf-data</TD></TR>
        </TABLE>
      >]
      n15 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>Agent</TD></TR>
          <TR><TD PORT="f0">SVTPerformanceDataWrapper</TD></TR>
        </TABLE>
      >]

      {rank=same n13 n15}

      n12 -> n13 [style=invis]

    }

    subgraph cluster_3 {
      label = "Storage";
      node[shape=cylinder]

      n3 [shape=plain label="" fixedsize=false]
      n31 [label="Prometheus"]
    }

    n00 -> n01 [label="collect perf data"]
    n01:f0 -> n12:f1
    n12:f1 -> n15:f0
    n15 -> n13:f0
    n00 -> n31 [label="save perf data"]
  }
```

- **设置VM的系统配置**

  VM设置VM的系统配置通过vmtoos-agent服务的V3 RESTFul API进行设置，必须在VM所在的节点进行设置，所以如果别的节点收到配置请求会转发到VM所在的节点进行处理，这是个同步的接口，设置的结果直接返回给调用者,设置完成后立即收集一次静态信息。

```{eval-rst}
.. graphviz::
  :align: center

  digraph G {
    rankdir=LR
    splines=true
    style=dotted
    nodesep=0.5
    node [shape=box fixedsize=false width=1.5 fontsize=10 style=filled fillcolor=snow fontsize=8 color=gray]
    edge [fontsize=10 color=darkgreen constraint=true]

    subgraph cluster_1 {
      label = "SVT Agent";

      n1 [shape=plain label="" fixedsize=false]
      n12 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>gRPC Service</TD></TR>
          <TR><TD PORT="f0">UpdateProperties</TD></TR>
        </TABLE>
      >]
      n13 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>SVT</TD></TR>
          <TR><TD PORT="f6">guest-set-hostname</TD></TR>
          <TR><TD PORT="f7">guest-set-user-password</TD></TR>
          <TR><TD PORT="f8">guest-set-dns-servers</TD></TR>
          <TR><TD PORT="f9">guest-set-ntp-servers</TD></TR>
          <TR><TD PORT="f10">guest-set-network</TD></TR>
          <TR><TD></TD></TR>
          <TR><TD PORT="f0">guest-fsfreeze-status</TD></TR>
          <TR><TD PORT="f1">guest-info</TD></TR>
          <TR><TD PORT="f2">guest-get-osinfo</TD></TR>
          <TR><TD PORT="f3">guest-network-get-interfaces</TD></TR>
          <TR><TD PORT="f4">guest-get-host-name</TD></TR>
          <TR><TD PORT="f5">guest-get-static-data</TD></TR>
        </TABLE>
      >]
      n14 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>DAO</TD></TR>
          <TR><TD PORT="f0">SVTReportsDAO</TD></TR>
        </TABLE>
      >]

      n15 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>Agent</TD></TR>
          <TR><TD PORT="f1">SVTPropertiesWrapper</TD></TR>
          <TR><TD PORT="f2">SVTStaticDataWrapper</TD></TR>
        </TABLE>
      >]

      {rank=same n12 n15 n14}
      n14 -> n15 -> n12 [style=invis]

    }

    subgraph cluster_3 {
      label = "Storage";
      node[shape=cylinder]

      n3 [shape=plain label="" fixedsize=false]
      n31 [label="Mongo DB"]
    }

    n4 [label="Client" shape=Mcircle fillcolor=skyblue]

    n4 -> n12:f0 [label="set vm properties"]
    n12:f0 -> n15
    n15:f1 -> n13:f6
    n13:f6:e -> n13:f7:e -> n13:f8:e -> n13:f9:e-> n13:f10:e
    n15:f2 -> n13:f0
    n13:f0:e -> n13:f1:e -> n13:f2:e -> n13:f3:e -> n13:f4:e -> n13:f5:e
    n13:f5:w -> n14:f0
    n14:f0:w -> n31 [label="save guest info"]
  }
```

- **重建VM**

  从快照和VM模块重建VM时需要处理一下挂载的SVT ISO，因为无法通过ELF Images的接口查询到SVT ISO，所以需要处理一下才可以在新的VM中继承SVT ISO

```{eval-rst}
.. graphviz::
  :align: center

  digraph G {
    rankdir=LR
    splines=true
    style=dotted
    nodesep=0.5
    node [shape=box fixedsize=false width=1 fontsize=10 style=filled fillcolor=snow fontsize=8 color=gray]
    edge [fontsize=8 color=darkgreen constraint=true]

    subgraph cluster_0 {
      label = "ELF";

      n0 [shape=plain label="" fixedsize=false]
      n00 [label="RESTFul API"]
      n01 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>Common</TD></TR>
          <TR><TD PORT="f0">VMTemplateWrapper</TD></TR>
          <TR><TD PORT="f1">VMSnapshotWrapper</TD></TR>
          <TR><TD PORT="f2">VMToolsWrapper</TD></TR>
        </TABLE>
      >]

      {rank=same n00 n01}

      n01 -> n00 [style=invis]
    }

    subgraph cluster_1 {
      label = "SVT Agent";

      n1 [shape=plain label="" fixedsize=false]
      n10 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>SVT</TD></TR>
          <TR><TD PORT="f0">SVTImageWrapper</TD></TR>
        </TABLE>
      >]
      n11 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>DAO</TD></TR>
          <TR><TD PORT="f0">SVTImageDAO</TD></TR>
        </TABLE>
      >]

      {rank=same n10 n11}
    }

    subgraph cluster_3 {
      label = "Storage";
      node[shape=cylinder]

      n3 [shape=plain label="" fixedsize=false]
      n32 [label="Mongo DB"]

    }

    n4 [label="Client" shape=Mcircle fillcolor=skyblue]

    n4 -> n00 [label="create vm from template"]
    n00 -> n01:f0
    n01:f2:w -> n10:f0:s
    n10 -> n11
    n11:f0 -> n32 [label="query iso info"]

    n4 -> n00 [label="create vm from snapshot" color=orange]
    n00 -> n01:f1:e [color=orange]
    n01:f2 -> n10:f0  [color=orange]
    n10 -> n11 [color=orange]
    n11:f0 -> n32 [label="query iso info" color=orange]

  }
```

- **快照回滚**

  因为快照回滚后可能会回到没有安装VMTools的状态，而vmtools-agent无法区分回滚后的VM是没有安装Guest Agent还是没有启动Guest Agent服务，所以在回滚时选择清空相关的VMTools数据，当这个VM启动后会再次收集VMTools的数据

```{eval-rst}
.. graphviz::
  :align: center

  digraph G {
    rankdir=LR
    splines=true
    style=dotted
    nodesep=0.5
    node [shape=box fixedsize=false width=1 fontsize=10 style=filled fillcolor=snow fontsize=8 color=gray]
    edge [fontsize=8 color=darkgreen constraint=true]

    subgraph cluster_0 {
      label = "ELF";

      n0 [shape=plain label="" fixedsize=false]
      n00 [label="RESTFul API"]
      n01 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>Common</TD></TR>
          <TR><TD PORT="f0">VMSnapshotWrapper</TD></TR>
          <TR><TD PORT="f2">VMToolsWrapper</TD></TR>
        </TABLE>
      >]

      {rank=same n00 n01}

      n01 -> n00 [style=invis]
    }

    subgraph cluster_1 {
      label = "SVT Agent";

      n1 [shape=plain label="" fixedsize=false]
      n10 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>SVT</TD></TR>
          <TR><TD PORT="f0">SVTStaticDataWrapper</TD></TR>
        </TABLE>
      >]
      n11 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>DAO</TD></TR>
          <TR><TD PORT="f0">ResourceDAO</TD></TR>
        </TABLE>
      >]

      {rank=same n10 n11}
    }

    subgraph cluster_3 {
      label = "Storage";
      node[shape=cylinder]

      n3 [shape=plain label="" fixedsize=false]
      n32 [label="Mongo DB"]

    }

    n4 [label="Client" shape=Mcircle fillcolor=skyblue]

    n4 -> n00 [label="rollback snapshot"]
    n00 -> n01:f0
    n01:f2 -> n10:f0
    n10 -> n11
    n11:f0 -> n32 [label="reset guest info"]
  }
```

- **设置VM网卡的静态IP**

  如果修改VM是网卡的IP/子网掩码/网关存在数据且至少有一个发生了变化，那么Job Center在更新VM是会调用vmtools-agent的gRPC接口设置网卡的静态IP，设置完成后会立即收集一次静态信息

```{eval-rst}
.. graphviz::
  :align: center

  digraph G {
    rankdir=LR
    splines=true
    style=dotted
    nodesep=0.5
    node [shape=box fixedsize=false width=1 fontsize=10 style=filled fillcolor=snow fontsize=8 color=gray]
    edge [fontsize=8 color=darkgreen constraint=true]

    subgraph cluster_0 {
      label = "ELF";

      n0 [shape=plain label="" fixedsize=false]
      n00 [label="RESTFul API"]
      n01 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>Common</TD></TR>
          <TR><TD PORT="f0">VMWrapper</TD></TR>
          <TR><TD PORT="f2">VMToolsWrapper</TD></TR>
        </TABLE>
      >]
      n02 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>Job Center</TD></TR>
          <TR><TD PORT="f0">kvm_vm_config</TD></TR>
        </TABLE>
      >]
      n03 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>gRPC Client</TD></TR>
          <TR><TD PORT="f0">VmToolsClient</TD></TR>
        </TABLE>
      >]

      {rank=same n01 n00}
      {rank=same n02 n03}

      n01 -> n00 [style=invis]
    }

    subgraph cluster_1 {
      label = "SVT Agent";

      n1 [shape=plain label="" fixedsize=false]
      n12 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>gRPC Service</TD></TR>
          <TR><TD PORT="f0">UpdateProperties</TD></TR>
        </TABLE>
      >]
      n13 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>SVT</TD></TR>
          <TR><TD PORT="f10">guest-set-network</TD></TR>
          <TR><TD></TD></TR>
          <TR><TD PORT="f0">guest-fsfreeze-status</TD></TR>
          <TR><TD PORT="f1">guest-info</TD></TR>
          <TR><TD PORT="f2">guest-get-osinfo</TD></TR>
          <TR><TD PORT="f3">guest-network-get-interfaces</TD></TR>
          <TR><TD PORT="f4">guest-get-host-name</TD></TR>
          <TR><TD PORT="f5">guest-get-static-data</TD></TR>
        </TABLE>
      >]
      n14 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>DAO</TD></TR>
          <TR><TD PORT="f0">SVTReportsDAO</TD></TR>
        </TABLE>
      >]

      n15 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>Agent</TD></TR>
          <TR><TD PORT="f1">SVTPropertiesWrapper</TD></TR>
          <TR><TD PORT="f2">SVTStaticDataWrapper</TD></TR>
        </TABLE>
      >]

      {rank=same n12 n15}
      {rank=same n13 n14}


    }

    subgraph cluster_3 {
      label = "Storage";
      node[shape=cylinder]

      n3 [shape=plain label="" fixedsize=false]
      n32 [label="Mongo DB"]

    }

    n4 [label="Client" shape=Mcircle fillcolor=skyblue]

    n4 -> n00 [label="update vm nic" color=orange]
    n00 -> n01 [color=orange]
    n01:f0 -> n02:w [color=orange style=dashed label="submit job"]
    n02:f0 -> n01:f2
    n01:f2 -> n03
    n03:f0 -> n12
    n12 -> n15
    n15:f1 -> n13:f10 [label="set network"]
    n15:f2 -> n13:f0 [label="collect static data"]
    n13:f0:w -> n13:f1:w -> n13:f2:w -> n13:f3:w -> n13:f4:w -> n13:f5:w
    n13:f5:e -> n14:f0
    n14:f0 -> n32 [label="save guest info"]
  }
```

- **创建VM文件系统一致性快照**

  当创建VM快照时选择了文件系统一致性快照，那么创建快照的JC任务会先通过vmtools-agent的gRPC接口冻结VM的文件系统，然后并发对VM的所有虚拟机做快照，快照结束后再解冻VM的文件系统，为了避免系统异常导致创建快照的job结束后VM一直被冻结，这里设计了两个兜底的策略：

  - 启动一个后台线程监视冻结持续的事件，超过阈值后强制解冻VM
  - 使用周期性任务检测非操作中的VM，强制解冻VM

```{eval-rst}
.. graphviz::
  :align: center

  digraph G {
    rankdir=LR
    splines=true
    style=dotted
    nodesep=0.5
    node [shape=box fixedsize=false width=1 fontsize=10 style=filled fillcolor=snow fontsize=8 color=gray]
    edge [fontsize=8 color=darkgreen constraint=true]

    subgraph cluster_0 {
      label = "ELF";

      n0 [shape=plain label="" fixedsize=false]
      n00 [label="RESTFul API"]
      n01 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>Common</TD></TR>
          <TR><TD PORT="f2">VMToolsWrapper</TD></TR>
          <TR><TD PORT="f0">VMSnapshotWrapper</TD></TR>
        </TABLE>
      >]
      n02 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>Job Center</TD></TR>
          <TR><TD PORT="f0">kvm_vm_fs_consistent_snapshot_create</TD></TR>
        </TABLE>
      >]
      n03 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>gRPC Client</TD></TR>
          <TR><TD PORT="f0">VmToolsClient</TD></TR>
        </TABLE>
      >]

      {rank=same n01 n02}
      {rank=same n00 n03}

    }

    subgraph cluster_1 {
      label = "SVT Agent";

      n1 [shape=plain label="" fixedsize=false]
      n10 [label="Scheduler Service" fillcolor=skyblue]
      n11 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>gRPC Client</TD></TR>
          <TR><TD PORT="f1">batch_thaw_svt_file_system</TD></TR>
        </TABLE>
      >]
      n12 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>gRPC Service</TD></TR>
          <TR><TD PORT="f2">BatchThawFileSystem</TD></TR>
          <TR><TD PORT="f1">ThawFileSystem</TD></TR>
          <TR><TD PORT="f0">FreezeFileSystem</TD></TR>
        </TABLE>
      >]
      n13 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>SVT</TD></TR>
          <TR><TD PORT="f0">guest-fsfreeze-freeze</TD></TR>
          <TR><TD PORT="f1">guest-fsfreeze-thaw</TD></TR>
        </TABLE>
      >]
      n14 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>DAO</TD></TR>
          <TR><TD PORT="f0">ResourceLockDAO</TD></TR>
        </TABLE>
      >]

      n15 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>Agent</TD></TR>
          <TR><TD PORT="f1">SVTFileSystemWrapper</TD></TR>
        </TABLE>
      >]

      {rank=same n12 n15 n13}
      {rank=same n10 n11}

      n11 -> n10 [style=invis]

    }

    subgraph cluster_3 {
      label = "Storage";
      node[shape=cylinder]

      n3 [shape=plain label="" fixedsize=false]
      n32 [label="Mongo DB"]

    }

    n4 [label="Client" shape=Mcircle fillcolor=skyblue]

    n4 -> n00 [label="update fs \nconsistent snapshot" color=orange]
    n00 -> n01:f0 [color=orange]
    n01 -> n02 [color=orange style=dashed label="submit job"]

    n02:f0:e -> n01:f2:e [label="2.thaw vm" color=brown constraint=false]
    n01:f2:w -> n03:f0:sw [color=brown]
    n03:f0:nw -> n12:f1:w [label="call rpc" color=brown]
    n12 -> n15 [color=brown]
    n15:f1:e -> n13:f1:e [label="thaw fs" color=brown]

    n10 -> n11 [label="thaw fs" color=blue]
    n11:f0 -> n12:f2 [color=blue]
    n12 -> n15 [color=blue]
    n15:f1 -> n14:f0 [color=blue label="1.get unlocked vms"]
    n14:f0 -> n32 [color=blue label="query locks"]
    n15 -> n13:f2 [color=blue label="2.thaw unlocked vms"]

    n02:f0:w -> n01:f2:nw [label="1.freeze vm" constraint=false]
    n01:f2 -> n03:f0:se
    n03:f0:ne -> n12:f0:w [label="call rpc"]
    n12 -> n15
    n15:f1:w -> n13:f0:w [label="freeze fs"]
    n15:f1:w -> n13:f2:w [label="thaw vm if timeout" style=dashed]
  }
```

- **清理VMTools数据**

  {code}`elf-tool elf_vm remove_vm_tools` 命令可以清理VM上残留的guest info信息，就是清理mongodb中这个VM的VMTools数据

```{eval-rst}
.. graphviz::
  :align: center

  digraph G {
    rankdir=LR
    splines=true
    style=dotted
    nodesep=0.5
    node [shape=box fixedsize=false width=1 fontsize=10 style=filled fillcolor=snow fontsize=8 color=gray]
    edge [fontsize=8 color=darkgreen constraint=true]

    subgraph cluster_0 {
      label = "ELF";

      n0 [shape=plain label="" fixedsize=false]
      n00 [label="CMD"]
      n01 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>Common</TD></TR>
          <TR><TD PORT="f0">VMToolsWrapper</TD></TR>
        </TABLE>
      >]

      {rank=same n00 n01}
    }

    subgraph cluster_1 {
      label = "SVT Agent";

      n1 [shape=plain label="" fixedsize=false]
      n10 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>SVT</TD></TR>
          <TR><TD PORT="f0">SVTStaticDataWrapper</TD></TR>
        </TABLE>
      >]
      n11 [shape=plaintext label=<
        <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
          <TR><TD>DAO</TD></TR>
          <TR><TD PORT="f0">SVTReportsDAO</TD></TR>
        </TABLE>
      >]

      {rank=same n10 n11}

      n11 -> n10 [style=invis]
    }

    subgraph cluster_3 {
      label = "Storage";
      node[shape=cylinder]

      n3 [shape=plain label="" fixedsize=false]
      n32 [label="Mongo DB"]

    }

    n4 [label="User" shape=Mcircle fillcolor=skyblue]

    n4 -> n00 [label="remove_vm_tools"]
    n00 -> n01:f0
    n01:f0 -> n10:f0
    n10:f0:e -> n11:f0
    n11:f0 -> n32 [label="reset guest info"]
  }
```
