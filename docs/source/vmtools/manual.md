# 使用手册

## 功能介绍

在ELF平台上节点OS和VM的Guest OS是隔离的，所以ELF平台无法直接获取VM内部的信息，SVT提供了一种virtio-serial通道，能够将VM内部的信息发送给Hypervisor，通过在虚拟机内部安装SVT工具，就可以支持ELF实现下面的功能：

- *实时收集VM Guest OS的配置信息*
- *实时收集VM Guest OS的性能信息*
- *ELF直接修改VM Guest OS的IP/用户名密码/Hostname/NTP server*
- *ELF生成VM的文件系统一致性快照*

## 安装SVT

SMTX VM Tools包含两个部分：

- Agent: 使用rpm进行打包安装，部署在每个ELF节点上，服务名为vmtools-agent
- SVT: 使用ISO进行打包，安装VM内部，就是对qemu-ga进行了重新打包，服务名是SVT

所以VMTools的安装分为集群内部启用和VM内部的安装两个步骤

### 集群启用SVT

1. **升级集群**
   : SMTX VM Tools的功能在4.0.3之后的版本才可以使用，如果集群的版本小于4.0.3，需要先升级集群的版本，具体的升级步骤请参考SMTXOS 4.0.3的升级手册。
2. **下载VM Tools安装ISO**
   : SMTX VM Tools以独立的ISO映像文件发布，不包含在SMTXOS ISO映像里面，需要单独下载 [SVT ISO]
3. **上传ISO到ELF集群**
   : 通过上传SMTX VM Tools ISO映像到集群可以启用集群的SMTX VM Tools功能, 在集群”系统设置=》虚拟机工具”页面上传ISO映像，上传成功后页面会显示启用的SMTX VM Tools的版本

### 虚拟机内安装SVT

1. **挂载SVT安装ISO映像**

   > SVT 安装程序通过CD ROM的方式挂载到VM中，然后在VM的Guest OS里面进行安装，在“VM详情“页面点击”安装虚拟机工具“按钮进行挂载，VM挂载ISO映像之前需要保证：
   >
   > > - VM的Guest OS是64位的操作系统
   > > - VM至少有一个空闲的CDROM

2. **Windows系统安装SVT服务**

   > SVT需要在Guest OS内部进行安装，如果Guest OS是Windows，那么SVT的安装需要Administrator的权限，具体的安装步骤如下：
   >
   > > - 双击虚拟机工具的CDROM，打开CDROM的目录：
   > > - 进入安装ISO映像所在的目录双击执行SMTX_VM_TOOLS_INSTALL.exe进行安装：
   > > - 安装完成检查SMTX VM Tools的系统服务是否运行

3. **Linux系统安装VMTools**

   > VMTools需要在Guest OS内部进行安装，如果Guest OS是Linux，那么SVT的安装需要root权限，具体的安装步骤如下：
   >
   > > - 使用root用户ssh到vm，或者使用普通用户ssh到vm然后使用sudo执行后续的安装命令:
   > >
   > >   ```
   > >   ssh root@<vm_ip>
   > >   ```
   > >
   > > - 在Guest OS内部挂载安装ISO映像，其中 dir_to_mount_iso 是要用来挂载 iso 映像的目录vmtools_iso_cdrom 是前面挂载步骤中，已挂载了 iso 映像的 CDROM 的设备名称。可以通过 lsblk 命令来查看此设备名称:
   > >
   > >   ```
   > >   mkdir /mnt/<dir_to_mount_iso>
   > >   mount -o loop /dev/<vmtools_iso_cdrom> /mnt/<dir_to_mount_iso>
   > >   # example
   > >   mkdir /mnt/cdrom
   > >   mount -o loop /dev/sr0 /mnt/cdrom
   > >   ```
   > >
   > > - 执行安装脚本:
   > >
   > >   ```
   > >   bash /mnt/<dir_to_mount_iso>/SMTX_VM_TOOLS_INSTALL.sh
   > >   ```
   > >
   > > - 检查SVT服务是否运行:
   > >
   > >   ```
   > >   systemctl status SVT
   > >   ```

## 升级SVT

Agent部分的升级随着集群的升级一起升级，因为SVT ISO是单独发布的，所以需要升级集群上存储SVT ISO

### 集群升级SVT

1. **下载VM Tools安装ISO**

   > 下载 [SVT ISO]，查看集群当前的SMTX VM Tools的版本，如果下载的ISO映像版本大于等于当前集群的SMTX VM Tools的版本，则可以进行升级集群SMTX VM Tools的操作，当前集群SMTX VM Tools的版本在集群”系统设置=》虚拟机工具”页面查看:

2. **上传新版本ISO到ELF集群**

   > 在”系统设置=》虚拟机工具”页面上传新版本的SMTX VM Tools ISO映像，上传成功后页面会显示升级后的SMTX VM Tools版本

### VM内升级SVT

1. **挂载SMTX VM Tools升级ISO映像**

   > 如果集群的SMTX VM Tools的版本大于VM内部的SMTX VM Tools的版本，那么这个VM的SVT就可以升级，升级需要先挂载SMTX VM Tools的升级ISO映像，VM挂载ISO映像之前需要保证：
   >
   > > - VM的Guest OS是64位的操作系统
   > > - VM处于开机状态
   > > - VM至少有一个空闲的CDROM或者有一个已经挂载了SMTX VM Tools映像的CDROM
   >
   > 在VM详情页面点击”升级虚拟机工具“挂载升级ISO映像

2. **Windows系统升级SVT**

   > 如果Guest OS是windows操作系统，那么SMTX VM Tools的升级操作需要Administrator的权限，具体的升级步骤如下：
   >
   > > - 双击虚拟机工具安装的CDROM，打开CDROM的目录：
   > > - 进入升级ISO映像所在的目录双击SMTX_VM_TOOLS_INSTALL.exe进行升级：
   > > - 升级完成后检查SMTX VM Tools的系统服务是否运行

3. **Linux系统升级SVT**

   > 如果Guest OS是Linux，那么SMTX VM Tools的升级操作需要root权限，具体的升级步骤如下：
   >
   > > - 使用root用户ssh到vm，或者使用普通用户ssh到vm然后使用sudo执行后续的升级命令:
   > >
   > >   ```
   > >   ssh root@<vm_ip>
   > >   ```
   > >
   > > - 在Guest OS内部挂载升级ISO映像，其中 dir_to_mount_iso 是要用来挂载 iso 映像的目录，vmtools_iso_cdrom 是前面挂载步骤中，已挂载了 iso 映像的 CDROM 的设备名称。可以通过 lsblk 命令来查看此设备名称:
   > >
   > >   ```
   > >   mkdir /mnt/<dir_to_mount_iso>
   > >   mount -o loop /dev/cdrom /mnt/<dir_to_mount_iso>
   > >   # example
   > >   mkdir /mnt/cdrom
   > >   mount -o loop /dev/sr0 /mnt/cdrom
   > >   ```
   > >
   > > - 执行安装脚本进行升级:
   > >
   > >   ```
   > >   bash /mnt/<dir_to_mount_iso>/SMTX_VM_TOOLS_INSTALL.sh
   > >   ```
   > >
   > > - 检查SVT服务是否运行:
   > >
   > >   ```
   > >   systemctl status SVT
   > >   ```

## 卸载VMTools

卸载SVT只要从VM内部删除SVT软件，如果VM挂载有SVT ISO点击VM详情页面的”弹出ISO映像”按钮卸载SMTX VM Tools ISO映像。

### Windows系统卸载SVT

和卸载普通的应用程序一样，从控制面板进行卸载

### Linux系统卸载SVT

SMTX VM Tools在Linux上安装的时候会创建卸载脚本，SMTX VM Tools的卸载需要root权限，具体的卸载步骤如下：

> - 使用root用户ssh到vm，或者使用普通用户ssh到vm然后使用sudo执行后续的安装命令:
>
>   ```
>   ssh root@*************
>   ```
>
> - 执行卸载命令:
>
>   ```
>   /opt/svt/uninstall_svt.sh
>   ```
>
> - 检查VMTools服务是否已经删除:
>
>   ```
>   systemctl status SVT
>   ```

## 使用VMTools

SVT主要提供三类功能，分别是：

- *收集Guest信息*
- *设置Guest OS*
- *创建一致性快照*

### 收集Guest信息

SVT收集到的Guest信息有两类:

- **静态信息**

  > 包括操作系统类型，内核版本，主机名，IP地址，网关地址，DNS服务器地址，SVT工具的版本和状态，静态信息主要展示在三个地方：
  > \* VM列表页面，展示IP地址和操作系统类型
  > \* VM详情页面，展示除网卡信息外的其它所有信息
  > \* VM虚拟网卡页面，展示网卡的IP，掩码和网关信息

- **性能信息**

  > SVT收集的性能信息包括CPU和内存的使用率，有监控报警系统负责展示

### 设置Guest OS

SVT在两个地方提供了设置Guest OS的操作：

- *VM详情页面*
- *VM虚拟网卡页面*

支持的设置Guest OS的操作包括：

- 设置主机名，windows系统需要重启后才生效
- 设置DNS服务器
- 设置NTP服务器
- 设置账户的密码
- 设置网卡的IP，掩码和网关，输入为空或者信息没有变化时跳过设置

### 创建一致性快照

VM创建快照的页面增加了 {code}`创建文件系统一致性快照` 的选项，它基于SVT在创建VM快照的时候将VM内存里的数据强制落盘，实现创建一致性的快照，

[svt iso]: http://************/distros/isostore/svt/
