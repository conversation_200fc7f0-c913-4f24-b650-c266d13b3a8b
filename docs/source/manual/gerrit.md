# Gerrit使用指南

Pyzbs使用Gerrit评审代码，本文对Gerrit的使用做一个简单的介绍，详细的介绍请参考 [GerritGuides]

## 开发流程介绍

在Pyzbs的开发流程中，我们使用 [Jira] 管理和跟踪开发任务，使用 [Jenkins] 进行持续集成，使用 [Gerrit] 进行代码Review，Gerrit在整个流程中处于核心的地位，它负责触发持续集成和自动更新Jira Issue状态，具体的流程如下;

```{eval-rst}
.. graphviz::
  :caption: Git Work Flow
  :align: center

  digraph {
    graph [rankdir=LR overlap=true splines=line]
    node [shape="box" width=0.1 height=1 label=""]
    edge [dir="forward" style=solid arrowhead="vee", arrowtail="vee" arrowsize=0.5]

    subgraph reviewer {
      graph [rank="same"]
      edge [dir="none" style="dashed"]

      reviewer_start [shape="plaintext", label="Reviewer" height=0]
      reviewer_review_result [shape="diamond", label="Review Result"]
      reviewer_start -> reviewer_review_result
    }

    subgraph developer {
      graph [rank="same"]
      edge [dir="none" style="dashed"]

      developer_start [shape="plaintext", label="Developer" height=0]

      developer_start -> developer_create_issue -> developer_start_issue -> developer_commit_code -> developer_ammend_code -> developer_add_reviewer -> developer_merge_commit

    }

    subgraph localrepo {
      graph [rank="same"]
      edge [dir="none" style="dashed"]

      localrepo_start [shape="plaintext", label="LocalRepo" height=0]
      localrepo_start -> localrepo_commit_code -> localrepo_ammend_code
    }

    subgraph gerrit {
      graph [rank="same"]
      edge [dir="none" style="dashed"]

      gerrit_start [shape="plaintext", label="Gerrit" height=0]
      gerrit_start -> gerrit_review_code -> gerrit_ci_failed -> gerrit_ci_success -> gerrit_reviewer_approve -> gerrit_merge_commit
    }

    subgraph jenkins {
      graph [rank="same"]
      edge [dir="none" style="dashed"]

      jenkins_start [shape="plaintext", label="Jenkins" height=0]
      jenkins_ci_result [shape="diamond", label="CI Result"]
      jenkins_start -> jenkins_create_ci
    }

    subgraph jira {
      graph [rank="same"]
      edge [dir="none" style="dashed"]

      jira_start [shape="plaintext", label="Jira" height=0]

      jira_start -> jira_create_issue -> jira_start_issue -> jira_review_issue -> jira_resolve_issue -> jira_close_issue

    }

    reviewer_start -> developer_start -> localrepo_start -> gerrit_start -> jenkins_start -> jira_start [style="invis" weight=3]
    developer_create_issue -> jira_create_issue [label="Create Task" weight=4]
    developer_start_issue -> jira_start_issue [label="Start Task" weight=4]
    developer_commit_code -> localrepo_commit_code  [label="Commit Source Code" weight=4]
    localrepo_commit_code -> gerrit_review_code [label="Create Review Request" weight=4]
    gerrit_review_code -> jira_review_issue [label="Issue IN REVIEW" weight=4]
    gerrit_review_code:s -> jenkins_create_ci:n [label="Trigger CI" weight=4]
    jenkins_create_ci -> jenkins_ci_result [label="Run CI"]
    jenkins_ci_result:w -> gerrit_ci_failed:e [label="Failed(gerrit -1)" weight=4]
    gerrit_ci_failed -> developer_ammend_code [label="Refine Code Request" weight=4]
    developer_ammend_code:ne -> localrepo_ammend_code:sw [label="Ammend Source Code"]
    localrepo_ammend_code:n -> gerrit_review_code:s [label="Patch Review Request" weight=4]
    jenkins_ci_result:s -> gerrit_ci_success [label="Success(gerrit +1)" ]
    developer_add_reviewer -> gerrit_ci_success [label="Add reviewers"]
    reviewer_review_result:e -> developer_ammend_code [label="Reject(Refine Code)"]
    reviewer_review_result:s -> gerrit_reviewer_approve [label="Approve(gerrit +2)" weight=3]
    gerrit_reviewer_approve -> jira_resolve_issue [label="Issue RESOLVED" weight=3]
    developer_merge_commit -> gerrit_merge_commit [label="Merge Commit" weight=3]
    gerrit_merge_commit -> jira_close_issue [label="Issue CLOSED" weight=3]
  }

```

## Gerrit工作流程

Gerrit Review的流程包含两个重要的概念：

- Change-Id

  Change-Id用来标示一次代码审查任务，也就是一次变更，它是一串SHA-1字符串且在repo的每个branch中是唯一的，由commit hook自动生成，生成的change-id添加到commit message的最后一行，通常一次变更会包含多次git commit，而每次提交的时候commit id都不相同，gerrit通过commit message里面的change-id将它们关联为同一次变更

- Patch Set

  一个change-id可以对应多个patch set，一个Patch Set就是一次commit，对应到gerrit中的一个暂存分支。提交一个新的patch set会自动覆盖这个change-id下面的前一个Patch Set， 默认情况下，仅最后一个Patch Set是有意义的，Code Review通过时，也仅仅是最后一个Patch Set会合并到指定的branch中。

所以Gerrit review是基于change-id进行的，详细的review操作界面请参考 [GerritReviewUI] , 主要的流程如下：

- 上传一个commit

  当通过git-review将commit上传到Gerrit上等待review时，Gerrit会创建一个格式为 {code}`refs/changes/xx/yy/zz` 的暂存分支来保存这个commit，在branch名称里面xx是change-id的后两位，yy是change number， zz为这个commit的patch set number

- 添加Reviewers

  在Gerrit的Change界面可以添加相关reviewers

- 上传一个新的patch set

  当上传的commit被reviewer打回来时，我们修改并重新提交comit，在原来的commit基础上进行修改，重新add，amend commit，然后上传这个新的commit

- 提交Change

  Gerrit的Change被配置成只有在Code-Review +2 和CI +1 的情况下才可以Submit，Submit时可能会有冲突，此时可以先尝试Gerrit页面提供的Rebase功能做一次Rebase操作，如果提示冲突，则需在本地解决冲突后重新提交一个Patch Set到该Change上。

## 配置

- 申请账号和权限

  SmartX的 [Gerrit] 使用公司的ldap账号进行登陆，如果登陆后没有pyzbs repo的权限，请请发信给***************申请权限

- 上传sshkey

  在开发机上生成ssh pubkey，然后上传到gerrit，上传的菜单路径为 "Settings" -> "SSH Public Keys"

- 安装git-review:

  ```
  pip install git-review
  ```

- 克隆代码仓库, 注意需要替换下面所有git repo中的username为登陆的账号:

  ```
  git clone "ssh://<EMAIL>:29518/pyzbs"
  ```

  然后切换到repo目录，后续的操作都要在repo目录下进行:

  ```
  cd pyzbs
  ```

- 添加review的gerrit remote分支:

  ```
  git remote add gerrit ssh://<EMAIL>:29518/pyzbs
  ```

- 配置commit hook:

  ```
  scp -p -P 29518 <EMAIL>:hooks/commit-msg ".git/hooks/"
  ```

- 配置commit user:

  ```
  git config --global user.name "Yanzhi Zhang"  # change to your name
  git config --global user.email "<EMAIL>"  # change to your email
  ```

## 提交评审

- 提交单个commit到master分支:

  ```
  git checkout -b feature-branch gerrit/master
  # after modifing some files
  git add files
  git commit -m "Fixes ELF-xxx commit log1"
  git review -t [feature-id] master
  ```

- 提交多个commit到master分支:

  ```
  git checkout -b feature-branch gerrit/master
  # after modifing some files
  git add files
  git commit -m "Fixes ELF-xxx commit log1"
  # modified some new files
  git add files
  git commit -m "Fixes ELF-xxx commit log2"
  git review -t [feature-id] master
  ```

## 修改已提交的评审

- 修改master分支上的单个commit

  如果你的commit被reviewer拒绝了，你可以在本地继续进行修改，修改完成后，调用git commit --amend修改你的上次commit，然后再次git review，示例如下:

  ```
  # commit is rejected by reviewer
  # modified some files...
  git commit --amend
  git review -t [feature-id] master
  ```

- 修改master分支上的多个commit

  假设我们当前在本地分支dev上，一次提交了3个commit，commit1/commit2/commit3. 如果commit2/commit1被reject了，修改方式如下:

  ```
  git rebase -i HEAD~3
  ```

  Rebase的选择界面如下:

  ```
  pick b5db992 commit1
  pick 75acfe3 commit2
  pick 4833149 commit3

  # Rebase 6ea4f2d..4833149 onto 6ea4f2d
  #
  # Commands:
  #  p, pick = use commit
  #  r, reword = use commit, but edit the commit message
  #  e, edit = use commit, but stop for amending
  #  s, squash = use commit, but meld into previous commit
  #  f, fixup = like "squash", but discard this commit's log message
  #  x, exec = run command (the rest of the line) using shell
  #
  # If you remove a line here THAT COMMIT WILL BE LOST.
  # However, if you remove everything, the rebase will be aborted.
  #
  ```

  通常，如果我们只需要修改commit log的内容，而不是代码内容，可以将对应的commit的 pick修改成r，或reword。如果想要修改代码，则修改成e。修改完后，保存并退出编辑器。然后git会根据你刚才指定的命令，逐一执行:

  > - 如果是p命令，不会有任何操作，直接执行下一条命令。
  >
  > - 如果是r命令，则会弹出一个编辑器，让你重新编辑commit log。
  >
  > - 如果是e命令，则会给出如下一段提示:
  >
  >   ```
  >   Stopped at 75acfe3... commit2
  >   You can amend the commit now, with
  >
  >       git commit --amend
  >   ```

  做完所有的修改后执行:

  ```
  git rebase --continue
  git review -t [feature-id] master
  ```

## Review代码

Reviewer收到review的请求后，需要到change页面逐行review代码，并在review的过程中在对应的代码位置添加comment:

- 如果没有comment，点击change页面的 {code}`REPLY` 按钮，选择 {code}`+1` 选项后 {code}`SEND` review结果，commit被approved
- 如果有comment，点击change页面的 {code}`REPLY` 按钮，选择 {code}`-1` 选项后 {code}`SEND` review结果，commit被rejected

## Merge代码

当commit被reviewer和CI approve后，commit的拥有者可以Merge代码到release分支:

- 如果只想merge feature的一个commit，直接 {code}`SUBMIT` 到release分支
- 如果想merge feature下面的所有commits，找到 {code}`Submitted together` 中最后一个commit，然后 {code}`SUBMIT INCLUDING PARENTS` 到release分支

## Cherry代码

有些代码在master分支merge后需要同时提交到别的release分支，这时可以使用cherry-pick进行操作:

```
git checkout -b [feature-branch] gerrit/[release-branch]
git cherry-pick -x [commit]
# update and commit ammend souce code if there are conflicts
git review -t [feature-id] [release-branch]
```

[gerrit]: http://gerrit.smartx.com/
[gerritguides]: https://gerrit-review.googlesource.com/Documentation/
[gerritreviewui]: https://gerrit-review.googlesource.com/Documentation/user-review-ui.html
[gerrit使用指南]: https://www.ieclipse.cn/2016/05/14/other/tech-gerrit-guide/
[gerrit工作流程]: http://lipeng1667.github.io/2017/01/18/gerrit-guide/
[jenkins]: http://*************:8080/job/pyzbs-gerrit-changes/
[jira]: http://jira.smartx.com/
