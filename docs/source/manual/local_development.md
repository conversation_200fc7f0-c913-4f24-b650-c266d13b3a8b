# pyzbs本地开发环境手册

## 本地开发环境V2版本简介

### 旧版本开发环境存在的问题

* **无法快捷的调试单测用例**\
  旧的版本的环境提供了本地执行单测用例的命令，但是没有提供直接调试测试用例的命令，想要调试单测用例的话，需要先修改单测执行脚本，然后进到单测的容器里面进行调试，过程很复杂

* **经常遇到podman版本不兼容的问题**\
  pyzbs master分支上Makefile里面使用了v0.1.11的smtxauto image，里面集成的podman版本是2.2.1，这个版本的兼容性有点问题，在好多同事的开发机上无法启动pod，必须回退到v0.1.5的smtxauto才可以启动pod(内部集成的podman版本是2.0.6)

* **需要手动清理残留的builder容器**\
  builder容器在第一次启动后在后台会一直运行，需要手工执行命令才可以清理干净，但是许多同事不知道怎么清理

* **smtxauto docker image太大**\
  harbor在IDC，如果大家的开发机不在IDC的话，第一次使用的时候会花费很长的时间拉取docker image


### 新版本开发环境改进的地方

* **支持进入测试用例调试模式**\
  添加了直接进入单测环境的make命令，方便调试单个测试用例

* **升级podman到3.x版本**\
  使用了debian的base image，支持安装最新版本的podman，解决了2.2.x版本的兼容性问题

* **自动清理builder容器**\
  改进了容器的使用方式，只有单测和build arm image的时候才使用container inside container，而且命令结束的时候自动清理builder容器，对于其它的操作直接使用container执行

* **使用精简版本的docker image**\
  将完整的smtxauto image按照功能模块拆出几个小的docker image，pyzbs使用到的image包括：
  * py2tools
  * py3tools
  * podrunner

* **支持跨平台打包rpm**\
  通过使用qemu-static可以在x86平台上运行arm64版本的容器，从而可以实现在x86的开发机上打包arm64平台的rpm包


## 本地开发环境设计方案

### 整体设计思路

最开始pyzbs的单元测试和rpm打包都依赖于远程CI系统来执行，本地开发环境很简单，只需要一个Python解释器然后安装上pyzbs依赖的包就可以了，主要用来给IDE提供语法检查的解释器环境，但是现有的本地开发环境存在下面的问题：

* **不能调试和执行单测**\
  如果要检验调试代码的bug，大部分情况下都通过CI job进行，这就需要频繁的提交patch到gerrit，整个过程比较繁琐，也非常耗时

* **不能静态检查和格式化代码**\
  最近我们在开发流程中增加了代码格式化和静态检查的步骤，而CI job只能做静态检查，无法做代码格式化，因为需要提交格式化后的代码

* **不能打包RPM**\
  想打包一个临时的rpm也要通过CI执行，打包完成后还要到CI server上拷贝下来，过程太繁琐，而且amd64和arm64的rpm需要在不同的机器上打包

所以我们想改进一下，让本地开发环境能够支持下面的这些操作：

* 代码格式化
* 代码静态检查
* 执行单元测试
* 调试单元测试
* 跨平台打包rpm
* 生成，浏览开发文档和测试报告

本地开发环境的主要设计目标是尽可能的和CI环境保持一致，这样可以避免代码提交后CI pipeline出现一些奇奇怪怪的问题，为了实现这一目标，我们采用：

* **容器化执行**：保证两者执行环境的配置是一样的
* **使用Makefile**：保证两者执行的命令是一样的

整个本地开发环境涉及到的开发工具和系统包括：

1. **Git**\
  本地代码版本控制工具，用来将pyzbs的代码克隆到本地 {code}`git clone "ssh://<your_username>@gerrit.smartx.com:29518/pyzbs"`

1. **Docker**\
  应用容器引擎，所有开发环境需要的工具都使用docker运行，不需要安装在开发机上, docker版本的最低版本要求如下(API version >= 1.41):

    ```
    [root@localhost pyzbs]# docker version
    Client: Docker Engine - Community
    Version:           20.10.10
    API version:       1.41
    Go version:        go1.16.9
    Git commit:        b485636
    Built:             Mon Oct 25 07:42:56 2021
    OS/Arch:           linux/amd64
    Context:           default
    Experimental:      true

    Server: Docker Engine - Community
    Engine:
      Version:          20.10.10
      API version:      1.41 (minimum version 1.12)
      Go version:       go1.16.9
      Git commit:       e2f740d
      Built:            Mon Oct 25 07:41:17 2021
      OS/Arch:          linux/amd64
      Experimental:     false
    containerd:
      Version:          1.4.11
      GitCommit:        5b46e404f6b9f661a205e28d59c982d3634148f8
    runc:
      Version:          1.0.2
      GitCommit:        v1.0.2-0-g52b36a2
    docker-init:
      Version:          0.19.0
      GitCommit:        de40ad0
    ```

1. **[Gerrit]**\
  代码版本控制和Review系统，所有的代码以patchset的形式提交到pyzbs的主代码库, 它是整理开发流程的核心系统，CI Job由它来触发，Jira issue也是由它来自动更新

1. **[Jira]**\
  开发任务管理系统，要求每个git commit都到对应 Project [ELF] 和 [TUNA] 下面的一个jira_issue或者subtask，jira issue需要手工创建，后续状态的更新由gerrit负责更新


### 详细设计

本地开发环境整体的结构如下图：

:::{graphviz}
  :caption: Git Work Flow
  :align: center

  digraph shells {
    size="7,8";
    node [fontsize=24, shape=plaintext];

    PC -> VM [style=dashed];
    VM -> Image;
    Image -> Container;
    Container -> NestedContainer;

    node [fontsize=20, shape=ellipse, style=filled, fillcolor=ivory];
    { rank=same;  PC VSCode Chrome; }

    node [fontsize=20, shape=ellipse, fillcolor=ivory];
    { rank=same;   VM Git PyzbsRepo Docker; }
    PyzbsRepo [shape=folder, fillcolor=seashell]

    node [fontsize=20, shape=cds];
    { rank=same;  Image "pyzbs-builder" "pyzbs-unittest" py2tools py3tools podrunner; }
    py2tools [fillcolor=palegreen];
    py3tools [fillcolor=palegreen];
    podrunner [fillcolor=wheat];
    "pyzbs-builder" [fillcolor=skyblue];
    "pyzbs-unittest" [fillcolor=wheat];

    node [fontsize=20, shape=record];
    { rank=same;  Container builder flake "rpm-build-amd64" venv;}
    builder [fillcolor=wheat, label="{{<f0> buildah|<f1> podman}|<f2>builder}"]
    venv [fillcolor=skyblue, label="{{<f0> buildah|<f1> podman}|<f2>venv}"]
    "rpm-build-amd64" [fillcolor=skyblue]
    black [fillcolor=palegreen]
    flake [fillcolor=palegreen]

    node [fontsize=20, shape=record];
    { rank=same;  NestedContainer "rpm-build-arm64" unittest; }
    "rpm-build-arm64" [fillcolor=skyblue]
    unittest [fillcolor=wheat, label="<f0> pyzbs|{{chunkd|iscsid|metad|nfsd|taskd}|{mongod|zookeeper}}"]

    Docker -> "pyzbs-builder" [label=build]
    Docker -> "pyzbs-unittest" [label=build]
    Docker -> py2tools [label=pull]
    Docker -> py3tools [label=pull]
    Docker -> podrunner [label=pull]
    py2tools -> flake [label=run]
    py3tools -> black [label=run]
    "pyzbs-builder" -> "rpm-build-amd64" [label=run]
    "pyzbs-unittest" -> unittest [label=loadImage]
    podrunner -> builder [label=run]
    podrunner -> venv [label=run]
    venv -> "pyzbs-builder" [label=buildWithQemu]
    venv -> "rpm-build-arm64" [label=runWithQemu]
    builder -> unittest [label=run]
    VSCode -> PyzbsRepo [label=ssh]
    Chrome -> Docker[label=view]
    Git -> PyzbsRepo [label=access]
    Git -> PyzbsRepo -> Docker [style=invis]
    "pyzbs-unittest" -> "podrunner" -> "pyzbs-builder" -> py3tools -> py2tools [style=invis]
    Docker -> PyzbsRepo [label=mount]
    unittest:f0 -> PyzbsRepo [label=mount]
  }
:::

其中包括：

* **一个 IDE**\
  IDE推荐使用vscode，它的Remote功能可以使用ssh直接打开和编辑远端开发机上的项目代码，也可以直接使用code-server在开发机上启动一个远程的IDE，然后通过浏览器访问这个IDE，使用体验和vscode基本一致。

* **一个浏览器**\
  用来查阅开发手册和测试报告，或者访问code-server启动的远程IDE

* **五个Docker Image**
  * py3tools: 封装了代码格式化工具black，从harbor上拉取
  * py2tools: 封装了静态检查工具flake8，从harbor上拉取
  * podrunner: 封装了podman和buildah，并设置docker的别名为podman，从harbor上拉取
  * pyzbs-builder: 封装打包pyzbs的rpm的工具，从dockerfile生成
  * pyzbs-unittest: 封装了单元测试和代码覆盖率统计的相关工具，用来建立单元测试的Pod，里面会运行单测使用的zbs和mongo服务，从dockerfile生成

* **一个VM**\
  本地开发环境对操作系统和内核要求如下：

  * 操作系统为Linux操作系统
  * 内核版本大于等于4.18，Centos 8和Debian满足条件，如果使用其它的Linux发行版需要自己升级一下内核
  * 必须安装有Git工具，用来下载代码和对代码进行版本控制
  * 必须安装有Docker，用创建本地开发环境使用到的builder容器和执行代码格式化和静态检查工具

  因为个人的笔记本一般使用Windows或者Mac OS系统，所以无法创建本地开发环境，所以我们需要一个VM来搭建本地开发环境，然后通过VSCode的Remote功能从笔记本上来访问开发环境，VM内部包括：

  * **builder容器**: 用来执行单元测试的pod
  * **venv容器**: 用来跨平台打包arm64版本的rpm  
  * **black容器**: 用来执行代码的格式化和格式检查
  * **flake容器**: 用来执行代码的静态检查
  * **rpm-build-amd64容器**: 打包amd64版本的rpm
  * **容器内的rpm-build-arm64容器**: 用来打包arm64版本的rpm
  * **容器内的unittest Pod**: 设置单元测试环境和执行单元测试，使用Kubernets的Pod资源文件定义，启动后内部的容器包括:
    * zbs-chund
    * zbs-iscsid
    * zbs-metad
    * mongod
    * zookeeper
    * zbs-nfsd
    * zbs-taskd
    * pytest: 执行单元测试和统计代码覆盖率

### Makefile定义

Makefile文件位于pyzbs repo的主目录下面，它用来自动化开发中的各种任务，因为makefile中的target不支持定义参数，只能通过环境变量来传递参数，所以使用target命令的格式为:
  ```
  make <target> [variable_name=value] [vairable_name=value]...
  ```

其中变量的名字和默认值定义在makefile中，常用的变量包括:

:::{list-table} Makefile Variable Definitions
  :widths: 30 70
  :header-rows: 1

  * * Name
    * Description

  * * LINE_LENGTH
    * 定义flake8和black静态代码检查时每行允许的最大字符个数，默认值是120

  * * CHECK_IGNORES
    * 定义静态代码检查时忽略的错误ID列表，ID之间用逗号分割

  * * UNITTEST_MODULES
    * 指定要执行单元测试的代码目录，使用相对路径

  * * MODULES
    * 指定要执行代码格式化和代码静态检查的代码目录，使用相对路径
:::

Makefile中定义的Target包括：

:::{list-table} Makefile Target Definitions
  :widths: 15 15 70
  :header-rows: 1

  * * Target
    * Trigger
    * Description

  * * docs
    * Manual/CI
    * 生成pyzbs项目的html格式的文档

  * * preview
    * Manual
    * 预览开发手册和测试报告，会在本地启动一个http server，通过浏览器访问http://<vm_ip>:8012就可以查看开发手册和测试报告

  * * format
    * Manual/CI
    * 使用black的风格格式化代码

  * * check
    * Manual/CI
    * 使用flake8对代码进行静态检查

  * * rpms_amd64 rpms_amd64_oem_inspur rpms_amd64_oem_anyvm
    * Manual/CI
    * 使用当前目录的代码打包amd64版本的rpm

  * * rpms_arm64 rpms_arm64_oem_inspur rpms_arm64_oem_anyvm
    * Manual/CI
    * 使用当前目录的代码打包arm64版本的rpm

  * * unittests
    * Manual/CI
    * 执行单元测试，可以通过modules变量设置需要测试的模块目录，目录之间用空格隔开

  * * debug_unittests
    * Manual
    * 进入调试单元测试模式，可以用来调试单个的测试用例

  * * venv
    * Manual
    * 打开venvvenv容器内的shell窗口，主要打包arm64版本的rpm

  * * clear_docs
    * Manual
    * 删除已经生成html格式的手册和测试报告

  * * clear_uuid
    * Manual
    * 删除.zbs_uuid，下次build pyzbs_image的时候会强制更新所有zbs的rpm

  * * clear
    * Manual/CI
    * 删除生成的rpm和unitest image的压缩包
:::


### Dockerfile定义

Dockerfile文件位于pyzbs repo的主目录下面，它用来生成打包rpm和执行单元测试的docker image, 采用了multiple stage的格式，从一个dockerfile里面可以生成下面三个target:

* **builder**: 用来打包amd64版本的rpm
* **unittest**: 用来启动单元测试的pod环境，在里面执行单元测试
* **builder-arm64**: 用来打包arm64版本的rpm


## 本地开发环境使用手册

### 配置开发机VM

* 查看内核版本是否大于 4.18.0-193，否则需要升级到 4.18.0-193 之上的内核
* 安装git,make {code}`yum install git make`
* 安装docker
* 需要启用root账户，本地开发会使用root做后续的所有操作

满足以上条件的开发机 VM 镜像文件已经制作好，可以使用 [开发机VM创建Job] 在任意 ELF 物理集群上导入镜像文件创建开发机 VM。

虚拟机用户名密码：root/HC!r0cks

虚拟机需要关闭防火墙：`systemctl stop firewalld && systemctl disable firewalld`

### 下载源代码

  ```
  git clone "http://<gerrit_user>@gerrit.smartx.com/a/pyzbs"
  ```

然后打开pyzbs的目录，后面的操作都在pyzbs repo的目录进行操作:
  ```
  cd pyzbs
  ```

### 生成开发手册

  ```
  make docs
  ```

### 查看开发手册和测试报告

  ```
  make preview
  ```
使用浏览器打开 {code}`http://<vm_ip>:8012` 就可以查看开发手册和测试报告


### 创建本地开发分支

基于master分支的HEAD创建本地开发分支:
  ```
  git checkout -b ELF-xxx master
  ```

### 在开发分支修改和提交代码

本地代码提交和修改历史使用git工具进行管理，具体的操作请参考Git使用指南：[Git_manual]

### 代码格式化

格式化pyzbs的代码:
  ```
  make format
  ```

### 代码静态检查

检查pyzbs的代码:
  ```
  make check
  ```

### 执行单元测试

  ```
  make unittests
  ```

### 调试单元测试用例

  ```
  make debug_unittests
  ```
等单测环境构建完毕后，你会在终端里面看到下面的输出
  ```
  2021-11-02T11:17:00.639324744Z + test_cmd='python -m gevent.monkey /usr/bin/py.test -vx  --html=docs/html/report/pytest_report.html --self-contained-html --cov-report=xml:docs/html/report/coverage.xml --cov-report=html:docs/html/report/coverage --cov=. --log-level=DEBUG --log-cli-level=DEBUG -s'
  2021-11-02T11:17:00.639412614Z + echo 'Debug test with command: python -m gevent.monkey /usr/bin/py.test -vx  --html=docs/html/report/pytest_report.html --self-contained-html --cov-report=xml:docs/html/report/coverage.xml --cov-report=html:docs/html/report/coverage --cov=. --log-level=DEBUG --log-cli-level=DEBUG -s zbs/test/ schema_sugar/ job_center/ smartx_app/ zbs_deploy/ zbs_rest/ tuna/ disk_healthd/tests/ elfx/tests/ salmon/tests/ smartx_vmtools/tests common/tests/ salmon/exporter/tests/'
  2021-11-02T11:17:00.639461573Z Debug test with command: python -m gevent.monkey /usr/bin/py.test -vx  --html=docs/html/report/pytest_report.html --self-contained-html --cov-report=xml:docs/html/report/coverage.xml --cov-report=html:docs/html/report/coverage --cov=. --log-level=DEBUG --log-cli-level=DEBUG -s zbs/test/ schema_sugar/ job_center/ smartx_app/ zbs_deploy/ zbs_rest/ tuna/ disk_healthd/tests/ elfx/tests/ salmon/tests/ smartx_vmtools/tests common/tests/ salmon/exporter/tests/
  2021-11-02T11:17:00.639509061Z + sleep infinity
  + docker exec -ti unittest-gfcff3e381-1-32f339a372eb9027cbed64dab48d2bfd-pytest bash
  [root@unittest-gfcff3e381-1-32f339a372eb9027cbed64dab48d2bfd pyzbs]#
  ```
按照终端上提示的 ` Debug test with command` 就可以调试测试用例了，调试完成后使用 `exit` 命令退出调试模式

### 打包amd64版本RPM

```
make rpms_amd64
# 或者使用rpms_amd64的别名rpms
make rpms
```

### 打包arm64版本RPM

```
make venv
# execute following command inside venv shell
make rpms_arm64
```

### 提交gerrit patch

```
git review -t <topic_name> <branch>
```

### 执行清理动作

* 清理html docs: {code}`make clear_docs`
* 清理build image的uuid: {code}`make clear_uuid`
* 清理running的builder: {code}`make clear_builder`


## Demo新版本的改进

* **查看docker image大小和podman的版本**
  ```
  docker pull harbor.smartx.com/smtxauto/py2tools:v0.1.15
  docker pull harbor.smartx.com/smtxauto/py3tools:v0.1.15
  docker pull harbor.smartx.com/smtxauto/podrunner:v0.1.15
  docker pull harbor.smartx.com/smtxauto/smtxauto:v0.1.15
  docker images|grep v0.1.15
  docker run --rm -ti harbor.smartx.com/smtxauto/podrunner:v0.1.15 podman --version
  ```

* **自动清理容器**
  ```
  # first shell
  make venv
  # second shell
  docker ps
  ```

* **调试单测用例**
  ```
  make debug_unittests
  ```

* **打包AMD64 RPM**
  ```
  make clear rpms
  ```

* **打包ARM64 RPM**
  ```
  make venv
  # execute following command inside venv
  make clear rpms_arm64
  ```


[elf]: http://jira.smartx.com/projects/ELF/issues
[gerrit]: http://gerrit.smartx.com/
[gerrit_manual]: gerrit.rst
[git_manual]: git.rst
[github]: https://github.smartx.com/smartx/smtxauto
[harbor]: https://harbor.smartx.com/harbor/projects/31/repositories/smtxauto%2Fsmtxauto
[jenkins]: http://*************:8080/job/pyzbs-gerrit-changes/
[jira]: http://jira.smartx.com
[poetry]: https://github.com/python-poetry/poetry
[pullrequest]: pull_request
[slack]: https://app.slack.com/client/T04B2HF09/C8TQQNV4P
[tuna]: http://jira.smartx.com/projects/TUNA/issues
[嵌套环境创建手册]: https://docs.google.com/document/d/1MllZQZpsRwLKiq7v4XJgwF-8Cnn2MUNsUOcy5HviPX0/edit#
[开发机vm创建job]: http://elfci.smartx.com:8080/view/Nested_Cluster/job/prepare_vm/
