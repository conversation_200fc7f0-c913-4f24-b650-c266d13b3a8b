# CI平台使用指南


## CI平台简介

### Jenkins CI平台存在的问题

* **Jenkins slave 节点空间太小**\
    现在每个slave节点的空间是150G，但是jenkins调度job到节点的时候会将pyzbs的CI job尽可能的调度到同一个节点上，加上pyzbs单测image比较大，所以经常有几个节点出现空间不足导致CI失败的问题，所以需要经常清理释放slave节点上的空间

* **测试结果和报告查看不方便**\
    测试报告位于归档的目录中，需要打开不同的文件才能看到单测的结果和代码覆盖率结果

* **缺少对自动build文档的支持**\
    虽然本地开发环境支持构建html文档，但是CI pipeline不支持对于每个commit自动构建文档，jenkins也缺少相应的组件用来托管html文档

* **Jenkinsfile修改和调试不方便**\
    Jenkinsfile使用DSL编写，里面嵌套Groovy脚本，大家对两者都不熟悉，所以不太好调试和修改


### 新CI平台改进的地方

* **使用rook部署了存储服务**\
    Rook是k8s上的ceph部署工具，部署完成后能够提供三种存储类型的storageclass：
    * **块存储**：部署CI平台数据库服务和打包上传docker image的时候会用到
    * **文件存储**：作为CI执行的workspace，能够被多个pipeline task同时挂载访问
    * **对象存储**：用来保存CI平台和pipeline执行的日志，归档CI的workspace，测试报告，html文档和rpm包

* **记录测试结果和代码覆盖率结果到数据库中**\
    单测执行结束后自动解析生成的测试报告，将测试数据存储在数据库中，所以在CI事件详情页面可以方便查看所有的测试数据：
    * **单测执行的测试结果数据**
    * **单个commit的代码覆盖率**
    * **整个项目的代码覆盖率**

    因为这些数据数据已经被持久化到了数据库中，所以对比分析历史的数据也会比较方便

* **支持文档的自动构建和托管**\
    为了让大家更方便的编写和查看开发文档，对pyzbs的文档做了一些优化：
    * 将rst格式的文档转换成了markdown格式，更好满足了大家写文档的习惯
    * CI pipeline中加入了文档的构建步骤，对于只修改文档的commit会跳过单测的步骤
    * CI平台提供文档托管服务，可以方便在线查看文档

* **使用Python脚本编写pipeline**\
    新的pipeline使用airflow的dag文件开发，是一个完整的Python脚本，大家对Python都比较熟悉，所以查看和修改起来更加方便


### CI pipeline对比

* **旧版本的pipeline流程**

    :::{diagrams}
    from diagrams import Cluster, Edge
    from diagrams.gcp.devtools import Tasks
    from sphinx_diagrams import SphinxDiagram

    with SphinxDiagram(
        title="Jenkins CI Pipeline",
        graph_attr={"fontsize": "36"},
        node_attr={"fontsize": "18", "labelloc": "t"},
    ):
        with Cluster(graph_attr={"style": "invis"}):
            group1 = Tasks("Clear") >> Tasks("Prepare") >> Tasks("Code_Check") >> Tasks("Build_Normal_RPM")
        with Cluster(graph_attr={"style": "invis"}):
            group2 =  Tasks("Build_anyvm") << Tasks("Build_inspur") << Tasks("Build_OEM_RPM") << Tasks("Unittests") << Tasks("Upload_Normal_RPM")
        group1 >> group2
    :::


* **新版本的pipeline流程**

    :::{diagrams}
    from diagrams import Cluster, Edge
    from diagrams.gcp.devtools import Tasks
    from sphinx_diagrams import SphinxDiagram

    with SphinxDiagram(
        title="Airflow CI Pipeline",
        graph_attr={"fontsize": "36"},
        node_attr={"fontsize": "18", "labelloc": "t"},
    ):
        with Cluster(label="Release", graph_attr={"labelloc": "b", "fontsize": "24"}):
            Tasks("Prepare") >> Tasks("Check") >> [
                Tasks("rpms_amd64"), Tasks("rpms_amd64_oem_anyvm"), Tasks("rpms_amd64_oem_inspur"),
                Tasks("rpms_arm64"), Tasks("rpms_arm64_oem_anyvm"), Tasks("rpms_arm64_oem_inspur"),
            ] >> Tasks("upload_rpms") >> Tasks("docs") >> Tasks("archive")
        with Cluster(label="Merge", graph_attr={"labelloc": "b", "fontsize": "24"}):
            Tasks("Prepare") >> Tasks("Check") >> [
                Tasks("rpms_amd64"), Tasks("rpms_amd64_oem_anyvm"), Tasks("rpms_amd64_oem_inspur"),
            ] >> Tasks("upload_rpms") >> Tasks("docs") >> Tasks("archive")
        with Cluster(label="Change", graph_attr={"labelloc": "b", "fontsize": "24"}):
            Tasks("Prepare") >> Tasks("Check") >> Tasks("rpms_amd64") >> Tasks("unittests") >> Tasks("docs") >> Tasks("archive")
    :::

* **新版的优点**
    * **流程更清晰**\
        旧版的pipeline是顺序执行的，没有条件分支，根据CI出发场景的不同在执行过程中动态跳过一部分task，而新版的pipeline能够清楚的区分每种场景的执行流程，流程的查看和修改都更加容易

    * **功能更多**\
        旧版不支持构建html格式的文档，新版支持了完整文档构建和托管流程：
        * 每个patch提交的时候都会测试能够成功构建文档，文档的链接会展示在CI event的详情页面
        * 每个patch合并到分支的时候会构建文档并上传到文档托管服务，保证每个分支的文档能够自动更新
        * 每个release创建的时候会构建文档并上传到文档托管服务，保证每个release都会有自己的文档
        * 文档托管服务支持从浏览器在线查看文档

    * **能力更强**\
        旧版pipeline需要在arm类型的jenkins slave上打包arm版本的rpm，需要两个job来分别构建amd64和arm64的rpm，新版的pipeline支持跨平台打包rpm，可以直接在同一个job中完成所有类型rpm的打包


## 新版CI平台设计方案

### 整体设计思路

Pyzbs的CI平台一直使用的是Jenkins，经过了两个版本的演变：
* **单机版本**\
    这是最老的版本，在一个VM上部署Jenkins服务，CI直接调用shell脚本执行，测试报告是一个很大的consoel日志文件，查看起来特别费劲，而且受单机性能的限制，同时启动的CI job不能超过6个

* **集群版本**\
    这是现在使用的版本，还是使用的的Jenkins平台，主要改变是：
    * 使用master-slave模式部署jenkins，设置了20个slave节点，能够支持的更多的job同时运行
    * 采用了Jenkinsfile，能够分阶段查看执行日志
    * 将代码静态检查和代码覆盖率作为代码提交通过的检查项
    * 支持html格式的pytest的测试报告，更方便查看

虽然第二个版本CI平台有了很大的改进，但是还是存在第一个章节里面提到的那些问题，而且有时候会遇到莫名其妙的Jenkins问题，所以我们想自己开发和部署一个CI平台，主要目标是：
* **性能要好**\
    能够支持更多的job并行执行
* **功能要多**\
    除了支持基本的Gerrit和手动触发CI执行，还要能够集中查看测试报告和代码覆盖率，能够支持文档的自动化构建和托管
* **运维要方便**\
    能够更方便的对计算资源和存储资源进行扩容

为了实现上面的目标，我们从下面几个方面进行技术选型：
* **计算资源管理**\
    选择K8S集群来部署自动化平台，既可以方便计算节点的添加，又可以使用K8S生态圈里面的各种工具
* **存储资源管理**\
    选择Rook部署存储服务，既可以同时提供对象，块和文件存储，又可以方便的对存储进行扩容
* **网络资源管理**\
    平台里面使用了很多的docker image，使用docker registry的镜像加速缓存来节省网络带宽，另外在缓存前面部署了自动翻墙代理，能够加速第一次镜像的下载速度
* **CI pipeline管理和调度**\
    选择Airflow进行任务调度和管理，既可以实现pipeline流程的可视化，又可以二次开发自己的CI调度和管理系统，而且支持python编写pipeline脚本


### CI平台的整体结构

:::{diagrams}
from diagrams import Cluster, Edge, Node
from diagrams.aws.network import GlobalAccelerator, VPCTrafficMirroring
from diagrams.aws.storage import EBS, EFS, S3, SimpleStorageServiceS3BucketWithObjects
from diagrams.azure.identity import Users
from diagrams.azure.network import Connections
from diagrams.azure.devops import Pipelines, Repos
from diagrams.gcp.devtools import ContainerRegistry, Tasks
from diagrams.gcp.api import Endpoints
from diagrams.onprem.database import Postgresql
from diagrams.onprem.network import Nginx
from diagrams.onprem.workflow import Airflow
from diagrams.onprem.vcs import Git
from sphinx_diagrams import SphinxDiagram

class FlowEdge(Edge):
    def __init__(self, **kwargs):
        kwargs["constraint"] = "false"
        kwargs["weight"] = "0"
        kwargs["color"] = "green"
        super().__init__(**kwargs)

class InvisEdge(Edge):
    def __init__(self, **kwargs):
        kwargs["constraint"] = "false"
        kwargs["style"] = "invis"
        kwargs["color"] = "red"
        kwargs["weight"] = "10"
        super().__init__(**kwargs)

class ControlEdge(Edge):
    def __init__(self, **kwargs):
        kwargs["constraint"] = "true"
        kwargs["color"] = "blue"
        kwargs["style"] = "invis"
        kwargs["weight"] = "10"
        super().__init__(**kwargs)

with SphinxDiagram(title="Airflow CI Platform", direction="TB", graph_attr={"splines": "ortho"}):
    with Cluster("External Service"):
        gerrit = Git("Gerrit")
        jira = Endpoints("Jira")
        harbor = ContainerRegistry("Harbor")
        dockerhub = ContainerRegistry("Dockerhub")
        yum_repo = Repos("Yum Repo")
        dev = Users("Developer")

        dockerhub >> ControlEdge() >> gerrit >> ControlEdge() >> yum_repo >> ControlEdge() >> Node(style="invis")  >> ControlEdge() >> Node(style="invis")
        harbor >> ControlEdge() >> dev >> ControlEdge() >> jira
        harbor >> InvisEdge(minlen="2") >> dockerhub

    with Cluster("K3S"):
        with Cluster("web_service", graph_attr={"style": "invis"}):
            with Cluster("Nginx Ingress"):
                airflow_web = Nginx("Airflow Ingress")
                docs_web = Nginx("Docs Ingress")
                docs_web >> InvisEdge() >> airflow_web

            with Cluster("Docker Registry"):
                fgw = GlobalAccelerator("FGW")
                mirror = VPCTrafficMirroring("Mirror")
                mirror >> InvisEdge() >> fgw

        with Cluster("Airflow"):
            webserver = Airflow("Web")
            runner = Pipelines("CI Runner")
            auth = Airflow("User")
            with Cluster("CI Pipeline"):
                pipeline_check = Tasks("Static Check")
                pipeline_rpms = Tasks("Generate RPMs")
                pipeline_unittests = Tasks("Run Unittests")
                pipeline_docs = Tasks("Build Docs")
                pipeline_unittests >> InvisEdge() >> pipeline_docs >> InvisEdge() >> pipeline_check >> InvisEdge() >> pipeline_rpms

            auth >> InvisEdge() >> webserver >> InvisEdge() >> runner
            auth >> ControlEdge() >> pipeline_unittests

        with Cluster("data_service", graph_attr={"style": "invis"}):
            with Cluster("Postgres Operator"):
                psql = Postgresql("Postgresql")

            with Cluster("Minio"):
                docs = SimpleStorageServiceS3BucketWithObjects("Docs")
                logs = SimpleStorageServiceS3BucketWithObjects("Airflow Logs")
                archive = SimpleStorageServiceS3BucketWithObjects("CICD Archive")
                docs >> InvisEdge() >> logs >> InvisEdge() >> archive

        with Cluster("Rook Storage"):
            block = EBS("Block Storage")
            obj = EBS("Object Storage")
            fs = EBS("File System Storage")
            block >> InvisEdge(minlen="5") >> obj >> InvisEdge() >> fs

        docs_web >> ControlEdge() >> auth
        pipeline_unittests >> ControlEdge() >> psql >> ControlEdge() >> block

    runner >> InvisEdge(minlen="6") >> gerrit
:::

整个CI流程会涉及到CI平台和外部服务两部分，CI平台部署在K3S集群上面，主要的组件包括：
* **Rook**\
    部署Ceph存储服务，提供块，对象和文件存储的Storageclass
* **Minio**\
    Minio作为ceph的对象存储网关使用，可以提供对象存储内容浏览的功能，CI平台主要用到了三个bucket:
    * **CICD Archive**: 用于CI workspace的归档，里面存储测试报告，代码覆盖率报告和每个commit构建的文档
    * **Airflow Logs**: 对接airflow的日志，ci pipeline的最终被转换为airflow的dag执行，所以pipeline的详细执行日志也在这里
    * **Docs**: 对于构建好的文档，如果需要发布的话就上传到这里
* **Postgres Operator**\
    用来部署postgres数据库，数据除了存储Airflow的任务和调度数据外，CI事件的信息也存储在这里
* **Airflow**
    * **User**: Airflow的用户管理系统，我们配置了LDAP登陆和google账户的oauth登陆
    * **Webserver**: Airflow的web服务
    * **CI Runner**: 在Airflow上二次开发的CI管理和调度系统
    * **CI Pipeline**: 定义在pyzbs repo中的dag文件，由CI Runner负责调度和执行
* **Docker Registry**
    * **Mirrior**: 使用docker registry部署的镜像加速缓存
    * **FGW**: 自动翻墙代理
* **Nginx Ingress**
    * **Airflow Ingress**: 代理了airflow的web页面
    * **Docs Ingress**: 代理了Docs bucket，负责自动修改文档文件的content type，让用户能够在浏览器里面查看文档


### Gerrit触发的CI流程

:::{diagrams}
from diagrams import Cluster, Edge, Node
from diagrams.aws.network import GlobalAccelerator, VPCTrafficMirroring
from diagrams.aws.storage import EBS, EFS, S3, SimpleStorageServiceS3BucketWithObjects
from diagrams.azure.identity import Users
from diagrams.azure.network import Connections
from diagrams.azure.devops import Pipelines, Repos
from diagrams.gcp.devtools import ContainerRegistry, Tasks
from diagrams.gcp.api import Endpoints
from diagrams.onprem.database import Postgresql
from diagrams.onprem.network import Nginx
from diagrams.onprem.workflow import Airflow
from diagrams.onprem.vcs import Git
from sphinx_diagrams import SphinxDiagram

class FlowEdge(Edge):
    def __init__(self, **kwargs):
        kwargs["constraint"] = "false"
        kwargs["weight"] = "0"
        kwargs["color"] = "green"
        super().__init__(**kwargs)

class InvisEdge(Edge):
    def __init__(self, **kwargs):
        kwargs["constraint"] = "false"
        kwargs["style"] = "invis"
        kwargs["color"] = "red"
        kwargs["weight"] = "10"
        super().__init__(**kwargs)

class ControlEdge(Edge):
    def __init__(self, **kwargs):
        kwargs["constraint"] = "true"
        kwargs["color"] = "blue"
        kwargs["style"] = "invis"
        kwargs["weight"] = "10"
        super().__init__(**kwargs)

with SphinxDiagram(title="Airflow CI Platform", direction="TB", graph_attr={"splines": "ortho"}):
    with Cluster("External Service"):
        gerrit = Git("Gerrit")
        jira = Endpoints("Jira")
        harbor = ContainerRegistry("Harbor")
        dockerhub = ContainerRegistry("Dockerhub")
        yum_repo = Repos("Yum Repo")
        dev = Users("Developer")

        dockerhub >> ControlEdge() >> gerrit >> ControlEdge() >> yum_repo >> ControlEdge() >> Node(style="invis")  >> ControlEdge() >> Node(style="invis")
        harbor >> ControlEdge() >> dev >> ControlEdge() >> jira
        harbor >> InvisEdge(minlen="2") >> dockerhub

    with Cluster("K3S"):
        with Cluster("web_service", graph_attr={"style": "invis"}):
            with Cluster("Nginx Ingress"):
                airflow_web = Nginx("Airflow Ingress")
                docs_web = Nginx("Docs Ingress")
                docs_web >> InvisEdge() >> airflow_web

            with Cluster("Docker Registry"):
                fgw = GlobalAccelerator("FGW")
                mirror = VPCTrafficMirroring("Mirror")
                mirror >> InvisEdge() >> fgw

        with Cluster("Airflow"):
            webserver = Airflow("Web")
            runner = Pipelines("CI Runner")
            auth = Airflow("User")
            with Cluster("CI Pipeline"):
                pipeline_check = Tasks("Static Check")
                pipeline_rpms = Tasks("Generate RPMs")
                pipeline_unittests = Tasks("Run Unittests")
                pipeline_docs = Tasks("Build Docs")
                pipeline_unittests >> InvisEdge() >> pipeline_docs >> InvisEdge() >> pipeline_check >> InvisEdge() >> pipeline_rpms

            auth >> InvisEdge() >> webserver >> InvisEdge() >> runner
            auth >> ControlEdge() >> pipeline_unittests

        with Cluster("data_service", graph_attr={"style": "invis"}):
            with Cluster("Postgres Operator"):
                psql = Postgresql("Postgresql")

            with Cluster("Minio"):
                docs = SimpleStorageServiceS3BucketWithObjects("Docs")
                logs = SimpleStorageServiceS3BucketWithObjects("Airflow Logs")
                archive = SimpleStorageServiceS3BucketWithObjects("CICD Archive")
                docs >> InvisEdge() >> logs >> InvisEdge() >> archive

        with Cluster("Rook Storage"):
            block = EBS("Block Storage")
            obj = EBS("Object Storage")
            fs = EBS("File System Storage")
            block >> InvisEdge(minlen="5") >> obj >> InvisEdge() >> fs

        docs_web >> ControlEdge() >> auth
        pipeline_unittests >> ControlEdge() >> psql >> ControlEdge() >> block

    runner >> InvisEdge(minlen="6") >> gerrit

    # flows
    dev >>  FlowEdge(xlabel="commit") >> gerrit
    dev >> FlowEdge(xlabel="comment") >> gerrit
    runner >> FlowEdge(xlabel="event stream") >> gerrit
    runner >> FlowEdge(xlabel="pull") >> mirror
    mirror >> FlowEdge(xlabel="pull") >> harbor
    mirror >> FlowEdge(xlabel="proxy") >> fgw >> FlowEdge(xlabel="pull") >> dockerhub
    runner >> FlowEdge(xlabel="schedule") >> [pipeline_docs, pipeline_check, pipeline_unittests, pipeline_rpms]
    pipeline_rpms >> FlowEdge(xlabel="upload") >> yum_repo
    pipeline_docs >> FlowEdge(xlabel="upload") >> docs
    pipeline_unittests >> FlowEdge(xlabel="upload") >> archive
    pipeline_check >> FlowEdge(xlabel="write") >> logs
    pipeline_unittests >> FlowEdge(xlabel="update") >> psql
    pipeline_rpms >> FlowEdge(xlabel="upload") >> archive
    [docs, logs, archive] >> FlowEdge(xlabel="save") >> obj
    psql >> FlowEdge(xlabel="mount") >> block
    runner >> FlowEdge(xlabel="comment") >> gerrit >> FlowEdge(xlabel="state transition") >> jira
:::

1. 用户提交patch或者merge代码后生成gerrit的ci event
1. CI Runner通过ssh event stream监控gerrit，有新的事件发生时就会启动一个dagrun执行CI pipeline
1. 如果执行pipeline的时候需要拉取docker镜像，会优先从镜像加速缓存里面拉取，拉取外部镜像的时候会使用自动翻墙代理进行加速
1. pipeline执行的详细日志会被上传到Airflow Logs bucket里面
1. pipeline生成的rpm会被保存在workspace目录，对于merge和release触发的pipeline会将rpm上传到yum server
1. 执行unittests后测试报告和代码覆盖率数据会记录在postgresql里面，原始报告保存在workspace里面，如果commit里面只有文档文件的修改那么会直接跳过unitests的执行
1. pipeline会自动构建文档保存在workspace目录，对于merge和release触发的pipeline会将文档上传到Docs bucket
1. pipeline执行结束的时候会将workspace目录归档到 CICD Archive bucket里面
1. CI执行结束的时候CI Runner将CI执行结果记录到gerrit里面
1. gerrit根据CI的结果自动更新jira ticket的状态


### 手工触发的CI流程

:::{diagrams}
from diagrams import Cluster, Edge, Node
from diagrams.aws.network import GlobalAccelerator, VPCTrafficMirroring
from diagrams.aws.storage import EBS, EFS, S3, SimpleStorageServiceS3BucketWithObjects
from diagrams.azure.identity import Users
from diagrams.azure.network import Connections
from diagrams.azure.devops import Pipelines, Repos
from diagrams.gcp.devtools import ContainerRegistry, Tasks
from diagrams.gcp.api import Endpoints
from diagrams.onprem.database import Postgresql
from diagrams.onprem.network import Nginx
from diagrams.onprem.workflow import Airflow
from diagrams.onprem.vcs import Git
from sphinx_diagrams import SphinxDiagram

class FlowEdge(Edge):
    def __init__(self, **kwargs):
        kwargs["constraint"] = "false"
        kwargs["weight"] = "0"
        kwargs["color"] = "green"
        super().__init__(**kwargs)

class InvisEdge(Edge):
    def __init__(self, **kwargs):
        kwargs["constraint"] = "false"
        kwargs["style"] = "invis"
        kwargs["color"] = "red"
        kwargs["weight"] = "10"
        super().__init__(**kwargs)

class ControlEdge(Edge):
    def __init__(self, **kwargs):
        kwargs["constraint"] = "true"
        kwargs["color"] = "blue"
        kwargs["style"] = "invis"
        kwargs["weight"] = "10"
        super().__init__(**kwargs)

with SphinxDiagram(title="Airflow CI Platform", direction="TB", graph_attr={"splines": "ortho"}):
    with Cluster("External Service"):
        gerrit = Git("Gerrit")
        jira = Endpoints("Jira")
        dev = Users("Developer")
        harbor = ContainerRegistry("Harbor")
        dockerhub = ContainerRegistry("Dockerhub")
        yum_repo = Repos("Yum Repo")

        dockerhub >> ControlEdge() >> gerrit >> ControlEdge() >> yum_repo >> ControlEdge() >> Node(style="invis")  >> ControlEdge() >> Node(style="invis")
        harbor >> ControlEdge() >> dev >> ControlEdge() >> jira
        harbor >> InvisEdge(minlen="2") >> dockerhub

    with Cluster("K3S"):
        with Cluster("web_service", graph_attr={"style": "invis"}):
            with Cluster("Nginx Ingress"):
                airflow_web = Nginx("Airflow Ingress")
                docs_web = Nginx("Docs Ingress")
                docs_web >> InvisEdge() >> airflow_web

            with Cluster("Docker Registry"):
                fgw = GlobalAccelerator("FGW")
                mirror = VPCTrafficMirroring("Mirror")
                mirror >> InvisEdge() >> fgw

        with Cluster("Airflow"):
            webserver = Airflow("Web")
            runner = Pipelines("CI Runner")
            auth = Airflow("User")
            with Cluster("CI Pipeline"):
                pipeline_check = Tasks("Static Check")
                pipeline_rpms = Tasks("Generate RPMs")
                pipeline_unittests = Tasks("Run Unittests")
                pipeline_docs = Tasks("Build Docs")
                pipeline_unittests >> InvisEdge() >> pipeline_docs >> InvisEdge() >> pipeline_check >> InvisEdge() >> pipeline_rpms

            auth >> InvisEdge() >> webserver >> InvisEdge() >> runner
            auth >> ControlEdge() >> pipeline_unittests

        with Cluster("data_service", graph_attr={"style": "invis"}):
            with Cluster("Postgres Operator"):
                psql = Postgresql("Postgresql")

            with Cluster("Minio"):
                docs = SimpleStorageServiceS3BucketWithObjects("Docs")
                logs = SimpleStorageServiceS3BucketWithObjects("Airflow Logs")
                archive = SimpleStorageServiceS3BucketWithObjects("CICD Archive")
                docs >> InvisEdge() >> logs >> InvisEdge() >> archive

        with Cluster("Rook Storage"):
            block = EBS("Block Storage")
            obj = EBS("Object Storage")
            fs = EBS("File System Storage")
            block >> InvisEdge(minlen="5") >> obj >> InvisEdge() >> fs

        docs_web >> ControlEdge() >> auth
        pipeline_unittests >> ControlEdge() >> psql >> ControlEdge() >> block

    runner >> InvisEdge(minlen="6") >> gerrit

    # flows
    dev >>  FlowEdge(xlabel="retrigger") >> airflow_web
    airflow_web >> FlowEdge(xlabel="redirect") >> auth
    auth >> FlowEdge(xlabel="login") >> webserver
    webserver >> FlowEdge(xlabel="trigger") >> runner
    runner >> FlowEdge(xlabel="pull") >> mirror
    mirror >> FlowEdge(xlabel="pull") >> harbor
    mirror >> FlowEdge(xlabel="proxy") >> fgw >> FlowEdge(xlabel="pull") >> dockerhub
    runner >> FlowEdge(xlabel="schedule") >> [pipeline_docs, pipeline_check, pipeline_unittests, pipeline_rpms]
    pipeline_rpms >> FlowEdge(xlabel="upload") >> yum_repo
    pipeline_docs >> FlowEdge(xlabel="upload") >> docs
    pipeline_unittests >> FlowEdge(xlabel="upload") >> archive
    pipeline_check >> FlowEdge(xlabel="write") >> logs
    pipeline_unittests >> FlowEdge(xlabel="update") >> psql
    pipeline_rpms >> FlowEdge(xlabel="upload") >> archive
    [docs, logs, archive] >> FlowEdge(xlabel="save") >> obj
    psql >> FlowEdge(xlabel="mount") >> block
    runner >> FlowEdge(xlabel="comment") >> gerrit >> FlowEdge(xlabel="state transition") >> jira
:::

除了触发方式不一样，CI pipeline执行的流程和gerrit触发的流程相同:
1. 用户访问CI平台的web页面，使用LDAP账号进行登陆
1. 在CI事件详情页面retrigger pipeline
1. 剩余的流程和gerrit触发的场景相同


### 文档托管的流程


:::{diagrams}
from diagrams import Cluster, Edge, Node
from diagrams.aws.network import GlobalAccelerator, VPCTrafficMirroring
from diagrams.aws.storage import EBS, EFS, S3, SimpleStorageServiceS3BucketWithObjects
from diagrams.azure.identity import Users
from diagrams.azure.network import Connections
from diagrams.azure.devops import Pipelines, Repos
from diagrams.gcp.devtools import ContainerRegistry, Tasks
from diagrams.gcp.api import Endpoints
from diagrams.onprem.database import Postgresql
from diagrams.onprem.network import Nginx
from diagrams.onprem.workflow import Airflow
from diagrams.onprem.vcs import Git
from sphinx_diagrams import SphinxDiagram

class FlowEdge(Edge):
    def __init__(self, **kwargs):
        kwargs["constraint"] = "false"
        kwargs["weight"] = "0"
        kwargs["color"] = "green"
        super().__init__(**kwargs)

class InvisEdge(Edge):
    def __init__(self, **kwargs):
        kwargs["constraint"] = "false"
        kwargs["style"] = "invis"
        kwargs["color"] = "red"
        kwargs["weight"] = "10"
        super().__init__(**kwargs)

class ControlEdge(Edge):
    def __init__(self, **kwargs):
        kwargs["constraint"] = "true"
        kwargs["color"] = "blue"
        kwargs["style"] = "invis"
        kwargs["weight"] = "10"
        super().__init__(**kwargs)

with SphinxDiagram(title="Airflow CI Platform", direction="TB", graph_attr={"splines": "ortho"}):
    with Cluster("External Service"):
        gerrit = Git("Gerrit")
        jira = Endpoints("Jira")
        harbor = ContainerRegistry("Harbor")
        dockerhub = ContainerRegistry("Dockerhub")
        yum_repo = Repos("Yum Repo")
        dev = Users("Developer")

        dockerhub >> ControlEdge() >> gerrit >> ControlEdge() >> yum_repo >> ControlEdge() >> Node(style="invis")  >> ControlEdge() >> Node(style="invis")
        harbor >> ControlEdge() >> dev >> ControlEdge() >> jira
        harbor >> InvisEdge(minlen="2") >> dockerhub

    with Cluster("K3S"):
        with Cluster("web_service", graph_attr={"style": "invis"}):
            with Cluster("Nginx Ingress"):
                airflow_web = Nginx("Airflow Ingress")
                docs_web = Nginx("Docs Ingress")
                docs_web >> InvisEdge() >> airflow_web

            with Cluster("Docker Registry"):
                fgw = GlobalAccelerator("FGW")
                mirror = VPCTrafficMirroring("Mirror")
                mirror >> InvisEdge() >> fgw

        with Cluster("Airflow"):
            webserver = Airflow("Web")
            runner = Pipelines("CI Runner")
            auth = Airflow("User")
            with Cluster("CI Pipeline"):
                pipeline_check = Tasks("Static Check")
                pipeline_rpms = Tasks("Generate RPMs")
                pipeline_unittests = Tasks("Run Unittests")
                pipeline_docs = Tasks("Build Docs")
                pipeline_unittests >> InvisEdge() >> pipeline_docs >> InvisEdge() >> pipeline_check >> InvisEdge() >> pipeline_rpms

            auth >> InvisEdge() >> webserver >> InvisEdge() >> runner
            auth >> ControlEdge() >> pipeline_unittests

        with Cluster("data_service", graph_attr={"style": "invis"}):
            with Cluster("Postgres Operator"):
                psql = Postgresql("Postgresql")

            with Cluster("Minio"):
                docs = SimpleStorageServiceS3BucketWithObjects("Docs")
                logs = SimpleStorageServiceS3BucketWithObjects("Airflow Logs")
                archive = SimpleStorageServiceS3BucketWithObjects("CICD Archive")
                docs >> InvisEdge() >> logs >> InvisEdge() >> archive

        with Cluster("Rook Storage"):
            block = EBS("Block Storage")
            obj = EBS("Object Storage")
            fs = EBS("File System Storage")
            block >> InvisEdge(minlen="5") >> obj >> InvisEdge() >> fs

        docs_web >> ControlEdge() >> auth
        pipeline_unittests >> ControlEdge() >> psql >> ControlEdge() >> block

    runner >> InvisEdge(minlen="6") >> gerrit

    # flows
    runner >> FlowEdge(xlabel="event stream") >> gerrit
    runner >> FlowEdge(xlabel="pull") >> mirror
    mirror >> FlowEdge(xlabel="pull") >> harbor
    mirror >> FlowEdge(xlabel="proxy") >> fgw >> FlowEdge(xlabel="pull") >> dockerhub
    runner >> FlowEdge(xlabel="schedule") >> pipeline_docs
    pipeline_docs >> FlowEdge(xlabel="upload") >> docs
    docs >> FlowEdge(xlabel="save") >> obj
    docs_web >> FlowEdge(xlabel="browse") >> dev
    docs >> FlowEdge(xlabel="proxy_pass") >> docs_web
    docs_web >> FlowEdge(xlabel="read") >> obj
:::

每个CI pipeline都会自动构建文档，对于merge和relase的pipeline，构建的文档会上传到Docs bucket里面，这个bucket里面的文档由CI平台自动托管：
1. 文档构建的流程同上面的CI流程
1. 用户在浏览器中打开Docs Ingress代理的链接
1. Docs Ingress从Docks bucket里面读取文档，并修改文档的content type为合适的类型后返回给用户
    ```
    apiVersion: networking.k8s.io/v1
    kind: Ingress
    metadata:
    name: minio-buckets-ingress
    namespace: rook-ceph # namespace:cluster
    annotations:
        kubernetes.io/ingress.class: "nginx"
        nginx.ingress.kubernetes.io/upstream-vhost: $HOST
        nginx.ingress.kubernetes.io/proxy-redirect-from: off
        nginx.ingress.kubernetes.io/proxy-redirect-to: off
        nginx.ingress.kubernetes.io/proxy-http-version: 1.1
        nginx.ingress.kubernetes.io/proxy-body-size: 1024m
        nginx.ingress.kubernetes.io/use-regex: "true"
        nginx.ingress.kubernetes.io/configuration-snippet: |
        proxy_set_header Connection "";
        more_set_headers 'Content-Type: text/plain';
        if ($request_uri ~ ^(.*)\.(html|htm|php)(\?.*)?$) {
            more_set_headers 'Content-Type: text/html; charset=utf-8';
        }
        if ($request_uri ~ ^(.*)\.(js)$) {
            more_set_headers 'Content-Type: application/javascript; charset=utf-8';
        }
        if ($request_uri ~ ^(.*)\.(svg)$) {
            more_set_headers 'Content-Type: image/svg+xml';
        }
        if ($request_uri ~ ^(.*)\.(png)$) {
            more_set_headers 'Content-Type: image/png';
        }
        if ($request_uri ~ ^(.*)\.(jpeg)$) {
            more_set_headers 'Content-Type: image/jpeg';
        }
        if ($request_uri ~ ^(.*)\.(gif)$) {
            more_set_headers 'Content-Type: image/gif';
        }
        if ($request_uri ~ ^(.*)\.(css)$) {
            more_set_headers 'Content-Type: text/css';
        }
    spec:
    rules:
    - http:
        paths:
        - path: /(airflow-logs|cicd|docs)-bucket/
            pathType: Prefix
            backend:
            service:
                name: minio
                port:
                number: 80
    ```

### CI平台迁移流程

* **提交新的pipeline的代码修改到pyzbs repo**\
    这个patch有Jenkinsfile和Dag两种格式的pipeline文件，所以可以同时支持Jenkins和Airflow两种CI平台

* **禁用Jenkins CI平台**\
    修改jenkins job的配置，禁止扫描新的gerrit ci事件

* **启用新的Airflow CI平台**\
    修改gerrit上pyzbs的description，添加CI平台的url http://auto.smartx.com

* **回退到Jenkins CI平台**\
    如果新的CI平台不稳定的话，可以随时修改jenkins job的配置启用旧的Jenkins平台


## 新版CI平台使用手册

### 登陆CI平台
CI平台的地址为<http://auto.smartx.com>, 不登陆也可以查看pipeline的信息，如果要在CI平台上retrigger job，请使用LDAP账号登陆后再操作

### 配置CI平台(管理员)
主要是配置Connection和Variable变量, connections包括：
* gerrit: 用于扫描event stream和拉取代码
* yum_server: 用于上传rpm和更新repo的index

相关的Vairable变量包括：
* BASE_WEBHOOK_URL: 需要设置为http://auto.smartx.com
* CI_WORKSPACE: workspace的pvc信息
* YUM_REPO_REFRESH_INTERVAL: 设置更新yum server上的repo的周期
* YUM_REPOS: 设置周期性更新index的repo列表

配置完成后触发 `cicontroller` DAG来启动CI Runner

### 配置webhook(管理员)
因为gerrit没有像github那样是吃webhook，所以这里配置的不是真正意义上的webhook，CI平台会扫描gerrit的event stream，然后根据每个项目的描述中是否含有BASE_WEBHOOK_URL来判断这个repo是否启用了CI，所以这里的配置webhook是指将 http://auto.smartx.com 添加到项目描述里面

### 触发CI pipeline
触发CI有三种渠道：
* gerrit事件触发，patch创建/更新，merge到分支，以及创建release tag的时候都会自动触发CI pipeline
* gerrit comment触发，因为新的CI平台支持chatops，所以可以在gerrit上添加comment来触发，现在只支持`/retest` 命令，后面可以根据大家的需求支持更多chatops命令
* 手工触发，在CI事件的详情页面有一个retrigger按钮，这个仅支持重新触CI pipeline

### 查看CI pipeline
CI pipeline开始执行后会在gerrit上添加comment，里面包含CI事件页面的链接，页面中的`Execution Date`链接可以打开cirunner的dagrun页面，查看pipeline的链接可以在run_ci_pipeline这个task的详情里面找到

### 查看workspace归档
CI事件详情页面的 `Workspace Archive` 链接可以查看workspace的归档目录

### 查看测试报告
CI事件详情页面的 `Reports` 可以看到测试报告的总体数据，点击数据旁边的链接可以查看完整的测试报告，包括：
* 单测结果数据
* commit代码覆盖率
* 项目全部代码的代码覆盖率

### 查看html格式的文档
* 查看构建的文档：在CI详情页面的 `Reports` 里面可以找到构建的文档链接
* 查看发布的文档：在CI平台的Bookmarks页面可以找到已经发布的文档链接

### 重新触发CI pipeline
对于一个已经执行的pipeline，有两种方式可以重新触发执行：
* chatops触发：在gerrit上添加 `/retest` 的comment
* 手工触发: 在CI详情页面点击 `Event Id` 旁边的 `retrigger` 按钮


## Demo 新CI平台的改进

* **CI Pipeline流程**
    * 菜单路径: CI/CD -> Dags
    * Pipeline name: cirunner--gerrit.smartx.com--pyzbs--cidag_xxx.py

* **CI Event详细信息页面**
    * 菜单路径: CI/CD -> Events

* **文档托管服务**
    * 菜单路径: Bookmarks -> Pyzbs Document



[elf]: http://jira.smartx.com/projects/ELF/issues
[gerrit]: http://gerrit.smartx.com/
[gerrit_manual]: gerrit.rst
[git_manual]: git.rst
[github]: https://github.smartx.com/smartx/smtxauto
[harbor]: https://harbor.smartx.com/harbor/projects/31/repositories/smtxauto%2Fsmtxauto
[jenkins]: http://*************:8080/job/pyzbs-gerrit-changes/
[jira]: http://jira.smartx.com
[poetry]: https://github.com/python-poetry/poetry
[pullrequest]: pull_request
[slack]: https://app.slack.com/client/T04B2HF09/C8TQQNV4P
[tuna]: http://jira.smartx.com/projects/TUNA/issues
[嵌套环境创建手册]: https://docs.google.com/document/d/1MllZQZpsRwLKiq7v4XJgwF-8Cnn2MUNsUOcy5HviPX0/edit#
[开发机vm创建job]: http://elfci.smartx.com:8080/view/Nested_Cluster/job/prepare_vm/
