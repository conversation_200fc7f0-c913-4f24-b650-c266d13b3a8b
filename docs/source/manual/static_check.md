# 静态检查

## 静态检查的工具

* **[black stype](https://github.com/psf/black/blob/master/docs/the_black_code_style.md)**： a strict subset of PEP 8

* **flake8**：使用下面的插件进行代码静态检查
   * flake8-quotes = "^3.2.0"
   * flake8-import-order = "^0.18.1"
   * flake8-mutable = "^1.2.0"
   * flake8-pep3101 = "^1.3.0"
   * flake8-docstrings = "^1.5.0"
   * flake8-builtins = "^1.5.3"
   * flake8-blind-except = "^0.1.1"
   * flake8 = "^3.8.3"
   * pydocstyle = "3.0.0"
   * flake8-comprehensions = "1.4.1"


## 代码提交流程

加了代码静态检查后，pyzbs的代码提交流程修改如下：

1. make check 看看是否有不符合规范的项目

1. 如果make check提示需要格式化代码，使用make format格式化代码

1. 按照make check的提示修改代码

1. 走原来的提交流程


## 代码升级过程

直接对旧版本的pyzbs代码进行静态检查会发现下面这些问题（只列举了其中一部分问题）：
    
   * A001: variable "file" is shadowing a python builtin
   * A002: argument "bytes" is shadowing a python builtin
   * A003: class attribute "list" is shadowing a python builtin
   * C401: Unnecessary generator* rewrite as a set comprehension
   * C402: Unnecessary generator* rewrite as a dict comprehension
   * C403: Unnecessary list comprehension* rewrite as a set comprehension
   * C404: Unnecessary list comprehension* rewrite as a dict comprehension
   * C405: Unnecessary list literal* rewrite as a set literal
   * D200: One-line docstring should fit on one line with quotes
   * D208: Docstring is over-indented
   * D209: Multi-line docstring closing quotes should be on a separate line
   * D210: No whitespaces allowed surrounding docstring text
   * D301: Use r""" if any backslashes in a docstring
   * D302: Use u""" for Unicode docstrings
   * D401: First line should be in imperative mood; try rephrasing
   * D413: Missing blank line after last section
   * E101: indentation contains mixed spaces and tabs
   * E262: inline comment should start with '# '
   * E265: block comment should start with '# '
   * E266: too many leading '#' for block comment
   * E302: expected 2 blank lines, found 1
   * E402: module level import not at top of file
   * E501: line too long (609 > 120 characters)
   * E711: comparison to None should be 'if cond is None:'
   * E712: comparison to True should be 'if cond is True:' or 'if cond:'
   * E713: test for membership should be 'not in'
   * E721: do not compare types, use 'isinstance()'
   * E731: do not assign a lambda expression, use a def
   * E741: ambiguous variable name 'l'
   * F401: '.device.Device' imported but unused
   * F402: import 'uuid' from line 5 shadowed by loop variable
   * F403: 'from .utils import *' used; unable to detect undefined names
   * F405: 'pd_to_sd' may be undefined, or defined from star imports: .utils
   * F523: '...'.format(...) has unused arguments at position(s): 0
   * F632: use ==/!= to compare constant literals (str, bytes, int, float, tuple)
   * F811: redefinition of unused 'RsyncTask' from line 26
   * F812: list comprehension redefines 'v' from line 14
   * F821: undefined name 'TimeoutError'
   * F841: F841 local variable 'mocked_load_by_paths' is assigned to but never used
   * I100: Import statements are in the wrong order. 'from mock import patch' should be before 'import pytest'
   * I101: Imported names are in the wrong order. Should be KVM_VM, KVM_VM_TEMPLATE
   * I201: Missing newline between import groups. 'from contextlib import nested' is identified as Stdlib and 'import mock' is identified as Third Party.
   * I202: Additional newline in a group of imports.      'from zbs.lib.mongo.db import mongodb' is identified as Third Party and 'import pytest' is identified as Third Party

所以不能直接在CI pipeline里面加入代码静态检查的stage，需要先对代码进程升级，是先有的代码能够通过静态检查，然后在改变pipeline，下面主要介绍一些对代码的升级过程。


### 在本地master分支初始化代码升级的commit

1. 添加makefile然后生成 commit A

1. make format然后生成commit B

1. 根据make check的提示修改代码，然后生成commit C，主要修改的内容如下：
    ```
      1. fix E231 by removing unnecessary comma
      2. ignore other failed check points with inline comment
    ```

1. 修改的模块包括：
    ```
        modified folders:
              common
              docs
              grpc_client
              job_center
              log_collector
              salmon
              schema_sugar
              smartx_app
              tuna_product_customize
              smartx_vmtools
              tuna
              zbs: exclude zbs/deps/
              zbs_deploy
              zbs_rest
    ```

### Rebase master分支上新的提交到本地的master

1. checkout gerrit/master

1. cherry-pick makefile的commit A

1. make format然后生成commit B

1. 根据make check的提示修改代码，然后生成commit C，主要修改的内容如下：
    ```
      1. fix E231 by removing unnecessary comma
      2. ignore other failed check points with inline comment
    ```

### 提交升级代码

1. git review 提交升级的commit A/B/C

1. 在gerrit上merge commit A/B/C


### 修改代码的脚本和工具

在初始化升级代码和rebase master分支新的提交时候需要使用下面的工具对代码进行检查和修改

* dos2unix：用来将代码文件中所有的windows换行符转换为linux的换行符
    ```
    find . -type f -print0 | xargs -0 dos2unix
    ```

* 具体修改代码的过程如下：
    ```
    make check|python format_with_check_points.py
    make format  # 执行这两个命令直到不存在需要format的代码文件
    make check
    # 搜索所有代码，将出现在docstring内部的 E501 放到docstring结束的哪一行
    # 对于无法通过脚本解决的checkpoint手工处理一下
    ```
    
* 修改代码用到的脚本format_with_check_points.py的内容为:

    ```
    #!/usr/bin/evn python
    
    import logging
    import os.path
    import re
    import sys
    
    
    def fix_e231(src_lines, line_no):
        src_lines[line_no* 1] = src_lines[line_no* 1].replace(",]", "]").replace(",}", "}")
        return src_lines
    
    
    def ignore_docstring_check_points(src_lines, line_no, check_point_name):
        checked_line_index = None
        if src_lines[line_no* 1].count('"""') < 2:  # multiple line docstring
            for number in range(line_no, len(src_lines)):
                if src_lines[number].find('"""') >= 0:  # end line of docstring
                    checked_line_index = number
                    break
        elif src_lines[line_no* 1].count('"""') == 2:  # oneline docstring
            checked_line_index = line_no* 1
    
        if checked_line_index is not None:
            src_lines = ignore_other_check_points(src_lines, checked_line_index + 1, check_point_name)
        return src_lines
    
    
    def ignore_other_check_points(src_lines, line_no, check_point_name):
        line = src_lines[line_no* 1]
        if "# noqa:" not in line:
            line = line.replace("\n", "  # noqa: {0}\n".format(check_point_name))
        else:
            line = line.replace("\n", ",{0}\n".format(check_point_name))
        src_lines[line_no* 1] = line
        return src_lines
    
    
    def format_one_src_file(src_path, check_points):
        logging.info("Format {0} according to check result of flake8: {1}".format(src_path, check_points))
        if not check_points:
            return
    
        with open(src_path) as src_reader:
            src_lines = src_reader.readlines()
            for line_no, check_point_name in check_points:
                if check_point_name == "E231":
                    src_lines = fix_e231(src_lines, line_no)
                elif check_point_name.startswith("D"):
                    src_lines = ignore_docstring_check_points(src_lines, line_no, check_point_name)
                else:
                    src_lines = ignore_other_check_points(src_lines, line_no, check_point_name)
    
        with open(src_path, "w") as src_writer:
            src_writer.writelines(src_lines)
    
    
    def format_all_src_files(result):
        check_points = []
        last_src_path = None
        for line in result.splitlines():
            try:
                src_path, line_no, column_no, description = line.split(':')[:4]
                assert os.path.isfile(src_path), "Invalid source code file path {0}".format(src_path)
                check_point = description.strip().strip().partition(" ")[0]
                if last_src_path is None:
                    last_src_path = src_path
                if last_src_path != src_path:
                    format_one_src_file(last_src_path, check_points)
                    last_src_path = src_path
                    check_points = [(int(line_no), check_point)]
                else:
                    check_points.append((int(line_no), check_point))
            except ValueError:
                pass
            except AssertionError as excp:
                logging.warning(excp)
                continue
        if last_src_path:
            format_one_src_file(last_src_path, check_points)
    
    
    if __name__ == "__main__":
        logging.basicConfig(level=logging.INFO)
        if len(sys.argv) == 1:
            logging.info("Read flake8 check result from stdin")
            check_result = sys.stdin.read()
        else:
            logging.info("Read flake8 check result from {0}".format(sys.args[1]))
            with open(sys.argv[1]) as result_io:
                check_result = result_io.read()
        format_all_src_files(check_result)
        logging.info("Done!")
    ```
