# Git使用指南

SmartX的所有代码都使用Git进行版本控制，本文对Git的使用做一个简单的介绍，详细的介绍请参考 [Git手册]

## 介绍

Git是一个开源的分布式版本控制系统，工作原理和流程如下图所示, 更详细的信息可以参考 [Git工作区域] :

```{eval-rst}
.. graphviz::
  :caption: Git Work Flow
  :align: center

  digraph {
    graph [rankdir=LR, rank="same"]
    edge [style=solid]

    subgraph remote {
      remote_1 [shape="plaintext", label="" height=0]
      remote [label="Remote" shape="cylinder" width=1 height=1.5]

      remote_1 -> remote [style="invis" height=0]
    }

    subgraph repository {
      repository_1 [shape="plaintext", label="" height=0]
      repository [label="Repository" shape="cylinder" width=1 height=1.5]

      repository_1 -> repository [style="invis" height=0]
    }

    subgraph index {
      index_1 [shape="plaintext", label="" height=0]
      index [label="Index" shape="cylinder" width=1 height=0.5]

      index_1 -> index [style="invis" height=0]
    }

    subgraph workspace {
      workspace_1 [shape="plaintext", label="" height=0]
      workspace [label="Workspace" shape="folder" width=1 height=1.5]

      workspace_1 -> workspace [style="invis"]
    }

    remote_1 -> repository_1 -> index_1 -> workspace_1 [style="invis"]
    remote:n -> workspace:n [label="pull" weight=1 color="green"]
    remote:e -> repository:w [label="fetch/clone" weight=5 color="green"]
    repository:e -> workspace:w [label="checkout" weight=5 color="green"]
    workspace:sw -> index:e [label="add" weight=3 color="blue"]
    index:w -> repository:se [label="commit" weight=1 color="blue"]
    repository:sw -> remote:se [label="push" weight=1 color="blue"]
  }
```

其中：

- Workspace

  工作区，就是repo的目录，开发代码时会修改这个目录下的文件

- Index / Stage

  暂存区，用来临时存放文件改动的地方，它实际上是一个index文件( {code}`.git/index` ), 当一个文件被 {code}`git add` 的时候，修改的文件被被写入到对象库中，同时将这个对象的ID更新到index文件中

- Repository

  仓库区（或本地仓库），工作区所在的目录有个隐藏的目录 {code}`.git` ，这个目录不属于工作区，而是仓库区

- Remote

  远端仓库，指托管在因特网或其他网络中的你的项目的版本库，一个仓库可以有好几个远端仓库，远端仓库可以设置成只读或者可以读写

本地仓库中文件的修改流程如下：

1. 在工作目区中添加或者修改文件
2. 对于那些需要进行版本控制的文件，将它们放入暂存区
3. 将暂存区的文件提交到本地仓库

在这个修改流程中，一个文件有四种状态：

- Untracked

  未跟踪, 工作区中的文件还没有加入到git库，不被git版本控制，通过git add 状态变为Staged

- Unmodify

  文件已经被git版本控制, 但是在工作区内未被修改, 即版本库中的文件快照内容与工作区中的文件完全一致，如果它被修改, 而变为Modified，如果使用git rm移出版本库, 则成为Untracked文件

- Modified

  文件已经被git版本控制，而且工作区的文件文件已修改, 通过git add可进入staged状态, 使用git checkout 可以丢弃修改的内容，返回到unmodify状态, 实际上git checkout是从仓库中取出文件, 覆盖当前修改

- Staged

  暂存状态. 执行git commit则将修改同步到版本库中, 可以将文件变为Unmodify状态. 执行git reset HEAD filename可以取消暂存将文件状态变为Modified

## Git安装

- Linux:

  ```
  yum install git
  # or
  apt-get install git
  # or
  dnf install git-all
  ```

- Mac:

  ```
  brew install git
  ```

  或者从 <https://git-scm.com/download/mac> 下载安装文件安装

- Windows

  从 <https://git-scm.com/download/win> 下载安装文件安装

## Git配置

- 配置User:

  ```
  git config --global user.name "Yanzhi Zhang"  # change to your name
  git config --global user.email "<EMAIL>"  # change to your email
  ```

- 配置ssl:

  ```
  git config --global http.sslVerify false
  ```

- 配置彩色输出:

  ```
  git config color.ui true
  ```

- 查看当前git配置:

  ```
  git config --list
  ```

## 仓库初始化

有两种途径可以初始化一个仓库

- Init，将当前目录初始化为git仓库:

  ```
  git init
  ```

- Clone，克隆一个远端仓库为一个本地仓库，远端仓库既可以是当前机器上的其它仓库，也可以是其它服务器上的仓库:

  ```
  # on same server
  git clone /path/to/repository
  # on different server
  git clone username@host:/path/to/repository
  ```

## 远端仓库管理

管理远端仓库的名令有下面几个:

- 列出远端仓库:

  ```
  git remote -v
  ```

- 添加远端仓库:

  ```
  git remote add [remote repo alias] [remote repo url]
  ```

- 删除远端仓库:

  ```
  git remote remove [remote repo alias]
  ```

- 重命名远端仓库:

  ```
  git remote rename [old remote repo alias] [new remote repo alias]
  ```

## 代码管理

- 提交代码

  - 暂存修改的文件

    可以暂存单个修改的文件:

    ```
    git add <filename>
    ```

    也可以暂存所有修改的文件:

    ```
    git add *
    ```

  - 提交修改的文件到当前分支的HEAD, 提交消息规范请参考 [CommitMessage指南]

    ```
    git commit -m "代码提交信息"
    ```

- 放弃本地代码修改

  - 放弃单个文件修改:

    ```
    git checkout -- <filename>
    ```

  - 放弃本地所有代码修改, 这里假设使用别名为origin的远端仓库来覆盖本地的master分支:

    ```
    git fetch origin
    git reset --hard origin/master
    ```

- 推送修改到远端仓库, 如果没有配置远端仓库，可以参考上面的远端仓库管理章节的命令

  - 推送分支到别名是origin的远端仓库的相同分支:

    ```
    git push origin <branch_name>
    ```

  - 推送分支到别名是origin的远端仓库的其它分支:

    ```
    git push origin <local_branch_name>:<remote_branch_name>
    ```

## 分支管理

使用分支意味着可以把你的工作从开发主线上分离开来，以免影响开发主线，在你创建仓库的时候，{code}`master` 是“默认的”分支，我们一般在其他分支上进行开发，完成后再将它们合并到主分支上。

- 列出分支:

  ```
  git branch  # list local branches
  git branch -a # list local and remote branches
  ```

- 创建和切换分支:

  ```
  git branch <new_branch_name>  # create a new branch
  git checkout <branch_name>  # switch to the branch
  git checkout -b <branch_name>  # create and switch to the new branch
  ```

- 分支删除:

  ```
  git branch -d <branch_name>
  git branch -D <branch_name>  # force delete a branch
  ```

- 分支暂存, 将当前分支为提交的修改暂存起来:

  ```
  git stash
  git stash list  # list all stash status
  git stash pop  # resume saved stash and remove it
  git stash drop  # drop saved stash without resuming it
  ```

- 分支合并, 我们开发中主要使用的是Rebase和Cherry-Pick, 具体的信息请参考 [Git分支合并方法]

  - Fast-Forward

    如果待合并的分支在当前分支的下游，也就是说没有分叉时，会发生快速合并，从test分支切换到master分支，然后合并test分支。如果我们不想要快速合并，那么我们可以强制指定为非快速合并，只需加上--no-ff参数

  - Squash

    svn的在合并分支时采用的就是这种方式，squash会在当前分支新建一个提交节点

  - Rebase

    当要合并两个分叉的分支时，merge的方式是将待合入分支和当前分支不同的部分，在当前分支新建节点:

    ```
    git rebase <branch_name>
    ```

  - Cherry-Pick

    选择任意一个commit合入当前分支，其合入的不是分支而是提交节点:

    ```
    git cherry-pick -x <commit_id>
    ```

## 标签管理

Git 可以给仓库历史中的某一个提交打上标签，标签本质上是版本库的一个快照，它实际上就是指向某个 commit 的指针，和分支的区别是分支可以移动，标签不能移动，比较有代表性的使用场景是使用这个功能来标记发布结点

- 创建标签:

  ```
  git tag v1.0
  ```

- 列出标签:

  ```
  git tag
  ```

- 删除标签:

  ```
  git tag -d v1.0
  ```

- 推送标签到远端仓库:

  ```
  git push origin v1.0
  git push origin v1.0 --force  # force overwrite remote tag
  ```

[commitmessage指南]: https://chris.beams.io/posts/git-commit/
[git分支合并方法]: https://segmentfault.com/a/1190000010806125
[git工作区域]: https://www.cnblogs.com/qdhxhz/p/9757390.html
[git手册]: https://git-scm.com/book/zh/v2
