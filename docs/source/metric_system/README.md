# MetricSystem
------------

MetricSystem is consists of `Gazer`, `CoralFish` and `collectd`.

They all plays an important role in our metric system.

`Gazer` is the the cluster monitoring system, it executes cron check
task via job-center.It monitors cluster parameters, produces `Event`
that includes `critical` and `warning` Event in which occurs our cluster

## Summary

```
---> data flow

                    +-----------+      +----------+        +----------+
                    |           |      |          |        |          |
                    | collectd  |      | collectd |        | collectd |
                    |           |      |          |        |          |
                    +-----+-----+      +------+---+        +---+------+
                          |                   |                |
                          |                   |                |
                          | via HTTP          | via HTTP       | via HTTP
                          |                   |                |
                          |                   |                |
                          |                   |                |
                   +----------------------------------------------------+
                   |      |                   |                |        |
                   |   +--v---------+  +------v-----+   +------v------+ |
                   |   |            |  |            |   |             | |
                   |   | CoralFish  |  | CoralFish  |   |  CoralFish  | |
                   |   | API Server |  | API Server |   |  API Server | |
                   |   |            |  |            |   |             | |
                   |   +------+-----+  +------+-----+   +------+------+ |
                   |          |               |                |        |
                   |          |     +---------v--------+       |        |
+---------------+  |          |     |                  |       |        |
|               |  |          +----->  MongoDB which   <-------+        |
| Data from     |  |                |  stores metric   |                |
| specified api |  |  CoralFish     |      data        |                |
| or library.   |  |                |                  |                |
|               |  |                +------------------+                |
+--------+------+  |                                                    |
         |         +------------------------------+---------------------+
         |                                        |
         |                                        |Metric and status data via HTTP
         |                                        |
         |                                        |
         | +------------------------------------------------------------------------+
         | |                                      |                                 |
         | |   +--------------------+     +-------v-----------------------------+   |
         | |   |                    |     |                                     |   |
         +----->  customize gazers  |     | Metric Or Status gazer that fetch   |   |
           |   |  (hard encoded in  |     | data from CoralFish and check it.   |   |
           |   |  source file)      |     |                                     |   |
           |   +--------------------+     +-------------------------------------+   |
           |                                                                        |
           |            Gazer (runs check via job-center's cron task)               |
           |                                                                        |
           +------------------------------------------------------------------------+

```
CoralFish and Gazer is just `concept`, they works as plugins of job-center
(as cron job) or serves API via `zbs-rest-server`.

Data flows from collectd(with python plugin) to coral_fish, and then
`Gazer` will query the data automatically via coral_fish http api, check
if the data reach the threshold of our pre-defined profile stored in DB.

Other `CustomizeGazers` will get data by hard-encoded python code, and
also validate them, triggers event(warning or critical, etc).


### Collectd

#### Summary
+ Runs on every node

#### Detail
Write plugin in follow the example in collectd_support/plugins.
It will collect metric data then put them into coral_fish via http api.

**Note** : after add a plugin, you should change collectd config to turn
on the plugin, `collectd.conf` is in pyzbs project directory.

### CoralFish
#### Summary
+ Runs on every node
+ Run as a zbs_rest's plugin
+ write/read data through http api
+ Adaptor support, write adapter to handle data which match specified
filters.

#### Detail
It stores data, aggregate data by minute/hour.

You can query the api with filters(only collectd's data field name is
supported now).

The default `interval` is in minute, so query result is array consists
of `aggregated point by interval`.


### Gazer
#### Summary
+ Runs only on single node at any time.
+ Reads data from coral_fish api/system lib.
+ Create `event`(or `alert`) if the data reaches the threshold.
+ Works as plugin of job-center(cron check) and zbs_rest(query events,
expire events, etc)

#### Detail
Gazer is the monitoring system that read data from coral_fish and access
data by system libraries(in customize gazers).

In most cases, it create event and expire it automatically when necessary.
You can also create an event by hand to call the Event api.

