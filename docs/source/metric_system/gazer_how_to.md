# Gazer How To
------------

Gazer is a monitor service that watch changes on SmartX cluster.

# Summary
## Gazer Type
+ customize gazer: customized hard-encode `python script`.
flexible but hard-encoded.
+ profile-based-gazer: based on metric data and profile config.
Consists of `metric source` and `GazerProfile`.

# Customize Gazer
It is a normal python class that has "check" method.

`smartx_app.gazer.common.registry.register_customized_gazer` is the decorator
to register a customize gazer to registry.
```
@register_customized_gazer(
    "storage_pool_migrate_recover_status",
    "storage_pool_migrate_recover_status",
)
class Example(object):
    def check(self):
        # do your check then create event or expire event.
        return
```

All customize gazers are located in `smartx_app/gazer/common/customize_gazers/`,
If you want to add new file , please remember to import it in
`customize_gazers/__init__.py`, otherwise, it will not be registered to
registry because this file has not executed yet, the decorator will not
work.

Once registered, the gazer will be called in job-center at intervals.

**Note**: If you need create or expire an event(called alert in fisheye),
please follow the example in customize_gazers.


# ProfileBasedGazer

## Summary
Profile based gazer is an checker that automatically check specified
cluster indicator then trigger a event.

This type of gazers are consists of two parts:

+ Metric data collectd by collectd plugin via `coral fish` API
+ GazerProfile stored in mongodb.

## Write a gazer

### collectd plugin to write data

Now you could write a collectd plugin to define the data you need.

```
import collectd
import psutil
from collectd_supports.utils import get_hostname

host = None


def config(ObjConfiguration):
    pass


def init():
    global host
    host = get_hostname()


def dispatch_data(plugin_instance, values):
    data = collectd.Values(
        meta={'0': True},
        host=host,
        type='gauge',
        plugin='node_cpu_metric',
        plugin_instance=plugin_instance,
        type_instance="used_cpu_percentage",
        values=values,
    )
    data.dispatch()


def reader(input_data=None):
    # "meta" is the required workaround for this version of collectd.
    # Without this, write_http does not work.
    # Because of collectd's unknow feature/bug, we can not write multiple
    # field of values here(refers to collectd's official cpu plugin).
    dispatch_data("cpu_usage_summary", [psutil.cpu_percent() / 100.0, ])


collectd.register_config(config)
collectd.register_init(init)
collectd.register_read(reader)
```

Put it into `collectd_supports/plugins` and add it to `collectd.conf`(
follow the example in the config file.)

Now you can query the data in coral_fish with filters: 
```
plugin=node_cpu_metric
plugin_instance=plugin_instance
type_instance=used_cpu_percentage
select_fields=host
```

### Write a GazerProfile
Write a python module in `smartx_app/gazer/job_center/profiles/cpu`. 

This profile will query data from coral_fish and check if the cpu
 usage is greater than 0.9(90%).

```
from smartx_app.gazer.common.base import (
    GazerProfile,
    METRIC_GAZER,
    Rule,
    WARNING
)


cpu_percentage = GazerProfile(
    name="cpu-percentage", gazer_type=METRIC_GAZER,
    event_level=WARNING, mode='or',
    rules=[
        Rule(
            name="cpu_percentage",
            operator="greater",
            value=[0.9],
            filters={
                "type_instance": "used_cpu_percentage",
                "operator": "mean"
            },
            select_fields=["host"],
            duration=5,
        )
    ],
    event_msg="cpu_percentage"  # in MessageBox
)
```

Then import it in `smartx_app/gazer/cmd.py` and add it to `create_default`
function.

# Deploy

After your code committed and merged into master, go to an environment
and run `ocean-gazer re_init` to add the new profile to DB.

Now job-center will use new profile to run our check.

# FAQ

Q: How do I remove all of the events after upgrade the packages.
A: Use `ocean-gazer clear-events` to remove all old events.