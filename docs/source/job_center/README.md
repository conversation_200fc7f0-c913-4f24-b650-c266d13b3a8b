# Job Center
- Job Center

    Job center 其实是通过对分析资源的描述而生成对应操作的系统，里面的每一个Job 是一堆资源描述的集合，资源包含VM，Volume，VM Snapshot， Volume Snapshot，VM template等，此外，Job还能描述定时触发的固定任务，如定时收集节点信息，报警任务
    
- Job Center 组件
    
    Job Center里面的组件包含这几个: Leader， Follower， Splitter， Scheduler， Dependency， Hook
    
    - Leader 主要负责任务拆分，任务梳理，分发任务，监控Follower完成情况
    
    - Splitter 是Leader运行过程中调用到的组件，一个资源对应有一个splitter， 例如vm对应会有vm_splitter，任务主要负责针对vm这类资源进行分析并拆分需要的任务
    
    - Dependency 当任务被拆分出来后，会有一组任务，然后任务由Leader根据规定的Dependency进行排序，确定任务执行的先后次序
    
    - Follower 就是真正任务的执行者，例如创建VM，创建Volume等，是由Leader分配给对应的Follower，其要处理的事情应尽可能的小，以防止发生故障时的无法恢复
    
    - Scheduler 定时触发任务的调度器，当注册了相应任务后，当到达时间后，会自动触发消息发送给Leader，并指定任务内容，不需要Splitter动态分析。
    
    - Hook 用于执行针对Job Center启动或者关闭时候需要进行处理的任务，例如启动时候需要往数据库更新自己状态，关闭时候也往数据库更新自己状态
    
    
- Job Center 设计细节

    Job Center的设计之初是为了处理除ZBS外，大部分的异步任务，例如Elf计算平台管理，Gazer监控系统。所以程序需要保持高可用和高可靠。我们基于python Celery框架上二次开发，达到我们的分布式系统的需求，简单的描述是当某个或者部分节点损坏的时候，任务能由正常的节点接管并继续未完成的任务。

    为了达到节点损坏后我们需要发现并恢复或者接着运行，我们每一个子任务需要粒度尽可能的小，并且需要是能够等幂执行，例如一个子任务只创建VM，另外一个子任务是启动VM。

    支持插件的功能，当对应的插件安装后，启动Job Center能发现，并读入模块。目前支持的插件有3个，Elf计算平台，Gazer监控平台，Management系统管理模块。

    Leader和Follower都属于Job Center里面的运行单元，这些单元根据实际场景，赋予Leader还是Follower的身份并执行对应的任务，所以从Job Center角度来看，并不会预留位置给Leader，也不会给Follower。而这些运行单元，都是通过Gevent进行协程管理，所以原则上说，大量阻塞计算的场景，或者不能被patch然后通过Epoll管理的场景，都是不适合放在JC进行管理的。我们会给每个物理机器的JC设定500个运行单元，这并不是每次启动都预留500个位置，而是最多能同时处理500个。

    当任务被提交到JC后，JC会进行简单的互斥锁检查，当Job带有的resources的uuid已经被锁上，那么后来的其他的带有这个uuid的Job会被拒绝执行，直到Job完成并解锁这个uuid，才运行再次提交关于这个uuid的Job。例如我需要对其中一个VM进行开机处理，那么当处理中的时候，其他人提交一个关机或者删除VM的操作，都会被拒绝。直到VM启动成功，或者启动失败，浏览器更新VM到最新状态，才能根据当前最新的状态重新提交新的Job。

