# Job JSON
    {
      "ctime": 1474271958, # job 创建时间
      "description": "VM_CREATE",   # 用于前后端沟通行为
      "handler": "a33aa772-bec1-3619-8a50-449ee8764f39",  # job处理worker id
      "job_id": "d1ed64e9-6dcf-4143-bfa0-bd7ed8dba863",
      "life_cycle": "run",  # 生命周期，目前只是run
      "resource_group": {   # task topo will depend on resource group to add dependency
        "default": [  # default group id, if do not submit any group info, then all resources will under default
        ],
        "xxxxxx-xxxxxxxxx-xxxxxxxx1": [ # group id
          "7aea2282-f942-40fc-9fd8-976fef9b19b2",
          "98cc3c44-3cd0-43e2-89f9-9eb1dba11706"
        ]
      }
      }
      "resources": {     # 资源，用于前端传入的资源的目标状态描述，
                          # 和schedule_task, one_time_task互斥
        "7aea2282-f942-40fc-9fd8-976fef9b19b2": {
          "_unset": {
            "volume_uuid": "",
            "zbs_snapshot_uuid": ""
          },
          "create_time": 1474271959,
          "name": "vdisk-1",
          "path": "/usr/share/smartx/volumes/7aea2282-f942-40fc-9fd8-976fef9b19b2",
          "resource_state": "in-use",
          "size": 100,
          "status": "created",
          "type": "KVM_VOLUME",
          "uuid": "7aea2282-f942-40fc-9fd8-976fef9b19b2"
        },
        "98cc3c44-3cd0-43e2-89f9-9eb1dba11706": {
          "_unset": {
            "name": ""
          },
          "create_time": 1474271962,
          "description": "",
          "disks": [
            {
              "boot": 1,
              "bus": "virtio",
              "path": "/usr/share/smartx/volumes/7aea2282-f942-40fc-9fd8-976fef9b19b2",
              "type": "disk"
            }
          ],
          "ha": false,
          "memory": 4294967296,
          "nics": [
            {
              "mac_address": "52:54:00:f8:39:bc",
              "ovs": "ovsbr-mgt",
              "vlan_uuid": "c8a1e42d-e0f3-4d50-a190-53209a98f157",
              "vlans": [
                {
                  "vlan_id": 0
                }
              ]
            }
          ],
          "node_ip": "**********",
          "resource_state": "in-use",
          "status": "running",
          "type": "KVM_VM",
          "uuid": "98cc3c44-3cd0-43e2-89f9-9eb1dba11706",
          "vcpu": 1,
          "vm_name": "abc123"
        }
      },
      "schedule_task": {  # 定时任务，用于定时触发传入的任务的描述，
                           # 和resources, one_time_task互斥
          "hosts": [],  # 定时任务需要执行的机器，None则为集群任务
          "args": [
              {
                "gazer_type": "customized",
                "name": "node_storage_status",
                "uid": "node_storage_status"
              }
          ],
          "name": "Schedule.Task.Gazer.Rule.Check",
          # Considering the job number increasing like hell when
          # make a specical gazer match a job, so now collect all
          # all gazer dict into `kwargs` parameter and jostle into one job
          # and spilt few subtasks by spitter.
          # It's a host-only mission, so it will process in all hosts in a cluster,
          # so if `hosts` parmeter is not `[]`, this parmeter will not be used
          "kwargs":
              {
                "node_data_ip_ping_ttl": {
                  'gazer_type': 'customized',
                  'name': 'node_data_ip_ping_ttl',
                  'uid': 'node_data_ip_ping_ttl'
                },
                "ovs_bonding_nics_status_uid": {
                    'gazer_type': 'customized',
                    'name': 'ovs_bonding_nics_status',
                    'uid': 'ovs_bonding_nics_status_uid'
                }
              }
      }, 
      "one_time_task": {  # 单次任务，用于前端传入的单次非资源任务描述，
                            # 和schedule_task, resources互斥
        "b33aa772-bec1-3619-8a50-449ee8764f3a": {
            "hosts": [],  # 单次任务需要执行的机器，None则为集群任务
            "data": {},  # 单次任务执行所需信息
            "name": "Task.Partition.Mount"
        }
      }, 
      "state": "done",  # job 状态
      "task_list": [  # leader自动维护任务列表
        {
          "data": null,
          "follower_name": "Task.Kvm.Volume.Create",
          "handler": "a33aa772-bec1-3619-8a50-449ee8764f39",
          "msg": "",
          "queue": null,
          "reference": "7aea2282-f942-40fc-9fd8-976fef9b19b2",
          "state": "done",
          "time": 1474271959,
          "uuid": "cc6efeb2-eed9-4524-8842-85e0e6453f59"
        },
        {
          "data": null,
          "follower_name": "Task.VM.Create",
          "handler": "a33aa772-bec1-3619-8a50-449ee8764f39",
          "msg": "",
          "queue": "**********",
          "reference": "98cc3c44-3cd0-43e2-89f9-9eb1dba11706",
          "state": "done",
          "time": 1474271962,
          "uuid": "544d4f70-91b6-4901-be93-70a968299119"
        },
        {
          "data": null,
          "follower_name": "Task.VM.Start",
          "handler": "a33aa772-bec1-3619-8a50-449ee8764f39",
          "msg": "",
          "queue": "**********",
          "reference": "98cc3c44-3cd0-43e2-89f9-9eb1dba11706",
          "state": "done",
          "time": 1474271964,
          "uuid": "06f80748-1800-4a91-bb18-07dda5875873"
        }
      ],
      "task_topo": {
        "cc6efeb2-eed9-4524-8842-85e0e6453f59": [],
        "544d4f70-91b6-4901-be93-70a968299119": [
            "cc6efeb2-eed9-4524-8842-85e0e6453f59"
        ],
        "06f80748-1800-4a91-bb18-07dda5875873": [
            "544d4f70-91b6-4901-be93-70a968299119"
        ]
      }
      "type": "action",
      "user": ""
    }
