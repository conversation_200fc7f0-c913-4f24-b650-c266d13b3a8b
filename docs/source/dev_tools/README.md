# DevTools
--------

We have created some tools for debug and develop.

After installation of pyzbs, you can use these tools. 


## zbs-tool
```
usage: zbs-tool [-h] [-v] [-f F]
                {ptable,jtable,zk,service,mongo,cluster,cfg,meta,smart,kv} ...

positional arguments:
  {ptable,jtable,zk,service,mongo,cluster,cfg,meta,smart,kv}
                        sub-command help
    ptable              show/modify local extent partitions.
    jtable              show/modify local journal partitions.
    zk                  show/modify zookeeper cluster info configured on this
                        host.
    service             show/modify service info on zk.
    mongo               show/modify mongo cluster info configured on this
                        host.
    cluster             Show cluster nodes information
    cfg                 Set/load local cfg option
    meta                meta management cmd
    smart               smartctl tools
    kv                  insert oem information, other kv, etc.Only support
                        string value now.
```

## ocean-gazer
With `ocean-gazer check your-gazer-name --debug`, you can 
use pdb to debug the gazer's check workflow.

```
ocean-gazer
Usage: ocean-gazer [OPTIONS] COMMAND [ARGS]...

Options:
  --help  Show this message and exit.

Commands:
  check         Run on rule check by gazer name.
  clear-events  delete all existed events
  create_event  Create an event locally by hand, 20 is info,...
  init          create the default gazer profile
  list          list gazer profiles
  re_init       delete all gazer profile and create default
  reset         delete all gazer profile

```

## collect_evn_info
Use this script to collect node logs and dump database.

```
/usr/share/smartx/script/collect_env_info.sh
```