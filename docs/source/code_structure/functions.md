# Functions
PyZBS do works as glue, it makes rpc requests to ZBS, provides WebAPI to user, etc.
PyZBS contains following main parts:

* RPC Clients
* RPC Server
* Commandline tools
* Web Service
* Metric Service
* Alert&Monitoring Service

## RPC Clients

PyZBS communicates with ZBS via rpc requests with protobuffer.
All clients implements in PyZBS in `client.py`, etc.

### ZBS-PRC-CLIENT

* ZBS-META - meta service , include pool, volume management(high-level), etc
* ZBS-CHUNK - zbs chunk service, volume, block, disk, cache management(low-level), etc

## Commandline Tools

Almost all commandline tools starts with "zbs", just input "zbs" then
press your `TAB` key.
Almost all commandline tools do its work by rpc requests, the job exactly
done in rpc-servers.
All commandline tools entry-point can be found in `setup.py`.

* zbs-chunk - chunk service client
* zbs-meta - meta service client
* zbs-task - taskd service client
* zbs-rest - manage root user, pwd in zbs web ui
* zbs-network - manage vlan in kvm
* zbs-nfs - nfs tools for zbs
* zbs-license - license management
* zbs-node - node operation (disk check, node info collecting)
* zbs-tool - mongo/zk/oem tools, for OPS, etc
* zbs-perf - perfs query client
* zbs-cdp - CDP job management
* zbs-iscsi -iscsi releated
* zbs-nfs - nfs releated
* ocean-gazer - ocean gazer profile manager, events query
* job-center - job-center worker/scheduler command line tools
* smart-job - job-center job management tools
* zbs-deploy - some deployment related tools

Note: Some other tools not listed here.

## Web Service

Web service provides two parts of the service.
Our current ui works with these two service.

* zbs_rest - FishEye api implements here
* schema_sugar - zbs_rest's require, to generate cli, rest api document by json-schema

## Metric Service

Metric service now performs by `coral_fish`, it works in zbs_rest as http api.

It provides metric data's writing, reading, aggregating.

Exactly, status data also stored as metric data now.

## Alert&Monitoring Service

The service provides two parts of service:

* system data collecting - all in `collectd_supports`, the plugins works with collectd
* event trigger - named `ocean_gazer`, it checks data from `coral_fish` and then create events written to mongodb


##Special Unit - FishEye

FishEye contains three parts now:

* new web-ui(not in pyzbs now)
* new web-api(`zbs_rest`)
* monitoring&metric service(`coral_fish`, `ocean_gazer` and `collectd_supports`)
