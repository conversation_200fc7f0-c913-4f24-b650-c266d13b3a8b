## API REFINE GUIDE
目前的API规范主要参考之前的restful.md

之前的设计上面主要还是返回数据的格式没有被规范

### URL Style

```
# Resources
/api/v2/disks

# child resource
/api/v2/disks/<id>
/api/v2/cluster/summary

# api document
/doc/api/v2/disks
/doc/api/v2/cluster/summary

# global doc
/doc

# docs
/doc/api/v2

```

Schema Style

```
# for resources password
{
	"schema": {
		"create": {
			"help": "change user's password",
			"type": "object",
			"properties": {
				"usename": {"type": "string", "minLength":1},
				"password": {"type": "string", "minLength":1},
			},
			"required": ["old_password", "new_password", "repeat"]
		},
	},
	"resource": ["session", ""],
	"returns": {
	  "update": {
	    "type": "object",
	    "properties": {
	      "token": {"type": "string"}
	    }
	  }
	}
}
```
Now the unittest will be auto-generated(todo), only provides the test_data
and all the validation will be done.

### Format

* json
* `"snake_case"` fields
* pretty print with gzip
* return some meta, like `"type"` and `"link"`

Basic:

```
{
  "ec":"UOK",
  "data": {
    "this_is_the_data": "an_object"
  },  // this format will be defined in "returns" in schema.
  "error": {
    "msg": "",
    "detail": ""  // or an object, for example for form error, it will be
                  // an javascript object
  }
  ...
}
```

For form error, the error detail may like:
```
{
  "title": ["blank"],
  "sub_title": ["too_long"]
}
```

### Document 
The doc will be generate automatically.

### Nested resources

| Method | HTTP verb | Path |
| - | - | - |
| create | `POST` | `/parents/:parent_id/children` |
| list | `GET` | `/parents/:parent_id/children` |
| get | `GET` | `/children/:id` |
| update | `PUT`/`PATCH` | `/children/:id` |
| delete | `DELETE` | `/children/:id` |

好处是url会比较短。但有个要求就是子resource可以根据id独立定位，而不需要父resource的id协助。
