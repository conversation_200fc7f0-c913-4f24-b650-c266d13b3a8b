  
#Partition mount or umount

###/api/v2/volumes/management/partitions

##Mount one partition disk

	POST /api/v2/management/partitions

### Parameters


| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| data_ip    | String    |  Node data ip              |
| hostname    | String    |  Node hostname	                  |
| path    | String    |  partition disk path                               |

### Success Response

```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": {
        	"job_id": "xxxxx-xxxxxxx-xxxxxx-xxxxx"
        },
        "error": {}
   }
```


## Umount one partition disk

	DELETE /api/v2/management/partitions
	
### Parameters


| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| data_ip    | String    |  Node data ip              |
| hostname    | String    |  Node hostname	                  |
| path    | String    |  partition disk path                               |

### Success Response

```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": {
        	"job_id": "xxxxx-xxxxxxx-xxxxxx-xxxxx"
        },
        "error": {}
   }
```