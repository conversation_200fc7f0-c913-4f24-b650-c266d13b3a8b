  
#Cache mount or umount

###/api/v2/volumes/management/caches

##Mount one cache disk

	POST /api/v2/management/caches

### Parameters


| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| data_ip    | String    |  Node data ip              |
| hostname    | String    |  Node hostname	                  |
| path    | String    |  cache disk path                               |

### Success Response

```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": {
        	"job_id": "xxxxx-xxxxxxx-xxxxxx-xxxxx"
        },
        "error": {}
   }
```


## Umount one cache disk

	DELETE /api/v2/management/caches

### Parameters


| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| data_ip    | String    |  Node data ip              |
| hostname    | String    |  Node hostname	                  |
| path    | String    |  cache disk path                               |

### Success Response

```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": {
        	"job_id": "xxxxx-xxxxxxx-xxxxxx-xxxxx"
        },
        "error": {}
   }
```