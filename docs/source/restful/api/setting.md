## Cluster Setting

### Show

GET /api/v2/settings/cluster

#### Success Response

```json
{
  "cluster_name": "smartx",
}
```

### Update

PUT /v2/settings/cluster

Params:
  - cluster_name

```
org-mode
|--------------+------------------------------+--------------+----------|
| name         | type                         | description  | required |
|--------------+------------------------------+--------------+----------|
| cluster_name | String                       | cluster name | True     |
|--------------+------------------------------+--------------+----------|
```

#### Success Response

```json
{
  "count": 1
}
```


## NTP Setting

### Show

GET /api/v2/settings/ntp

#### Success Response

```json
{
  "ntp_mode": "external",
  "ntp_servers": ["0.centos.pool.ntp.org", "1.centos.pool.ntp.org"]
}
```

### Update

PUT /v2/settings/ntp

Params:
  - ntp_mode
  - ntp_servers

```
org-mode
|--------------+------------------------------+--------------+----------|
| name         | type                         | description  | required |
|--------------+------------------------------+--------------+----------|
| ntp_mode     | Enum("internal", "external") | NTP mode     | True     |
| ntp_servers  | Array                        | NTP servers  | False    |
|--------------+------------------------------+--------------+----------|
```

#### Success Response

```json
{
  "job_id": "2171ea54-9d03-4a11-ae0d-173d0fbaf8ac"
}
```


## DNS Setting

### Show

GET /api/v2/settings/dns

#### Success Response

```json
{
  "dns_servers": ["***************", "*******"]
}
```

### Update

PUT /v2/settings/ntp

Params:
  - ntp_mode
  - ntp_servers

```
org-mode
|--------------+------------------------------+--------------+----------|
| name         | type                         | description  | required |
|--------------+------------------------------+--------------+----------|
| dns_servers  | Array                        | DNS servers  | True     |
|--------------+------------------------------+--------------+----------|
```

#### Success Response

```json
{
  "job_id": "f87bd5d1-b6dc-41e7-aaaa-bb100e79f11c"
}
```
