# Metric
ZBS metric service
###/api/v2/metric

CURL example:

```
curl -i -X GET http://localhost:10402/api/v2/metric?\
as_status=false&end_timestamp=1449109911&interval=1\
&locale=zh_cn&plugin=cluster_performance&\
select_fields=type_instance&start_timestamp=1449102711

```

### Success Response

#### Unit

+ IOPS: N times per second
+ Laterncy: ns
+ Bandwidth: Bps



```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": [
            {
                "data": [
                    [
                        39784450.0
                    ],
                    [
                        38906800.0
                    ],
                    [
                        37564550.0
                    ],
                    [
                        37614950.0
                    ],
                    [
                        41651000.0
                    ]
                ],
                "type_instance": "cluster_perf_bandwidth"
            }
        ],
        "error": {}
   }

```
## List metric

    GET /api/v2/metric
