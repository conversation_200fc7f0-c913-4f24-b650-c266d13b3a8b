# Coral Fish data format
Coral Fish receives data format just like what collectd sends.

Example listed below:

```
schema = {
            u'dstypes': [u'gauge'],  # collectd's inner field, please use this value or just ignore
                                     # With non-collectd environment, use what you want
            u'plugin': u'memory',    # collectd's plugin name
            u'dsnames': [u'value'],  # Data sheet names for "values" field
            u'interval': 10.0,       # The interval of data been collected
            u'host': u'ubuntu',      # Host name/ip the data produces or target host of this value
            u'values': [219128000.0],# Value list, maps to "dsnames"; "dsnames" and "values" should
                                     # always have the same length.
                                     # Note: This value now can only be an number
            u'time': 1438149831.902, # Data's ctime
            u'plugin_instance': u'',
            u'type_instance': u'free',
            u'type': u'memory'
        }
```

## Write to coral_fish
Send a `POST` request to `http://host:port/in`.The data should be a list of data-dict.
An example listed below:

```
[
    {
       "dsnames": [''],
       ...
    }
    ...
]
```

Note: When you send a request to coral_fish, "time" field in data
will be treated as localtime and will be converted to utc time.

If you give a "is_utc=true" query string(`http://host:port/in?is_utc=true`)
the convention will not be done.

## READ from coral_fish
send a get request to coral_fish:

```
GET http://host:port/api/v2/metric?type_instance=memory
GET http://host:port/api/v2/monitor?type_instance=memory
```

Query args you can use:

### Filters
Filters for data:
+ type
+ type_instance
+ plugin
+ plugin_instance
+ host
+ start_timestamp: utc timestamp
+ end_timestamp: utc timestamp

Note: If "as_status" option not given, start_timestamp and end_timestamp is required.

### Extra Options
These option affects the query_result

+ as_status: for /metric, default is false, return time-series data;The /monitor is on the contrary
+ operator: operator to handler the time-series data result, only works
for "as_status=false", you can choose it from "sum", "mean", "max"
+ select_field: the filed appears in query_result, just like SQL's select but supports only one field