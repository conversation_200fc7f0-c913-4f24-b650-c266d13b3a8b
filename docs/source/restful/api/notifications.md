# Notification(Old)
list all not expired zbs notification including warning, critical, info
###/api/v2/notifications

CURL example:

```
   curl -i -X GET http://localhost:10402/api/v2/notifications
```

### Success Response

```
   HTTP/1.1 200 OK
   {
       "ec": "UOK",
       "data": {
           "30": [
           {
           		# 触发这个事件的gazer名字
                "gazer": gazer,  
                # 触发这个事件所对应的gazer的uid
                "gazer_uid": "gazer's uid",  
                # 事件等级（信息，注意，警告）
                "level": level,  
                # 导致事件的data, 包含三个内容，all_data, 
                # raw_data, processed_data,分别是
                # 所有数据（这个gazer所检查的所有数据），
                # 触发rule的原始数据（用于渲染帮助信息等）， 
                # 传给checker的处理后数据
                "data": data,    
                "message": message,
                "help_message": help_message,
                "ctime": now,
                # 事件更新时间，用来维护事件流
                "mtime": now,       
                # 事件是否过期，用来维护事件流
                "expired": False,   
           }
           ],
           "20": [],
           "50": [],
       },
       "error": {}
   }
```

## list zbs notifications

    GET /api/v2/notifications
    
    
    
-------------------------------------------------------

# New Event API(Same as old notifications)

###/api/v2/gazer/events

CURL example:

```
   curl -i -X GET http://localhost:10402/api/v2/gazer/events
```

### Success Response


```
   HTTP/1.1 200 OK
   {
       "ec": "UOK",
       "data": {
           "30": [
           {
           		# 触发这个事件的gazer名字
                "gazer": gazer,  
                # 触发这个事件所对应的gazer的uid
                "gazer_uid": "gazer's uid",  
                # 事件等级（信息，注意，警告）
                "level": level,  
                # 导致事件的data, 包含三个内容，all_data, 
                # raw_data, processed_data,分别是
                # 所有数据（这个gazer所检查的所有数据），
                # 触发rule的原始数据（用于渲染帮助信息等）， 
                # 传给checker的处理后数据
                "data": data,    
                "message": message,
                "help_message": help_message,
                "ctime": now,
                # 事件更新时间，用来维护事件流
                "mtime": now,       
                # 事件是否过期，用来维护事件流
                "expired": False,   
           }
           ],
           "20": [],
           "50": [],
       },
       "error": {}
   }
```

## Create event by hand

    POST /api/v2/gazer/events

### Parameters
Use all following parameters in your http data-body (as json/application)

Required:

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| gazer    | String    |  gazer_name, use any string(do not overwrite name of internal gazers)    |
| gazer_uid    | String    |  gazer_uid, use any string(do not overwrite uuid of internal gazers)   |
| level    | integer    |  event level, choose from 30(warnning) and 50(critical)    |

Optional:

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| topic    | String    |  use any string that make sense    |
| message    | String    |   use any string that make sense   |
| help_message    | String    |  user's how to, use any string that make sense    |


Data Example:

```
{
  "gazer": "mygazer",
  "gazer_uid": "my-gazer-uid",
  "level": 30
}
```

### Success Response
```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": {},
        "error": {}
   }
```

### Use commandline to create an event.

2.3.0 rest-cmd
```
smartx gazer events create test_gazer hello_i_am_id 30 --topic test --message "Opps I'm Warnning Message"

usage: smartx gazer events create [-h] [--topic TOPIC] [--message MESSAGE]
                                  [--help_message HELP_MESSAGE]
                                  gazer gazer_uid level

positional arguments:
  gazer                 {u'minLength': 1, u'type': u'string'}
  gazer_uid             {u'minLength': 1, u'type': u'string'}
  level                 {u'enum': [30, 50], u'type': u'integer'}

optional arguments:
  -h, --help            show this help message and exit
  --topic TOPIC         {u'type': u'string'}
  --message MESSAGE     {u'type': u'string'}
  --help_message HELP_MESSAGE
                        {u'type': u'string'}

```

2.2.3 cmd
```
ocean-gazer create_event 30 test_gazer hello_i_am_id

Usage: ocean-gazer create_event [OPTIONS] LEVEL GAZER GAZER_UID

  Create an event locally by hand, 30 is warnning, 50 is critical

Options:
  --topic TEXT
  --message TEXT
  --help_message TEXT
```


## Update an event as "expired" or "unexpired"

    PUT /api/v2/gazer/events/<gazer_uid>

### Parameters
Use all following parameters in your http data-body (as json/application)

Required:

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| expired    | Boolean    |  True and False in json format    |


Data Example:

```
{
  "expired": true,
}
```

### Success Response
```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": {},
        "error": {}
   }
```
