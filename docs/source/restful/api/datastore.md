# Datastore
zbs datastore related operations 
###/api/v2/datastores

CURL example:

```
   curl -i -X POST http://localhost:10402/api/v2/datastores
        -H 'Content-Type: application/json' \
        -d '{"name": "test_datastore","replica_num": 2, \
        	"description":"", "thin_provision": true}'

```

### Success Response

```
   HTTP/1.1 200 OK
   {
       "ec": "UOK",
       "data": {},
       "error": {}
   }

```
## Create a zbs datastore

    POST /api/v2/datastores

### Parameters

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| name    | String    |  Name of datastore.		              |
| replica_num    | Integer    |  Number of replication		              |
| description    | String    |  Description of datastore		              |
| thin_provision    | Boolean    |  Datastore thin provision		              |

## Show a datastore

    GET /api/v2/datastores/:datastore_name

### Success Response
```
   HTTP/1.1 200 OK
   {
       "ec": "UOK",
       "data": {
           'created_time': {
               'nseconds': 822976975L,
               'seconds': 1447998855L
           },
           'description': u'',
           'id': 3,
           'name': u'xen',
           'nfs_export': True,
           'replica_num': 3,
           'thin_provision': True,
           'user': u''
       },
       "error": {}
   }

```

## List all datastore

    GET /api/v2/datastores

### Success Response
```
   HTTP/1.1 200 OK
   {
       "ec": "UOK",
       "data":[
           {
               'created_time': {
                   'nseconds': 822976975L,
                   'seconds': 1447998855L
               },
               'description': u'',
               'id': 3,
               'name': u'xen',
               'nfs_export': True,
               'replica_num': 3,
               'thin_provision': True,
               'user': u''
           }
       ],
       "error": {}
   }

```

## Update a datastore

    PUT /api/v2/datastores/:datastore_name

### Parameters

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| name    | String    |  Name of datastore.		              |
| replica_num    | Integer    |  Number of replication		              |
| description    | String    |  Description of datastore		              |
| thin_provision    | Boolean    |  Datastore thin provision

## Delete a datastore

    DELETE /api/v2/datastores/:datastore_name