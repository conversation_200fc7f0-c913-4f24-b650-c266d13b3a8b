# Management & Deployment
Management and deploy related api, including access host hardware info and deploy host

## API List


### common api
| Operation                                  |     HTTP Request                            |
| ----                                       |      -----------                            |
| [cluster info](#get-cluster-info)          | GET  /api/v2/management/cluster    |
| [list cluster hosts](#list-cluster-hosts)  | GET  /api/v2/management/hosts              |
| [add host](#add-host)                      | POST /api/v2/management/hosts              |
| [host info](#get-host-info)                | GET /api/v2/management/hosts/:host_id            |

### management api
| Operation                           |     HTTP Request                            |
| ----                                |      -----------                            |
| [mount disk](#mount-disk)           | POST /api/v2/management/hosts/:host_id/mount\_disk             |
| [unmount disk](#unmount-disk)       | POST /api/v2/management/hosts/:host_id/unmount\_disk       |
| [hosts info csv](#hosts-info-csv)       | POST /api/v2/management/hosts/export.csv       |
| [disks info csv](#disks-info-csv)       | POST /api/v2/management/hosts/disks.csv       |


## API Detail

### <a name="get-cluster-info"></a> Get Cluster Info

Access cluster information

#### HTTP request
      GET /api/v2/management/cluster
      
#### Success Response
```
{
   "ec": "UOK",
   	"data": {
		"name": {string},
  		"total_hosts": {integer},
  		"running_hosts": {integer},
  		"halt_hosts": {integer},	
  		"cpu_count": {integer},
		"memory": {string},
		"sata_disk_size": {string},
		"ssd_disk_size": {string}	 
	} ,
	"error": {} 
}
```

-----------

### <a name="list-cluster-hosts"></a> List Cluster Hosts

Access all hosts in cluster

#### HTTP request
      GET /api/v2/management/hosts

#### Query parameter
| Parameter Name    | Type      | Optional  | Description    |
| ------    | ----      | ----  | ----      |
| page      |  interger    |    Y   |     |
| per_page  |  interger    |    Y   |     |

#### Success Response
```
{
   "ec": "UOK",
   	"data": {
		"total": {integer},
		"page": {integer},
		"hosts": [
			{
     			"id": {integer},
     			"hypervisor_ip": {string},
     			"hostname": {string},
     			"tags": {array},
     			"type": {string},
     			"ipmi_ip": {string},
     			"ifaces": {array[Iface]},
     			"disks": {array[Disk]},
     			"cpu": {CPU},
     			"memory": {string},
     			"status": {string},    
			}
		],
	},
	"error": {} 
}
```
-------------


### <a name="hosts-info-csv"></a> Get Cluster Hosts CSV

Access cluster hosts information

#### HTTP request
      GET /api/v2/management/hosts/export.csv
      
#### Success Response
CSV file.

-----------

### <a name="disks-info-csv"></a> Get Cluster Disks CSV

Access cluster disks information

#### HTTP request
      GET /api/v2/management/hosts/disks.csv
      
#### Success Response
CSV file.

-----------

### <a name="add-host"></a> Add Host To Cluster

Add a host to cluster

#### HTTP request
      POST /api/v2/management/hosts
      
#### Body parameter
```
{
     "hostname": {string},
     "tags": {array},
     "ifaces": {array[Iface]},
     "disks": {array[Disk]},
}
```

#### Success response
```
{
   "ec": "UOK",
   	"data": {
	} ,
	"error": {} 
}
```

-------------

### <a name="mount-disk"></a>  Mount Disk

Mount a disk

#### HTTP request
      POST api/v2/management/hosts/:chunk_id/disks
      
#### URL path parameter
| Parameter Name    | Type      | Optional  | Description    |
| ------    | ----      | ----  | ----      |
| chunk_id   |  interger    |       |         |

#### Query strings
| Parameter Name    | Type      | Optional  | Description    |
| ------    | ----      | ----  | ----      |
| path   |  string    |       |         |
| purpose   |  enum("cache", "partition")    |       |         |


#### Success Response
```
{
	"ec": "UOK",
	"data": {
   	},
   	"error": {}
}
```

-------------

### <a name="unmount-disk"></a>  Unmount Disk

Unmount a disk

#### HTTP request
      POST /api/v2/management/hosts/:chunk_id/unmount_disk
      
#### URL path parameter
| Parameter Name    | Type      | Optional  | Description    |
| ------    | ----      | ----  | ----      |
| chunk_id   |  interger    |       |        |


#### Query strings
| Parameter Name    | Type      | Optional  | Description    |
| ------    | ----      | ----  | ----      |
| path   |  string    |       |         |
| purpose   |  enum("cache", "partition")    |       |         |

#### Success Response
```
{
	"ec": "UOK",
	"data": {
   	},
   	"error": {}
}
```

-------------

## Objects

### <a name="disk-object"></a> Disk Object
```
{
   "serial_number": {string},
   "size": {string},
   "disk_type": {string},
   "model_number": {string},  
   "function": {string},      # storage or cache
   "drive": {string},         # /dev/sdc
   "status": {string},        #
   "io_errors": {integer}
}
```

### <a name="cpu-object"></a> CPU Object
```
{
   "type": {string},
   "core_count": {string},
   "frequency": {string}
}
```

### <a name="memory-object"></a> Memory Object
```
{
   "type": {string},
   "frequency": {string},
   "size": {string}
}
```

### <a name="iface-object"></a> Iface Object
```
{
   "name": {string},      # ex: eth0
   "ipv4": {string},
   "ipv6": {string},
   "function": {string}   # management or data
}
```











