# FileSnapshot
ZBS file snapshot api

###/api/v2/file_snapshots

    GET /api/v2/file_snapshots?
        export_name=:export_name&path=:path

### Parameters

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| export_name  | String    |  export name                    |
| path    | String    |  file path                           |

### Success Response

```
   HTTP/1.1 200 OK
   {
        "data": [
            {
                "description": "snapshot_desc",
                "diff_size": 0,
                "name": "snapshot_name",
                "volume_id": 24,
                "created_time": {
                    "seconds": 1452723709,
                    "nseconds": 232533743
                },
                "nfs_meta": {
                    "ctime": {
                        "seconds": 1452723709,
                        "nseconds": 226363635
                    },
                    "mode": 432,
                    "mtime": {
                        "seconds": 1452723709,
                        "nseconds": 226363635
                    },
                    "atime": {
                        "seconds": 1452723709,
                        "nseconds": 226363635
                    },
                    "type": 1,
                    "size": 0
                },
                "size": 1073741824
            }
        ],
        "ec": "UOK",
        "error": {}
   }
```
## Create snapshot

CURL example:

```
curl -i http://localhost:10402/api/v2/file_snapshots \
     -H 'Content-Type: application/json' \
     -d '{"export_name": "default", "path": "file", \
          "name": "snapshot", "description": "desc"}'
```

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| export_name  | String    |  export name                    |
| path    | String    |  file path                           |
| name    | String    |  snapshot name                       |
| description    | String    |  snapshot description         |

## Update snapshot

    PUT /api/v2/file_snapshots/:snapshot_name

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| snapshot_name  (in url)  | String    |  old snapshot name  |
| export_name  | String    |  export name                    |
| path    | String    |  file path                           |
| name    | String    |  new snapshot name                   |
| description    | String    |  new snapshot description     |

## Delete snapshot

    DELETE /api/v2/file_snapshots/:snapshot_name

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| snapshot_name  (in url)  | String    |      snapshot name  |
| export_name  | String    |  export name                    |
| path    | String    |  file path                           |
