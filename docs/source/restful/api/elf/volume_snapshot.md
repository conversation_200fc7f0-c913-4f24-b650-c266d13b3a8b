  
#Volume_snapshots

###/api/v2/volume_snapshots

##Create a volume Snapshot

	POST /api/v2/volume_snapshots/

### Parameters


| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| volume_uuid    | String    |  Volume uuid                  |
| name    | String    |  Volume snapshot name	                  |
| description    | String    |  volume snapshot description                               |

### Success Response

```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": {
        	"job_id": "xxxxx-xxxxxxx-xxxxxx-xxxxx"
        },
        "error": {}
   }
```


##Delete volume snapshot


	DELETE /api/v2/volume_snapshots/<id>

## List all volume snapshot

	GET /api/v2/volume_snapshots

```
   HTTP/1.1 200 OK
    {
        "data": [
            {
                "create_time": 1463552151,
                "description": "",
                "name": "vdisk-3-snapshot-0",
                "resource_state": "in-use",
                "size": 0,
                "status": "created",
                "type": "KVM_VOLUME_SNAPSHOT",
                "uuid": "09257a02-f017-4399-a2d7-6a3baf62a0ab",
                "volume_name": "vdisk-3",
                "volume_size": 12,
                "volume_uuid": "bb5ac3f1-49b8-4833-baa1-
                				c62dee3f5744",
                "zbs_snapshot_uuid": "6f8fb737-d64c-4d42-
                				894f-5e78617064a7"
            },
            {
                "create_time": 1463552177,
                "description": "",
                "name": "vdisk-7-snapshot-0",
                "resource_state": "in-use",
                "size": 0,
                "status": "created",
                "type": "KVM_VOLUME_SNAPSHOT",
                "uuid": "8a34df3c-2590-470a-9b27-beafa54eacdc",
                "volume_name": "vdisk-7",
                "volume_size": 12,
                "volume_uuid": "176b7de4-6d4a-4ee3-ad60-
                				07f1797e710b",
                "zbs_snapshot_uuid": "ca6810e3-4c4b-45dd-
                				88d7-62d0c7be08d8"
            },
        ]
    }
```


## Show one volume snapshot

	GET /api/v2/volume_snapshots/<id>

## Rollback one volume snapshot

	POST /api/v2/volume_snapshots/<id>/rollback
	

### Response

```
   HTTP/1.1 200 OK
    {
        "data": {
            "job_id": "xxxxxxx-xxxxxxx-xxxx-xxx"
        }
    }
    
```

## Rebuild one volume snapshot

	POST /api/v2/volume_snapshots/<id>/rebuild
	
### Parameters
    {
        "name": "new_name",               
        "description": "new_description",    
    }
    
### Response

```
   HTTP/1.1 200 OK
    {
        "data": {
            "job_id": "xxxxxxx-xxxxxxx-xxxx-xxx"
        }
    }
    
```