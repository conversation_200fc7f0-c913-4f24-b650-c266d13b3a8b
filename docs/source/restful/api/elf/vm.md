  
#VM

###/api/v2/vms

##Create a vm

Not available at the version, checkout /api/v2/jobs

##Delete vm
	
Not available at the version, checkout /api/v2/jobs

## List all vms

	GET /api/v2/vms

```
   HTTP/1.1 200 OK
    {
        "data": [
            {
                "create_time": 1463040470,
                "description": "",
                "disks": [
                    {
                        "boot": 1,
                        "bus": "virtio",
                        "path": "/usr/share/smartx/volumes/
                        e52a0488-6e9e-4d94-a8c9-8085a4100dfb",
                        "type": "disk"
                    }
                ],
                "ha": false,
                "memory": 4294967296,
                "nics": [
                    {
                        "mac_address": "52:54:00:b7:cb:d2",
                        "vlans": [
                            {
                                "vlan_id": 0
                            }
                        ]
                    }
                ],
                "node_ip": "*********",
                "resource_state": "in-use",
                "status": "running",
                "type": "KVM_VM",
                "uuid": "16ed70e6-7177-4e4b-9337-1878836e0a1e",
                "vcpu": 1,
                "vm_name": "YiKeSaiTing"
            }
        ]
    }

```


## Show one vm

	GET /api/v2/vms/<id>

## Clone one vm
	POST /api/v2/vms/<id>/clone
	
### Parameters
    {
        "vm_name": "wqp-win-2008-clone",    # new clone vm name
        "vcpu": 6,                          # new vcpu number
        "memory": 17179869184,              # new memory size
        "ha": true,                         # new ha value
        "node_ip": "*********",             # new vm node ip
        "description": "克隆自 wqp-win-2008",# new vm description
        "nics": [                           # new vm nics
            {
                "vlans": [
                    {
                        "vlan_id": 0
                    }
                ]
            }
        ],
        "disks": [                         # new append disk
            {
                "type": "disk",            # append new empty disk
                "boot": 1,
                "bus": "virtio",
                "size": 10,
                "name": "vdisk-4"
            },
            {
                "path": "/usr/share/smartx/volumes/
                	bda8f691-bfbc-47e4-bb48-dc12e4e8ce7d",
                "type": "disk",            # append exist disk
                "boot": 1,
                "bus": "virtio",
                "size": 11
            }
        ],
        "status": "stopped"                # vm expected state
    }
### Response

```
   HTTP/1.1 200 OK
    {
        "data": {
            "job_id": "xxxxxxx-xxxxxxx-xxxx-xxx"
        }
    }
    
```