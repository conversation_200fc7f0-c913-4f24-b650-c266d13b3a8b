# Snapshot

ZBS snapshot related operations
###/api/v2/storage/pools/:pool_name/volumes/:volume_name

CURL example:

```
   curl -i -X GET http://localhost:10402/api/v2/storage
   	/pools/test_pool_name/volumes/test_volume_name

```

### Success Response

```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": {
            "snapshots": [{
                "created_time": {
                    "nseconds": 450488495,
                    "seconds": 1448510824
                },
                "description": "new data store",
                "diff_size": 268435456,
                "name": "test",
                "size": 1073741824,
                "replica_num": 2,
                "thin_provision": true,
                "volume_id": 40
            }
            ]
            
        },
        "error": {}
   }
```
## Create a snapshot

    POST /api/v2/stroage/pools/:pool_name/
    		volumes/:volume_name/snapshots

### Parameters

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| description    | String    |  Snapshot description             |
| name    | String    |  Snapshot name             |

## Show a snapshot

    GET /api/v2/storage/pools/:pool_name/
    	volumes/:volume_name/snapshots/:snapshot_name
    
### Success Response

```
   HTTP/1.1 200 OK
   {
    "data": {
        "chunks": [
            {
                "data_ip": 694462656,
                "data_port": 10201,
                "heartbeat_ip": 694462656,
                "heartbeat_port": 10202,
                "id": 1,
                "io_info": {
                    "avg_bandwidth": 2728,
                    "avg_iops": 4,
                    "avg_latency": 1299135.625
                },
                "name": "80046354-f0f8-4fbb-8f4e-63838a86113c",
                "parent_id": 0,
                "registered_date": 1450956496,
                "rpc_ip": 694462656,
                "rpc_port": 10200,
                "space_info": {
                    "id": 1,
                    "provisioned_data_space": 477546676224,
                    "rx_pextents": 0,
                    "total_cache_capacity": 135340228608,
                    "total_data_capacity": 2823404126208,
                    "tx_pextents": 0,
                    "used_cache_space": 132553113600,
                    "used_data_space": 397016039424
                },
                "status": 2
            },
            {
                "data_ip": 677685440,
                "data_port": 10201,
                "heartbeat_ip": 677685440,
                "heartbeat_port": 10202,
                "id": 2,
                "io_info": {
                    "avg_bandwidth": 1073.5999755859375,
                    "avg_iops": 13,
                    "avg_latency": 1341776
                },
                "name": "82c615e8-5d6d-4f53-9fdd-758b23f18638",
                "parent_id": 0,
                "registered_date": 1450956498,
                "rpc_ip": 677685440,
                "rpc_port": 10200,
                "space_info": {
                    "id": 2,
                    "provisioned_data_space": 477546676224,
                    "rx_pextents": 0,
                    "total_cache_capacity": 414849171456,
                    "total_data_capacity": 2823404126208,
                    "tx_pextents": 0,
                    "used_cache_space": 262786514944,
                    "used_data_space": 374199025664
                },
                "status": 2
            },
            {
                "data_ip": 660908224,
                "data_port": 10201,
                "heartbeat_ip": 660908224,
                "heartbeat_port": 10202,
                "id": 3,
                "io_info": {
                    "avg_bandwidth": 8456,
                    "avg_iops": 11.600000381469727,
                    "avg_latency": 2178773.5
                },
                "name": "f5e6837f-e5e2-4800-9f25-bdccbd866650",
                "parent_id": 0,
                "registered_date": 1450956569,
                "rpc_ip": 660908224,
                "rpc_port": 10200,
                "space_info": {
                    "id": 3,
                    "provisioned_data_space": 490700013568,
                    "rx_pextents": 0,
                    "total_cache_capacity": 414849171456,
                    "total_data_capacity": 2823404126208,
                    "tx_pextents": 0,
                    "used_cache_space": 288211337216,
                    "used_data_space": 458487758848
                },
                "status": 2
            }
        ],
        "snapshot": {
            "created_time": {
                "nseconds": 977423672,
                "seconds": 1452767405
            },
            "description": "",
            "diff_size": 268435456,
            "name": "test",
            "size": 1073741824,
            "volume_id": 40
        },
        "vextents": [
            {
                "location": 258,
                "vextent_id": 789
            },
            {
                "location": 0,
                "vextent_id": 790
            },
            {
                "location": 0,
                "vextent_id": 791
            },
            {
                "location": 0,
                "vextent_id": 792
            }
        ]
    },
    "ec": "UOK",
    "error": {}
}
```

## Update a snapshot

    PUT /api/v2/storage/pools/:pool_name/
    		volumes/:volume_name/snapshots/:snapshot_name

### Parameters

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| name    | String    |  new snapshot name	              |
| description    | String    |  Snapshot description             |


## Delete a snapshot

    DELETE /api/v2/storage/pools/:pool_name/
    		volumes/:volume_name/snapshots/:snapshot_name
    		
## Rollabck a snapshot

	POST /api/v2/storage/pools/:pool_name/
    		volumes/:volume_name/snapshots/:snapshot_name/rollback