# Datastore
Vmware datastore related operations 
###/api/v2/vmware/datastores/:datastore_name

CURL example:

```
curl -i -X PUT http://localhost:10402/api/v2/vmware/\
		datastores/test_datastore\
     -H 'Content-Type: application/json' \
     -d '{"mount_table": ["**************", "**************"]}'

```

### Success Response

```
   HTTP/1.1 200 OK
   {
     "ec": "UOK",
     "data": {},
     "error": {}
   }

```
## Create or update a vmware datastore with mount info

    PUT /api/v2/vmware/datastores/:datastore_name

### Parameters

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| mount_table    | Array    |  Hosts need to be mount		              |

## Show a vmware datastore and mount info

    GET /api/v2/vmware/datastores/:datastore_name

### Success Response

```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": ["**************", "**************"],
        "error": {}
   }
```

## List all vmware datastore and mount info

    GET /api/v2/vmware/datastores

### Success Response

```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": {
            "test_datastore" : 
            	["**************", "**************"]
        }
        "error": {}
   }
```
