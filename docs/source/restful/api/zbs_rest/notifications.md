# Notification
list all not expired zbs notification including warning, critical, info
###/api/v2/notifications

CURL example:

```
   curl -i -X GET http://localhost:10402/api/v2/notifications
```

### Success Response

```
   HTTP/1.1 200 OK
   {
       "ec": "UOK",
       "data": {
           "30": [
           {
                "gazer": gazer,  # 触发这个事件的gazer名字
                "gazer_uid": "gazer's uid",  # 触发这个事件所对应的gazer的uid
                "level": level,  # 事件等级（信息，注意，警告）
                "data": data,    # 导致事件的data, 包含三个内容，all_data, raw_data, processed_data,分别是
                                 # 所有数据（这个gazer所检查的所有数据），触发rule的原始数据（用于渲染帮助信息等）， 
                                 # 传给checker的处理后数据
                "message": message,
                "help_message": help_message,
                "ctime": now,
                "mtime": now,       # 事件更新时间，用来维护事件流
                "expired": False,   # 事件是否过期，用来维护事件流
           }
           ],
           "20": [],
           "50": [],
       },
       "error": {}
   }
```

## list zbs notifications

    GET /api/v2/notifications