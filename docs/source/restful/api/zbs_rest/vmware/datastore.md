# Datastore
Vmware datastore related operations 
###/api/v2/vmware/datastores

CURL example:

```
   curl -i -X POST http://localhost:10402/api/v2/vmware/datastores
        -H 'Content-Type: application/json' \
        -d '{"name": "test_datastore","mount_table": ["**************", "**************"]}'

```

### Success Response

```
   HTTP/1.1 200 OK
   {
     "ec": "UOK",
     "data": {},
     "error": {}
   }

```
## Create a vmware datastore and mount nfs on it

    POST /api/v2/vmware/datastores

### Parameters

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| name    | String    |  Name of datastore.		              |
| mount_table    | Array    |  Hosts need to be mount		              |

## Show a vmware datastore and mount info

    GET /api/v2/vmware/datastores/test_datastore

### Success Response

```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": ["**************", "**************"],
        "error": {}
   }
```

## List all vmware datastore and mount info

    GET /api/v2/vmware/datastores

### Success Response

```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": {
            "test_datastore" : ["**************", "**************"]
        }
        "error": {}
   }
```

## Update a vmware datastore and mount info

    PUT /api/v2/vmware/datastores/test_datastore

### Parameters

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| mount_table    | Array    |  Hosts need to be mount        |