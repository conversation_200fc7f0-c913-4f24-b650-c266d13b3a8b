# Volume
ZBS volume related operations
###/api/v2/storage/pools/:pool_id/volumes

CURL example:

```
   curl -i -X GET http://localhost:10402/api/v2/storage/pools/new_data_store11/volumes
```

### Success Response

```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": [
            {
                "bps_burst": 0,
                "created_time": {
                    "nseconds": 367467034,
                    "seconds": 1448105639
                },
                "id": 14391,
                "iops_burst": 0,
                "name": "0000383e",
                "pid": 5,
                "replica_num": 2,
                "size": 1073741824,
                "status": 1,
                "thin_provision": true
            },
            {
                "bps_burst": 0,
                "created_time": {
                    "nseconds": 376791730,
                    "seconds": 1448105639
                },
                "id": 14392,
                "iops_burst": 0,
                "name": "0000383f",
                "pid": 5,
                "replica_num": 2,
                "size": 1073741824,
                "status": 1,
                "thin_provision": true
            },
            {
                "bps_burst": 0,
                "created_time": {
                    "nseconds": 398888411,
                    "seconds": 1448105639
                },
                "id": 14394,
                "iops_burst": 0,
                "name": "00003841",
                "pid": 5,
                "replica_num": 2,
                "size": 1073741824,
                "status": 1,
                "thin_provision": true
            }
        ],
        "error": {}
   }
```
## Create a volume

    POST /api/v2/stroage/pools/:pool_id/volumes

### Parameters

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| volume_name    | String    |  Volume name                  |
| pool_name    | String    |  Pool name	                  |
| size    | Integer    |  Size                               |
| replica_num    | Integer    |  Replication number          |
| thin_provision    | String    |  Thin provision            |
| snapshot    | String    |  Snapshot name                   |
| description    | String    |  Volume description           |
| iops    | Integer    |  Volume iops                        |
| bps    | Integer    |  Volume bps                          |

## List all volumes

    GET /api/v2/storage/pools/:pool_id/volumes

## Show a volume

    GET /api/v2/storage/pools/:pool_id/volumes/:id

### Success Response

```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": {
            "bps_burst": 0,
            "created_time": {
                "nseconds": 398888411,
                "seconds": 1448105639
            },
            "id": 14394,
            "iops_burst": 0,
            "name": "00003841",
            "pid": 5,
            "replica_num": 2,
            "size": 1073741824,
            "status": 1,
            "thin_provision": true
        },
        "error": {}
   }
```

## Update a volume

    PUT /api/v2/storage/pools/:pool_id/volumes/:id

### Parameters

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| volume_name    | String    |  New volume name		              |
| pool_name    | String    |  Pool name	              |
| size    | Integer    |  Size              |
| replica_num    | Integer    |  Replication number            |
| thin_provision    | String    |  Thin provision           |
| snapshot    | String    |  Snapshot name             |
| description    | String    |  Volume description             |
| iops    | Integer    |  Volume iops             |
| bps    | Integer    |  Volume bps             |

## Delete a volume

    DELETE /api/v2/storage/pools/:pool_id/volumes/:id