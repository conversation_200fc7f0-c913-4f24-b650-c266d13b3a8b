# Pool
ZBS pool related operations
###/api/v2/storage/pools

CURL example:

```
   curl -i -X GET http://localhost:10402/api/v2/storage/pools

```

### Success Response

```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": [
            {
                "created_time": {
                    "nseconds": 450488495,
                    "seconds": 1448510824
                },
                "description": "new data store",
                "id": 12,
                "name": "-",
                "nfs_export": true,
                "replica_num": 2,
                "thin_provision": true,
                "user": ""
            },
            {
                "created_time": {
                    "nseconds": 893144638,
                    "seconds": 1448510907
                },
                "description": "new data store",
                "id": 13,
                "name": "_",
                "nfs_export": true,
                "replica_num": 2,
                "thin_provision": true,
                "user": ""
            },
            {
                "created_time": {
                    "nseconds": 718475347,
                    "seconds": 1447998698
                },
                "id": 1,
                "name": "default",
                "replica_num": 2,
                "thin_provision": true
            }
        ],
        "error": {}
   }
```
## Create a pool

    POST /api/v2/stroage/pools

### Parameters

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| pool_name    | String    |  Pool name		              |
| replica_num    | Integer    |  Replication number	              |
| thin_provision    | Boolean    |  Thin provision	              |
| description    | String    |  Pool description             |

## Show a pool

    GET /api/v2/storage/pools/:id
    
### Success Response

```
   HTTP/1.1 200 OK
   {
        "ec": "UOK",
        "data": {
            "created_time": {
                "nseconds": 450488495,
                "seconds": 1448510824
            },
            "description": "new data store",
            "id": 12,
            "name": "-",
            "nfs_export": true,
            "replica_num": 2,
            "thin_provision": true,
            "user": ""
        }
        "error": {}
   }
```

## Update a pool

    PUT /api/v2/storage/pools/:id

### Parameters

| Name    | Type      | Description                          |
|---------|-----------|--------------------------------------|
| pool_name    | String    |  New pool name		              |
| replica_num    | Integer    |  Replication number	              |
| thin_provision    | Boolean    |  Thin provision	              |
| description    | String    |  Pool description             |

## Delete a pool

    DELETE /api/v2/storage/pools/:id