# Summary

All zbs_rest's api impelmented by an specified schema(named 'json-schema').

From this schema, you can know what argument you should passed to use this api.

## Quick Start
Now let us start from getting the api list from our server `http://localhost:10402`(where you start your zbs_rest server).

### Get API List
Open Your browser then visit `http://localhost:10402/meta`, you can find a list of api available.

#### Plural Resource
A classical api maybe seems like this(All method list below is http method.):

```
GET /api/v2/datatores list of datastore
POST /api/v2/datatores create an new datastore
GET /api/v2/datastres/<data_store_name>  return the info of the datasotre
PUT /api/v2/datastres/<data_store_name>  modify the datastore
DELETE /api/v2/datastores/<data_store_name> delete the datastore
```
This type of resources has many copy in its type, called "plural resource".

#### Singular Resource
There may be another type of resource(The whole world has only one of it) called "singular resource":

```
GET /api/v2/auth/session GET user's login info
POST /api/v2/auth/session do an new login
DELETE /api/v2/auth/session delete the session and logout the current user
PUT(if exists) /api/v2/auth/session modify the session info
```

## How to read this api
Now you have known what the api behaves.What params should you passed if you want to use an api?

You can read the schema-definition as shown below:

```
{
    "schema": {
        "create": {
            "help": "create a session, equals to 'login' operation.",
            "type": "object",
            "properties": {
                "username": {"type": "string"},
                "password": {"type": "string"},
                "remember": {"type": "boolean"}
            },
            "required": ["username", "password"]
        },
        "delete": {
            "help": "delete a session, equals to logout operation.",
            "type": "object",
            "properties": {},
        }
    },
    "resource": "session",
}
```

Yes, the schema shown above tells us information about the resource `session`.

The api has four rows of information.

### Summary
Now in this singular resource, the operation_name-http_method map is:

```
show - GET
create - POST
delete - DELETE
update - PUT
```
More detail please search "RESTFUL API design"

For plural resource, it is:

```
index - GET
show - GET
create - POST
delete - DELETE
update - PUT
```

### Create
`create` tells us the api support the `create` operation, `properties` listed in the schema
tells us we should pass `username`(string), `password`(string), `remeber`(boolean) in `json-format`.

You should always send your data in json-format and "application-json" header in your http request.

### Delete
`delete` tells us we can logout (delete the session) and no params is required.

### show
Here this `session` object has not `show` method.But sometimes if not `show` listed in schema, there maybe
`show` method implemented(no params is required, yes).


## View Meta Info
You can view a api's meta info or schema in api's meta interface.

For singular resource, visit url like`/api/v2/auth/session/meta` to view its meta info.
For plural resource, visit url like `/api/v2/datastores/<any_string>/meta` to view its meta info.
Some times you may find some url like `/api/v2/datastores//meta`, please add any string content in this url
between the two slash `/` and you can view the meta information.




