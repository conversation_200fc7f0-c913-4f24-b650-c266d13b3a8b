# Compute summary
Return VirtualMachines and system summary information

###/api/v2/compute/summary

CURL example:

```
   curl -i -X GET http://localhost:10402/api/v2/compute/summary
```

### Success Response

```
   HTTP/1.1 200 OK
   {
       "ec": "UOK",
       "data": {
            "provisioned_cpu_cores": 0,
            "provisioned_memory": 0,
            "total_cpu_cores": 0,
            "total_memory": 0,
            "total_vms": 0,
            "vms_state": {
              "poweredOff": 0,
              "poweredOn": 0,
              "suspended": 0
            }
        },
       "error": {}
   }
```
