## 如何设计一个RESTful API

### Versioning

Prefer version api using path: `/1/xxx` or `/v1/xxx`.

### Format

* json
* `"snake_case"` fields
* pretty print with gzip
* return some meta, like `"type"` and `"link"`

```
{
  "type": "Blog",
  "link": "http://api.example.org/v1/blogs/123",
  "title": "Foo Bar",
  "sub_title": "A great blog of foobar",
  ...
}
```

### HTTP status code

#### Option A: always 200

只要是服务器处理了，就返回200。而调用是否成功，看返回body中的状态码。

成功：
```
{
  "status": "ok",
  "data": {
    ...
  }
}
```

失败：
```
{
  "status": "some_error",
  "error": {
    ...
  }
}
```

错误码直接用string就可以，比较直观。


#### Option 2: use properly

4xx/5xx会被用来表示调用中遇到的错误。好处是更加的http化，符合RESTful的标准，但坏处是会对api使用者解析错误信息造成困扰。

### Resource validation errors

有一类很讨厌的错误就是在创建或者更新resource时遇到某些field合法性检查不通过。通常包括：

* `blank`: 必须的字段没给
* `too_long`: 字段太长
* `too_short`: 字段太短
* `invalid`: 字段格式错误
* `not_unique`: 唯一的字段有冲突

这类错误通常会给出422 Unprocessable Entity。为了给使用端更多的信息以指导用户更正输入，常见的做法是返回：

```
{
  "status": "unprocessable_entity",
  "error": {
    "title": ["blank"],
    "sub_title": ["too_long"]
  }
}
```

### users

| Method | HTTP verb | Path |
| - | - | - |
| sign up | `POST` | `/users` |

### Authentication

一般API的认证都是通过token来完成的。token放到头部的`Authorization`字段即可。

| Method | HTTP verb | Path |
| - | - | - |
| sign in | `POST` | `/session` |
| sign out | `POST` | `/session` |

#### OAuth 2.0

许多api采用OAuth的方式来统一认证。好处是很标准，也有很多库支持。推荐！

### Resources

很多时候，一种resource对应数据库的一张表。注意：常规来说，必须使用复数（和数据表使用复数一个道理）。而只有在复数没有意义的情况下才选择使用单数。例如session就应该是单数，因为一个端只能有一个session。

| Method | HTTP verb | Path |
| - | - | - |
| create | `POST` | `/resources` |
| list | `GET` | `/resources` |
| get | `GET` | `/resources/:id` |
| update | `PUT`/`PATCH` | `/resources/:id` |
| delete | `DELETE` | `/resources/:id` |

#### 关于list分页：

* 基于page的分页：需要两个参数：`page`和`per_page`。好处是简单，但如果中间有新条目插入，容易出现分页移位。
* 基于cursor的分页。好处是不会出现移位，但比较复杂。

### Nested resources

#### Option A: full path

| Method | HTTP verb | Path |
| - | - | - |
| create | `POST` | `/parents/:parent_id/children` |
| list | `GET` | `/parents/:parent_id/children` |
| get | `GET` | `/parents/:parent_id/children/:id` |
| update | `PUT`/`PATCH` | `/parents/:parent_id/children/:id` |
| delete | `DELETE` | `/parents/:parent_id/children/:id` |

#### Option B: shallow path

| Method | HTTP verb | Path |
| - | - | - |
| create | `POST` | `/parents/:parent_id/children` |
| list | `GET` | `/parents/:parent_id/children` |
| get | `GET` | `/children/:id` |
| update | `PUT`/`PATCH` | `/children/:id` |
| delete | `DELETE` | `/children/:id` |

好处是url会比较短。但有个要求就是子resource可以根据id独立定位，而不需要父resource的id协助。

### MISC

#### 超出CRUD范畴的操作怎么办

* 定义一个新的resource。例如，银行账户的转账操作，很多人会定义为`POST /accounts/bill/transfer/1/to/steve`。这是不符合restful语义的。其实你可以定义一个transfer的resource，发起转账就是新建一个transfer entity。所以就变成了，`POST /transfers?from=bill&to=steve&ammount=1`，而撤销转账就可以变为`DELETE /transfers/:id`了。
* 还有一些情况，是resource之间的关联关系。例如在github中，用户能对repo进行star操作。很多人就会定义为`POST /angular/angular-js/star`（注意，这里的star是动词），但如果把star也当做是一个resource，就可以变为`PUT /angular/angular-js/stars/users/:user_id`。而撤销也比较简单，`DELETE /angular/angular-js/stars/users/:user_id`。但可以看到的是，star其实是个退化的resource，因为它只表示一种关联关系，而本身不带任何字段。

## 如何写一个方便使用和维护的RESTful API文档

### 定义通用的一些设定

* endpoint
* format
* authentication
* paging
* errors

### 定义resource的字段

* 字段名称
* 字段类型
* 字段说明
* 样例

### 定义resource的API

* 功能介绍，尤其是有什么坑
* 权限要求，例如是否需要是管理员
* URL
* HTTP verb
* Query params，也就是URL中`?`后面的东西，包括：
  * 介绍
  * 类型
  * 限制
  * 样例
* Body params，包括：
  * 介绍
  * 类型
  * 限制
  * 样例
* Response样例
* Errors

### TIPS

* 在文档开头包含一个目录。
* 对于nested resources来说，先父后子。
* **文档应该先写。**客户端可以mock一个后端，就可以不必等API开发完成就能进行开发了。

## 如何为RESTful API实现一个SDK

### 封装最底层的HTTP library

不管是选用标准库中的http方法，还是选择第三方的http client库，都应该对所采用的库进行封装。

封装在里面的内容应该有：

* request 拼接，包括：
  * HTTP verb
  * Authentication header and other headers
  * Full url = endpoint + path + query_params
  * Body
* logging
* error parsing/converting
* data parsing

提供给上层的接口：

| Method | Fingerprint |
| - | - | - |
| get | `get(path, query_params, on_success(data), on_failure(error))` |
| post | `post(path, body_params, on_success(data), on_failure(error))` |
| put | `put(path, body_params, on_success(data), on_failure(error))` |
| patch | `patch(path, body_params, on_success(data), on_failure(error))` |
| delete | `delete(path, on_success(data), on_failure(error))` |

### Resource models

为每一个resource实现一个model class。包含：

* json中的字段到实例变量名之间的mapping
* 需要客户端完成的字段合法性检查
* CRUD操作
* 其他相关API操作

### **必须禁止**

* 给SDK的使用者返回服务器返回的json数据
* 给SDK的使用者返回服务器返回的错误码
