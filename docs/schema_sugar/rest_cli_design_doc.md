API to command line
---------------------------------


## 概述
功能基本的实现思路是根据之前API当中使用的Schema定义来生成命令行。
整个过程如下：

```
Fetch schema list from server(standalone client)
             +
             |
             |
             |
             v
Generate cmd parser and help msg

             +
             |
             |
             |
             v
Do login and send command via parsing schema

             +
             |
             |
             v
Get response and generate output
```

在以上流程中，命令行所需要的信息都由服务器提供，目前，定义的Schema基础结构如下：

```
{
        "schema": {
            "show": {
                "type": "object",
                "help": "show a pool's info",
            },
            "index": {
                "type": "object",
                "help": "list all pools",
            },
            "create": {
                "type": "object",
                "help": "create a pool",
                "properties": {
                    "pool_name": {"type": "string"},
                    "replica_num": {
                        "type": "number",
                        "enum": [1, 2, 3]
                    },
                    "thin_provision": {"type": "boolean"},
                    "description": {"type": "string"},
                },
                "required": ["pool_name", "replica_num", "thin_provision"]
            },
            "update": {
                "type": "object",
                "help": "update a pool",
                "properties": {
                    "pool_name": {"type": "string"},
                    "replica_num": {
                        "type": "number",
                        "enum": [1, 2, 3]
                    },
                    "thin_provision": {"type": "boolean"},
                    "description": {"type": "string"},
                },
            },
        },
        "resources": "pools",
    }

```

以上述Schema为例，从服务器取得这个Schema之后，可以从声明获知，这个资源支持一些什么方法（update, show, create, index）。

命令行生成的时候，会读取properties信息（所需字段）和help message，其中帮助信息对应cmd的help参数，properties则定义了每个参数的类型，参数类型规则将会被map到命令行的参数类型（例如string->string, number->float or int），帮助信息中也会包含对于各对应参数的类型描述（甚至是help）

举例说明如何生成对应的命令行
### Index
对于Index，只有help信息，则用户

```
smartx pools index
# This command should list all the resources that get from the server
# In json format
smartx pools --help
# Here is should list all the operations that the api supports, for example
# “index”, “show”, “create” and help message about each command(parameters and basic help)
smartx zbs_pools index
# returns
{"ec": "EOK", "data": [{"name": "pool1"}...] }

# show the help for index command, include parameters（and its format, requires, etc） and basic help
```

###Create
create操作的Schema如下

```
 "create": {
                "type": "object",
                "help": "create a pool",
                "properties": {
                    "pool_name": {"type": "string"},
                    "replica_num": {
                        "type": "number",
                        "enum": [1, 2, 3]
                    },
                    "thin_provision": {"type": "boolean"},
                    "description": {"type": "string"},
                },
                "required": ["pool_name", "replica_num", "thin_provision"]
            },

```
这个Schema包含如下信息：

help：Help Message
properties：parameters which should be passed to api

比如create命令会变为

```
smartx pools  create pool_name replica_num thin_provision --description “hello”
```

修改的命令

```
smartx pools update pool_name --replica_num false --thin_provision true --description “hello”

```

每一个propertie都是一个参数，参数会被以一定的规则映射到命令行。

必选参数：会被映射为 位置参数，比如pool_name,
可选参数：会被映射为可选参数，比如”--description”
复杂参数：参数本身是一个json对象，而不是字符串或者整数，则会被要求输入一个json字符串，简单的字符串参数或者整数则直接映射为命令行参数，简单起见，参数不会递归映射，只会映射第一层的参数。

上述，就是整个Rest命令行实现的基本规则。


## 关于返回类型描述（将在API doc设计里进行描述）

API Doc Generation
---------------------------------

文档生成实际上也是基于Schema的。
还是以上面相同一个Schema进行说明。

文档生成的内容基本上和命令行生成的时候描述一致。

增加的内容：
返回值描述，需要在Schema中加入返回类型的描述，告诉用户会返回什么样的数据，数据本身也是用JsonSchema进行描述。
文档生成的文本全部来自于Schema定义，或者Python的DocString，这个具体实现的时候再考虑吧，实现的时候会顺便补完Rest API所有的文档信息。


## 实现状况

### 已实现
+ 基本的命令行，现在，使用SchemaSugar定义的RestAPI都可以使用命令行

### 希望支持的特性
+ 支持ExtraAction，相关定义在SchemaSugar的用法当中有描述，现在命令行仅支持CRUD
+ 支持文件上传

## 代码结构
Source:

```
schema_sugar/client
```

Usage:

```
zbs_rest/cmd_client.py
```

Run:

```
python zbs_rest/cmd_client.py
```
