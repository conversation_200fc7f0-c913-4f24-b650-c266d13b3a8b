- #Plugin

  Job Center(JC)以plugin形式加载代码，当plugin安装到特定的目录，JC启动的时候就会自动load到内存执行

  每个plugin需要按照指定的格式开发，才能加载成功，下面给出example：

  - ##elf/job_center/config.py

    每一个plugin，都需要有一个config.py配置文件去描述这个plugin里面的内容，里面包含4个参数

    - ###follower_map

    描述的是follower函数和leader分配的操作名称之间的对应关系，如：

        follower_map = {
            TASK_PARTITION_MOUNT: partition_mount,
            TASK_PARTITION_UMOUNT: partition_umount,
            TASK_CACHE_MOUNT: cache_mount,
            TASK_CACHE_UMOUNT: cache_umount,
            SCHEDULE_TASK_NODE_COLLECT_CHECK: node_info_collect,
            SCHEDULE_TASK_UPDATE_SCVM_IP: update_scvm_ip,
        }

     Leader在分配任务的时候，并不会知道使用的函数是什么，他只关心需要的行为是什么，而上面代码的Key就是Leader分配的时候的操作常量，而Value就是操作的真正函数。而Follower则负责加载实际的函数，这样Follower在处理Leader分配的任务的时候，才能对应得上。

    - ###scheduler_beat

    用来描述定时任务的变量，如：

        scheduler_beat = {
            'node-info-collect': {
                'task': 'smartx_app.management.job_center.scheduler.'
                        'cron.schedule_collect_node_info',
                'schedule': timedelta(seconds=COLLECT_NODE_INTERVAL),
            }
        }

    这里描述的是在这个plugin下，有2个定时任务，一个是定时collect node info， 另外一个是定时update scvm ip。task描述的是触发后执行的任务函数的绝对路径，schedule是任务的interval，根据任务的需要定义。因为我们定时触发的scheduler会在master节点部署起码3个来达到冗余的效果，所以当我们3个都触发的时候，需要辨别并只提交一次，这部分是需要在触发的函数里面处理的。

        @app.task
        def schedule_collect_node_info():
            now = int(time.time()) / COLLECT_NODE_INTERVAL * COLLECT_NODE_INTERVAL
            if try_set_cron_job(now, "node-collect"):
                node_ips = utils.get_running_worker_ips_from_mongo()
                job_submit.delay(
                    "Scheduler",
                    "Auto check",
                    type=JOB_TYPE_SCHEDULE,
                    schedule_task={
                        "name": SCHEDULE_TASK_NODE_COLLECT_CHECK,
                        "hosts": node_ips,
                    }
                )


    上面代码是scheduler触发的执行的绝对路径的函数，首先他需要算出基于interval来做一个不精确整除，来算出最近的一个需要提交任务的时间点，然后try_set_cron_job来尝试请求这个时间点能否提交任务，假如这个时间点已经被其他节点的scheduler抢占提交，则不会重复提交。
    提交的内容中schedule_task是必须的，用来指定需要做的任务，而hosts则指定执行的节点，当不指定的时候，会随机找一个节点执行。

    - ###Promote gazer system
        以`ovs_bonding_nics_status_uid`和`node_data_ip_ping_ttl`
        为例子，现在的gazer执行逻辑是遍历所有gazer的config_dict，每个config_dict都是一个字典
        config_dict = {
            "gazer_type": "GAZER_TYPE",
            "name": "gazer_name",
            "uid": "gazer_uid"
        }
        遍历的过程会提交一个job，意思是有多少个gazer的config_dict,就会根据它生成多少个job
        这样当报警项数量多之后，每十秒执行一次会带来很多job存入mongodb中

        ```python
            if try_set_cron_job(now, "gazer-check"):
                job_submit.delay(
                    "Scheduler",
                    "Auto check",
                    type=JOB_TYPE_SCHEDULE,
                    schedule_task={
                        "name": SCHEDULE_TASK_GAZER_RULE_CHECK,
                        "args": [
                            {
                                "gazer_type": "customized",
                                "name": "ovs_bonding_nics_status",
                                "uid": "ovs_bonding_nics_status_uid"
                            }
                        ],
                    }
                ) # Here is submit one job
        ```

        ```python
            if try_set_cron_job(now, "gazer-check"):
                job_submit.delay(
                    "Scheduler",
                    "Auto check",
                    type=JOB_TYPE_SCHEDULE,
                    schedule_task={
                        "name": SCHEDULE_TASK_GAZER_RULE_CHECK,
                        "args": [
                            {
                                "gazer_type": "customized",
                                "name": "node_data_ip_ping_ttl",
                                "uid": "node_data_ip_ping_ttl"
                            }
                        ],
                    }
                ) # Here is submit another job
        ```

        将报警合成一个job并拆成多个subtasks可以以以下方式实现
        在scheduler_task中增加一个另外的键值对`kwargs: {"gazer_uid": {"gazer_uid", "gazer_name", "gazer_type"}}`
        由原先的`args: [{"gazer_uid", "gazer_name", "gazer_type"}, ]`列表改为字典，是因为SchedulerAndOneTimeTaskSpitter中可以通过
        reference作为键提取对应的值 -> gazer的config_dict
        因为拆分多个subtasks之后，不指定在那个host中执行，也就是有可能在任意一个节点中执行，所以需要将所有的config_dict存入schedule_task中，并在handle_schedule_process方法中根据reference获取对应的config_dict

        ```python
            @app.task
            def schedule_gazer_check_submit():
                all_gazer_config = get_all_gazer_config()
                dct_args = dict(
                    (gazer.get('uid'), gazer)
                    for gazer in get_all_gazer_config()
                )
                if len(dct_args) != len(all_gazer_config):
                    logging.error(
                        "scheduler_gazer_check_submit has some "
                        "error on get gazer_config dict."
                    )

                now = int(time.time()) / GAZER_CHECK_INTERVAL * GAZER_CHECK_INTERVAL
                if try_set_cron_job(now, "gazer-check"):
                    job_submit.delay(
                        "Scheduler",
                        "Auto check",
                        type=JOB_TYPE_SCHEDULE,
                        schedule_task={
                            "name": SCHEDULE_TASK_GAZER_RULE_CHECK,
                            "kwargs": dct_args,
                            """
                                # Considering the job number increasing like hell when
                                # make a specical gazer match a job, so now collect all
                                # all gazer dict into `kwargs` parameter and jostle into one job
                                # and spilt few subtasks by spitter.
                                # It's a host-only mission, so it will process in all hosts in a cluster,
                                # so if `hosts` parmeter is not `[]`, this parmeter will not be used

                            """
                        }
                    )
        ```

        In order to satisfy this data structure, we change SchedulelAndOneTimeTaskSpitter.
        为了使得Spitter能满足新添加的字段，所以要对新加的`kwargs`字段进行处理,主要是将key值取出来
        作为refernce

        ```python
            class SchedulerAndOneTimeActionSplitter(SplitterBase):

                def split(self, schedule_task, reference=None):
                    schedule_name = schedule_task['name']
                    tasks = []
                    if schedule_task.get("hosts"):
                        for host in schedule_task["hosts"]:
                            tasks.append(
                                self.generate_task(
                                    schedule_name,
                                    reference or schedule_name, # referece in schedule is task name
                                                                # in one time task is reference
                                    host
                                )
                            )
                    elif schedule_task.get("kwargs"):
                        for key in schedule_task["kwargs"].keys():
                            tasks.append(
                                self.generate_task(
                                    schedule_name,
                                    reference=key  # reference can get config_dict
                                )
                            )
                    else:
                        tasks.append(
                            self.generate_task(
                                schedule_name,
                                reference or schedule_name
                            )
                        )
                    return tasks
        ```

    - ###splitter_set


    用来描述resource type和splitter的对应关系

        splitter_set = {
            PARTITION_DISK: PartitionOperationSplitter(),
            CACHE_DISK: CacheOperationSplitter()
        }

    例如CacheOperationSplitter就是用来处理对应的资源类型CACHE_DISK的，这个splitter会被加载到Leader，然后当收到Job里面包含CACHE_DISK的类型的时候，就使用CacheOperationSplitter来进行拆分工作。

    - ###dependencies

    用来描述任务之间的依赖关系，用于解决多任务处理的时候的先后次序

        dependencies = {
            TASK_VM_CREATE: [
                TASK_VM_DELETE,
                TASK_KVM_VOLUME_CREATE,
                TASK_ISCSI_VOLUME_CREATE
            ]
        }

    这里表达的依赖关系是，在VM被创建之前，他需要依赖的任务是KVM Volume的创建，ISCSI的Volume的创建，还有VM的删除（释放资源）。当定义好这些依赖关系，Leader就会知道splitter拆分出来的任务是怎么调配的。

  - ##elf/job_center/follower/kvm/vm.py

    这里是所有的关于elf的vm操作的follower的函数定义


        @app.task(base=FollowerBase, bind=True)
        @FollowerBase.handle_follower_process
        @FollowerBase.include_libvirt_conn
        def kvm_vm_create(self, vm_json, libvirt_conn=None):
            """create kvm virtual machine through libvirt

            :param vm_json: complete kvm vm type json including all vm info
            :return: vm json after modification
            """
            vm_uuid = vm_json['uuid']
            for nic in vm_json['nics']:
                for vlan in nic['vlans']:
                    ensure_vlan_network(vlan['vlan_id'])
                if not nic.get('mac_address'):
                    nic['mac_address'] = random_mac_addr()
            try:
                libvirt_domain = libvirt_conn.lookupByName(vm_uuid)
                domain_uuid = libvirt_domain.UUIDString()
            except libvirt.libvirtError:
                domain_uuid = None
            # generate xml with domain uuid if domain found
            # otherwise let libvirt generate a new one
            xml = elf_json_to_xml(vm_json, domain_uuid)
            libvirt_conn.defineXML(xml)
            result = {
                'node_ip': CURRENT_NODE_IP,
                'create_time': utc_now_timestamp(),
                "_unset": {
                    "name": ""  # unset the name since this name maybe add
                                # by template vm_create,
                                # however job need it so unset here
                }
            }
            self.resource_done(result)
            return result

    上面代码是一个事例，一个创建VM的follower的事例，函数中参数，vm_json则是job中resource的数据，也就是描述vm的属性如vcpu，memory等，然后这个函数就需要根据这些参数，创建对应的VM。

    Follower其中一个必须的属性就是函数操作需要是等幂的，所以中间有一个try catch去处理VM的uuid，以防重复创建。

    与此同时，Follower的返回值，也决定了数据是怎么被处理的。

    - False

        代表这个resource并不会更新或者保存到mongodb

    - None

        代表这个resource会被更新到mongodb

    - {Key:Value}

        代表只更新对应的Key和Value到mongodb

    - tuple (new handle result method)

        tuple[0] 是return code，代表行为
            FOLLOWER_SAVE_RESOURCE_AND_JOB = 1
            FOLLOWER_SAVE_JOB_ONLY = 2
            FOLLOWER_DONOT_SAVE = 3
        tuple[1] 是return resource数据

    Follower的装饰器中，前2个是必须项，其他的是按需使用，例如FollowerBase.include_libvirt_conn

    Follower中的行为应该尽可能的粒度小，类写操作只能一个，否则很难实现等幂，或者服务器崩溃后很难达到恢复的效果。


  - ##elf/job_center/leader/splitter/kvm/vm.py

    这是关于VM的Splitter的定义，用来处理VM在各种状态下，需要生成的操作。

    当定义一个类并继承SplitterBase后，实现split的函数，如下图，那么leader会自动的把vm的信息传递到这个函数，然后我们通过重写split函数去实现我们的效果。

        @SplitterBase.include_current_json
        def split(self, vm_json_expected, vm_json):
            """diff current vm_json and expected vm json

            :param vm_json_expected: current vm json from frontend
            :param vm_json: vm resource json from mongodb
            :return: a list of vm operations
            """

    例如上面函数传入当前的vm的json和期望的vm的json，通过对比他们state的状态差异，我们就能生成对应的操作，如当前是stopped，但我期望是running，那么可以通过状态机生成一个vm_start的操作给leader让他安排start这个vm。


  - ##elf/job_center/hook/tasks.py

    Job Center支持一些celery的特性的hook， 例如启动时的操作，关闭前的操作等


  - ##elf/job_center/schedluer/cron.py
    Job Center 支持定时任务，写一个定时任务需要在几个地方做定义

    - 在config.py的schedule_beats设置定时触发的函数，和间隔时间

    - 触发的函数需要进行判断当前是否有权限提交Job（用于协调多个Scheduler，防止同一个时间段重复提交Job），然后提交Job

    - 写下Job对应的Follower函数并让Job Center加载，上一条规则提交的Job只要包含对应的Follower参数，Leader就会自动分配任务到Follower执行


  - ## /pyzbs/zbs/smartx_app/common/resource_db_map.py

    Job Center 会读取这里的map来获取每一个resource type对应的db和collection，然后用于更新job中resource到对应的db和collection中去
    主要用于FollowerBase，Follower自动的处理resource的数据，便于使用者focus在真正需要处理的业务中。



