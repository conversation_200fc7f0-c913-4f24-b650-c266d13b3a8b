# Copyright (c) 2014, SMARTX
# All rights reserved.

import logging

from common.config.constant import DATA_IP
from common.lib.utils.config_util import get_config_ip


def get_data_ip():
    return get_config_ip(DATA_IP)


def get_host_config_uuid():
    HOST_UUID = "/etc/zbs/uuid"
    host_config_uuid = ""
    try:
        with open(HOST_UUID) as f:
            host_config_uuid = f.read().strip()
    except OSError:
        logging.exception("Failed to access host uuid file({}).".format(HOST_UUID))
    return host_config_uuid
