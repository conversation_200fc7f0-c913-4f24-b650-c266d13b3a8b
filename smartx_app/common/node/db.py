import pymongo

from common.config.constant import DEFAULT_DB_NAME
from common.mongo.db import mongodb
from smartx_app.common.node.config import get_host_config_uuid


def query_hosts(offset=0, limit=None):
    db = mongodb[DEFAULT_DB_NAME]
    cursor = db.host_infos.find(sort=[("_id", pymongo.DESCENDING)])
    if offset > 0:
        cursor = cursor.skip(offset)
    if limit is not None:
        cursor = cursor.limit(limit)
    return list(cursor)


def query_local_host():
    """
    :rtype: dict or None
    """
    db = mongodb[DEFAULT_DB_NAME]
    doc = db.host_infos.find_one(
        {"host_uuid": get_host_config_uuid()},
        sort=[("_id", pymongo.DESCENDING)],
    )
    return doc


def fuzzy_search_by_name(text, projection):
    import re

    current_projection = {"_id": 0}
    if projection:
        current_projection.update(projection)

    cursor = mongodb[DEFAULT_DB_NAME].host_infos.find(
        {"name": {"$regex": re.sub(r"(\*|\.|\?|\+|\$|\^|\[|\]|\(|\)|\{|\}|\||\\|/)", r"\\\1", text), "$options": "i"}},
        current_projection,
    )

    return list(cursor)


def query_host_by_data_ip(data_ip):
    """
    :param data_ip:     host data ip
    :return:            dict or None
    """
    db = mongodb[DEFAULT_DB_NAME]
    doc = db.host_infos.find_one({"data_ip": data_ip})
    return doc


def get_host_ips(host_uuids=None):
    """
    Get host ips from host_infos db collection.

    :param host_uuids:      host uuid list, return all host ips
                            if it is empty or None.
    :return:
    """
    projection = {"host_uuid": 1, "management_ip": 1, "data_ip": 1}
    if host_uuids:
        query = {"host_uuid": {"$in": host_uuids}}
    else:
        query = {"host_uuid": {"$exists": True}}
    return list(mongodb[DEFAULT_DB_NAME].host_infos.find(query, projection))


def get_host_data_ip(host_uuid):
    host_ips = get_host_ips([host_uuid])
    return host_ips[0]["data_ip"] if host_ips else None
