# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
from common.mongo.constant import RESOURCE_DB
from smartx_app.common.resource_type import (
    DATA_PORT,
    DISK_IMAGE,
    ISO_IMAGE,
    KVM_VM,
    KVM_VM_SNAPSHOT,
    KVM_VM_TEMPLATE,
    KVM_VOL,
    KVM_VOL_ISCSI,
    KVM_VOL_ISCSI_SNAPSHOT,
    KVM_VOL_ISCSI_TEMPLATE,
    KVM_VOL_SNAPSHOT,
    KVM_VOL_TEMPLATE,
    NETWORK_GENERAL_NETWORK,
    NETWORK_SYSTEM_NETWORK,
    NETWORK_VDS,
    NETWORK_VLAN,
    STORAGE_CLUSTER,
    STORAGE_POLICY,
)

GENERAL_RESOURCE_COLLECTION = "resource"

DB_KEY = "db"

COLLECTION_KEY = "collection"

RESOURCE_DB_MAP = {
    KVM_VM: {DB_KEY: RESOURCE_DB, COLLECTION_KEY: GENERAL_RESOURCE_COLLECTION},
    KVM_VM_SNAPSHOT: {DB_KEY: RESOURCE_DB, COLLECTION_KEY: GENERAL_RESOURCE_COLLECTION},
    KVM_VM_TEMPLATE: {DB_KEY: RESOURCE_DB, COLLECTION_KEY: GENERAL_RESOURCE_COLLECTION},
    KVM_VOL: {DB_KEY: RESOURCE_DB, COLLECTION_KEY: GENERAL_RESOURCE_COLLECTION},
    KVM_VOL_ISCSI: {DB_KEY: RESOURCE_DB, COLLECTION_KEY: GENERAL_RESOURCE_COLLECTION},
    KVM_VOL_SNAPSHOT: {DB_KEY: RESOURCE_DB, COLLECTION_KEY: GENERAL_RESOURCE_COLLECTION},
    KVM_VOL_TEMPLATE: {DB_KEY: RESOURCE_DB, COLLECTION_KEY: GENERAL_RESOURCE_COLLECTION},
    KVM_VOL_ISCSI_SNAPSHOT: {DB_KEY: RESOURCE_DB, COLLECTION_KEY: GENERAL_RESOURCE_COLLECTION},
    KVM_VOL_ISCSI_TEMPLATE: {DB_KEY: RESOURCE_DB, COLLECTION_KEY: GENERAL_RESOURCE_COLLECTION},
    ISO_IMAGE: {DB_KEY: RESOURCE_DB, COLLECTION_KEY: GENERAL_RESOURCE_COLLECTION},
    DISK_IMAGE: {DB_KEY: RESOURCE_DB, COLLECTION_KEY: GENERAL_RESOURCE_COLLECTION},
    NETWORK_VDS: {DB_KEY: RESOURCE_DB, COLLECTION_KEY: "vds"},
    NETWORK_VLAN: {DB_KEY: RESOURCE_DB, COLLECTION_KEY: "vlan_v2"},
    STORAGE_POLICY: {DB_KEY: RESOURCE_DB, COLLECTION_KEY: "storage_policy"},
    DATA_PORT: {DB_KEY: RESOURCE_DB, COLLECTION_KEY: "dataport"},
    NETWORK_SYSTEM_NETWORK: {DB_KEY: RESOURCE_DB, COLLECTION_KEY: "network"},
    NETWORK_GENERAL_NETWORK: {DB_KEY: RESOURCE_DB, COLLECTION_KEY: "network"},
    STORAGE_CLUSTER: {DB_KEY: RESOURCE_DB, COLLECTION_KEY: "storage_cluster"},
}


def get_db_collection(resource_type):
    db_name = RESOURCE_DB_MAP[resource_type][DB_KEY]
    collection_name = RESOURCE_DB_MAP[resource_type][COLLECTION_KEY]
    return db_name, collection_name
