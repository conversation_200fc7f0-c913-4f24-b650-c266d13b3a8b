/*
 * lock_driver_allock.c: Allock is a lock driver for libvirt, based on ZBS
 * ISCSI permission access control.
 *
 * Copyright (C) 2022 SmartX, Inc.
 *
 */
#include <config.h>

#include "internal.h"
#include "lock_driver.h"
#include "viralloc.h"
#include "virconf.h"
#include "virlog.h"
#include "virobject.h"
#include "virstring.h"

#include "zbs_allow_list.h"

#define VIR_FROM_THIS VIR_FROM_LOCKING

/*
 * 'locking' can be configured in libvirt conf file in order to filter out the
 *  allock logs.
 */
VIR_LOG_INIT("locking.lock_driver_allock");

#define LUN_ARRAY_BUFLEN 1024

#define CALL_ALLOCK_FUNCTION_WITH_ZBS_AGENT(allockLunFunc, priv, flags)              \
    do {                                                                             \
        if ((priv)->res_count == 0) {                                                \
            VIR_INFO("No resource need lock");                                       \
            ret = 0;                                                                 \
        }                                                                            \
        for (size_t i = 0; i < (priv)->res_count; i++) {                             \
            allockLunPtr res = (priv)->res_args[i];                                  \
            agent = zbsAgentLookupBystorageIP(g_allockZbsAgentList, res->storageIP); \
                                                                                     \
            if (agent == NULL) {                                                     \
                VIR_ERROR("Select ZBS agent failed");                                \
                ret = -1;                                                            \
                break;                                                               \
            }                                                                        \
                                                                                     \
            virObjectLock(agent);                                                    \
            if (allockLunFunc((flags), res, agent) < 0) {                            \
                ret = -1;                                                            \
                virObjectUnlock(agent);                                              \
                break;                                                               \
            }                                                                        \
            ret = 0;                                                                 \
            virObjectUnlock(agent);                                                  \
        }                                                                            \
    } while (0)

static const char *g_vhostMarkFile = "/etc/zbs/.boostenabled";

typedef struct _allockPrivate allockPrivate;
typedef allockPrivate *allockPrivatePtr;

struct _allockPrivate {
    char *vm_name;
    int res_count;
    allockLunPtr res_args[LUN_ARRAY_BUFLEN];
};

static zbsAgentListPtr g_allockZbsAgentList;

static zbsAllowListDriverPtr g_defaultZbsAllowListDrv;

/* === iscsi === */
static bool iscsiIsPathValid(const char *path);

/* === initiator === */
static int initiatorParseSharedStatus(char *initiator, bool *shared);
static int initiatorParseVmUuid(const char *initiator, char **vmUuid);
static int initiatorCheckVmUuidMatched(const char *initiator, const char *vmUuid, bool *match);
static int initiatorExtractConflictInitiators(const char *targetInitiator,
                                              const char *existingInitiators,
                                              int *nConflicts,
                                              char ***conflictInitiators);

/* === allock helper === */
static bool allockIsVhostEnabled(void);
static bool allockIsAllowListDriveVhost(zbsAllowListDriverPtr zbsAllowListDrv);
static void allockPrivFree(allockPrivatePtr priv);

/* === allock lun common === */
static allockLunPtr allockLunNew(char *target, int lunId);
static allockLunPtr allockLunNewFromIscsiPath(const char *iscsiPath);
static void allockLunFree(allockLunPtr lun);
static bool allockLunIsOpenOwnership(const zbsLunAllowListPtr zbsLunInfo);
static bool allockLunIsEmptyOwnership(const zbsLunAllowListPtr zbsLunInfo);
static bool allockLunIsExpectedOwnership(const zbsLunAllowListPtr zbsLunInfo,
                                         const char *expectedInitiator);
static bool allockLunIsSingleAccess(const zbsLunAllowListPtr zbsLunInfo);
static bool allockLunIsStatusSafe(const zbsLunAllowListPtr zbsLunInfo);
static bool allockLunIsExpectedSharedOwnership(const zbsLunAllowListPtr zbsLunInfo,
                                               const char *expectedInitiator);
static int allockLunCheckSharedOwnershipSafe(const zbsLunAllowListPtr zbsLunInfo,
                                             const char *myInitiator);

/* === allock lun operation === */
static int allockLunAcquire(unsigned int flags, allockLunPtr lun, zbsAgentPtr agent);
static int allockLunRelease(unsigned int flags, allockLunPtr lun, zbsAgentPtr agent);
static int allockLunPrepareStart(unsigned int flags, allockLunPtr lun, zbsAgentPtr agent);
static int allockLunPrepareEnd(unsigned int flags, allockLunPtr lun, zbsAgentPtr agent);

/* === allock lun operation.acquire === */
static int allockLunAcquirePowerOn(const allockLunPtr lun, zbsAgentPtr agent);
static int allockLunAcquireSafe(const allockLunPtr lun, zbsAgentPtr agent);
static int allockLunAcquireNormalResume(const allockLunPtr lun, zbsAgentPtr agent);

/* === allock lun operation.release === */
static int allockLunReleaseSafe(const allockLunPtr lun, zbsAgentPtr agent);

/* === allock lun operation.prepare start === */
static int allockLunPrepareStartPowerOn(const allockLunPtr lun, zbsAgentPtr agent);
static int allockLunPrepareStartPowerOnShared(const allockLunPtr lun,
                                              zbsLunAllowListPtr zbsLunInfo,
                                              zbsAgentPtr agent);
static int allockLunPrepareStartInMigration(const allockLunPtr lun, zbsAgentPtr agent);

/* === allock lun operation.prepare end === */
static int allockLunPrepareEndInMigration(const allockLunPtr lun, zbsAgentPtr agent);

/* === allock core functions === */
static int allockInit(unsigned int version, const char *configFile, unsigned int flags);
static int allockDeinit(void);
static int allockNew(virLockManagerPtr lock,
                     unsigned int type,
                     size_t nparams,
                     virLockManagerParamPtr params,
                     unsigned int flags);
static void allockFree(virLockManagerPtr lock);
static int allockAddResource(virLockManagerPtr lock,
                             unsigned int type,
                             const char *name,
                             size_t nparams,
                             virLockManagerParamPtr params,
                             unsigned int flags);
static int allockAcquire(virLockManagerPtr lock,
                         const char *state,
                         unsigned int flags,
                         virDomainLockFailureAction action,
                         int *fd);
static int allockRelease(virLockManagerPtr lock, char **state, unsigned int flags);
static int allockInquire(virLockManagerPtr lock, char **state, unsigned int flags);
static int allockPrepareStart(virLockManagerPtr lock, unsigned int flags);
static int allockPrepareEnd(virLockManagerPtr lock, unsigned int flags);

/* ==== iscsi === */
static bool
iscsiIsPathValid(const char *path)
{
    return STREQLEN(path, "iqn", 3);
}

/* ==== initiator === */

/*
 * initiatorParseSharedStatus:
 * To determine if the resource shared status. A example initiator
 * is like: iqn.2013-11.org.smartx:<vm_uuid>.<host_uuid>.<0/1>,
 * where the last bit indicates whether lun is shared, 1 for shared
 * 0 for otherwise.
 *
 * @initiator: the name of initiator to parse.
 * @shared: function's output indicating whether lun is shared or not.
 * return: 0 for valid parse, -1 for invalid name.
 */
static int
initiatorParseSharedStatus(char *initiator, bool *shared)
{
    char **initiatorParts = NULL;
    char **elfNameParts = NULL;
    int ret = -1;

    initiatorParts = virStringSplit(initiator, ":", 2);
    if (initiatorParts == NULL) {
        goto cleanup;
    }

    if (initiatorParts[1] == NULL) {
        goto cleanup;
    }

    elfNameParts = virStringSplit(initiatorParts[1], ".", 3);
    if (elfNameParts == NULL) {
        goto cleanup;
    }

    if (elfNameParts[2] == NULL) {
        goto cleanup;
    }

    if (elfNameParts[2][0] == '0') {
        ret = 0;
        *shared = false;
    } else if (elfNameParts[2][0] == '1') {
        ret = 0;
        *shared = true;
    } else {
        ret = -1;
    }

cleanup:
    virStringListFree(initiatorParts);
    virStringListFree(elfNameParts);
    return ret;
}

/*
 * initiatorParseVmUuid:
 * To parse the vm uuid from a initiator. A example initiator
 * is like: iqn.2013-11.org.smartx:<vm_uuid>.<host_uuid>.<0/1>.
 *
 * @initiator: the name of initiator to parse.
 * @vmUuid: function's output for parsed vm uuid.
 * return: 0 for valid parse, -1 for invalid name.
 */
static int
initiatorParseVmUuid(const char *initiator, char **vmUuid)
{
    VIR_AUTOSTRINGLIST initiatorParts = NULL;
    VIR_AUTOSTRINGLIST elfNameParts = NULL;
    int ret = -1;
    size_t count;

    initiatorParts = virStringSplitCount(initiator, ":", 2, &count);
    if (initiatorParts == NULL) {
        return ret;
    }

    if (count != 2) {
        return ret;
    }

    elfNameParts = virStringSplitCount(initiatorParts[1], ".", 3, &count);
    if (elfNameParts == NULL) {
        return ret;
    }

    if (count != 3) {
        return ret;
    }

    *vmUuid = g_strdup(elfNameParts[0]);

    ret = 0;
    return ret;
}

/*
 * initiatorCheckVmUuidMatched:
 * Check if the vm uuid in a initiator name matches the given vm uuid.
 *
 * @initiator: the name of initiator to check.
 * @vmUuid: target vm uuid to match.
 * @match: output if the initiator has matched vm uuid.
 * return: 0 for valid match result, -1 for failed match.
 */
static int
initiatorCheckVmUuidMatched(const char *initiator, const char *vmUuid, bool *match)
{
    int parseRet;
    g_autofree char *parsedVmUuid = NULL;

    parseRet = initiatorParseVmUuid(initiator, &parsedVmUuid);
    if (parseRet < 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Failed to parse initiator: %s"), initiator);
        return -1;
    }

    *match = STREQ(vmUuid, parsedVmUuid);
    return 0;
}

/*
 * initiatorExtractConflictInitiators:
 * Conflict initiator: for initiator name A and B, if they are not the
 * same, but contains the same vm uuid, we say B is A's conflict initiator.
 *
 * @targetInitiator: the initiator to whom we want to find conflict initiator.
 * @existingInitiators: the initiators list from which we wanna find conflict
 * initiators.
 * @nConflicts: number of conflict initiators found, output value.
 * @conflictInitiators: list of conflict initiators found, output value.
 * return: 0 for valid extraction, -1 for failed extraction.
 */
static int
initiatorExtractConflictInitiators(const char *targetInitiator,
                                   const char *existingInitiators,
                                   int *nConflicts,
                                   char ***conflictInitiators)
{
    bool matched;
    g_autofree char *vmUuid = NULL;
    VIR_AUTOSTRINGLIST initiators = NULL;
    VIR_AUTOSTRINGLIST cInitiators = NULL;
    int nExistingInitiators;
    int conflictIndex = 0;

    *nConflicts = 0;

    if (virStringIsEmpty(existingInitiators)) {
        return 0;
    }

    if (initiatorParseVmUuid(targetInitiator, &vmUuid) < 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Failed to parse initiator: %s"), targetInitiator);
        return -1;
    }

    if (!(initiators = virStringSplit(existingInitiators, ",", 0))) {
        virReportError(
            VIR_ERR_INTERNAL_ERROR, _("Failed to split initiators: %s"), existingInitiators);
        return -1;
    }

    nExistingInitiators = virStringListLength((const char *const *)initiators);

    if (VIR_ALLOC_N(cInitiators, nExistingInitiators) < 0) {
        virReportError(
            VIR_ERR_INTERNAL_ERROR, _("%s"), "Failed to allocate memory for conflict initiators");
        return -1;
    }

    for (int i = 0; i < nExistingInitiators; i++) {
        if (initiators[i] == NULL) {
            continue;
        }

        if (STREQ(initiators[i], targetInitiator)) {
            continue;
        }

        if (initiatorCheckVmUuidMatched(initiators[i], vmUuid, &matched) != 0) {
            virReportError(
                VIR_ERR_INTERNAL_ERROR, _("Failed to check initiator: %s"), initiators[i]);
            return -1;
        }

        if (matched) {
            cInitiators[conflictIndex] = g_strdup(initiators[i]);
            conflictIndex++;
        }
    }

    *nConflicts = conflictIndex;
    *conflictInitiators = g_steal_pointer(&cInitiators);

    return 0;
}

/* ==== lun common === */
static allockLunPtr
allockLunNew(char *target, int lunId)
{
    allockLunPtr res = NULL;

    if (VIR_ALLOC_VAR(res, allockLunPtr, 1) < 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Failed to allocate memory for lun"));
        return NULL;
    }

    if (lunId == 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Invalid iscsi lun id 0"));
        goto error;
    }
    res->lunId = lunId;

    res->targetName = g_strdup(target);

    return res;

error:
    allockLunFree(res);
    return NULL;
}

static void
allockLunFree(allockLunPtr lun)
{
    if (lun == NULL) {
        return;
    }
    VIR_FREE(lun->targetName);
    VIR_FREE(lun->assignedInitiator);
    VIR_FREE(lun);
}

/*
 * virIscsiConvertPathToLun:
 * Convert iscsi path to lun struct, we parse out the target name
 * and lun id, the example iscsi path is like:
 * iqn.2016-02.com.smartx:system:<target name>/<lun id>
 *
 */
static allockLunPtr
allockLunNewFromIscsiPath(const char *iscsiPath)
{
    allockLunPtr res = NULL;
    char **pathInfoList = NULL;
    char **iqnInfoList = NULL;
    char *iqnTarget = NULL;
    char *lunIdStr = NULL;
    char *target = NULL;

    if (!(pathInfoList = virStringSplit(iscsiPath, "/", 2))) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Invalid iscsi path: %s"), iscsiPath);
        return NULL;
    }

    iqnTarget = pathInfoList[0];

    iqnInfoList = virStringSplit(iqnTarget, ":", 3);
    if (iqnInfoList == NULL) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Invalid iscsi path: %s"), iscsiPath);
        goto cleanup;
    }

    lunIdStr = pathInfoList[1];
    target = iqnInfoList[2];

    if (lunIdStr == NULL || target == NULL) {
        virReportError(VIR_ERR_INTERNAL_ERROR,
                       _("Invalid iscsi lun info: %s %s"),
                       NULLSTR(lunIdStr),
                       NULLSTR(target));
        goto cleanup;
    }

    res = allockLunNew(target, atoi(lunIdStr));

cleanup:
    virStringListFree(pathInfoList);
    virStringListFree(iqnInfoList);
    return res;
}

/* ==== allock helper functions === */

static void
allockPrivFree(allockPrivatePtr priv)
{
    if (priv == NULL) {
        return;
    }
    VIR_FREE(priv->vm_name);

    for (size_t i = 0; i < priv->res_count; i++) {
        allockLunFree(priv->res_args[i]);
    }

    VIR_FREE(priv);
}

static bool
allockIsVhostEnabled(void)
{
    FILE *fp = NULL;
    /*
       The vhost config file should always contain
       true or false, so we just use an arbitrary
       amount of buffer which is enough.
     */
    char content[8];
    bool ret = false;

    /*
       In upgraded smtxos from old version, this file
       does not exist, so we see it as disabled.
     */
    if (access(g_vhostMarkFile, R_OK) == -1) {
        if (errno == ENOENT) {
            return false;
        }

        VIR_ERROR(_("failed to access %s: %s"), g_vhostMarkFile, strerror(errno));
        abort();
    }

    fp = fopen(g_vhostMarkFile, "r");
    /*
        Even though the file does not exist, the access above
        should handle it. So we do not expect any situation to
        cause the fopen to return NULL and keep the program still
        running.
    */
    if (fp == NULL) {
        VIR_ERROR(_("failed to read %s: %s"), g_vhostMarkFile, strerror(errno));
        abort();
    }

    if (fgets(content, sizeof(content), fp) == NULL) {
        VIR_ERROR(_("error when reading %s"), g_vhostMarkFile);
        abort();
    }

    ret = STREQLEN(content, "true", 4);

    fclose(fp);
    return ret;
}

static bool
allockIsAllowListDriveVhost(zbsAllowListDriverPtr zbsAllowListDrv)
{
    return STREQ(zbsAllowListDrv->driverType, "ZBS_VHOST");
}

/* === allock lun functions === */

static bool
allockLunIsOpenOwnership(const zbsLunAllowListPtr zbsLunInfo)
{
    return STREQ(zbsLunInfo->allowedInitiators, "*/*");
}

static bool
allockLunIsEmptyOwnership(const zbsLunAllowListPtr zbsLunInfo)
{
    return STREQ(zbsLunInfo->allowedInitiators, "");
}

static bool
allockLunIsExpectedOwnership(const zbsLunAllowListPtr zbsLunInfo, const char *expectedInitiator)
{
    return STREQ(zbsLunInfo->allowedInitiators, expectedInitiator);
}

static bool
allockLunIsSingleAccess(zbsLunAllowListPtr zbsLunInfo)
{
    return zbsLunInfo->singleAccess;
}

static bool
allockLunIsStatusSafe(zbsLunAllowListPtr zbsLunInfo)
{
    return (allockLunIsOpenOwnership(zbsLunInfo) || allockLunIsSingleAccess(zbsLunInfo));
}

static bool
allockLunIsExpectedSharedOwnership(const zbsLunAllowListPtr zbsLunInfo,
                                   const char *expectedInitiator)
{
    return virStringMatch(zbsLunInfo->allowedInitiators, expectedInitiator);
}

/*
 * allockLunCheckSharedOwnershipSafe:
 * Check if current shared lun is in safe state, which means, there should
 * be no conflict initiator against "myInitiator" in the allow list.
 *
 * return: 0 for ownership safe, -1 for ownership unsafe, -2 is check failed.
 */
static int
allockLunCheckSharedOwnershipSafe(const zbsLunAllowListPtr zbsLunInfo, const char *myInitiator)
{
    int nConflict;
    VIR_AUTOSTRINGLIST conflictInitiators = NULL;
    g_autofree char *allInitiators = NULL;

    if (allockLunIsOpenOwnership(zbsLunInfo)) {
        return 0;
    }

    if (initiatorExtractConflictInitiators(
            myInitiator, zbsLunInfo->allowedInitiators, &nConflict, &conflictInitiators) < 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR,
                       "%s",
                       _("CheckSharedOwnership: Failed to extract conflict initiator"));
        return -2;
    }

    if (nConflict > 0) {
        allInitiators = virStringListJoin((const char **)conflictInitiators, ",");
        virReportError(VIR_ERR_INTERNAL_ERROR,
                       _("CheckSharedOwnership: conflict initiator exists: %s"),
                       allInitiators);
        return -1;
    }

    return 0;
}

static int
allockLunAcquirePowerOn(const allockLunPtr lun, zbsAgentPtr agent)
{
    int ret = -1;
    zbsLunAllowListPtr currentLunInfo = lun->allowListDriver->zbsLunGetAllowList(agent, lun);

    if (currentLunInfo == NULL) {
        virReportError(VIR_ERR_INTERNAL_ERROR, "%s", _("Acquire power-on: Failed to get lun info"));
        return -1;
    }

    if (lun->shared) {
        if (allockLunIsOpenOwnership(currentLunInfo)) {
            /*
                Shared lun with open ownership cannot be handled by allock because
                it cannot really know who else is using the lun now, so just skip
                handling, same to other cases.
            */
            ret = 0;
            goto cleanup;
        }

        if (allockLunCheckSharedOwnershipSafe(currentLunInfo, lun->assignedInitiator) != 0) {
            VIR_ERROR(_("%s"), "Failed to check shared ownership");
            goto cleanup;
        }

        if (!allockLunIsExpectedSharedOwnership(currentLunInfo, lun->assignedInitiator)) {
            virReportError(VIR_ERR_INTERNAL_ERROR,
                           _("Acquire power-on: Expected owner %s is not in owners: %s"),
                           lun->assignedInitiator,
                           currentLunInfo->allowedInitiators);
            goto cleanup;
        }
    } else {
        if (!allockLunIsStatusSafe(currentLunInfo)) {
            virReportError(
                VIR_ERR_INTERNAL_ERROR, "%s", _("Acquire power-on: Lun is not single access"));
            goto cleanup;
        }

        if (!allockLunIsExpectedOwnership(currentLunInfo, lun->assignedInitiator)) {
            virReportError(VIR_ERR_INTERNAL_ERROR,
                           _("Acquire power-on: Expected owner %s, current owner: %s"),
                           lun->assignedInitiator,
                           currentLunInfo->allowedInitiators);
            goto cleanup;
        }
    }

    ret = 0;

cleanup:
    zbsLunAllowListFree(currentLunInfo);
    return ret;
}

/**
 * allockLunAcquireSafe:
 *
 * Does not require the owner to be itself, but has to assure
 * that there is no conflict owner. This currently happens in two
 * scenarios:
 * 1. End of migration: when dest vm resumes itself, it has to
 *    assure that the src vm has released itself.
 * 2. Disk attach: requires that there is no current owner of
 *    the attached disk.
 */

static int
allockLunAcquireSafe(const allockLunPtr lun, zbsAgentPtr agent)
{
    int ret = -1;
    zbsLunAllowListPtr currentLunInfo = lun->allowListDriver->zbsLunGetAllowList(agent, lun);

    if (currentLunInfo == NULL) {
        virReportError(VIR_ERR_INTERNAL_ERROR, "%s", _("Acquire safe: Failed to get lun info"));
        return -1;
    }

    if (lun->shared) {
        if (allockLunIsOpenOwnership(currentLunInfo)) {
            VIR_WARN(_("%s"), "Acquire safe: Skip shared lun with open ownership");
            ret = 0;
            goto cleanup;
        }

        if (allockLunCheckSharedOwnershipSafe(currentLunInfo, lun->assignedInitiator) != 0) {
            VIR_ERROR(_("%s"), "Failed to check shared ownership");
            goto cleanup;
        }

        if (allockLunIsExpectedSharedOwnership(currentLunInfo, lun->assignedInitiator)) {
            ret = 0;
            goto cleanup;
        }
    } else {
        if (!allockLunIsStatusSafe(currentLunInfo)) {
            virReportError(
                VIR_ERR_INTERNAL_ERROR, "%s", _("Acquire safe: Lun is not single access"));
            goto cleanup;
        }

        /* if the caller already holds the ownership, then succeeds */
        if (allockLunIsOpenOwnership(currentLunInfo) ||
            allockLunIsExpectedOwnership(currentLunInfo, lun->assignedInitiator)) {
            ret = 0;
            goto cleanup;
        }

        if (!allockLunIsEmptyOwnership(currentLunInfo)) {
            virReportError(VIR_ERR_INTERNAL_ERROR,
                           _("Acquire safe: Lun is not released, current owner: %s"),
                           currentLunInfo->allowedInitiators);
            goto cleanup;
        }
    }

    if (lun->allowListDriver->zbsLunAppendInitiatorToAllowListSafe(
            agent, lun, lun->assignedInitiator) < 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR,
                       _("Acquire safe: Failed to add initiator %s"),
                       lun->assignedInitiator);
        goto cleanup;
    }

    ret = 0;

cleanup:
    zbsLunAllowListFree(currentLunInfo);
    return ret;
}

/**
 * allockLunAcquireNormalResume:
 *
 * In this case, vm has to be assure that it is still the owner of the lun,
 * otherwise it refuses to acquire. Since a normal puase will not really
 * release the lock either. This strategy is simply to prevent a vm resume's
 * success after a its lun has been "stolen" by other conflict owner during
 * the pause.
 */
static int
allockLunAcquireNormalResume(allockLunPtr lun, zbsAgentPtr agent)
{
    int ret = -1;
    zbsLunAllowListPtr currentLunInfo = lun->allowListDriver->zbsLunGetAllowList(agent, lun);

    if (currentLunInfo == NULL) {
        virReportError(
            VIR_ERR_INTERNAL_ERROR, "%s", _("Acquire normal resume: Failed to get lun info"));
        return -1;
    }

    if (allockLunIsOpenOwnership(currentLunInfo)) {
        ret = 0;
        goto cleanup;
    }

    if (lun->shared) {
        if (allockLunCheckSharedOwnershipSafe(currentLunInfo, lun->assignedInitiator) != 0) {
            VIR_ERROR(_("%s"), "Failed to check shared ownership");
            goto cleanup;
        }

        if (!allockLunIsExpectedSharedOwnership(currentLunInfo, lun->assignedInitiator)) {
            virReportError(VIR_ERR_INTERNAL_ERROR,
                           _("Acquire normal resume: Expected owner %s is not in owners: %s"),
                           lun->assignedInitiator,
                           currentLunInfo->allowedInitiators);
            goto cleanup;
        }
    } else {
        if (!allockLunIsExpectedOwnership(currentLunInfo, lun->assignedInitiator)) {
            virReportError(VIR_ERR_INTERNAL_ERROR,
                           _("Acquire normal resume: Expected owner %s, current owner: %s"),
                           lun->assignedInitiator,
                           currentLunInfo->allowedInitiators);
            goto cleanup;
        }
    }

    ret = 0;

cleanup:
    zbsLunAllowListFree(currentLunInfo);
    return ret;
}

/**
 * allockLunReleaseSafe:
 *
 * VM removes its ownership of lun.
 * 1. In the end of live migration, src calls it to release its
 *    ownership.
 * 2. When detach disk, vm also cleans its ownership on corresponding
 *    lun.
 */
static int
allockLunReleaseSafe(const allockLunPtr lun, zbsAgentPtr agent)
{
    int ret = -1;
    zbsLunAllowListPtr currentLunInfo = lun->allowListDriver->zbsLunGetAllowList(agent, lun);

    if (currentLunInfo == NULL) {
        virReportError(VIR_ERR_INTERNAL_ERROR, "%s", _("Release safe: Failed to get lun info"));
        return -1;
    }

    if (allockLunIsOpenOwnership(currentLunInfo)) {
        ret = 0;
        goto cleanup;
    }

    if (!lun->shared) {
        if (!allockLunIsStatusSafe(currentLunInfo)) {
            virReportError(
                VIR_ERR_INTERNAL_ERROR, "%s", _("Release safe: Lun is not single access"));
            goto cleanup;
        }
    }

    if (lun->allowListDriver->zbsLunRemoveInitiatorFromAllowList(agent, lun, lun->assignedInitiator) <
        0) {
        virReportError(VIR_ERR_INTERNAL_ERROR,
                       _("Release safe: Failed to remove the initiator %s"),
                       lun->assignedInitiator);
        goto cleanup;
    }

    ret = 0;

cleanup:
    zbsLunAllowListFree(currentLunInfo);
    return ret;
}

/**
 * allockLunPrepareStartPowerOnShared:
 *
 * VM gets shared and non-conflict ownership for itself.
 */
static int
allockLunPrepareStartPowerOnShared(allockLunPtr lun,
                                   zbsLunAllowListPtr zbsLunInfo,
                                   zbsAgentPtr agent)
{
    VIR_AUTOSTRINGLIST cInitiators = NULL;
    int nCInitiators;

    if (allockLunIsOpenOwnership(zbsLunInfo)) {
        VIR_WARN("Prepare Power-on: Shared Lun %s/%d is open ownership, ignore",
                 lun->targetName,
                 lun->lunId);
        return 0;
    }

    if (initiatorExtractConflictInitiators(
            lun->assignedInitiator, zbsLunInfo->allowedInitiators, &nCInitiators, &cInitiators) <
        0) {
        virReportError(VIR_ERR_INTERNAL_ERROR,
                       "%s",
                       _("Prepare Power-on: Failed to remove conflict initiators, "
                         "target=%s lun=%u"),
                       lun->targetName,
                       lun->lunId);
        return -1;
    }

    for (int i = 0; i < nCInitiators; i++) {
        VIR_INFO("Prepare Power-on: Removing the conflict initiator %s", cInitiators[i]);

        if (lun->allowListDriver->zbsLunRemoveInitiatorFromAllowList(agent, lun, cInitiators[i]) < 0) {
            virReportError(VIR_ERR_INTERNAL_ERROR,
                           _("Prepare Power-on: Failed to remove the initiator %s"),
                           lun->assignedInitiator);
            return -1;
        }
    }

    if (lun->allowListDriver->zbsLunAppendInitiatorToAllowListSafe(
            agent, lun, lun->assignedInitiator) < 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR,
                       _("Prepare Power-on: Failed to add the initiator %s"),
                       lun->assignedInitiator);
        return -1;
    }

    return 0;
}

/**
 * allockLunPrepareStartPowerOn:
 *
 * VM gets the exclusive onwership for itself to be launched.
 */
static int
allockLunPrepareStartPowerOn(allockLunPtr lun, zbsAgentPtr agent)
{
    int ret = -1;
    zbsLunAllowListPtr currentLunInfo = lun->allowListDriver->zbsLunGetAllowList(agent, lun);

    if (currentLunInfo == NULL) {
        virReportError(VIR_ERR_INTERNAL_ERROR, "%s", _("Prepare Power-on: Failed to get lun info"));
        return -1;
    }

    if (lun->shared) {
        if (allockLunPrepareStartPowerOnShared(lun, currentLunInfo, agent) < 0) {
            VIR_ERROR(_("Prepare Power-on: Failed allockLunPrepareStartPowerOnShared"));
            goto cleanup;
        }
    } else {
        if (!allockLunIsStatusSafe(currentLunInfo)) {
            virReportError(
                VIR_ERR_INTERNAL_ERROR, "%s", _("Prepare Power-on: Lun is not single access"));
            goto cleanup;
        }

        if (lun->allowListDriver->zbsLunUpdateInitiatorToAllowListForce(
                agent, lun, lun->assignedInitiator) < 0) {
            virReportError(VIR_ERR_INTERNAL_ERROR,
                           _("Prepare Power-on: Failed to update the initiator %s"),
                           lun->assignedInitiator);
            goto cleanup;
        }
    }

    ret = 0;

cleanup:
    zbsLunAllowListFree(currentLunInfo);
    return ret;
}

/**
 * allockLunPrepareStartInMigration:
 *
 * In-migrating vm needs to temporarily adds itself to allow list
 * in order to get the vm process launched.
 *
 */
static int
allockLunPrepareStartInMigration(allockLunPtr lun, zbsAgentPtr agent)
{
    int ret = -1;
    zbsLunAllowListPtr currentLunInfo = lun->allowListDriver->zbsLunGetAllowList(agent, lun);

    if (currentLunInfo == NULL) {
        virReportError(
            VIR_ERR_INTERNAL_ERROR, "%s", _("Prepare In-migration: Failed to get lun info"));
        return -1;
    }

    if (allockLunIsOpenOwnership(currentLunInfo)) {
        ret = 0;
        goto cleanup;
    }

    if (!lun->shared) {
        if (!allockLunIsStatusSafe(currentLunInfo)) {
            virReportError(
                VIR_ERR_INTERNAL_ERROR, "%s", _("Prepare In-migration: Lun is not single access"));
            goto cleanup;
        }
    }

    /*
        Currently the reason for PrepareStartInMigration is that dest
        QEMU process will communicate with lun through ISCSI login
        when being launched, if its initiator is not in the LUN's allow
        list, the launch will fail. So we need to temporarily add its
        initiator into the allow list in the PrepareStartInMigration and
        remove it in PrepareEndInMigration which is called after the process
        is launched.

        But in vhost mode, the way that QEMU communicates with backend storage
        is not ISCSI, so there is no ISCSI login in the launch of dest process.
        Therefore, we can just get rid off PrepareStart/EndInMigration.
    */
    if (allockIsAllowListDriveVhost(lun->allowListDriver)) {
        ret = 0;
        goto cleanup;
    }

    if (!lun->shared) {
        if (lun->allowListDriver->zbsLunSetAllowListSingeAccess(agent, lun, false) < 0) {
            virReportError(VIR_ERR_INTERNAL_ERROR,
                           "%s",
                           _("Prepare In-migration: Failed to set single access off"));
            goto cleanup;
        }
    }

    if (lun->allowListDriver->zbsLunAppendInitiatorToAllowListSafe(
            agent, lun, lun->assignedInitiator) < 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR,
                       _("Prepare In-migration: Failed to add the initiator %s"),
                       lun->assignedInitiator);
        goto rollback;
    }

    ret = 0;

cleanup:
    zbsLunAllowListFree(currentLunInfo);
    return ret;

rollback:
    if (!lun->shared) {
        VIR_ERROR(_("%s"), "Prepare In-migration: Failed, rolling back lun status");

        if (lun->allowListDriver->zbsLunSetAllowListSingeAccess(agent, lun, true) < 0) {
            virReportError(
                VIR_ERR_INTERNAL_ERROR, "%s", _("Prepare In-migration: Failed to set initiator \
                           and could not rollback single access"));
        }
    }

    zbsLunAllowListFree(currentLunInfo);
    return ret;
}

/**
 * allockLunPrepareEndInMigration:
 *
 * Corresponding to allockLunPrepareStartInMigration, this function
 * removes the vm's temporary ownership and set the lun back to original
 * expected status after the vm process is launched.
 *
 */
static int
allockLunPrepareEndInMigration(allockLunPtr lun, zbsAgentPtr agent)
{
    zbsLunAllowListPtr currentLunInfo = NULL;
    int ret = -1;

    if (allockIsAllowListDriveVhost(lun->allowListDriver)) {
        return 0;
    }

    currentLunInfo = lun->allowListDriver->zbsLunGetAllowList(agent, lun);

    if (currentLunInfo == NULL) {
        virReportError(VIR_ERR_INTERNAL_ERROR, "%s", _("End in-migration: Failed to get lun info"));
        return -1;
    }

    if (allockLunIsOpenOwnership(currentLunInfo)) {
        ret = 0;
        goto cleanup;
    }

    if (!lun->shared) {
        if (allockLunIsSingleAccess(currentLunInfo)) {
            virReportError(
                VIR_ERR_INTERNAL_ERROR, "%s", _("End in-migration: Lun is single access"));
            goto cleanup;
        }
    }

    if (lun->allowListDriver->zbsLunRemoveInitiatorFromAllowList(agent, lun, lun->assignedInitiator)) {
        virReportError(VIR_ERR_INTERNAL_ERROR,
                       _("End in-migration: Failed to remove the initiator %s"),
                       lun->assignedInitiator);
        goto cleanup;
    }

    if (!lun->shared) {
        if (lun->allowListDriver->zbsLunSetAllowListSingeAccess(agent, lun, true) < 0) {
            virReportError(VIR_ERR_INTERNAL_ERROR,
                           _("%s"),
                           "End in-migration: Failed to set single access on");
            goto cleanup;
        }
    }

    ret = 0;

cleanup:
    zbsLunAllowListFree(currentLunInfo);
    return ret;
}

/* === allock core functions === */

static int
allockLunAcquire(unsigned int flags, allockLunPtr lun, zbsAgentPtr agent)
{
    int ret;

    VIR_INFO("Acquire lun: target=%s, lun=%d, initiator=%s",
             lun->targetName,
             lun->lunId,
             lun->assignedInitiator);

    if (flags & VIR_LOCK_MANAGER_ACQUIRE_MIGRATION_RESUME ||
        flags & VIR_LOCK_MANAGER_ACQUIRE_DISK_ATTACH) {
        /* Acquire works on dest migration resume */
        ret = allockLunAcquireSafe(lun, agent);
    } else if (flags & VIR_LOCK_MANAGER_ACQUIRE_RESUME) {
        /* Acquire works on normal vm resume */
        ret = allockLunAcquireNormalResume(lun, agent);
    } else {
        /* Other cases go through power on */
        ret = allockLunAcquirePowerOn(lun, agent);
    }

    return ret;
}

static int
allockLunRelease(unsigned int flags, allockLunPtr lun, zbsAgentPtr agent)
{
    int ret;

    VIR_INFO("Release lun: target=%s, lun=%d, initiator=%s",
             lun->targetName,
             lun->lunId,
             lun->assignedInitiator);

    if (flags & VIR_LOCK_MANAGER_RELEASE_MIGRATION_PAUSE ||
        flags & VIR_LOCK_MANAGER_RELEASE_DISK_DETACH) {
        /* Release works on migration src vm pause */
        ret = allockLunReleaseSafe(lun, agent);
    } else {
        /* Other cases nothing to handle */
        ret = 0;
    }

    return ret;
}

static int
allockLunPrepareStart(unsigned int flags, allockLunPtr lun, zbsAgentPtr agent)
{
    int ret;

    VIR_INFO("PrepareStart lun: target=%s, lun=%d, initiator=%s",
             lun->targetName,
             lun->lunId,
             lun->assignedInitiator);

    if (flags & VIR_LOCK_MANAGER_IN_MIGRATING) {
        /* Prepare works for incoming migration process on dest host */
        ret = allockLunPrepareStartInMigration(lun, agent);
    } else if (flags & VIR_LOCK_MANAGER_STARTUP) {
        /* Prepare works for newly launched vm process */
        ret = allockLunPrepareStartPowerOn(lun, agent);
    } else {
        /* Other cases nothing to handle */
        ret = 0;
    }

    return ret;
}

static int
allockLunPrepareEnd(unsigned int flags, allockLunPtr lun, zbsAgentPtr agent)
{
    int ret;

    VIR_INFO("PrepareEnd lun: target=%s, lun=%d, initiator=%s",
             lun->targetName,
             lun->lunId,
             lun->assignedInitiator);

    if (flags & VIR_LOCK_MANAGER_IN_MIGRATING) {
        /* For incoming migration process preparation finished
           on dest host */
        ret = allockLunPrepareEndInMigration(lun, agent);
    } else {
        /* Other cases nothing to handle */
        ret = 0;
    }

    return ret;
}

/* === allock core functions === */

/**
 * allockInit:
 *
 * Only called once during the lifecycle of whole libvirt process.
 * It loads the configurations does global initialization for zbs
 * agent instance shared among threads.
 * Returns -1 for initialization failure, 0 for success.
 */
static int
allockInit(unsigned int version ATTRIBUTE_UNUSED,
           const char *configFile ATTRIBUTE_UNUSED,
           unsigned int flags ATTRIBUTE_UNUSED)
{
    bool isZbsDisag = false;

    isZbsDisag = zbsIsDisaggregatedArch();
    if (isZbsDisag && !zbsIsRemoteStorageConfigured()) {
        /*
            We allow the situation when no storage is configured
            by doing nothing. In this case, ZBS agent is not initialized,
            any further operation of disk lock will fail.
        */
        VIR_INFO("Running in disaggregated mode, remote storage is"
                 " not configured yet, skip initializing ZBS");
        return 0;
    }

    if (VIR_ALLOC(g_allockZbsAgentList) < 0) {
        goto error;
    }
    if (zbsAgentListInitialize(g_allockZbsAgentList, isZbsDisag) < 0) {
        goto error;
    }

    if (allockIsVhostEnabled()) {
        VIR_INFO("Allock initialize in zbs vhost mode");
        g_defaultZbsAllowListDrv = &zbsVhostAllowListDrv;
    } else {
        VIR_INFO("Allock initialize in zbs iscsi mode");
        g_defaultZbsAllowListDrv = &zbsIscsiAllowListDrv;
    }
    VIR_INFO("Initialized Allock");

    return 0;

error:
    allockDeinit();
    return -1;
}

/**
 * allockDeinit:
 *
 * Safely clean some stuff at the end of the process.
 */
static int
allockDeinit(void)
{
    VIR_INFO("Deinitializing...");
    if (!g_allockZbsAgentList) {
        return 0;
    }

    for (int i = 0; i < g_allockZbsAgentList->nagents; i++) {
        virObjectUnref(g_allockZbsAgentList->agents[i]);
    }

    VIR_FREE(g_allockZbsAgentList);

    return 0;
}

/**
 * allockNew:
 * This is the first to be called when libvirt tries to do a lock
 * operation on the domain.
 *
 * @lock: The lock plugin reference of libvirt, lock->privateData will
 * be passed to allockAddResource which is called
 * afterward.
 * @params: Any other domain-wide param (like domain name).
 *
 * Returns -1 on failure, 0 on success.
 */
static int
allockNew(virLockManagerPtr lock,
          unsigned int type,
          size_t nparams,
          virLockManagerParamPtr params,
          unsigned int flags ATTRIBUTE_UNUSED)
{
    virLockManagerParamPtr param = NULL;
    allockPrivatePtr priv = NULL;

    if (g_allockZbsAgentList == NULL) {
        virReportError(VIR_ERR_INTERNAL_ERROR, "%s", _("Zbs Agent list is not initialized"));
        return -1;
    }

    if (type != VIR_LOCK_MANAGER_OBJECT_TYPE_DOMAIN) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Unsupported object type %d"), type);
        return -1;
    }

    if (VIR_ALLOC(priv) < 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Failed to allocate memory for lock data"));
        return -1;
    }

    for (size_t i = 0; i < nparams; i++) {
        param = &params[i];
        if (STREQ(param->key, "name")) {
            priv->vm_name = g_strdup(param->value.str);
            break;
        }
    }

    if (priv->vm_name == NULL) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("No vm name found"));
        goto error;
    }

    lock->privateData = priv;

    VIR_INFO("New Allock for %s", priv->vm_name);
    return 0;

error:
    allockPrivFree(priv);
    return -1;
}

/**
 * allockFree
 * Called at the end of each lock operation to free the data that
 * will not be needed anymore.
 *
 */
static void
allockFree(virLockManagerPtr lock ATTRIBUTE_UNUSED)
{
    allockPrivatePtr priv = lock->privateData;
    allockPrivFree(priv);
}

/**
 * allockAddResource:
 * This function is called for each disk on a domain, it primarily parses
 * the desired info from a disk's iscsi path and aggregate to a allockLunPtr.
 * Also it parses the initiator name set for this lun to determine some
 *
 * @lock: See allockNew
 * @name: The path of the disk (libvirt's native behavior)
 * @params: Some other params that we want to pass to lock service.
 *
 * Returns -1 on failure, 0 on success.
 */
static int
allockAddResource(virLockManagerPtr lock,
                  unsigned int type ATTRIBUTE_UNUSED,
                  const char *name,
                  size_t nparams,
                  virLockManagerParamPtr params,
                  unsigned int flags ATTRIBUTE_UNUSED)
{
    allockPrivatePtr priv = lock->privateData;
    virLockManagerParamPtr param;
    int sharedRet;
    bool shared;
    char *initiator = NULL;
    allockLunPtr lun = NULL;
    char *storageIP = NULL;
    int bus = -1;
    int ret = -1;

    if (flags & VIR_LOCK_MANAGER_RESOURCE_READONLY) {
        return 0;
    }

    /* Ignore non-iscsi path, such as NFS */
    if (!iscsiIsPathValid(name)) {
        return 0;
    }

    if (priv->res_count == LUN_ARRAY_BUFLEN) {
        virReportError(
            VIR_ERR_INTERNAL_ERROR, _("Max resource number %d reached"), LUN_ARRAY_BUFLEN);
        return -1;
    }

    for (size_t i = 0; i < nparams; i++) {
        param = &params[i];
        if (STREQ(param->key, "initiator.iqn")) {
            initiator = g_strdup(param->value.cstr);
        }

        if (STREQ(param->key, "host.name")) {
            storageIP = g_strdup(param->value.cstr);
        }

        if (STREQ(param->key, "bus")) {
            bus = param->value.iv;
        }
    }

    if (initiator == NULL) {
        /* Resource with no initiator specified, such as legacy running vm,
           should be ignored */
        return 0;
    }

    sharedRet = initiatorParseSharedStatus(initiator, &shared);
    if (sharedRet < 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Invalid initiator name: %s"), initiator);
        ret = -1;
        goto cleanup;
    }

    if (storageIP == NULL) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("No storage IP specified"));
        ret = -1;
        goto cleanup;
    }

    lun = allockLunNewFromIscsiPath(name);
    if (lun == NULL) {
        VIR_ERROR(_("Failed to convert iscsi path %s to lun"), name);
        ret = -1;
        goto cleanup;
    }

    lun->assignedInitiator = initiator;
    lun->shared = shared;
    lun->storageIP = storageIP;
    lun->allowListDriver = g_defaultZbsAllowListDrv;

    /* On vhost, disk with IDE bus still goes with zbs_iscsi */
    if (allockIsAllowListDriveVhost(g_defaultZbsAllowListDrv)) {
        if (bus == -1) {
            VIR_ERROR(_("Failed to get bus type for disk, initiator=%s, bus=%d"), initiator, bus);
            ret = -1;
            goto cleanup;
        }

        if (bus == VIR_DOMAIN_DISK_BUS_IDE) {
            lun->allowListDriver = &zbsIscsiAllowListDrv;
        }
    }

    priv->res_args[priv->res_count] = lun;
    priv->res_count++;

    VIR_INFO("Added resource target=%s lun_id=%d owner=%s, shared=%d, storage_ip=%s, bus=%d, allowlist_driver=%s",
             lun->targetName,
             lun->lunId,
             lun->assignedInitiator,
             lun->shared,
             lun->storageIP,
             bus,
             lun->allowListDriver->driverType);

    return 0;

cleanup:
    VIR_FREE(initiator);
    VIR_FREE(storageIP);
    return ret;
}

/**
 * allockAcquire:
 * This function does the actual job of lock acquring, based on different
 * operation contexts, it chooses different strategies.
 *
 * @lock: The saved state of this lock acquisition, states were generated
 * from previous allockAddResource.
 * @flags: Used to pass operation contexts for this acquisition.
 *
 * Returns 0 for success, -1 for failure.
 */
static int
allockAcquire(virLockManagerPtr lock,
              const char *state ATTRIBUTE_UNUSED,
              unsigned int flags,
              virDomainLockFailureAction action ATTRIBUTE_UNUSED,
              int *fd ATTRIBUTE_UNUSED)
{
    allockPrivatePtr priv = lock->privateData;
    zbsAgentPtr agent;
    int ret = -1;

    VIR_INFO(
        "Acquire locks for vm %s, flags=%d, res_count=%d", priv->vm_name, flags, priv->res_count);

    if (flags & VIR_LOCK_MANAGER_ACQUIRE_REGISTER_ONLY) {
        return 0;
    }

    CALL_ALLOCK_FUNCTION_WITH_ZBS_AGENT(allockLunAcquire, priv, flags);

    if (ret < 0) {
        VIR_ERROR("Acquire failed");
        return ret;
    }
    VIR_INFO("Acquire succeeded");

    return 0;
}

static int
allockRelease(virLockManagerPtr lock, char **state ATTRIBUTE_UNUSED, unsigned int flags)
{
    allockPrivatePtr priv = lock->privateData;
    zbsAgentPtr agent;
    int ret = -1;

    VIR_INFO(
        "Release locks for vm %s, flags=%d, res_count=%d", priv->vm_name, flags, priv->res_count);

    CALL_ALLOCK_FUNCTION_WITH_ZBS_AGENT(allockLunRelease, priv, flags);

    if (ret < 0) {
        VIR_ERROR("Release failed");
        return ret;
    }
    VIR_INFO("Release succeeded");

    return 0;
}

/**
 * allockInquire
 *
 * This interface is made in order to help libvirt get the lock state during VM
 * migration and save it in the domain xml, allock does not have concept of lock
 * state in xml, so it does not need lock state inquiry either.
 *
 */
static int
allockInquire(virLockManagerPtr lock ATTRIBUTE_UNUSED,
              char **state ATTRIBUTE_UNUSED,
              unsigned int flags ATTRIBUTE_UNUSED)
{
    return 0;
}

/**
 * allockPrepareStart
 *
 * This interface is called in order to help domain preparation.
 * Two cases will call it:
 *  1. Start a new vm process;
 *  2. Migration dest starts a incoming vm process.
 *
 */
static int
allockPrepareStart(virLockManagerPtr lock, unsigned int flags)
{
    allockPrivatePtr priv = lock->privateData;
    zbsAgentPtr agent;
    int ret = -1;

    VIR_INFO("PrepareStart locks for vm %s, flags=%d, res_count=%d",
             priv->vm_name,
             flags,
             priv->res_count);

    CALL_ALLOCK_FUNCTION_WITH_ZBS_AGENT(allockLunPrepareStart, priv, flags);

    if (ret < 0) {
        VIR_ERROR("PrepareStart failed");
        return ret;
    }
    VIR_INFO("PrepareStart succeeded");

    return 0;
}

/**
 * allockPrepareEnd
 *
 * This interface is called in order to end domain preparation.
 * It will be called every time after allockPrepareStart is called.
 *
 */
static int
allockPrepareEnd(virLockManagerPtr lock, unsigned int flags)
{
    allockPrivatePtr priv = lock->privateData;
    zbsAgentPtr agent;
    int ret = -1;

    VIR_INFO("PrepareEnd locks for vm %s, flags=%d, res_count=%d",
             priv->vm_name,
             flags,
             priv->res_count);

    CALL_ALLOCK_FUNCTION_WITH_ZBS_AGENT(allockLunPrepareEnd, priv, flags);

    if (ret < 0) {
        VIR_ERROR("PrepareEnd failed");
        return ret;
    }
    VIR_INFO("PrepareEnd succeeded");

    return 0;
}

virLockDriver virLockDriverImpl = {
    .version = VIR_LOCK_MANAGER_VERSION,

    .flags = VIR_LOCK_MANAGER_USES_STATE,

    .drvInit = allockInit,
    .drvDeinit = allockDeinit,

    .drvNew = allockNew,
    .drvFree = allockFree,

    .drvAddResource = allockAddResource,

    .drvAcquire = allockAcquire,
    .drvRelease = allockRelease,
    .drvInquire = allockInquire,

    .drvPrepareStart = allockPrepareStart,
    .drvPrepareEnd = allockPrepareEnd,
};
