/*
 * allock.h: Allock is a lock driver for libvirt, based on ZBS
 * permission access control.
 *
 * Copyright (C) 2022 SmartX, Inc.
 *
 */

#ifndef __ALLOCK_H__
#define __ALLOCK_H__

#include "internal.h"

typedef struct _zbsAllowListDriver zbsAllowListDriver;
typedef zbsAllowListDriver *zbsAllowListDriverPtr;

/* Store the dynamic info set by ELF */
typedef struct _allockLun allockLun;
typedef allockLun *allockLunPtr;

struct _allockLun {
    unsigned int lunId;
    char *targetName;
    char *assignedInitiator;
    bool shared;
    char* storageIP;
    zbsAllowListDriverPtr allowListDriver;
};

#endif /* __ALLOCK_H__ */
