/*
 * zbs_allow_list.c: Implementation of zbs_allow_list library.
 *
 * Copyright (C) 2022 SmartX, Inc.
 *
 */
#include <config.h>
#include <glib.h>
#include <stdio.h>

#include "zbs_allow_list.h"

#include "viralloc.h"
#include "virconf.h"
#include "virlog.h"
#include "virstring.h"

#define VIR_FROM_THIS VIR_FROM_LOCKING

VIR_LOG_INIT("locking.zbs_allow_list");

static const char *g_configFile = "/etc/zbs/zbs.conf";
static const char *g_platformFile = "/etc/zbs/platform";
static const char *g_remoteStorageConfigFile = "/etc/elfvirt/storage_cluster.conf";

static virClassPtr zbsAgentClass;

enum ZbsErrorCode {
    ESocketConnect = -100301,
    EProtoAsyncClient = -100330,
    EServiceUnavailable = -100503,
    ENotLeader = -100719,
    ENotReady = -5001,
    ETimedOut = -100012,
    ECancelled = -5011,
    EZKConnectError = -100701,
};

/**
 * zbsLoadInternalServiceAddresses:
 * @addrs:(out): Array of addr string
 * @addrsNum:(out): Num of addrs
 *
 * Reading from /etc/zbs/zbs.conf for configuration
 *
 * Returns 0 on success and -1 on error when configuration file does not exists or if the context
 * format is incorrect.
 **/
static int
zbsLoadInternalServiceAddresses(char ***addrs, int *addrsNum)
{
    virConfPtr conf;
    g_autofree char *addr = NULL;
    int ret = -1;

    *addrsNum = 0;

    if (access(g_configFile, R_OK) == -1) {
        virReportSystemError(errno, _("Unable to access config file %s"), g_configFile);
        return ret;
    }

    conf = virConfReadFile(g_configFile, VIR_CONF_FLAG_ZBS_FORMAT);
    if (conf == NULL) {
        VIR_ERROR(_("Failed to parse %s"), g_configFile);
        return ret;
    }

    if (virConfGetValueString(conf, "zookeeper", &addr) < 0 || !addr) {
        VIR_ERROR(_("%s"), "Failed to find hosts from zbs.conf");
        goto cleanup;
    }

    if (virStringListAdd(addrs, addr) < 0) {
        goto cleanup;
    }
    *addrsNum = 1;
    ret = 0;

cleanup:
    virConfFree(conf);
    return ret;
}

/**
 * zbsLoadExternalServiceAddresses:
 * @addrs:(out): Array of addr string
 * @addrsNum:(out): Num of addrs
 *
 * Reading from /etc/elfvirt/storage_cluster.conf for configuration
 *
 * Returns 0 on success and -1 on error when the context of configuration file format is incorrect.
 *
 * When SMTXELF is newly deployed without being associated with any ZBS cluster,
 * g_remoteStorageConfigFile may not exist, we return success in this scenario.
 * When SMTXELF removes the last associated ZBS cluster and g_remoteStorageConfigFile becomes empty,
 * resulting in an empty 'groups', we also return success.
 **/
static int
zbsLoadExternalServiceAddresses(char ***addrs, int *addrsNum)
{
    g_autoptr(GKeyFile) keyfile;
    g_autoptr(GError) error = NULL;
    VIR_AUTOSTRINGLIST groups = NULL;

    g_autofree gchar *zbsRmoteIp = NULL;
    g_autofree gchar *blockdPort = NULL;
    g_autofree gchar *dataChannelPort = NULL;
    g_auto(virBuffer) addr = VIR_BUFFER_INITIALIZER;

    *addrsNum = 0;

    if (access(g_remoteStorageConfigFile, R_OK) == -1) {
        virReportSystemError(errno, _("Unable to access config file %s"), g_configFile);
        return 0;
    }

    keyfile = g_key_file_new();
    if (!g_key_file_load_from_file(keyfile, g_remoteStorageConfigFile, G_KEY_FILE_NONE, &error)) {
        VIR_ERROR(_("Error loading file %s: %s"), g_remoteStorageConfigFile, error->message);
        return -1;
    }

    groups = g_key_file_get_groups(keyfile, NULL);
    for (gchar **group = groups; *group != NULL; group++) {
        zbsRmoteIp = g_key_file_get_string(keyfile, *group, "ip", &error);
        if (zbsRmoteIp == NULL) {
            VIR_ERROR(_("Failed to find ip from storage_cluster.conf: %s"), error->message);
            return -1;
        }

        blockdPort = g_key_file_get_string(keyfile, *group, "blockd_port", &error);
        if (blockdPort == NULL) {
            VIR_ERROR(_("Failed to find blockd port from storage_cluster.conf: %s"),
                      error->message);
            return -1;
        }

        dataChannelPort = g_key_file_get_string(keyfile, *group, "data_channel_port", &error);
        if (dataChannelPort == NULL) {
            VIR_WARN(_("Failed to find data channel port :%s, in storage_cluster.conf, use 10201 "
                       "by default"),
                     error->message);

            /*
            10201 is ZBS internal IO protocol port, its value does not
            change, but we still try to load it from config file.
            */
            dataChannelPort = g_strdup("10201");
        }

        virBufferAsprintf(&addr, "%s:%s:%s", zbsRmoteIp, dataChannelPort, blockdPort);
        if (virStringListAdd(addrs, virBufferContentAndReset(&addr)) < 0) {
            return -1;
        }
        (*addrsNum)++;
    }

    return 0;
}

bool
zbsIsDisaggregatedArch(void)
{
    FILE *fp = NULL;
    char content[8];
    bool ret = false;

    if (access(g_platformFile, R_OK) == -1) {
        if (errno == ENOENT) {
            return false;
        }

        VIR_ERROR(_("failed to access %s: %d"), g_platformFile, strerror(errno));
        abort();
    }

    fp = fopen(g_platformFile, "r");
    if (fp == NULL) {
        VIR_ERROR(_("failed to read %s: %s"), g_platformFile, strerror(errno));
        abort();
    }

    if (fgets(content, sizeof(content), fp) == NULL) {
        VIR_ERROR(_("error when reading %s"), g_platformFile);
        abort();
    }

    ret = STREQLEN(content, "elf", 3);

    fclose(fp);
    return ret;
}

bool
zbsIsRemoteStorageConfigured(void)
{
    if (access(g_remoteStorageConfigFile, R_OK) == -1) {
        if (errno == ENOENT) {
            return false;
        }

        VIR_ERROR(_("failed to access %s: %d"), g_remoteStorageConfigFile, strerror(errno));
        abort();
    }

    return true;
}

bool
zbsIsErrorRetriable(int error)
{
    bool ret = false;

    switch (error) {
        case ESocketConnect:
        case EProtoAsyncClient:
        case EServiceUnavailable:
        case ENotLeader:
        case ENotReady:
        case ETimedOut:
        case ECancelled:
        case EZKConnectError:
            ret = true;
            break;
        default:
            ret = false;
    }

    return ret;
}

void
zbsLunAllowListFree(zbsLunAllowListPtr lunAllowList)
{
    if (lunAllowList == NULL) {
        return;
    }

    VIR_FREE(lunAllowList->allowedInitiators);
    VIR_FREE(lunAllowList);
}

/**
 * Initialization list of zbs agent, including creating usable instance of
 * zbs client.
 *
 * Return 0 for success, -1 for failure.
 */
int
zbsAgentListInitialize(zbsAgentListPtr agents, bool isDisaggregatedArch)
{
    int ret = -1;
    g_autofree char *err = NULL;
    VIR_AUTOSTRINGLIST addrs = NULL;
    zbsAgentPtr agent;
    int addrsNum = 0;

    if (isDisaggregatedArch) {
        ret = zbsLoadExternalServiceAddresses(&addrs, &addrsNum);
    } else {
        ret = zbsLoadInternalServiceAddresses(&addrs, &addrsNum);
    }

    if (ret < 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR, "%s", _("Failed to load zbs service addresses"));
        return ret;
    }

    if (VIR_ALLOC_N(agents->agents, addrsNum + 1) < 0) {
        return -1;
    }
    agents->nagents = addrsNum;

    /*
     * Initialize zbsAgent class so later we can use the class
     * to instantiate a lockable object zbsAgent.
     */
    if (!VIR_CLASS_NEW(zbsAgent, virClassForObjectLockable())) {
        return -1;
    }

    for (int i = 0; i < addrsNum; i++) {
        VIR_INFO("ZBS service address: %s", addrs[i]);

        agent = virObjectLockableNew(zbsAgentClass);
        if (agent == NULL) {
            return -1;
        }
        agent->isRemote = isDisaggregatedArch;

        if (isDisaggregatedArch) {
            VIR_AUTOSTRINGLIST portal = NULL;
            VIR_INFO("Creating external ZBS client");
            /*
            External ZBS storage is usually with higher latency, default zbs client
            timeout is 200ms which is not suitable for external access, so we set
            it 1000ms.
            */
            agent->zbsClient = zbs_create_external_with_timeout(addrs[i], 1000, &err);
            portal = virStringSplit(addrs[i], ":", 3);
            agent->storageIP = g_strdup(*portal);
        } else {
            agent->zbsClient = zbs_create(addrs[i], &err);
            agent->storageIP = g_strdup("127.0.0.1");
        }

        if (err != NULL) {
            virReportError(VIR_ERR_INTERNAL_ERROR, _("Failed to connect zbs: %s"), err);
            return -1;
        }

        agents->agents[i] = agent;
    }

    return 0;
}

/**
 * This will be called whenever zbsAgent object's ref hits 0,
 * which means the program is about to exit. A newly created instance
 * has 1 ref by default, virObjectUnref is called to decrease the ref
 * number.
 */
void
zbsAgentDispose(void *obj)
{
    zbsAgentPtr agent = obj;

    VIR_INFO("Destroying ZBS client");
    if (agent->zbsClient) {
        zbs_destroy(agent->zbsClient);
        agent->zbsClient = NULL;
    }
}

zbsAgentPtr
zbsAgentLookupBystorageIP(zbsAgentListPtr agents, const char *storageIP)
{
    zbsAgentPtr agent;

    assert(agents != NULL);

    if (storageIP == NULL) {
        VIR_WARN("Get a NULL storage IP, return NULL agent");
        return NULL;
    }

    for (int i = 0; i < agents->nagents; i++) {
        agent = agents->agents[i];
        if (STREQ(agent->storageIP, storageIP)) {
            return agent;
        }
    }

    return NULL;
}
