/*
 * zbs_iscsi.c: Implementation for zbs allow list iscsi driver.
 *
 * Copyright (C) 2022 SmartX, Inc.
 *
 */
#include <config.h>

#include "viralloc.h"
#include "virlog.h"
#include "virstring.h"

#include "zbs_allow_list.h"

#define VIR_FROM_THIS VIR_FROM_LOCKING

VIR_LOG_INIT("locking.zbs_iscsi");

static zbsLunAllowListPtr zbsLunAllowListNewFromZbsIscsiLun(zbs_lun_t *zbsNativeLun);
static zbsLunAllowListPtr zbsIscsiLunGetAllowList(zbsAgentPtr agent, const allockLunPtr lun);
static int
zbsIscsiLunSetAllowListSingeAccess(zbsAgentPtr agent, const allockLunPtr lun, bool single_access);
static int zbsIscsiLunAppendInitiatorToAllowListSafe(zbsAgentPtr agent,
                                                     const allockLunPtr lun,
                                                     const char *initiator);
static int zbsIscsiLunUpdateInitiatorToAllowListForce(zbsAgentPtr agent,
                                                      const allockLunPtr lun,
                                                      const char *initiator);
static int zbsIscsiLunRemoveInitiatorFromAllowList(zbsAgentPtr agent,
                                                   const allockLunPtr lun,
                                                   const char *initiator);

static zbsLunAllowListPtr
zbsLunAllowListNewFromZbsIscsiLun(zbs_lun_t *zbsNativeLun)
{
    zbsLunAllowListPtr lunInfo = NULL;

    if (VIR_ALLOC(lunInfo) < 0) {
        virReportError(
            VIR_ERR_INTERNAL_ERROR, _("%s"), "Failed to allocate memory for zbsLunAllowListPtr");
        return NULL;
    }

    lunInfo->allowedInitiators = g_strdup(zbsNativeLun->allowed_initiators);

    lunInfo->singleAccess = zbsNativeLun->single_access;

    return lunInfo;
}

static zbsLunAllowListPtr
zbsIscsiLunGetAllowList(zbsAgentPtr agent, const allockLunPtr lun)
{
    int zbsRet = 0;
    zbs_lun_t zbsLunData = {0};
    zbsLunAllowListPtr lunInfo = NULL;

    zbsRet = ZBS_REQUEST(zbs_lun_get(agent->zbsClient, lun->targetName, lun->lunId, &zbsLunData));
    if (zbsRet != 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Zbs request failed: %d"), zbsRet);
        goto cleanup;
    }

    lunInfo = zbsLunAllowListNewFromZbsIscsiLun(&zbsLunData);

cleanup:
    zbs_lun_free_allowed_initiators_memory(&zbsLunData);

    return lunInfo;
}

static int
zbsIscsiLunSetAllowListSingeAccess(zbsAgentPtr agent, const allockLunPtr lun, bool single_access)
{
    int zbsRet;

    zbsRet = ZBS_REQUEST(
        zbs_lun_set_single_access(agent->zbsClient, lun->targetName, lun->lunId, single_access));
    if (zbsRet != 0) {
        virReportError(
            VIR_ERR_INTERNAL_ERROR, _("Set single access: ZBS request failed: %d"), zbsRet);
        return -1;
    }

    return 0;
}

static int
zbsIscsiLunAppendInitiatorToAllowListSafe(zbsAgentPtr agent,
                                          const allockLunPtr lun,
                                          const char *initiator)
{
    int zbsRet;

    zbsRet = ZBS_REQUEST(
        zbs_lun_add_allowed_initiators(agent->zbsClient, lun->targetName, lun->lunId, initiator));

    if (zbsRet != 0) {
        virReportError(
            VIR_ERR_INTERNAL_ERROR, _("Append allowlist: ZBS request failed: %d"), zbsRet);
        return -1;
    }

    return 0;
}

static int
zbsIscsiLunUpdateInitiatorToAllowListForce(zbsAgentPtr agent,
                                           const allockLunPtr lun,
                                           const char *initiator)
{
    int zbsRet;

    zbsRet = ZBS_REQUEST(zbs_lun_update_allowed_initiators(
        agent->zbsClient, lun->targetName, lun->lunId, initiator));

    if (zbsRet != 0) {
        virReportError(
            VIR_ERR_INTERNAL_ERROR, _("Update allowlist: ZBS request failed: %d"), zbsRet);
        return -1;
    }

    return zbsIscsiLunSetAllowListSingeAccess(agent, lun, true);
}

static int
zbsIscsiLunRemoveInitiatorFromAllowList(zbsAgentPtr agent,
                                        const allockLunPtr lun,
                                        const char *initiator)
{
    int zbsRet;

    zbsRet = ZBS_REQUEST(zbs_lun_remove_allowed_initiators(
        agent->zbsClient, lun->targetName, lun->lunId, initiator));

    if (zbsRet != 0) {
        virReportError(
            VIR_ERR_INTERNAL_ERROR, _("Remove allowlist: ZBS request failed: %d"), zbsRet);
        return -1;
    }

    return 0;
}

zbsAllowListDriver zbsIscsiAllowListDrv = {
    .driverType = "ZBS_ISCSI",
    .zbsLunAppendInitiatorToAllowListSafe = zbsIscsiLunAppendInitiatorToAllowListSafe,
    .zbsLunGetAllowList = zbsIscsiLunGetAllowList,
    .zbsLunSetAllowListSingeAccess = zbsIscsiLunSetAllowListSingeAccess,
    .zbsLunRemoveInitiatorFromAllowList = zbsIscsiLunRemoveInitiatorFromAllowList,
    .zbsLunUpdateInitiatorToAllowListForce = zbsIscsiLunUpdateInitiatorToAllowListForce,
};
