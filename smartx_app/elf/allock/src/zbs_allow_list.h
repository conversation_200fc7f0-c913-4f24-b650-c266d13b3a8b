/*
 * zbs_allow_list.h: Header for zbs_allow_list library, declared ZBS allow list
 * related common structures and functions.
 *
 * Copyright (C) 2022 SmartX, Inc.
 *
 */

#ifndef __ZBS_ALLOW_LIST_H__
#define __ZBS_ALLOW_LIST_H__

#include <assert.h>
#include <time.h>
#include <unistd.h>

#include "virobject.h"
#include "zbs/libzbs.h"

#include "allock.h"

/* === ZBS related structs === */
typedef struct _zbsAgent zbsAgent;
typedef zbsAgent *zbsAgentPtr;

typedef struct _zbsAgentList zbsAgentList;
typedef zbsAgentList *zbsAgentListPtr;

/*
 * Zbs client instance does not support concurrency and libvirt is multi-threaded,
 * so wrap it in an ZbsAgent(allockZbsAgent below) object which is lockable.
 */
struct _zbsAgent {
    virObjectLockable parent;
    zbs_t zbsClient;
    bool isRemote;
    char *storageIP;
};

struct _zbsAgentList {
    zbsAgentPtr *agents;
    int nagents;
};

/* Store the dynamic lun info obtained from ZBS */
typedef struct _zbsLunAllowList zbsLunAllowList;
typedef zbsLunAllowList *zbsLunAllowListPtr;

struct _zbsLunAllowList {
    char *allowedInitiators;
    bool singleAccess;
};

struct _zbsAllowListDriver {
    const char *driverType;

    int (*zbsLunSetAllowListSingeAccess)(zbsAgentPtr agent,
                                         const allockLunPtr lun,
                                         bool single_access);
    int (*zbsLunAppendInitiatorToAllowListSafe)(zbsAgentPtr agent,
                                                const allockLunPtr lun,
                                                const char *initiator);
    int (*zbsLunUpdateInitiatorToAllowListForce)(zbsAgentPtr agent,
                                                 const allockLunPtr lun,
                                                 const char *initiator);
    int (*zbsLunRemoveInitiatorFromAllowList)(zbsAgentPtr agent,
                                              const allockLunPtr lun,
                                              const char *initiator);
    zbsLunAllowListPtr (*zbsLunGetAllowList)(zbsAgentPtr agent, const allockLunPtr lun);
};

/* === Helper functions for ZBS configuration */
bool zbsIsDisaggregatedArch(void);
bool zbsIsRemoteStorageConfigured(void);

/* === Helper functions for ZBS API === */
bool zbsIsErrorRetriable(int error);
void zbsLunAllowListFree(zbsLunAllowListPtr lunAllowList);
int zbsAgentListInitialize(zbsAgentListPtr agents, bool isDisaggregatedArch);
void zbsAgentDispose(void *obj);
zbsAgentPtr zbsAgentLookupBystorageIP(zbsAgentListPtr agents, const char *storageIP);

/* === Helper Macros for ZBS interaction === */
#define ZBS_REQUEST_TRY_MAX_DURATION_SEC 60
#define ZBS_REQUEST_INTERVAL 5
/*
   Request template for zbs call, the rule is as follow:

   We set 60s as the max try duration, for the first 6 requests, if
   the request keeps failing, we continue trying without interval.
   After these ones fail, we will space out the rest of the requests
   by 5 seconds.
*/
#define ZBS_REQUEST(exp)                                                \
    ({                                                                  \
        int zbsTempRet;                                                 \
        int tryCnt = 0;                                                 \
        uint32_t elapsedTime = 0;                                       \
        struct timespec start, now;                                     \
                                                                        \
        if (clock_gettime(CLOCK_MONOTONIC_RAW, &start) < 0) {           \
            VIR_ERROR(_("clock time failed: %s"), strerror(errno));     \
            abort();                                                    \
        }                                                               \
                                                                        \
        do {                                                            \
            zbsTempRet = (exp);                                         \
            if (zbsTempRet == 0) {                                      \
                break;                                                  \
            }                                                           \
                                                                        \
            if (!zbsIsErrorRetriable(zbsTempRet)) {                     \
                VIR_WARN("zbs failed: %d, no retry", zbsTempRet);       \
                break;                                                  \
            }                                                           \
                                                                        \
            tryCnt++;                                                   \
            if (clock_gettime(CLOCK_MONOTONIC_RAW, &now) < 0) {         \
                VIR_ERROR(_("clock time failed: %s"), strerror(errno)); \
                abort();                                                \
            }                                                           \
            elapsedTime = (now.tv_sec - start.tv_sec);                  \
            VIR_WARN("zbs failed: %d, tried %d times, "                 \
                     "elapsed time: %u",                                \
                     zbsTempRet,                                        \
                     tryCnt,                                            \
                     elapsedTime);                                      \
                                                                        \
            if (tryCnt > 5) {                                           \
                sleep(ZBS_REQUEST_INTERVAL);                            \
            }                                                           \
        } while (elapsedTime < ZBS_REQUEST_TRY_MAX_DURATION_SEC);       \
                                                                        \
        zbsTempRet;                                                     \
    })

/* === Declared ZBS allow list drivers === */

/* Will be implemented in individual C files. */
extern zbsAllowListDriver zbsIscsiAllowListDrv;
extern zbsAllowListDriver zbsVhostAllowListDrv;

#endif /* __ZBS_ALLOW_LIST_H__ */
