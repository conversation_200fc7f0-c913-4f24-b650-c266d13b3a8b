/*
 * zbs_vhost.c: Implementation for zbs allow list vhost driver.
 *
 * Copyright (C) 2022 SmartX, Inc.
 *
 */
#include <config.h>

#include "viralloc.h"
#include "virlog.h"
#include "virstring.h"

#include "zbs_allow_list.h"

#define VIR_FROM_THIS VIR_FROM_LOCKING

VIR_LOG_INIT("locking.zbs_vhost");

/* === Helper functions === */
static zbsLunAllowListPtr
zbsLunAllowListNewFromVhostPermission(zbs_vhost_io_permission_record_t permission,
                                      const allockLunPtr lun);
static char *vhostLunGenInitiatorFromPermission(zbs_vhost_io_permission_record_t permission,
                                                const allockLunPtr lun);
static int vhostLunExtractUuidsFromInitiator(const char *initiator, char **hostUuid, char **vmUuid);
static bool vhostLunIsPermissionOpen(const char *permissionOwner);
static char *vhostExtractInitiatorPrefix(const char *initiator);

/* === Implementation functions for driver interfaces === */
static zbsLunAllowListPtr zbsVhostLunGetAllowList(zbsAgentPtr agent, const allockLunPtr lun);
static int
zbsVhostLunSetAllowListSingeAccess(zbsAgentPtr agent, const allockLunPtr lun, bool single_access);
static int zbsVhostLunAppendInitiatorToAllowListSafe(zbsAgentPtr agent,
                                                     const allockLunPtr lun,
                                                     const char *initiator);
static int zbsVhostLunUpdateInitiatorToAllowListForce(zbsAgentPtr agent,
                                                      const allockLunPtr lun,
                                                      const char *initiator);
static int zbsVhostLunRemoveInitiatorFromAllowList(zbsAgentPtr agent,
                                                   const allockLunPtr lun,
                                                   const char *initiator);

static bool
vhostLunIsPermissionOpen(const char *permissionOwner)
{
    return STREQ(permissionOwner, "*");
}

/*
 * vhostLunCheckPermissionValid:
 * Host owner and vm owner must be both open at the same time. If
 * this condition is not met, the permission is invalid.
 */
static bool
vhostLunCheckPermissionValid(const char *hostUuid, const char *vmUuid)
{
    return vhostLunIsPermissionOpen(hostUuid) == vhostLunIsPermissionOpen(vmUuid);
}

/*
 * vhostLunFormSingleInitiatorName:
 * Generate a initiator name from host uuid & vm uuid, following format:
 * <iscsi prefix>:<vm uuid>.<machine uuid>.<shared bit>
 */
static char *
vhostLunFormSingleInitiatorName(const char *hostUuid, const char *vmUuid, const char *prefix, const allockLunPtr lun)
{
    virBuffer initiatorBuf = VIR_BUFFER_INITIALIZER;

    virBufferAsprintf(&initiatorBuf, "%s:%s.%s", prefix, vmUuid, hostUuid);

    /* Although the shared bit must always be 0 since allock does not touch shared
       disk at all, it's still better to dynamically set this bit based on data
       from allock. */
    /* TODO(Tianren): replace the initiator suffix with defined macro */
    if (lun->shared) {
        virBufferStrcat(&initiatorBuf, ".1", NULL);
    } else {
        virBufferStrcat(&initiatorBuf, ".0", NULL);
    }

    return virBufferContentAndReset(&initiatorBuf);
}

/*
 * vhostExtractInitiatorPrefix:
 * An iscsi initiator looks like: <prefix>:<name>.
*/
static char *
vhostExtractInitiatorPrefix(const char *initiator)
{
    VIR_AUTOSTRINGLIST initiatorParts = NULL;

    initiatorParts = virStringSplit(initiator, ":", 2);
    if (initiatorParts == NULL) {
        return NULL;
    }

    return g_strdup(initiatorParts[0]);
}


/*
 * vhostLunGenInitiatorNameFromPermission:
 * permission: return info from zbs API, it contains two groups: machine(host) uuid and
 * vm uuid, which corresponds to one vm instance.
 *
 * return: an allock-acknowledged initiator name which is in one of the following formats:
 *         empty onwership: "",
 *         open ownership: '* / *', (ignore spaces)
 *         with owner: iqn.2013-11.org.smartx:<vm uuid>.<machine uuid>.<shared bit>
 *         NULL: invalid vhost permission, cannot generate a initiator name
 */
static char *
vhostLunGenInitiatorFromPermission(zbs_vhost_io_permission_record_t permission,
                                   const allockLunPtr lun)
{
    virBuffer initiatorListBuf = VIR_BUFFER_INITIALIZER;
    int entrySize = zbs_lun_get_vhost_io_permission_entries_size(permission);
    const char *hostUuid = NULL;
    const char *vmUuid = NULL;
    g_autofree char *tempInitiatorName = NULL;
    g_autofree char *prefix = NULL;

    if (entrySize < 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR,
                       _("zbs-vhost: permission has unexpected entry size: %d"),
                       entrySize);
        return NULL;
    }

    if (entrySize == 0) {
        /* Empty ownership */
        virBufferAsprintf(&initiatorListBuf, "%s", "");
        return virBufferContentAndReset(&initiatorListBuf);
    }

    /* Get first entry to only check for open permission */
    hostUuid = zbs_lun_get_vhost_io_permission_entry_machine_uuid(permission, 0);
    vmUuid = zbs_lun_get_vhost_io_permission_entry_vm_uuid(permission, 0);

    if (vhostLunIsPermissionOpen(vmUuid) && vhostLunIsPermissionOpen(hostUuid)) {
        /* Open ownership */
        virBufferAsprintf(&initiatorListBuf, "%s", "*/*");
        return virBufferContentAndReset(&initiatorListBuf);
    }

    if (!vhostLunCheckPermissionValid(hostUuid, vmUuid)) {
        virReportError(VIR_ERR_INTERNAL_ERROR,
                       _("zbs-vhost: Invalid lun permission: host owner=%s, vm owner=%s"),
                       hostUuid,
                       vmUuid);
        return NULL;
    }

    if (!(prefix = vhostExtractInitiatorPrefix(lun->assignedInitiator))) {
        virReportError(VIR_ERR_INTERNAL_ERROR,
                       _("zbs-vhost: failed to parse prefix of initiator %s"),
                       lun->assignedInitiator);
        return NULL;
    }

    /* Real initiators condition */
    for (size_t i = 0; i < entrySize; i++) {
        hostUuid = zbs_lun_get_vhost_io_permission_entry_machine_uuid(permission, i);
        vmUuid = zbs_lun_get_vhost_io_permission_entry_vm_uuid(permission, i);

        tempInitiatorName = vhostLunFormSingleInitiatorName(hostUuid, vmUuid, prefix, lun);

        virBufferStrcat(&initiatorListBuf, tempInitiatorName, ",", NULL);
    }

    virBufferTrimLen(&initiatorListBuf, 1);

    return virBufferContentAndReset(&initiatorListBuf);
}

static zbsLunAllowListPtr
zbsLunAllowListNewFromVhostPermission(zbs_vhost_io_permission_record_t permission,
                                      const allockLunPtr lun)
{
    char *initiator;
    zbsLunAllowListPtr lunAllowList = NULL;
    bool singleAccess = zbs_lun_vhost_get_single_access(permission);

    if (VIR_ALLOC(lunAllowList) < 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR,
                       _("%s"),
                       "zbs-vhost: Failed to allocate memory for zbsLunAllowListPtr");
        return NULL;
    }

    lunAllowList->singleAccess = singleAccess;

    initiator = vhostLunGenInitiatorFromPermission(permission, lun);
    if (initiator == NULL) {
        VIR_ERROR(_("zbs-vhost: Failed to generate initiator name: target=%s,lun=%u"),
                  lun->targetName,
                  lun->lunId);
        goto error;
    }

    lunAllowList->allowedInitiators = initiator;

    return lunAllowList;

error:
    zbsLunAllowListFree(lunAllowList);
    return NULL;
}

/*
 * vhostLunExtractUuidsFromInitiator:
 * Extract host uuid and vm uuid from a initiator,
 * (example initiator: iqn.2013-11.org.smartx:<vm uuid>.<machine uuid>.<shared bit>).
 *
 * initiator: the input initiator name to parse.
 * hostUuid: the extracted host uuid, it's an output.
 * vmUuid: the extracted vm uuid, it's an output.
 *
 * return: 0 for success, -1 for invalid initiator name or extraction error.
 */
static int
vhostLunExtractUuidsFromInitiator(const char *initiator, char **hostUuid, char **vmUuid)
{
    char **initiatorParts = NULL;
    char **elfNameParts = NULL;
    int ret = -1;

    initiatorParts = virStringSplit(initiator, ":", 2);
    if (initiatorParts == NULL) {
        goto cleanup;
    }

    if (initiatorParts[1] == NULL) {
        goto cleanup;
    }

    elfNameParts = virStringSplit(initiatorParts[1], ".", 3);
    if (elfNameParts == NULL) {
        goto cleanup;
    }

    if (elfNameParts[2] == NULL) {
        goto cleanup;
    }

    *vmUuid = g_strdup(elfNameParts[0]);
    *hostUuid = g_strdup(elfNameParts[1]);

    ret = 0;

cleanup:
    virStringListFree(initiatorParts);
    virStringListFree(elfNameParts);
    return ret;
}

static zbsLunAllowListPtr
zbsVhostLunGetAllowList(zbsAgentPtr agent, const allockLunPtr lun)
{
    int zbsRet;
    zbs_vhost_io_permission_record_t permission = NULL;
    zbsLunAllowListPtr lunInfo = NULL;

    zbsRet = ZBS_REQUEST(zbs_lun_get_vhost_io_permission(
        agent->zbsClient, lun->targetName, lun->lunId, &permission));
    if (zbsRet != 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Zbs request failed: %d"), zbsRet);
        goto cleanup;
    }

    lunInfo = zbsLunAllowListNewFromVhostPermission(permission, lun);
    if (lunInfo == NULL) {
        VIR_ERROR(_("zbs-vhost: failed to get lun allow list: target=%s,lun=%u"),
                  lun->targetName,
                  lun->lunId);
    }

cleanup:
    zbs_lun_vhost_free_record(permission);
    return lunInfo;
}

static int
zbsVhostLunSetAllowListSingeAccess(zbsAgentPtr agent, const allockLunPtr lun, bool single_access)
{
    int zbsRet;

    zbsRet = ZBS_REQUEST(zbs_lun_vhost_set_single_access(
        agent->zbsClient, lun->targetName, lun->lunId, single_access));
    if (zbsRet != 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR,
                       _("zbs-vhost: Set single access: ZBS request failed: %d"),
                       zbsRet);
        return -1;
    }

    return 0;
}

static int
zbsVhostLunAppendInitiatorToAllowListSafe(zbsAgentPtr agent,
                                          const allockLunPtr lun,
                                          const char *initiator)
{
    char *hostUuid = NULL;
    char *vmUuid = NULL;
    int parseRet;
    int zbsRet;
    int ret = -1;

    parseRet = vhostLunExtractUuidsFromInitiator(initiator, &hostUuid, &vmUuid);
    if (parseRet < 0) {
        virReportError(
            VIR_ERR_INTERNAL_ERROR, _("zbs-vhost: Failed to parse initiator: %s"), initiator);
        goto cleanup;
    }

    zbsRet = ZBS_REQUEST(zbs_lun_add_vhost_io_permission(
        agent->zbsClient, lun->targetName, lun->lunId, hostUuid, vmUuid));

    if (zbsRet != 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR,
                       _("zbs-vhost: Append allowlist: ZBS request failed: %d"),
                       zbsRet);
        goto cleanup;
    }

    ret = 0;

cleanup:
    VIR_FREE(hostUuid);
    VIR_FREE(vmUuid);

    return ret;
}

static int
zbsVhostLunUpdateInitiatorToAllowListForce(zbsAgentPtr agent,
                                           const allockLunPtr lun,
                                           const char *initiator)
{
    char *hostUuid = NULL;
    char *vmUuid = NULL;
    int parseRet;
    int zbsRet;
    int ret = -1;

    parseRet = vhostLunExtractUuidsFromInitiator(initiator, &hostUuid, &vmUuid);
    if (parseRet < 0) {
        virReportError(
            VIR_ERR_INTERNAL_ERROR, _("zbs-vhost: Failed to parse initiator: %s"), initiator);
        goto cleanup;
    }

    zbsRet = ZBS_REQUEST(zbs_lun_set_vhost_io_permission(
        agent->zbsClient, lun->targetName, lun->lunId, hostUuid, vmUuid));

    if (zbsRet != 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR,
                       _("zbs-vhost: Update allowlist: ZBS request failed: %d"),
                       zbsRet);
        goto cleanup;
    }

    ret = zbsVhostLunSetAllowListSingeAccess(agent, lun, true);

cleanup:
    VIR_FREE(hostUuid);
    VIR_FREE(vmUuid);

    return ret;
}

static int
zbsVhostLunRemoveInitiatorFromAllowList(zbsAgentPtr agent,
                                        const allockLunPtr lun,
                                        const char *initiator)
{
    char *hostUuid = NULL;
    char *vmUuid = NULL;
    int parseRet;
    int zbsRet;
    int ret = -1;

    parseRet = vhostLunExtractUuidsFromInitiator(initiator, &hostUuid, &vmUuid);
    if (parseRet < 0) {
        virReportError(
            VIR_ERR_INTERNAL_ERROR, _("zbs-vhost: Failed to parse initiator: %s"), initiator);
        goto cleanup;
    }

    zbsRet = ZBS_REQUEST(zbs_lun_remove_vhost_io_permission(
        agent->zbsClient, lun->targetName, lun->lunId, hostUuid, vmUuid));

    if (zbsRet != 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR,
                       _("zbs-vhost: Remove allowlist: ZBS request failed: %d"),
                       zbsRet);
        goto cleanup;
    }

    ret = 0;

cleanup:
    VIR_FREE(hostUuid);
    VIR_FREE(vmUuid);

    return ret;
}

zbsAllowListDriver zbsVhostAllowListDrv = {
    .driverType = "ZBS_VHOST",
    .zbsLunAppendInitiatorToAllowListSafe = zbsVhostLunAppendInitiatorToAllowListSafe,
    .zbsLunGetAllowList = zbsVhostLunGetAllowList,
    .zbsLunSetAllowListSingeAccess = zbsVhostLunSetAllowListSingeAccess,
    .zbsLunRemoveInitiatorFromAllowList = zbsVhostLunRemoveInitiatorFromAllowList,
    .zbsLunUpdateInitiatorToAllowListForce = zbsVhostLunUpdateInitiatorToAllowListForce,
};
