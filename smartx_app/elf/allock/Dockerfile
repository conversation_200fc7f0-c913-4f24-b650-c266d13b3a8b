ARG DISTRO

# DOCKER FILE: https://newgh.smartx.com/elf/libvirt-rpm/tree/main/builder
FROM registry.smtx.io/elf/libvirt-builder-$DISTRO:1.0.5

ARG ZBS_VERSION

# Create a fake zbs conf for testing allock
RUN mkdir -p /etc/zbs; echo 'zookeeper = 127.0.0.1:2181' > /etc/zbs/zbs.conf

# Below are the part that needs to be re-executed every time the image
# is built. Add a timestamp arg to invalidate the part below from latest
# cached data.
ARG TIMESTAMP

RUN yum clean all && yum install -y libzbs-$ZBS_VERSION libzbs-devel-$ZBS_VERSION

ENV LANG "en_US.UTF-8"
