/*
 * Copyright (C) 2022 SmartX, Inc.
 */

#include <config.h>

#include "testutils.h"

#include "mock.h"
#include "zbs_allow_list.c"

typedef struct _testData testData;
struct _testData {
    const char *storageIP;
    const char *expectedRet;
};

static int
testInitializeZbsAgents(const void *data)
{
    zbsAgentListPtr agents = NULL;
    int ret = 0;
    bool *isDisaggregatedArch = (bool *)data;

    if (VIR_ALLOC(agents) < 0) {
        return -1;
    }

    ret = zbsAgentListInitialize(agents, *isDisaggregatedArch);

    if (ret < 0) {
        return -1;
    }

    if (*isDisaggregatedArch) {
        if (agents->nagents != 2) {
            return -1;
        }

        if (STRNEQ(agents->agents[0]->storageIP, "************")) {
            return -1;
        }

        if (!agents->agents[0]->isRemote) {
            return -1;
        }

        if (STRNEQ(agents->agents[1]->storageIP, "************")) {
            return -1;
        }

        if (!agents->agents[0]->isRemote) {
            return -1;
        }
    } else {
        if (agents->nagents != 1) {
            return -1;
        }

        if (STRNEQ(agents->agents[0]->storageIP, "127.0.0.1")) {
            return -1;
        }

        if (agents->agents[0]->isRemote) {
            return -1;
        }
    }

    return 0;
}

int
testLookupZbsAgentByStorageIP(const void *data)
{
    zbsAgentListPtr agents = NULL;
    zbsAgentPtr agent = NULL;
    testData *testData = (void *)data;

    if (VIR_ALLOC(agents) < 0) {
        return -1;
    }

    if (zbsAgentListInitialize(agents, true) < 0) {
        return -1;
    }

    agent = zbsAgentLookupBystorageIP(agents, testData->storageIP);

    if (testData->expectedRet == NULL) {
        if (agent != NULL) {
            return -1;
        }
    }

    if (testData->expectedRet != NULL) {
        if (STRNEQ(agent->storageIP, testData->storageIP)) {
            return -1;
        }
    }

    return 0;
}

static int
mymain(void)
{
    bool isDisaggregatedArch = true;
    if (virTestRun("InitializeExternalZbsAgents", testInitializeZbsAgents, &isDisaggregatedArch) <
        0) {
        return -1;
    }

    isDisaggregatedArch = false;
    if (virTestRun("InitializeInternalZbsAgents", testInitializeZbsAgents, &isDisaggregatedArch) <
        0) {
        return -1;
    }

    testData test1 = {
        .storageIP = "************",
        .expectedRet = "************",
    };

    testData test2 = {
        .storageIP = "************",
        .expectedRet = NULL,
    };

    if (virTestRun("LookupZBSagentByStorageIP: valid storage IP",
                   testLookupZbsAgentByStorageIP,
                   &test1) < 0) {
        return -1;
    }

    if (virTestRun("LookupZBSagentByStorageIP: invalid storage IP",
                   testLookupZbsAgentByStorageIP,
                   &test2) < 0) {
        return -1;
    }

    return 0;
}

/* Replace the file related functions with mock functions defined in .so */
VIR_TEST_MAIN_PRELOAD(mymain, abs_builddir "/file_mock.so")
