/*
 * Copyright (C) 2022 SmartX, Inc.
 */

#include <config.h>

#include "testutils.h"

#include "lock_driver_allock.c"
#include "mock.h"

typedef struct _testData testData;
struct _testData {
    /* testFlag is used to indicate the file mock module
       what should be done */
    const char *testFlag;
    bool expectedRet;
};

static int
testIsDisaggregatedArch(const void *data)
{
    testData *testData = (void *)data;
    bool ret;

    if (testData->testFlag != NULL) {
        setenv(testData->testFlag, "1", 1);
    }

    ret = zbsIsDisaggregatedArch();
    printf("return %d", ret);

    if (testData->testFlag != NULL) {
        unsetenv(testData->testFlag);
    }

    if (ret != testData->expectedRet) {
        return -1;
    }

    return 0;
}

static int
testIsRemoteStorageConfigured(const void *data)
{
    testData *testData = (void *)data;
    bool ret;

    if (testData->testFlag != NULL) {
        setenv(testData->testFlag, "1", 1);
    }

    ret = zbsIsRemoteStorageConfigured();

    if (testData->testFlag != NULL) {
        unsetenv(testData->testFlag);
    }

    if (ret != testData->expectedRet) {
        return -1;
    }

    return 0;
}

static int
mymain(void)
{
    setenv(allockMockDisaggregatedTest, "1", 1);

    testData test1 = {
        .testFlag = allockMockInDisaggregated,
        .expectedRet = true,
    };

    testData test2 = {
        .testFlag = allockMockNotInDisaggregated,
        .expectedRet = false,
    };

    testData test3 = {
        .testFlag = allockMockPlatformFileNotExist,
        .expectedRet = false,
    };

    testData test4 = {
        .testFlag = allockMockRemoteStorageConfigured,
        .expectedRet = true,
    };

    testData test5 = {
        .testFlag = allockMockRemoteStorageNotConfigured,
        .expectedRet = false,
    };

    if (virTestRun("zbsIsDisaggregatedArch1: in disaggregated arch",
                   testIsDisaggregatedArch,
                   &test1) < 0) {
        return -1;
    }

    if (virTestRun("zbsIsDisaggregatedArch2: not in disaggregated arch",
                   testIsDisaggregatedArch,
                   &test2) < 0) {
        return -1;
    }

    if (virTestRun("zbsIsDisaggregatedArch3: platform file not exist",
                   testIsDisaggregatedArch,
                   &test3) < 0) {
        return -1;
    }

    if (virTestRun("zbsIsRemoteStorageConfigured1: remote storage configured",
                   testIsRemoteStorageConfigured,
                   &test4) < 0) {
        return -1;
    }

    if (virTestRun("zbsIsRemoteStorageConfigured2: remote storage not configured",
                   testIsRemoteStorageConfigured,
                   &test5) < 0) {
        return -1;
    }

    return 0;
}

/* Replace the file related functions with mock functions defined in .so */
VIR_TEST_MAIN_PRELOAD(mymain, abs_builddir "/file_mock.so")
