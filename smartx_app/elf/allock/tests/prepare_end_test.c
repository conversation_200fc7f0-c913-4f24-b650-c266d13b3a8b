/*
 * Copyright (C) 2022 SmartX, Inc.
 */

#include <config.h>

#include "viralloc.h"

#include "lock_driver_allock.c"
#include "utils.h"

/* === Non-shared LUN Tests === */

/*
 * Testcase: PrepareEnd for in-migration, allowlist=src_initiator,dest_initiator,
 * single access=false. Should succeed. <PERSON><PERSON>'s final state should be
 * allowlist=src_initiator, single_access=true.
 */
static int
testPrepareEndInMigrationSuccess(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;
    char *srcInitiator = "iqn.2013-11.org.smartx:6802a6c3-1747-4080-89d5-bfe7caa17245.src-host.0";
    char *destInitiator = DEFAULT_ISCSI_INITIATOR;
    char *initiatorList = allockTestJoinInitiators(srcInitiator, destInitiator);

    if (allockTestCtxPresetLunStatus(testCtx, initiatorList, false) < 0) {
        return -1;
    }

    callRet = allockPrepareEnd(testCtx->lock, VIR_LOCK_MANAGER_IN_MIGRATING);

    if (callRet != 0) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, srcInitiator, true)) {
        return -1;
    }

    return 0;
}

/*
 * Testcase: PrepareEnd for in-migration, allowlist=src_initiator,
 * single access=true. Should Fail.
 */
static int
testPrepareEndInMigrationSingleAccess(const void *data)
{
    int callRet;
    int ret = -1;
    allockTestCtxPtr testCtx = (void *)data;
    char *srcInitiator = "iqn.2013-11.org.smartx:6802a6c3-1747-4080-89d5-bfe7caa17245.src-host.0";

    if (allockTestCtxPresetLunStatus(testCtx, srcInitiator, true) < 0) {
        return -1;
    }

    callRet = allockPrepareEnd(testCtx->lock, VIR_LOCK_MANAGER_IN_MIGRATING);

    if (callRet != -1) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, srcInitiator, true)) {
        return -1;
    }

    return 0;
}

/* === Shared LUN Tests === */

/*
 * Testcase: PrepareEnd for in-migration shared lun, allowlist=src_initiator,
 * single access=false.
 */
static int
testPrepareEndInMigrationSharedSuccess(const void *data)
{
    int callRet;
    int ret = -1;
    allockTestCtxPtr testCtx = (void *)data;
    char *srcInitiator = "iqn.2013-11.org.smartx:6802a6c3-1747-4080-89d5-bfe7caa17245.src-host.1";
    char *destInitiator = DEFAULT_ISCSI_SHARED_INITIATOR;
    g_autofree char *initiatorList = allockTestJoinInitiators(srcInitiator, destInitiator);

    if (allockTestCtxPresetLunStatus(testCtx, initiatorList, false) < 0) {
        return -1;
    }

    callRet = allockPrepareEnd(testCtx->lock, VIR_LOCK_MANAGER_IN_MIGRATING);

    if (callRet != 0) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, srcInitiator, false)) {
        return -1;
    }

    return 0;
}

static int
testPrepareEndInMigrationSharedOpenOwnership(const void *data)
{
    int callRet;
    int ret = -1;
    allockTestCtxPtr testCtx = (void *)data;

    if (allockTestCtxPresetLunStatus(testCtx, "*/*", false) < 0) {
        return -1;
    }

    callRet = allockPrepareEnd(testCtx->lock, VIR_LOCK_MANAGER_IN_MIGRATING);

    if (callRet != 0) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, "*/*", false)) {
        return -1;
    }

    return 0;
}

int
testRun(allockTestCtxPtr testCtx)
{
    if (virTestRun("allockPrepareEnd-InMigration: Success",
                   testPrepareEndInMigrationSuccess,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunPrepareEnd-InMigration: Failure: SingleAccess",
                   testPrepareEndInMigrationSingleAccess,
                   testCtx) < 0) {
        return -1;
    }

    allockTestCtxSetAllockLun(testCtx, true, DEFAULT_ISCSI_SHARED_INITIATOR);

    if (virTestRun("allockLunPrepareEnd-InMigration: Success: Shared",
                   testPrepareEndInMigrationSharedSuccess,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunPrepareEnd-InMigration: Success: Shared & open ownership",
                   testPrepareEndInMigrationSharedOpenOwnership,
                   testCtx) < 0) {
        return -1;
    }

    return 0;
}

ALLOCK_API_TEST(testRun);
