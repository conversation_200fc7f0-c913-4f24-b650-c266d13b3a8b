/*
 * Copyright (c) 2022 SMARTX
 * All rights reserved.
 */

#include <config.h>
#include <stdbool.h>
#include <stdint.h>
#include <stdio.h>

#include "viralloc.h"
#include "virbuffer.h"
#include "virerror.h"
#include "virstring.h"
#include "zbs/common.h"

#define VIR_FROM_THIS VIR_FROM_LOCKING

#define MAX_LUN_NUM 256

typedef void *zbs_t;

typedef struct zbs_vhost_io_permission_record {
    /*
        Record id list is splitted by comma, one record id is format of:
        "<vm uuid>.<host uuid(machine uuid)>"
    */
    char *record_id_list;
    bool single_access;
} zbs_vhost_io_permission_record;

typedef zbs_vhost_io_permission_record *zbs_vhost_io_permission_record_t;

typedef struct zbs_lun {
    uint64_t size;
    char name[ZBS_MAX_NAMELEN + 1];
    char desc[ZBS_MAX_DESCLEN + 1];
    uint32_t replica_num;
    bool thin_provision;
    char *allowed_initiators;
    bool single_access;
    char *target;
    uint32_t lun_id;
    zbs_vhost_io_permission_record_t permission_record_ptr;
} zbs_lun_t;

typedef zbs_lun_t *zbs_lun_t_ptr;

typedef struct zbs_service {
    zbs_lun_t_ptr zbs_luns[MAX_LUN_NUM];
    size_t lun_num;
} zbs_service_t;

int zbs_initialize_service(void);
zbs_t zbs_create(const char *hosts, char **error);
zbs_t zbs_create_external_with_timeout(const char *hosts, int timeout_ms, char **error);
void zbs_destroy(zbs_t zbs);

int zbs_lun_create(zbs_t zbs,
                   const char *target,
                   uint32_t lun_id,
                   const char *allowed_initiators,
                   const bool single_access);

int zbs_lun_get(zbs_t zbs, const char *target, uint32_t lun_id, zbs_lun_t *lun);

int zbs_lun_add_allowed_initiators(zbs_t zbs,
                                   const char *target_name,
                                   uint32_t lun_id,
                                   const char *new_initiators);

int zbs_lun_remove_allowed_initiators(zbs_t zbs,
                                      const char *target_name,
                                      uint32_t lun_id,
                                      const char *initiators);

int
zbs_lun_set_single_access(zbs_t zbs, const char *target_name, uint32_t lun_id, bool single_access);

int zbs_lun_update_allowed_initiators(zbs_t zbs,
                                      const char *target,
                                      uint32_t lun_id,
                                      const char *new_initiators);

int zbs_lun_update(zbs_t zbs,
                   const char *target_name,
                   uint32_t lun_id,
                   const char *allowed_initiators,
                   bool single_access);

int zbs_lun_free_allowed_initiators_memory(zbs_lun_t *lun);

void zbs_lun_free(zbs_lun_t_ptr zbs_lun);

static zbs_lun_t_ptr helper_zbs_find_lun_ref(const char *target,
                                             uint32_t lun_id,
                                             zbs_lun_t_ptr lun_list[],
                                             size_t lun_list_len);
static bool helper_string_in_list(const char *str, char **list);

int zbs_lun_add_vhost_io_permission(
    zbs_t zbs, const char *target, uint32_t lun_id, const char *machine_uuid, const char *vm_uuid);

int zbs_lun_remove_vhost_io_permission(
    zbs_t zbs, const char *target, uint32_t lun_id, const char *machine_uuid, const char *vm_uuid);

int zbs_lun_set_vhost_io_permission(
    zbs_t zbs, const char *target, uint32_t lun_id, const char *machine_uuid, const char *vm_uuid);

int zbs_lun_clear_vhost_io_permission(zbs_t zbs, const char *target, uint32_t lun_id);

int zbs_lun_get_vhost_io_permission(zbs_t zbs,
                                    const char *target,
                                    uint32_t,
                                    zbs_vhost_io_permission_record_t *record);

int zbs_lun_get_vhost_io_permission_entries_size(zbs_vhost_io_permission_record_t record);

const char *
zbs_lun_get_vhost_io_permission_entry_machine_uuid(zbs_vhost_io_permission_record_t record,
                                                   int idx);

const char *zbs_lun_get_vhost_io_permission_entry_vm_uuid(zbs_vhost_io_permission_record_t record,
                                                          int idx);

void zbs_lun_vhost_free_record(zbs_vhost_io_permission_record_t record);

int zbs_lun_vhost_set_single_access(zbs_t zbs, const char *target, uint32_t lun_id, bool enable);

bool zbs_lun_vhost_get_single_access(zbs_vhost_io_permission_record_t record);
