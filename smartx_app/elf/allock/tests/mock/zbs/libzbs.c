/*
 * Copyright (c) 2022 SMARTX
 * All rights reserved.
 */

#include "libzbs.h"

static zbs_lun_t_ptr helper_zbs_find_lun_ref(const char *target,
                                             uint32_t lun_id,
                                             zbs_lun_t_ptr lun_list[],
                                             size_t lun_list_len);
static bool helper_string_in_list(const char *str, char **list);

/*
g_zbs_service:
Mock Libzbs lun service: maintain a global lun list and provide libzbs
API for caller to do CRUD operations on luns.

It's not thread safe yet so unittest tests which uses it should not call
it in multi-threads.

TODO(tianren): mock the failed zbs requests.
*/
static zbs_service_t *g_zbs_service;

/* === internal helper functions === */
static zbs_lun_t_ptr
helper_zbs_find_lun_ref(const char *target,
                        uint32_t lun_id,
                        zbs_lun_t_ptr lun_list[],
                        size_t lun_list_len)
{
    zbs_lun_t_ptr zbs_lun_temp = NULL;

    for (size_t i = 0; i < lun_list_len; i++) {
        zbs_lun_temp = lun_list[i];

        if (STREQ(zbs_lun_temp->target, target) && zbs_lun_temp->lun_id == lun_id) {
            return zbs_lun_temp;
        }
    }
    return NULL;
}

static bool
helper_string_in_list(const char *str, char **list)
{
    for (size_t i = 0; list[i]; i++) {
        if (STREQ(str, list[i])) {
            return true;
        }
    }
    return false;
}

static char *
helper_append_to_uuid_allow_list(const char *uuid_list, const char *uuid)
{
    virBuffer initiatorListBuffer = VIR_BUFFER_INITIALIZER;

    if (STREQ(uuid_list, "*")) {
        virBufferStrcat(&initiatorListBuffer, "", NULL);
    }

    if (!STREQ(uuid_list, "")) {
        virBufferStrcat(&initiatorListBuffer, uuid_list, ",", NULL);
    }

    virBufferStrcat(&initiatorListBuffer, uuid, NULL);
    return virBufferContentAndReset(&initiatorListBuffer);
}

static char *
helper_remove_from_uuid_allow_list(const char *uuid_list, const char *uuid)
{
    virBuffer initiatorListBuffer = VIR_BUFFER_INITIALIZER;
    char **remove_list = NULL;
    char **existing_uuid_list = NULL;

    existing_uuid_list = virStringSplit(uuid_list, ",", 0);

    for (size_t i = 0; existing_uuid_list[i]; i++) {
        if (!STREQ(existing_uuid_list[i], uuid)) {
            virBufferStrcat(&initiatorListBuffer, existing_uuid_list[i], ",", NULL);
        }
    }

    if (virBufferUse(&initiatorListBuffer) > 0) {
        /* Trim the last comma */
        virBufferTrimLen(&initiatorListBuffer, 1);
    }

    return virBufferContentAndReset(&initiatorListBuffer);
}

static const char *
helper_generate_record_id(const char *host_uuid, const char *vm_uuid)
{
    virBuffer record_id_buf = VIR_BUFFER_INITIALIZER;
    virBufferAsprintf(&record_id_buf, "%s.%s", vm_uuid, host_uuid);
    return virBufferContentAndReset(&record_id_buf);
}

/* === API functions === */
int
zbs_initialize_service(void)
{
    if (VIR_ALLOC(g_zbs_service) < 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Failed to initialize zbs service"));
        return -1;
    }
    g_zbs_service->lun_num = 0;
    return 0;
}

int
zbs_lun_create(zbs_t zbs,
               const char *target,
               uint32_t lun_id,
               const char *allowed_initiators,
               const bool single_access)
{
    zbs_lun_t_ptr new_lun = NULL;

    if (g_zbs_service->lun_num == MAX_LUN_NUM) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Cannot create more luns"));
        return -1;
    }

    if (VIR_ALLOC(new_lun) < 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Failed to initialize a lun"));
        return -1;
    }

    new_lun->target = g_strdup(target);
    new_lun->allowed_initiators = g_strdup(allowed_initiators);
    new_lun->lun_id = lun_id;
    new_lun->single_access = single_access;

    g_zbs_service->zbs_luns[g_zbs_service->lun_num] = new_lun;
    g_zbs_service->lun_num++;

    return 0;

error:
    zbs_lun_free(new_lun);
    return -1;
}

/* === ISCSI API === */

int
zbs_lun_get(zbs_t zbs, const char *target, uint32_t lun_id, zbs_lun_t *lun)
{
    zbs_lun_t_ptr zbs_lun_ref =
        helper_zbs_find_lun_ref(target, lun_id, g_zbs_service->zbs_luns, g_zbs_service->lun_num);

    if (zbs_lun_ref == NULL) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Did not find lun %s %u"), target, lun_id);
        return -1;
    }

    lun->allowed_initiators = g_strdup(zbs_lun_ref->allowed_initiators);
    lun->single_access = zbs_lun_ref->single_access;

    return 0;
}

zbs_t
zbs_create(const char *hosts, char **error)
{
    return NULL;
}

zbs_t
zbs_create_external_with_timeout(const char *hosts, int timeout_ms, char **error)
{
    return NULL;
}

void
zbs_destroy(zbs_t zbs)
{
    return;
}

int
zbs_lun_add_allowed_initiators(zbs_t zbs,
                               const char *target_name,
                               uint32_t lun_id,
                               const char *new_initiators)
{
    zbs_lun_t_ptr zbs_lun_ref = helper_zbs_find_lun_ref(
        target_name, lun_id, g_zbs_service->zbs_luns, g_zbs_service->lun_num);
    virBuffer initiatorListBuffer = VIR_BUFFER_INITIALIZER;
    g_autofree char **new_initiator_list = NULL;

    if (zbs_lun_ref == NULL) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Did not find lun %s %u"), target_name, lun_id);
        return -1;
    }

    if (STREQ(zbs_lun_ref->allowed_initiators, "*/*")) {
        zbs_lun_ref->allowed_initiators = "";
    }

    if (!STREQ(zbs_lun_ref->allowed_initiators, "")) {
        virBufferStrcat(&initiatorListBuffer, zbs_lun_ref->allowed_initiators, ",", NULL);
    }

    new_initiator_list = virStringSplit(new_initiators, ",", 0);
    if (new_initiator_list == NULL) {
        virReportError(
            VIR_ERR_INTERNAL_ERROR, _("Failed to parse new initiators %s"), new_initiators);
        return -1;
    }

    for (int i = 0; new_initiator_list[i]; i++) {
        if (!virStringMatch(zbs_lun_ref->allowed_initiators, new_initiator_list[i])) {
            virBufferStrcat(&initiatorListBuffer, new_initiator_list[i], NULL);
        }
    }

    virBufferTrim(&initiatorListBuffer, ",");

    zbs_lun_free_allowed_initiators_memory(zbs_lun_ref);
    zbs_lun_ref->allowed_initiators = virBufferContentAndReset(&initiatorListBuffer);

    return 0;
}

int
zbs_lun_remove_allowed_initiators(zbs_t zbs,
                                  const char *target_name,
                                  uint32_t lun_id,
                                  const char *initiators)
{
    virBuffer initiatorListBuffer = VIR_BUFFER_INITIALIZER;
    char **removeList = NULL;
    char **initiatorList = NULL;
    zbs_lun_t_ptr zbs_lun_ref = helper_zbs_find_lun_ref(
        target_name, lun_id, g_zbs_service->zbs_luns, g_zbs_service->lun_num);

    if (zbs_lun_ref == NULL) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Did not find lun %s %u"), target_name, lun_id);
        return -1;
    }

    removeList = virStringSplit(initiators, ",", 0);

    initiatorList = virStringSplit(zbs_lun_ref->allowed_initiators, ",", 0);

    for (size_t i = 0; initiatorList[i]; i++) {
        if (!helper_string_in_list(initiatorList[i], removeList)) {
            virBufferStrcat(&initiatorListBuffer, initiatorList[i], ",", NULL);
        }
    }

    if (virBufferUse(&initiatorListBuffer) > 0) {
        /* Trim the last comma */
        virBufferTrimLen(&initiatorListBuffer, 1);
    }

    zbs_lun_free_allowed_initiators_memory(zbs_lun_ref);
    zbs_lun_ref->allowed_initiators = virBufferContentAndReset(&initiatorListBuffer);

    virStringListFree(removeList);
    virStringListFree(initiatorList);
    return 0;
}

int
zbs_lun_set_single_access(zbs_t zbs, const char *target_name, uint32_t lun_id, bool single_access)
{
    zbs_lun_t_ptr zbs_lun_ref = helper_zbs_find_lun_ref(
        target_name, lun_id, g_zbs_service->zbs_luns, g_zbs_service->lun_num);

    if (zbs_lun_ref == NULL) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Did not find lun %s %u"), target_name, lun_id);
        return -1;
    }

    zbs_lun_ref->single_access = single_access;

    return 0;
}

int
zbs_lun_update_allowed_initiators(zbs_t zbs,
                                  const char *target,
                                  uint32_t lun_id,
                                  const char *new_initiators)
{
    zbs_lun_t_ptr zbs_lun_ref =
        helper_zbs_find_lun_ref(target, lun_id, g_zbs_service->zbs_luns, g_zbs_service->lun_num);

    if (zbs_lun_ref == NULL) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Did not find lun %s %u"), target, lun_id);
        return -1;
    }
    zbs_lun_ref->allowed_initiators = g_strdup(new_initiators);

    return 0;
}

int
zbs_lun_update(zbs_t zbs,
               const char *target_name,
               uint32_t lun_id,
               const char *allowed_initiators,
               bool single_access)
{
    if (zbs_lun_update_allowed_initiators(zbs, target_name, lun_id, allowed_initiators) < 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR,
                       _("Failed to update allowed initiator of %s %u"),
                       target_name,
                       lun_id);
        return -1;
    }

    if (zbs_lun_set_single_access(zbs, target_name, lun_id, single_access) < 0) {
        virReportError(
            VIR_ERR_INTERNAL_ERROR, _("Failed to set single access of %s %u"), target_name, lun_id);
        return -1;
    }

    return 0;
}

/* Exposed by libzbs for caller to free initiators after getting it. */
int
zbs_lun_free_allowed_initiators_memory(zbs_lun_t_ptr zbs_lun)
{
    if (zbs_lun == NULL) {
        return 0;
    }

    VIR_FREE(zbs_lun->allowed_initiators);
    return 0;
}

void
zbs_lun_free(zbs_lun_t_ptr zbs_lun)
{
    if (zbs_lun == NULL) {
        return;
    }
    zbs_lun_free_allowed_initiators_memory(zbs_lun);
    VIR_FREE(zbs_lun->target);
    VIR_FREE(zbs_lun);
}

/* === vhost API === */

int
zbs_lun_add_vhost_io_permission(
    zbs_t zbs, const char *target, uint32_t lun_id, const char *machine_uuid, const char *vm_uuid)
{
    char *new_record_id_list = NULL;
    const char *record_id;
    zbs_lun_t_ptr zbs_lun_ref =
        helper_zbs_find_lun_ref(target, lun_id, g_zbs_service->zbs_luns, g_zbs_service->lun_num);

    if (zbs_lun_ref == NULL) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Did not find lun %s %u"), target, lun_id);
        return -1;
    }

    record_id = helper_generate_record_id(machine_uuid, vm_uuid);

    new_record_id_list = helper_append_to_uuid_allow_list(
        zbs_lun_ref->permission_record_ptr->record_id_list, record_id);

    VIR_FREE(zbs_lun_ref->permission_record_ptr->record_id_list);
    zbs_lun_ref->permission_record_ptr->record_id_list = new_record_id_list;

    return 0;
}

int
zbs_lun_remove_vhost_io_permission(
    zbs_t zbs, const char *target, uint32_t lun_id, const char *machine_uuid, const char *vm_uuid)
{
    const char *record_id;
    char *new_record_id_list = NULL;
    zbs_lun_t_ptr zbs_lun_ref =
        helper_zbs_find_lun_ref(target, lun_id, g_zbs_service->zbs_luns, g_zbs_service->lun_num);

    if (zbs_lun_ref == NULL) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Did not find lun %s %u"), target, lun_id);
        return -1;
    }

    record_id = helper_generate_record_id(machine_uuid, vm_uuid);
    new_record_id_list = helper_remove_from_uuid_allow_list(
        zbs_lun_ref->permission_record_ptr->record_id_list, record_id);

    VIR_FREE(zbs_lun_ref->permission_record_ptr->record_id_list);
    zbs_lun_ref->permission_record_ptr->record_id_list = new_record_id_list;

    return 0;
}

int
zbs_lun_set_vhost_io_permission(
    zbs_t zbs, const char *target, uint32_t lun_id, const char *machine_uuid, const char *vm_uuid)
{
    const char *record_id;
    zbs_lun_t_ptr zbs_lun_ref =
        helper_zbs_find_lun_ref(target, lun_id, g_zbs_service->zbs_luns, g_zbs_service->lun_num);

    if (zbs_lun_ref == NULL) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Did not find lun %s %u"), target, lun_id);
        return -1;
    }

    record_id = helper_generate_record_id(machine_uuid, vm_uuid);

    VIR_FREE(zbs_lun_ref->permission_record_ptr->record_id_list);

    zbs_lun_ref->permission_record_ptr->record_id_list = g_strdup(record_id);

    return 0;
}

int
zbs_lun_get_vhost_io_permission(zbs_t zbs,
                                const char *target,
                                uint32_t lun_id,
                                zbs_vhost_io_permission_record_t *record)
{
    zbs_lun_t_ptr zbs_lun_ref =
        helper_zbs_find_lun_ref(target, lun_id, g_zbs_service->zbs_luns, g_zbs_service->lun_num);

    if (zbs_lun_ref == NULL) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Did not find lun %s %u"), target, lun_id);
        return -1;
    }

    *record = zbs_lun_ref->permission_record_ptr;

    return 0;
}

int
zbs_lun_get_vhost_io_permission_entries_size(zbs_vhost_io_permission_record_t record)
{
    char **uuid_list = NULL;
    int num = 0;

    if (STREQ(record->record_id_list, "*.*")) {
        return 1;
    }

    if (STREQ(record->record_id_list, ".")) {
        return 0;
    }

    uuid_list = virStringSplit(record->record_id_list, ",", 0);

    while (uuid_list[num]) {
        num++;
    }

    return num;
}

const char *
zbs_lun_get_vhost_io_permission_entry_machine_uuid(zbs_vhost_io_permission_record_t record, int idx)
{
    int size = zbs_lun_get_vhost_io_permission_entries_size(record);
    char **record_list;

    if (idx >= size) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Index %d is out of size %d"), idx, size);
        return NULL;
    }

    record_list = virStringSplit(record->record_id_list, ",", 0);
    return virStringSplit(record_list[idx], ".", 2)[1];
}

const char *
zbs_lun_get_vhost_io_permission_entry_vm_uuid(zbs_vhost_io_permission_record_t record, int idx)
{
    int size = zbs_lun_get_vhost_io_permission_entries_size(record);
    char **record_list;

    if (idx >= size) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Index %d is out of size %d"), idx, size);
        return NULL;
    }

    record_list = virStringSplit(record->record_id_list, ",", 0);
    return virStringSplit(record_list[idx], ".", 2)[0];
}

void
zbs_lun_vhost_free_record(zbs_vhost_io_permission_record_t record)
{
    /* Mock free */
    return;
}

int
zbs_lun_vhost_set_single_access(zbs_t zbs, const char *target, uint32_t lun_id, bool single_access)
{
    zbs_lun_t_ptr zbs_lun_ref =
        helper_zbs_find_lun_ref(target, lun_id, g_zbs_service->zbs_luns, g_zbs_service->lun_num);

    if (zbs_lun_ref == NULL) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("Did not find lun %s %u"), target, lun_id);
        return -1;
    }

    zbs_lun_ref->permission_record_ptr->single_access = single_access;

    return 0;
}

bool
zbs_lun_vhost_get_single_access(zbs_vhost_io_permission_record_t record)
{
    return record->single_access;
}
