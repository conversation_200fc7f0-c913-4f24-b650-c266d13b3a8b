/*
 * Copyright (C) 2022 SmartX, Inc.
 */

#include <config.h>
#include <stdio.h>

#include "file.h"

#include "virbuffer.h"
#include "virstring.h"

char *
generateZBSConfigContent(char *group, const char *zookeeperHosts)
{
    char *output = NULL;
    virBuffer outputBuf = VIR_BUFFER_INITIALIZER;

    virBufferAsprintf(&outputBuf, "[%s]\nzookeeper = %s\n", group, zookeeperHosts);

    return virBufferContentAndReset(&outputBuf);
}
