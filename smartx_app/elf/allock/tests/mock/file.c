/*
 * Copyright (C) 2022 SmartX, Inc.
 */

#include <config.h>
#include <fcntl.h>
#include <stdio.h>

#include "file.h"

#include "string.h"

#include "virmock.h"
#include "virstring.h"

#include "mock.h"

#define VIR_FROM_THIS VIR_FROM_LOCKING

static const char *g_vhostMarkFile = "/etc/zbs/.boostenabled";
static const char *g_platformFile = "/etc/zbs/platform";
static const char *g_remoteStorageConfigFile = "/etc/elfvirt/storage_cluster.conf";
static const char *g_configFile = "/etc/zbs/zbs.conf";

/*
    We only mock results for intentional conditions, depending on
    whether some specific env variables are set. For other conditions,
    we shall still redirect the call to the real sys functions defined
    below.

    The mock implementation makes use of libvirt's virmock framework.
*/
static FILE *(*real_fopen)(const char *path, const char *mode);
static int (*real_access)(const char *path, int mode);
int (*real_virFileReadAll)(const char *path, int maxlen, char **buf);
gboolean (*real_g_key_file_load_from_file)(GKeyFile *key_file,
                                           const gchar *file,
                                           GKeyFileFlags flags,
                                           GError **error);

static void
init_syms(void)
{
    VIR_MOCK_REAL_INIT(fopen);
    VIR_MOCK_REAL_INIT(access);
    VIR_MOCK_REAL_INIT(virFileReadAll);
    VIR_MOCK_REAL_INIT(g_key_file_load_from_file);
}

int
access(const char *path, int mode)
{
    init_syms();

    if (STREQ(path, g_vhostMarkFile)) {
        if (getenv(allockMockVhostEnabled) || getenv(allockMockVhostDisabled)) {
            return 0;
        }

        if (getenv(allockMockVhostFileNoExist)) {
            errno = ENOENT;
            return -1;
        }
    }

    if (STREQ(path, g_platformFile)) {
        if (getenv(allockMockPlatformFileNotExist)) {
            errno = ENOENT;
            return -1;
        } else {
            return 0;
        }
    }

    if (STREQ(path, g_remoteStorageConfigFile)) {
        if (getenv(allockMockRemoteStorageNotConfigured)) {
            errno = ENOENT;
            return -1;
        } else {
            return 0;
        }
    }

    return real_access(path, mode);
}

FILE *
fopen(const char *path, const char *mode)
{
    init_syms();

    if (STREQ(path, g_vhostMarkFile)) {
        if (getenv(allockMockVhostEnabled)) {
            return fmemopen((void *)"true", 4, mode);
        } else if (getenv(allockMockVhostDisabled)) {
            return fmemopen((void *)"false", 5, mode);
        }
    }

    if (STREQ(path, g_platformFile)) {
        if (getenv(allockMockInDisaggregated)) {
            return fmemopen((void *)"elf", 3, mode);
        } else if (getenv(allockMockNotInDisaggregated)) {
            return fmemopen((void *)"kvm", 3, mode);
        }
    }

    return real_fopen(path, mode);
}

int
virFileReadAll(const char *path, int maxlen, char **buf)
{
    char *content = NULL;

    if (STREQ(path, g_configFile)) {
        content = generateZBSConfigContent("cluster", allockMockZookeeperHosts);
        *buf = content;
    }

    return strlen(content);
}

gboolean
g_key_file_load_from_file(GKeyFile *key_file,
                          const gchar *file,
                          GKeyFileFlags flags,
                          GError **error)
{
    if (STREQ(file, g_remoteStorageConfigFile)) {
        if (!real_g_key_file_load_from_file) {
            VIR_MOCK_REAL_INIT(g_key_file_load_from_file);
        }

        return real_g_key_file_load_from_file(
            key_file, "tests/testdata/storage_cluster.conf", flags, error);
    }

    return false;
}
