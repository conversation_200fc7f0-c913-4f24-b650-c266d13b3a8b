/*
 * Copyright (C) 2022 SmartX, Inc.
 */

#ifndef __ALLOCK_MOCK__
#define __ALLOCK_MOCK__

const char *allockMockVhostTest = "VHOST_TEST";
const char *allockMockVhostFileNoExist = "VHOST_FILE_NO_EXIST";
const char *allockMockVhostEnabled = "VHOST_ENABLED";
const char *allockMockVhostDisabled = "VHOST_DISABLED";

const char *allockMockDisaggregatedTest = "DISAGGREGATED_TEST";
const char *allockMockPlatformFileNotExist = "PLATFORM_FILE_NOT_EXIST";
const char *allockMockInDisaggregated = "DISAGGREGATED_ENABLED";
const char *allockMockNotInDisaggregated = "DISAGGREGATED_NOT_ENABLED";

const char *allockMockRemoteStorageNotConfigured = "REMOTE_STORAGE_NOT_CONFIGURED";
const char *allockMockRemoteStorageConfigured = "REMOTE_STORAGE_CONFIGURED";

const char *allockMockZookeeperHosts = "ZOOKEEPER_HOSTS";

char *generateZBSConfigContent(char *group, const char *zookeeperHosts);
#endif /* __ALLOCK_MOCK__ */
