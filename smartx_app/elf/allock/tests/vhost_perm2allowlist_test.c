/*
 * Copyright (C) 2022 SmartX, Inc.
 */
#include <config.h>

#include "testutils.h"

#include "utils.h"
#include "zbs_allow_list.h"
#include "zbs_vhost.c"

typedef struct _testData testData;
struct _testData {
    zbs_vhost_io_permission_record testRecord;
    allockLun testLun;
    bool expectedNullAllowList;
    char *expectedInitiator;
    bool expectedSingleAccess;
};

static int
testExpectedAllowListNewFromVhostPermission(const void *data)
{
    testData *testData = (void *)data;
    zbsLunAllowListPtr allowListPtr =
        zbsLunAllowListNewFromVhostPermission(&testData->testRecord, &testData->testLun);

    if (testData->expectedNullAllowList) {
        /* Only for wrong data case */
        if (allowListPtr != NULL) {
            return -1;
        }
    } else {
        if (!STREQ(allowListPtr->allowedInitiators, testData->expectedInitiator)) {
            VIR_TEST_VERBOSE("Unexpected initiator from zbsLunAllowListNewFromVhostPermission,\
                             expected=%s, actual=%s",
                             testData->expectedInitiator,
                             allowListPtr->allowedInitiators);
            return -1;
        }

        if (allowListPtr->singleAccess != testData->expectedSingleAccess) {
            VIR_TEST_VERBOSE("Unexpected single access from zbsLunAllowListNewFromVhostPermission,\
                             expected=%d, actual=%d",
                             testData->expectedSingleAccess,
                             allowListPtr->singleAccess);
            return -1;
        }
    }

    return 0;
}

int
main(int argc, char **argv)
{
    int ret = 0;
    char *nonSharedInitiator = "iqn.2013-11.org.smartx:vm_uuid1.host_uuid1.0";
    char *sharedInitiator = "iqn.2013-11.org.smartx:vm_uuid1.host_uuid1.1";

    testData correctDataOneOwner = {
        .testRecord = {.record_id_list = "vm_uuid1.host_uuid1", .single_access = true},
        .testLun =
            {
                .lunId = 1,
                .targetName = DEFAULT_TARGET,
                .assignedInitiator = nonSharedInitiator,
                .shared = false,
            },
        .expectedInitiator = nonSharedInitiator,
        .expectedSingleAccess = true,
    };

    testData correctDataOneOwnerShared = {
        .testRecord = {.record_id_list = "vm_uuid1.host_uuid1", .single_access = false},
        .testLun =
            {
                .lunId = 1,
                .targetName = DEFAULT_TARGET,
                .assignedInitiator = sharedInitiator,
                .shared = true,
            },
        .expectedInitiator = sharedInitiator,
        .expectedSingleAccess = false,
    };

    testData correctDataMultiOwnerShared = {
        .testRecord = {.record_id_list = "vm_uuid1.host_uuid1,vm_uuid2.host_uuid2",
                       .single_access = false},
        .testLun =
            {
                .lunId = 1,
                .targetName = DEFAULT_TARGET,
                .assignedInitiator = sharedInitiator,
                .shared = true,
            },
        .expectedInitiator = "iqn.2013-11.org.smartx:vm_uuid1.host_uuid1.1,"
                             "iqn.2013-11.org.smartx:vm_uuid2.host_uuid2.1",
        .expectedSingleAccess = false,
    };

    testData correctDataNoOwner = {
        .testRecord =
            {/* Empty */
             .record_id_list = ".",
             .single_access = true},
        .testLun =
            {
                .lunId = 1,
                .targetName = DEFAULT_TARGET,
                .assignedInitiator = nonSharedInitiator,
                .shared = false,
            },
        .expectedInitiator = "",
        .expectedSingleAccess = true,
    };

    testData correctDataOpenOwnership = {
        .testRecord = {.record_id_list = "*.*", .single_access = false},
        .testLun =
            {
                .lunId = 1,
                .targetName = DEFAULT_TARGET,
                .assignedInitiator = nonSharedInitiator,
                .shared = false,
            },
        .expectedInitiator = "*/*",
        .expectedSingleAccess = false,
    };

    /* host is open but vm is not */
    testData wrongDataInvalidPermission1 = {
        .testRecord = {.record_id_list = "vm1.*", .single_access = false},
        .testLun =
            {
                .lunId = 1,
                .targetName = DEFAULT_TARGET,
                .assignedInitiator = nonSharedInitiator,
                .shared = false,
            },
        .expectedNullAllowList = true,
    };

    /* vm is open but host is not */
    testData wrongDataInvalidPermission2 = {
        .testRecord = {.record_id_list = "*.host1", .single_access = false},
        .testLun =
            {
                .lunId = 1,
                .targetName = DEFAULT_TARGET,
                .assignedInitiator = nonSharedInitiator,
                .shared = false,
            },
        .expectedNullAllowList = true,
    };

    if (virTestRun("zbsLunAllowListNewFromVhostPermission1: Correct permission",
                   testExpectedAllowListNewFromVhostPermission,
                   &correctDataOneOwner) < 0) {
        ret = -1;
    }

    if (virTestRun("zbsLunAllowListNewFromVhostPermission2: Correct permission one owner",
                   testExpectedAllowListNewFromVhostPermission,
                   &correctDataOneOwnerShared) < 0) {
        ret = -1;
    }

    if (virTestRun(
            "zbsLunAllowListNewFromVhostPermission3: Correct permission for multi owner shared lun",
            testExpectedAllowListNewFromVhostPermission,
            &correctDataMultiOwnerShared) < 0) {
        ret = -1;
    }

    if (virTestRun("zbsLunAllowListNewFromVhostPermission4: No owner",
                   testExpectedAllowListNewFromVhostPermission,
                   &correctDataNoOwner) < 0) {
        ret = -1;
    }

    if (virTestRun("zbsLunAllowListNewFromVhostPermission5: Open Ownership",
                   testExpectedAllowListNewFromVhostPermission,
                   &correctDataOpenOwnership) < 0) {
        ret = -1;
    }

    if (virTestRun("zbsLunAllowListNewFromVhostPermission6: Invalid permission1",
                   testExpectedAllowListNewFromVhostPermission,
                   &wrongDataInvalidPermission1) < 0) {
        ret = -1;
    }

    if (virTestRun("zbsLunAllowListNewFromVhostPermission7: Invalid permission2",
                   testExpectedAllowListNewFromVhostPermission,
                   &wrongDataInvalidPermission2) < 0) {
        ret = -1;
    }

    return ret;
}
