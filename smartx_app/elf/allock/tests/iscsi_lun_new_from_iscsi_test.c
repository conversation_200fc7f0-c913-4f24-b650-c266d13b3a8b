/*
 * Copyright (C) 2022 SmartX, Inc.
 */
#include <config.h>

#include "lock_driver_allock.c"
#include "testutils.h"

typedef struct _testIscsiData testIscsiData;
struct _testIscsiData {
    const char *inputIscsiPath;
    const char *expectedTargetName;
    unsigned int expectedLunId;
};

static int
testLunNewFromIscsiPathSucceed(const void *data ATTRIBUTE_UNUSED)
{
    int ret = -1;
    testIscsiData *testData = (void *)data;
    allockLunPtr lun = allockLunNewFromIscsiPath(testData->inputIscsiPath);

    if (lun == NULL) {
        goto cleanup;
    }

    if (virTestCompareToString(lun->targetName, testData->expectedTargetName) < 0) {
        goto cleanup;
    }

    if (lun->lunId != testData->expectedLunId) {
        goto cleanup;
    }

    ret = 0;

cleanup:
    return ret;
}

static int
testLunNewFromIscsiPathFail(const void *data ATTRIBUTE_UNUSED)
{
    int ret = -1;
    testIscsiData *testData = (void *)data;
    allockLunPtr lun = allockLunNewFromIscsiPath(testData->inputIscsiPath);

    if (lun != NULL) {
        goto cleanup;
    }

    ret = 0;

cleanup:
    return ret;
}

int
main(int argc, char **argv)
{
    int ret = 0;

    testIscsiData correctDataSet = {
        .inputIscsiPath = "iqn.2016-02.com.smartx:system:zbs-iscsi-datastore-1641955505003r/115",
        .expectedTargetName = "zbs-iscsi-datastore-1641955505003r",
        .expectedLunId = 115,
    };

    testIscsiData wrongDataSet1 = {
        .inputIscsiPath = "iqn.2016-02.com.smartx:system:zbs-iscsi-datastore-1641955505003r",
        .expectedTargetName = NULL,
    };

    testIscsiData wrongDataSet2 = {
        .inputIscsiPath = "iqn.2016-02.com.smartx:zbs-iscsi-datastore-1641955505003r/114",
        .expectedTargetName = NULL,
    };

    if (virTestRun("allockLunNewFromIscsiPath1: correct path",
                   testLunNewFromIscsiPathSucceed,
                   &correctDataSet) < 0) {
        ret = -1;
    }

    if (virTestRun("allockLunNewFromIscsiPath2: wrong & missing lun id",
                   testLunNewFromIscsiPathFail,
                   &wrongDataSet1) < 0) {
        ret = -1;
    }

    if (virTestRun("allockLunNewFromIscsiPath3: wrong & missing 'system' in target name",
                   testLunNewFromIscsiPathFail,
                   &wrongDataSet2) < 0) {
        ret = -1;
    }

    return ret;
}
