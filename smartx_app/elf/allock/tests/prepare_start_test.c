/*
 * Copyright (C) 2022 SmartX, Inc.
 */

#include <config.h>

#include "viralloc.h"

#include "lock_driver_allock.c"
#include "utils.h"

/* === Non-shared LUN Tests === */

/*
 * Testcase: PrepareStart for power-on when allowlist=other_initiator,
 * single_access=true. Should succeed, and final lun state should be
 * allowlist=new_initiator, single_access=true.
 */
static int
testPrepareStartPowerOnOtherOwner(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;
    char *otherInitiator =
        "iqn.2013-11.org.smartx:6802a6c3-1747-4080-89d5-bfe7caa17245.other-host.0";

    if (allockTestCtxPresetLunStatus(testCtx, otherInitiator, true) < 0) {
        return -1;
    }

    callRet = allockPrepareStart(testCtx->lock, VIR_LOCK_MANAGER_STARTUP);

    if (callRet != 0) {
        return -1;
    }

    /* For PowerOn, force to update to the expected owner and single access */
    if (!allockTestCtxCheckLunStatus(testCtx, DEFAULT_ISCSI_INITIATOR, true)) {
        return -1;
    }

    return 0;
}

/*
 * Testcase: PrepareStart for power-on when lun is open ownership,
 * should succeed, final lun's state is allowlist=my_initiator,
 * single_access=true.
 */
static int
testPrepareStartPowerOnOpenOwnership(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;

    if (allockTestCtxPresetLunStatus(testCtx, "*/*", false) < 0) {
        return -1;
    }

    callRet = allockPrepareStart(testCtx->lock, VIR_LOCK_MANAGER_STARTUP);

    if (callRet != 0) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, DEFAULT_ISCSI_INITIATOR, true)) {
        return -1;
    }

    return 0;
}

/*
 * Testcase: PrepareStart for power-on, allowlist=src_initiator,
 * single access=false. Should fail.
 */
static int
testPrepareStartPowerOnNotSingleAccess(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;
    char *otherInitiator =
        "iqn.2013-11.org.smartx:6802a6c3-1747-4080-89d5-bfe7caa17245.other-host.0";

    if (allockTestCtxPresetLunStatus(testCtx, otherInitiator, false) < 0) {
        return -1;
    }

    callRet = allockPrepareStart(testCtx->lock, VIR_LOCK_MANAGER_STARTUP);

    /* For PowerOn, lun without open ownership but false single access,
       the operation should fail */
    if (callRet != -1) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, otherInitiator, false)) {
        return -1;
    }

    return 0;
}

/*
 * Testcase: PrepareStart for in-migration, allowlist=src_initiator,
 * single access=true. Should succeed, the lun's final state should
 * be single_access=false, allowlist=my_initiator,dest_initiator.
 */
static int
testPrepareStartInMigrationSuccess(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;
    char *srcInitiator = "iqn.2013-11.org.smartx:6802a6c3-1747-4080-89d5-bfe7caa17245.src-host.0";
    g_autofree char *initiatorList =
        allockTestJoinInitiators(srcInitiator, DEFAULT_ISCSI_INITIATOR);

    if (allockTestCtxPresetLunStatus(testCtx, srcInitiator, true) < 0) {
        return -1;
    }

    callRet = allockPrepareStart(testCtx->lock, VIR_LOCK_MANAGER_IN_MIGRATING);

    if (callRet != 0) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, initiatorList, false)) {
        return -1;
    }

    return 0;
}

/*
 * Testcase: PrepareStart for in-migration when lun is open ownership,
 * should succeed, and allow list does not change.
 */
static int
testPrepareStartInMigrationOpenOwnership(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;

    if (allockTestCtxPresetLunStatus(testCtx, "*/*", false) < 0) {
        return -1;
    }

    callRet = allockPrepareStart(testCtx->lock, VIR_LOCK_MANAGER_IN_MIGRATING);

    if (callRet != 0) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, "*/*", false)) {
        return -1;
    }

    return 0;
}

/*
 * Testcase: PrepareStart for in-migration, allowlist=src_initiator,
 * single access=false. Should fail.
 */
static int
testPrepareStartInMigrationNotSingleAccess(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;
    char *srcInitiator = "iqn.2013-11.org.smartx:6802a6c3-1747-4080-89d5-bfe7caa17245.src-host.0";

    if (allockTestCtxPresetLunStatus(testCtx, srcInitiator, false) < 0) {
        return -1;
    }

    callRet = allockPrepareStart(testCtx->lock, VIR_LOCK_MANAGER_IN_MIGRATING);

    if (callRet != -1) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, srcInitiator, false)) {
        return -1;
    }

    return 0;
}

/* === Shared LUN Tests === */

static int
testPrepareStartPowerOnSharedConflictOwner(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;
    char *otherInitiators =
        "iqn.2013-11.org.smartx:6802a6c3-1747-4080-89d5-bfe7caa17245."
        "48043bff-f43d-45da-97e3-a3d1d3c4c177.1,"
        "iqn.2013-11.org.smartx:other-vm.48043bff-f43d-45da-97e3-a3d1d3c4c177.1";

    g_autofree char *expectedOwners =
        allockTestJoinInitiators("iqn.2013-11.org.smartx:other-vm"
                                 ".48043bff-f43d-45da-97e3-a3d1d3c4c177.1",
                                 DEFAULT_ISCSI_SHARED_INITIATOR);

    if (allockTestCtxPresetLunStatus(testCtx, otherInitiators, false) < 0) {
        return -1;
    }

    callRet = allockPrepareStart(testCtx->lock, VIR_LOCK_MANAGER_STARTUP);

    if (callRet != 0) {
        return -1;
    }

    /* For PowerOn, force to remove conflict initiator and add its own initiator */
    if (!allockTestCtxCheckLunStatus(testCtx, expectedOwners, false)) {
        return -1;
    }

    return 0;
}

static int
testPrepareStartPowerOnSharedAlreadyOwner(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;
    char *initiator1 = "iqn.2013-11.org.smartx:other-vm.48043bff-f43d-45da-97e3-a3d1d3c4c177.1";
    g_autofree char *presetOwners =
        allockTestJoinInitiators(initiator1, DEFAULT_ISCSI_SHARED_INITIATOR);

    if (allockTestCtxPresetLunStatus(testCtx, presetOwners, false) < 0) {
        return -1;
    }

    callRet = allockPrepareStart(testCtx->lock, VIR_LOCK_MANAGER_STARTUP);

    if (callRet != 0) {
        return -1;
    }

    /* For PowerOn, force to remove conflict initiator and add its own initiator */
    if (!allockTestCtxCheckLunStatus(testCtx, presetOwners, false)) {
        return -1;
    }

    return 0;
}

static int
testPrepareStartPowerOnSharedEmptyOwnership(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;

    if (allockTestCtxPresetLunStatus(testCtx, "", false) < 0) {
        return -1;
    }

    callRet = allockPrepareStart(testCtx->lock, VIR_LOCK_MANAGER_STARTUP);

    if (callRet != 0) {
        return -1;
    }

    /* For PowerOn, add its own initiator */
    if (!allockTestCtxCheckLunStatus(testCtx, DEFAULT_ISCSI_SHARED_INITIATOR, false)) {
        return -1;
    }

    return 0;
}

static int
testPrepareStartPowerOnSharedOpenOwnership(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;

    if (allockTestCtxPresetLunStatus(testCtx, "*/*", false) < 0) {
        return -1;
    }

    callRet = allockPrepareStart(testCtx->lock, VIR_LOCK_MANAGER_STARTUP);

    if (callRet != 0) {
        return -1;
    }

    /* For PowerOn, do nothing on open ownership */
    if (!allockTestCtxCheckLunStatus(testCtx, "*/*", false)) {
        return -1;
    }

    return 0;
}

static int
testPrepareStartInMigrationSharedSuccess(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;
    char *srcInitiator = "iqn.2013-11.org.smartx:6802a6c3-1747-4080-89d5-bfe7caa17245."
                         "48043bff-f43d-45da-97e3-a3d1d3c4c177.1";
    char *destInitiator = DEFAULT_ISCSI_SHARED_INITIATOR;
    g_autofree char *expectedOwners = allockTestJoinInitiators(srcInitiator, destInitiator);

    if (allockTestCtxPresetLunStatus(testCtx, srcInitiator, false) < 0) {
        return -1;
    }

    callRet = allockPrepareStart(testCtx->lock, VIR_LOCK_MANAGER_IN_MIGRATING);

    if (callRet != 0) {
        return -1;
    }

    /* For InMigration, dest VM appends itself into it */
    if (!allockTestCtxCheckLunStatus(testCtx, expectedOwners, false)) {
        return -1;
    }

    return 0;
}

static int
testPrepareStartInMigrationSharedOpenOwnership(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;

    if (allockTestCtxPresetLunStatus(testCtx, "*/*", false) < 0) {
        return -1;
    }

    callRet = allockPrepareStart(testCtx->lock, VIR_LOCK_MANAGER_IN_MIGRATING);

    if (callRet != 0) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, "*/*", false)) {
        return -1;
    }

    return 0;
}

int
testRun(allockTestCtxPtr testCtx)
{
    if (virTestRun("allockLunPrepareStartPowerOn1: Success",
                   testPrepareStartPowerOnOtherOwner,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunPrepareStartPowerOn2: Success, Open Ownership",
                   testPrepareStartPowerOnOpenOwnership,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunPrepareStartPowerOn3: Failure, Not single access",
                   testPrepareStartPowerOnNotSingleAccess,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunPrepareStartInMigration1: Success",
                   testPrepareStartInMigrationSuccess,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunPrepareStartInMigration2: Success, Open Ownership",
                   testPrepareStartInMigrationOpenOwnership,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunPrepareStartInMigration3: Failure, Not single access",
                   testPrepareStartInMigrationNotSingleAccess,
                   testCtx) < 0) {
        return -1;
    }

    allockTestCtxSetAllockLun(testCtx, true, DEFAULT_ISCSI_SHARED_INITIATOR);

    if (virTestRun("allockLunPrepareStartPowerOnShared1: Success, shared & conflict onwership",
                   testPrepareStartPowerOnSharedConflictOwner,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunPrepareStartPowerOnShared2: Success, shared & already owner",
                   testPrepareStartPowerOnSharedAlreadyOwner,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunPrepareStartPowerOnShared3: Success, shared & empty ownership",
                   testPrepareStartPowerOnSharedEmptyOwnership,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunPrepareStartPowerOnShared4: Success, shared & open ownership",
                   testPrepareStartPowerOnSharedOpenOwnership,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunPrepareStartInMigrationShared1: Success, shared",
                   testPrepareStartInMigrationSharedSuccess,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunPrepareStartInMigrationShared2: Success, shared & open ownership",
                   testPrepareStartInMigrationSharedOpenOwnership,
                   testCtx) < 0) {
        return -1;
    }

    return 0;
}

ALLOCK_API_TEST(testRun);
