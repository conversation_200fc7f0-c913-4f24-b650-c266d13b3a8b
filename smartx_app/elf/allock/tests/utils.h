/*
 * Copyright (C) 2022 SmartX, Inc.
 */

#include <config.h>

#include "lock_driver.h"
#include "testutils.h"

/* Used as default client vm initiator for lock operation */
#define DEFAULT_ISCSI_INITIATOR                                                            \
    "iqn.2013-11.org.smartx:6802a6c3-1747-4080-89d5-bfe7caa17245.72897ded-83fc-4356-a0f5-" \
    "28ad6f4ae46d.0"
#define DEFAULT_ISCSI_SHARED_INITIATOR                                                     \
    "iqn.2013-11.org.smartx:6802a6c3-1747-4080-89d5-bfe7caa17245.72897ded-83fc-4356-a0f5-" \
    "28ad6f4ae46d.1"
#define DEFAULT_TARGET "iqn.2016-02.com.smartx:system:zbs-iscsi-datastore-1641955505003r"
#define DEFAULT_VM_NAME "vm_name"
#define DEFAULT_TEST_LUN_NUM 3

/*
 * We use allockTestCtx to store the unittest context for a testsuit. This records
 * virLockManager reference state, and also provides status check function according
 * to the lock state.
 */
typedef struct _allockTestCtx allockTestCtx;
typedef allockTestCtx *allockTestCtxPtr;

struct _allockTestCtx {
    virLockManagerPtr lock;
};

/*
 * This is a template for testing allock lock operation functions, such
 * as prepareStart, acquire, release, etc. It does initializes new test
 * context, pre-create test resources(LUNs) and init allock service before
 * tests start, and do some teardown job such as freeing context after
 * the tests are finished.
 */
#define ALLOCK_API_TEST(allockTestFunc)                                \
    int main(int argc, char **argv)                                    \
    {                                                                  \
        int ret = -1;                                                  \
        allockTestCtxPtr ctx = allockTestCtxNew(DEFAULT_TEST_LUN_NUM); \
        if (ctx == NULL) {                                             \
            VIR_TEST_VERBOSE("Failed to create test context\n");       \
            goto teardown;                                             \
        }                                                              \
        if (allockTestSetup(ctx) < 0) {                                \
            VIR_TEST_VERBOSE("Failed to setup resources for tests\n"); \
            goto teardown;                                             \
        }                                                              \
        if (allockInit(0, "", 0) < 0) {                                \
            VIR_TEST_VERBOSE("Failed to initialize allock\n");         \
            goto teardown;                                             \
        }                                                              \
        ret = allockTestFunc(ctx);                                     \
    teardown:                                                          \
        allockTestCtxFree(ctx);                                        \
        return ret;                                                    \
    }

allockTestCtxPtr allockTestCtxNew(unsigned int testLunNum);

void allockTestCtxFree(allockTestCtxPtr testCtx);

int allockTestSetup(const allockTestCtxPtr textCtx);

/*
 * allockTestCtxSetLunPreStatus: set the zbs LUN's status to a desired
 * status in order to start a specific testcase.
 */
int allockTestCtxPresetLunStatus(allockTestCtxPtr testCtx,
                                 const char *presetAllowedInitiators,
                                 bool presetSingleAccess);

/*
 * allockTestCtxCheckLunStatus: Check the if LUNs' status is the same as
 * expected status.
 */
bool allockTestCtxCheckLunStatus(const allockTestCtxPtr testCtx,
                                 const char *expectedAllowedInitiators,
                                 bool expectedSingleAccess);

char *allockTestJoinInitiators(char *initiator1, char *initiator2);

void allockTestCtxSetAllockLun(allockTestCtxPtr testCtx, bool shared, const char *initiator);
