/*
 * Copyright (C) 2022 SmartX, Inc.
 */

#include "utils.h"

#include "lock_driver_allock.c"

static virLockManagerPtr
allockTestLockManagerNew(void)
{
    virLockManagerPtr lock = NULL;
    allockPrivatePtr priv = NULL;

    if (VIR_ALLOC(lock) < 0) {
        VIR_TEST_VERBOSE("Failed to allocate memory\n");
        return NULL;
    }

    if (VIR_ALLOC(priv) < 0) {
        VIR_TEST_VERBOSE("Failed to allocate memory\n");
        goto error;
    }

    priv->res_count = 0;
    lock->privateData = priv;

    return lock;

error:
    allockFree(lock);
    return NULL;
}

static bool
allockTestIsLunExpectedStatus(allockLunPtr lun,
                              const char *expectedInititator,
                              bool expectedSingleAccess)
{
    zbs_lun_t_ptr zbsLun;
    int zbsRet;
    bool ret = false;

    if (VIR_ALLOC(zbsLun) < 0) {
        virReportError(VIR_ERR_INTERNAL_ERROR, _("%s"), "Failed to initialize a lun");
        return false;
    }

    zbsRet = zbs_lun_get(NULL, lun->targetName, lun->lunId, zbsLun);

    if (zbsRet < 0) {
        virReportError(
            VIR_ERR_INTERNAL_ERROR, _("Failed to find lun %s %u"), lun->targetName, lun->lunId);
        goto cleanup;
    }

    if (zbsLun->single_access != expectedSingleAccess) {
        virReportError(VIR_ERR_INTERNAL_ERROR,
                       _("Single Access: %d, expected: %d"),
                       zbsLun->single_access,
                       expectedSingleAccess);
        goto cleanup;
    }

    if (!STREQ(zbsLun->allowed_initiators, expectedInititator)) {
        virReportError(VIR_ERR_INTERNAL_ERROR,
                       _("Initiator: %s, expected: %s"),
                       zbsLun->allowed_initiators,
                       expectedInititator);
        goto cleanup;
    }

    ret = true;

cleanup:
    zbs_lun_free_allowed_initiators_memory(zbsLun);
    return ret;
}

allockTestCtxPtr
allockTestCtxNew(unsigned int testLunNum)
{
    allockTestCtxPtr testCtx = NULL;
    virLockManagerPtr lock = NULL;
    allockPrivatePtr priv = NULL;

    if (VIR_ALLOC(testCtx) < 0) {
        VIR_TEST_VERBOSE("Failed to allocate memory\n");
        return NULL;
    }

    lock = allockTestLockManagerNew();
    if (lock == NULL) {
        goto error;
    }
    priv = lock->privateData;

    for (size_t i = 1; i <= testLunNum; i++) {
        allockLunPtr lun = allockLunNew(DEFAULT_TARGET, i);
        if (lun == NULL) {
            goto error;
        }
        lun->assignedInitiator = g_strdup(DEFAULT_ISCSI_INITIATOR);
        lun->shared = false;
        lun->storageIP ="127.0.0.1";
        lun->allowListDriver = &zbsIscsiAllowListDrv;
        priv->res_args[priv->res_count] = lun;
        priv->res_count++;
    }

    testCtx->lock = lock;
    return testCtx;

error:
    allockTestCtxFree(testCtx);
    return NULL;
}

void
allockTestCtxFree(allockTestCtxPtr testCtx)
{
    if (testCtx == NULL) {
        return;
    }

    if (testCtx->lock != NULL) {
        allockFree(testCtx->lock);
    }

    VIR_FREE(testCtx);
}

int
allockTestSetup(const allockTestCtxPtr testCtx)
{
    allockPrivatePtr priv = testCtx->lock->privateData;

    if (zbs_initialize_service() < 0) {
        VIR_TEST_VERBOSE("Failed to initialize zbs service\n");
        return -1;
    }

    for (size_t i = 0; i < priv->res_count; i++) {
        if (zbs_lun_create(
                NULL, priv->res_args[i]->targetName, priv->res_args[i]->lunId, "*/*", false) < 0) {
            VIR_TEST_VERBOSE("Failed to prepare test lun\n");
            return -1;
        }
    }

    return 0;
}

int
allockTestCtxPresetLunStatus(const allockTestCtxPtr testCtx,
                             const char *presetAllowedInitiators,
                             bool presetSingleAccess)
{
    allockLunPtr tempLunPtr = NULL;
    allockPrivatePtr priv = testCtx->lock->privateData;

    for (size_t i = 0; i < priv->res_count; i++) {
        tempLunPtr = priv->res_args[i];
        if (zbs_lun_update(NULL,
                           tempLunPtr->targetName,
                           tempLunPtr->lunId,
                           presetAllowedInitiators,
                           presetSingleAccess) < 0) {

            VIR_TEST_VERBOSE("Failed to update lun"
                             "target=%s, lun_id=%u\n",
                             tempLunPtr->targetName,
                             tempLunPtr->lunId);
            return -1;
        }
    }

    return 0;
}

bool
allockTestCtxCheckLunStatus(const allockTestCtxPtr testCtx,
                            const char *expectedAllowedInitiators,
                            bool expectedSingleAccess)
{
    allockLunPtr tempLunPtr = NULL;
    allockPrivatePtr priv = testCtx->lock->privateData;

    for (size_t i = 0; i < priv->res_count; i++) {
        tempLunPtr = priv->res_args[i];
        if (!allockTestIsLunExpectedStatus(
                tempLunPtr, expectedAllowedInitiators, expectedSingleAccess)) {
            return false;
        }
    }

    return true;
}

char *
allockTestJoinInitiators(char *initiator1, char *initiator2)
{
    virBuffer strListBuf = VIR_BUFFER_INITIALIZER;
    virBufferAsprintf(&strListBuf, "%s,%s", initiator1, initiator2);
    return virBufferContentAndReset(&strListBuf);
}

void
allockTestCtxSetAllockLun(allockTestCtxPtr testCtx, bool shared, const char *initiator)
{
    allockPrivatePtr priv = testCtx->lock->privateData;

    for (size_t i = 0; i < priv->res_count; i++) {
        priv->res_args[i]->shared = shared;
        priv->res_args[i]->assignedInitiator = g_strdup(initiator);
    }
}
