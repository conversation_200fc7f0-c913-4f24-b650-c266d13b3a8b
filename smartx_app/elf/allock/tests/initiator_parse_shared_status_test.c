/*
 * Copyright (C) 2022 SmartX, Inc.
 */
#include <config.h>

#include "lock_driver_allock.c"
#include "testutils.h"

typedef struct _testInitiatorData testInitiatorData;
struct _testInitiatorData {
    char *inputInitiatorName;
    int expectedRet;
    bool expectedShared;
};

static int
testInitiatorParseSharedStatus(const void *data ATTRIBUTE_UNUSED)
{
    testInitiatorData *testData = (void *)data;
    bool shared;
    int callRet;
    int ret = -1;
    callRet = initiatorParseSharedStatus(testData->inputInitiatorName, &shared);

    if (callRet != testData->expectedRet) {
        goto cleanup;
    }

    if (callRet == 0) {
        if (shared != testData->expectedShared) {
            goto cleanup;
        }
    }

    ret = 0;

cleanup:
    return ret;
}

int
main(int argc, char **argv)
{
    int ret = 0;

    testInitiatorData correctDataSet1 = {
        .inputInitiatorName = "iqn.2013-11.org.smartx:\
                               6802a6c3-1747-4080-89d5-bfe7caa17245.\
                               72897ded-83fc-4356-a0f5-28ad6f4ae46d.0",
        .expectedRet = 0,
        .expectedShared = false,
    };

    testInitiatorData correctDataSet2 = {
        .inputInitiatorName = "iqn.2013-11.org.smartx:\
                               6802a6c3-1747-4080-89d5-bfe7caa17245.\
                               72897ded-83fc-4356-a0f5-28ad6f4ae46d.1",
        .expectedRet = 0,
        .expectedShared = true,
    };

    testInitiatorData wrongDataSet1 = {
        .inputInitiatorName = "iqn.2013-11.org.smartx:\
                               6802a6c3-1747-4080-89d5-bfe7caa17245.1",
        .expectedRet = -1,
    };

    testInitiatorData wrongDataSet2 = {
        .inputInitiatorName = "iqn.2013-11.org.smartx:\
                               6802a6c3-1747-4080-89d5-bfe7caa17245.\
                               72897ded-83fc-4356-a0f5-28ad6f4ae46d.5",
        .expectedRet = -1,
    };

    testInitiatorData wrongDataSet3 = {
        .inputInitiatorName = "iqn.2013-11.org.smartx.5",
        .expectedRet = -1,
    };

    testInitiatorData wrongDataSet4 = {
        .inputInitiatorName = "iqn.2013-11.org.smartx:\
                               6802a6c3-1747-4080-89d5-bfe7caa17245.\
                               72897ded-83fc-4356-a0f5-28ad6f4ae46d.abc123.5",
        .expectedRet = -1,
    };

    if (virTestRun("initiatorParseSharedStatus1: correct & non-shared",
                   testInitiatorParseSharedStatus,
                   &correctDataSet1) < 0) {
        ret = -1;
    }

    if (virTestRun("initiatorParseSharedStatus2: correct & shared",
                   testInitiatorParseSharedStatus,
                   &correctDataSet2) < 0) {
        ret = -1;
    }

    if (virTestRun("initiatorParseSharedStatus3: wrong & too few uuid",
                   testInitiatorParseSharedStatus,
                   &wrongDataSet1) < 0) {
        ret = -1;
    }

    if (virTestRun("initiatorParseSharedStatus4: wrong shared bit",
                   testInitiatorParseSharedStatus,
                   &wrongDataSet2) < 0) {
        ret = -1;
    }

    if (virTestRun("initiatorParseSharedStatus5: wrong iqn format",
                   testInitiatorParseSharedStatus,
                   &wrongDataSet3) < 0) {
        ret = -1;
    }

    if (virTestRun("initiatorParseSharedStatus6: unexpected uuid parts number",
                   testInitiatorParseSharedStatus,
                   &wrongDataSet4) < 0) {
        ret = -1;
    }

    return ret;
}
