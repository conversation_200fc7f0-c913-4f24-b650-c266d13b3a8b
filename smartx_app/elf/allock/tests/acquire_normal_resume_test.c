/*
 * Copyright (C) 2022 SmartX, Inc.
 */

#include <config.h>

#include "viralloc.h"

#include "lock_driver_allock.c"
#include "utils.h"

/* === Non-shared LUN Tests === */

/*
 * Testcase: Acquire for normal resume when lun is my_initiator.
 * Should succeed, and allow list does not change.
 */
static int
testNormalResumeCorrectOwner(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;

    if (allockTestCtxPresetLunStatus(testCtx, DEFAULT_ISCSI_INITIATOR, true) < 0) {
        return -1;
    }

    callRet = allockAcquire(testCtx->lock, NULL, VIR_LOCK_MANAGER_ACQUIRE_RESUME, 0, NULL);

    if (callRet != 0) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, DEFAULT_ISCSI_INITIATOR, true)) {
        return -1;
    }

    return 0;
}

/*
 * Testcase: Acquire for normal resume when lun is open ownership.
 * Should succeed, and allow list does not change.
 */
static int
testNormalResumeOpenOwnership(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;

    if (allockTestCtxPresetLunStatus(testCtx, "*/*", false) < 0) {
        return -1;
    }

    callRet = allockAcquire(testCtx->lock, NULL, VIR_LOCK_MANAGER_ACQUIRE_RESUME, 0, NULL);

    if (callRet != 0) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, "*/*", false)) {
        return -1;
    }

    return 0;
}

/*
 * Testcase: Acquire for normal resume when lun allowlist=other_initiator.
 * Should fail.
 */
static int
testNormalResumeWrongOwner(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;
    char *otherInitiator =
        "iqn.2013-11.org.smartx:6802a6c3-1747-4080-89d5-bfe7caa17245.other-host.0";

    if (allockTestCtxPresetLunStatus(testCtx, otherInitiator, true) < 0) {
        return -1;
    }

    callRet = allockAcquire(testCtx->lock, NULL, VIR_LOCK_MANAGER_ACQUIRE_RESUME, 0, NULL);

    if (callRet != -1) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, otherInitiator, true)) {
        return -1;
    }

    return 0;
}

/*
 * Testcase: Acquire for normal resume when lun allowlist=empty.
 * Should fail.
 */
static int
testNormalResumeEmptyOwnership(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;

    if (allockTestCtxPresetLunStatus(testCtx, "", true) < 0) {
        return -1;
    }

    callRet = allockAcquire(testCtx->lock, NULL, VIR_LOCK_MANAGER_ACQUIRE_RESUME, 0, NULL);

    if (callRet != -1) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, "", true)) {
        return -1;
    }

    return 0;
}

/* === Shared LUN Tests === */

static int
testNormalResumeSharedIsOwner(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;
    char *otherInitiator = "iqn.2013-11.org.smartx:6f30bfc2-ded5-45dc-8875-f7d38f8cb45e."
                           "48043bff-f43d-45da-97e3-a3d1d3c4c177.1";
    g_autofree char *presetOwners =
        allockTestJoinInitiators(DEFAULT_ISCSI_SHARED_INITIATOR, otherInitiator);

    if (allockTestCtxPresetLunStatus(testCtx, presetOwners, false) < 0) {
        return -1;
    }

    callRet = allockAcquire(testCtx->lock, NULL, VIR_LOCK_MANAGER_ACQUIRE_RESUME, 0, NULL);

    if (callRet != 0) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, presetOwners, false)) {
        return -1;
    }

    return 0;
}

static int
testNormalResumeSharedIsExclusiveOwner(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;
    char *presetOwners = DEFAULT_ISCSI_SHARED_INITIATOR;

    if (allockTestCtxPresetLunStatus(testCtx, presetOwners, false) < 0) {
        return -1;
    }

    callRet = allockAcquire(testCtx->lock, NULL, VIR_LOCK_MANAGER_ACQUIRE_RESUME, 0, NULL);

    if (callRet != 0) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, presetOwners, false)) {
        return -1;
    }

    return 0;
}

static int
testNormalResumeSharedConflictOwner(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;
    char *presetOwners =
        "iqn.2013-11.org.smartx:"
        "43af4a60-ab39-40db-8586-9046b4cfdc8f.48043bff-f43d-45da-97e3-a3d1d3c4c177.1,"
        "iqn.2013-11.org.smartx:6f30bfc2-ded5-45dc-8875-f7d38f8cb45e."
        "48043bff-f43d-45da-97e3-a3d1d3c4c177.1";

    if (allockTestCtxPresetLunStatus(testCtx, presetOwners, false) < 0) {
        return -1;
    }

    callRet = allockAcquire(testCtx->lock, NULL, VIR_LOCK_MANAGER_ACQUIRE_RESUME, 0, NULL);

    if (callRet != -1) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, presetOwners, false)) {
        return -1;
    }

    return 0;
}

static int
testNormalResumeSharedNotOwner(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;
    char *presetOwners =
        "iqn.2013-11.org.smartx:"
        "6802a6c3-1747-4080-89d5-bfe7caa17245.48043bff-f43d-45da-97e3-a3d1d3c4c177.1,"
        "iqn.2013-11.org.smartx:6f30bfc2-ded5-45dc-8875-f7d38f8cb45e."
        "48043bff-f43d-45da-97e3-a3d1d3c4c177.1";

    if (allockTestCtxPresetLunStatus(testCtx, presetOwners, false) < 0) {
        return -1;
    }

    callRet = allockAcquire(testCtx->lock, NULL, VIR_LOCK_MANAGER_ACQUIRE_RESUME, 0, NULL);

    if (callRet != -1) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, presetOwners, false)) {
        return -1;
    }

    return 0;
}

static int
testNormalResumeSharedOpenOwnership(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;

    if (allockTestCtxPresetLunStatus(testCtx, "*/*", false) < 0) {
        return -1;
    }

    callRet = allockAcquire(testCtx->lock, NULL, VIR_LOCK_MANAGER_ACQUIRE_RESUME, 0, NULL);

    if (callRet != 0) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, "*/*", false)) {
        return -1;
    }

    return 0;
}

int
testRun(allockTestCtxPtr testCtx)
{
    if (virTestRun("allockLunAcquireNormalResume1: Success, correct owner",
                   testNormalResumeCorrectOwner,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunAcquireNormalResume2: Success, open ownership",
                   testNormalResumeOpenOwnership,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunAcquireNormalResume3: Failure, wrong owner",
                   testNormalResumeWrongOwner,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunAcquireNormalResume4: Failure, empty ownership",
                   testNormalResumeEmptyOwnership,
                   testCtx) < 0) {
        return -1;
    }

    allockTestCtxSetAllockLun(testCtx, true, DEFAULT_ISCSI_SHARED_INITIATOR);

    if (virTestRun("allockLunAcquireNormalResumeShared1: Success, shared lun & in ownership",
                   testNormalResumeSharedIsOwner,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunAcquireNormalShared2: Success, shared lun & mono ownership",
                   testNormalResumeSharedIsExclusiveOwner,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunAcquireNormalResumeShared3: Failure, shared lun & conflict initiator",
                   testNormalResumeSharedConflictOwner,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunAcquireNormalResumeShared4: Failure, shared lun & initiator not owner",
                   testNormalResumeSharedNotOwner,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunAcquireNormalResumeShared5: Failure, shared lun & initiator not owner",
                   testNormalResumeSharedOpenOwnership,
                   testCtx) < 0) {
        return -1;
    }

    return 0;
}

ALLOCK_API_TEST(testRun);
