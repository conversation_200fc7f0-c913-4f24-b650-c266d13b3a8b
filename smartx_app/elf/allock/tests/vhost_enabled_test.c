/*
 * Copyright (C) 2022 SmartX, Inc.
 */

#include <config.h>

#include "testutils.h"

#include "lock_driver_allock.c"
#include "mock.h"

typedef struct _testData testData;
struct _testData {
    /* testFlag is used to indicate the file mock module
       what should be done */
    const char *testFlag;
    bool expectedRet;
};

static int
testVhostEnabled(const void *data)
{
    testData *testData = (void *)data;
    bool ret;

    if (testData->testFlag != NULL) {
        setenv(testData->testFlag, "1", 1);
    }

    ret = allockIsVhostEnabled();

    if (testData->testFlag != NULL) {
        unsetenv(testData->testFlag);
    }

    if (ret != testData->expectedRet) {
        return -1;
    }

    return 0;
}

static int
mymain(void)
{
    setenv(allockMockVhostTest, "1", 1);

    testData test1 = {
        .testFlag = allockMockVhostFileNoExist,
        .expectedRet = false,
    };

    testData test2 = {
        .testFlag = allockMockVhostEnabled,
        .expectedRet = true,
    };

    testData test3 = {
        .testFlag = allockMockVhostDisabled,
        .expectedRet = false,
    };

    if (virTestRun("allockIsVhostEnabled1: Vhost file not exist", testVhostEnabled, &test1) < 0) {
        return -1;
    }

    if (virTestRun("allockIsVhostEnabled2: Vhost enabled", testVhostEnabled, &test2) < 0) {
        return -1;
    }

    if (virTestRun("allockIsVhostEnabled3: Vhost disabled", testVhostEnabled, &test3) < 0) {
        return -1;
    }

    return 0;
}

/* Replace the file related functions with mock functions defined in .so */
VIR_TEST_MAIN_PRELOAD(mymain, abs_builddir "/file_mock.so")
