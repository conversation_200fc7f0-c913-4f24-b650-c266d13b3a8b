/*
 * Copyright (C) 2022 SmartX, Inc.
 */
#include <config.h>

#include "testutils.h"
#include "viralloc.h"

#include "lock_driver_allock.c"
#include "utils.h"

typedef struct _testData testData;
struct _testData {
    char *initiator;
    const char *host;
    int bus;
    char *expectedAllowlistDrv;
    int expectedRet;
};

static int
testAddResource(const void *data)
{
    int callRet;
    allockPrivate priv = {
        .vm_name = DEFAULT_VM_NAME,
        .res_count = 0,
    };
    virLockManager lock = {.privateData = &priv};

    testData *testData = (void *)data;

    virLockManagerParam lparams[] = {
        {
            .type = VIR_LOCK_MANAGER_PARAM_TYPE_ULONG,
            .key = "initiator.iqn",
            .value = {.cstr = testData->initiator},
        },
        {
            .type = VIR_LOCK_MANAGER_PARAM_TYPE_STRING,
            .key = "host.name",
            .value = {.cstr = testData->host},
        },
        {
            .type = VIR_LOCK_MANAGER_PARAM_TYPE_INT,
            .key = "bus",
            .value = {.iv = testData->bus},
        }
    };
    char *path = "iqn.2016-02.com.smartx:system:zbs-iscsi-datastore-1641955505003r/15";

    callRet = allockAddResource(&lock, 0, path, 3, lparams, 0);
    if (callRet < testData->expectedRet) {
        return -1;
    }

    if (testData->expectedRet < 0) {
        return 0;
    }

    if (priv.res_count != 1) {
        return -1;
    }

    if (STRNEQ(priv.res_args[0]->storageIP, testData->host)) {
        return -1;
    }

    if (STRNEQ(priv.res_args[0]->allowListDriver->driverType, testData->expectedAllowlistDrv)) {
        return -1;
    }

    return 0;
}

int
main(int argc, char **argv)
{
    testData test1 = {
        .initiator = DEFAULT_ISCSI_INITIATOR,
        .host = "***********",
        .bus = 0,
        .expectedAllowlistDrv = "ZBS_ISCSI",
    };

    testData test2 = {
        .initiator = DEFAULT_ISCSI_SHARED_INITIATOR,
        .host = "127.0.0.1",
        .bus = 3,
        .expectedAllowlistDrv = "ZBS_ISCSI",
    };

    testData test3 = {
        .initiator = DEFAULT_ISCSI_INITIATOR,
        .host = "***********",
        .bus = 0,
        .expectedAllowlistDrv = "ZBS_ISCSI",
    };

    testData test4 = {
        .initiator = DEFAULT_ISCSI_SHARED_INITIATOR,
        .host = "127.0.0.1",
        .bus = 3,
        .expectedAllowlistDrv = "ZBS_VHOST",
    };

    testData test5 = {
        .initiator = DEFAULT_ISCSI_SHARED_INITIATOR,
        .host = "127.0.0.1",
        .bus = -1,
        .expectedAllowlistDrv = "ZBS_VHOST",
        .expectedRet = -1,
    };

    g_defaultZbsAllowListDrv = &zbsIscsiAllowListDrv;

    if (virTestRun("allockAddResource1: Success, non-shared, remote storage IP, ZBS_ISCSI+IDE",
                   testAddResource,
                   &test1) < 0) {
        return -1;
    }

    if (virTestRun("allockAddResource2: Success, shared, local storage IP, ZBS_ISCSI+VIRTIO",
                   testAddResource, &test2) < 0) {
        return -1;
    }

    g_defaultZbsAllowListDrv = &zbsVhostAllowListDrv;

    if (virTestRun("allockAddResource3: Success, non-shared, remote storage IP, ZBS_VHOST+IDE",
                   testAddResource,
                   &test3) < 0) {
        return -1;
    }

    if (virTestRun("allockAddResource4: Success, shared, local storage IP, ZBS_VHOST+VIRTIO",
                   testAddResource, &test4) < 0) {
        return -1;
    }

    if (virTestRun("allockAddResource5: Success, shared, local storage IP, ZBS_VHOST+UNKNOWN_BUS",
                   testAddResource, &test5) < 0) {
        return -1;
    }

    return 0;
}
