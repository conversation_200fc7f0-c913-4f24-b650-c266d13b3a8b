/*
 * Copyright (C) 2024 SmartX, Inc.
 */
#include <config.h>

#include "testutils.h"

#include "lock_driver_allock.c"

typedef struct _testExtractVmUuidData testExtractVmUuidData;
struct _testExtractVmUuidData {
    char *testInitiator;
    int expectedRet;
    char *expectedVmUuid;
};

typedef struct _testExtractConflictInitiatorsdData testExtractConflictInitiatorsdData;
struct _testExtractConflictInitiatorsdData {
    char *targetInitiator;
    char *existingInitiators;
    char *expectedConflictInitiators;
    int expectedConflictNumber;
    int expectedRet;
};

typedef struct _testCheckVmUuidData testCheckVmUuidData;
struct _testCheckVmUuidData {
    char *initiator;
    char *vmUuid;
    bool expectedMatched;
    int expectedRet;
};

static int
testExtractVmUuidsFromInitiator(const void *data)
{
    testExtractVmUuidData *td = (void *)data;

    g_autofree char *hostUuid = NULL;
    g_autofree char *vmUuid = NULL;

    int ret = initiatorParseVmUuid(td->testInitiator, &vmUuid);

    if (td->expectedRet == -1) {
        if (ret != td->expectedRet) {
            VIR_TEST_VERBOSE("Unexpected return value from vhostLunExtractUuidsFromInitiator,\
                            expected=%d, actual=%d",
                             td->expectedRet,
                             ret);
            return -1;
        }

        if (hostUuid != NULL) {
            VIR_TEST_VERBOSE("Unexpected host uuid from vhostLunExtractUuidsFromInitiator,\
                             expected=NULL, actual=%s",
                             hostUuid);
            return -1;
        }

        if (vmUuid != NULL) {
            VIR_TEST_VERBOSE("Unexpected vm uuid from vhostLunExtractUuidsFromInitiator,\
                             expected=NULL, actual=%s",
                             vmUuid);
            return -1;
        }
    } else {
        if (!STREQ(vmUuid, td->expectedVmUuid)) {
            VIR_TEST_VERBOSE("Unexpected vm uuid from vhostLunExtractUuidsFromInitiator,\
                             expected=%s, actual=%s",
                             vmUuid,
                             td->expectedVmUuid);
            return -1;
        }
    }

    return 0;
}

static int
testExtractConflictInitiators(const void *data)
{
    testExtractConflictInitiatorsdData *td = (void *)data;
    int nConflict = 0;
    VIR_AUTOSTRINGLIST conflictInitiators = NULL;
    int ret;
    g_autofree char *result = NULL;

    ret = initiatorExtractConflictInitiators(
        td->targetInitiator, td->existingInitiators, &nConflict, &conflictInitiators);
    if (ret != td->expectedRet) {
        VIR_TEST_VERBOSE("Unexpected return value from initiatorExtractConflictInitiators,\
                            expected=%d, actual=%d",
                         td->expectedRet,
                         ret);
        return -1;
    }

    if (td->expectedRet == 0) {
        if (nConflict != td->expectedConflictNumber) {
            VIR_TEST_VERBOSE("Unexpected conflict initiators number: %d, expected: %d",
                             nConflict,
                             td->expectedConflictNumber);
            return -1;
        }

        if (td->expectedConflictNumber > 0) {
            result = virStringListJoin((const char **)conflictInitiators, ",");
            if (!STREQ(result, td->expectedConflictInitiators)) {
                VIR_TEST_VERBOSE("Unexpected conflict initiators: %s, expected: %s",
                                 result,
                                 td->expectedConflictInitiators);
                return -1;
            }
        }
    } else {
        if (conflictInitiators != NULL) {
            VIR_TEST_VERBOSE("Unexpected conflict initiators not null");
            return -1;
        }

        if (nConflict != 0) {
            VIR_TEST_VERBOSE("Unexpected conflict initiators number not 0");
            return -1;
        }
    }

    return 0;
}

static int
testCheckVmUuidMatched(const void *data)
{
    testCheckVmUuidData *td = (void *)data;
    bool matched;
    int ret;

    ret = initiatorCheckVmUuidMatched(td->initiator, td->vmUuid, &matched);
    if (ret != td->expectedRet) {
        VIR_TEST_VERBOSE("Unexpected return value from initiatorCheckMatchVmUuid,\
                            expected=%d, actual=%d",
                         td->expectedRet,
                         ret);
        return -1;
    }

    if (td->expectedRet == 0) {
        if (matched != td->expectedMatched) {
            VIR_TEST_VERBOSE("Unexpected initiator vm uuid match: %d, expected: %d",
                             matched,
                             td->expectedMatched);
            return -1;
        }
    }

    return 0;
}

static int
doTestExtractVmUuidsFromInitiator(void)
{
    int ret = 0;

    /*
        Correct initiator format is:
            iqn.2013-11.org.smartx:<vm_uuid>.<host_uuid>.<shared bit>
        Any input that violates this format will be given -1 return value.
    */
    testExtractVmUuidData correctDataNonShared = {
        .testInitiator = "iqn.2013-11.org.smartx:vm_uuid1.host_uuid1.0",
        .expectedRet = 0,
        .expectedVmUuid = "vm_uuid1",
    };

    testExtractVmUuidData correctDataShared = {
        .testInitiator = "iqn.2013-11.org.smartx:vm_uuid1.host_uuid1.1",
        .expectedRet = 0,
        .expectedVmUuid = "vm_uuid1",
    };

    testExtractVmUuidData wrongData1 = {
        .testInitiator = "iqn.2013-11.org.smartx:host_uuid1.0",
        .expectedRet = -1,
    };

    testExtractVmUuidData wrongData2 = {
        .testInitiator = "iqn.2013-11.org.smartx.host_uuid1.0",
        .expectedRet = -1,
    };

    testExtractVmUuidData wrongData3 = {
        .testInitiator = "iqn.2013-11.org.smartx:vm_uuid1.host_uuid1",
        .expectedRet = -1,
    };

    if (virTestRun("vhostLunExtractUuidsFromInitiator1: Correct initiator",
                   testExtractVmUuidsFromInitiator,
                   &correctDataNonShared) < 0) {
        ret = -1;
    }

    if (virTestRun("vhostLunExtractUuidsFromInitiator2: Correct Initiator (shared)",
                   testExtractVmUuidsFromInitiator,
                   &correctDataShared) < 0) {
        ret = -1;
    }

    if (virTestRun("vhostLunExtractUuidsFromInitiator3: Wrong format",
                   testExtractVmUuidsFromInitiator,
                   &wrongData1) < 0) {
        ret = -1;
    }

    if (virTestRun("vhostLunExtractUuidsFromInitiator4: Wrong format",
                   testExtractVmUuidsFromInitiator,
                   &wrongData2) < 0) {
        ret = -1;
    }

    if (virTestRun("vhostLunExtractUuidsFromInitiator5: Wrong format",
                   testExtractVmUuidsFromInitiator,
                   &wrongData3) < 0) {
        ret = -1;
    }

    return ret;
}

static int
doTestExtractConflictInitiators(void)
{
    int ret = 0;

    testExtractConflictInitiatorsdData conflictInitiatorData1 = {
        .existingInitiators = "iqn.2013-11.org.smartx:b4c555db-d2e2-40f7-8c3a-4e1915cb4ab9"
                              ".83f6f3a1-3e60-49e1-b14a-a1d486394a8b.1,"
                              "iqn.2013-11.org.smartx:cd02f888-ffe1-491f-805f-a64eb1e630e8"
                              ".83f6f3a1-3e60-49e1-b14a-a1d486394a8b.1,"
                              "iqn.2013-11.org.smartx:be37f095-058a-442f-bfd7-2ca06a03426d"
                              ".ee5db71c-3f81-43f0-8777-36469b460445.1",
        .expectedConflictNumber = 1,
        .expectedConflictInitiators = "iqn.2013-11.org.smartx:cd02f888-ffe1-491f-805f-a64eb1e630e8"
                                      ".83f6f3a1-3e60-49e1-b14a-a1d486394a8b.1",
        .targetInitiator = "iqn.2013-11.org.smartx:cd02f888-ffe1-491f-805f-a64eb1e630e8"
                           ".ee5db71c-3f81-43f0-8777-36469b460445.1",
        .expectedRet = 0,
    };

    if (virTestRun("initiatorExtractConflictInitiators: One conflict initiator",
                   testExtractConflictInitiators,
                   &conflictInitiatorData1) < 0) {
        ret = -1;
    }

    testExtractConflictInitiatorsdData conflictInitiatorData2 = {
        .existingInitiators = "iqn.2013-11.org.smartx:b4c555db-d2e2-40f7-8c3a-4e1915cb4ab9"
                              ".83f6f3a1-3e60-49e1-b14a-a1d486394a8b.1,"
                              "iqn.2013-11.org.smartx:be37f095-058a-442f-bfd7-2ca06a03426d"
                              ".83f6f3a1-3e60-49e1-b14a-a1d486394a8b.1,"
                              "iqn.2013-11.org.smartx:b4c555db-d2e2-40f7-8c3a-4e1915cb4ab9"
                              ".ee5db71c-3f81-43f0-8777-36469b460445.1,"
                              "iqn.2013-11.org.smartx:b4c555db-d2e2-40f7-8c3a-4e1915cb4ab9"
                              ".b917db3f-7cfa-48df-bdd5-e7e19867f71c.1",
        .expectedConflictNumber = 3,
        .expectedConflictInitiators = "iqn.2013-11.org.smartx:b4c555db-d2e2-40f7-8c3a-4e1915cb4ab9"
                                      ".83f6f3a1-3e60-49e1-b14a-a1d486394a8b.1,"
                                      "iqn.2013-11.org.smartx:b4c555db-d2e2-40f7-8c3a-4e1915cb4ab9"
                                      ".ee5db71c-3f81-43f0-8777-36469b460445.1,"
                                      "iqn.2013-11.org.smartx:b4c555db-d2e2-40f7-8c3a-4e1915cb4ab9"
                                      ".b917db3f-7cfa-48df-bdd5-e7e19867f71c.1",
        .targetInitiator = "iqn.2013-11.org.smartx:b4c555db-d2e2-40f7-8c3a-4e1915cb4ab9"
                           ".867632c6-75d2-444c-bf21-48d7af0586b5.1",
        .expectedRet = 0,
    };

    if (virTestRun("initiatorExtractConflictInitiators: Multiple conflict initiators",
                   testExtractConflictInitiators,
                   &conflictInitiatorData2) < 0) {
        ret = -1;
    }

    testExtractConflictInitiatorsdData conflictInitiatorData3 = {
        .existingInitiators = "iqn.2013-11.org.smartx:b4c555db-d2e2-40f7-8c3a-4e1915cb4ab9"
                              ".83f6f3a1-3e60-49e1-b14a-a1d486394a8b.1,"
                              "iqn.2013-11.org.smartx:be37f095-058a-442f-bfd7-2ca06a03426d"
                              ".83f6f3a1-3e60-49e1-b14a-a1d486394a8b.1,"
                              "iqn.2013-11.org.smartx:867632c6-75d2-444c-bf21-48d7af0586b5"
                              ".ee5db71c-3f81-43f0-8777-36469b460445.1,"
                              "iqn.2013-11.org.smartx:b917db3f-7cfa-48df-bdd5-e7e19867f71c"
                              ".b917db3f-7cfa-48df-bdd5-e7e19867f71c.1",
        .expectedConflictNumber = 0,
        .expectedConflictInitiators = NULL,
        .targetInitiator = "iqn.2013-11.org.smartx:b4649b16-eec1-4a75-9e5a-ed5bb8cab77b"
                           ".b917db3f-7cfa-48df-bdd5-e7e19867f71c.1",
        .expectedRet = 0,
    };

    if (virTestRun("initiatorExtractConflictInitiators: No conflict initiators",
                   testExtractConflictInitiators,
                   &conflictInitiatorData3) < 0) {
        ret = -1;
    }

    testExtractConflictInitiatorsdData conflictInitiatorData4 = {
        .existingInitiators = "iqn.2013-11.org.smartx:b4c555db-d2e2-40f7-8c3a-4e1915cb4ab9"
                              ".83f6f3a1-3e60-49e1-b14a-a1d486394a8b.1,"
                              "iqn.2013-11.org.smartx:b917db3f-7cfa-48df-bdd5-e7e19867f71c"
                              ".83f6f3a1-3e60-49e1-b14a-a1d486394a8b,"
                              "iqn.2013-11.org.smartx:867632c6-75d2-444c-bf21-48d7af0586b5"
                              ".867632c6-75d2-444c-bf21-48d7af0586b5.1,"
                              "iqn.2013-11.org.smartx:867632c6-75d2-444c-bf21-48d7af0586b5"
                              ".ee5db71c-3f81-43f0-8777-36469b460445.1",
        .expectedConflictNumber = 0,
        .expectedConflictInitiators = NULL,
        .targetInitiator = "iqn.2013-11.org.smartx:b917db3f-7cfa-48df-bdd5-e7e19867f71c"
                           ".b917db3f-7cfa-48df-bdd5-e7e19867f71c.1",
        .expectedRet = -1,
    };

    if (virTestRun("initiatorExtractConflictInitiators: Invalid conflict initiator name",
                   testExtractConflictInitiators,
                   &conflictInitiatorData4) < 0) {
        ret = -1;
    }

    testExtractConflictInitiatorsdData conflictInitiatorData5 = {
        .existingInitiators = "",
        .expectedConflictNumber = 0,
        .expectedConflictInitiators = NULL,
        .targetInitiator = "iqn.2013-11.org.smartx:b917db3f-7cfa-48df-bdd5-e7e19867f71c"
                           ".b917db3f-7cfa-48df-bdd5-e7e19867f71c.1",
        .expectedRet = 0,
    };

    if (virTestRun("initiatorExtractConflictInitiators: No existing initiators",
                   testExtractConflictInitiators,
                   &conflictInitiatorData5) < 0) {
        ret = -1;
    }

    testExtractConflictInitiatorsdData conflictInitiatorData6 = {
        .existingInitiators = "iqn.2013-11.org.smartx:b4c555db-d2e2-40f7-8c3a-4e1915cb4ab9"
                              ".83f6f3a1-3e60-49e1-b14a-a1d486394a8b.1",
        .expectedConflictNumber = 0,
        .expectedConflictInitiators = NULL,
        .targetInitiator = "iqn.2013-11.org.smartx:b4c555db-d2e2-40f7-8c3a-4e1915cb4ab9",
        .expectedRet = -1,
    };

    if (virTestRun("initiatorExtractConflictInitiators: Invalid target initiator name",
                   testExtractConflictInitiators,
                   &conflictInitiatorData6) < 0) {
        ret = -1;
    }

    testExtractConflictInitiatorsdData conflictInitiatorData7 = {
        .existingInitiators = "iqn.2013-11.org.smartx:b917db3f-7cfa-48df-bdd5-e7e19867f71c"
                              ".b917db3f-7cfa-48df-bdd5-e7e19867f71c.1,"
                              "iqn.2013-11.org.smartx:b917db3f-7cfa-48df-bdd5-e7e19867f71c"
                              ".83f6f3a1-3e60-49e1-b14a-a1d486394a8b.1,"
                              "iqn.2013-11.org.smartx:867632c6-75d2-444c-bf21-48d7af0586b5"
                              ".ee5db71c-3f81-43f0-8777-36469b460445.1",
        .expectedConflictNumber = 1,
        .expectedConflictInitiators = "iqn.2013-11.org.smartx:b917db3f-7cfa-48df-bdd5-e7e19867f71c"
                                      ".83f6f3a1-3e60-49e1-b14a-a1d486394a8b.1",
        .targetInitiator = "iqn.2013-11.org.smartx:b917db3f-7cfa-48df-bdd5-e7e19867f71c"
                           ".b917db3f-7cfa-48df-bdd5-e7e19867f71c.1",
        .expectedRet = 0,
    };

    if (virTestRun("initiatorExtractConflictInitiators: Same initiator name",
                   testExtractConflictInitiators,
                   &conflictInitiatorData7) < 0) {
        ret = -1;
    }

    return ret;
}

static int
doTestCheckMatchVmUuid(void)
{
    int ret = 0;

    testCheckVmUuidData checkUuidData1 = {
        .initiator = "iqn.2013-11.org.smartx:ee5db71c-3f81-43f0-8777-36469b460445.cd02f888-ffe1-"
                     "491f-805f-a64eb1e630e8.1",
        .vmUuid = "ee5db71c-3f81-43f0-8777-36469b460445",
        .expectedMatched = true,
        .expectedRet = 0,
    };

    if (virTestRun("initiatorCheckVmUuidMatched: Matched vm uuid",
                   testCheckVmUuidMatched,
                   &checkUuidData1) < 0) {
        ret = -1;
    }

    testCheckVmUuidData checkUuidData2 = {
        .initiator = "iqn.2013-11.org.smartx:ee5db71c-3f81-43f0-8777-36469b460445.cd02f888-ffe1-"
                     "491f-805f-a64eb1e630e8.1",
        .vmUuid = "19cf431d-0a95-4750-a67b-f46b36449418",
        .expectedMatched = false,
        .expectedRet = 0,
    };

    if (virTestRun("initiatorCheckVmUuidMatched: Unmatched vm uuid",
                   testCheckVmUuidMatched,
                   &checkUuidData2) < 0) {
        ret = -1;
    }

    testCheckVmUuidData checkUuidData3 = {
        .initiator = "iqn.2013-11.org.smartx:cd02f888-ffe1-491f-805f-a64eb1e630e8.1",
        .vmUuid = "19cf431d-0a95-4750-a67b-f46b36449418",
        .expectedMatched = false,
        .expectedRet = -1,
    };

    if (virTestRun("initiatorCheckVmUuidMatched: Invalid initiator",
                   testCheckVmUuidMatched,
                   &checkUuidData3) < 0) {
        ret = -1;
    }

    return ret;
}

int
main(int argc, char **argv)
{
    int ret = 0;

    if (doTestExtractVmUuidsFromInitiator() < 0) {
        ret = -1;
    }

    if (doTestExtractConflictInitiators() < 0) {
        ret = -1;
    }

    if (doTestCheckMatchVmUuid() < 0) {
        ret = -1;
    }

    return ret;
}
