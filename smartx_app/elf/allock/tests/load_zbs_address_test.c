/*
 * Copyright (C) 2022 SmartX, Inc.
 */

#include <config.h>

#include "testutils.h"

#include "mock.h"
#include "zbs_allow_list.c"

static int
testLoadExternalZbsAddresses(const void *data)
{
    char **addr = NULL;
    int addrsNum = 0;
    int ret = 0;

    ret = zbsLoadExternalServiceAddresses(&addr, &addrsNum);

    if (ret < 0 || addrsNum != 2) {
        return -1;
    }

    if (STRNEQ(addr[0], "************:10201:10206")) {
        return -1;
    }

    if (STRNEQ(addr[1], "************:10201:10206")) {
        return -1;
    }

    if (addr[2]) {
        return -1;
    }

    return 0;
}

static int
testLoadInternalZbsAddresses(const void *data)
{
    char **addr = NULL;
    int addrsNum = 0;
    int ret = 0;

    ret = zbsLoadInternalServiceAddresses(&addr, &addrsNum);

    if (ret < 0 || addrsNum != 1) {
        return -1;
    }

    if (STRNEQ(addr[0], "ZOOKEEPER_HOSTS")) {
        return -1;
    }

    if (addr[1]) {
        return -1;
    }

    return 0;
}

static int
mymain(void)
{
    if (virTestRun("LoadExternalZbsAddres: all params set1", testLoadExternalZbsAddresses, NULL) <
        0) {
        return -1;
    }

    if (virTestRun("LoadInternalZbsAddres: all params set1", testLoadInternalZbsAddresses, NULL) <
        0) {
        return -1;
    }

    return 0;
}

/* Replace the file related functions with mock functions defined in .so */
VIR_TEST_MAIN_PRELOAD(mymain, abs_builddir "/file_mock.so")
