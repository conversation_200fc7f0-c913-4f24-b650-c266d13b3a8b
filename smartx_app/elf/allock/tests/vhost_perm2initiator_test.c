/*
 * Copyright (C) 2022 SmartX, Inc.
 */
#include <config.h>

#include "testutils.h"

#include "utils.h"
#include "zbs_vhost.c"

typedef struct _testData testData;
struct _testData {
    zbs_vhost_io_permission_record testRecord;
    allockLun testLun;
    char *expectedInitiator;
};

static int
testExpectedPermissionGenInitiator(const void *data)
{
    testData *testData = (void *)data;
    char *genInitiator =
        vhostLunGenInitiatorFromPermission(&testData->testRecord, &testData->testLun);

    if (testData->expectedInitiator == NULL) {
        /* Only for wrong data case */
        if (genInitiator != NULL) {
            return -1;
        }
    } else {
        if (!STREQ(genInitiator, testData->expectedInitiator)) {
            VIR_TEST_VERBOSE("Failed to generate correct initator, expected=%s, actual=%s",
                             testData->expectedInitiator,
                             genInitiator);
            return -1;
        }
    }

    return 0;
}

int
main(int argc, char **argv)
{
    int ret = 0;
    char *nonSharedInitiator = "iqn.2013-11.org.smartx:vm_uuid1.host_uuid1.0";
    char *sharedInitiator = "iqn.2013-11.org.smartx:vm_uuid1.host_uuid1.1";

    testData correctDataOneOwner = {
        .testRecord = {.record_id_list = "vm_uuid1.host_uuid1", .single_access = true},
        .testLun =
            {
                .lunId = 1,
                .targetName = DEFAULT_TARGET,
                .assignedInitiator = nonSharedInitiator,
                .shared = false,
            },
        .expectedInitiator = nonSharedInitiator,
    };

    testData correctDataMultiOwner = {
        .testRecord = {.record_id_list = "vm_uuid1.host_uuid1,vm_uuid2.host_uuid2",
                       .single_access = true},
        .testLun =
            {
                .lunId = 1,
                .targetName = DEFAULT_TARGET,
                .assignedInitiator = nonSharedInitiator,
                .shared = false,
            },
        .expectedInitiator = "iqn.2013-11.org.smartx:vm_uuid1.host_uuid1.0,"
                             "iqn.2013-11.org.smartx:vm_uuid2.host_uuid2.0",
    };

    testData correctDataOneOwnerShared = {
        .testRecord = {.record_id_list = "vm_uuid1.host_uuid1", .single_access = true},
        .testLun =
            {
                .lunId = 1,
                .targetName = DEFAULT_TARGET,
                .assignedInitiator = sharedInitiator,
                .shared = true,
            },
        .expectedInitiator = sharedInitiator,
    };

    testData correctDataNoOwner = {
        .testRecord = {.record_id_list = ".", .single_access = true},
        .testLun =
            {
                .lunId = 1,
                .targetName = DEFAULT_TARGET,
                .assignedInitiator = "",
                .shared = false,
            },
        .expectedInitiator = "",
    };

    testData correctDataOpenOwnership = {
        .testRecord = {.record_id_list = "*.*", .single_access = false},
        .testLun =
            {
                .lunId = 1,
                .targetName = DEFAULT_TARGET,
                .assignedInitiator = nonSharedInitiator,
                .shared = false,
            },
        .expectedInitiator = "*/*"};

    /* host is open but vm is not */
    testData wrongDataInvalidPermission1 = {
        .testRecord = {.record_id_list = "vm1.*", .single_access = false},
        .testLun =
            {
                .lunId = 1,
                .targetName = DEFAULT_TARGET,
                .assignedInitiator = nonSharedInitiator,
                .shared = false,
            },
        .expectedInitiator = NULL};

    /* vm is open but host is not */
    testData wrongDataInvalidPermission2 = {
        .testRecord = {.record_id_list = "*.host1", .single_access = false},
        .testLun =
            {
                .lunId = 1,
                .targetName = DEFAULT_TARGET,
                .assignedInitiator = nonSharedInitiator,
                .shared = false,
            },
        .expectedInitiator = NULL};

    if (virTestRun("vhostLunGenInitiatorFromPermission1: Correct one-owner permission",
                   testExpectedPermissionGenInitiator,
                   &correctDataOneOwner) < 0) {
        ret = -1;
    }

    if (virTestRun("vhostLunGenInitiatorFromPermission2: Correct multi-owner permission",
                   testExpectedPermissionGenInitiator,
                   &correctDataMultiOwner) < 0) {
        ret = -1;
    }

    if (virTestRun("vhostLunGenInitiatorFromPermission3: Correct permission for shared lun",
                   testExpectedPermissionGenInitiator,
                   &correctDataOneOwnerShared) < 0) {
        ret = -1;
    }

    if (virTestRun("vhostLunGenInitiatorFromPermission4: No owner",
                   testExpectedPermissionGenInitiator,
                   &correctDataNoOwner) < 0) {
        ret = -1;
    }

    if (virTestRun("vhostLunGenInitiatorFromPermission5: Open Ownership",
                   testExpectedPermissionGenInitiator,
                   &correctDataOpenOwnership) < 0) {
        ret = -1;
    }

    if (virTestRun("vhostLunGenInitiatorFromPermission6: Invalid permission1",
                   testExpectedPermissionGenInitiator,
                   &wrongDataInvalidPermission1) < 0) {
        ret = -1;
    }

    if (virTestRun("vhostLunGenInitiatorFromPermission7: Invalid permission2",
                   testExpectedPermissionGenInitiator,
                   &wrongDataInvalidPermission2) < 0) {
        ret = -1;
    }

    return ret;
}
