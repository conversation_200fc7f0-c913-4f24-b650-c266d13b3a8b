/*
 * Copyright (C) 2022 SmartX, Inc.
 */
#include <config.h>

#include "testutils.h"

#include "utils.h"
#include "zbs_allow_list.h"
#include "zbs_vhost.c"

typedef struct _testData testData;
struct _testData {
    char *testInitiator;
    int expectedRet;
    char *expectedHostUuid;
    char *expectedVmUuid;
};

static int
testExtractUuidsFromInitiator(const void *data)
{
    testData *td = (void *)data;

    char *hostUuid = NULL;
    char *vmUuid = NULL;

    int ret = vhostLunExtractUuidsFromInitiator(td->testInitiator, &hostUuid, &vmUuid);

    if (td->expectedRet == -1) {
        if (ret != td->expectedRet) {
            VIR_TEST_VERBOSE("Unexpected return value from vhostLunExtractUuidsFromInitiator,\
                            expected=%d, actual=%d",
                             td->expectedRet,
                             ret);
            return -1;
        }

        if (hostUuid != NULL) {
            VIR_TEST_VERBOSE("Unexpected host uuid from vhostLunExtractUuidsFromInitiator,\
                             expected=NULL, actual=%s",
                             hostUuid);
            return -1;
        }

        if (vmUuid != NULL) {
            VIR_TEST_VERBOSE("Unexpected vm uuid from vhostLunExtractUuidsFromInitiator,\
                             expected=NULL, actual=%s",
                             vmUuid);
            return -1;
        }
    } else {
        if (!STREQ(hostUuid, td->expectedHostUuid)) {
            VIR_TEST_VERBOSE("Unexpected host uuid from vhostLunExtractUuidsFromInitiator,\
                             expected=%s, actual=%s",
                             hostUuid,
                             td->expectedHostUuid);
            return -1;
        }

        if (!STREQ(vmUuid, td->expectedVmUuid)) {
            VIR_TEST_VERBOSE("Unexpected vm uuid from vhostLunExtractUuidsFromInitiator,\
                             expected=%s, actual=%s",
                             vmUuid,
                             td->expectedVmUuid);
            return -1;
        }
    }

    return 0;
}

int
main(int argc, char **argv)
{
    int ret = 0;

    /*
        Correct initiator format is:
            iqn.2013-11.org.smartx:<vm_uuid>.<host_uuid>.<shared bit>
        Any input that violates this format will be given -1 return value.
    */
    testData correctDataNonShared = {
        .testInitiator = "iqn.2013-11.org.smartx:vm_uuid1.host_uuid1.0",
        .expectedRet = 0,
        .expectedHostUuid = "host_uuid1",
        .expectedVmUuid = "vm_uuid1",
    };

    testData correctDataShared = {
        .testInitiator = "iqn.2013-11.org.smartx:vm_uuid1.host_uuid1.1",
        .expectedRet = 0,
        .expectedHostUuid = "host_uuid1",
        .expectedVmUuid = "vm_uuid1",
    };

    testData wrongData1 = {
        .testInitiator = "iqn.2013-11.org.smartx:host_uuid1.0",
        .expectedRet = -1,
    };

    testData wrongData2 = {
        .testInitiator = "iqn.2013-11.org.smartx.host_uuid1.0",
        .expectedRet = -1,
    };

    testData wrongData3 = {
        .testInitiator = "iqn.2013-11.org.smartx:vm_uuid1.host_uuid1",
        .expectedRet = -1,
    };

    if (virTestRun("vhostLunExtractUuidsFromInitiator1: Correct initiator",
                   testExtractUuidsFromInitiator,
                   &correctDataNonShared) < 0) {
        ret = -1;
    }

    if (virTestRun("vhostLunExtractUuidsFromInitiator2: Correct Initiator (shared)",
                   testExtractUuidsFromInitiator,
                   &correctDataShared) < 0) {
        ret = -1;
    }

    if (virTestRun("vhostLunExtractUuidsFromInitiator3: Wrong format",
                   testExtractUuidsFromInitiator,
                   &wrongData1) < 0) {
        ret = -1;
    }

    if (virTestRun("vhostLunExtractUuidsFromInitiator4: Wrong format",
                   testExtractUuidsFromInitiator,
                   &wrongData2) < 0) {
        ret = -1;
    }

    if (virTestRun("vhostLunExtractUuidsFromInitiator5: Wrong format",
                   testExtractUuidsFromInitiator,
                   &wrongData3) < 0) {
        ret = -1;
    }

    return ret;
}
