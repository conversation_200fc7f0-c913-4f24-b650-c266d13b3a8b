/*
 * Copyright (C) 2022 SmartX, Inc.
 */

#include <config.h>

#include "viralloc.h"

#include "lock_driver_allock.c"
#include "utils.h"

/* === Non-Shared LUN Tests === */

static int
testAcquireSafeSuccess(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;

    if (allockTestCtxPresetLunStatus(testCtx, "", true) < 0) {
        return -1;
    }

    callRet =
        allockAcquire(testCtx->lock, NULL, VIR_LOCK_MANAGER_ACQUIRE_MIGRATION_RESUME, 0, NULL);

    if (callRet != 0) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, DEFAULT_ISCSI_INITIATOR, true)) {
        return -1;
    }

    return 0;
}

static int
testAcquireSafeNotReleased(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;
    char *srcInitiator = "iqn.2013-11.org.smartx:6802a6c3-1747-4080-89d5-bfe7caa17245."
                         "48043bff-f43d-45da-97e3-a3d1d3c4c177.0";

    if (allockTestCtxPresetLunStatus(testCtx, srcInitiator, true) < 0) {
        return -1;
    }

    callRet =
        allockAcquire(testCtx->lock, NULL, VIR_LOCK_MANAGER_ACQUIRE_MIGRATION_RESUME, 0, NULL);

    if (callRet != -1) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, srcInitiator, true)) {
        return -1;
    }

    return 0;
}

static int
testAcquireSafeNotSingleAccess(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;

    if (allockTestCtxPresetLunStatus(testCtx, "", false) < 0) {
        return -1;
    }

    callRet =
        allockAcquire(testCtx->lock, NULL, VIR_LOCK_MANAGER_ACQUIRE_MIGRATION_RESUME, 0, NULL);

    if (callRet != -1) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, "", false)) {
        return -1;
    }

    return 0;
}

/* === Shared LUN Tests === */

static int
testAcquireSafeOpenOwnership(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;

    if (allockTestCtxPresetLunStatus(testCtx, "*/*", false) < 0) {
        return -1;
    }

    callRet =
        allockAcquire(testCtx->lock, NULL, VIR_LOCK_MANAGER_ACQUIRE_MIGRATION_RESUME, 0, NULL);

    if (callRet != 0) {
        return -1;
    }

    if (!allockTestCtxCheckLunStatus(testCtx, "*/*", false)) {
        return -1;
    }

    return 0;
}

static int
testAcquireSafeSharedSuccess(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;
    char *initiator1 = "iqn.2013-11.org.smartx:6f30bfc2-ded5-45dc-8875-f7d38f8cb45e.other-host1.1";
    char *initiator2 = "iqn.2013-11.org.smartx:48043bff-f43d-45da-97e3-a3d1d3c4c177.other-host2.1";
    g_autofree char *presetOwners = allockTestJoinInitiators(initiator1, initiator2);
    g_autofree char *expectedOwners =
        allockTestJoinInitiators(presetOwners, DEFAULT_ISCSI_SHARED_INITIATOR);

    if (allockTestCtxPresetLunStatus(testCtx, presetOwners, false) < 0) {
        return -1;
    }

    callRet =
        allockAcquire(testCtx->lock, NULL, VIR_LOCK_MANAGER_ACQUIRE_MIGRATION_RESUME, 0, NULL);

    if (callRet != 0) {
        return -1;
    }

    /* For AcquireSafe, dest adds itself into the owners */
    if (!allockTestCtxCheckLunStatus(testCtx, expectedOwners, false)) {
        return -1;
    }

    return 0;
}

static int
testAcquireSafeSharedConflict(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;
    char *initiator1 = "iqn.2013-11.org.smartx:6802a6c3-1747-4080-89d5-bfe7caa17245.other-host1.1";
    char *initiator2 = "iqn.2013-11.org.smartx:48043bff-f43d-45da-97e3-a3d1d3c4c177.other-host2.1";
    g_autofree char *presetOwners = allockTestJoinInitiators(initiator1, initiator2);

    if (allockTestCtxPresetLunStatus(testCtx, presetOwners, false) < 0) {
        return -1;
    }

    callRet =
        allockAcquire(testCtx->lock, NULL, VIR_LOCK_MANAGER_ACQUIRE_MIGRATION_RESUME, 0, NULL);

    if (callRet != -1) {
        return -1;
    }

    /* For Acquire safe failure, nothing changes */
    if (!allockTestCtxCheckLunStatus(testCtx, presetOwners, false)) {
        return -1;
    }

    return 0;
}

static int
testAcquireSafeSharedOpenOwnership(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;

    if (allockTestCtxPresetLunStatus(testCtx, "*/*", false) < 0) {
        return -1;
    }

    callRet =
        allockAcquire(testCtx->lock, NULL, VIR_LOCK_MANAGER_ACQUIRE_MIGRATION_RESUME, 0, NULL);

    if (callRet != 0) {
        return -1;
    }

    /* For Acquire safe open ownership, nothing changes */
    if (!allockTestCtxCheckLunStatus(testCtx, "*/*", false)) {
        return -1;
    }

    return 0;
}

static int
testAcquireSafeSharedAlreadyOwner(const void *data)
{
    int callRet;
    allockTestCtxPtr testCtx = (void *)data;
    char *initiator1 = DEFAULT_ISCSI_SHARED_INITIATOR;
    char *initiator2 = "iqn.2013-11.org.smartx:48043bff-f43d-45da-97e3-a3d1d3c4c177.other-host2.1";
    g_autofree char *presetOwners = allockTestJoinInitiators(initiator1, initiator2);

    if (allockTestCtxPresetLunStatus(testCtx, presetOwners, false) < 0) {
        return -1;
    }

    callRet =
        allockAcquire(testCtx->lock, NULL, VIR_LOCK_MANAGER_ACQUIRE_MIGRATION_RESUME, 0, NULL);

    if (callRet != 0) {
        return -1;
    }

    /* For Acquire safe already owner, nothing changes */
    if (!allockTestCtxCheckLunStatus(testCtx, presetOwners, false)) {
        return -1;
    }

    return 0;
}

int
testRun(allockTestCtxPtr testCtx)
{
    allockTestCtxSetAllockLun(testCtx, false, DEFAULT_ISCSI_INITIATOR);

    if (virTestRun("allockLunAcquireSafe1: Success: migration resume",
                   testAcquireSafeSuccess,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunAcquireSafe2: Failure: migration resume & not released",
                   testAcquireSafeNotReleased,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunAcquireSafe3: Success: migration resume & open ownership",
                   testAcquireSafeOpenOwnership,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunAcquireSafe4: Failure: migration resume & not single access",
                   testAcquireSafeNotSingleAccess,
                   testCtx) < 0) {
        return -1;
    }

    allockTestCtxSetAllockLun(testCtx, true, DEFAULT_ISCSI_SHARED_INITIATOR);

    if (virTestRun("allockLunAcquireSafeShared1: Success, shared & migration resume",
                   testAcquireSafeSharedSuccess,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun("allockLunAcquireSafeShared2: Failure, shared & migration resume & conflict",
                   testAcquireSafeSharedConflict,
                   testCtx) < 0) {
        return -1;
    }

    if (virTestRun(
            "allockLunAcquireSafeShared3: Success, shared & migration resume & open ownership",
            testAcquireSafeSharedOpenOwnership,
            testCtx) < 0) {
        return -1;
    }

    if (virTestRun(
            "allockLunAcquireSafeShared4: Success, shared & migration resume & already owner",
            testAcquireSafeSharedAlreadyOwner,
            testCtx) < 0) {
        return -1;
    }

    return 0;
}

ALLOCK_API_TEST(testRun);
