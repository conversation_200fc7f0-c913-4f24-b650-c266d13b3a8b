ARCH ?= $(shell uname -m)
# different Linux(MacOS) distro use different arch name, so we unify them using the same name aarch64
# eg. on MacOS with Apple silicon arch name is arm64, we use aarch64 as the arch name
ifeq ($(ARCH),arm64)
ARCH := aarch64
endif

ifeq ($(TARGET_DISTRO),openeuler)
BUILD_DISTRO := oe1
else ifeq ($(TARGET_DISTRO),tencent)
BUILD_DISTRO := tl3
else
BUILD_DISTRO := el7
endif

# aarch64 + el7 is not supported
ifeq ($(ARCH),aarch64)
BUILD_DISTRO := oe1
endif

ABS_SRCDIR = $(shell pwd)
LIBVIRT_DIR ?= $(ABS_SRCDIR)
ZBS_VERSION ?=
BUILD_IMAGE = allock-builder-$(BUILD_DISTRO):1.0.0
CUR_TIME := $(shell date "+%G%m%d_%H%M%S")


LIBVIRT_SRC_NAME = libvirt-6.2.0
LIBVIRT_SRC_TAR_LINK = http://192.168.17.20/elf-src-tar/libvirt-6.2.0.tar.xz
LIBVIRT_SRC_PATH = $(LIBVIRT_DIR)/$(LIBVIRT_SRC_NAME)
LIBVIRT_BUILD_PATH = $(LIBVIRT_DIR)/$(LIBVIRT_SRC_NAME)/build

# Compile parameters
LIB_CFLAGS = $(shell pkg-config --cflags libzbs) \
			 $(shell pkg-config --cflags glib-2.0) \
			 $(shell pkg-config --cflags libxml-2.0)
LIBADD = $(shell pkg-config --libs libzbs) \
		 $(shell pkg-config --libs glib-2.0) \
		 $(shell pkg-config --libs libxml-2.0)

INCLUDES =	-I$(LIBVIRT_SRC_PATH) \
			-I$(LIBVIRT_BUILD_PATH)\
			-I$(LIBVIRT_BUILD_PATH)/include \
			-I$(LIBVIRT_SRC_PATH)/include \
			-I$(LIBVIRT_SRC_PATH)/src/util \
			-I$(LIBVIRT_SRC_PATH)/src/locking \
			-I$(LIBVIRT_SRC_PATH)/src \
			-I$(LIBVIRT_SRC_PATH)/src/conf \
			-I$(LIBVIRT_SRC_PATH)/tests \
			-I$(ABS_SRCDIR)/src
CC = gcc -std=gnu99
DEFS = -DHAVE_CONFIG_H
# gnulib uses comparison between signed and unsigned so we add no-sign-compare
WARN_CFLAGS = -W -Werror -Wno-sign-compare -Wunused-but-set-variable
CFLAGS = -g -shared -fPIC

ALLOCK_CFLAGS =	$(INCLUDES) \
				$(LIB_CFLAGS) \
				$(LIBADD) \
				$(CFLAGS) \
				$(STRICT_FRAME_LIMIT_CFLAGS) \
				$(WARN_CFLAGS)


# === Options for tests ===
TEST_FLAGS = -Dabs_builddir="\"$(ABS_SRCDIR)\"" -Dabs_srcdir="\"$(ABS_SRCDIR)\""
TEST_INCLUDES = -I$(LIBVIRT_SRC_PATH)/tests -I$(ABS_SRCDIR)/src -I$(ABS_SRCDIR)/tests/mock

# Since tests will link libvirt.la and libgnu.la generated by libvirt's compilation,
# we use libtool to link the objects.
TEST_CC = libtool --mode=link gcc -std=gnu99

TEST_DEP_OBJ = $(LIBVIRT_BUILD_PATH)/src/libvirt.la \
			   $(LIBVIRT_BUILD_PATH)/tests/testutils.o \
			   $(ABS_SRCDIR)/libzbs_mock.o \
			   $(ABS_SRCDIR)/utils.o \
			   $(ABS_SRCDIR)/zbs_allow_list.o \
			   $(ABS_SRCDIR)/zbs_iscsi.o \
			   $(ABS_SRCDIR)/zbs_vhost.o

TESTS_SRC = $(wildcard tests/*_test.c)
# E.g. tests/prepare_start_test which is an executable
TESTS = $(TESTS_SRC:.c=)


# Compiling allock driver will need libvirt's config.h, which is
# generated via 'configure'
libvirt-src:
	@echo "libvirt src directory:" $(LIBVIRT_SRC_PATH)
	mkdir -p $(LIBVIRT_DIR)
	curl -L -o $(LIBVIRT_DIR)/$(LIBVIRT_SRC_NAME).tar.xz $(LIBVIRT_SRC_TAR_LINK)
	cd $(LIBVIRT_DIR); tar -xf $(LIBVIRT_SRC_NAME).tar.xz
	cd $(LIBVIRT_SRC_PATH) \
		&& autoreconf -if \
		&& rm -f po/stamp-po \
		&& mkdir build \
		&& cd build \
		&& ../configure

# Set LIBVIRT_DIR to /tmp when running in container
docker-build: build-image
	docker run --privileged --network host --rm \
	-v $(ABS_SRCDIR):/root/allock -w /root/allock \
	$(BUILD_IMAGE) make build LIBVIRT_DIR='/tmp'

build-image:
ifeq ($(ZBS_VERSION),)
	@echo "Please specify zbs version to compile allock"
	exit 1
endif
	@echo "Use libzbs $(ZBS_VERSION) to compile"
	docker build --build-arg ZBS_VERSION="$(ZBS_VERSION)" \
		--build-arg DISTRO=$(BUILD_DISTRO) \
		--build-arg ARCH=$(ARCH) \
		--build-arg TIMESTAMP=$(CUR_TIME) \
		--ulimit nofile=1048576:1048576 \
		-t $(BUILD_IMAGE) .

build: libvirt-src
	$(CC) $(DEFS) $(ALLOCK_CFLAGS) -o allock.so src/lock_driver_allock.c src/zbs_allow_list.c src/zbs_iscsi.c src/zbs_vhost.c

# === Unittest ====
docker-unittests: build-image
	docker run --privileged --network host --rm \
	-v $(ABS_SRCDIR):/root/allock -w /root/allock \
	$(BUILD_IMAGE) make unittests LIBVIRT_DIR='/tmp'

unittests: tests-build $(TESTS) tests-run

# Tests will depend on some compiled objects TEST_DEP_OBJ to run.
tests-build: libvirt-src
	cd $(LIBVIRT_BUILD_PATH); make -j
	cd $(LIBVIRT_BUILD_PATH)/tests; make -j testutils.o
# Libzbs mock object
	$(CC) $(DEFS) $(ALLOCK_CFLAGS) -g -o libzbs_mock.o tests/mock/zbs/libzbs.c
	$(CC) $(DEFS) $(ALLOCK_CFLAGS) -g -o file_mock.so tests/mock/file.c tests/mock/config.c
# Common library for allock unittests
	$(CC) $(DEFS) $(TEST_FLAGS) $(ALLOCK_CFLAGS) $(TEST_INCLUDES) -g -o utils.o tests/utils.c
	$(CC) $(DEFS) $(TEST_FLAGS) $(ALLOCK_CFLAGS) $(TEST_INCLUDES) -g -o zbs_allow_list.o src/zbs_allow_list.c
	$(CC) $(DEFS) $(TEST_FLAGS) $(ALLOCK_CFLAGS) $(TEST_INCLUDES) -g -o zbs_iscsi.o src/zbs_iscsi.c
	$(CC) $(DEFS) $(TEST_FLAGS) $(ALLOCK_CFLAGS) $(TEST_INCLUDES) -g -o zbs_vhost.o src/zbs_vhost.c

$(TESTS):
	$(TEST_CC) $(DEFS) $(TEST_FLAGS) $(TEST_INCLUDES) $(ALLOCK_CFLAGS) -g -o $@ $@.c $(TEST_DEP_OBJ)

tests-run:
	for test in $(TESTS); do \
		VIR_TEST_VERBOSE=1 $$test || exit 1;\
	done

clean:
	rm -f *.so *.o
	rm -f *.tar.xz
	rm -rf $(LIBVIRT_SRC_NAME)
	rm -f $(TESTS)
	rm -rf tests/.libs
