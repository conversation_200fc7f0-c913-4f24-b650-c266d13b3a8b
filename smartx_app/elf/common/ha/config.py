import logging

from common.election import election_info
from common.lib import cfg
from common.lib.zk import Zookeeper
from common.mongo.db import mongodb
from smartx_app.elf.common import constants as elf_common_constants
from smartx_app.elf.common.config import platform
from smartx_app.elf.common.utils import election


class Config:
    def __init__(
        self,
        hb_lost_threshold,
        hb_write_fail_threshold,
        network_probe_fail_threshold,
        vm_os_max_heartbeat_lost_time_sec,
        vm_os_recover_limit_max_attempts,
        vm_os_recover_limit_time_windows_hour,
        version,
    ):
        self.hb_lost_threshold = hb_lost_threshold
        self.hb_write_fail_threshold = hb_write_fail_threshold
        self.network_probe_fail_threshold = network_probe_fail_threshold
        self.vm_os_max_heartbeat_lost_time_sec = vm_os_max_heartbeat_lost_time_sec
        self.vm_os_recover_limit_max_attempts = vm_os_recover_limit_max_attempts
        self.vm_os_recover_limit_time_windows_hour = vm_os_recover_limit_time_windows_hour
        self.version = version

    @staticmethod
    def validate_dict(obj):
        if obj.get(elf_common_constants.HB_LOST_THRESHOLD_KEY) is None:
            raise RuntimeError("{} not exists".format(elf_common_constants.HB_LOST_THRESHOLD_KEY))
        if obj.get(elf_common_constants.HB_WRITE_FAIL_THRESHOLD_KEY) is None:
            raise RuntimeError("{} not exists".format(elf_common_constants.HB_WRITE_FAIL_THRESHOLD_KEY))
        if obj.get(elf_common_constants.NETWORK_PROBE_FAIL_THRESHOLD_KEY) is None:
            raise RuntimeError("{} not exists".format(elf_common_constants.NETWORK_PROBE_FAIL_THRESHOLD_KEY))
        if obj.get(elf_common_constants.VM_OS_MAX_HEARTBEAT_LOST_TIME_SEC_KEY) is None:
            raise RuntimeError("{} not exists".format(elf_common_constants.VM_OS_MAX_HEARTBEAT_LOST_TIME_SEC_KEY))
        if obj.get(elf_common_constants.VM_OS_RECOVER_LIMIT_MAX_ATTEMPTS_KEY) is None:
            raise RuntimeError("{} not exists".format(elf_common_constants.VM_OS_RECOVER_LIMIT_MAX_ATTEMPTS_KEY))
        if obj.get(elf_common_constants.VM_OS_RECOVER_LIMIT_TIME_WINDOWS_HOUR_KEY) is None:
            raise RuntimeError("{} not exists".format(elf_common_constants.VM_OS_RECOVER_LIMIT_TIME_WINDOWS_HOUR_KEY))
        if obj.get(elf_common_constants.VERSION_KEY) is None:
            raise RuntimeError("{} not exists".format(elf_common_constants.VERSION_KEY))

    @staticmethod
    def default(version=0):
        return Config.from_sensitivity_level(elf_common_constants.HA_SENSITIVITY_NORMAL, version)

    @staticmethod
    def from_dict(obj):
        Config.validate_dict(obj)
        return Config(
            hb_lost_threshold=obj[elf_common_constants.HB_LOST_THRESHOLD_KEY],
            hb_write_fail_threshold=obj[elf_common_constants.HB_WRITE_FAIL_THRESHOLD_KEY],
            network_probe_fail_threshold=obj[elf_common_constants.NETWORK_PROBE_FAIL_THRESHOLD_KEY],
            vm_os_max_heartbeat_lost_time_sec=obj[elf_common_constants.VM_OS_MAX_HEARTBEAT_LOST_TIME_SEC_KEY],
            vm_os_recover_limit_max_attempts=obj[elf_common_constants.VM_OS_RECOVER_LIMIT_MAX_ATTEMPTS_KEY],
            vm_os_recover_limit_time_windows_hour=obj[elf_common_constants.VM_OS_RECOVER_LIMIT_TIME_WINDOWS_HOUR_KEY],
            version=obj[elf_common_constants.VERSION_KEY],
        )

    @staticmethod
    def from_sensitivity_level(level, version=0):
        if level not in elf_common_constants.HA_SENSITIVITY_LEVELS:
            raise ValueError("Invalid sensitivity value '{}'".format(level))

        return Config(
            hb_lost_threshold=elf_common_constants.HA_CONFIG_KEY_SENSITIVITY_MAP[
                elf_common_constants.HB_LOST_THRESHOLD_KEY
            ][level],
            hb_write_fail_threshold=elf_common_constants.HA_CONFIG_KEY_SENSITIVITY_MAP[
                elf_common_constants.HB_WRITE_FAIL_THRESHOLD_KEY
            ][level],
            network_probe_fail_threshold=elf_common_constants.HA_CONFIG_KEY_SENSITIVITY_MAP[
                elf_common_constants.NETWORK_PROBE_FAIL_THRESHOLD_KEY
            ][level],
            vm_os_max_heartbeat_lost_time_sec=elf_common_constants.HA_CONFIG_KEY_SENSITIVITY_MAP[
                elf_common_constants.VM_OS_MAX_HEARTBEAT_LOST_TIME_SEC_KEY
            ][level],
            vm_os_recover_limit_max_attempts=elf_common_constants.HA_CONFIG_KEY_SENSITIVITY_MAP[
                elf_common_constants.VM_OS_RECOVER_LIMIT_MAX_ATTEMPTS_KEY
            ][level],
            vm_os_recover_limit_time_windows_hour=elf_common_constants.HA_CONFIG_KEY_SENSITIVITY_MAP[
                elf_common_constants.VM_OS_RECOVER_LIMIT_TIME_WINDOWS_HOUR_KEY
            ][level],
            version=version,
        )

    @staticmethod
    def _find_sensitivity_level(config_key, config_value):
        level_map = elf_common_constants.HA_CONFIG_KEY_SENSITIVITY_MAP[config_key]
        for level, value in level_map.items():
            if value == config_value:
                return level
        return None

    def to_sensitivity_level(self):
        level = self._find_sensitivity_level(elf_common_constants.HB_LOST_THRESHOLD_KEY, self.hb_lost_threshold)
        if level is None:
            raise ValueError("invalid sensitivity level {}".format(level))
        return level

    def to_dict(self):
        return {
            elf_common_constants.HB_LOST_THRESHOLD_KEY: self.hb_lost_threshold,
            elf_common_constants.HB_WRITE_FAIL_THRESHOLD_KEY: self.hb_write_fail_threshold,
            elf_common_constants.NETWORK_PROBE_FAIL_THRESHOLD_KEY: self.network_probe_fail_threshold,
            elf_common_constants.VM_OS_MAX_HEARTBEAT_LOST_TIME_SEC_KEY: self.vm_os_max_heartbeat_lost_time_sec,
            elf_common_constants.VM_OS_RECOVER_LIMIT_MAX_ATTEMPTS_KEY: self.vm_os_recover_limit_max_attempts,
            elf_common_constants.VM_OS_RECOVER_LIMIT_TIME_WINDOWS_HOUR_KEY: self.vm_os_recover_limit_time_windows_hour,
            elf_common_constants.VERSION_KEY: self.version,
        }


# ==========  HA configuration related DB operations =====
class HAConfigDAO:
    def __init__(self, db_proxy) -> None:
        self._db_proxy = db_proxy
        self._collection = self._db_proxy.resources.elf_cluster

    def get_host_ha_config(self, host_uuid):
        filter_obj = {"hosts.host_uuid": host_uuid}
        projection = {"hosts.$": 1}
        result = self._collection.find_one(filter_obj, projection)

        if result and "hosts" in result and len(result["hosts"]) > 0:
            host_ha_config = result["hosts"][0].get("ha_config")
            return host_ha_config
        else:
            return None

    def get_all_host_ha_config(self):
        projection = {"hosts": 1}
        result = self._collection.find_one({}, projection)

        if result:
            return {h["host_uuid"]: h.get("ha_config") for h in result.get("hosts", [])}
        else:
            return {}

    def get_cluster_ha_config(self):
        filter_obj = {}
        projection = {"ha_config": 1}
        cluster_ha_config = self._collection.find_one(filter_obj, projection)

        if cluster_ha_config:
            return cluster_ha_config.get("ha_config")
        else:
            return None

    def update_host_ha_config(self, host_uuid, new_ha_config, insert_mode=False):
        if insert_mode:
            filter_obj = {"hosts": {"$elemMatch": {"host_uuid": host_uuid, "ha_config": {"$exists": False}}}}
        else:
            filter_obj = {"hosts.host_uuid": host_uuid}

        updates = {"$set": {"hosts.$.ha_config": new_ha_config}}

        update_result = self._collection.update_one(filter_obj, updates)

        return update_result

    def update_cluster_ha_config(self, new_ha_config, insert_mode=False):
        if insert_mode:
            filter_obj = {"ha_config": {"$exists": False}, "cluster_uuid": {"$exists": True}}
        else:
            expected_origin_version = new_ha_config[elf_common_constants.VERSION_KEY] - 1
            filter_obj = {"ha_config.version": expected_origin_version}

        updates = {"$set": {"ha_config": new_ha_config}}

        update_result = self._collection.update_one(filter_obj, updates)

        return update_result

    def remove_host(self, host_uuid):
        cluster_uuid = cfg.DeployConfigManager().get_cluster_uuid()

        result = self._collection.update_one(
            {
                "cluster_uuid": cluster_uuid,
                "hosts.host_uuid": host_uuid,
            },
            {"$pull": {"hosts": {"host_uuid": host_uuid}}},
        )

        return result

    def unset_host_ha_config(self, host_uuid):
        filter_obj = {"hosts": {"$elemMatch": {"host_uuid": host_uuid, "ha_config": {"$type": 10}}}}
        updates = {"$unset": {"hosts.$.ha_config": ""}}

        update_result = self._collection.update_one(filter_obj, updates)

        return update_result


def get_current_config_status(db_proxy):
    ops = HAConfigDAO(db_proxy)
    cluster_config = ops.get_cluster_ha_config()
    hosts_config = ops.get_all_host_ha_config()
    num_total_hosts = len(hosts_config)
    num_consistent_hosts = 0

    for host_uuid in hosts_config:
        if hosts_config[host_uuid]["version"] == cluster_config["version"]:
            num_consistent_hosts = num_consistent_hosts + 1

    return {
        "status": "consistent" if num_consistent_hosts == num_total_hosts else "inconsistent",
        "progress": {
            "total": num_total_hosts,
            "finished": num_consistent_hosts,
        },
        "config": cluster_config,
    }


def get_elf_vm_monitor_leader_ip():
    if platform.is_in_elf():
        zookeeper = Zookeeper()
        res = election_info.get_all_service_info(zookeeper.conn, services=["elf-vm-monitor"])
        if len(res) == 1:
            return res[0].members[0].node_id
    else:
        return election.ZBSMetaMembership().leader_ip()


def remove_node_from_elf_cluster(host_uuid):
    result = HAConfigDAO(mongodb).remove_host(host_uuid)
    if result.modified_count == 1:
        logging.info("Successfully deleted the node's elf_cluster information.")
    else:
        logging.info("Host uuid {} does not exist in elf_cluster.".format(host_uuid))
