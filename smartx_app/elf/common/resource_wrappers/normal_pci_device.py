# Copyright (c) 2022 SMARTX, Inc.
# All rights reserved.
import functools

from smartx_app.elf.common import constants as elf_constants
from smartx_app.elf.common.resource_wrappers import pci_device
from smartx_app.elf.common.utils import vm_start
from smartx_proto.errors import pyerror_pb2 as py_error


class NormalPCIDevice(pci_device.PCIDevice):
    _subtype = elf_constants.PASS_THROUGH_PCI

    def __init__(self, uuid, guest_pci_address):
        super().__init__(uuid, guest_pci_address)


class NormalPCIDeviceHandler(pci_device.PCIDeviceHandler):
    _subtype = elf_constants.PASS_THROUGH_PCI

    _dev_assign_failed_ec = py_error.PCI_ASSIGN_FAILED
    _dev_release_failed_ec = py_error.PCI_RELEASE_FAILED

    def __init__(self, vm_json):
        super().__init__(vm_json)

    def _initialize_devices(self):
        return [
            NormalPCIDevice(dev["uuid"], dev.get("pci_address", None)) for dev in list(self._matched_host_devs.values())
        ]

    def _release_device(self, host_uuid, device_id):
        return self._tuna_client.release_pci(self.vm_uuid, host_uuid, device_id)

    def _allocate_device(self, device_id):
        self._tuna_client.allocate_pci(self.vm_uuid, self.host_uuid, device_id)
        return self._tuna_client.get_pci_info(self.host_uuid, device_id)

    def _get_device_assign_infos(self, host_uuid):
        return self._tuna_client.get_pci_assign_infos(host_uuid)


def release_device_on_start_failure(func):
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        try:
            return func(self, vm_json, *args, **kwargs)
        except Exception:
            NormalPCIDeviceHandler(vm_json).release_device_silent()
            raise

    return wrapper


def allocate_device_to_start(func):
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        normal_pci_device_handler = NormalPCIDeviceHandler(vm_json)
        if normal_pci_device_handler.vm_has_matched_device():
            if vm_json["status"] == elf_constants.VM_RUNNING:
                kwargs["normal_pci_devices"] = normal_pci_device_handler.allocate_device()
            else:
                kwargs["normal_pci_devices"] = normal_pci_device_handler.allocate_mock_pci_address()

        try:
            return func(self, vm_json, *args, **kwargs)
        except Exception:
            if vm_start.need_release_device_on_exception_after_start(vm_json, kwargs["libvirt_conn"]):
                normal_pci_device_handler.release_device_silent()
            raise

    return wrapper
