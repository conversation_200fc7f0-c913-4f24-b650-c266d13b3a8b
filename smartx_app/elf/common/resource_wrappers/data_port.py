# Copyright (c) 2013-2022, SMARTX
# All rights reserved.
import logging
import uuid

from common.config import resources
from smartx_app.elf.common import code
from smartx_app.elf.common import constants as elf_constants
from smartx_app.elf.common.config import platform
from smartx_app.elf.common.events import dataport_event_wrapper as dp_event
from smartx_app.elf.common.resource_query import (
    vm_querier,
    vm_template_querier,
    volume_querier,
    volume_template_querier,
)
from smartx_app.elf.common.resource_wrappers import iscsi_volume_manager as volume_manager
from smartx_app.elf.common.resource_wrappers import vm_template_wrapper as tpl_wrapper
from smartx_app.elf.common.resource_wrappers import vm_wrapper
from smartx_app.elf.common.resource_wrappers import volume_wrapper as vol_wrapper
from smartx_app.elf.common.resources import base, data_port, storage_cluster
from smartx_app.elf.common.utils import disk, elf_fs, iscsi, volume, zbs_iscsi
from smartx_app.elf.common.utils import format_factory as ff
from smartx_app.elf.common.utils import storage_cluster as sc_utils
from smartx_app.elf.common.utils import validator as vd
from smartx_app.elf.common.utils import virtualization as virt
from smartx_app.elf.job_center import constants
from smartx_proto.errors import pyerror_pb2 as py_error


class DataPortErrorNotFound(Exception):
    pass


# DataPort is design to be the transformer between ELF resources and external
# objects. Every DataPort object represent a transformation task from one type
# to another. User could use it to import a ovf package to an elf vm, or export
# disks to qcow2 files.
#
# This object also manages all intermediates, including temp volumes,
# and outcomes, etc ovf packages, disk files, elf vm. When DataPort is deleted,
# all of them will be removed too.
class DataPortWrapper:
    """DataPort Resource Wrapper

    There are some public methods:
    * (static) from_create_request
    * (static) from_uuid
    * (static) from_dict
    * to_user_view
    * save
    * submit_convert
    * submit_delete
    * remove_temporary_resources
    * remove_user_resources
    * remove_conversion_results
    * delete_self
    * mark_as_failed
    * mark_as_succeeded
    * mark_as_canceled
    * try_mark_as_canceling
    * try_mark_as_in_progress
    * resource

    See DataPort for data model
    """

    DEFAULT_RECYCLE_TIME = 80 * 60 * 60
    VALID_CONVERSION = {
        data_port.DataPort.RES_OVF: (
            data_port.DataPort.RES_VM,
            data_port.DataPort.RES_VM_TEMPLATE,
        ),
        data_port.DataPort.RES_VM: (data_port.DataPort.RES_OVF, data_port.DataPort.RES_DISK),
        data_port.DataPort.RES_VM_TEMPLATE: (
            data_port.DataPort.RES_OVF,
            data_port.DataPort.RES_DISK,
        ),
        data_port.DataPort.RES_DISK: (data_port.DataPort.RES_ELF_VOLUME),
        data_port.DataPort.RES_ELF_VOLUME: (data_port.DataPort.RES_DISK),
    }

    @staticmethod
    def from_create_request(request):
        src_type = request["src_type"]
        dst_type = request["dst_type"]
        reclaim_time = DataPortWrapper.DEFAULT_RECYCLE_TIME
        elf_fs_client = elf_fs.ElfFsClient()

        if not DataPortWrapper._validate_conversion(src_type, dst_type):
            raise base.ResourceException(
                "invalid data port conversion from {} to {}".format(src_type, dst_type),
                py_error.DATAPORT_INVALID_CONVERSION,
            )

        try:
            task_arguments = DataPortWrapper._build_tasks_arguments_from_request(request, elf_fs_client)
            related_resources = DataPortWrapper._generate_related_resources_from_request(request)

            # build resource
            res = data_port.DataPort.new(
                uuid=str(uuid.uuid4()),
                src_type=src_type,
                dst_type=dst_type,
                task_arguments=task_arguments,
                reclaim_time=reclaim_time,
                related_resources=related_resources,
            )

            return DataPortWrapper(res)
        except Exception:
            elf_fs_client.reclaim_created_file()
            raise

    @staticmethod
    def from_uuid(data_port_uuid):
        res = data_port.DataPort.from_uuid(data_port_uuid)
        if res is None:
            raise DataPortErrorNotFound()

        return DataPortWrapper(res)

    @staticmethod
    def from_dict(doc):
        res = data_port.DataPort.from_dict(doc)
        return DataPortWrapper(res)

    def __init__(self, resource):
        self._resource = resource

    @staticmethod
    def _validate_conversion(src_type, dst_type):
        if src_type not in DataPortWrapper.VALID_CONVERSION:
            return False

        if dst_type not in DataPortWrapper.VALID_CONVERSION[src_type]:
            return False

        return True

    @staticmethod
    def _validate_disks_storage_cluster_consistency(dst_disks):
        """
        Check all disks are under the same storage cluster.
        Raise exception if storage_cluster_uuid of disks are not the same in disaggregated mode.
        """
        if not platform.is_in_elf():
            return

        unique_cluster_uuids = set()
        client = elf_fs.ElfFsClient()
        prev_disk = None

        for curr_disk in dst_disks:
            if curr_disk["type"] == constants.DISK:
                if curr_disk.get("path"):
                    vols = vol_wrapper.VolumeWrapper.batch_query_by_path([curr_disk["path"]])
                    if len(vols) > 0:
                        unique_cluster_uuids.add(vols[0]["storage_cluster_uuid"])
                    else:
                        raise base.ResourceException(
                            f"The volume does not exist, path: {curr_disk['path']}",
                            py_error.DATAPORT_INVALID_CONVERSION,
                        )
                else:
                    # dst_disk does not contain 'path' indicating that it is the volume to be created
                    unique_cluster_uuids.add(curr_disk["storage_cluster_uuid"])

            elif curr_disk["type"] == constants.IMPORTED_DISK:
                file_meta = client.get_file_meta(elf_fs.get_file_id_from_url(curr_disk["url"]))
                unique_cluster_uuids.add(file_meta.get("storage_cluster_uuid"))

            if len(unique_cluster_uuids) > 1:
                raise base.ResourceException(
                    f"Disk({curr_disk}) and Disk({prev_disk}) are in different storage clusters"
                    f"({unique_cluster_uuids})",
                    py_error.DATAPORT_INVALID_CONVERSION,
                )

            prev_disk = curr_disk

    @staticmethod
    def _build_tasks_arguments_from_request(request, elf_fs_client):
        tasks = {}

        # import vm or template from disk or ovf
        if request["src_type"] in (data_port.DataPort.RES_DISK, data_port.DataPort.RES_OVF) and request["dst_type"] in (
            data_port.DataPort.RES_VM,
            data_port.DataPort.RES_VM_TEMPLATE,
        ):
            DataPortWrapper._validate_disks_storage_cluster_consistency(request["dst_attr"]["disks"])

            task = DataPortWrapper._generate_convert_disk_task_from_vm(
                request["src_attr"]["disks"], request["dst_attr"]
            )
            tasks.update(task)

        # import volume
        if (
            request["src_type"] == data_port.DataPort.RES_DISK
            and request["dst_type"] == data_port.DataPort.RES_ELF_VOLUME
        ):
            task = DataPortWrapper._generate_convert_disk_task_from_volumes(
                request["src_attr"]["disks"], request["dst_attr"]["volumes"]
            )
            tasks.update(task)

        # export vm or vm template to disk or ovf
        if request["src_type"] in (
            data_port.DataPort.RES_VM,
            data_port.DataPort.RES_VM_TEMPLATE,
        ) and request["dst_type"] in (data_port.DataPort.RES_DISK, data_port.DataPort.RES_OVF):
            # find that vm or vm_template
            vm_json = None
            if request["src_type"] == data_port.DataPort.RES_VM:
                vm_json = vm_querier.VMQuerier().get(request["src_attr"]["uuid"])
            else:
                vm_json = vm_template_querier.VMTemplateQuerier().get(request["src_attr"]["uuid"])

            if vm_json is None:
                raise base.ResourceException(
                    "no such uuid {}".format(request["src_attr"]["uuid"]),
                    (
                        py_error.DATAPORT_SRC_VM_NOT_FOUND
                        if request["src_type"] == data_port.DataPort.RES_VM
                        else py_error.DATAPORT_SRC_VM_TPL_NOT_FOUND
                    ),
                )

            dst_disk_type = data_port.DataPort.FILE_TYPE_VMDK
            if request["dst_type"] == data_port.DataPort.RES_DISK:
                dst_disk_type = request["dst_attr"]["dst_disk_type"]

            task = None
            if request["src_type"] == data_port.DataPort.RES_VM:
                task = DataPortWrapper._generate_export_volumes_task_from_vm(vm_json, dst_disk_type, elf_fs_client)
            else:
                task = DataPortWrapper._generate_export_volumes_task_from_vm_template(
                    vm_json, dst_disk_type, elf_fs_client
                )
            tasks.update(task)

            # generate build ovf subtask if necessary
            if request["dst_type"] == data_port.DataPort.RES_OVF:
                task = DataPortWrapper._generate_build_ovf_task(
                    request["src_attr"]["uuid"], with_mac_address=request["dst_attr"].get("keep_mac", False)
                )
                tasks.update(task)

        # export volume
        if (
            request["src_type"] == data_port.DataPort.RES_ELF_VOLUME
            and request["dst_type"] == data_port.DataPort.RES_DISK
        ):
            task = DataPortWrapper._generate_export_volumes_task_from_volumes(
                request["src_attr"]["volumes"], request["dst_attr"]["dst_disk_type"], elf_fs_client
            )
            tasks.update(task)

        return tasks

    # It handles both vm json and vm_template json
    @staticmethod
    def _generate_convert_disk_task_from_vm(disks, vm_json):
        conversions = []

        for each_disk in disks:
            # find this disk in vm or vm_template
            policy_uuid = None
            encryption_algorithm = elf_constants.VOLUME_ENCRYPTION_ALG_PLAINTEXT
            for vol in vm_json["disks"]:
                if vol["type"] != constants.IMPORTED_DISK:
                    continue

                if vol["url"] == each_disk["url"]:
                    policy_uuid = vol.get("storage_policy_uuid", None)
                    encryption_algorithm = vol.get(
                        "encryption_algorithm", elf_constants.VOLUME_ENCRYPTION_ALG_PLAINTEXT
                    )
                    break

            vol_uuid = str(uuid.uuid4())
            client = elf_fs.ElfFsClient()
            file_meta = client.get_file_meta(elf_fs.get_file_id_from_url(each_disk["url"]))
            conversion = {
                "storage_cluster_uuid": file_meta.get("storage_cluster_uuid"),
                "src_url": each_disk["url"],
                "src_real_size": file_meta["file_size"],
                "dst_disk_type": data_port.DataPort.FILE_TYPE_RAW,
                "dst_url": DataPortWrapper._create_temporary_lun_for_file(
                    file_meta,
                    vol_uuid,
                    policy_uuid,
                    encryption_algorithm,
                ),
                "dst_temp_volume_uuid": vol_uuid,
            }
            conversions.append(conversion)

        return {"convert_disks": {"src": conversions}}

    @staticmethod
    def _align_to_2_GB(integer):
        mask = (1 << 31) - 1

        # round up alignment
        return (int(integer) + mask) & ~mask

    @staticmethod
    def create_data_port_lun(
        volume_uuid,
        storage_policy_uuid,
        size_in_byte=None,
        src_target=None,
        src_lun_id=None,
        storage_cluster_uuid=None,
        encryption_algorithm=elf_constants.VOLUME_ENCRYPTION_ALG_PLAINTEXT,
    ):
        iscsi_client = zbs_iscsi.ZbsClientWrapper(sc_utils.init_zbs_iscsi_client(storage_cluster_uuid))
        manager = volume_manager.ISCSIVolumeManager.from_storage_policy_uuid(storage_policy_uuid, iscsi_client)

        # The allowed_initiators specified for creating_lun in vhost mode have a special format:
        # 'iqn.2013-11.org.smartx:<vm uuid>.<host uuid>.<shared bit>',
        # while the allowed_initiators specified by OVF does not have the above format.
        # So we should specified a allowed_initiators with "" when create_lun, and perform the
        # 'zbs_lun_update' function after creating the lun.
        lun = manager.create_lun(
            volume_uuid,
            size_in_byte=size_in_byte,
            src_target_name=src_target,
            src_lun_id=src_lun_id,
            allowed_initiators="",
            single_access=True,
            encryption_algorithm=encryption_algorithm,
        )
        zbs_iscsi.zbs_lun_update(
            iscsi_client, lun["target_name"], lun["lun_id"], elf_constants.DEFAULT_QEMU_IMG_INITIATOR_NAME, True
        )

        if storage_cluster_uuid:
            sc = storage_cluster.StorageCluster.load(storage_cluster_uuid)
            portal = f"{sc.access_info.ip}:{sc_utils.gen_default_ports()[elf_constants.ISCSI_PORT_KEY]}"
        else:
            portal = "127.0.0.1:3260"

        return f"iscsi://{portal}/{lun['path'][len('iscsi://') :]}"

    @staticmethod
    def delete_data_port_lun(volume_uuid, storage_cluster_uuid=None):
        iscsi_client = zbs_iscsi.ZbsClientWrapper(sc_utils.init_zbs_iscsi_client(storage_cluster_uuid))
        manager = volume_manager.ISCSIVolumeManager.get_default(iscsi_client)

        # In versions before 6.0.0, the name of the temporary volume created when importing
        # OVF will carry the '.data_port_temp' suffix, but after 6.0.0 we will not carry
        # this suffix for the following reasons:
        #   1. In versions before 6.0.0, the temporary volume created during export will not
        #      carry this suffix;
        #   2. Carrying this suffix will cause the volume uuid recorded in the database to
        #      be inconsistent with the actual volume uuid, making it difficult to
        #      understand and troubleshoot.
        # However, if there is an import failure case in the previous version, then it
        # cannot delete the temporary lun according to the dst_temp_volume_uuid recorded in
        # the database.
        # Therefore, in order to deal with the previous OVF import failure scenario, the
        # normal volume uuid and the volume with the suffix '.data_port_temp' are deleted at
        # the same time
        return manager.delete_lun(volume_uuid) or manager.delete_lun(f"{volume_uuid}.data_port_temp")

    @staticmethod
    def _create_temporary_lun_for_file(
        file_meta,
        vol_uuid,
        storage_policy_uuid,
        encryption_algorithm=elf_constants.VOLUME_ENCRYPTION_ALG_PLAINTEXT,
    ):
        location = elf_fs.ElfFsClient().get_file_path(elf_fs.get_url_from_file_id(file_meta["uuid"]))
        info = ff.get_disk_info_by_qemu(location, file_real_size=file_meta["file_size"])

        return DataPortWrapper.create_data_port_lun(
            vol_uuid,
            storage_policy_uuid,
            size_in_byte=DataPortWrapper._align_to_2_GB(info["virtual-size"]),
            storage_cluster_uuid=file_meta["storage_cluster_uuid"],
            encryption_algorithm=encryption_algorithm,
        )

    @staticmethod
    def _generate_convert_disk_task_from_volumes(disks, volumes):
        conversions = []

        for each_disk, each_volume in zip(disks, volumes):
            client = elf_fs.ElfFsClient()
            file_meta = client.get_file_meta(elf_fs.get_file_id_from_url(each_disk["url"]))
            vol_uuid = str(uuid.uuid4())
            conversion = {
                "storage_cluster_uuid": file_meta.get("storage_cluster_uuid"),
                "src_url": each_disk["url"],
                "src_real_size": file_meta["file_size"],
                "dst_disk_type": data_port.DataPort.FILE_TYPE_RAW,
                "dst_url": DataPortWrapper._create_temporary_lun_for_file(
                    file_meta,
                    vol_uuid,
                    each_volume.get("storage_policy_uuid", None),
                    each_volume.get("encryption_algorithm", elf_constants.VOLUME_ENCRYPTION_ALG_PLAINTEXT),
                ),
                "dst_temp_volume_uuid": vol_uuid,
            }
            conversions.append(conversion)

        return {"convert_disks": {"src": conversions}}

    @staticmethod
    def _generate_build_ovf_task(src_id, with_mac_address=False):
        return {"build_ovf": {"resource_uuid": src_id, "with_mac_address": with_mac_address}}

    @staticmethod
    def _generate_export_volumes_task_from_vm(vm_json, dst_disk_type, elf_fs_client):
        conversions = []

        vq = volume_querier.VolumeQuerier()
        disks = vq.query({"path": {"$in": [each["path"] for each in vm_json["disks"]]}}, top=256)
        disks = {each["path"]: each for each in disks}

        for each in vm_json["disks"]:
            if each["type"] != constants.DISK:
                continue

            each_disk = disks[each["path"]]
            # Do no export sharing disk
            if each_disk["sharing"]:
                continue

            filename = "{}.{}".format(each_disk["name"], dst_disk_type)
            volume = vol_wrapper.VolumeWrapper.get(each["volume_uuid"])
            # storage_cluster_uuid may not exist in the volume before the upgrade,
            # so need to use the get method here.
            storage_cluster_uuid = volume.get("storage_cluster_uuid")
            dest_file_size = DataPortWrapper._estimate_export_file_size(
                volume.get("path"), volume["size_in_byte"], dst_disk_type
            )

            conversion = {
                "storage_cluster_uuid": storage_cluster_uuid,
                "src_url": each_disk["path"],
                "dst_disk_type": dst_disk_type,
                "dst_url": DataPortWrapper._create_elf_fs_file_placeholder(
                    elf_fs_client,
                    name=filename,
                    size=dest_file_size,
                    storage_cluster_uuid=storage_cluster_uuid,
                ),
                "file_name": filename,
                "bus_type": each["bus"],
                "storage_policy_uuid": volume["storage_policy_uuid"],
            }
            conversions.append(conversion)

        if len(conversions) == 0:
            logging.info("no export volumes convertion found in this data port")

        return {"export_volumes": {"src": conversions}}

    @staticmethod
    def _generate_export_volumes_task_from_vm_template(tpl_json, dst_disk_type, elf_fs_client):
        conversions = []

        vq = volume_template_querier.VolumeTemplateQuerier()

        disks = vq.query(
            {
                "uuid": {
                    "$in": [
                        each["volume_template_uuid"] for each in tpl_json["disks"] if each["type"] == constants.DISK
                    ]
                }
            },
            top=256,
        )
        disks = {each["uuid"]: each for each in disks}

        for each in tpl_json["disks"]:
            if each["type"] != constants.DISK:
                continue

            each_disk = disks[each["volume_template_uuid"]]

            filename = "{}.{}".format(each_disk["name"], dst_disk_type)
            # storage_cluster_uuid may not exist in the volume template before the upgrade,
            # so need to use the get method here.
            storage_cluster_uuid = each_disk.get("storage_cluster_uuid")
            dest_file_size = DataPortWrapper._estimate_export_file_size(
                each_disk.get("path"), each_disk["size"], dst_disk_type
            )

            conversion = {
                "storage_cluster_uuid": storage_cluster_uuid,
                "src_url": each_disk["path"],
                "dst_disk_type": dst_disk_type,
                "dst_url": DataPortWrapper._create_elf_fs_file_placeholder(
                    elf_fs_client,
                    name=filename,
                    size=dest_file_size,
                    storage_cluster_uuid=storage_cluster_uuid,
                ),
                "file_name": filename,
                "bus_type": each["bus"],
                "storage_policy_uuid": each_disk["storage_policy_uuid"],
            }
            conversions.append(conversion)

        if len(conversions) == 0:
            logging.info("no export volumes convertion found in this data port")

        return {"export_volumes": {"src": conversions}}

    @staticmethod
    def _estimate_export_file_size(iscsi_path, lun_size, disk_type):
        # Export product on elf platform is thin provisioned
        if platform.is_in_elf():
            return lun_size

        if disk_type == data_port.DataPort.FILE_TYPE_VMDK or disk_type == data_port.DataPort.FILE_TYPE_QCOW2:
            info = iscsi.parse_iscsi_url(iscsi_path)
            occupied_size = zbs_iscsi.zbs_get_lun_occupied_size(info["authority"], info["lun_id"])
            return DataPortWrapper._align_to_2_GB(round(occupied_size * 1.1))

        # For raw disk
        return lun_size

    @staticmethod
    def _create_elf_fs_file_placeholder(client, name=None, size=None, storage_cluster_uuid=None):
        if name is None:
            name = str(uuid.uuid4())

        file_id = client.create_file(name, size=size, storage_cluster_uuid=storage_cluster_uuid)

        return elf_fs.get_url_from_file_id(file_id)

    @staticmethod
    def _generate_export_volumes_task_from_volumes(volumes, dst_disk_type, elf_fs_client):
        conversions = []
        vq = volume_querier.VolumeQuerier()
        vols = vq.query({"uuid": {"$in": [each["uuid"] for each in volumes]}}, top=256)
        vols = {vol["uuid"]: vol for vol in vols}

        for each in volumes:
            vol = vols.get(each["uuid"], None)
            if vol is None:
                raise base.ResourceException(
                    "no volumes {} found".format(each["uuid"]), py_error.DATAPORT_VOLUME_NOT_EXISTS
                )

            filename = "{}.{}".format(vol["name"], dst_disk_type)
            # storage_cluster_uuid may not exist in the volume before the upgrade,
            # so need to use the get method here.
            storage_cluster_uuid = vol.get("storage_cluster_uuid")
            dest_file_size = DataPortWrapper._estimate_export_file_size(
                vol.get("path"), vol["size_in_byte"], dst_disk_type
            )

            conversion = {
                "storage_cluster_uuid": storage_cluster_uuid,
                "src_url": vol["path"],
                "dst_disk_type": dst_disk_type,
                "dst_url": DataPortWrapper._create_elf_fs_file_placeholder(
                    elf_fs_client,
                    name=filename,
                    size=dest_file_size,
                    storage_cluster_uuid=storage_cluster_uuid,
                ),
                "file_name": filename,
                # This is a fake bus_type, which will only effect 'adapter_type'
                # of generated vmdk file
                "bus_type": "scsi",
                "storage_policy_uuid": vol["storage_policy_uuid"],
            }
            conversions.append(conversion)

        if len(conversions) == 0:
            logging.info("no export volumes convertion found in this data port")

        return {"export_volumes": {"src": conversions}}

    @staticmethod
    def _generate_related_resources_from_request(request):
        related_resources = {}

        if request["dst_type"] == data_port.DataPort.RES_VM:
            related_resources[constants.KVM_VM] = request["dst_attr"]
            if virt.is_aarch64():
                related_resources[constants.KVM_VM]["firmware"] = elf_constants.VM_FIRMWARE_UEFI

        if request["dst_type"] == data_port.DataPort.RES_VM_TEMPLATE:
            related_resources[constants.KVM_VM_TEMPLATE] = request["dst_attr"]
            if virt.is_aarch64():
                related_resources[constants.KVM_VM_TEMPLATE]["firmware"] = elf_constants.VM_FIRMWARE_UEFI

        if request["dst_type"] == data_port.DataPort.RES_ELF_VOLUME:
            related_resources[constants.KVM_VOL_ISCSI] = request["dst_attr"]["volumes"]

        return related_resources

    def to_user_view(self):
        user_view = {
            "uuid": self._resource.uuid,
            "status": self._resource.status,
            "src_type": self._resource.src_type,
            "dst_type": self._resource.dst_type,
            "convert_result": {},
        }

        if self._resource.status != data_port.DataPort.STATUS_SUCCEEDED:
            return user_view

        # task 'export_volumes' should exists when export vm, vm_template or volumes to disk files
        elf_fs_cli = elf_fs.ElfFsClient()

        if self._resource.dst_type == data_port.DataPort.RES_OVF:
            ovf_url = self._resource.task_progress["build_ovf"]["dst_file_url"]

            meta = elf_fs_cli.get_file_meta(elf_fs.get_file_id_from_url(ovf_url))

            user_view["convert_result"]["files"] = [
                {
                    "type": data_port.DataPort.FILE_TYPE_OVF,
                    "url": ovf_url,
                    "file_name": self._resource.task_progress["build_ovf"]["file_name"],
                    "file_size": meta["file_size"],
                    "md5": meta["md5"],
                }
            ]

            # task 'export_volumes' should exists when export ovf
            for each in self._resource.task_arguments["export_volumes"]["src"]:
                # query md5 and file size
                meta = elf_fs_cli.get_file_meta(elf_fs.get_file_id_from_url(each["dst_url"]))

                user_view["convert_result"]["files"].append(
                    {
                        "type": each["dst_disk_type"],
                        "url": each["dst_url"],
                        "file_name": each["file_name"],
                        "file_size": meta["file_size"],
                        "md5": meta["md5"],
                    }
                )

        if self._resource.dst_type == data_port.DataPort.RES_DISK:
            user_view["convert_result"]["files"] = []

            for each in self._resource.task_arguments["export_volumes"]["src"]:
                # query md5 and file size
                meta = elf_fs_cli.get_file_meta(elf_fs.get_file_id_from_url(each["dst_url"]))

                user_view["convert_result"]["files"].append(
                    {
                        "type": each["dst_disk_type"],
                        "url": each["dst_url"],
                        "file_name": each["file_name"],
                        "file_size": meta["file_size"],
                        "md5": meta["md5"],
                    }
                )

        return user_view

    def resource(self):
        return self._resource

    def save(self):
        self._resource.save()
        return self._resource.uuid

    def _handle_vm_disks_resources(self, disks, res_uuid):
        disk.check_repeated_path(disks)

        disk.ensure_cdrom_bus(disks, "data_port", res_uuid=res_uuid)

        # pre-allocate raw disk file on nfs if there's imported disk
        for each_disk in disks:
            if each_disk["type"] != constants.IMPORTED_DISK:
                continue
            # find that conversion task
            dst_url = None
            for task in self._resource.task_arguments["convert_disks"]["src"]:
                if task["src_url"] == each_disk["url"]:
                    # get preallocate dst file on nfs
                    dst_url = task["dst_url"]
                    each_disk["storage_cluster_uuid"] = task["storage_cluster_uuid"]
                    break

            if dst_url is None:
                raise base.ResourceException("can not find disk conversion task by url", py_error.DATAPORT_INVALID)

            # update vm disk with src_export_id and src_inode_id
            parsed = iscsi.parse_iscsi_url(dst_url)
            authority, lun_id = parsed["authority"], parsed["lun_id"]

            each_disk["type"] = constants.DISK
            each_disk.pop("url")
            each_disk["src_target_name"] = authority
            each_disk["src_lun_id"] = lun_id
            # It is mandatory to specify clone_before_create=True here because:
            # 1. Consistent with smartx elf behavior;
            # 2. If this parameter is not specified, the lun_move method will
            #    be used when creating a new virtual volume, which will move the
            #    allowed_initiators on the temporary lun to the newly created volume.
            #    This is inconsistent with the attributes of the new virtual volume.
            #    However, using clone method does not have this problem.
            each_disk["clone_before_create"] = True

        # check storage policy
        for each_disk in disks:
            if "path" not in each_disk and "size_in_byte" not in each_disk:
                src_export_id = each_disk.get("src_export_id")
                src_inode_id = each_disk.get("src_inode_id")
                src_target_name = each_disk.get("src_target_name")
                src_lun_id = each_disk.get("src_lun_id")

                if (src_export_id and src_inode_id) or (src_target_name and src_lun_id):
                    each_disk["storage_policy_uuid"] = volume.check_or_create_storage_policy(
                        src_target_name,
                        src_lun_id,
                        src_export_id,
                        src_inode_id,
                        each_disk.get("storage_policy_uuid"),
                        each_disk["storage_cluster_uuid"],
                    )
                else:
                    raise base.ResourceException(
                        "Invalid (src_export_id={}, src_inode_id={})"
                        " or (src_target_name={}, src_lun_id={}"
                        ").".format(src_export_id, src_inode_id, src_target_name, src_lun_id),
                        py_error.REST_INVALID_PARAMETERS,
                    )

    def _generate_vm_resources(self):
        # There's something different from the vm_doc we use in VM API,
        # like "disks"
        vm_doc = self._resource.related_resources[constants.KVM_VM]

        vm_uuid = str(uuid.uuid4())
        self._handle_vm_disks_resources(vm_doc["disks"], vm_uuid)

        # From now on, the whole vm_doc is the same as usual one
        disks, volumes = vm_wrapper.VMWrapper.build_disks(vm_doc["disks"])

        vm_wrapper.VMWrapper.vm_name_unique_check(vm_doc["vm_name"])

        vm = vm_wrapper.VMWrapper.new(
            vm_uuid=vm_uuid,
            vm_name=vm_doc["vm_name"],
            vcpu=vm_doc["vcpu"],
            cpu=vm_doc.get("cpu", {"topology": {"cores": 1, "sockets": vm_doc["vcpu"]}}),
            memory=vm_doc["memory"],
            node_ip=vm_doc.get("node_ip", None),
            ha=vm_doc["ha"],
            boot_with_host=vm_doc.get("boot_with_host", False),
            ha_priority=vm_doc.get("ha_priority", elf_constants.HA_PRIORITY_DEFAULT),
            description=vm_doc.get("description", ""),
            nics=vm_doc["nics"],
            disks=disks,
            hostdevs=vm_doc.get("hostdevs", []),
            auto_schedule=vm_doc.get("auto_schedule", False),
            clock_offset=vm_doc.get("clock_offset", elf_constants.VM_CLOCK_OFFSET_UTC),
            win_opt=vm_doc.get("win_opt"),
            nested_virtualization=vm_doc.get("nested_virtualization"),
            cpu_model=vm_doc.get("cpu_model", constants.CLUSTER_DEFAULT_CPU_MODEL),
            status=vm_doc.get("status", None),
            internal=vm_doc.get("internal", False),
            firmware=vm_doc.get("firmware", elf_constants.VM_FIRMWARE_BIOS),
            quota_policy=vm_doc.get("quota_policy"),
            video_type=vm_doc.get("video_type"),
            guest_os_type=vm_doc.get("guest_os_type", elf_constants.GuestOSType.UNKNOWN),
        )

        if vm_doc.get("auto_schedule"):
            check_item = {vd.ValidatorChain.check.auto_schedule_node: None, vd.ValidatorChain.check.forbidden_sriov: {}}
        else:
            check_item = {vd.ValidatorChain.check.node_avaliable: vm_doc["node_ip"]}
        if vm_doc.get("ha"):
            check_item.update({vd.ValidatorChain.check.forbidden_sriov: {}})

        check_item.update({vd.ValidatorChain.check.ip_available: None})
        check_item.update({vd.ValidatorChain.check.is_valid_vlan: {}})
        check_item.update({vd.ValidatorChain.check.vm_memory_size: {}})
        check_item.update({vd.ValidatorChain.check.nic_mac_address: {}})
        check_item.update({vd.ValidatorChain.check.encryption_algorithm: {}})

        vd.ValidatorChain(vm).validate(check_item)

        return vm.generate_resources_for_creation(volumes, folder_uuid=vm_doc.get("folder_uuid", None))

    def _generate_vm_template_resources(self):
        vm_tpl_doc = self._resource.related_resources[constants.KVM_VM_TEMPLATE]

        template_uuid = str(uuid.uuid4())
        self._handle_vm_disks_resources(vm_tpl_doc["disks"], template_uuid)

        for index, each_disk in enumerate(vm_tpl_doc.get("disks", [])):
            each_disk["boot"] = index + 1

        vm_tpl = tpl_wrapper.VMTemplateWrapper.new(
            template_uuid=template_uuid,
            name=vm_tpl_doc.get("name") or vm_tpl_doc.get("vm_name"),
            description=vm_tpl_doc.get("description"),
            ha=vm_tpl_doc.get("ha"),
            firmware=vm_tpl_doc.get("firmware", elf_constants.VM_FIRMWARE_BIOS),
            vcpu=vm_tpl_doc.get("vcpu"),
            cpu=vm_tpl_doc.get("cpu", {"topology": {"cores": 1, "sockets": vm_tpl_doc["vcpu"]}}),
            memory=vm_tpl_doc.get("memory"),
            nics=vm_tpl_doc.get("nics"),
            disks=vm_tpl_doc["disks"],
            nested_virtualization=vm_tpl_doc.get("nested_virtualization"),
            cpu_model=vm_tpl_doc.get("cpu_model", constants.CLUSTER_DEFAULT_CPU_MODEL),
            quota_policy=vm_tpl_doc.get("quota_policy"),
            clock_offset=vm_tpl_doc.get("clock_offset", elf_constants.VM_CLOCK_OFFSET_UTC),
            video_type=vm_tpl_doc.get("video_type"),
            win_opt=vm_tpl_doc.get("win_opt", False),
            cloud_init_supported=vm_tpl_doc.get("cloud_init_supported", False),
            guest_os_type=vm_tpl_doc.get("guest_os_type"),
        )

        return vm_tpl.generate_resources_for_creation()

    def _generate_one_volume_resources(self, conversion, volume_doc):
        src_iscsi_path = conversion["dst_url"]
        parsed = iscsi.parse_iscsi_url(src_iscsi_path)
        src_target_name, src_lun_id = parsed["authority"], parsed["lun_id"]

        storage_policy_uuid = volume_doc.get("storage_policy_uuid")

        if src_target_name and src_lun_id:
            # assign the value `KVM_VOL_ISCSI` to avoid
            # the not expected type.
            storage_policy_uuid = volume.check_or_create_storage_policy(
                src_target_name,
                src_lun_id,
                None,
                None,
                storage_policy_uuid,
                storage_cluster_uuid=conversion["storage_cluster_uuid"],
            )
        else:
            raise base.ResourceException(
                "Invalid src_target_name={}, src_lun_id={}".format(src_target_name, src_lun_id),
                py_error.REST_INVALID_PARAMETERS,
            )

        return vol_wrapper.VolumeWrapper.new(
            size_in_byte=None,
            name=volume_doc["name"],
            description=volume_doc.get("description"),
            volume_type=constants.KVM_VOL_ISCSI,
            storage_policy_uuid=storage_policy_uuid,
            sharing=volume_doc.get("sharing", False),
            src_target_name=src_target_name,
            src_lun_id=src_lun_id,
            allowed_initiators=volume_doc.get("allowed_initiators", ""),
            single_access=volume_doc.get("single_access", True),
            storage_cluster_uuid=conversion["storage_cluster_uuid"],
            clone_before_create=True,
            encryption_algorithm=volume_doc.get("encryption_algorithm", elf_constants.VOLUME_ENCRYPTION_ALG_PLAINTEXT),
        ).generate_resources_for_creation()

    def _generate_volume_resources(self, conversions):
        volume_docs = self._resource.related_resources[constants.KVM_VOL_ISCSI]

        resources = {}

        if len(conversions) != len(volume_docs):
            raise base.ResourceException(
                "length of input disks is not equal with expeceted volumes", py_error.DATAPORT_INVALID
            )

        for conversion, vol in zip(conversions, volume_docs):
            resources.update(self._generate_one_volume_resources(conversion, vol))

        return resources

    def submit_convert(self, user=None):
        from job_center.handler.leader.workers import job_submit

        self._resource.status = data_port.DataPort.STATUS_IN_PROGRESS

        resources = {self._resource.uuid: self._resource.to_dict()}

        # generate other neccessary resources
        if constants.KVM_VM in self._resource.related_resources:
            resources.update(self._generate_vm_resources())

        if constants.KVM_VM_TEMPLATE in self._resource.related_resources:
            resources.update(self._generate_vm_template_resources())

        if constants.KVM_VOL_ISCSI in self._resource.related_resources:
            resources.update(self._generate_volume_resources(self._resource.task_arguments["convert_disks"]["src"]))

        # create event
        event = dp_event.DataPortEventWrapper(user).event_dataport_start_conversion(self._resource.to_dict())

        # submit job
        job_info = {
            "user": user,
            "description": code.API_DATA_PORT_CONVERT,
            "resources": resources,
            "resource_group": None,
            "job_id": str(uuid.uuid4()),
            "event": event,
        }

        return {"job_id": job_submit(**job_info)}

    def submit_delete(self, user=None):
        from job_center.handler.leader.workers import job_submit

        self._resource.resource_state = resources.RESOURCE_REMOVED

        resource = {self._resource.uuid: self._resource.to_dict()}

        event = dp_event.DataPortEventWrapper(user).event_dataport_delete(self._resource.to_dict())

        job_info = {
            "user": user,
            "description": code.API_DATA_PORT_DELETE,
            "resources": resource,
            "resource_group": None,
            "job_id": str(uuid.uuid4()),
            "event": event,
        }

        return {"job_id": job_submit(**job_info)}

    # remove temporary luns when import/export volumes
    def remove_temporary_resources(self):
        task_progress = self._resource.task_progress
        task_arguments = self._resource.task_arguments

        # Remove temporary volumes when export volumes or ovf
        if "export_volumes" in task_progress:
            for vol in task_progress["export_volumes"]["volumes"]:
                # Will ignore when volume not exists.
                # When there is a conversion task before the upgrade, the storage_cluster_uuid
                # field may not exist in the conversion parameters after the upgrade,
                # so the get method needs to be used here.
                DataPortWrapper.delete_data_port_lun(vol["uuid"], vol.get("storage_cluster_uuid"))

        if "convert_disks" in task_arguments:
            for conversion in task_arguments["convert_disks"]["src"]:
                # Will ignore when volume not exists.
                # When there is a conversion task before the upgrade, the storage_cluster_uuid
                # field may not exist in the conversion parameters after the upgrade,
                # so the get method needs to be used here.
                DataPortWrapper.delete_data_port_lun(
                    conversion["dst_temp_volume_uuid"], conversion.get("storage_cluster_uuid")
                )

    # remove user uploaded resources, including original disk files
    def remove_user_resources(self):
        client = elf_fs.ElfFsClient()
        task_arguments = self._resource.task_arguments

        # Remove src disk file when import vm/vm_template/volume
        if "convert_disks" in task_arguments:
            for conversion in task_arguments["convert_disks"]["src"]:
                client.delete_file(elf_fs.get_file_id_from_url(conversion["src_url"]))

    def remove_conversion_results(self):
        client = elf_fs.ElfFsClient()
        task_progress = self._resource.task_progress
        task_arguments = self._resource.task_arguments

        # Remove ovf files
        if "build_ovf" in task_progress:
            ovf_file = task_progress["build_ovf"]["dst_file_url"]
            if ovf_file is not None and len(ovf_file) > 0:
                client.delete_file(elf_fs.get_file_id_from_url(ovf_file))

        # Remove dst disk when export
        if "export_volumes" in task_arguments:
            for conversion in task_arguments["export_volumes"]["src"]:
                client.delete_file(elf_fs.get_file_id_from_url(conversion["dst_url"]))

    # Should be called inside job center worker
    def delete_self(self):
        if self._resource.status == data_port.DataPort.STATUS_IN_PROGRESS:
            raise base.ResourceException("Can not delete when data port is running", py_error.DATAPORT_IS_RUNNING)

        self.remove_temporary_resources()
        self.remove_user_resources()
        self.remove_conversion_results()

    # update to db immediately
    def mark_as_failed(self):
        self._resource.update_status(data_port.DataPort.STATUS_FAILED)

    # update to db immediately
    def mark_as_succeeded(self):
        self._resource.update_status(data_port.DataPort.STATUS_SUCCEEDED)

    # update to db immediately
    def mark_as_canceled(self):
        self._resource.update_status(data_port.DataPort.STATUS_CANCELED)

    # update to db immediately
    # This method may failed due to condition racing
    def try_mark_as_canceling(self):
        return self._resource.update_status_atomic(data_port.DataPort.STATUS_CANCELING)

    # update to db immediately
    # This method may failed due to condition racing
    def try_mark_as_in_progress(self):
        return self._resource.update_status_atomic(data_port.DataPort.STATUS_IN_PROGRESS)

    def create_task_progress(self, task_name, attrs, status=data_port.DataPort.TASK_STATUS_IN_PROGRESS):
        self._resource.create_task_progress(task_name, attrs, status)

    def start_new_disk_conversion_process(self, pid):
        self._resource.start_new_disk_conversion_process(pid)

    def start_new_export_volume_process(self, pid):
        self._resource.start_new_export_volume_process(pid)

    def save_temp_volume_when_export_volumes(self, vol):
        self._resource.save_temp_volume_when_export_volumes(vol)

    def save_ovf_file(self, url, name):
        self._resource.save_ovf_file(url, name)

    def mark_task_finished(self, name):
        self._resource.mark_task_finished(name)
