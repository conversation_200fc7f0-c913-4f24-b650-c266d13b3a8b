# Copyright (c) 2013-2016, SMARTX
# All rights reserved.
from contextlib import contextmanager
import logging
import math
import random
import sys
import time
import uuid

from pymongo import ASCENDING, errors

from common.mongo.db import mongodb
from smartx_app.elf.common import constants
from smartx_app.elf.common.config import platform
from smartx_app.elf.common.resources.base import ResourceException
from smartx_app.elf.common.resources.storage_policy import StoragePolicy
from smartx_app.elf.common.utils import cluster, zbs_iscsi
from smartx_app.elf.common.utils import iscsi as iscsi_util
from smartx_app.elf.common.utils import resource as resource_util
from smartx_app.elf.common.utils import volume_encryption as volume_enc_utils
from smartx_app.elf.common.utils import volume_properties as volume_properties_util
from smartx_proto.errors import pyerror_pb2 as py_error
from zbs.config.constant import LUN_BLOCK_SIZE
from zbs.lib.zexception import ZConnectException, ZException
from zbs.nfs.client import NFS_TYPE_FILE, ZbsNFS
from zbs.proto import error_pb2 as zbs_error


class _BaseISCSIVolumeManager:
    DEFAULT_ELF_ISCSI_TARGET_PREFIX = constants.DEFAULT_ISCSI_TARGET_PREFIX
    _ALL_LUN_IDS = set(range(1, 254))
    _MAXIMUM_TRYING_COUNT = 3
    _FREEZE_GUARANTEE_UX_NAME = "target_name_1_lun_id_1"

    _has_freeze_guarantee_ux = None
    resources_db = mongodb.resources

    def __init__(self, storage_policy, iscsi_client=None):
        self.iscsi_client = zbs_iscsi.ZbsClientWrapper(iscsi_client)
        self.storage_policy = storage_policy

    @classmethod
    def ensure_index(cls):
        from pymongo import HASHED

        try:
            indexes = cls.resources_db.iscsi_paths.index_information()
        except errors.OperationFailure:
            indexes = {}

        if "volume_uuid_1" not in indexes:
            cls.resources_db.iscsi_paths.create_index("volume_uuid", background=True, unique=True)

        if "target_name_hash" not in indexes:
            cls.resources_db.iscsi_paths.create_index([("target_name", HASHED)], background=True)
        if "path_hash" not in indexes:
            cls.resources_db.iscsi_paths.create_index([("path", HASHED)])

        if "target_name_1_lun_id_1_path_1" not in indexes:
            cls.resources_db.iscsi_paths.create_index(
                [("target_name", ASCENDING), ("lun_id", ASCENDING), ("path", ASCENDING)]
            )

        if cls._FREEZE_GUARANTEE_UX_NAME not in indexes:
            try:
                cls.resources_db.iscsi_paths.create_index(
                    [("target_name", ASCENDING), ("lun_id", ASCENDING)],
                    background=True,
                    unique=True,
                )
            except errors.DuplicateKeyError as e:
                # For the upgraded cluster, theoretically there is an abnormal
                # situation where the `target_name` & `lun_id` is not unique.
                # It is avoided by double check, see method: `_freeze_lun_record`
                # and `unfreeze_lun_record` for more details.
                logging.warning(
                    "[ISCSIVolumeManager.ensure_index] Abandon creating the unique index "
                    "`iscsi_paths.target_name_1_lun_id_1`, error: %s" % str(e)
                )

    @classmethod
    def has_freeze_guarantee_ux(cls):
        if cls._has_freeze_guarantee_ux is None:
            indexes = cls.resources_db.iscsi_paths.index_information()
            cls._has_freeze_guarantee_ux = cls._FREEZE_GUARANTEE_UX_NAME in indexes

        return cls._has_freeze_guarantee_ux

    @classmethod
    def get_default(cls, iscsi_client=None):
        """Get the default volume manager."""
        return cls.from_storage_policy_uuid(StoragePolicy.DEFAULT_STORAGE_POLICY_UUID, iscsi_client)

    @classmethod
    def from_storage_policy_uuid(cls, storage_policy_uuid, iscsi_client=None):
        """Specify the storage policy uuid to create a volume manager."""
        return cls(StoragePolicy.load(storage_policy_uuid), iscsi_client)

    @classmethod
    def get_exist_target_names(cls):
        """The target names exist in MongoDB."""
        return cls.resources_db.iscsi_paths.distinct("target_name")

    @classmethod
    def remove_exist_target_names(cls, target_names):
        cls.resources_db.iscsi_paths.remove({"target_name": {"$in": target_names}})

    @classmethod
    def next_target_name(cls):
        """Get the next target name as `zbs-iscsi-datastore-<uuid>`."""
        return "{}-{}".format(cls.DEFAULT_ELF_ISCSI_TARGET_PREFIX, str(uuid.uuid4()))

    @property
    def target_names(self):
        """Get all target names from the current storage policy."""
        return list(self.storage_policy.datastores)

    @property
    def volume_min_size_in_byte(self):
        """lun's min size=256M*stripe_num"""
        if not hasattr(self, "__volume_min_size_in_byte"):
            setattr(self, "__volume_min_size_in_byte", LUN_BLOCK_SIZE * self.storage_policy.stripe_num)
        return getattr(self, "__volume_min_size_in_byte")

    def calc_valid_size_in_byte(self, size_in_byte):
        return int(math.ceil(float(size_in_byte) / self.volume_min_size_in_byte)) * self.volume_min_size_in_byte

    def is_valid_volume_size(self, size_in_byte):
        """whether it is valid volume size in byte."""
        return self.calc_valid_size_in_byte(size_in_byte) == size_in_byte

    def _get_available_lun_ids(self, target_name):
        return self._ALL_LUN_IDS - self._get_used_lun_ids(target_name)

    def _get_used_lun_ids(self, target_name):
        return set(self.resources_db.iscsi_paths.distinct("lun_id", {"target_name": target_name, "lun_id": {"$ne": 0}}))

    def _filter_available_targets(self, target_names, limit=10):
        cursor = self.resources_db.iscsi_paths.aggregate(
            [
                {"$match": {"target_name": {"$in": target_names}}},
                {"$project": {"_id": 0, "target_name": 1}},
                {"$group": {"_id": "$target_name", "c": {"$sum": 1}}},
                {"$match": {"c": {"$lt": 254}}},
                {"$sort": {"c": 1}},
                {"$limit": limit},
            ]
        )

        return [x["_id"] for x in cursor]

    def _update_or_reload_storage_policy(self):
        for _ in range(0, self._MAXIMUM_TRYING_COUNT):
            new_target_name = self.next_target_name()
            # lun id 0 is not use for volume, just as a placeholder
            # to freeze the target.
            if not self._freeze_lun_record(new_target_name, 0):
                time.sleep(1)
                continue

            try:
                self.storage_policy.add_target(new_target_name)
                break
            except ResourceException:
                logging.warning(
                    "The storage policy({}) has been concurrently modified, so move `{}` to trash.".format(
                        self.storage_policy.uuid, new_target_name
                    )
                )

                # reload the storage policy
                self.storage_policy = StoragePolicy.load(self.storage_policy.uuid)
                break

    def _pick_one_available_lun(self):
        for _ in range(0, self._MAXIMUM_TRYING_COUNT):
            target_names = self._filter_available_targets(self.storage_policy.datastores[:])
            if target_names:
                available_lun_ids = []
                random.shuffle(target_names)

                while target_names:
                    target_name = target_names.pop()
                    available_lun_ids.extend(
                        [(target_name, lun_id) for lun_id in self._get_available_lun_ids(target_name)]
                    )
                    # Expansion is required if the available LUN IDs is less than 20.
                    if len(available_lun_ids) >= 20:
                        return random.choice(available_lun_ids)

            self._update_or_reload_storage_policy()
        else:
            logging.warning(
                "Failed to get an available lun of the storage policy({}).".format(self.storage_policy.uuid)
            )
            raise ResourceException(
                "Failed to get an available lun of the storage policy.", py_error.STORAGE_POLICY_UNAVAILABLE_TARGET
            )

    def _ensure_current_target(self, target_name):
        try:
            self.iscsi_client.target_create(
                target_name,
                **self.storage_policy.gen_target_create_parameters(),
            )
        except ZException as e:
            if e.ec_name != zbs_error.ErrorCode.Name(zbs_error.EDuplicate):
                raise

    def _get_iscsi_path(self, froze_lun_record):
        return "iscsi://{}/{}".format(
            self.iscsi_client.target_get(froze_lun_record["target_name"]).iqn_name,
            froze_lun_record["lun_id"],
        )

    def _create_lun(
        self,
        volume_uuid,
        size_in_byte=None,
        src_snapshot_id=None,
        src_lun_id=None,
        src_pool_name=None,
        src_export_id=None,
        src_inode_id=None,
        clone_before_create=True,
        ret_diff_size=False,
        ret_extra_lun_attrs=None,
        preferred_cid=None,
        allowed_initiators=None,
        single_access=False,
        prioritized=None,
        encrypt_method=None,
        labels=None,
    ):
        froze_lun_record = self.freeze_lun_record(volume_uuid)
        args_str_4_logging = (
            "froze_lun_record={}, size_in_byte={}, src_lun_id={}, "
            "src_pool_name={}, src_snapshot_id={}, src_snapshot_id={},"
            "src_inode_id={}, clone_before_create={}, prioritized={}, encrypt_method={}, labels={}".format(
                froze_lun_record,
                size_in_byte,
                src_lun_id,
                src_pool_name,
                src_snapshot_id,
                src_export_id,
                src_inode_id,
                clone_before_create,
                prioritized,
                encrypt_method,
                labels,
            )
        )

        try:
            self._ensure_current_target(froze_lun_record["target_name"])
            lun = self._create_lun_from_froze_record_best_effort(
                froze_lun_record=froze_lun_record,
                size_in_byte=size_in_byte,
                src_snapshot_id=src_snapshot_id,
                src_lun_id=src_lun_id,
                src_pool_name=src_pool_name,
                src_inode_id=src_inode_id,
                src_export_id=src_export_id,
                clone_before_create=clone_before_create,
                preferred_cid=preferred_cid,
                allowed_initiators=allowed_initiators,
                single_access=single_access,
                prioritized=prioritized,
                encrypt_method=encrypt_method,
                labels=labels,
            )
        except ZException as e:
            logging.warning(f"Failed to create volume {froze_lun_record['volume_uuid']}, error: {e}")
            self.unfreeze_lun_record(froze_lun_record["volume_uuid"], safe=True)
            raise
        else:
            logging.info("Successfully create a new LUN: {}.".format(args_str_4_logging))

            return self._get_return_data_from_lun(lun, froze_lun_record, ret_diff_size, ret_extra_lun_attrs)

    def _get_return_data_from_lun(self, lun, froze_lun_record, ret_diff_size, ret_extra_lun_attrs):
        ret_data = {}
        if ret_diff_size:
            volume = self.iscsi_client.volume_show_by_id(lun.volume_id).volume
            unique_size = volume.unique_size
            ret_data["diff_size"] = resource_util.ensure_diff_size(unique_size)
            ret_data["unique_size"] = unique_size

        ret_data.update(froze_lun_record)
        ret_data["size_in_byte"] = lun.size
        ret_data["zbs_volume_id"] = lun.volume_id
        ret_data["path"] = self._get_iscsi_path(froze_lun_record)
        ret_data["prioritized"] = lun.prioritized
        ret_data["thin_provision"] = lun.thin_provision
        ret_data["encryption_algorithm"] = volume_enc_utils.convert_zbs_volume_encrypt_method_to_elf(
            lun.encrypt_method if hasattr(lun, "encrypt_method") else None
        )

        if ret_extra_lun_attrs:
            for attr in ret_extra_lun_attrs:
                ret_data.setdefault(attr, getattr(lun, attr))

        return ret_data

    def _create_lun_from_froze_record_best_effort(
        self,
        froze_lun_record,
        size_in_byte,
        src_snapshot_id,
        src_lun_id,
        src_pool_name,
        src_inode_id,
        src_export_id,
        clone_before_create,
        preferred_cid,
        allowed_initiators,
        single_access,
        prioritized,
        encrypt_method,
        labels,
    ):
        try:
            return self._create_lun_from_froze_record(
                froze_lun_record,
                size_in_byte,
                src_snapshot_id,
                src_lun_id,
                src_pool_name,
                src_inode_id,
                src_export_id,
                clone_before_create,
                preferred_cid,
                allowed_initiators,
                single_access,
                prioritized,
                encrypt_method,
                labels,
            )
        except ZConnectException as e:
            if e.user_code == py_error.ZBS_ISCSI_CLIENT_TIMEOUT:
                lun_id = froze_lun_record["lun_id"]
                pool_name = froze_lun_record["target_name"]
                volume_uuid = froze_lun_record["volume_uuid"]

                logging.warning(
                    f"[_create_lun_from_froze_record_best_effort] Wait as long as possible when the LUN({pool_name}, "
                    f"{lun_id}, {volume_uuid}) creation timeout..."
                )

                lun = resource_util.get_res_best_effort(
                    get_res_func=lambda: self.iscsi_client.lun_get(pool_name, lun_id), res_name=volume_uuid
                )
                if lun is not None:
                    return lun
            raise

    def _create_lun_from_froze_record(
        self,
        froze_lun_record,
        size_in_byte,
        src_snapshot_id,
        src_lun_id,
        src_pool_name,
        src_inode_id,
        src_export_id,
        clone_before_create,
        preferred_cid,
        allowed_initiators,
        single_access,
        prioritized,
        encrypt_method,
        labels,
    ):
        try:
            # convert a normal NFS file to an elf LUN
            if src_export_id and src_inode_id:
                return FileToLunHelper(self.iscsi_client.client).create_lun_from_file(
                    lun_id=froze_lun_record["lun_id"],
                    lun_name=froze_lun_record["volume_uuid"],
                    pool_name=froze_lun_record["target_name"],
                    src_export_id=src_export_id,
                    src_inode_id=src_inode_id,
                    clone_before_create=clone_before_create,
                )

            # convert a normal LUN to an elf LUN
            if not clone_before_create and src_pool_name and src_lun_id:
                result = self.iscsi_client.lun_move(
                    pool_name=froze_lun_record["target_name"],
                    lun_id=froze_lun_record["lun_id"],
                    lun_name=froze_lun_record["volume_uuid"],
                    src_pool_name=src_pool_name,
                    src_lun_id=src_lun_id,
                )
                self.iscsi_client.lun_update(
                    pool_name=froze_lun_record["target_name"],
                    lun_id=froze_lun_record["lun_id"],
                    labels=labels,
                )

                return result

            return self.iscsi_client.lun_create(
                lun_id=froze_lun_record["lun_id"],
                lun_name=froze_lun_record["volume_uuid"],
                pool_name=froze_lun_record["target_name"],
                size=size_in_byte,
                src_lun_id=src_lun_id,
                src_pool_name=src_pool_name,
                src_snapshot_id=src_snapshot_id,
                preferred_cid=preferred_cid,
                # TODO(yaodong): support read only
                # read_only=self.storage_policy.read_only,
                stripe_num=self.storage_policy.stripe_num,
                stripe_size=self.storage_policy.stripe_size,
                allowed_initiators=allowed_initiators,
                single_access=single_access,
                prioritized=prioritized,
                thin_provision=self.storage_policy.thin_provision,
                encrypt_method=encrypt_method,
                labels=labels,
            )
        except ZException as e:
            if e.ec_name == zbs_error.ErrorCode.Name(zbs_error.ECipherNotSupport):
                raise ResourceException(
                    "Failed to create volume because of the encryption is not enabled",
                    py_error.VOLUME_STORAGE_SIDE_ENCRYPTION_UNSUPPORTED,
                )

            # The src storage object is not found,
            # but it may be due to repeated creation.
            # we should try to detect this scene.
            if e.ec_name not in (
                zbs_error.ErrorCode.Name(zbs_error.ENotFound),
                zbs_error.ErrorCode.Name(zbs_error.EDuplicate),
            ):
                raise

            try:
                exist_lun = self.iscsi_client.lun_get(
                    pool_name=froze_lun_record["target_name"], lun_id=froze_lun_record["lun_id"]
                )
            except ZException:
                # The src storage object is not found and is not caused by
                # repeated creation, so re-raise the pre ZException.
                logging.warning(
                    "Fail to get LUN({}, {}), re-raise outer exception(volume_uuid={}).".format(
                        froze_lun_record["target_name"], froze_lun_record["lun_id"], froze_lun_record["volume_uuid"]
                    )
                )
                raise e
            else:
                # it shouldn't unless someone adds lun manually or bugs.
                if exist_lun.name == froze_lun_record["volume_uuid"]:
                    logging.warning("Try to create an exist lun({}).".format(froze_lun_record["volume_uuid"]))
                    return exist_lun
                else:
                    logging.error(
                        "(serious)Fail to create a new LUN. "
                        "The expected LUN({}) is present but with "
                        "a different name({}).".format(froze_lun_record["volume_uuid"], exist_lun.name)
                    )
                    raise e

    def _freeze_lun_record(self, target_name, lun_id, volume_uuid=None):
        criteria = {"target_name": target_name, "lun_id": lun_id}
        volume_uuid = volume_uuid or str(uuid.uuid4())
        iscsi_paths_col = self.resources_db.iscsi_paths

        try:
            result = iscsi_paths_col.update_one(
                criteria,
                {"$setOnInsert": {"volume_uuid": volume_uuid, "create_time": int(time.time())}},
                upsert=True,
            )
        except errors.DuplicateKeyError:
            logging.info(
                "Failed to freeze LUN record ({}, {}) because of errors.DuplicateKeyError.".format(target_name, lun_id)
            )
            return False
        else:
            if result.upserted_id is None:
                return False

            # For the upgraded cluster without the unique index `target_name_1_lun_id_1`,
            # so double check to avoid inserting the same document more than once.
            if not self.has_freeze_guarantee_ux() and iscsi_paths_col.count_documents(criteria) > 1:
                logging.info(
                    "Failed to freeze LUN record ({}, {}) because of docs count more than 1.".format(
                        target_name, lun_id
                    )
                )
                iscsi_paths_col.delete_one({"volume_uuid": volume_uuid})
                return False

            return True

    def _ensure_new_skip_all_zero_first_write(self, volume_uuid, lun, new_skip_all_zero_first_write):
        """
        This method is used to set the `new_skip_all_zero_first_write` for the volume creation.
        Due to the zbs API limitation, it is not supported to set the `new_skip_all_zero_first_write`
        when creating the lun. So we need to update it after creating the lun.

        Only in one scenario, the across cluster live migration, the `new_skip_all_zero_first_write`
        will be set to True. If failed to update it, it is not fatal error for the live migration.
        So we just log the error.
        """
        try:
            self.iscsi_client.lun_update(
                lun["lun_id"], lun["target_name"], new_skip_all_zero_first_write=new_skip_all_zero_first_write
            )
        except Exception as e:
            logging.warning(
                "Failed to update new_skip_all_zero_first_write for volume({}). Error is {}".format(volume_uuid, e)
            )

    def _update_iscsi_allowed_initiators(self, volume_uuid, froze_lun_record, allowed_initiators, single_access):
        logging.info(
            "Update iscsi allowed initiators for target{} with allowed_initiators {} and single_access {}".format(
                froze_lun_record["path"], allowed_initiators, single_access
            )
        )

        try:
            iscsi_util.update_iscsi_initiator(
                froze_lun_record["path"], allowed_initiators, single_access, self.iscsi_client
            )
        except Exception as e:
            error = ResourceException(
                "Failed to update iscsi initiator target({}) lun({}) for volume({}). Error is {}".format(
                    froze_lun_record["lun_id"], froze_lun_record["target_name"], volume_uuid, e
                ),
                py_error.VOLUME_UPDATE_ISCSI_INITIATOR_FAILED,
            )
            logging.error("{}".format(error))

            raise error from None

    def freeze_lun_record(self, volume_uuid, target_name=None, lun_id=None):
        """
        Freeze the lun record of the current target.

        target_name and lun_id are optional parameters.:
            The caller ensures that the unique lun_id and
            the concurrent conflict of the lun_id. SHOULD BE CAREFUL.
        """
        for _ in range(0, self._MAXIMUM_TRYING_COUNT):
            froze_lun_record = self.get_froze_lun_record(volume_uuid)
            if froze_lun_record is not None:
                return froze_lun_record

            if (target_name, lun_id).count(None) == 0:
                selected_target_name, selected_lun_id = target_name, lun_id
            else:
                selected_target_name, selected_lun_id = self._pick_one_available_lun()

            if self._freeze_lun_record(selected_target_name, selected_lun_id, volume_uuid):
                return self.get_froze_lun_record(volume_uuid)
        else:
            logging.warning("Failed to freeze lun of the storage policy({}).".format(self.storage_policy.uuid))
            raise ResourceException("Failed to freeze lun.", py_error.STORAGE_POLICY_LUN_RECORD_FREEZE_FAILURE)

    def unfreeze_lun_record(self, volume_uuid, safe=False):
        """Unfreeze the froze lun record if it exists.
        lun -> target -> storage policy is no longer a binding relationship
        """
        froze_lun_record = self.get_froze_lun_record(volume_uuid)
        if froze_lun_record is None:
            return True

        def mark_reclaim():
            self.resources_db.iscsi_paths.update_one({"volume_uuid": volume_uuid}, {"$set": {"status": "reclaiming"}})

        def unfreeze():
            self.resources_db.iscsi_paths.delete_one({"volume_uuid": volume_uuid})
            if not self.has_freeze_guarantee_ux():
                # Delete dirty data by `target_name` and `lun_id` for the upgraded cluster
                # without the unique index `target_name_1_lun_id_1`.
                self.resources_db.iscsi_paths.delete_many(
                    {"target_name": froze_lun_record["target_name"], "lun_id": froze_lun_record["lun_id"]}
                )
            return True

        if not safe:
            return unfreeze()

        try:
            self.iscsi_client.lun_get(pool_name=froze_lun_record["target_name"], lun_id=froze_lun_record["lun_id"])
        except Exception as exc:
            if getattr(exc, "ec_name", None) == zbs_error.ErrorCode.Name(zbs_error.ENotFound):
                logging.warning(
                    f"[unfreeze_lun_record] Due to the LUN({froze_lun_record['target_name']}, "
                    f"{froze_lun_record['lun_id']}) not existing, safely unfreeze the vol({volume_uuid} record."
                )
                return unfreeze()
            else:
                logging.warning(
                    "[unfreeze_lun_record] Not sure whether to unfreeze lun record, so we can't "
                    "unfreeze it({}), unexpected exception: {}".format(froze_lun_record["volume_uuid"], str(exc))
                )
                mark_reclaim()
                return False
        else:
            logging.warning(
                f"[unfreeze_lun_record] The LUN({froze_lun_record['target_name']}, {froze_lun_record['lun_id']}) "
                f"is present, so we can't unfreeze lun record({froze_lun_record['volume_uuid']})"
            )
            mark_reclaim()
            return False

    def get_froze_lun_record(self, volume_uuid):
        """Get the froze lun record if it exists.
        After the storage policy is available for modification,
        lun -> target -> storage policy is no longer a binding relationship
        """
        return self.resources_db.iscsi_paths.find_one({"volume_uuid": volume_uuid, "lun_id": {"$ne": 0}}, {"_id": 0})

    def create_lun(
        self,
        volume_uuid,
        size_in_byte=None,
        src_snapshot_id=None,
        src_volume_uuid=None,
        src_target_name=None,
        src_lun_id=None,
        src_export_id=None,
        src_inode_id=None,
        clone_before_create=True,
        ret_diff_size=False,
        ret_extra_lun_attrs=None,
        preferred_cid=None,
        allowed_initiators=None,
        single_access=False,
        prioritized=None,
        new_skip_all_zero_first_write=False,
        encryption_algorithm=None,
        vendor="",
        product="",
        serial="",
        wwn="",
    ):
        """Create a lun for the specified volume uuid. You must specify one and
        only specify one of `size_in_byte` or `src_snapshot_id` or
        `src_volume_uuid`.

        :param volume_uuid:         current volume uuid
        :param size_in_byte:        volume size
        :param src_snapshot_id:     rebuild volume from this snapshot id if
                                    it is specified.
        :param src_volume_uuid:     clone from this volume if it is specified.
        :param src_target_name:     create volume from the lun if the
                                    `src_target_name` and the `src_lun_id` are
                                    specified.
        :param src_lun_id:          create volume from the lun if the
                                    `src_target_name` and the `src_lun_id` are
                                    specified.
        :param src_export_id:       create volume from the NFS file if the
                                    `src_export_id` and the `src_inode_id` are
                                    specified.
        :param src_inode_id:        create volume from the NFS file if the
                                    `src_export_id` and the `src_inode_id` are
                                    specified.
        :param clone_before_create: how to create volume from the src lun or the
                                    src file.
                                    If the `src_target_name` and the
                                    `src_lun_id` are specified, will clone from
                                    the src lun if it is True, otherwise move
                                    the src lun to ell target.
                                    If the `src_export_id` and the
                                    `src_inode_id` are specified, will convert
                                    the src file copy to lun if it is True,
                                    otherwise convert the src file itself to lun.
        :param ret_diff_size:       return the volume `diff_size` and `unique_size` field value
                                    if it is True, default is False.
        :param ret_extra_lun_attrs: return some extra attribute values of lun
                                    if it is specified. It must be a `list`
                                    or `tuple`.
        :param preferred_cid:
        :param single_access:       tell underlying storage whether this lun
                                    can be accessed by multiple connection
        :param allowed_initiators:  tell underlying storage which initiators
                                    can be allowed to access this lun.
                                    Default is None, which indicates Open Access.
                                    In vhost mode, that is set after creating lun, drive by vhost io permission.
                                    In non-vhost mode, that is set in creating lun, drive by iscsi allowed initiator.
        :param vendor:              Vendor of disk when the LUN is used as a disk.
        :param product:             Product of disk when the LUN is used as a disk.
        :param serial:              Serial of disk when the LUN is used as a disk.
        :param wwn:                 WWN of disk when the LUN is used as a disk.

        :param encryption_algorithm: the encryption algorithm for the volume. Default is not encrypted(PLAINTEXT).

        :return:                    dict, contains the keys: `target_name`,
                                    `lun_id`, `volume_uuid`, `create_time`,
                                    `size_in_byte`, `path`, `lun_volume_id`,
                                    [`diff_size`, `unique_size`] if ret_diff_size is True
        """
        StoragePolicy.check_settings(self.storage_policy.settings)
        encryption_algorithm = encryption_algorithm or constants.VOLUME_ENCRYPTION_ALG_PLAINTEXT

        if [
            size_in_byte,
            src_snapshot_id,
            src_volume_uuid,
            all([src_target_name, src_lun_id]) or None,
            all([src_export_id, src_inode_id]) or None,
        ].count(None) != 4:
            raise ValueError(
                "You must specify one and only specify one of `size_in_byte` "
                "or `src_snapshot_id` or `src_volume_uuid` or "
                "(`src_target_name` and `src_lun_id`) or "
                "(`src_export_id` and `src_inode_id`)."
            )

        if src_volume_uuid:
            src_froze_lun_record = self.get_froze_lun_record(src_volume_uuid)
            if src_froze_lun_record is None:
                raise ResourceException(
                    "The lun record of src volume({}) is not found in the storage policy({}).".format(
                        src_volume_uuid, self.storage_policy.uuid
                    ),
                    py_error.STORAGE_POLICY_LUN_RECORD_NOT_FOUND,
                )
            kwargs = {
                "src_lun_id": src_froze_lun_record["lun_id"],
                "src_pool_name": src_froze_lun_record["target_name"],
            }
        elif src_snapshot_id:
            kwargs = {"src_snapshot_id": src_snapshot_id}
        elif src_target_name and src_lun_id:
            kwargs = {
                "src_lun_id": src_lun_id,
                "src_pool_name": src_target_name,
                "clone_before_create": clone_before_create,
            }
        elif src_export_id and src_inode_id:
            kwargs = {
                "src_export_id": src_export_id,
                "src_inode_id": src_inode_id,
                "clone_before_create": clone_before_create,
            }
        else:
            if not self.is_valid_volume_size(size_in_byte):
                raise ResourceException(
                    "`{}` is a invalid size for lun in the storage policy({}).".format(
                        size_in_byte, self.storage_policy.uuid
                    ),
                    py_error.STORAGE_POLICY_INVALID_LUN_SIZE,
                )
            kwargs = {"size_in_byte": size_in_byte}

        if isinstance(ret_extra_lun_attrs, tuple | list):
            kwargs["ret_extra_lun_attrs"] = ret_extra_lun_attrs

        kwargs["ret_diff_size"] = ret_diff_size
        if not self.storage_policy.thin_provision:
            kwargs["preferred_cid"] = preferred_cid

        kwargs["allowed_initiators"] = allowed_initiators
        kwargs["single_access"] = single_access

        kwargs["prioritized"] = prioritized
        kwargs["encrypt_method"] = volume_enc_utils.convert_elf_volume_encryption_algorithm_to_zbs(encryption_algorithm)

        kwargs["labels"] = volume_properties_util.generate_lun_label_of_volume_properties(vendor, product, serial, wwn)

        lun = self._create_lun(volume_uuid, **kwargs)

        if platform.boost_enabled() and allowed_initiators is not None:
            # When creating a volume in the vm creation / clone,
            # allowed_initiators is None
            #
            # This is the real iscsi allowed initiator setting,
            # which is derived by vhost io permission.
            try:
                self._update_iscsi_allowed_initiators(volume_uuid, lun, allowed_initiators, single_access)
            except Exception as e:
                try:
                    self.delete_lun(volume_uuid)
                except Exception as delete_lun_err:
                    logging.exception("Delete lun for volume {} failed: {}".format(volume_uuid, delete_lun_err))

                raise e

        if new_skip_all_zero_first_write:
            self._ensure_new_skip_all_zero_first_write(
                volume_uuid=volume_uuid, lun=lun, new_skip_all_zero_first_write=True
            )

        return lun

    def delete_lun(self, volume_uuid, delete_permanently=True):
        """Delete volume's lun."""
        froze_lun_record = self.get_froze_lun_record(volume_uuid)
        if froze_lun_record:
            try:
                # # We should clear initiator whitelist first,
                # or zbs will block delete operation when whitelist is not empty.
                self.iscsi_client.lun_update(
                    froze_lun_record["lun_id"], froze_lun_record["target_name"], new_allowed_initiators=""
                )
                self.iscsi_client.lun_delete(
                    froze_lun_record["lun_id"], froze_lun_record["target_name"], delete_permanently=delete_permanently
                )
            except ZException as e:
                if e.ec_name == zbs_error.ErrorCode.Name(zbs_error.ENotFound):
                    self.unfreeze_lun_record(volume_uuid)
                else:
                    raise
            else:
                self.unfreeze_lun_record(volume_uuid)
        return froze_lun_record

    def rollback_lun(self, volume_uuid, snapshot_id):
        """Rollback volume's lun to the specified snapshot"""
        froze_lun_record = self.get_froze_lun_record(volume_uuid)
        if froze_lun_record is None:
            raise ResourceException(
                "The lun record of volume({}) is not found in the storage policy({}).".format(
                    volume_uuid, self.storage_policy.uuid
                ),
                py_error.STORAGE_POLICY_LUN_RECORD_NOT_FOUND,
            )

        return self.iscsi_client.snapshot_rollback(
            target_name=froze_lun_record["target_name"], lun_id=froze_lun_record["lun_id"], snapshot_id=snapshot_id
        )

    def ensure_lun_config(self, volume_uuid):
        """Ensure that lun is the config of the target storage policy"""
        froze_lun_record = self.get_froze_lun_record(volume_uuid)
        if froze_lun_record is None:
            raise ResourceException(
                "Not Found the lun record of volume({}).".format(volume_uuid),
                py_error.STORAGE_POLICY_LUN_RECORD_NOT_FOUND,
            )

        exist_lun = self.iscsi_client.lun_get(
            pool_name=froze_lun_record["target_name"], lun_id=froze_lun_record["lun_id"]
        )

        kwargs = self.storage_policy.gen_update_parameters_from_lun(exist_lun)

        if kwargs:
            logging.info("update volume {} lun to {}".format(volume_uuid, kwargs))

            # The reservation here is that lun_update can be any replica_num and provision,
            # but the actual requirements must be:
            # - a larger replica_num to be updated
            # - thin provision to thick provision
            # which is guaranteed by the upper level caller
            return self.iscsi_client.lun_update(
                froze_lun_record["lun_id"], pool_name=froze_lun_record["target_name"], **kwargs
            )


class FileToLunHelper:
    def __init__(self, iscsi_client=None, nfs_client=None):
        self.iscsi_client = zbs_iscsi.ZbsClientWrapper(iscsi_client)
        self.nfs_client = nfs_client or ZbsNFS("")

    @contextmanager
    def get_inode_path(self, src_export_id, src_inode_id, clone_to_tmp=True):
        copy_inode = None
        try:
            inodes = self.nfs_client.get_parent_hierarchy_inodes(inode_id=src_inode_id, include_current=True)
            if clone_to_tmp:
                src_inode = inodes[0]
                inodes[0] = copy_inode = self.nfs_client.inode_create(
                    parent_id=src_inode.parent_id,
                    name="copy_{}_{}".format(src_inode.id, str(uuid.uuid4())),
                    nfs_type=NFS_TYPE_FILE,
                    src_inode_id=src_inode.id,
                )
            inode_path = "/".join(reversed([inode.name for inode in inodes]))
            export_name = self.nfs_client.pool_show_by_id(src_export_id).name
            full_inode_path = "/{}/{}".format(export_name, inode_path)
            yield full_inode_path
        except Exception:
            if copy_inode is not None:
                logging.warning("LUN create failed, try to delete the temporary file({}). ".format(copy_inode.name))

                exc_type, exc_value, exc_traceback = sys.exc_info()
                try:
                    self.nfs_client.inode_delete(parent_id=copy_inode.parent_id, name=copy_inode.name)
                except Exception:
                    logging.warning("Delete the temporary file({}) failed.".format(copy_inode.name))
                    raise exc_type(exc_value).with_traceback(exc_traceback)
            raise

    def create_lun_from_file(self, lun_id, lun_name, pool_name, src_export_id, src_inode_id, clone_before_create=True):
        """
        Warning: it not supports repeat conversions(i.e.`clone_before_create` is
        False) from the same src inode.
        """
        with self.get_inode_path(
            src_export_id=src_export_id, src_inode_id=src_inode_id, clone_to_tmp=clone_before_create
        ) as inode_path:
            return self.iscsi_client.lun_create(
                lun_id=lun_id, lun_name=lun_name, pool_name=pool_name, inode_path=inode_path
            )


class _KvmISCSIVolumeManager(_BaseISCSIVolumeManager):
    pass


class _ElfISCSIVolumeManager(_BaseISCSIVolumeManager):
    def __init__(self, storage_policy, iscsi_client=None):
        super().__init__(storage_policy, iscsi_client)
        self.storage_cluster_uuid = self.iscsi_client.storage_cluster_uuid

    @classmethod
    def ensure_index(cls):
        from pymongo import HASHED

        try:
            indexes = cls.resources_db.iscsi_paths.index_information()
        except errors.OperationFailure:
            indexes = {}

        if "volume_uuid_1" not in indexes:
            cls.resources_db.iscsi_paths.create_index("volume_uuid", background=True, unique=True)

        if "path_hash" not in indexes:
            cls.resources_db.iscsi_paths.create_index([("path", HASHED)])

    def _freeze_lun_record(self, target_name, lun_id, volume_uuid):
        criteria = {"volume_uuid": volume_uuid}
        iscsi_paths_col = self.resources_db.iscsi_paths
        create_time = int(time.time())

        iscsi_paths_col.update_one(
            criteria,
            {
                "$setOnInsert": {
                    "target_name": target_name,
                    "lun_id": lun_id,
                    "create_time": create_time,
                    "storage_cluster_uuid": self.storage_cluster_uuid,
                }
            },
            upsert=True,
        )

        return {"target_name": target_name, "lun_id": lun_id, "volume_uuid": volume_uuid, "create_time": create_time}

    def create_lun(
        self,
        volume_uuid,
        size_in_byte=None,
        src_snapshot_id=None,
        src_volume_uuid=None,
        src_target_name=None,
        src_lun_id=None,
        src_export_id=None,
        src_inode_id=None,
        clone_before_create=True,
        ret_diff_size=False,
        ret_extra_lun_attrs=None,
        preferred_cid=None,
        allowed_initiators="",
        single_access=True,
        prioritized=None,
        new_skip_all_zero_first_write=False,
        encryption_algorithm=None,
        vendor="",
        product="",
        serial="",
        wwn="",
    ):
        volume_uuid = volume_uuid or str(uuid.uuid4())
        StoragePolicy.check_settings(self.storage_policy.settings)
        encryption_algorithm = encryption_algorithm or constants.VOLUME_ENCRYPTION_ALG_PLAINTEXT

        if [
            size_in_byte,
            src_snapshot_id,
            src_volume_uuid,
            all([src_target_name, src_lun_id]) or None,
        ].count(None) != 3:
            raise ValueError(
                "You must specify one and only specify one of `size_in_byte` "
                "or `src_snapshot_id` or `src_volume_uuid` or (`src_target_name` and `src_lun_id`)."
            )

        if any((src_export_id, src_inode_id)):
            raise ValueError("Separated mode not support `src_export_id` or `src_inode_id`.")

        if src_volume_uuid:
            src_froze_lun_record = self.get_froze_lun_record(src_volume_uuid)
            kwargs = {
                "src_lun_id": src_froze_lun_record["lun_id"],
                "src_pool_name": src_froze_lun_record["target_name"],
            }
        elif src_snapshot_id:
            kwargs = {"src_snapshot_id": src_snapshot_id}
        elif src_target_name and src_lun_id:
            kwargs = {"src_lun_id": src_lun_id, "src_pool_name": src_target_name}
        else:
            if not self.is_valid_volume_size(size_in_byte):
                raise ResourceException(
                    "`{}` is a invalid size for lun in the storage policy({}).".format(
                        size_in_byte, self.storage_policy.uuid
                    ),
                    py_error.STORAGE_POLICY_INVALID_LUN_SIZE,
                )
            kwargs = {"size": size_in_byte}

        kwargs.update(self.storage_policy.gen_target_create_by_requirement_parameters())

        if not self.storage_policy.thin_provision:
            kwargs["preferred_cid"] = preferred_cid

        kwargs["allowed_initiators"] = allowed_initiators
        kwargs["single_access"] = single_access

        # Target requirement
        kwargs["external_use"] = True
        kwargs["labels"] = {b"SMTXELF": bytes(cluster.get_cluster_id(), "utf-8")}
        kwargs["adaptive_iqn_whitelist"] = True
        kwargs["prioritized"] = prioritized
        # encryption setting
        kwargs["encrypt_method"] = volume_enc_utils.convert_elf_volume_encryption_algorithm_to_zbs(encryption_algorithm)
        volume_properties = volume_properties_util.generate_lun_label_of_volume_properties(vendor, product, serial, wwn)
        kwargs["lun_labels"] = {bytes(k, "utf-8"): bytes(v, "utf-8") for k, v in volume_properties.items()}

        try:
            self.iscsi_client.lun_create_by_target_requirement(secondary_id=volume_uuid, **kwargs)
        except ZException as e:
            if e.ec_name == zbs_error.ErrorCode.Name(zbs_error.ECipherNotSupport):
                raise ResourceException(
                    "Failed to create volume because of the encryption is not enabled",
                    py_error.VOLUME_STORAGE_SIDE_ENCRYPTION_UNSUPPORTED,
                )

            if e.ec_name != zbs_error.ErrorCode.Name(zbs_error.EDuplicate):
                raise

        target, lun = self.iscsi_client.target_and_lun_get_by_secondary_id(volume_uuid)

        froze_lun_record = {}
        try:
            froze_lun_record = self._freeze_lun_record(target.name, lun.lun_id, volume_uuid)
        except Exception as e:
            self.iscsi_client.lun_delete(lun.lun_id, target.name)
            raise ResourceException(
                "Failed to record target({}) lun({}) for volume({}). Error is {}".format(
                    target.name, lun.lun_id, volume_uuid, str(e)
                ),
                py_error.STORAGE_POLICY_LUN_RECORD_FREEZE_FAILURE,
            )

        if new_skip_all_zero_first_write:
            self._ensure_new_skip_all_zero_first_write(
                volume_uuid=volume_uuid, lun=froze_lun_record, new_skip_all_zero_first_write=True
            )

        return self._get_return_data_from_lun(lun, froze_lun_record, ret_diff_size, ret_extra_lun_attrs)


if platform.is_in_kvm_or_san():

    class ISCSIVolumeManager(_KvmISCSIVolumeManager):
        pass

else:

    class ISCSIVolumeManager(_ElfISCSIVolumeManager):
        pass
