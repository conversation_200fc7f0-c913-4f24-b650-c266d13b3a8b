# Copyright (c) 2013-2016, SMARTX
# All rights reserved.
import copy
import logging

from common.config.constant import (
    DEFAULT_ZBS_IMAGE_POOL_NAME,
    DEFAULT_ZBS_VOLUME_POOL_NAME,
    DEFAULT_ZBS_VOLUME_TEMPLATE_POOL_NAME,
)
from common.config.resources import RESOURCE_HIDDEN, RESOURCE_IN_USE
from common.mongo.db import mongodb
from smartx_app.elf.common import constants as elf_common_constants
from smartx_app.elf.common.code import API_VOLUME_CLONE, API_VOLUME_CREATE, API_VOLUME_DELETE, API_VOLUME_EDIT
from smartx_app.elf.common.constants import (
    DEFAULT_VM_VOLUME_PATH,
    ISCSI_PATH_PREFIX,
    NFS_LOCATION_PREFIX,
)
from smartx_app.elf.common.events.volume_events_wrapper import VolumeEventWrapper
from smartx_app.elf.common.resource_query.volume_querier import CommonVolumeQuerier, VolumeQuerier
from smartx_app.elf.common.resources.base import ResourceException
from smartx_app.elf.common.resources.storage_policy import StoragePolicy
from smartx_app.elf.common.resources.volume import ISCSIVolume, NFSVolume, Volume
from smartx_app.elf.common.resources.volume_snapshot import Snapshot
from smartx_app.elf.common.utils import disk as disk_util
from smartx_app.elf.common.utils import iscsi, zbs_iscsi
from smartx_app.elf.common.utils import storage_cluster as sc_utils
from smartx_app.elf.common.utils import volume_properties as volume_properties_util
from smartx_app.elf.common.utils.resource import byte_to_GiB, ensure_diff_size
from smartx_app.elf.job_center.constants import (
    DISK,
    KVM_VM,
    KVM_VOL,
    KVM_VOL_ISCSI,
    KVM_VOL_ISCSI_SNAPSHOT,
    KVM_VOL_ISCSI_TEMPLATE,
    KVM_VOL_SNAPSHOT,
    KVM_VOL_SUPER,
    KVM_VOL_TEMPLATE,
)
from smartx_proto.errors import pyerror_pb2 as py_error
from zbs.nfs.client import ZbsNFS


class VolumeWrapper:
    _VOLUMES = {KVM_VOL: NFSVolume, KVM_VOL_ISCSI: ISCSIVolume}
    _VOL_SNAPSHOT_TYPES = {KVM_VOL: KVM_VOL_SNAPSHOT, KVM_VOL_ISCSI: KVM_VOL_ISCSI_SNAPSHOT}
    db = mongodb.resources
    nfs_client = ZbsNFS()

    def __init__(self, volume):
        self.volume = volume

    @staticmethod
    def fit_storage_policy(volume_doc):
        if volume_doc:
            if volume_doc.get("storage_policy_uuid", None) is None:
                volume_doc["storage_policy_uuid"] = StoragePolicy.DEFAULT_STORAGE_POLICY_UUID

            # compatible with the previous nfs volume
            if "size_in_byte" not in volume_doc and "size" in volume_doc:
                volume_doc["size_in_byte"] = volume_doc["size"] * 2**30

        return volume_doc

    @staticmethod
    def submit_job(description, resources, event=None):
        from job_center.handler.leader.workers import job_submit

        return {"job_id": job_submit(user="", description=description, resources=resources, event=event)}

    @classmethod
    def load(cls, volume_uuid):
        volume = Volume.load(volume_uuid)
        return cls(volume)

    @classmethod
    def new(
        cls,
        name,
        size_in_byte,
        description,
        this_volume_uuid=None,
        volume_type=KVM_VOL,
        storage_policy_uuid=None,
        sharing=False,
        src_lun_id=None,
        src_target_name=None,
        src_inode_id=None,
        src_export_id=None,
        src_volume_id=None,
        new_size_in_byte=None,
        clone_before_create=None,
        allowed_initiators=None,
        single_access=False,
        storage_cluster_uuid=None,
        resident_in_cache=False,
        resident_in_cache_percentage=0,
        encryption_algorithm=None,
        vendor=None,
        product=None,
        serial=None,
        wwn=None,
    ):
        if not storage_policy_uuid:
            storage_policy_uuid = StoragePolicy.DEFAULT_STORAGE_POLICY_UUID

        if (src_export_id and src_inode_id) or (src_lun_id and src_target_name):
            # If the volume is created from a storage object(iscsi lun or NFS file),
            # we ignore the specified encryption algorithm.
            encryption_algorithm = elf_common_constants.TEMPORARY_VOLUME_ENCRYPTION_ALG_STORAGE_OBJECT
        elif encryption_algorithm is None:
            encryption_algorithm = elf_common_constants.VOLUME_ENCRYPTION_ALG_PLAINTEXT

        volume = cls._VOLUMES[volume_type](
            name=name,
            this_volume_uuid=this_volume_uuid,
            size_in_byte=size_in_byte,
            description=description,
            storage_policy_uuid=storage_policy_uuid,
            sharing=sharing,
            src_lun_id=src_lun_id,
            src_target_name=src_target_name,
            src_inode_id=src_inode_id,
            src_export_id=src_export_id,
            new_size_in_byte=new_size_in_byte,
            clone_before_create=clone_before_create,
            zbs_volume_id=src_volume_id,
            allowed_initiators=allowed_initiators,
            single_access=single_access,
            storage_cluster_uuid=storage_cluster_uuid,
            resident_in_cache=resident_in_cache,
            resident_in_cache_percentage=resident_in_cache_percentage,
            encryption_algorithm=encryption_algorithm,
        )

        volume.generate_properties()
        if vendor is not None:
            volume.vendor = vendor
        if product is not None:
            volume.product = product
        if serial is not None:
            volume.serial = serial
        if wwn is not None:
            volume.wwn = wwn

        return cls(volume)

    @classmethod
    def query_from_db(cls, volume_uuid=None, paths=None):
        query = {
            "type": {
                # Use all types to compatible with the
                # old nfs volume api. It's a bit ugly.
                "$in": list(cls._VOLUMES.keys())
            },
            "resource_state": RESOURCE_IN_USE,
        }

        if volume_uuid is not None:
            query["uuid"] = volume_uuid

        if paths is not None:
            query["path"] = {"$in": paths}

        cursor = cls.db.resource.find(query, {"_id": 0})
        return [cls.fit_storage_policy(doc) for doc in cursor]

    @classmethod
    def query_from_db_by_uuids(cls, uuids):
        query = {
            # Use all types to compatible with the old nfs volume api.
            "type": {"$in": list(cls._VOLUMES.keys())},
            "uuid": {"$in": uuids},
            "resource_state": RESOURCE_IN_USE,
        }

        cursor = cls.db.resource.find(query, {"_id": 0})
        return [cls.fit_storage_policy(doc) for doc in cursor]

    @classmethod
    def _query_resource_from_db(cls, query=None, project=None):
        current_query = {"type": KVM_VM, "resource_state": RESOURCE_IN_USE}
        if query:
            current_query.update(query)

        current_project = {"_id": 0}
        if project:
            current_project.update(project)

        return cls.db.resource.find(current_query, current_project)

    @classmethod
    def fetch_all_iscsi_volumes(cls, query_cond=None, fields_filter=None):
        q = copy.deepcopy(query_cond) if query_cond else {}
        q.update({"type": KVM_VOL_ISCSI, "resource_state": RESOURCE_IN_USE})

        f = copy.deepcopy(fields_filter) if fields_filter else {}
        f.update({"_id": 0})

        return cls.db.resource.find(q, f)

    @classmethod
    def page_query(
        cls, count, skip_page, sort_criteria, filter_criteria, projection, start_uuid, end_uuid, skip_to_last
    ):
        # Will set to the default storage policy by the
        # method:`fit_storage_policy` if the query will not return the
        # `storage_policy_uuid` field.
        if projection:
            projection += ",storage_policy_uuid"

        data = VolumeQuerier().page_query(
            count=count,
            skip_page=skip_page,
            sort_criteria=sort_criteria,
            filter_criteria=filter_criteria,
            projection=projection,
            start_uuid=start_uuid,
            end_uuid=end_uuid,
            skip_to_last=skip_to_last,
        )
        list(map(cls.fit_storage_policy, data["entities"]))

        return data

    @classmethod
    def search(cls, text, limit=None, filter_criteria=None):
        volume_querier = CommonVolumeQuerier() if filter_criteria else VolumeQuerier()
        data = volume_querier.search(text, limit, filter_criteria)

        if "mounting" in data["filter_criteria"]:
            data["entities"] = [x for x in data["entities"] if (x["mounting"] == data["filter_criteria"]["mounting"])]

        list(map(cls.fit_storage_policy, data["entities"]))

        return data

    @classmethod
    def get(cls, volume_uuid):
        return cls.fit_storage_policy(VolumeQuerier().get(volume_uuid))

    @classmethod
    def batch_query(cls, volume_uuid_list, projection=None, querier=None):
        if not querier:
            querier = VolumeQuerier()

        if not isinstance(volume_uuid_list, list | set):
            volume_uuid_list = set(volume_uuid_list)
        volumes = querier.query({"uuid": {"$in": volume_uuid_list}}, top=len(volume_uuid_list), projection=projection)
        list(map(cls.fit_storage_policy, volumes))
        return volumes

    @classmethod
    def batch_query_by_path(cls, volume_paths, projection=None, batch_size=1000):
        volumes = []
        for i in range(0, len(volume_paths), batch_size):
            part_paths = volume_paths[i : i + batch_size]
            volumes.extend(
                VolumeQuerier().query(
                    {"path": {"$in": part_paths}, "type": {"$in": [KVM_VOL, KVM_VOL_ISCSI]}},
                    projection=projection,
                    top=len(part_paths),
                )
            )
        list(map(cls.fit_storage_policy, volumes))

        return volumes

    @classmethod
    def query_iscsi_volume_and_template_by_uuids(cls, resource_uuids, include_hidden=False):
        # Since `VolumeQuerier` will override "type" in mongo query statement,
        # we use mongodb directly to avoid this.
        resource_state_filter = {"$in": [RESOURCE_IN_USE, RESOURCE_HIDDEN]} if include_hidden else RESOURCE_IN_USE
        volumes = list(
            cls.db.resource.find(
                {
                    "resource_state": resource_state_filter,
                    "uuid": {"$in": resource_uuids},
                    "type": {"$in": [KVM_VOL_ISCSI, KVM_VOL_ISCSI_TEMPLATE]},
                }
            )
        )
        list(map(cls.fit_storage_policy, volumes))

        return volumes

    @classmethod
    def convert_local_path_to_zbs_path(cls, path):
        if path is None:
            logging.error("Invalid local path: {}".format(path))
            return None

        # iscsi path
        if path.startswith(ISCSI_PATH_PREFIX):
            return path[len(ISCSI_PATH_PREFIX) :]

        # nfs path
        real_path = path[len(NFS_LOCATION_PREFIX) + 1 :]
        slash_index = real_path.find("/")

        if real_path[:slash_index] == "images":
            return "/{}/{}".format(DEFAULT_ZBS_IMAGE_POOL_NAME, real_path[slash_index + 1 :])
        elif real_path[:slash_index] == "volumes":
            return "/{}/{}".format(DEFAULT_ZBS_VOLUME_POOL_NAME, real_path[slash_index + 1 :])
        else:
            # in version 3.1.0, it only for nfs volume template
            return path

    @classmethod
    def convert_zbs_path_to_local_path(cls, path):
        if path is None:
            logging.error("Invalid zbs path: {}".format(path))
            return None

        if path.startswith("/"):
            # nfs path here
            _, pool, path = path.split("/")

            if DEFAULT_ZBS_IMAGE_POOL_NAME == pool:
                return "{}/images/{}".format(NFS_LOCATION_PREFIX, path)
            else:
                return "{}/volumes/{}".format(NFS_LOCATION_PREFIX, path)
        else:
            # iscsi path here
            return ISCSI_PATH_PREFIX + path

    @classmethod
    def get_mountable_by_paths(cls, paths, vm_uuid=None, restrict=True):
        if not paths:
            return []
        vols_exist = Volume.load_by_paths(paths)
        if restrict:
            paths_found = [x.path for x in vols_exist]
            paths_not_found = [x for x in paths if x not in paths_found]
            if paths_not_found:
                # Raise a `ResourceException` if we can't find
                # volumes by paths
                raise ResourceException(
                    "Volumes({}) are not exist.".format(", ".join(paths_not_found)), py_error.VOLUME_NOT_FOUND
                )
        non_shared_vol_paths = [x.path for x in vols_exist if not x.sharing]
        vms_cursor = mongodb.resources.resource.find(
            {
                "type": KVM_VM,
                "resource_state": RESOURCE_IN_USE,
                "uuid": {"$ne": vm_uuid},
                "disks.path": {"$in": non_shared_vol_paths},
            },
            {"_id": 0, "uuid": 1, "disks.path": 1},
        )
        paths_mounted = {}
        for vm_vol in vms_cursor:
            for disk in vm_vol["disks"]:
                disk_path = disk["path"]
                if disk_path in non_shared_vol_paths:
                    vm_uuids = paths_mounted.setdefault(disk_path, [])
                    vm_uuids.append(vm_vol["uuid"])
        if paths_mounted:
            # Raise a `ResourceException` if the non-shared volume is
            # already mounted by other vm
            logging.warning(
                "Volumes mounted by other vms are %s.",
                ", ".join(["{}, ({})".format(x, ",".join(y)) for x, y in list(paths_mounted.items())]),
            )
            raise ResourceException(
                "Volumes({}) are already mounted by other vms.".format(", ".join(list(paths_mounted.keys()))),
                py_error.VOLUME_MOUNT_IN_OTHER_VMS,
            )
        return vols_exist

    @classmethod
    def check_shared_volumes_mountable_for_vm(cls, vm_uuid, shared_volumes):
        # Due to some history issue, the legacy shared volumes used to be
        # open access for its allow list, this is not allowed anymore after
        # we support allow list for shared volumes. The allowlist of existing
        # shared volumes should be updated to our expected state on cluster
        # upgrade, those who failed to get updated will not be allowed to mount
        # to other VMs for safety concern.
        def _generate_vol_storage_to_zbs_client_map(volumes):
            res = {}
            for vol in volumes:
                if vol.storage_cluster_uuid not in res:
                    res[vol.storage_cluster_uuid] = zbs_iscsi.ZbsClientWrapper(
                        sc_utils.init_zbs_iscsi_client(vol.storage_cluster_uuid)
                    )

            return res

        def _get_new_mounted_volumes(_vm_uuid, _volumes):
            vm_cursor = cls._query_resource_from_db(
                query={"uuid": _vm_uuid},
                project={"_id": 0, "disks.volume_uuid": 1, "disks.type": 1},
            )

            vm = list(vm_cursor)
            if not vm:
                # Only one condition could happen: VM is being created,
                # in such case, we should check all the shared volumes.
                return _volumes

            existing_volumes = [disk["volume_uuid"] for disk in vm[0]["disks"] if disk["type"] == DISK]

            return [volume for volume in _volumes if volume.uuid not in existing_volumes]

        if not shared_volumes:
            return

        new_mounted_volumes = _get_new_mounted_volumes(vm_uuid, shared_volumes)
        if not new_mounted_volumes:
            return

        volstorage2client_map = _generate_vol_storage_to_zbs_client_map(new_mounted_volumes)

        for volume in new_mounted_volumes:
            if iscsi.is_lun_open_access(
                disk_util.get_disk_iqn(volume.path),
                volstorage2client_map[volume.storage_cluster_uuid],
            ):
                raise ResourceException(
                    "Shared volume with open access is not allowed to mount",
                    py_error.MOUNT_OPEN_ACCESS_SHARED_DISK,
                )

    @classmethod
    def get_mountable_by_volume_uuid_list(cls, volume_uuid_list, vm_uuid=None, restrict=True):
        if not volume_uuid_list:
            return []

        vols_exist = Volume.load_multi(volume_uuid_list, allow_partial=True)
        if restrict:
            uuids_found = [x.uuid for x in vols_exist]
            uuids_not_found = [x for x in volume_uuid_list if x not in uuids_found]
            if uuids_not_found:
                # Raise a `ResourceException` if we can't find
                # volumes by paths
                raise ResourceException(
                    "Volumes({}) are not exist.".format(", ".join(uuids_not_found)), py_error.VOLUME_NOT_FOUND
                )

        non_shared_vol_uuids = [x.uuid for x in vols_exist if not x.sharing]

        vms_cursor = cls._query_resource_from_db(
            query={
                "uuid": {"$ne": vm_uuid},
                "disks.volume_uuid": {"$in": non_shared_vol_uuids},
            },
            project={"_id": 0, "uuid": 1, "disks.volume_uuid": 1, "disks.type": 1},
        )

        volume_uuid_mounted = {}
        for vm in vms_cursor:
            for disk in vm["disks"]:
                if disk["type"] == "disk" and disk["volume_uuid"] in non_shared_vol_uuids:
                    volume_uuid_mounted.setdefault(disk["volume_uuid"], []).append(vm["uuid"])
        if volume_uuid_mounted:
            # Raise a `ResourceException` if the non-shared volume is
            # already mounted by other vm
            logging.warning(
                "Volumes mounted by other vms are %s.",
                ", ".join(["{}, ({})".format(x, ",".join(y)) for x, y in list(volume_uuid_mounted.items())]),
            )
            raise ResourceException(
                "Volumes({}) are already mounted by other vms.".format(", ".join(list(volume_uuid_mounted.keys()))),
                py_error.VOLUME_MOUNT_IN_OTHER_VMS,
            )

        cls.check_shared_volumes_mountable_for_vm(vm_uuid, [vol for vol in vols_exist if vol.sharing])

        return vols_exist

    def generate_resources_for_creation(self):
        return {self.volume.uuid: self.volume.dumps()}

    def submit_create(self, user=None):
        event = VolumeEventWrapper(
            user=user, uuid=self.volume.uuid, name=self.volume.name, volume_type=self.volume.type
        ).event_volume_create(self.volume)
        return self.submit_job(API_VOLUME_CREATE, self.generate_resources_for_creation(), event=event)

    def submit_delete(self, include_snapshots=True, delete_permanently=True, user=None):
        vol_json = self.volume.delete(delete_permanently=delete_permanently)

        resources = {self.volume.uuid: vol_json}
        event = VolumeEventWrapper(
            user=user, uuid=self.volume.uuid, name=self.volume.name, volume_type=self.volume.type
        ).event_volume_delete()
        if include_snapshots:
            snapshots = Snapshot.load_snapshots_by_volume_uuid(self.volume.uuid, is_vm_snapshot=False)
            for snapshot in snapshots:
                resources[snapshot.uuid] = snapshot.delete()

        return self.submit_job(API_VOLUME_DELETE, resources, event=event)

    def submit_clone(self, name, description, this_volume_uuid=None, user=None, resident_in_cache=False):
        from smartx_app.elf.common.utils import validator

        new_volume = self.volume.clone(
            name=name, this_volume_uuid=this_volume_uuid, description=description, resident_in_cache=resident_in_cache
        )
        check_items = {validator.ValidatorChain.check.resident_in_cache: {}}
        if this_volume_uuid:
            check_items.update({validator.ValidatorChain.check.uuid_uniqueness: {}})

        validator.ValidatorChain(new_volume).validate(check_items)

        event = VolumeEventWrapper(
            user=user, uuid=new_volume["uuid"], name=new_volume["name"], volume_type=new_volume.get("type")
        ).event_volume_clone(self.volume.name)
        return self.submit_job(API_VOLUME_CLONE, {new_volume["uuid"]: new_volume}, event=event)

    def submit_update(self, name, description, size_in_byte, storage_policy_uuid, user=None, resident_in_cache=None):
        from smartx_app.elf.common.utils import validator

        vol_json = self.volume.dumps()
        existed_vol_json = copy.deepcopy(vol_json)
        vol_json["name"] = name
        vol_json["description"] = description
        if size_in_byte:
            vol_json["size_in_byte"] = size_in_byte
        if resident_in_cache is not None:
            vol_json["resident_in_cache"] = resident_in_cache

        check_items = {}
        if storage_policy_uuid:
            check_items[validator.ValidatorChain.check.storage_policy] = storage_policy_uuid
            vol_json["storage_policy_uuid"] = storage_policy_uuid
        if resident_in_cache:
            check_items[validator.ValidatorChain.check.resident_in_cache] = {}

        validator.ValidatorChain(self).validate(check_items)

        event = VolumeEventWrapper(
            user=user,
            uuid=self.volume.uuid,
            name=self.volume.name,
            volume_type=self.volume.type,
            storage_policy_uuid=self.volume.storage_policy_uuid,
        ).event_volume_update(existed_vol_json, name, description, size_in_byte, storage_policy_uuid)

        return self.submit_job(API_VOLUME_EDIT, {self.volume.uuid: vol_json}, event=event)

    @classmethod
    def _iscsi_vol_as_template(cls, vol):
        iscsi_client = zbs_iscsi.ZbsClientWrapper(sc_utils.init_zbs_iscsi_client(vol.get("storage_cluster_uuid")))
        volume = iscsi_client.volume_show_by_id(vol["zbs_volume_id"]).volume
        unique_size = volume.unique_size
        return {
            "_id": vol["_id"],
            "src_export_id": vol["src_export_id"],
            "src_inode_id": vol["src_inode_id"],
            "target_name": vol["target_name"],
            "lun_id": vol["lun_id"],
            "create_time": vol["create_time"],
            "size": vol["size_in_byte"],
            "uuid": vol["uuid"],
            "zbs_volume_id": vol["zbs_volume_id"],
            "storage_policy_uuid": vol["storage_policy_uuid"],
            "storage_cluster_uuid": vol.get("storage_cluster_uuid"),
            "type": KVM_VOL_ISCSI_TEMPLATE,
            "status": vol["status"],
            "description": vol["description"],
            "src_target_name": vol["src_target_name"],
            "src_lun_id": vol["src_lun_id"],
            "alloc_even": False,
            "path": vol["path"],
            "volume_uuid": vol["volume_uuid"],
            "name": vol["name"],
            "diff_size": ensure_diff_size(unique_size),
            "unique_size": unique_size,
            "resource_state": vol["resource_state"],
            "clone_before_create": vol["clone_before_create"],
            "resident_in_cache": vol["resident_in_cache"],
            "encryption_algorithm": vol.get(
                "encryption_algorithm", elf_common_constants.VOLUME_ENCRYPTION_ALG_PLAINTEXT
            ),
        }

    @classmethod
    def _move_nfs_volume(cls, src_export_name, src_volume_id, dst_export_name, dst_name):
        export_id = cls.nfs_client.pool_show(src_export_name).id
        dst_export_id = cls.nfs_client.pool_show(dst_export_name).id[:18]
        src_inode_path = cls.nfs_client.get_inode_full_path(src_volume_id[:18], export_id)
        inode = cls.nfs_client.file_move_between_exports(
            dst_parent_id=dst_export_id, dst_name=dst_name, src_inode_path=src_inode_path
        )
        return inode

    @classmethod
    def _nfs_vol_as_template(cls, vol):
        volume = cls.nfs_client.volume_show_by_id(vol["zbs_volume_id"])
        from_export_name = vol.get("export_name") or DEFAULT_ZBS_VOLUME_POOL_NAME

        inode = cls._move_nfs_volume(
            src_export_name=from_export_name,
            src_volume_id=vol["zbs_volume_id"],
            dst_export_name=DEFAULT_ZBS_VOLUME_TEMPLATE_POOL_NAME,
            dst_name=vol["uuid"],
        )
        unique_size = volume.volume.unique_size
        return {
            "_id": vol["_id"],
            "uuid": vol["uuid"],
            "status": vol["status"],
            "size": inode.attr.size,
            "description": vol["description"],
            "diff_size": ensure_diff_size(unique_size),
            "unique_size": unique_size,
            "export_name": DEFAULT_ZBS_VOLUME_TEMPLATE_POOL_NAME,
            "name": vol["name"],
            "volume_size": int(byte_to_GiB(vol["size_in_byte"])),
            "create_time": vol["create_time"],
            "alloc_even": False,
            "zbs_volume_id": inode.volume_id,
            "resource_state": vol["resource_state"],
            "volume_uuid": vol["uuid"],
            "type": KVM_VOL_TEMPLATE,
            "from_export_name": from_export_name,
            # the `resident_in_cache` is always false for nfs volume template
            "resident_in_cache": False,
            "encryption_algorithm": elf_common_constants.VOLUME_ENCRYPTION_ALG_PLAINTEXT,
        }

    @classmethod
    def convert_to_volume_template(cls, vol_path):
        volume = cls.db.resource.find_one({"path": vol_path, "type": {"$in": [KVM_VOL, KVM_VOL_ISCSI]}})

        if not volume:
            raise ResourceException("Volume are not exist.", py_error.VOLUME_NOT_FOUND)

        if volume["sharing"]:
            raise ResourceException(
                "Shared volume({}) cannot be converted".format(volume["uuid"]),
                py_error.SHARING_VOLUME_NOT_ALLOW_CONVERTING,
            )

        _convert_func_map = {KVM_VOL_ISCSI: cls._iscsi_vol_as_template, KVM_VOL: cls._nfs_vol_as_template}
        return _convert_func_map[volume["type"]](volume)

    @classmethod
    def _convert_volume(cls, vol):
        vendor, product, serial, wwn = volume_properties_util.generate_properties(vol["uuid"])

        inode = cls._move_nfs_volume(
            src_export_name=DEFAULT_ZBS_VOLUME_TEMPLATE_POOL_NAME,
            src_volume_id=vol["zbs_volume_id"],
            dst_export_name=vol["from_export_name"],
            dst_name=vol["uuid"],
        )
        return {
            "_id": vol["_id"],
            "uuid": vol["uuid"],
            "status": vol["status"],
            "size_in_byte": vol["size"],
            "sharing": False,
            "description": vol["description"],
            "mounting": True,
            "zbs_volume_id": inode.volume_id,
            "create_time": vol["create_time"],
            "super_type": KVM_VOL_SUPER,
            "resource_state": vol["resource_state"],
            "path": DEFAULT_VM_VOLUME_PATH + "/" + vol["uuid"],
            "size": vol["volume_size"],
            "type": KVM_VOL,
            "from_export_name": None,
            "name": vol["name"],
            "volume_uuid": vol["uuid"],
            "vendor": vendor,
            "product": product,
            "serial": serial,
            "wwn": wwn,
        }

    @staticmethod
    def _convert_iscsi_volume(vol):
        vendor, product, serial, wwn = volume_properties_util.generate_properties(vol["uuid"])
        labels = volume_properties_util.generate_lun_label_of_volume_properties(vendor, product, serial, wwn)
        iscsi_client = zbs_iscsi.ZbsClientWrapper(sc_utils.init_zbs_iscsi_client(vol.get("storage_cluster_uuid")))
        iscsi_client.lun_update(pool_name=vol["target_name"], lun_id=vol["lun_id"], labels=labels)
        return {
            "_id": vol["_id"],
            "uuid": vol["uuid"],
            "src_export_id": vol["src_export_id"],
            "src_inode_id": vol["src_inode_id"],
            "target_name": vol["target_name"],
            "lun_id": vol["lun_id"],
            "create_time": vol["create_time"],
            "mounting": True,
            "zbs_volume_id": vol["zbs_volume_id"],
            "storage_policy_uuid": vol["storage_policy_uuid"],
            "storage_cluster_uuid": vol.get("storage_cluster_uuid"),
            "type": KVM_VOL_ISCSI,
            "status": vol["status"],
            "sharing": False,
            "description": vol["description"],
            "src_target_name": vol["src_target_name"],
            "src_lun_id": vol["src_lun_id"],
            "super_type": KVM_VOL_SUPER,
            "path": vol["path"],
            "volume_uuid": vol["volume_uuid"],
            "name": vol["name"],
            "size_in_byte": vol["size"],
            "resource_state": vol["resource_state"],
            "clone_before_create": vol["clone_before_create"],
            "resident_in_cache": vol["resident_in_cache"],
            "encryption_algorithm": vol.get(
                "encryption_algorithm", elf_common_constants.VOLUME_ENCRYPTION_ALG_PLAINTEXT
            ),
            "vendor": vendor,
            "product": product,
            "serial": serial,
            "wwn": wwn,
        }

    @classmethod
    def convert_to_volume(cls, volume_template_uuid):
        vol_template = cls.db.resource.find_one(
            {
                "uuid": volume_template_uuid,
                "type": {"$in": [KVM_VOL_ISCSI_TEMPLATE, KVM_VOL_TEMPLATE]},
                "resource_state": RESOURCE_IN_USE,
            }
        )

        _convert_func_map = {KVM_VOL_ISCSI_TEMPLATE: cls._convert_iscsi_volume, KVM_VOL_TEMPLATE: cls._convert_volume}

        return _convert_func_map[vol_template["type"]](vol_template)

    @staticmethod
    def get_zbs_volume_pool_name(volume):
        if volume["type"] == KVM_VOL:
            # NFS does not have target name, use default pool name.
            return DEFAULT_ZBS_VOLUME_POOL_NAME
        else:
            return volume["target_name"]

    @staticmethod
    def get_zbs_volume_name(volume):
        # For NFS volume, the initial name is only first certain length of the zbs
        # volume id, which is different from iSCSI volume's name.
        # (These are both ZBS default behavior for initial names, ELF has never changed
        #  the volume name ).
        if volume["type"] == KVM_VOL:
            return volume["zbs_volume_id"][:18]
        else:
            return volume["zbs_volume_id"]

    @staticmethod
    def get_zbs_volume_id(volume):
        # In ELF, we store volume_id, named zbs_volume_id, into mongoDB. Sometimes, when we use nfs
        # clients, we might need volume_id instead of iNode id.
        return volume["zbs_volume_id"]

    def show_volume_properties(self):
        properties = {
            "vendor": self.volume.vendor,
            "product": self.volume.product,
            "serial": self.volume.serial,
            "wwn": self.volume.wwn,
        }

        if self.volume.type == KVM_VOL:
            labels = None
        else:
            iscsi_client = zbs_iscsi.ZbsClientWrapper(sc_utils.init_zbs_iscsi_client(self.volume.storage_cluster_uuid))
            lun_info = iscsi_client.lun_get(self.volume.target_name, self.volume.lun_id)
            labels = lun_info.labels

        return properties, labels

    def update_iscsi_volume_properties(self, vendor, product, serial, wwn):
        current_properties = {
            "vendor": self.volume.vendor,
            "product": self.volume.product,
            "serial": self.volume.serial,
            "wwn": self.volume.wwn,
        }
        updated_properties = {}
        for key, value in (("vendor", vendor), ("product", product), ("serial", serial), ("wwn", wwn)):
            if value:
                updated_properties[key] = value

        if updated_properties:
            # According to ZBS restriction, all the four labels for volume properties should always be updated
            # at the same time.
            new_properties = current_properties
            new_properties.update(updated_properties)

            # Caller ensure currently all vms mounting the volume are not active
            vms_mounting_the_volume = self.get_mounting_vms()
            for vm in vms_mounting_the_volume:
                for disk in vm["disks"]:
                    if disk["path"] == self.volume.path:
                        disk["serial"] = new_properties["serial"]

            iscsi_client = zbs_iscsi.ZbsClientWrapper(sc_utils.init_zbs_iscsi_client(self.volume.storage_cluster_uuid))
            iscsi_client.lun_update(self.volume.lun_id, self.volume.target_name, labels=new_properties)

            with mongodb.start_session() as session:
                with session.start_transaction():
                    self.db.resource.update_one(
                        {"uuid": self.volume.uuid}, {"$set": updated_properties}, session=session
                    )

                    for vm in vms_mounting_the_volume:
                        self.db.resource.update_one(
                            {"uuid": vm["uuid"]}, {"$set": {"disks": vm["disks"]}}, session=session
                        )

    def get_mounting_vms(self):
        vms = list(
            self.db.resource.find(
                {"type": KVM_VM, "resource_state": RESOURCE_IN_USE, "disks.volume_uuid": self.volume.uuid},
                {"_id": 0, "uuid": 1, "disks": 1, "vm_name": 1, "status": 1},
            )
        )
        return vms
