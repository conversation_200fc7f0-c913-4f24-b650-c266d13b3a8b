# Copyright (c) 2013-2022, SMARTX
# All rights reserved.
import copy
from datetime import datetime
import itertools
import logging
import math
import time
import uuid

from common.config.resources import RESOURCE_HIDDEN, RESOURCE_IN_USE, RESOURCE_REMOVED
from common.mongo.db import mongodb
from job_center.common import utils as jc_utils
from job_center.common.constants import (
    JOB_PENDING,
    JOB_PROCESSING,
    JOB_TYPE_ACTION,
)
from job_center.config import JOB_DB_NAME
from smartx_app.common.node.db import query_host_by_data_ip, query_hosts
from smartx_app.elf.common import constants as elf_common_constants
from smartx_app.elf.common.code import (
    API_DB_VM_RECOVER,
    API_VM_CLONE,
    API_VM_CREATE,
    API_VM_DELETE,
    API_VM_EDIT,
    API_VM_EDIT_DEVICE,
    API_VM_EDIT_NIC,
    API_VM_EDIT_VOL,
    API_VM_FORCE_REBOOT,
    API_VM_FORCE_STOPPED,
    API_VM_MIGRATE,
    API_VM_MIGRATE_ACROSS_CLUSTER,
    API_VM_MIGRATE_COMPUTATION_ACROSS_CLUSTER,
    API_VM_PAUSE,
    API_VM_REBOOT,
    API_VM_RECOVER,
    API_VM_RESUME,
    API_VM_SNAPSHOT_REBUILD,
    API_VM_START,
    API_VM_STOPPED,
    API_VM_STORAGE_LIVE_MIGRATE,
    API_VM_TEMPLATE_VM_CREATE,
)
from smartx_app.elf.common.constants import (
    OVER_NETWORK_USB,
    OVS_HEALTH_STATUS_HEALTHY,
    OVS_HEALTH_STATUS_UNDETECTABLE,
    OVS_HEALTH_STATUS_UNHEALTHY,
    PASS_THROUGH_GPU,
    PASS_THROUGH_NIC,
    PASS_THROUGH_PCI,
    PASS_THROUGH_USB,
    PASS_THROUGH_VF_PCI,
    VGPU,
    VM_ACTIVE_STATES,
    VM_ALL_STATES,
    VM_CLOCK_OFFSET_UTC,
    VM_DELETED,
    VM_FORCE_STOPPED,
    VM_INTERFACE_SRIOV,
    VM_REBOOT,
    VM_RESET,
    VM_RUNNING,
    VM_STOPPED,
    VM_SUSPENDED,
    VM_UNKNOWN,
    VM_VIDEO_CIRRUS,
)
from smartx_app.elf.common.events.vm_events_wrapper import VMEventWrapper
from smartx_app.elf.common.resource_query.base import CriteriaResolver
from smartx_app.elf.common.resource_query.utils import ResourceConvertor
from smartx_app.elf.common.resource_query.vm_querier import VMQuerier
from smartx_app.elf.common.resource_wrappers import gpu, mdev, normal_pci_device, pnic, sriov, sriov_vf, usb, vgpu
from smartx_app.elf.common.resource_wrappers.vm_additional_info_manager import VMAdditionalInfoWrapper
from smartx_app.elf.common.resource_wrappers.vm_snapshot_wrapper import VMSnapshotWrapper
from smartx_app.elf.common.resource_wrappers.vmtools_wrapper import VMToolsWrapper
from smartx_app.elf.common.resource_wrappers.volume_snapshot_wrapper import VolumeSnapshotWrapper
from smartx_app.elf.common.resource_wrappers.volume_wrapper import VolumeWrapper
from smartx_app.elf.common.resources import volume_vm_relationship as vol_vm_rel
from smartx_app.elf.common.resources.attribute import Attribute
from smartx_app.elf.common.resources.base import ResourceException
from smartx_app.elf.common.resources.folder import Folder
from smartx_app.elf.common.resources.storage_policy import StoragePolicy
from smartx_app.elf.common.resources.vm_template import VMTemplate
from smartx_app.elf.common.utils import cloud_init, network, node_info, pci_device_cache, resource
from smartx_app.elf.common.utils import (
    disk as disk_util,
)
from smartx_app.elf.common.utils import mirror_vm as mirror_vm_utils
from smartx_app.elf.common.utils.dhcp import delete_ip_for_vm
from smartx_app.elf.common.utils.disk import ensure_cdrom_bus, generate_disk_key, restore_serial
from smartx_app.elf.job_center.constants import (
    CDROM,
    CORE2DUO,
    DISK,
    ISO_IMAGE,
    KVM_VM,
    KVM_VM_TEMPLATE,
    KVM_VOL,
    KVM_VOL_ISCSI,
)
from smartx_app.elf.job_center.lib.converter.domain import vm_clone
from smartx_app.elf.scheduler.constants import OperationContext
from smartx_app.network.common.utils.vds_utils import get_vlan_all
from smartx_proto.errors import pyerror_pb2 as py_error


class VMWrapper:
    _API_TO_VM_STATUS_MAPPING = {
        API_VM_START: VM_RUNNING,
        API_VM_STOPPED: VM_STOPPED,
        API_VM_FORCE_STOPPED: VM_FORCE_STOPPED,
        API_VM_PAUSE: VM_SUSPENDED,
        API_VM_RESUME: VM_RUNNING,
        API_VM_REBOOT: VM_REBOOT,
        API_VM_FORCE_REBOOT: VM_RESET,
    }

    db = mongodb.resources

    def __init__(self, vm_doc):
        self.vm_doc = vm_doc

    @staticmethod
    def fit_storage_policy(vm_doc):
        if vm_doc:
            for vol in vm_doc["disks"]:
                if vol["type"] == "disk":
                    VolumeWrapper.fit_storage_policy(vol)
        return vm_doc

    @staticmethod
    def fit_clock_offset(vm_doc):
        if "clock_offset" not in vm_doc:
            vm_doc["clock_offset"] = VM_CLOCK_OFFSET_UTC
        return vm_doc

    @staticmethod
    def fit_uptime(vm_doc):
        if vm_doc["status"] == VM_RUNNING:
            last_start_time = vm_doc.get("last_start_time", 0) or 0
            last_resume_time = vm_doc.get("last_resume_time", 0) or 0
            uptime = int(time.time()) - max(last_start_time, last_resume_time)
            # Prevent uptime from calculating negative numbers due to time regression
            vm_doc["uptime"] = max(0, uptime)
        else:
            vm_doc["uptime"] = 0

        return vm_doc

    @staticmethod
    def fit_cpu_topo(vm_doc):
        if "cpu" not in vm_doc:
            vm_doc["cpu"] = {"topology": {"cores": 1, "sockets": vm_doc["vcpu"]}}
        return vm_doc

    @staticmethod
    def fit_nested_vm(vm_doc):
        if "nested_virtualization" not in vm_doc:
            vm_doc["nested_virtualization"] = False
        return vm_doc

    @staticmethod
    def fit_cpu_model(vm_doc):
        if "cpu_model" not in vm_doc:
            vm_doc["cpu_model"] = CORE2DUO
            vm_doc["guest_cpu_model"] = CORE2DUO
        return vm_doc

    @staticmethod
    def fit_win_opt(vm_doc):
        if "win_opt" not in vm_doc:
            vm_doc["win_opt"] = False
        return vm_doc

    @staticmethod
    def fit_firmware(vm_doc):
        if "firmware" not in vm_doc:
            vm_doc["firmware"] = "BIOS"
        return vm_doc

    @staticmethod
    def fit_guest_os_type(vm_doc):
        if "guest_os_type" not in vm_doc:
            vm_doc["guest_os_type"] = elf_common_constants.GuestOSType.UNKNOWN
        return vm_doc

    @staticmethod
    def _fit_volume_info(vm_doc):
        """fit disk.name disk.size disk.volume_is_sharing to vm.disks"""
        if vm_doc:
            volume_uuid_list = [x["volume_uuid"] for x in vm_doc["disks"] if x["type"] == DISK]
            vols_mapping = {x["uuid"]: x for x in VolumeWrapper.batch_query(volume_uuid_list)}

            for disk in vm_doc["disks"]:
                if disk["type"] == DISK and disk["volume_uuid"] in vols_mapping:
                    vol = vols_mapping[disk["volume_uuid"]]

                    disk["name"] = vol["name"]
                    disk["size"] = vol["size_in_byte"]
                    disk["volume_is_sharing"] = vol.get("sharing", False)
                    disk["storage_policy_uuid"] = vol["storage_policy_uuid"]
                    disk["encryption_algorithm"] = vol.get(
                        "encryption_algorithm", elf_common_constants.VOLUME_ENCRYPTION_ALG_PLAINTEXT
                    )

                    if vol.get("type") == KVM_VOL_ISCSI:
                        disk["resident_in_cache"] = vol.get("resident_in_cache", False)
                        disk["resident_in_cache_percentage"] = vol.get("resident_in_cache_percentage", 0)
        return vm_doc

    @staticmethod
    def _fit_volume_storage_policy(vm_docs):
        """Retrieve the volume's real storage_policy_uuid"""
        if vm_docs:
            volume_uuid_list = [x["volume_uuid"] for vm_doc in vm_docs for x in vm_doc["disks"] if x["type"] == DISK]
            projection = {"type": 1, "uuid": 1, "storage_policy_uuid": 1}
            vols_mapping = {x["uuid"]: x for x in VolumeWrapper.batch_query(volume_uuid_list, projection)}

            for vm_doc in vm_docs:
                for disk in vm_doc["disks"]:
                    if disk["type"] == DISK and disk["volume_uuid"] in vols_mapping:
                        vol = vols_mapping[disk["volume_uuid"]]
                        disk["storage_policy_uuid"] = vol["storage_policy_uuid"]
        return vm_docs

    @staticmethod
    def fit_nic_info(vm_doc):
        if vm_doc:
            resource.add_more_info_to_nic(vm_doc["nics"])
        return vm_doc

    @staticmethod
    def fit_device_info(vm_doc):
        """fit device.name device.type device.sys_path etc to vm"""
        if vm_doc:
            devices = vm_doc.get("hostdevs", [])
            if not devices:
                vm_doc["hostdevs"] = []
                return vm_doc

            hostdevs = []
            for device in devices:
                # Compatible interface of return pass_through usb json.
                if device.get("type") == PASS_THROUGH_USB:
                    pass_through_usb = usb.create_usb(device["uuid"], device["type"], device["host_id"])
                    try:
                        pass_through_usb.update_base_info()
                    except:
                        logging.warning("Get usb({}) base info failed.".format(pass_through_usb.usb_id))

                    hostdevs.append(pass_through_usb.dump())
                    continue

                hostdevs.append(device)

            vm_doc["hostdevs"] = hostdevs

        return vm_doc

    @staticmethod
    def fit_additional_info(vm_docs):
        vm_uuids = [vm_doc["uuid"] for vm_doc in vm_docs]
        docs = VMAdditionalInfoWrapper.query(vm_uuids, {"domain_uuid": 1, "cloud_init": 1, "internal_product": 1})

        addl_infos = {doc["vm_uuid"]: doc for doc in docs}

        for vm_doc in vm_docs:
            addl_info = addl_infos.get(vm_doc["uuid"], {})
            vm_doc["bios_uuid"] = addl_info.get("domain_uuid", "")
            vm_doc["cloud_init_supported"] = "cloud_init" in addl_info
            vm_doc["internal_product"] = addl_info.get("internal_product")

    @staticmethod
    def fit_vmtools_info(vm_docs):
        vm_uuids = [vm_doc["uuid"] for vm_doc in vm_docs]
        vmtools_iso_paths = VMToolsWrapper.batch_get_vmtools_iso_path(vm_uuids)

        for vm_doc in vm_docs:
            vm_doc["svt_iso"] = vmtools_iso_paths.get(vm_doc["uuid"], "")

    @staticmethod
    def _fit_cpu_exclusive(vm_doc):
        if "vcpu_to_pcpu" in vm_doc["cpu_exclusive"]:
            del vm_doc["cpu_exclusive"]["vcpu_to_pcpu"]

    @staticmethod
    def _fit_cpu_qos(vm_doc):
        if "reservation_enabled" in vm_doc.get("cpu_qos", {}):
            del vm_doc["cpu_qos"]["reservation_enabled"]

    @staticmethod
    def _fit_device_info_for_retrieval(vm_doc):
        hostdevs = []
        for device in vm_doc.get("hostdevs", []):
            # For PCI devices, drop pci address, only return uuid and type
            if device.get("type") in (PASS_THROUGH_GPU, PASS_THROUGH_NIC, PASS_THROUGH_PCI, PASS_THROUGH_VF_PCI):
                hostdevs.append({"uuid": device["uuid"], "type": device["type"]})
                continue

            # For VGPU devices, drop pci address, only return uuid, type and gpu_uuid
            if device.get("type") == VGPU:
                hostdevs.append(
                    {"vgpu_type_id": device["vgpu_type_id"], "type": device["type"], "gpu_uuid": device["gpu_uuid"]}
                )
                continue

            hostdevs.append(device)

        vm_doc["hostdevs"] = hostdevs

    @classmethod
    def submit_batch_job(cls, description, batch_resources, job_id=None, event=None):
        """submit resources of batch VMs operation to job center,
        resources are grouped by VM

        :param description: description of the batch job
        :param batch_resources: list of resources dict

        :return: job id
        """
        if not batch_resources:
            raise ResourceException("No resource is selected.", py_error.VM_NOT_FOUND)
        job_resources = {}
        job_resource_groups = {}
        for resources in batch_resources:
            job_resources.update(resources)
            # group resources belong to same VM
            job_resource_groups[str(uuid.uuid4())] = list(resources.keys())
        return cls.submit_job(
            description, job_resources, resource_group=job_resource_groups, job_id=job_id, event=event
        )

    @staticmethod
    def submit_job(description, resources, resource_group=None, job_id=None, event=None):
        from job_center.handler.leader.workers import job_submit

        job_id = job_id or str(uuid.uuid4())
        job_info = {
            "user": "",
            "description": description,
            "resources": resources,
            "resource_group": resource_group,
            "job_id": job_id,
            "event": event,
        }
        return {"job_id": job_submit(**job_info)}

    @staticmethod
    def submit_migrate_across_cluster_job(resource):
        from job_center.handler.leader.workers import job_submit
        from smartx_app.elf.job_center.constants import TASK_VM_ACROSS_CLUSTER_MIGRATE

        task_uuid = resource["vm_json"]["uuid"]
        job_id = str(uuid.uuid4())
        resource.update({"job_id": job_id})
        one_time_task = {
            task_uuid: {
                "hosts": [resource["vm_json"]["node_ip"]],
                "data": resource,
                "name": TASK_VM_ACROSS_CLUSTER_MIGRATE,
            }
        }

        return {
            "job_id": job_submit(
                user="", description=API_VM_MIGRATE_ACROSS_CLUSTER, one_time_task=one_time_task, job_id=job_id
            )
        }

    @staticmethod
    def submit_migrate_computation_across_cluster_job(job_resource):
        from job_center.handler.leader.workers import job_submit
        from smartx_app.elf.job_center import constants

        if job_resource["vm_json"]["status"] == VM_RUNNING:
            one_time_task_name = constants.TASK_VM_ACROSS_CLUSTER_LIVE_MIGRATE_COMPUTATION
        else:
            one_time_task_name = constants.TASK_VM_ACROSS_CLUSTER_COLD_MIGRATE_COMPUTATION

        task_uuid = job_resource["vm_json"]["uuid"]
        job_id = str(uuid.uuid4())
        job_resource.update({"job_id": job_id})
        one_time_task = {
            task_uuid: {
                "hosts": [job_resource["vm_json"]["node_ip"]],
                "data": job_resource,
                "name": one_time_task_name,
            }
        }
        return {
            "job_id": job_submit(
                user="",
                description=API_VM_MIGRATE_COMPUTATION_ACROSS_CLUSTER,
                one_time_task=one_time_task,
                job_id=job_id,
            )
        }

    @staticmethod
    def submit_storage_live_migrate_job(resources):
        from job_center.handler.leader.workers import job_submit
        from smartx_app.elf.job_center.constants import TASK_VM_STORAGE_LIVE_MIGRATE

        task_uuid = resources["vm_json"]["uuid"]
        job_id = str(uuid.uuid4())
        resources.update({"job_id": job_id})
        one_time_task = {
            task_uuid: {
                "hosts": [resources["vm_json"]["node_ip"]],
                "data": resources,
                "name": TASK_VM_STORAGE_LIVE_MIGRATE,
            }
        }

        return {
            "job_id": job_submit(
                user="", description=API_VM_STORAGE_LIVE_MIGRATE, one_time_task=one_time_task, job_id=job_id
            )
        }

    @staticmethod
    def build_disks(raw_disks, vm_uuid=None):
        """
        build disks to create new volumes and update vm `disks`.

        :param raw_disks:   list of dict, the form of dict that are defined
                            in the corresponding sugar.
        :param vm_uuid:     uuid of vm which is going to mount these disks
        :return:            a tuple(disks, volumes), the first member is
                            `disks` that will be used by vm, the second
                            member is `volumes` that will be used to create
                            new volumes.
        """
        disks, volumes = [], []
        exist_vol_paths = []
        exist_keys = [d.get("key", -1) for d in raw_disks if d["type"] == CDROM]
        for index, raw_disk in enumerate(raw_disks):
            volume_uuid = None
            # non-exist volume
            if "path" not in raw_disk:
                volume = VolumeWrapper.new(
                    name=raw_disk["name"],
                    size_in_byte=raw_disk.get("size_in_byte"),
                    description="",
                    volume_type=KVM_VOL_ISCSI,
                    storage_policy_uuid=raw_disk.get("storage_policy_uuid"),
                    src_export_id=raw_disk.get("src_export_id"),
                    src_inode_id=raw_disk.get("src_inode_id"),
                    src_target_name=raw_disk.get("src_target_name"),
                    src_lun_id=raw_disk.get("src_lun_id"),
                    new_size_in_byte=raw_disk.get("new_size_in_byte"),
                    clone_before_create=raw_disk.get("clone_before_create"),
                    storage_cluster_uuid=raw_disk.get("storage_cluster_uuid"),
                    resident_in_cache=raw_disk.get("resident_in_cache", False),
                    encryption_algorithm=raw_disk.get("encryption_algorithm"),
                    vendor=raw_disk.get("vendor"),
                    product=raw_disk.get("product"),
                    serial=raw_disk.get("serial"),
                    wwn=raw_disk.get("wwn"),
                ).volume

                volumes.append(volume)
                path = "@{{{}/path}}".format(volume.uuid)
                volume_uuid = volume.uuid
                volume_serial = volume.serial
            else:
                path = raw_disk["path"]
                exist_vol_paths.append(path)
                volume_serial = None

            disk = {
                "boot": index + 1,
                "type": raw_disk["type"],
                "bus": raw_disk["bus"],
                # `size_in_byte` just for volume extend,
                # will pop it in the end.
                "size_in_byte": raw_disk.get("size_in_byte", -1),
                "path": path,
                "storage_policy_uuid": raw_disk.get("storage_policy_uuid", None),
                "quota_policy": raw_disk.get("quota_policy"),
            }

            if "resident_in_cache" in raw_disk:
                disk.update({"resident_in_cache": raw_disk["resident_in_cache"]})

            if raw_disk["type"] == CDROM:
                if not raw_disk.get("key"):
                    key = generate_disk_key(CDROM, exist_keys)
                    if not key:
                        raise ResourceException("CDRom number has exceeded the limit.", py_error.VM_CDROM_MAXIMUN)
                    else:
                        disk["key"] = key
                        exist_keys.append(key)
                else:
                    disk["key"] = raw_disk["key"]
                disk["disabled"] = raw_disk.get("disabled", False)

                if "host" in raw_disk:
                    disk["host"] = raw_disk["host"]
            else:
                disk["volume_uuid"] = volume_uuid
                disk["serial"] = volume_serial
            disks.append(disk)

        # Fill in `storage_policy_uuid` field if it is a exist
        # volume(only disk). cdrom do not support storage policy,
        # so keep it is None.
        exist_vol_disks = [d for d in disks if d["type"] == DISK and d["path"] in exist_vol_paths]
        if exist_vol_disks:
            vols_exist = VolumeWrapper.get_mountable_by_paths(
                paths=[d["path"] for d in exist_vol_disks], vm_uuid=vm_uuid
            )

            path_to_vol = {vol.path: vol for vol in vols_exist}
            for disk in exist_vol_disks:
                vol = path_to_vol[disk["path"]]
                # For an exist volume, use volume serial as disk serial
                disk["serial"] = vol.serial

                is_vol_modified = False

                if disk["storage_policy_uuid"]:
                    # upgrade the storage policy of the volume
                    # only iscsi volume can set
                    if vol.type == KVM_VOL_ISCSI and disk["storage_policy_uuid"] != vol.storage_policy_uuid:
                        vol.storage_policy_uuid = disk["storage_policy_uuid"]
                        is_vol_modified = True

                else:
                    # upgrade the storage policy of the volume
                    # compatible with the old nfs volumes
                    disk["storage_policy_uuid"] = vol.storage_policy_uuid or StoragePolicy.DEFAULT_STORAGE_POLICY_UUID

                # Volume resize
                if disk["size_in_byte"] != -1 and disk["size_in_byte"] != vol.size_in_byte:
                    vol.size_in_byte = disk["size_in_byte"]
                    is_vol_modified = True

                # resident volume
                if "resident_in_cache" in disk:
                    if vol.type == KVM_VOL_ISCSI and disk["resident_in_cache"] != vol.resident_in_cache:
                        vol.resident_in_cache = disk["resident_in_cache"]
                        is_vol_modified = True

                disk["volume_uuid"] = vol.uuid

                if not vol.sharing or is_vol_modified:
                    volumes.append(vol)

        # `size_in_byte` is not need in the disk of the vm, so pop it.
        [x.pop("size_in_byte") for x in disks]

        # `resident_in_cache` is not need in the disk of the mv, so pop it.
        [x.pop("resident_in_cache", None) for x in disks]

        return disks, volumes

    @classmethod
    def fit_extra_fields(cls, vm_doc):
        cls.fit_extra_fields_for_multi_vms([vm_doc])
        return vm_doc

    @classmethod
    def fit_extra_fields_for_multi_vms(cls, vm_docs):
        for vm_doc in vm_docs:
            cls.fit_storage_policy(vm_doc)
            cls.fit_clock_offset(vm_doc)
            cls.fit_uptime(vm_doc)
            cls.fit_cpu_topo(vm_doc)
            cls.fit_nested_vm(vm_doc)
            cls.fit_cpu_model(vm_doc)
            cls.fit_win_opt(vm_doc)
            cls.fit_firmware(vm_doc)
            cls.fit_guest_os_type(vm_doc)
            cls.fit_device_info(vm_doc)

        cls.fit_additional_info(vm_docs)
        cls.fit_vmtools_info(vm_docs)

    @classmethod
    def fit_fields_for_retrieval(cls, vm_docs):
        """Fill or change some fields of VMs for retrieval only.
        :param vm_docs: VMs to fill or change.
        :type vm_docs: list
        :return: None
        """
        list(map(cls._fit_cpu_exclusive, vm_docs))
        list(map(cls._fit_cpu_qos, vm_docs))
        list(map(cls._fit_device_info_for_retrieval, vm_docs))
        cls._fit_volume_storage_policy(vm_docs)
        cls._fit_nic_health_status(vm_docs)

    @classmethod
    def fit_fields_for_retrieval_single_vm_only(cls, vm_doc):
        """Fill or change some fields of VM for retrieval only.

        Retrieving for a single VM requires filling more extra fields,
        which is not necessary for the list/batch query/search.

        :param vm_doc: VM to fill or change.
        :type vm_doc: dict
        :return: None
        """
        cls._fit_cpu_exclusive(vm_doc)
        cls._fit_cpu_qos(vm_doc)
        cls._fit_device_info_for_retrieval(vm_doc)
        cls._fit_volume_info(vm_doc)
        cls._fit_nic_health_status(vm_docs=(vm_doc,))

    @classmethod
    def _fit_nic_health_status(cls, vm_docs):
        if not vm_docs:
            return

        all_host_faulty_ovsbrs_map = _get_all_host_faulty_ovsbrs_map()

        for vm_doc in vm_docs:
            vm_node_ip = vm_doc["node_ip"]

            if vm_doc["status"] != VM_RUNNING or all_host_faulty_ovsbrs_map.get(vm_node_ip, None) is None:
                # When ovs bridge monitor is not working in the elf vm monitor,
                # all_host_faulty_ovsbrs_map.get(vm_node_ip, None) is None.
                for nic in vm_doc["nics"]:
                    nic["ovs_health_status"] = OVS_HEALTH_STATUS_UNDETECTABLE
                continue

            for nic in vm_doc["nics"]:
                if nic["model"] == "sriov" or nic.get("link", "") != "up":
                    ovs_health_status = OVS_HEALTH_STATUS_UNDETECTABLE
                elif nic.get("ovs", "") in all_host_faulty_ovsbrs_map[vm_node_ip]:
                    ovs_health_status = OVS_HEALTH_STATUS_UNHEALTHY
                else:
                    ovs_health_status = OVS_HEALTH_STATUS_HEALTHY

                nic["ovs_health_status"] = ovs_health_status

    @classmethod
    def page_query(
        cls,
        count,
        skip_page,
        sort_criteria,
        filter_criteria,
        projection,
        start_uuid,
        end_uuid,
        skip_to_last,
        current_page=1,
    ):
        # Will set to the default value by the method:`fit_extra_fields` if
        # the query will not return these fields.
        if projection:
            projection += ",disks,hostdevs,clock_offset,cpu,nested_virtualization"

        data = VMQuerier().page_query(
            count=count,
            skip_page=skip_page,
            sort_criteria=sort_criteria,
            filter_criteria=filter_criteria,
            projection=projection,
            start_uuid=start_uuid,
            end_uuid=end_uuid,
            skip_to_last=skip_to_last,
            current_page=current_page,
        )
        VMToolsWrapper.apply_nics(*data["entities"])
        VMToolsWrapper.apply_guest_infos(*data["entities"])
        Folder.apply_folder(data["entities"])
        cls.fit_extra_fields_for_multi_vms(data["entities"])
        cls.fit_fields_for_retrieval(data["entities"])

        return data

    @classmethod
    def search_macs_by_ip(cls, ip_address_text):
        from smartx_app.network.common.utils.dhcp import DHCPError, IPAllocatorProxy

        ip_allocator = IPAllocatorProxy(timeout=20)

        try:
            mappings = ip_allocator.search_macs_by_ip(ip_address_text).get("macipmappings", [])
        except DHCPError:
            mappings = []
            logging.warning("Catch the `DHCPError`, so can't search macs by ip.")
        macs = [m["mac"] for m in mappings]
        # vmtools ip to macs
        try:
            vmtools_related_macs = VMToolsWrapper.get_ip_related_macs(ip_address_text)
            # If vmtools_related_macs is too large, the regex query generated in
            # VMQuerier().search() will be too long and raise an exception: "Regular
            # expression is too long".
            # In testing, mac address nums up to 128 can be handled.
            # In case other kind of text is too long, we limit length of
            # vmtools_related_macs to 100
            if len(vmtools_related_macs) > 100:
                vmtools_related_macs = vmtools_related_macs[:100]

        except BaseException:
            vmtools_related_macs = []
        macs.extend(vmtools_related_macs)
        return macs

    @classmethod
    def search(cls, text, limit=None, filter_criteria=None):
        from smartx_app.common.node.db import fuzzy_search_by_name

        text = [text]
        text.extend(cls.search_macs_by_ip(text[0]))
        text.extend([x["data_ip"] for x in fuzzy_search_by_name(text[0], {"data_ip": 1})])
        data = VMQuerier().search([x for x in text if x], limit, filter_criteria)
        VMToolsWrapper.apply_nics(*data["entities"])
        VMToolsWrapper.apply_guest_infos(*data["entities"])
        Folder.apply_folder(data["entities"])
        cls.fit_extra_fields_for_multi_vms(data["entities"])
        cls.fit_fields_for_retrieval(data["entities"])

        return data

    @classmethod
    def get(cls, vm_uuid, querier=None):
        if not querier:
            querier = VMQuerier()

        vm = querier.get(vm_uuid)
        if vm:
            VMToolsWrapper.apply_guest_infos(vm)
            cls.fit_extra_fields(vm)
            cls.fit_nic_info(vm)
            cls.fit_fields_for_retrieval_single_vm_only(vm)

        return vm

    @classmethod
    def update_nics(cls, vm_uuid, nics):
        logging.info("Update vm({}) nics ({}).".format(vm_uuid, nics))
        cls.db.resource.update({"uuid": vm_uuid}, {"$set": {"nics": nics, "update_time": int(time.time())}})

    @classmethod
    def update_hostdevs(cls, vm_uuid, hostdevs, update_time):
        logging.info("Update vm({}) hostdevs ({}).".format(vm_uuid, hostdevs))
        res = cls.db.resource.update(
            {"uuid": vm_uuid, "update_time": update_time},
            {"$set": {"hostdevs": hostdevs, "update_time": int(time.time())}},
        )
        return res.get("nModified") != 0

    @classmethod
    def update_sync_vm_time_on_resume(cls, vm_uuid, sync_vm_time_on_resume):
        logging.info("Set VM({}) sync_vm_time_on_resume to {}.".format(vm_uuid, sync_vm_time_on_resume))
        cls.db.resource.update(
            {"uuid": vm_uuid, "type": KVM_VM, "resource_state": RESOURCE_IN_USE},
            {"$set": {"sync_vm_time_on_resume": sync_vm_time_on_resume, "update_time": int(time.time())}},
        )

    @classmethod
    def batch_query(cls, vm_uuid_list):
        vms = VMQuerier().query({"uuid": {"$in": vm_uuid_list}}, top=len(vm_uuid_list))
        VMToolsWrapper.apply_nics(*vms)
        VMToolsWrapper.apply_guest_infos(*vms)
        Folder.apply_folder(vms)
        cls.fit_extra_fields_for_multi_vms(vms)
        cls.fit_fields_for_retrieval(vms)

        return vms

    @staticmethod
    def _status():
        return {s: 0 for s in VM_ALL_STATES}

    @classmethod
    def summary(cls):
        total_vms = 0
        total_vcpu = 0
        total_memory = 0
        total_status = cls._status()
        nodes = {}
        cursor = cls.db.resource.find(
            {"type": KVM_VM, "resource_state": RESOURCE_IN_USE},
            {"_id": 0, "status": 1, "vcpu": 1, "memory": 1, "node_ip": 1, "cpu_exclusive": 1, "cpu_qos": 1},
        )
        cluster_memory_info = node_info.get_cluster_memory_info()
        host_infos = query_hosts()
        node_ip_to_memory = {
            host["data_ip"]: host["memory"].get(elf_common_constants.TOTAL_MEM_FIELD_NAME, 0)
            for host in cluster_memory_info
        }
        node_ip_to_os_memory = {
            host["data_ip"]: host["memory"].get(elf_common_constants.OS_MEM_USED_FIELD_NAME, 0)
            for host in cluster_memory_info
        }
        node_ip_to_cpu = {
            host.get("data_ip"): {
                # TODO(wei.wang) The 'smtx_os_cpu' and 'smtx_os_memory' will be removed in a future version.
                # TODO(wei.wang) It is now recommended to use 'elfvirt_vm_cpu' and 'elfvirt_vm_memory'.
                "smtx_os_cpu": node_info.CPUInfoCache.get_qemu_cpu_num_on_host(host["host_uuid"]),
                "elfvirt_vm_cpu": node_info.CPUInfoCache.get_qemu_cpu_num_on_host(host["host_uuid"]),
                "hz_advertised_raw": host.get("cpu", {}).get("hz_advertised_raw", [0])[0],
            }
            for host in host_infos
        }

        for host in host_infos:
            nodes.update(
                {
                    host.get("data_ip"): {
                        "count": 0,
                        "vcpu": 0,
                        "memory": 0,
                        "running_pause_memory": 0,
                        "node_memory": node_ip_to_memory.get(host.get("data_ip")),
                        "smtx_os_memory": node_ip_to_os_memory.get(host.get("data_ip")),
                        "smtx_os_cpu": node_ip_to_cpu.get(host.get("data_ip"), {}).get("smtx_os_cpu", 0),
                        "elfvirt_vm_memory": node_ip_to_os_memory.get(host.get("data_ip")),
                        "elfvirt_vm_cpu": node_ip_to_cpu.get(host.get("data_ip"), {}).get("elfvirt_vm_cpu", 0),
                        "status": cls._status(),
                        "exclusive_cpu": 0,
                        "vcpu_of_active_vms": 0,
                        "available_exclusive_cpu": 0,
                        "cpu_reservation_hz": 0,
                        "available_cpu_reservation_hz": 0,
                    }
                }
            )

        for x in cursor:
            total_vms += 1
            total_vcpu += x["vcpu"]
            total_memory += x["memory"]
            total_status[x["status"]] += 1

            node_summary = nodes.setdefault(
                x.get("node_ip", None),
                {
                    "count": 0,
                    "vcpu": 0,
                    "memory": 0,
                    "running_pause_memory": 0,
                    "node_memory": node_ip_to_memory.get(x.get("node_ip")),
                    "smtx_os_memory": node_ip_to_os_memory.get(x.get("node_ip")),
                    "smtx_os_cpu": node_ip_to_cpu.get(x.get("node_ip"), {}).get("smtx_os_cpu", 0),
                    "elfvirt_vm_memory": node_ip_to_os_memory.get(x.get("node_ip")),
                    "elfvirt_vm_cpu": node_ip_to_cpu.get(x.get("node_ip"), {}).get("elfvirt_vm_cpu", 0),
                    "status": cls._status(),
                    "exclusive_cpu": 0,
                    "vcpu_of_active_vms": 0,
                    "available_exclusive_cpu": 0,
                    "cpu_reservation_hz": 0,
                    "available_cpu_reservation_hz": 0,
                },
            )
            node_summary["count"] += 1
            node_summary["vcpu"] += x["vcpu"]
            node_summary["memory"] += x["memory"]
            node_summary["status"][x["status"]] += 1
            if x["status"] in VM_ACTIVE_STATES:
                node_summary["running_pause_memory"] += x["memory"]
                node_summary["vcpu_of_active_vms"] += x["vcpu"]
                if x.get("cpu_exclusive", {}).get("actual_enabled", False):
                    node_summary["exclusive_cpu"] += len(x["cpu_exclusive"]["vcpu_to_pcpu"])
                if x.get("cpu_qos", {}).get("reservation_enabled", False):
                    node_summary["cpu_reservation_hz"] += x.get("cpu_qos", {}).get("reservation_hz", 0)

        for node_ip, node_summary in nodes.items():
            total_cpus_count = node_summary["elfvirt_vm_cpu"]
            total_cpus_exclusive_count = node_summary["exclusive_cpu"]
            total_cpus_reservation_hz = node_summary["cpu_reservation_hz"]
            cpu_hz_advertised = node_ip_to_cpu.get(node_ip, {}).get("hz_advertised_raw", 0)

            total_cpus_reservation_count = math.ceil(
                total_cpus_reservation_hz / elf_common_constants.CPU_QOS_MAX_RESERVABLE_RATIO / cpu_hz_advertised
            )
            min_shared_cpus_count = math.ceil(total_cpus_count * elf_common_constants.SHARED_CPU_THRESHOLD)
            node_summary["available_exclusive_cpu"] = (
                total_cpus_count - max(total_cpus_reservation_count, min_shared_cpus_count) - total_cpus_exclusive_count
            )

            shared_cpus_hz = (total_cpus_count - total_cpus_exclusive_count) * cpu_hz_advertised
            node_summary["available_cpu_reservation_hz"] = (
                math.floor(shared_cpus_hz * elf_common_constants.CPU_QOS_MAX_RESERVABLE_RATIO)
                - total_cpus_reservation_hz
            )

        return {"count": total_vms, "vcpu": total_vcpu, "memory": total_memory, "status": total_status, "nodes": nodes}

    @classmethod
    def folder_summary(cls, vms_uuid):
        from smartx_app.elf.common.resource_query.volume_querier import VolumeQuerier

        total_vms = 0
        total_vcpu = 0
        total_memory = 0
        total_usage_memory = 0
        total_volume_size = 0
        total_status = cls._status()
        volumes = []

        if vms_uuid:
            for i in range(0, len(vms_uuid), 500):
                cursor = cls.db.resource.find(
                    {"type": KVM_VM, "resource_state": RESOURCE_IN_USE, "uuid": {"$in": vms_uuid[i : i + 500]}},
                    {"_id": 0, "status": 1, "vcpu": 1, "memory": 1, "folder_uuid": 1, "disks": 1},
                )

                for x in cursor:
                    total_vms += 1
                    total_vcpu += x["vcpu"]
                    total_memory += x["memory"]
                    total_status[x["status"]] += 1
                    total_usage_memory += x["memory"] if (x["status"] in VM_ACTIVE_STATES) else 0
                    for x in x["disks"]:
                        if x["type"] != CDROM:
                            volumes.append(x["path"])
                volume_result = VolumeQuerier().query(
                    {"path": {"$in": volumes}, "type": {"$in": [KVM_VOL, KVM_VOL_ISCSI]}},
                    projection={"size_in_byte": 1, "size": 1, "uuid": 1, "path": 1, "_id": 0},
                    top=len(volumes),
                )
                total_volume_size += sum(v.get("size_in_byte", v.get("size", 0)) for v in volume_result)
        return {
            "count": total_vms,
            "vcpu": total_vcpu,
            "memory": total_memory,
            "status": total_status,
            "total_usage_memory": total_usage_memory,
            "total_volume_size": total_volume_size,
        }

    @classmethod
    def new(
        cls,
        vm_name,
        vcpu,
        cpu,
        memory,
        node_ip,
        ha,
        description,
        nics,
        disks,
        vm_uuid=None,
        hostdevs=None,
        auto_schedule=False,
        clock_offset=None,
        nested_virtualization=None,
        cpu_model=None,
        status=None,
        win_opt=False,
        internal=False,
        internal_product=None,
        firmware=None,
        quota_policy=None,
        video_type=None,
        guest_os_type=None,
        sync_vm_time_on_resume=False,
        cpu_exclusive=None,
        placement_groups=None,
        boot_with_host=False,
        ha_priority=elf_common_constants.HA_PRIORITY_DEFAULT,
        vm_version=None,
        vm_version_mode=None,
        cpu_qos=None,
        migratable=True,
        local_ha_policy=elf_common_constants.LOCAL_HA_POLICY_DEFAULT,
    ):
        cpu_exclusive = cpu_exclusive if cpu_exclusive else {"expected_enabled": False}
        vm_doc = {
            "uuid": vm_uuid or str(uuid.uuid4()),
            "vm_name": vm_name,
            "vcpu": vcpu,
            "cpu": cpu,
            "memory": memory,
            "node_ip": node_ip,
            "ha": ha,
            "boot_with_host": boot_with_host,
            "ha_priority": ha_priority,
            "description": description,
            "nics": nics,
            "disks": disks,
            "hostdevs": hostdevs or [],
            "type": KVM_VM,
            "resource_state": RESOURCE_IN_USE,
            "status": status or VM_RUNNING,
            "create_time": None,
            "auto_schedule": auto_schedule,
            "clock_offset": clock_offset or VM_CLOCK_OFFSET_UTC,
            "nested_virtualization": nested_virtualization,
            "cpu_model": cpu_model,
            "win_opt": win_opt,
            "internal": internal,
            "internal_product": internal_product,
            "firmware": firmware,
            "quota_policy": quota_policy,
            "video_type": video_type or VM_VIDEO_CIRRUS,
            "guest_os_type": guest_os_type or elf_common_constants.GuestOSType.UNKNOWN,
            "sync_vm_time_on_resume": sync_vm_time_on_resume,
            "cpu_exclusive": cpu_exclusive,
            "placement_groups": placement_groups or [],
            "vm_version": vm_version or elf_common_constants.VM_VERSION_LATEST,
            "vm_version_mode": vm_version_mode or elf_common_constants.VM_VERSION_MODE_AUTO,
            "cpu_qos": cpu_qos or elf_common_constants.DEFAULT_CPU_QOS_JSON,
            "migratable": migratable,
            "anti_malware": elf_common_constants.DEFAULT_ANTI_MALWARE,
            "local_ha_policy": local_ha_policy,
        }

        return cls(vm_doc)

    @classmethod
    def query_from_db(cls, vm_uuid=None):
        if vm_uuid is not None:
            query = {"uuid": vm_uuid}
        else:
            query = None

        return [cls.fit_extra_fields(vm) for vm in cls._query_from_db(query)]

    @classmethod
    def fetch_all(cls, query_cond=None, fields_filter=None, include_hidden=False):
        q = copy.deepcopy(query_cond) if query_cond else {}
        query_resource_state = RESOURCE_IN_USE if not include_hidden else {"$in": [RESOURCE_IN_USE, RESOURCE_HIDDEN]}
        q.update({"type": KVM_VM, "resource_state": query_resource_state})

        f = copy.deepcopy(fields_filter) if fields_filter else {}
        f.update({"_id": 0})

        return cls.db.resource.find(q, f)

    @classmethod
    def _query_from_db(cls, query=None, project=None):
        current_query = {"type": KVM_VM, "resource_state": RESOURCE_IN_USE}
        if query:
            current_query.update(query)

        current_project = {"_id": 0}
        if project:
            current_project.update(project)

        return cls.db.resource.find(current_query, current_project)

    @classmethod
    def find_one_from_disk_path(cls, disk_path):
        if not disk_path:
            return None

        vm_doc = cls.db.resource.find_one(
            {"type": KVM_VM, "resource_state": RESOURCE_IN_USE, "disks.path": disk_path}, {"_id": 0}
        )

        return cls(cls.fit_extra_fields(vm_doc)) if vm_doc else None

    @classmethod
    def load(cls, vm_uuid):
        vm_docs = cls.query_from_db(vm_uuid)
        if vm_docs:
            return cls(vm_docs[0])
        raise ResourceException("vm({}) is not exist.".format(vm_uuid), py_error.VM_NOT_FOUND)

    @classmethod
    def batch_load(cls, vm_uuids):
        """generate VMWrappers using given vm uuids

        :param vm_uuids: list of input vm uuid

        :return: list of VMWrapper
        """
        vm_docs = cls.query_from_db({"$in": vm_uuids})
        missed_vm_uuids = list(set(vm_uuids) - {x["uuid"] for x in vm_docs})
        if missed_vm_uuids:
            raise ResourceException("vms({}) are not exist.".format(",".join(missed_vm_uuids)), py_error.VM_NOT_FOUND)
        return [cls(x) for x in vm_docs]

    @property
    def disks(self):
        return [vol for vol in self.vm_doc["disks"] if vol["type"] == "disk"]

    @property
    def cdroms(self):
        return [vol for vol in self.vm_doc["disks"] if vol["type"] == "cdrom"]

    def _submit_action_preview(self, api_name):
        from smartx_app.common import constants

        vm_doc_copy = copy.deepcopy(self.vm_doc)
        if api_name in self._API_TO_VM_STATUS_MAPPING:
            vm_doc_copy["status"] = self._API_TO_VM_STATUS_MAPPING[api_name]

        # When the VM that supports cloud-init is started, migrated or rebuilt, the "cloud_init" field
        # needs to be added. This field is used to determine whether the TASK_VM_PREPARE_CLOUD_INIT subtask
        # needs to be generated in the job splitter.
        if api_name in (API_VM_START, API_VM_MIGRATE, API_VM_RECOVER):
            cloud_init_info = cloud_init.get_cloud_init_info_from_db(vm_doc_copy["uuid"])
            if cloud_init_info is not None:
                vm_doc_copy["cloud_init"] = cloud_init_info

                # After upgrading to SMTXOS 5.1.0, the legacy VMs that support cloud-init mount the config drive ISO
                # under the ZBS NFS path, these VMs need to be changed to the local disk path when starting.
                if api_name == API_VM_START:
                    for x in vm_doc_copy.get("disks", []):
                        if (x["type"] == constants.CDROM) and (
                            x["path"] == cloud_init.get_config_drive_iso_from_zbs_nfs_path(vm_doc_copy["uuid"])
                        ):
                            x["path"] = cloud_init.get_config_drive_iso(vm_doc_copy["uuid"])
                            logging.info(
                                "The config drive ISO mounted on the {} of the VM ({}) "
                                "that supports cloud-init has been modified to {}".format(
                                    constants.CDROM, vm_doc_copy["uuid"], x["path"]
                                )
                            )

        return api_name, {vm_doc_copy["uuid"]: vm_doc_copy}

    def submit_clone_preview(self, new_vm):
        volume_uuids = [vol["volume_uuid"] for vol in self.disks]
        volumes = VolumeWrapper.query_from_db_by_uuids(volume_uuids)
        vm_copy = copy.deepcopy(self.vm_doc)

        new_vm.setdefault("nics", vm_copy["nics"])
        network.ensure_nic(new_vm["nics"])
        network.ensure_vlan_for_nics(new_vm["nics"])
        network.config_nic_pci_address_for_vm_clone(vm_copy["nics"], new_vm["nics"])

        resources = vm_clone(vm_copy, new_vm, volumes)
        return API_VM_CLONE, resources

    def generate_resources_for_creation(self, volumes, folder_uuid=None, cloud_init_info=None):
        vm_doc = copy.deepcopy(self.vm_doc)
        is_mirror_vm = "mirror_vm" in vm_doc

        if folder_uuid:
            folder = Folder.load(folder_uuid).info()
            vm_doc["folder"] = folder

        # generate cloud-init data for the VM
        if cloud_init_info:
            vm_doc["cloud_init"] = cloud_init_info

            # The cloud-init data of the src VM will be used by the mirror VM, directly.
            if not is_mirror_vm:
                cloud_init.prepare_data_for_vm(vm_doc)

        # generate NICs and VLANs for the VM
        network.ensure_nic(vm_doc["nics"])
        network.ensure_vlan_for_nics(vm_doc["nics"])

        # generate volumes for the VM
        disk_util.ensure_cdrom_bus(vm_doc["disks"], "vm_create", res_uuid=self.vm_doc["uuid"])

        resources = {self.vm_doc["uuid"]: vm_doc}
        resources.update({vol.uuid: vol.dumps() for vol in volumes})

        if is_mirror_vm:
            for vol in volumes:
                mirror_vm_utils.ensure_mirror_volume_creation(
                    volume=resources[vol.uuid], mirror_context=vm_doc["mirror_vm"]["context"]
                )

        return resources

    def submit_create(self, volumes, job_id=None, event=None, folder_uuid=None, cloud_init=None):
        resources = self.generate_resources_for_creation(volumes, folder_uuid=folder_uuid, cloud_init_info=cloud_init)

        return self.submit_job(API_VM_CREATE, resources, job_id=job_id, event=event)

    def submit_migrate_preview(self):
        return self._submit_action_preview(API_VM_MIGRATE)

    def submit_rebuild_preview(self, asiainfo_gvm_disable_am_under_ha: bool):
        api_name, job_resources = self._submit_action_preview(API_VM_RECOVER)

        if asiainfo_gvm_disable_am_under_ha:
            job_resources[self.vm_doc["uuid"]]["anti_malware"] = elf_common_constants.DEFAULT_ANTI_MALWARE

        return api_name, job_resources

    def submit_recover_preview(self):
        return self._submit_action_preview(API_DB_VM_RECOVER)

    def submit_edit(
        self,
        vm_name=None,
        vcpu=None,
        cpu=None,
        memory=None,
        ha=None,
        ha_priority=None,
        local_ha_policy=None,
        description=None,
        clock_offset=None,
        nested_virtualization=None,
        cpu_model=None,
        win_opt=None,
        firmware=None,
        quota_policy=None,
        video_type=None,
        event=None,
        guest_os_type=None,
        cpu_exclusive=None,
        boot_with_host=None,
        vm_version=None,
        vm_version_mode=None,
        cpu_qos=None,
    ):
        from smartx_app.elf.common.utils.validator import ValidatorChain

        check_args = [
            vm_name,
            vcpu,
            memory,
            ha,
            local_ha_policy,
            boot_with_host,
            description,
            clock_offset,
            win_opt,
            video_type,
            vm_version,
            vm_version_mode,
        ]

        if check_args.count(None) == len(check_args):
            raise ValueError("You must specify at least one argument.")

        vm_doc_copy = copy.deepcopy(self.vm_doc)
        if vm_name is not None:
            vm_doc_copy["vm_name"] = vm_name

        if vcpu is not None:
            vm_doc_copy["vcpu"] = vcpu

        if cpu is not None:
            vm_doc_copy["cpu"] = cpu

        if (
            cpu_exclusive is not None
            and cpu_exclusive["expected_enabled"] != vm_doc_copy["cpu_exclusive"]["expected_enabled"]
        ):
            vm_doc_copy["cpu_exclusive"] = cpu_exclusive

        if memory is not None:
            vm_doc_copy["memory"] = memory

        if ha is not None:
            # Since OS HA can be triggered only when the os_status value is healthy and restarting,
            # a VM with an operating system failure needs to set os_status to healthy after turning on HA.
            if not vm_doc_copy["ha"] and ha:
                vm_doc_copy["os_status"] = elf_common_constants.OS_STATUS_HEALTHY
            vm_doc_copy["ha"] = ha

        if boot_with_host is not None:
            vm_doc_copy["boot_with_host"] = boot_with_host

        if ha_priority is not None:
            vm_doc_copy["ha_priority"] = ha_priority

        if local_ha_policy is not None:
            vm_doc_copy["local_ha_policy"] = local_ha_policy

        if description is not None:
            vm_doc_copy["description"] = description

        if clock_offset is not None:
            vm_doc_copy["clock_offset"] = clock_offset

        if nested_virtualization is not None:
            vm_doc_copy["nested_virtualization"] = nested_virtualization

        if win_opt is not None:
            vm_doc_copy["win_opt"] = win_opt

        if firmware is not None:
            vm_doc_copy["firmware"] = firmware

        if quota_policy is not None:
            vm_doc_copy["quota_policy"] = quota_policy

        if video_type is not None:
            vm_doc_copy["video_type"] = video_type

        if guest_os_type is not None:
            vm_doc_copy["guest_os_type"] = guest_os_type

        if vm_version is not None:
            vm_doc_copy["vm_version"] = vm_version

        if vm_version_mode is not None:
            vm_doc_copy["vm_version_mode"] = vm_version_mode

        check_infos = {
            ValidatorChain.check.vm_memory_size: {},
            ValidatorChain.check.cpu_exclusive: {
                "origin_expected_enabled": self.vm_doc.get("cpu_exclusive", {}).get("expected_enabled", False),
                "origin_vcpu_count": self.vm_doc["vcpu"],
            },
        }

        if cpu_model is not None:
            vm_doc_copy["cpu_model"] = cpu_model
            # VMs that already have a cpu model that is incompatible are not affected
            check_infos.update({ValidatorChain.check.cpu_model_compatible: {}})

        if cpu_qos is not None:
            # reservation_enabled fields can only be changed by the scheduler results,
            # and the original or default values need to be retained here
            vm_cpu_qos = {
                "shares": cpu_qos["shares"],
                "reservation_hz": cpu_qos["reservation_hz"],
                "limit_hz": cpu_qos["limit_hz"],
                "reservation_enabled": (
                    vm_doc_copy["cpu_qos"].get("reservation_enabled")
                    if vm_doc_copy.get("cpu_qos")
                    else elf_common_constants.DEFAULT_CPU_QOS_RESERVATION_ENABLE
                ),
            }
            vm_doc_copy["cpu_qos"] = vm_cpu_qos

            check_infos.update({ValidatorChain.check.cpu_qos: {}})
            if (
                vm_doc_copy["status"] == elf_common_constants.VM_RUNNING
                and vm_doc_copy["cpu_qos"]["reservation_hz"] != elf_common_constants.DEFAULT_CPU_QOS_RESERVATION_HZ
            ):
                # HOT_ADD is necessary because it is used by the memory filter in the scheduler.
                # the CPU qos filter does not use it.
                check_infos.update(
                    {
                        ValidatorChain.check.node_avaliable: {
                            "node_ip": vm_doc_copy["node_ip"],
                            "op_context": OperationContext.HOT_ADD,
                        }
                    }
                )

        ValidatorChain(vm_doc_copy).validate(check_infos)

        return self.submit_job(API_VM_EDIT, {vm_doc_copy["uuid"]: vm_doc_copy}, event=event)

    def submit_edit_nics(self, nics, user=None):
        from smartx_app.elf.common.utils.dhcp import fill_in_ip_for_vms
        from smartx_app.elf.common.utils.validator import ValidatorChain

        vm_doc_copy = copy.deepcopy(self.vm_doc)

        # fill info to current nics
        # fill IP from DHCP
        fill_in_ip_for_vms([vm_doc_copy])
        # fill IP from VMTools
        VMToolsWrapper.apply_nics(vm_doc_copy)

        current_nics = vm_doc_copy["nics"]

        # fill info to new nics
        network.restore_vlan_nic(current_nics, nics)
        network.restore_vpc_nic(current_nics, nics)
        network.ensure_nic(nics)

        # fill vlan info
        vm_doc_copy["nics"] = nics
        network.ensure_vlan_for_nics(vm_doc_copy["nics"])

        event = VMEventWrapper(user=user).event_vm_edit_nics(self.vm_doc, current_nics, nics)
        check_item = {
            ValidatorChain.check.ip_available: None,
            ValidatorChain.check.is_valid_vlan: {},
            ValidatorChain.check.nic_mac_address: {},
            ValidatorChain.check.vnic_qos: {},
            ValidatorChain.check.nic_configuration_update_valid: {},
            ValidatorChain.check.vpc_nic_creation: {},
        }

        # Active VMs could change PF of an SR-IOV nic, so use (mac_address, pf_id) as the identity of an SR-IOV nic.
        expected_sriov_nics = {
            (nic["mac_address"], nic["pf_id"]): nic for nic in nics if nic.get("model") == VM_INTERFACE_SRIOV
        }
        if expected_sriov_nics:
            check_item.update({ValidatorChain.check.nic_sriov: None})

        if self.vm_doc["status"] in VM_ACTIVE_STATES:
            expected_sriov_nics_ids = expected_sriov_nics.keys()
            current_sriov_nics_ids = [
                (nic["mac_address"], nic["pf_id"])
                for nic in self.vm_doc["nics"]
                if nic.get("model") == VM_INTERFACE_SRIOV
            ]
            newly_added_sriov_nics_mac_address = [
                mac for mac in expected_sriov_nics_ids if mac not in current_sriov_nics_ids
            ]
            if newly_added_sriov_nics_mac_address:
                check_item.update(
                    {
                        ValidatorChain.check.only_current_node: {"op_context": OperationContext.HOT_ADD},
                        ValidatorChain.check.state_allowed: [VM_RUNNING],
                    },
                )

        # VM with unknown status only allow detaching all sriov nics,
        # so it is forbidden to pass sriov nics in vm_doc_copy.
        if self.vm_doc["status"] == VM_UNKNOWN:
            check_item.update({ValidatorChain.check.forbidden_sriov: {}})

        ValidatorChain(vm_doc_copy).validate(check_item)

        return self.submit_job(API_VM_EDIT_NIC, {vm_doc_copy["uuid"]: vm_doc_copy}, event=event)

    @staticmethod
    def get_devs_by_types(devs: list, dev_types: tuple):
        return [d for d in devs if d["type"] in dev_types]

    @staticmethod
    def _is_shareable_hostdev(d) -> bool:
        return d["type"] in (VGPU, *elf_common_constants.SUPPORTED_MDEVS)

    def _handle_shareable_hostdevs(self, expect_hostdevs, current_hostdevs) -> tuple[list, list]:
        diff_handlers = {
            VGPU: self._diff_vgpus,
        }

        for dtype in elf_common_constants.SUPPORTED_MDEVS:
            diff_handlers[dtype] = self._diff_mdevs

        total_attached = []
        total_detached = []

        for dev_type in diff_handlers:
            expect_devs = self.get_devs_by_types(expect_hostdevs, (dev_type))
            current_devs = self.get_devs_by_types(current_hostdevs, (dev_type))
            attached_devs, detached_devs = diff_handlers[dev_type](dev_type, expect_devs, current_devs)

            total_attached.extend(attached_devs)
            total_detached.extend(detached_devs)

        return total_attached, total_detached

    def submit_edit_hostdevs(self, hostdevs, user=None, user_type="USER", description=API_VM_EDIT_DEVICE):
        from smartx_app.elf.common.utils.validator import ValidatorChain

        vm_doc_copy = copy.deepcopy(self.vm_doc)
        pci_device_info_cache = pci_device_cache.PCIDeviceInfoCache()

        # step 1: Split into hostdevs that need to be detached and attached
        # Currently 2 hostdev types:
        # Exclusive devs: pass-through GPU, pass-through USB, over-network USB, pass-through NIC
        # Shareable devs: vGPU and MDEV(only MDEV_HCT for now)
        #
        # For exclusive devs, each device has a unique uuid. Attached devices and
        # detached devices and be obtained by comparing uuid.
        # For shareable dev, which includes mdev, each dev doesn't have a uuid. In order to
        # get attached and detached devs, we have to compare physical dev uuid, type_id and
        # count of each type.
        #
        # Here we deal with shareable and exclusive devices separately.
        current_hostdevs = vm_doc_copy.get("hostdevs", [])
        expect_uuids = {d["uuid"] for d in hostdevs if not self._is_shareable_hostdev(d)}
        current_uuids = {d["uuid"] for d in current_hostdevs if not self._is_shareable_hostdev(d)}

        # ==== Exclusive hostdevs ====
        attach_hostdevs = [d for d in hostdevs if not self._is_shareable_hostdev(d) and d["uuid"] not in current_uuids]
        detach_hostdevs = [
            d for d in current_hostdevs if not self._is_shareable_hostdev(d) and d["uuid"] not in expect_uuids
        ]

        # ==== Sharable hostdevs ====
        expect_shdevs = [d for d in hostdevs if self._is_shareable_hostdev(d)]
        current_shdevs = [d for d in current_hostdevs if self._is_shareable_hostdev(d)]

        attached_shdevs, detached_shdevs = self._handle_shareable_hostdevs(expect_shdevs, current_shdevs)
        attach_hostdevs.extend(attached_shdevs)
        detach_hostdevs.extend(detached_shdevs)

        # step 2: edit and check hostdevs of vm_doc
        check_items = self._gen_check_items_for_hostdevs_edit(
            hostdevs, attach_hostdevs, detach_hostdevs, vm_doc_copy["status"], pci_device_info_cache
        )
        vm_doc_copy["hostdevs"] = hostdevs
        ValidatorChain(vm_doc_copy).validate(check_items)
        resources = {vm_doc_copy["uuid"]: vm_doc_copy}

        # step 3:  event audit
        attach_hostdevs_names = self._gen_hostdevs_names_for_event(attach_hostdevs, vm_doc_copy, pci_device_info_cache)
        detach_hostdevs_names = self._gen_hostdevs_names_for_event(detach_hostdevs, vm_doc_copy, pci_device_info_cache)

        event = VMEventWrapper(user=user, user_type=user_type).event_vm_edit_devices(
            self.vm_doc,
            attached_devices_names=attach_hostdevs_names,
            detached_devices_names=detach_hostdevs_names,
            job_description=description,
        )

        return self.submit_job(description, resources, event=event)

    @staticmethod
    def submit_one_time_task(description, one_time_task=None, event=None):
        from job_center.handler.leader import workers

        return {
            "job_id": workers.job_submit(user="", description=description, one_time_task=one_time_task, event=event)
        }

    @staticmethod
    def _diff_vgpus(_, expected_vgpus, current_vgpus) -> tuple[list, list]:
        """Compare expected vGPUs and current vGPUs, get attached vGPUs and detached vGPUs"""

        def group_by_gpu_uuid(vgpus):
            """
            Group vGPUs by GPU uuid
            :param vgpus: vGPU list to be grouped
            :return: A dict, key is gpu_uuid, value format is {"vgpu_type_id": "nvidia-322", "count": 3}
            """
            gpu_uuid_to_vgpu_info = {}
            for x in vgpus:
                if x["gpu_uuid"] not in gpu_uuid_to_vgpu_info:
                    gpu_uuid_to_vgpu_info[x["gpu_uuid"]] = {"vgpu_type_id": x["vgpu_type_id"], "count": 1}
                else:
                    gpu_uuid_to_vgpu_info[x["gpu_uuid"]]["count"] += 1

            return gpu_uuid_to_vgpu_info

        def group_diff(first_vgpu_group_info, second_vgpu_group_info):
            """
            Compare two vGPU group info and get vGPUs in the first group but no in the second group
            :param first_vgpu_group_info:  result from group_by_gpu_uuid()
            :param second_vgpu_group_info: result from group_by_gpu_uuid()
            :return:
            """
            ret = []
            for gpu_uuid, first_info in list(first_vgpu_group_info.items()):
                second_info = second_vgpu_group_info.get(gpu_uuid)
                if not second_info or first_info["vgpu_type_id"] != second_info["vgpu_type_id"]:
                    ret.extend(
                        list(
                            itertools.repeat(
                                {"gpu_uuid": gpu_uuid, "type": VGPU, "vgpu_type_id": first_info["vgpu_type_id"]},
                                first_info["count"],
                            )
                        )
                    )
                elif first_info["count"] > second_info["count"]:
                    ret.extend(
                        list(
                            itertools.repeat(
                                {"gpu_uuid": gpu_uuid, "type": VGPU, "vgpu_type_id": first_info["vgpu_type_id"]},
                                first_info["count"] - second_info["count"],
                            )
                        )
                    )
            return ret

        expected_vgpu_group = group_by_gpu_uuid(expected_vgpus)
        current_vgpu_group = group_by_gpu_uuid(current_vgpus)
        attached_vgpus = group_diff(expected_vgpu_group, current_vgpu_group)
        detached_vgpus = group_diff(current_vgpu_group, expected_vgpu_group)

        return attached_vgpus, detached_vgpus

    @staticmethod
    def _diff_mdevs(dev_type, expected_mdevs, current_mdevs) -> tuple[list, list]:
        def group_by_pdev_id(mdevs):
            """
            Group mdev by pdev uuid
            :param mdevs: list of mdevs
            :return: A dict, key is pdev_uuid, value format is {"mdev_type_id": "xxxx", "count": 3}
            """
            pdev_uuid_to_mdev_info = {}
            for x in mdevs:
                if x["uuid"] not in pdev_uuid_to_mdev_info:
                    pdev_uuid_to_mdev_info[x["uuid"]] = {"mdev_type_id": x["mdev_type_id"], "count": 1}
                else:
                    pdev_uuid_to_mdev_info[x["uuid"]]["count"] += 1

            return pdev_uuid_to_mdev_info

        def group_diff(first_mdev_group_info, second_mdev_group_info, dev_type):
            """
            first_mdev_group_info - second_mdev_group_info
            :param first_mdev_group_info:  result from group_by_gpu_uuid()
            :param second_mdev_group_info: result from group_by_gpu_uuid()
            :return:
            """
            ret = []
            for pdev_uuid, first_info in list(first_mdev_group_info.items()):
                second_info = second_mdev_group_info.get(pdev_uuid)
                if not second_info or first_info["mdev_type_id"] != second_info["mdev_type_id"]:
                    ret.extend(
                        list(
                            itertools.repeat(
                                {"uuid": pdev_uuid, "type": dev_type, "mdev_type_id": first_info["mdev_type_id"]},
                                first_info["count"],
                            )
                        )
                    )
                elif first_info["count"] > second_info["count"]:
                    ret.extend(
                        list(
                            itertools.repeat(
                                {"uuid": pdev_uuid, "type": dev_type, "mdev_type_id": first_info["mdev_type_id"]},
                                first_info["count"] - second_info["count"],
                            )
                        )
                    )
            return ret

        expected_mdev_group = group_by_pdev_id(expected_mdevs)
        current_mdev_group = group_by_pdev_id(current_mdevs)
        attached_mdevs = group_diff(expected_mdev_group, current_mdev_group, dev_type)
        detached_mdevs = group_diff(current_mdev_group, expected_mdev_group, dev_type)

        return attached_mdevs, detached_mdevs

    def _gen_hostdevs_names_for_event(self, hostdevs, vm_doc_copy, pci_device_info_cache):
        """Generate names of hostdevs for audit event
        :param hostdevs: hostdev list
        :param vm_doc_copy: vm_json
        :param pci_device_info_cache: Cache to get PCI device info
        :return: A list containing names of hostdevs
        """
        hostdevs_names = []
        for hostdev in hostdevs:
            if hostdev.get("type") in (PASS_THROUGH_GPU, PASS_THROUGH_NIC, PASS_THROUGH_PCI, PASS_THROUGH_VF_PCI):
                pci_device_info = pci_device_info_cache.get_pci_device_info(
                    vm_doc_copy["node_ip"], hostdev["uuid"], hostdev["type"]
                )
                # When PCI device is unmounted on the host, device info cannot be queried. Use device_uuid as the name.
                pci_device_name = hostdev["uuid"]
                if pci_device_info:
                    pci_device_name = "{}({})".format(pci_device_info.get("name"), hostdev["uuid"])
                hostdevs_names.append(pci_device_name)

            elif hostdev.get("type") in (PASS_THROUGH_USB, OVER_NETWORK_USB):
                # request the TUNA interface to update the usb base Info
                # in order to get the name of the usb
                usb_device = usb.create_usb(hostdev["uuid"], hostdev["type"], hostdev["host_id"])
                try:
                    usb_device.update_base_info()
                except:
                    logging.warning("Get usb({}) base info failed.".format(usb_device.usb_id))
                usb_info = usb_device.dump()
                hostdevs_names.append("{}({})".format(usb_info.get("name"), hostdev["uuid"]))

            elif hostdev.get("type") == VGPU:
                gpu_info = pci_device_info_cache.get_pci_device_info(
                    vm_doc_copy["node_ip"], hostdev["gpu_uuid"], PASS_THROUGH_GPU
                )
                # When GPU of the vGPU is unmounted on the host, GPU info cannot be queried. vGPU name contains only
                # GPU uuid.
                vgpu_name = "VGPU {}({})".format(hostdev["vgpu_type_id"], hostdev["gpu_uuid"])
                if gpu_info:
                    vgpu_name = "VGPU {}({} {})".format(
                        hostdev["vgpu_type_id"], gpu_info.get("name"), hostdev["gpu_uuid"]
                    )
                hostdevs_names.append(vgpu_name)

        return hostdevs_names

    def _gen_check_items_for_hostdevs_edit(
        self, hostdevs, attach_hostdevs, detach_hostdevs, vm_status, pci_device_info_cache
    ):
        from smartx_app.elf.common.utils.validator import ValidatorChain

        check_items = {}

        # If PCI devices exist in hostdevs, confirm that these devices are online
        expected_pci_devices = [
            d
            for d in hostdevs
            if d.get("type") in (PASS_THROUGH_GPU, PASS_THROUGH_NIC, PASS_THROUGH_PCI, PASS_THROUGH_VF_PCI)
        ]
        if expected_pci_devices:
            check_items.update({ValidatorChain.check.pci_hostdevs_state: pci_device_info_cache})

        expected_vgpus = [d for d in hostdevs if d.get("type") == VGPU]
        if expected_vgpus:
            check_items.update({ValidatorChain.check.vgpu: pci_device_info_cache})

        # If any PCI Device is mounted/unmounted, the VM must be in 'stopped' status
        attached_pci_devices = [
            attach_hostdev["uuid"]
            for attach_hostdev in attach_hostdevs
            if attach_hostdev["type"] in (PASS_THROUGH_GPU, PASS_THROUGH_NIC, PASS_THROUGH_PCI, PASS_THROUGH_VF_PCI)
        ]
        detached_pci_devices = [
            detach_hostdev["uuid"]
            for detach_hostdev in detach_hostdevs
            if detach_hostdev["type"] in (PASS_THROUGH_GPU, PASS_THROUGH_NIC, PASS_THROUGH_PCI, PASS_THROUGH_VF_PCI)
        ]

        attached_shdevs = [d for d in attach_hostdevs if self._is_shareable_hostdev(d)]
        detached_shdevs = [d for d in detach_hostdevs if self._is_shareable_hostdev(d)]

        if (attached_pci_devices or detached_pci_devices) or (attached_shdevs or detached_shdevs):
            check_items.update({ValidatorChain.check.state_allowed: [VM_STOPPED]})

        ha_unsupported_hostdev = [d for d in hostdevs if d.get("type") not in elf_common_constants.HA_SUPPORTED_HOSTDEV]
        if ha_unsupported_hostdev:
            check_items.update({ValidatorChain.check.ha_state: False})

        # VM with unknown status only allow detaching all hostdevs,
        # so it is forbidden to pass hostdevs in vm_doc_copy.
        if vm_status == VM_UNKNOWN:
            check_items = {ValidatorChain.check.hostdev_exists: False}

        return check_items

    def _get_del_volumes(self, disk_paths_del):
        disk_paths_current = [disk["path"] for disk in self.disks]
        disk_paths_del_valid = [path for path in disk_paths_del if path in disk_paths_current]

        if len(disk_paths_del_valid) != len(disk_paths_del):
            logging.warning(
                "Try to delete volumes({}) that aren't mounting on the vm.".format(
                    ", ".join([path for path in disk_paths_del if path not in disk_paths_del_valid])
                )
            )

        if disk_paths_del_valid:
            valid_vols = VolumeWrapper.get_mountable_by_paths(disk_paths_del_valid, vm_uuid=self.vm_doc.get("uuid"))
            return [vol.delete() for vol in valid_vols if not getattr(vol, "sharing", False)]
        else:
            return []

    def _get_del_volumes_by_uuids(self, volume_uuids_del, delete_volumes_permanently=True):
        volume_uuids_current = [disk["volume_uuid"] for disk in self.disks]
        volume_uuids_del_valid = [x for x in volume_uuids_del if x in volume_uuids_current]

        if len(volume_uuids_del_valid) != len(volume_uuids_del):
            logging.warning(
                "Try to delete volumes({}) that aren't mounting on the vm.".format(
                    ", ".join([x for x in volume_uuids_del if x not in volume_uuids_del_valid])
                )
            )

        if volume_uuids_del_valid:
            valid_vols = VolumeWrapper.get_mountable_by_volume_uuid_list(
                volume_uuids_del_valid, vm_uuid=self.vm_doc.get("uuid")
            )
            return [
                vol.delete(delete_permanently=delete_volumes_permanently)
                for vol in valid_vols
                if not getattr(vol, "sharing", False)
            ]
        else:
            return []

    def _get_del_volume_snapshots(self, disk_vol_uuids):
        if not disk_vol_uuids:
            return []
        snapshots = VolumeSnapshotWrapper.get_by_volume_uuid(disk_vol_uuids)
        resources = []
        for s in snapshots:
            r = s.delete()
            resources.append(r)
        return resources

    def submit_edit_disks(self, raw_disks, user=None, disk_paths_del=None, quota_policy=None):
        from smartx_app.elf.common.utils.validator import ValidatorChain

        if len(raw_disks) == 0:
            raise ValueError("`raw_disks` can not be empty.")

        # check if it is a mount/umount svt iso job
        job_name = VMToolsWrapper.get_job_name(self.vm_doc, updated_disks=raw_disks, deleted_disks=disk_paths_del)
        if not job_name:
            job_name = API_VM_EDIT_VOL

        ValidatorChain(self).validate({ValidatorChain.check.storage_policy_for_disk: {"disks": raw_disks}})
        event = VMEventWrapper(user=user).event_vm_edit_disks(
            self.vm_doc, self.vm_doc["disks"], raw_disks, disk_paths_del, quota_policy
        )

        if self.vm_doc["status"] == VM_STOPPED:
            disk_util.ensure_cdrom_bus(raw_disks, "vm_edit_disks", res_uuid=self.vm_doc["uuid"])

        disks, volumes = self.build_disks(raw_disks, vm_uuid=self.vm_doc["uuid"])
        ValidatorChain([volume.dumps() for volume in volumes]).validate({ValidatorChain.check.resident_in_cache: {}})

        disk_paths_expected = [disk["path"] for disk in disks if disk["type"] == "disk"]
        disk_paths_del = disk_paths_del or []
        disk_paths_del_valid = [path for path in disk_paths_del if path not in disk_paths_expected]

        if len(disk_paths_del_valid) != len(disk_paths_del):
            logging.warning(
                "Try to delete volumes({}) that will mount on the vm.".format(
                    ", ".join([path for path in disk_paths_del if path not in disk_paths_del_valid])
                )
            )

        restore_serial(disks, self.vm_doc["disks"])

        vm_doc_copy = copy.deepcopy(self.vm_doc)
        vm_doc_copy["disks"] = disks

        vm_doc_copy["quota_policy"] = quota_policy

        resources = {vm_doc_copy["uuid"]: vm_doc_copy}

        resources.update({vol.uuid: vol.dumps() for vol in volumes})
        resources.update({vol["uuid"]: vol for vol in self._get_del_volumes(disk_paths_del_valid)})

        return self.submit_job(job_name, resources, event=event)

    def _get_node_used_node_used_memory(self):
        cursor = self._query_from_db(
            {
                "node_ip": self.vm_doc["node_ip"],
                "uuid": {"$ne": self.vm_doc["uuid"]},  # exclude the current vm
                "status": {"$in": VM_ACTIVE_STATES},
            },
            {"memory": 1},
        )
        return sum(vm["memory"] for vm in cursor)

    def _get_node_total_memory(self):
        from smartx_app.common.node.db import query_host_by_data_ip

        host = query_host_by_data_ip(self.vm_doc["node_ip"])
        if host is None:
            raise ResourceException("Node({}) is not found.".format(self.vm_doc["node_ip"]), py_error.NODE_NOT_FOUND)

        return host["memory"]["total"]

    def submit_start_preview(self):
        api_name, resources = self._submit_action_preview(API_VM_START)

        vm_uuid = self.vm_doc["uuid"]
        vm_resource_json = resources[vm_uuid]
        ensure_cdrom_bus(vm_resource_json["disks"], "vm_start", res_uuid=vm_uuid)

        return api_name, resources

    def submit_reboot_preview(self, force=False, screenshot_file_uuid=None):
        api_name, resources = self._submit_action_preview(API_VM_FORCE_REBOOT if force else API_VM_REBOOT)

        if screenshot_file_uuid:
            resources[self.vm_doc["uuid"]]["screenshot_file_uuid"] = screenshot_file_uuid

        return api_name, resources

    def submit_stop_preview(self, force=False):
        api_name = API_VM_FORCE_STOPPED if force else API_VM_STOPPED
        return self._submit_action_preview(api_name)

    def submit_pause_preview(self):
        return self._submit_action_preview(API_VM_PAUSE)

    def submit_resume_preview(self):
        return self._submit_action_preview(API_VM_RESUME)

    def submit_delete_preview(
        self,
        include_volumes=False,
        include_volume_snapshots=False,
        include_vm_snapshots=False,
        delete_volumes_permanently=True,
    ):
        vm_doc_copy = copy.deepcopy(self.vm_doc)
        vm_doc_copy.update({"status": VM_DELETED, "resource_state": RESOURCE_REMOVED})

        resources = {vm_doc_copy["uuid"]: vm_doc_copy}
        if include_volumes:
            vols = self._get_del_volumes_by_uuids(
                [disk["volume_uuid"] for disk in self.disks], delete_volumes_permanently=delete_volumes_permanently
            )

            resources.update({vol["uuid"]: vol for vol in vols})
            if include_volume_snapshots:
                resources.update(
                    {snp["uuid"]: snp for snp in self._get_del_volume_snapshots([vol["uuid"] for vol in vols])}
                )
        if include_vm_snapshots:
            vss = list(VMSnapshotWrapper.bulk_delete_from_vm_uuids([vm_doc_copy["uuid"]]).values())
            if vss:
                resources.update(vss[0])
        return API_VM_DELETE, resources

    @classmethod
    def vms_export(cls, file_name, value, filter_criteria, local_language="en-US"):
        if local_language not in elf_common_constants.SUPPORT_LANGUAGE:
            local_language = "en-US"
        field_definitions = [
            {"label": elf_common_constants.VM_EXPORT_INFO["name"][local_language], "field": "vm_name"},
            {"label": elf_common_constants.VM_EXPORT_INFO["uuid"][local_language], "field": "uuid"},
            {"label": elf_common_constants.VM_EXPORT_INFO["ip"][local_language], "field": "vm_ip"},
            {"label": elf_common_constants.VM_EXPORT_INFO["status"][local_language], "field": "status"},
            {"label": elf_common_constants.VM_EXPORT_INFO["guest_os"][local_language], "field": "os_version"},
            {"label": elf_common_constants.VM_EXPORT_INFO["ha"][local_language], "field": "ha"},
            {"label": elf_common_constants.VM_EXPORT_INFO["boot_with_host"][local_language], "field": "boot_with_host"},
            {"label": elf_common_constants.VM_EXPORT_INFO["vmtools"][local_language], "field": "vmtools"},
            {"label": elf_common_constants.VM_EXPORT_INFO["vCPU"][local_language], "field": "vcpu"},
            {"label": elf_common_constants.VM_EXPORT_INFO["memory"][local_language], "field": "memory"},
            {"label": elf_common_constants.VM_EXPORT_INFO["host"][local_language], "field": "node_ip"},
            {"label": elf_common_constants.VM_EXPORT_INFO["firmware"][local_language], "field": "firmware"},
            {"label": elf_common_constants.VM_EXPORT_INFO["disks"][local_language], "field": "disks"},
            {"label": elf_common_constants.VM_EXPORT_INFO["nics"][local_language], "field": "nics"},
            {"label": elf_common_constants.VM_EXPORT_INFO["description"][local_language], "field": "description"},
            {
                "label": elf_common_constants.VM_EXPORT_INFO["create_time"][local_language],
                "field": lambda x: datetime.utcfromtimestamp(x["create_time"]).isoformat(),
            },
            {"label": elf_common_constants.VM_EXPORT_INFO["folder"][local_language], "field": "folder_name"},
        ]
        if value:
            vms_info = cls.search(text=value, filter_criteria=filter_criteria)["entities"]
        else:
            projection = {
                "uuid": 1,
                "vm_name": 1,
                "status": 1,
                "ha": 1,
                "boot_with_host": 1,
                "vcpu": 1,
                "memory": 1,
                "node_ip": 1,
                "firmware": 1,
                "disks": 1,
                "nics": 1,
                "create_time": 1,
                "description": 1,
                "type": 1,
                "_id": 0,
            }
            cr = CriteriaResolver(
                sort_fields_support=None,
                filter_fields_locked=("super_type", "type", "resource_state"),
                projection_fields_omitted=("_id",),
            )
            query = cr.resolve_filter_criteria(filter_criteria)
            if "folder_uuid" in query:
                folder_uuid = query.pop("folder_uuid")
                vms = Folder.load(folder_uuid).relation()["vm_uuids"]
                if vms:
                    query["uuid"] = {"$in": vms}
            vms_info = VMQuerier().get_vms(query, projection)
            VMToolsWrapper.apply_guest_infos(*vms_info)
            list(map(cls.fit_firmware, vms_info))

        disks = []
        for vm in vms_info:
            disks.extend(vm.get("disks"))
        disks_path = [disk["path"] for disk in disks]
        volumes = {x["path"]: x for x in VolumeWrapper.query_from_db(paths=disks_path)}
        images = {
            x["path"]: x
            for x in list(
                cls.db.resource.find(
                    {"type": ISO_IMAGE, "resource_state": RESOURCE_IN_USE}, {"_id": 0, "path": 1, "name": 1}
                )
            )
        }
        vlans = {x["uuid"]: x for x in get_vlan_all()}
        Folder.apply_folder_names(vms_info)

        uuids = [v["uuid"] for v in vms_info]
        result = Attribute.get_resources_binding(KVM_VM, uuids)
        resource_attribute_map = {}
        keys = []
        for u in result:
            attributes = {}
            for a in u["attributes"]:
                attributes[a["key"]] = a["value"]
                keys.append(a["key"])
            resource_attribute_map[u["uuid"]] = attributes

        keys = list(set(keys))
        field_definitions += [{"label": i, "field": i} for i in keys]

        for x in vms_info:
            x["ha"] = "on" if x["ha"] else "off"
            x["boot_with_host"] = "on" if x["boot_with_host"] else "off"
            x["memory"] = float(x["memory"]) / 1024 / 1024 / 1024
            disk_info = ""
            nic_info = ""
            ips = []
            ip_addresses = []
            for disk in x["disks"]:
                if disk["type"] == "disk":
                    if not volumes.get(disk["path"]):
                        disk_info += " -;"
                        continue
                    if volumes[disk["path"]].get("sharing"):
                        disk_info += "shared volume "
                    else:
                        disk_info += "volume "
                    disk_info += (
                        "| "
                        + volumes[disk["path"]]["name"]
                        + " | "
                        + volumes[disk["path"]]["uuid"]
                        + " | "
                        + disk["bus"]
                        + " | "
                        + str(volumes[disk["path"]]["size_in_byte"] >> 30)
                        + " GiB;"
                    )
                else:
                    disk_info += "CDROM | "
                    cdrom_name = images[disk["path"]].get("name", "-") if images.get(disk["path"]) else "-"
                    disk_info += cdrom_name + ";"
            x["disks"] = disk_info
            for nic in x["nics"]:
                nic_info += nic["mac_address"] + " | " + vlans[nic["vlan_uuid"]]["name"] + ";"
            for guest_nic in x["guest_info"]["nics"]:
                ip_addresses += guest_nic["ip_addresses"]
            for ip in ip_addresses:
                if ip["ip_address_type"] == "ipv4" and ip["ip_address"] != "127.0.0.1":
                    ips.append(ip["ip_address"])
            x["nics"] = nic_info
            x["os_version"] = x["guest_info"]["os_version"] if x["guest_info"]["os_version"] else "-"
            x["vm_ip"] = "-" if len(ips) == 0 else " |".join(ips)
            x["vmtools"] = "enable" if x["guest_info"]["ga_state"] == elf_common_constants.SVT_ACTIVE else "disable"
            x.pop("guest_info")
            attributes = resource_attribute_map.get(x["uuid"], {})
            x.update(attributes)
        convertor = ResourceConvertor(vms_info)
        return convertor.csv_response(file_name, field_definitions)

    @classmethod
    def vm_name_unique_check(cls, vm_name, is_raise=True):
        docs = VMQuerier().get_vms({"vm_name": vm_name}, None)
        if not docs:
            job_vm_names = []
            job = mongodb[JOB_DB_NAME].job.find(
                {
                    "description": {
                        "$in": [
                            API_VM_SNAPSHOT_REBUILD,
                            API_VM_CLONE,
                            API_VM_TEMPLATE_VM_CREATE,
                            API_VM_EDIT,
                            API_VM_CREATE,
                        ]
                    },
                    "type": JOB_TYPE_ACTION,
                    "state": {"$in": [JOB_PROCESSING, JOB_PENDING]},
                },
                {"_id": 0, "resources": 1},
            )
            for j in job:
                for r in list(j["resources"].values()):
                    if r.get("type") == KVM_VM:
                        name = r.get("vm_name")
                        if name:
                            job_vm_names.append(name)
            if vm_name not in job_vm_names:
                return True
        if is_raise:
            raise ResourceException(
                "A VM with the same name({}) already exists.".format(vm_name), py_error.VM_DUPLICATED_NAME
            )
        else:
            return False

    @classmethod
    def check_vm_uuid_exists(cls, vm_uuid):
        if list(cls._query_from_db({"uuid": vm_uuid})) or jc_utils.check_resource_lock(vm_uuid):
            raise ResourceException(
                "A VM with the same uuid({}) already exists.".format(vm_uuid), py_error.VM_DUPLICATED_UUID
            )

    def has_sharing_volume(self):
        paths = [disk["path"] for disk in self.vm_doc["disks"]]
        volumes = [x for x in VolumeWrapper.query_from_db(paths=paths)]
        share_volume = [x for x in volumes if x["sharing"]]
        if len(share_volume) == 0:
            return False
        else:
            return True

    def get_schedule_node_ip(self):
        from smartx_app.elf.scheduler.client import (
            NoAvailableHostException,
            ScheduleException,
            get_auto_schedule_result,
        )

        try:
            schedule_result = get_auto_schedule_result(vm_json_expected=self.vm_doc, pre_allocated=False)
        except NoAvailableHostException as exp:
            logging.exception("Get auto schedule IP failed.")
            error_codes = {
                "HostAvailableMemoryFilter": py_error.NODE_OUT_OF_MEMORY,
                "HostCPUCompatibilityFilter": (py_error.PRECHECK_VMS_CPU_INCOMPATIBLE),
                "PlacementGroupFilter": (py_error.PRECHECK_PLACEMENT_GROUPS_UNMATCH),
                "NetworkFilter": py_error.PRECHECK_VMS_NETWORK_UNMTCH,
                "OnlineHostFilter": py_error.PRECHECK_HOST_UNAVAILABLE,
                "MigratableFilter": py_error.VM_IS_NOT_MIGRATABLE,
                "AsiainfoGVMMustFilter": py_error.PRECHECK_ASIAINFO_AM_REQUIREMENTS_NOT_MET,
            }
            error_code = error_codes.get(exp.latest_filter_name, py_error.VM_SCHEDULE_FAILED)
            raise ResourceException("vm {} auto schedule failed.".format(self.vm_doc["uuid"]), error_code)
        except ScheduleException as exp:
            logging.exception("Get auto schedule IP failed.")
            raise ResourceException("vm {} auto schedule failed.".format(self.vm_doc["uuid"]), exp.user_code)
        else:
            logging.info("Schedule VM({}) to host({}).".format(self.vm_doc["uuid"], schedule_result["host_data_ip"]))
            return schedule_result["host_data_ip"]

    def _deletet_vm_related_info(self, vm):
        delete_ip_info = {"uuid": vm["uuid"], "status": VM_DELETED, "nics": vm.get("nics")}
        delete_ip_for_vm(delete_ip_info)
        VMAdditionalInfoWrapper.remove(vm["uuid"])

        try:
            cloud_init.remove_config_drive_iso(vm["uuid"])
        except Exception:
            logging.exception("Failed to remove the config drive iso for VM '%s'" % vm["uuid"])

        Attribute.batch_change_resource_type(KVM_VM, vm["uuid"], KVM_VM_TEMPLATE)
        vol_vm_rel.VolVMRelation.delete(vm_id=vm["uuid"], just_one=False)

    # TODO(Jingming): Clean the domain of a vm when converting it to a template (ELF-3797).
    def as_template(self, name, cloud_init_supported=False):
        """
        VM has fields that template does not have:
            u'auto_schedule',
            u'clock_offset',
            u'cluster_cpu_model',
            u'guest_cpu_model',
            u'last_shutdown_time',
            u'last_start_time',
            u'last_resume_time',
            u'node_ip',
            u'update_time',
            u'video_type',
            u'vm_name',
            u'win_opt',
            u'hostdevs'

        Template has fields but vm does not have:
            u'name', u'unique_size'

        Disks(nfs and iscsi as the same):
            VM's disk has but template's disk does not have:
                'volume_uuid'

            Template's disk has but vm's disk does not have:
                u'diff_size', u'export_name', u'size', u'volume_template_uuid'
        Nics:
            VM's nic has fields but template's nic does not have:
                u'pci_address', u"mac_address", u"interface_id", u"queues"

        Volume:
            ISCSI volume has fields but ISCSI volume template does not have:
                u'mounting', u'sharing', u'size_in_byte', u'super_type'

            ISCSI volume template has fields but ISCSI volume does not have:
                u'alloc_even', u'diff_size', 'unique_size', u'size'

            NFS volume has fields but NFS volume template does not have:
                u'mounting', u'path', u'sharing', u'size_in_byte', u'super_type'

            NFS volume template has fields but NFS volume does not have:
                u'alloc_even', u'diff_size', 'unique_size', u'export_name', u'volume_size', u'volume_uuid'

        Delete vm logic from smartx_app.elf.job_center.follower.kvm.vm.kvm_vm_delete

        :return:
        """

        # The VM may fail to release hostdevs when shutting down.
        # So, here is to ensure that the hostdevs has been correctly released on the TUNA side
        # before the VM is converted to a template.

        # Release operations should be before db operations
        # in case release failed and db cannot rollback.
        usb.USBHandler(self.vm_doc).release_unexpected_usbs_by_tuna_record()
        gpu.GPUHandler(self.vm_doc).release_unexpected_gpus_by_tuna_record()
        vgpu.VGPUHandler(self.vm_doc).release_unexpected_vgpus_by_tuna_record()
        pnic.PNICHandler(self.vm_doc).release_unexpected_pnics_by_tuna_record()
        normal_pci_device.NormalPCIDeviceHandler(self.vm_doc).release_unexpected_devices_by_tuna_record()
        sriov_vf.VFHandler(self.vm_doc).release_unexpected_vf_pci_devices_by_tuna_record()
        mdev.MDevHandler(self.vm_doc).release_unexpected_mdevs_by_tuna_record()

        def _get_nics():
            vm_doc_copy = copy.deepcopy(self.vm_doc)
            resource.pop_nic_attr(
                vm_doc_copy,
                (
                    resource.VM_NIC_PCI_ADDRESS,
                    resource.VM_NIC_INTERFACE_ID,
                    resource.VM_NIC_MAC_ADDRESS,
                    resource.VM_NIC_QUEUES,
                ),
            )
            nics = sriov.exclude_sriov_nics(vm_doc_copy.get("nics", []))
            return nics

        vm = self.vm_doc
        vol_paths = [disk["path"] for disk in vm["disks"] if disk["type"] == "disk"]
        collection = self.db.resource
        bulk = collection.initialize_unordered_bulk_op()
        disks_map = {}

        count = 0
        for vol_path in vol_paths:
            try:
                vol = VolumeWrapper.convert_to_volume_template(vol_path)
            except ResourceException as e:
                logging.warning(str(e))
                continue

            disks_map[vol_path] = {
                "diff_size": vol["diff_size"],
                "volume_template_uuid": vol["uuid"],
                "export_name": vol.get("export_name"),
                "size": vol["size"],
            }

            # ELF-5421: disallow convertion between vm and template with resident volume
            # so `resident_in_cache` of all iscsi volumes in this vm should be `false`
            if "resident_in_cache" in vol:
                disks_map[vol_path]["resident_in_cache"] = vol["resident_in_cache"]

            bulk.find({"uuid": vol["uuid"]}).replace_one(vol)
            count += 1
        if count:
            bulk.execute()

        disks = []
        for d in vm.get("disks", []):
            if d["type"] == "disk":
                converted = disks_map.get(d["path"])
                if converted:
                    d.update(converted)
                    d.pop("volume_uuid", None)
                    disks.append(d)
            else:
                # skip cloud init cdrom
                if cloud_init.is_config_drive_iso(d["path"]):
                    d["path"] = ""
                disks.append(d)

        template = VMTemplate(
            template_uuid=vm["uuid"],
            name=name,
            description=vm.get("description"),
            ha=vm.get("ha"),
            firmware=vm.get("firmware"),
            vcpu=vm.get("vcpu"),
            cpu=vm.get("cpu"),
            memory=vm.get("memory"),
            nics=_get_nics(),
            disks=disks,
            nested_virtualization=vm.get("nested_virtualization"),
            cpu_model=vm.get("cpu_model"),
            quota_policy=vm.get("quota_policy"),
            create_time=int(time.time()),
            clock_offset=vm.get("clock_offset"),
            video_type=vm.get("video_type"),
            win_opt=vm.get("win_opt"),
            cloud_init_supported=cloud_init_supported,
            guest_os_type=vm.get("guest_os_type", elf_common_constants.GuestOSType.UNKNOWN),
            sync_vm_time_on_resume=vm.get("sync_vm_time_on_resume", False),
            local_ha_policy=vm.get("local_ha_policy", elf_common_constants.LOCAL_HA_POLICY_DEFAULT),
        )

        collection.replace_one({"uuid": vm["uuid"]}, template.dumps())

        self._deletet_vm_related_info(vm)

    def has_local_hostdevs(self):
        # VM with local hostdevs or sr-iov nics must start at current node
        local_hostdevs = [d for d in self.vm_doc["hostdevs"] if d.get("type") in (PASS_THROUGH_USB, PASS_THROUGH_GPU)]
        sriov_nics = [nic for nic in self.vm_doc["nics"] if nic.get("model") == VM_INTERFACE_SRIOV]
        return local_hostdevs or sriov_nics

    def can_start_at_specified_node_after_schedule(self, node_ip):
        from smartx_app.elf.scheduler import client

        vm_json_expected = copy.deepcopy(self.vm_doc)
        vm_json_expected["status"] = VM_RUNNING
        try:
            result = client.get_auto_schedule_result(
                vm_json_expected, pre_allocated=False, preferred_hosts=[query_host_by_data_ip(node_ip)["host_uuid"]]
            )
        except Exception as e:
            raise ResourceException(
                "Check VM could start at current node after scheduler failed, error={}".format(e),
                py_error.VM_SCHEDULE_FAILED,
            )
        return result["host_data_ip"] == node_ip


def query_active_exclusive_vms_cpu_mapping(node_ip):
    return list(
        VMWrapper.fetch_all(
            {
                "status": {"$in": VM_ACTIVE_STATES},
                "cpu_exclusive.expected_enabled": True,
                "cpu_exclusive.actual_enabled": True,
                "node_ip": node_ip,
            },
            {"_id": 0, "uuid": 1, "cpu_exclusive": 1},
        )
    )


def query_all_active_exclusive_vms():
    return list(
        VMWrapper.fetch_all(
            {
                "status": {"$in": VM_ACTIVE_STATES},
                "cpu_exclusive.expected_enabled": True,
                "cpu_exclusive.actual_enabled": True,
            },
            {"_id": 0, "uuid": 1, "cpu_exclusive.vcpu_to_pcpu": 1, "node_ip": 1},
        )
    )


def query_active_shared_vms_cpu_mapping(node_ip):
    all_active_vms = list(
        VMWrapper.fetch_all(
            {"status": {"$in": VM_ACTIVE_STATES}, "node_ip": node_ip},
            {"_id": 0, "uuid": 1, "cpu_exclusive": 1},
        )
    )

    active_exclusive_vms = query_active_exclusive_vms_cpu_mapping(node_ip)
    exclusive_vms_uuid = [vm["uuid"] for vm in active_exclusive_vms]

    return [vm for vm in all_active_vms if vm["uuid"] not in exclusive_vms_uuid]


def query_bad_exclusive_vms_on_node(node_ip):
    return list(
        VMWrapper.fetch_all(
            {
                # Here we don't count VM status in VM_MIGRATING because
                # the VM may soon change to other node. Recover CPU
                # exclusive for it is meaningless.
                "status": {"$in": [VM_RUNNING, VM_SUSPENDED]},
                "cpu_exclusive.expected_enabled": True,
                "cpu_exclusive.actual_enabled": False,
                "node_ip": node_ip,
            },
            None,
        )
    )


def get_cpu_qos_reservation_disabled_vms_on_node(node_ip):
    return list(
        VMWrapper.fetch_all(
            {
                "node_ip": node_ip,
                "cpu_qos.reservation_enabled": False,
                "status": {"$in": VM_ACTIVE_STATES},
            },
            None,
        )
    )


def get_batch_schedule_node_ip(vms, pre_allocated=False):
    from smartx_app.elf.scheduler import client

    # Reduce the use of leader scheduler if no pre-allocated resources are needed
    if not pre_allocated:
        _client = client.Client(leader_required=False)
    else:
        _client = None

    with client.safe_batch_schedule(client=_client) as scheduler:
        try:
            for vm in vms:
                scheduler.add_vm(
                    vm_uuid=vm["uuid"],
                    vm_status=vm["status"],
                    memory=vm["memory"],
                    vcpu=vm["vcpu"],
                    cpu_model=vm["cpu_model"],
                    vlans=[x["vlan_uuid"] for x in vm["nics"]],
                    placement_groups=vm.get("placement_groups", []),
                    cpu_exclusive_expected_enabled=vm["cpu_exclusive"]["expected_enabled"],
                    cpu_reservation_hz=vm.get("cpu_qos", {}).get(
                        "reservation_hz", elf_common_constants.DEFAULT_CPU_QOS_RESERVATION_HZ
                    ),
                    migratable=vm.get("migratable", True),
                    anti_malware=vm.get("anti_malware", elf_common_constants.DEFAULT_ANTI_MALWARE),
                )
            # In the full copy scenario,
            # it is necessary to allow CPU exclusive degradation and CPU reservation disabled
            # to obtain the scheduling results that can support partial scheduling success.
            # This way, the batch scheduling of multiple VMs in the full copy scenario won't fail entirely.
            scheduler.schedule(
                pre_allocated=pre_allocated,
                partial_results=False,
                allow_cpu_exclusive_degrade=True,
                allow_cpu_reservation_disable=True,
            )
        except client.NoAvailableHostException as excp:
            error_codes = {
                "HostAvailableMemoryFilter": py_error.NODE_OUT_OF_MEMORY,
                "HostCPUCompatibilityFilter": py_error.PRECHECK_VMS_CPU_INCOMPATIBLE,
                "PlacementGroupFilter": py_error.PRECHECK_PLACEMENT_GROUPS_UNMATCH,
                "NetworkFilter": py_error.PRECHECK_VMS_NETWORK_UNMTCH,
                "OnlineHostFilter": py_error.PRECHECK_HOST_UNAVAILABLE,
                "MigratableFilter": py_error.VM_IS_NOT_MIGRATABLE,
                "AsiainfoGVMMustFilter": py_error.PRECHECK_ASIAINFO_AM_REQUIREMENTS_NOT_MET,
            }
            error_code = error_codes.get(excp.latest_filter_name, py_error.VM_SCHEDULE_FAILED)
            raise ResourceException(str(excp), error_code)
        except client.ScheduleException as excp:
            raise ResourceException(str(excp)[len(excp.short_message) :], excp.user_code)

        return scheduler.scheduled_ips


def _merge_vm_for_schedule(old_vm, new_vm, new_uuid):
    """
    Args:
        old_vm (dict): source VM info
        new_vm (dict): new VM info, VM from clone or template creation
                        and snapshot rebuilding may have only partial VM fields
        new_uuid (str): new VM uuid
    Returns:
        dict: merged VM info for schedule
                {
                    "uuid": "",
                    "status": "",
                    "memory": "",
                    "vcpu": {},
                    "cpu_model": "",
                    "nics": [],
                    "cpu_exclusive": {}
                }
    """

    key_list = ("memory", "vcpu", "cpu_model", "nics")
    temp_schedule_vm = {key: new_vm.get(key, old_vm[key]) for key in key_list}
    temp_schedule_vm.update({"placement_groups": new_vm.get("placement_groups", [])})

    if "cpu_exclusive" in new_vm:
        temp_schedule_vm["cpu_exclusive"] = new_vm["cpu_exclusive"]
    else:
        temp_schedule_vm["cpu_exclusive"] = old_vm.get("cpu_exclusive", elf_common_constants.DEFAULT_CPU_EXCLUSIVE_JSON)

    if "cpu_qos" in new_vm:
        temp_schedule_vm["cpu_qos"] = new_vm["cpu_qos"]
    else:
        temp_schedule_vm["cpu_qos"] = old_vm.get("cpu_qos", elf_common_constants.DEFAULT_CPU_QOS_JSON)

    # The VM created by default will be VM_STOPPED unless specified by the new VM
    temp_schedule_vm["status"] = new_vm.get("status", VM_STOPPED)

    temp_schedule_vm["uuid"] = new_uuid
    return temp_schedule_vm


def _get_all_host_faulty_ovsbrs_map() -> dict:
    from smartx_app.elf.common.utils import ha_related

    host_faulty_ovsbrs_map = {}
    for info in ha_related.get_all_hosts_heartbeat_info(show_keys=("faulty_ovsbrs", "data_ip", "host_uuid")):
        if not info.get("data_ip"):
            logging.warning("Unexpected host heartbeat info: {}".format(info))
            continue

        host_faulty_ovsbrs_map[info["data_ip"]] = info.get("faulty_ovsbrs", [])

    return host_faulty_ovsbrs_map


def prepare_node_ip_for_full_copy(old_resource, new_vms):
    """
    For create VMs that require full copy and are auto scheduled,
    each schedule a possible node for them

    If successful, auto_schedule will be set to False

    Args:
        old_resource (dict): source VM/template/snapshot info
        new_vms (list): new VM info, VM clone/snapshot rebuild/template create with partial VM fields
    """
    temp_schedule_vms = []
    temp_uuid_to_new_vm = {}
    for new_vm in new_vms:
        if not (new_vm.get("is_full_copy", False) and new_vm.get("auto_schedule")):
            continue

        # Use a temp uuid for schedule
        temp_uuid = str(uuid.uuid4())
        temp_schedule_vm = _merge_vm_for_schedule(old_resource, new_vm, temp_uuid)

        temp_schedule_vms.append(temp_schedule_vm)
        temp_uuid_to_new_vm[temp_uuid] = new_vm

    if temp_schedule_vms:
        node_ips = get_batch_schedule_node_ip(temp_schedule_vms, pre_allocated=False)

        for temp_uuid, new_vm in list(temp_uuid_to_new_vm.items()):
            new_vm["node_ip"] = node_ips[temp_uuid]
            new_vm["auto_schedule"] = False
            logging.info(
                "VM(name={}, temp_uuid={}) will be scheduled to node {}".format(
                    new_vm["vm_name"],
                    temp_uuid,
                    new_vm["node_ip"],
                )
            )


def query_vms_cpu_qos_on_node(node_ip=None, vm_states=None):
    query_cond = {}
    if node_ip is not None:
        query_cond["node_ip"] = node_ip
    if vm_states is not None:
        query_cond["status"] = {"$in": vm_states}

    return list(
        VMWrapper.fetch_all(query_cond=query_cond, fields_filter={"_id": 0, "uuid": 1, "cpu_qos": 1, "node_ip": 1})
    )
