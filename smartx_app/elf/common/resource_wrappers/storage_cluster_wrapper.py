# Copyright (c) 2013-2022, SMARTX
# All rights reserved.
from common.mongo.db import mongodb
from smartx_app.elf.common import code
from smartx_app.elf.common.resources import storage_cluster
from smartx_app.elf.common.utils import storage_cluster as sc_utils
from smartx_app.elf.job_center import constants as elf_jc_constants


class StorageClusterWrapper:
    db = mongodb.resources

    def submit_job(self, description, event=None):
        from job_center.handler.leader import workers

        res = self._resource.dumps()
        guard = sc_utils.ClusterHAHeartbeatStoragesGuard()
        res["_ha_heartbeat"] = (guard.storages, guard.version)
        resources = {res["uuid"]: res}

        return {"job_id": workers.job_submit(user="", description=description, resources=resources, event=event)}

    @classmethod
    def submit_create(cls, data, user=None):
        ins = cls(
            storage_cluster.StorageCluster(
                uuid=data["uuid"],
                name=data["name"],
                description=data.get("description", ""),
                access_info=data["access_info"],
                is_raw_password=True,
            )
        )
        return ins.submit_job(code.API_STORAGE_CLUSTER_CREATE)

    def __init__(self, resource):
        self._resource: storage_cluster.StorageCluster = resource

    def _dump_job_resources(self):
        r = self._resource.dumps()
        return {r["uuid"]: r}

    def submit_update(self, data):
        self._resource.update_access_info(
            data["access_info"]["ip"], data["access_info"]["username"], data["access_info"]["password"]
        )

        if "name" in data:
            self._resource.name = data["name"]
        if "description" in data:
            self._resource.description = data["description"]

        return self.submit_job(code.API_STORAGE_CLUSTER_UPDATE)

    def submit_delete(self):
        self._resource.status = elf_jc_constants.STORAGE_CLUSTER_DELETED
        return self.submit_job(code.API_STORAGE_CLUSTER_DELETE)
