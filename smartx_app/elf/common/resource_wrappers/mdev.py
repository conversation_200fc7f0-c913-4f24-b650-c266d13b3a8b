# Copyright (c) 2024 elfvirt, Inc.
# All rights reserved.
import copy
import functools
import logging
import uuid

from smartx_app.elf.common import constants as elf_constants
from smartx_app.elf.common.resource_wrappers import pci_device
from smartx_app.elf.common.resources import base
from smartx_app.elf.common.utils import vm_start
from smartx_proto.errors import pyerror_pb2 as py_error

"""
Mdev is the base class for mdev devices, future newly added
mdev devices, such as i915, ccw could re-use MDev and MDevHandler.

Supported MDev device should be defined in MDevHandler._MDEV_REGISTRY
along with its desired class.
"""

# =========== MDev Class Definitions ===============


class MDev(pci_device.PCIDevice):
    MDEV_MODEL = "vfio-pci"

    def __init__(self, mdev_uuid, guest_pci_address, pdev_uuid, mdev_type_id, mdev_json):
        self._mdev_type_id = mdev_type_id
        self.pdev_uuid = pdev_uuid
        self.mdev_json = mdev_json
        super().__init__(mdev_uuid, guest_pci_address)

    @property
    def mdev_type_id(self):
        return self._mdev_type_id

    def set_mdev_uuid(self, mdev_uuid):
        self._mdev_uuid = mdev_uuid

    @property
    def domain_json(self):
        dev_domain_json = {
            "@mode": "subsystem",
            "@type": "mdev",
            "@model": self.MDEV_MODEL,
            "source": {"address": {"@uuid": self._mdev_uuid}},
            "alias": {"@name": self.alias_name},
        }
        if self.guest_pci_address:
            dev_domain_json["address"] = self.guest_pci_address

        return dev_domain_json


class HCTMDev(MDev):
    MDEV_MODEL = "hct"


# =========== MDev Handlers ===============


class MDevHandler(pci_device.PCIDeviceHandler):
    _subtype = elf_constants.MDEV
    _MDEV_REGISTRY = {
        elf_constants.MDEV: MDev,
        elf_constants.MDEV_HCT: HCTMDev,
    }
    _dom_host_type = "mdev"

    def __init__(self, vm_json):
        super().__init__(vm_json)
        self._mdev_devices = self._devices

    @property
    def _matched_host_devs(self):
        return {mdev.uuid: mdev.mdev_json for mdev in self._mdev_devices}

    def _get_sorted_pdevs_by_uuid(self, vm_json):
        mdev_device_json = sorted(
            (d for d in vm_json.get("hostdevs", []) if d["type"] in self._MDEV_REGISTRY), key=lambda x: x["uuid"]
        )
        return mdev_device_json

    def vm_has_mdev_device(self):
        return len(self._mdev_devices) > 0

    def _initialize_devices(self):
        mdev_pci_devices = []
        previous_pdev_uuid = None
        index = 0

        for device in self._get_sorted_pdevs_by_uuid(self._vm_json):
            current_pdev_uuid = device["uuid"]
            if current_pdev_uuid != previous_pdev_uuid:
                index = 0
                previous_pdev_uuid = current_pdev_uuid

            vdev_uuid = self._gen_uuid(current_pdev_uuid, index)
            mdev_class = self._MDEV_REGISTRY[device["type"]]
            mdev_device = mdev_class(
                vdev_uuid, device.get("guest_pci_address"), current_pdev_uuid, device["mdev_type_id"], device
            )
            mdev_pci_devices.append(mdev_device)

            index += 1

        return mdev_pci_devices

    def _get_device_assign_infos(self, host_uuid):
        mdev_assign_infos = []
        pci_assign_infos = self._tuna_client.get_pci_assign_infos(host_uuid)
        for x in pci_assign_infos:
            if x["assign_type"] != elf_constants.PCI_USAGE_MDEV:
                continue

            for assigned_mdev in x["assigned_mdevs"]:
                if assigned_mdev["assign_id"] == "":
                    continue

                assigned_vm_uuid = self.parse_vm_uuid_from_assign_id(assigned_mdev["assign_id"])
                if assigned_vm_uuid == self.vm_uuid:
                    mdev_assign_infos.append(
                        {
                            "host_uuid": host_uuid,
                            "pdev_uuid": x["device_id"],
                            "assign_id": assigned_mdev["assign_id"],
                            "mdev_type_id": x["assign_mdev_type"]["mdev_type_id"],
                        }
                    )

        return mdev_assign_infos

    def release_unexpected_mdevs_by_tuna_record(self):
        self.release_unexpected_devices_by_tuna_record()

    def release_unexpected_devices_by_tuna_record(self):
        devs_assign_infos = self._get_all_hosts_dev_assign_infos()

        current_mounted_mdevs = []
        if self._vm_json["status"] in (
            elf_constants.VM_RUNNING,
            elf_constants.VM_SUSPENDED,
        ):
            current_mounted_mdevs = [self._gen_assign_id(mdev.uuid) for mdev in self._mdev_devices]

        vm_unexpected_assign_dev_infos = [x for x in devs_assign_infos if x["assign_id"] not in current_mounted_mdevs]
        if len(vm_unexpected_assign_dev_infos) == 0:
            return

        logging.info("VM({}) has unreleased MDev device({})".format(self.vm_uuid, vm_unexpected_assign_dev_infos))
        # To keep the logic simple, we release mdev one by one because "unexpected mdev"
        # is not common case.
        for assign_info in vm_unexpected_assign_dev_infos:
            self._release_pci_device_handler(
                self._release_mdev_devices,
                assign_info["host_uuid"],
                assign_info["pdev_uuid"],
                assign_info["mdev_type_id"],
                [assign_info["assign_id"]],
            )

    @staticmethod
    def _gen_uuid(pdev_uuid, index):
        return "{}-mdev{}".format(pdev_uuid, index)

    def gen_all_assign_ids(self):
        return [self._gen_assign_id(mdev.uuid) for mdev in self._mdev_devices]

    @staticmethod
    def get_vm_uuid_from_assign_id(assign_id):
        return pci_device.PCIDeviceHandler.parse_vm_uuid_from_assign_id(assign_id)

    @staticmethod
    def get_mdev_uuid_from_assign_id(assign_id):
        return pci_device.PCIDeviceHandler.parse_dev_uuid_from_assign_id(assign_id)

    def _release_mdev_devices(self, host_uuid, pdev_uuid, mdev_type_id, assign_ids):
        self._tuna_client.release_mdev(host_uuid, pdev_uuid, mdev_type_id, assign_ids)

    def _gen_mdev_type_id_to_pdev_map(self):
        # mdev_type_id_to_pdev:
        # {
        #     mdev_type_id: {
        #         pdev_uuid1: [mdev_uuids],
        #         ...
        #     },
        #     ...
        # }
        mdev_type_id_to_pdev = {}
        for mdev_device in self._mdev_devices:
            if mdev_device.mdev_type_id not in mdev_type_id_to_pdev:
                mdev_type_id_to_pdev[mdev_device.mdev_type_id] = {}

            if mdev_device.pdev_uuid not in mdev_type_id_to_pdev[mdev_device.mdev_type_id]:
                mdev_type_id_to_pdev[mdev_device.mdev_type_id][mdev_device.pdev_uuid] = []

            mdev_type_id_to_pdev[mdev_device.mdev_type_id][mdev_device.pdev_uuid].append(
                self._gen_assign_id(mdev_device.uuid)
            )

        return mdev_type_id_to_pdev

    def update_mdev_guest_pci_address(self, domain_json):
        return self.update_device_guest_pci_address(domain_json)

    def allocate_mdev(self):
        mdev_type_id_to_pdev = self._gen_mdev_type_id_to_pdev_map()

        assign_result = {}  # A dict, key is assign_id, value is mdev_uuid

        for mdev_type_id, pdev_uuid_to_assign_ids in list(mdev_type_id_to_pdev.items()):
            for pdev_uuid, assign_ids in list(pdev_uuid_to_assign_ids.items()):
                try:
                    assign_result.update(
                        self._tuna_client.assign_mdev(self.host_uuid, pdev_uuid, mdev_type_id, assign_ids)
                    )
                except Exception as e:
                    raise base.ResourceException(
                        "Allocate MDev on Device({}) of host({}) with mdev_type_id={}, assign_ids={} failed, "
                        "error={}".format(pdev_uuid, self.host_uuid, mdev_type_id, assign_ids, str(e)),
                        py_error.MDEV_ASSIGN_FAILED,
                    )

        for mdev in self._mdev_devices:
            mdev.set_mdev_uuid(assign_result[self._gen_assign_id(mdev.uuid)])

        return copy.deepcopy(self._mdev_devices)

    def allocate_mock_mdev(self):
        for mdev in self._mdev_devices:
            mdev.set_mdev_uuid(str(uuid.uuid4()))

        return copy.deepcopy(self._mdev_devices)

    def release_mdev_devices_silent(self):
        mdev_type_id_to_pdev = self._gen_mdev_type_id_to_pdev_map()

        for mdev_type_id, pdev_uuid_to_assign_ids in list(mdev_type_id_to_pdev.items()):
            for pdev_uuid, assign_ids in list(pdev_uuid_to_assign_ids.items()):
                try:
                    self._release_mdev_devices(self.host_uuid, pdev_uuid, mdev_type_id, assign_ids)
                except Exception as e:
                    logging.warning(
                        "Release Mdev(assign ids: {}) of PCI({}) silent failed, error is ({})".format(
                            assign_ids, pdev_uuid, str(e)
                        )
                    )


def allocate_mdev_devices_to_start(func):
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        mdev_handler = MDevHandler(vm_json)
        if mdev_handler.vm_has_mdev_device():
            if vm_json["status"] == elf_constants.VM_RUNNING:
                kwargs["mdev_devices"] = mdev_handler.allocate_mdev()
            else:
                kwargs["mdev_devices"] = mdev_handler.allocate_mock_mdev()

        try:
            return func(self, vm_json, *args, **kwargs)
        except Exception:
            if vm_start.need_release_device_on_exception_after_start(vm_json, kwargs["libvirt_conn"]):
                mdev_handler.release_mdev_devices_silent()
            raise

    return wrapper


def release_mdev_devices_on_start_failure(func):
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        try:
            return func(self, vm_json, *args, **kwargs)
        except Exception:
            MDevHandler(vm_json).release_mdev_devices_silent()
            raise

    return wrapper
