# Copyright (c) 2013-2022, SMARTX
# All rights reserved.
from concurrent.futures import TimeoutError
import contextlib
import copy
from distutils.version import StrictVersion
import errno
import ipaddress
import logging
import os
import stat

import grpc

from common.config import resources
from common.mongo.db import mongodb
from grpc_client.vmtools import client as vmtools_client
from smartx_app.elf.common import constants
from smartx_app.elf.common.code import API_VM_MOUNT_SVT, API_VM_UMOUNT_SVT
from smartx_app.elf.common.resources import base
from smartx_app.elf.http.common import client
from smartx_app.network.common.utils import dhcp
from smartx_proto.errors import pyerror_pb2 as py_error


class VMToolsWrapper:
    db = mongodb.vmtools

    def __init__(self):
        self._guest_info = {
            "os_version": None,
            "kernel_info": None,
            "hostname": None,
            "gateway_ips": [],
            "dns_servers": [],
            "nics": [],
            "ga_state": None,
            "ga_version": None,
            "expired": False,
            "vip": None,
            "mounted": False,
            "storage": {"size": None, "used": None, "mount_points": [], "disks": [], "storage_pools": []},
            "update_time": None,
        }

    @property
    def default_guest_info(self):
        return copy.deepcopy(self._guest_info)

    @property
    def guest_info_fields(self):
        return list(self._guest_info.keys())

    @classmethod
    def get_iso_directory(cls):
        """return iso storage dir and create it if not exist"""
        try:
            if not os.path.exists(constants.ISO_STORAGE_DIR):
                os.makedirs(constants.ISO_STORAGE_DIR)
                os.chmod(
                    constants.ISO_STORAGE_DIR,
                    stat.S_IRUSR
                    | stat.S_IWUSR
                    | stat.S_IXUSR
                    | stat.S_IRGRP
                    | stat.S_IXGRP
                    | stat.S_IROTH
                    | stat.S_IXOTH,
                )
        except OSError as e:
            if e.errno != errno.EEXIST:
                raise
        return constants.ISO_STORAGE_DIR

    @classmethod
    def update_vmtools_iso(cls, vm_uuid, svt_iso=""):
        cls.db.vm_configurations.update_one({"vm_uuid": vm_uuid}, {"$set": {"svt_iso": svt_iso}}, upsert=True)

    @classmethod
    def remove_vm_configuration(cls, vm_uuid):
        cls.db.vm_configurations.delete_one({"vm_uuid": vm_uuid})

    @classmethod
    def get_vmtools_iso_path(cls, vm_uuid):
        vmtools_iso = cls.db.vm_configurations.find_one({"vm_uuid": vm_uuid}, {"_id": 0})
        return vmtools_iso["svt_iso"] if vmtools_iso else None

    @classmethod
    def batch_get_vmtools_iso_path(cls, vm_uuids):
        vmtools_isos = cls.db.vm_configurations.find({"vm_uuid": {"$in": list(vm_uuids)}}, {"_id": 0})
        return {vmtools_iso["vm_uuid"]: vmtools_iso["svt_iso"] if vmtools_iso else "" for vmtools_iso in vmtools_isos}

    @classmethod
    def is_vmtools_iso_mounting(cls, vmtools_images):
        if not vmtools_images:
            return False

        svt_paths = [image["path"] for image in vmtools_images]
        return cls.db.vm_configurations.find_one({"svt_iso": {"$in": svt_paths}}, {"_id": 1}) is not None

    @classmethod
    def create_iso_image(
        cls,
        image_name,
        image_uuid,
        path,
        size,
        file_name,
        description="",
        token=None,
        timeout=None,
        version=0,
        storage_cluster_uuid="",
    ):
        try:
            token = token or client.get_service_token()
            return vmtools_client.vmtools_client.create_svt_image(
                image_name,
                image_uuid,
                path,
                size,
                file_name,
                description=description,
                token=token,
                timeout=timeout,
                version=version,
                storage_cluster_uuid=storage_cluster_uuid,
            )
        except Exception as excp:
            logging.warning("Failed to create iso image for {}, error: {}".format(image_name, str(excp)))
            raise

    @classmethod
    def parse_version(cls, version_str):
        try:
            version = StrictVersion(version_str).version
            return version[0] * 1000000 + version[1] * 1000 + version[2]
        except (ValueError, AttributeError):
            return None

    @classmethod
    def cast_storage_data_type(cls, svt_data: dict) -> None:
        if not svt_data.get("storage_info"):
            return
        storage_data = svt_data["storage_info"]
        # Python grpc will convert int64 type to str, so we need to convert str to int.
        # In addition, when protobuf filed value is None, python grpc will convert None
        # to the default value of the type, for example, for the type int64, if its value
        # is `None`, it will be `0` after grpc conversion, in order to differentiate
        # between `None` and `0`, we change `None` to `-1` in the grpc server, and then
        # convert it to `None` when it is recognized as -1.
        storage_data["size"] = int(storage_data["size"]) if storage_data["size"] != "-1" else None
        storage_data["used"] = int(storage_data["used"]) if storage_data["used"] != "-1" else None
        for item in storage_data["disks"]:
            item["size"] = int(item["size"]) if item["size"] != "-1" else None
            item["used"] = int(item["used"]) if item["used"] != "-1" else None
        for item in storage_data["mount_points"]:
            item["size"] = int(item["size"]) if item["size"] != "-1" else None
            item["used"] = int(item["used"]) if item["used"] != "-1" else None

    @classmethod
    def cast_vm_svt_data_type(cls, svt_data: dict) -> None:
        if not svt_data:
            return
        svt_data["last_update"] = int(svt_data["last_update"]) if svt_data["last_update"] else None
        cls.cast_storage_data_type(svt_data)

    @classmethod
    def cast_vm_svt_data_type_list(cls, svt_data: list[dict]) -> None:
        if not svt_data:
            return
        for item in svt_data:
            item["last_update"] = int(item["last_update"]) if item["last_update"] else None
            cls.cast_storage_data_type(item)

    def _query_guest_infos(self, svt_image, *resources, token=None):
        if not resources:
            return []

        try:
            token = token or client.get_service_token()
            guest_infos = {x["uuid"]: self.default_guest_info for x in resources}
            vm_svt_data_list = vmtools_client.vmtools_client.batch_get_vm_svt_data(
                list(guest_infos.keys()), token=token
            ).get("vm_svt_data", [])
            self.cast_vm_svt_data_type_list(vm_svt_data_list)

            for queried_guest_info in vm_svt_data_list:
                vm_uuid = queried_guest_info["vm_uuid"]
                queried_static_info = queried_guest_info.get("static_info", {})
                guest_info = guest_infos[vm_uuid]
                guest_info["update_time"] = queried_guest_info.get("last_update")
                if queried_static_info.get("ga_state") in (constants.SVT_RESTRICTED, constants.SVT_NON_INSTALLED):
                    guest_info["ga_state"] = queried_static_info["ga_state"]
                    continue
                for field in self.guest_info_fields:
                    if field in queried_static_info:
                        guest_info[field] = queried_static_info[field]
                    if field == "nics":
                        for nic in guest_info[field]:
                            nic["ip_addresses"] = [
                                address
                                for address in nic.get("ip_addresses", [])
                                if not ipaddress.ip_address(address["ip_address"]).is_link_local
                            ]
                    if field == "ga_version" and queried_static_info.get(field):
                        guest_info[field] = self.parse_version(queried_static_info[field])
                guest_info["storage"] = queried_guest_info.get("storage_info", {})
                if svt_image and guest_info["ga_version"]:
                    if int(guest_info["ga_version"]) < int(svt_image["version"]):
                        guest_info["expired"] = True

            return [(x, guest_infos[x["uuid"]]) for x in resources]
        except Exception:
            logging.error(
                "Failed to apply guest info for VMs: {}".format([x["uuid"] for x in resources]), exc_info=True
            )
            return []

    @classmethod
    def reset_guest_info(cls, vm_uuid, token=None):
        try:
            token = token or client.get_service_token()
            vmtools_client.vmtools_client.reset_svt_data(vm_uuid, token=token)
        except Exception as excp:
            logging.warning("Failed to reset VM({}) svt data, error: {}".format(vm_uuid, str(excp)))

    @classmethod
    def get_vm_svt_data(cls, vm_uuid, token=None):
        try:
            token = token or client.get_service_token()
            vm_svt_data = vmtools_client.vmtools_client.get_vm_svt_data(vm_uuid, token=token)
            cls.cast_vm_svt_data_type(vm_svt_data)
            return vm_svt_data
        except Exception as excp:
            logging.warning("Failed to get VM({}) svt static data, error: {}".format(vm_uuid, str(excp)))
            return None

    @staticmethod
    def apply_guest_nics_to_resource(resource, nics):
        """Use ip addresses from nics of guest into to update vm resource

        :param resource: vm resource to be updated
        :param nics: nics of guest info

        :return: whether vm resource is updated
        """
        changed = False
        if resource.get("type") != constants.KVM_VM:
            return changed

        # Some old VM resource may have gateway and subnet_mask in nics, so we
        # need to make sure that gateway and subnet_mask are removed from nics.
        for vm_nic in resource["nics"]:
            if "gateway" in vm_nic or "subnet_mask" in vm_nic or "ip_type" in vm_nic:
                vm_nic.pop("gateway", None)
                vm_nic.pop("subnet_mask", None)
                vm_nic.pop("ip_type", None)
                changed = True

        origin_nics = {x["mac_address"].lower(): x for x in resource["nics"]}
        for nic in [x for x in nics if "hardware_address" in x]:
            mac_address = nic["hardware_address"]
            if mac_address not in origin_nics:
                continue
            origin_nic = origin_nics[mac_address]
            origin_nic["gateway"] = nic.get("gateway_ip", "")
            ip_addresses = [x for x in nic["ip_addresses"] if x["ip_address_type"] == "ipv4"]
            if ip_addresses:
                origin_nic["ip_address"] = ip_addresses[0]["ip_address"]
                origin_nic["subnet_mask"] = ip_addresses[0].get("netmask", "")
            changed = True
        return changed

    @classmethod
    def apply_nics(cls, *resources):
        """Update ip address of vm resources with its guest info"""
        for resource, guest_info in cls()._query_guest_infos(None, *resources):
            if resource.get("type") == constants.KVM_VM:
                cls.apply_guest_nics_to_resource(resource, guest_info["nics"])

    @classmethod
    def apply_cdroms_to_resource(cls, resource, svt_images):
        """add name to cdrom of vm resource if it contains vmtools image"""
        changed = False
        if not resource:
            return changed
        svt_image_map = {x["path"]: x for x in svt_images}
        supported_types = [constants.KVM_VM, constants.KVM_VM_SNAPSHOT, constants.KVM_VM_TEMPLATE]
        if resource.get("type") not in supported_types:
            return changed
        # add name for SMTX VMTools cdrom device in vm resources
        cdroms = [x for x in resource["disks"] if x["type"] == "cdrom"]
        for cdrom in cdroms:
            cdrom_path = cdrom.get("path")
            if cdrom_path in svt_image_map:
                cdrom["name"] = svt_image_map[cdrom_path]["name"]
                changed = True
        return changed

    @classmethod
    def apply_cdroms(cls, *resources):
        """add name to cdrom of vm resources if it contains vmtools image"""
        svt_image_list = cls.get_images()
        for resource in resources:
            cls.apply_cdroms_to_resource(resource, svt_image_list)

    @classmethod
    def apply_guest_infos(cls, *resources):
        """Add their guest info for all input vm resources"""
        svt_image_list = cls.get_images()
        latest_image = None if not svt_image_list else svt_image_list[0]
        for resource, guest_info in cls()._query_guest_infos(latest_image, *resources):
            if resource.get("type") == constants.KVM_VM:
                cls.apply_guest_nics_to_resource(resource, guest_info["nics"])
                resource["guest_info"] = guest_info
                if cls.apply_cdroms_to_resource(resource, svt_image_list):
                    guest_info["mounted"] = True

    @classmethod
    def get_job_name(cls, vm_doc, updated_disks=None, deleted_disks=None):
        """
        Check the vm edit disk job, if this a SVT CD mount/umount job,
        return API_VM_MOUNT_SVT/API_VM_UMOUNT_SVT, otherwise return None.
        API_VM_MOUNT_SVT job only mount the SVT ISO to an empty cdrom,
        and API_VM_UMOUNT_SVT only umount all cdroms contain a SVT ISO
        """
        if deleted_disks or not updated_disks or len(vm_doc["disks"]) != len(updated_disks):
            return None

        mount_paths = []
        umount_paths = []
        for old_disk, new_disk in zip(vm_doc["disks"], updated_disks):
            try:
                old_disk_type = old_disk["type"]
                new_disk_type = new_disk["type"]
                old_disk_path = old_disk["path"]
                new_disk_path = new_disk["path"]
                assert old_disk_type == new_disk_type, "disk type is changed"
                if old_disk_type != "cdrom":
                    assert old_disk_path == new_disk_path, "disk path is changed"
                elif old_disk_path != "" and new_disk_path == "":
                    umount_paths.append(old_disk_path)
                elif old_disk_path != new_disk_path:
                    mount_paths.append(new_disk_path)
                else:
                    assert old_disk_path == new_disk_path, "cdrom path is changed"
                assert len(mount_paths) < 2, "mount more than 1 cdrom"
                assert not mount_paths or not umount_paths, "both mount and umount cdrom"
            except (AssertionError, AttributeError, KeyError) as excp:  # NOQA
                return None

        svt_images = cls.get_images()
        if not svt_images:
            return None
        svt_images_paths = [x["path"] for x in svt_images]
        if mount_paths:
            if mount_paths[0] in svt_images_paths:
                return API_VM_MOUNT_SVT
        elif umount_paths:
            if set(umount_paths).issubset(svt_images_paths):
                return API_VM_UMOUNT_SVT
        return None

    @classmethod
    def get_images(cls, version=None, path=None, token=None, timeout=None) -> list[dict]:
        """return all available vmtools images"""
        try:
            token = token or client.get_service_token()
            svt_images = vmtools_client.vmtools_client.batch_get_svt_images(
                version=version, path=path, token=token, timeout=timeout
            )
            image_list = svt_images["svt_images"]
            for item in image_list:
                item["size"] = int(item["size"])
                item["version"] = int(item["version"])
                item["create_time"] = int(item["create_time"])
            return image_list
        except Exception as excp:
            logging.warning("Failed to get images, error: {}".format(str(excp)))
            return []

    @classmethod
    def get_image_by_vm_uuid(cls, vm_uuid, token=None, timeout=None):
        target_vm_configuration = cls.db.vm_configurations.find_one({"vm_uuid": vm_uuid}, {"_id": 0})

        # if target_vm_configuration is None,
        # it means that the vmtools iso hasn't been installed.
        if not target_vm_configuration:
            return None

        # if target_vm_configuration["svt_iso"] is "",
        # it means that the vmtools iso has been uninstalled previously.
        if not target_vm_configuration["svt_iso"]:
            return None

        svt_images = cls.get_images(path=target_vm_configuration["svt_iso"], token=token, timeout=timeout)
        if not svt_images:
            logging.warning(
                "Failed to get image with path: {}, vm_uuid: {}".format(target_vm_configuration["svt_iso"], vm_uuid)
            )
            return None

        return svt_images[0]

    @classmethod
    def get_latest_image(cls, token=None, timeout=None) -> dict:
        try:
            token = token or client.get_service_token()
            svt_images = vmtools_client.vmtools_client.batch_get_svt_images(latest=True, token=token, timeout=timeout)
            latest_image = svt_images["svt_images"][0] if svt_images["svt_images"] else None
            if latest_image:
                latest_image["size"] = int(latest_image["size"])
                latest_image["version"] = int(latest_image["version"])
                latest_image["create_time"] = int(latest_image["create_time"])
            return latest_image
        except Exception as excp:
            logging.warning("Failed to get image, error: {}".format(str(excp)))
            return None

    @classmethod
    def delete_images(cls, image_uuids, token=None, timeout=None):
        try:
            token = token or client.get_service_token()
            vmtools_client.vmtools_client.delete_svt_images(image_uuids, token=token, timeout=timeout)
        except Exception as excp:
            logging.warning("Failed to delete images {}, err: {}".format(image_uuids, str(excp)))
            raise

    @classmethod
    def check_nic_info_match(cls, vm_nic, svt_nic):
        # If ip_type is not passed in from API, it is treated as static.
        # This is for compatibility with other tools or calling lower version of API
        # to configure IP, such as v2v.
        ip_type = vm_nic.get("ip_type", constants.IP_TYPE_STATIC)

        # If ip_type of nic is dhcp, not change its configuration, the reason is that
        # vmtools does not support dhcp configuration. Therefore, if the ip type is dhcp,
        # it is considered that the configuration has been changed to dhcp in guest OS,
        # and vmtools does not need to configure it.
        # So, in the case where ip type is dhcp, we think of nic info is the same.
        if ip_type == constants.IP_TYPE_DHCP:
            return True

        if ip_type != svt_nic.get("ip_type"):
            return False

        if vm_nic.get("gateway", "") != svt_nic.get("gateway_ip", ""):
            return False

        # ip_addresses is a list, include: IPv4, IPv6, etc.
        # The following IP address are IPv4.
        for svt_ip_info in svt_nic["ip_addresses"]:
            if svt_ip_info["ip_address"] == vm_nic["ip_address"] and svt_ip_info["netmask"] == vm_nic["subnet_mask"]:
                return True

        return False

    @classmethod
    def update_svt_network(cls, vm_json, token=None):
        """Set static IP for nics whose network is not enable dhcp"""
        vm_uuid = vm_json["uuid"]
        nic_list = []
        token = token or client.get_service_token()

        # get origin nic infos from svt_reports collection
        nic_map = {}

        svt_info = cls.get_vm_svt_data(vm_uuid, token=token)
        if not svt_info or not svt_info.get("static_info"):
            return {}

        svt_static_info = svt_info["static_info"]
        if svt_static_info.get("ga_state") != constants.SVT_ACTIVE:
            logging.warning("SVT in VM({}) is not running, cannot configure network.".format(vm_uuid))
            return {}

        for nic in svt_static_info.get("nics", []):
            mac_address = nic.get("hardware_address")
            if mac_address:
                nic_map[mac_address.lower()] = nic

        # Compare expected nic infos to origin nic infos, get the nic_list that need to be configured
        vm_nics_mac_list = []
        for vm_nic in vm_json["nics"]:
            vm_nic_mac = vm_nic["mac_address"].lower()
            vm_nics_mac_list.append(vm_nic_mac)

            if "vlan_uuid" not in vm_nic or "ip_address" not in vm_nic or "subnet_mask" not in vm_nic:
                continue
            if dhcp.DHCPProxy.is_enable_dhcp(vm_nic["vlan_uuid"]):
                # skip nic which is connected to a dhcp network
                continue

            # If the NIC already exists in VM, compare its configuration information.
            # If the configuration information is different, modify it. If the same, skipped.
            if vm_nic_mac in nic_map and cls.check_nic_info_match(vm_nic, nic_map[vm_nic_mac]):
                continue

            # If NIC is newly added, and vmtools does not collect its configuration info,
            # so we will modify the network configuration directly.
            nic_list.append(
                {
                    "mac": vm_nic_mac,
                    "ip": vm_nic["ip_address"],
                    "mask": vm_nic["subnet_mask"],
                    "gateway": vm_nic.get("gateway", ""),
                }
            )

        if not nic_list:
            # if nic_list is not empty, the svt data will be updated by 'update_svt_properties'.
            # if nic_list is empty and some nics are hot detached, the svt data needs to be updated immediately.
            for mac_address in nic_map.keys():
                if mac_address not in vm_nics_mac_list:
                    VMToolsWrapper.batch_update_svt_data(vm_uuids=[vm_uuid], host_ip=vm_json["node_ip"])
                    break
            return {}

        # Reorder configuration.
        # Make it a priority to configure NICs with empty gateways(delete gateways)
        # to avoid the possibility of adding multiple default gateways.
        nic_list = sorted(nic_list, key=lambda x: x["gateway"])

        try:
            return vmtools_client.vmtools_client.update_svt_properties(
                vm_json["uuid"],
                "network_list",
                {"network_list": nic_list},
                token=token,
            )
        except Exception as excp:
            if "SVT_SERVICE_NOT_CONNECTED" not in str(excp):
                logging.info("Failed to update network of VM(%s) with SVT: %s", vm_json["uuid"], excp)
                raise

    @classmethod
    def batch_update_svt_data(
        cls,
        vm_uuids: list[str],
        token: str | None = None,
        timeout: int | None = None,
        host_ip: str | None = None,
        host_port: int | None = None,
    ) -> list[str]:
        try:
            token = token or client.get_service_token()
            return_data = vmtools_client.VmToolsClient(host_ip, host_port).batch_update_svt_data(
                vm_uuids, token, timeout
            )
            return return_data["vm_uuids"]
        except Exception as excp:
            logging.warning("Failed update VMs({}) svt data, error: {}".format(vm_uuids, str(excp)))
            return []

    @classmethod
    def match_image(cls, iso_path):
        """
        Check if path is a image or svt image.
        If it is not, then return None.
        :param iso_path:
        :return:
        """
        svt_images = cls.get_images()
        image = mongodb.resources.resource.find_one(
            {"resource_state": constants.RESOURCE_IN_USE, "path": iso_path, "type": constants.ISO_IMAGE}
        )
        if not image and svt_images:
            matched_images = [x for x in svt_images if x["path"] == iso_path]
            if matched_images:
                image = matched_images[0]

        return image

    @classmethod
    def get_ip_related_macs(cls, ip: str, token=None) -> list[str]:
        ips = [ip] if not isinstance(ip, list) else ip
        try:
            token = token or client.get_service_token()
            macs = vmtools_client.vmtools_client.search_macs(ips, token=token)
            return macs["macs"]
        except Exception as excp:
            logging.warning("Failed to get macs for ips: {}, error: {}".format(ips, str(excp)))
            return []

    @classmethod
    @contextlib.contextmanager
    def freeze_vm(cls, vm_uuid, timeout=None, token=None):
        """freeze guest os file system for a limited time"""
        try:
            logging.info("Start to freeze VM(%s)", vm_uuid)
            token = token or client.get_service_token()
            vmtools_client.vmtools_client.freeze_svt_file_system(vm_uuid, token=token)
            yield
            fs_status = vmtools_client.vmtools_client.get_svt_file_system_status(vm_uuid, token=token)
            # if the status of FileSystem is not frozen, then it must be thawed by grpc
            # backgroud thread which is used to monitor if forzen status is timeout
            if fs_status["status"] != "frozen":
                raise TimeoutError("Frozen status of SVT file system is timeout on VM %s" % vm_uuid)
        finally:
            try:
                vmtools_client.vmtools_client.thaw_svt_file_system(vm_uuid, token=token)
            except grpc.RpcError as excp:
                logging.warning("Thaw SVT file system for VM({}) failed, error: {}".format(vm_uuid, excp))

    @classmethod
    def sync_vm_time_with_host(cls, vm_uuid, token=None):
        """Synchronize the VM system time with the host"""
        token = token or client.get_service_token()
        svt_info = cls.get_vm_svt_data(vm_uuid, token=token)
        if not svt_info or not svt_info.get("static_info"):
            raise base.ResourceException(
                "VM({}) static info collected by vmtools is empty, make sure that "
                "vmtools-agent service on host is running".format(vm_uuid),
                py_error.SVT_STATIC_DATA_EMPTY,
            )

        svt_state = svt_info["static_info"].get("ga_state")
        if svt_state not in (constants.SVT_ACTIVE, constants.SVT_INACTIVE):
            raise base.ResourceException(
                "VM({}) SVT state is {}, can not sync time with host".format(vm_uuid, svt_state),
                py_error.SVT_STATUS_ABNORMAL,
            )

        vmtools_client.vmtools_client.sync_vm_time_with_host(vm_uuid, token=token)

    @classmethod
    def upgrade_svt_service(cls, vm_uuid, token=None):
        token = token or client.get_service_token()
        vmtools_client.vmtools_client.upgrade_svt_service(vm_uuid, token=token)

    @classmethod
    def manage_guest_service(cls, vm_uuid, service_name, action, need_vmtools_iso, token=None):
        token = token or client.get_service_token()
        return vmtools_client.vmtools_client.manage_guest_service(
            vm_uuid=vm_uuid, service_name=service_name, action=action, need_vmtools_iso=need_vmtools_iso, token=token
        )

    @classmethod
    def batch_manage_guest_service(cls, vm_uuids, service_name, action, need_vmtools_iso, token=None):
        token = token or client.get_service_token()
        return vmtools_client.vmtools_client.batch_manage_guest_service(
            vm_uuids, service_name, action, need_vmtools_iso, token=token
        )

    @staticmethod
    def submit_job(description, resources=None, one_time_task=None, event=None):
        from job_center.handler.leader import workers

        return {
            "job_id": workers.job_submit(
                user="", description=description, resources=resources, one_time_task=one_time_task, event=event
            )
        }


def clean_residual_vm_configurations():
    vm_resource_collection = mongodb.resources.resource
    vm_configuration_collection = mongodb.vmtools.vm_configurations

    vm_uuids = {
        vm_doc["uuid"]
        for vm_doc in vm_resource_collection.find(
            {
                "type": constants.KVM_VM,
                "resource_state": resources.RESOURCE_IN_USE,
            },
            {"uuid": 1},
        )
    }
    vmtools_related_uuids = {doc["vm_uuid"] for doc in vm_configuration_collection.find({}, {"vm_uuid": 1})}
    vm_residual_uuids = vmtools_related_uuids - vm_uuids
    if vm_residual_uuids:
        vm_configuration_collection.delete_many({"vm_uuid": {"$in": list(vm_residual_uuids)}})
