# Copyright (c) 2013-2016, SMARTX
# All rights reserved.
from common.config.resources import RESOURCE_IN_USE
from common.mongo.db import mongodb
from smartx_app.elf.common.code import (
    API_VOLUME_SNAPSHOT_CREATE,
    API_VOLUME_SNAPSHOT_DELETE,
    API_VOLUME_SNAPSHOT_REBUILD,
    API_VOLUME_SNAPSHOT_ROLLBACK,
)
from smartx_app.elf.common.events.volume_events_wrapper import VolumeEventWrapper
from smartx_app.elf.common.events.volume_snapshot_events_wrapper import VolumeSnapshotEventWrapper
from smartx_app.elf.common.resource_query.volume_snapshot_querier import VolumeSnapshotQuerier
from smartx_app.elf.common.resources.base import ResourceException
from smartx_app.elf.common.resources.iscsi_snapshot import ISCSIVolumeSnapshot
from smartx_app.elf.common.resources.volume import Volume
from smartx_app.elf.common.resources.volume_snapshot import Snapshot, VolumeSnapshot
from smartx_app.elf.http.common import exceptions
from smartx_app.elf.job_center.constants import KVM_VOL, KVM_VOL_ISCSI, KVM_VOL_ISCSI_SNAPSHOT, KVM_VOL_SNAPSHOT
from smartx_proto.errors import pyerror_pb2 as py_error


class VolumeSnapshotWrapper:
    _SNAPSHOTS = {
        KVM_VOL_ISCSI: lambda volume, snapshot_name, snapshot_desc: ISCSIVolumeSnapshot(
            name=snapshot_name,
            description=snapshot_desc,
            volume_uuid=volume.uuid,
            volume_size=volume.size_in_byte,
            volume_name=volume.name,
            target_name=volume.target_name,
            lun_id=volume.lun_id,
            storage_policy_uuid=volume.storage_policy_uuid,
            storage_cluster_uuid=volume.storage_cluster_uuid,
            sharing=volume.sharing,
            resident_in_cache=volume.resident_in_cache,
        ),
        KVM_VOL: lambda volume, snapshot_name, snapshot_desc: VolumeSnapshot(
            name=snapshot_name,
            description=snapshot_desc,
            volume_uuid=volume.uuid,
            volume_size=volume.size_in_byte,
            volume_name=volume.name,
            sharing=False,
        ),
    }
    db = mongodb.resources

    def __init__(self, snapshot):
        self.snapshot = snapshot

    @classmethod
    def new(cls, snapshot_name, snapshot_desc, volume_uuid):
        try:
            volume = Volume.load(volume_uuid)
            snapshot = cls._SNAPSHOTS[volume.type](volume, snapshot_name, snapshot_desc)
            return cls(snapshot)
        except ResourceException:
            raise exceptions.VolumeError("Volume({}) is not found.".format(volume_uuid), py_error.VOLUME_NOT_FOUND)

    @classmethod
    def update(cls, snapshot_uuid, name, description):
        if not name:
            raise ResourceException("Must specify the snapshot name", py_error.VOLUME_SNAPSHOT_UPDATE_FAILED)

        update_doc = {"name": name}
        if description is not None:
            update_doc["description"] = description

        result = cls.db.resource.update_one(
            {
                "uuid": snapshot_uuid,
                "resource_state": RESOURCE_IN_USE,
                "type": KVM_VOL_ISCSI_SNAPSHOT,
            },
            {"$set": update_doc},
        )

        # Reentrant allowed
        return result.matched_count == 1

    @classmethod
    def load(cls, snapshot_uuid):
        return cls(Snapshot.load(snapshot_uuid))

    @classmethod
    def query_from_db(cls, snapshot_uuid=None, show_vm_snapshot=False):
        query = {"resource_state": RESOURCE_IN_USE, "type": {"$in": [KVM_VOL_SNAPSHOT, KVM_VOL_ISCSI_SNAPSHOT]}}
        projection = {"_id": 0}

        if snapshot_uuid is not None:
            query["uuid"] = snapshot_uuid
            return [cls.db.resource.find_one(query, projection)]

        if show_vm_snapshot:
            query["vm_snapshot"] = {"$exists": True, "$ne": None}
        else:
            query["$or"] = [{"vm_snapshot": {"$exists": False}}, {"vm_snapshot": None}]
        return list(cls.db.resource.find(query, projection))

    @classmethod
    def page_query(
        cls, count, skip_page, sort_criteria, filter_criteria, projection, start_uuid, end_uuid, skip_to_last
    ):
        return VolumeSnapshotQuerier().page_query(
            count=count,
            skip_page=skip_page,
            sort_criteria=sort_criteria,
            filter_criteria=filter_criteria,
            projection=projection,
            start_uuid=start_uuid,
            end_uuid=end_uuid,
            skip_to_last=skip_to_last,
        )

    @classmethod
    def search(cls, text, limit=None):
        return VolumeSnapshotQuerier().search(text, limit)

    @classmethod
    def get(cls, snapshot_uuid):
        return VolumeSnapshotQuerier().get(snapshot_uuid)

    @classmethod
    def get_by_volume_uuid(cls, vol_uuids):
        if not vol_uuids:
            return []
        query = {
            "resource_state": RESOURCE_IN_USE,
            "type": {"$in": [KVM_VOL_SNAPSHOT, KVM_VOL_ISCSI_SNAPSHOT]},
            "volume_uuid": {"$in": vol_uuids},
        }
        projection = {"_id": 0}
        cursor = mongodb.resources.resource.find(query, projection)
        return [Snapshot._create(c) for c in cursor]

    @staticmethod
    def submit_job(description, resources, event=None):
        from job_center.handler.leader.workers import job_submit

        return {"job_id": job_submit(user="", description=description, resources=resources, event=event)}

    def submit_create(self, user=None):
        event = VolumeSnapshotEventWrapper(
            user=user, uuid=self.snapshot.uuid, name=self.snapshot.name, snapshot_type=self.snapshot.type
        ).event_volume_snapshot_create()
        return self.submit_job(API_VOLUME_SNAPSHOT_CREATE, {self.snapshot.uuid: self.snapshot.dumps()}, event=event)

    def submit_delete(self, user=None):
        event = VolumeSnapshotEventWrapper(
            user=user, uuid=self.snapshot.uuid, name=self.snapshot.name, snapshot_type=self.snapshot.type
        ).event_volume_snapshot_delete()
        return self.submit_job(API_VOLUME_SNAPSHOT_DELETE, {self.snapshot.uuid: self.snapshot.delete()}, event=event)

    def submit_rollback(self, user=None):
        volume = self.snapshot.rollback()
        event = VolumeSnapshotEventWrapper(
            user=user,
            uuid=self.snapshot.uuid,
            name=self.snapshot.name,
            snapshot_type=self.snapshot.type,
        ).event_volume_snapshot_rollback(volume["name"])
        return self.submit_job(API_VOLUME_SNAPSHOT_ROLLBACK, {volume["uuid"]: volume}, event=event)

    def submit_rebuild(self, name, description, this_volume_uuid=None, user=None, resident_in_cache=False):
        from smartx_app.elf.common.utils import validator

        volume = self.snapshot.rebuild(name=name, description=description, this_volume_uuid=this_volume_uuid)

        check_items = {validator.ValidatorChain.check.resident_in_cache: {}}
        if volume["type"] == KVM_VOL_ISCSI:
            volume["resident_in_cache"] = resident_in_cache
        if this_volume_uuid:
            check_items[validator.ValidatorChain.check.uuid_uniqueness] = {}

        validator.ValidatorChain(volume).validate(check_items)

        event = VolumeEventWrapper(user=user).event_volume_rebuild(self.snapshot, volume)
        return self.submit_job(API_VOLUME_SNAPSHOT_REBUILD, {volume["uuid"]: volume}, event=event)
