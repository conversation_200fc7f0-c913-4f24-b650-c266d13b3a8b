import functools
import logging
import time

import libvirt
import xmltodict

from common.elf import client as elf_client
from common.mongo.db import mongodb
from job_center.common import exceptions as jc_excpt
from job_center.config import LIBVIRT_LIVE_DETACH_DEVICE_TIMEOUT
from smartx_app.elf.common import constants as elf_constants
from smartx_app.elf.common.resource_wrappers import vmtools_wrapper
from smartx_app.elf.common.utils import internal_product, vm_start, vs_controller
from smartx_app.vmtools.job_center import constants as jc_vmtools_constants
from smartx_app.vmtools.job_center.lib import guest_service
from smartx_proto.errors import pyerror_pb2 as py_error
from smartx_vmtools.agent import svt_guest_service_wrapper
from smartx_vmtools.common import constants as vmtools_constants


def get_latest_vmtools_version() -> int:
    svt_image = vmtools_wrapper.VMToolsWrapper.get_latest_image()
    if not svt_image:
        return 0

    # version format: v[0] * 1000_000 + v[1] * 1000 + v[2]
    # for 4.1.0, the encoded version is 4001000
    return svt_image["version"]


def submit_one_time_task(description, one_time_task=None, event=None):
    from job_center.handler.leader import workers

    return {"job_id": workers.job_submit(user="", description=description, one_time_task=one_time_task, event=event)}


def get_current_anti_malware_by_vm_uuid(vm_uuid):
    vm_doc = mongodb.resources.resource.find_one({"uuid": vm_uuid}, {"_id": 0, "anti_malware": 1})
    if vm_doc:
        return vm_doc["anti_malware"]
    return {}


def _get_libvirt_json(libvirt_conn, vm_uuid):
    try:
        libvirt_domain = libvirt_conn.lookupByName(vm_uuid)
        libvirt_xml = libvirt_domain.XMLDesc(0)
        return xmltodict.parse(libvirt_xml)
    except libvirt.libvirtError as e:
        # when create and start up the VM, AM is not allowed. but the DPI should be enabled
        # in this case, the libvirt domain is not defined yet
        if e.get_error_code() == libvirt.VIR_ERR_NO_DOMAIN or "Domain not found" in str(e):
            return {}
        raise


def allocate_devices_to_start(func):
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        vm_uuid = vm_json["uuid"]
        gvm_wrapper = AsiaInfoGVMWrapper(vm_json)

        try:
            if vm_json["status"] in elf_constants.VM_ACTIVE_STATES:
                libvirt_json = _get_libvirt_json(kwargs["libvirt_conn"], vm_uuid)
                kwargs["anti_malware_devices"] = gvm_wrapper.allocate_devices(libvirt_json, online_dpi=True)
            return func(self, vm_json, *args, **kwargs)
        except:
            if vm_start.need_release_device_on_exception_after_start(vm_json, kwargs["libvirt_conn"]):
                gvm_wrapper.release_devices_silent(offline_dpi=True)
            raise

    return wrapper


def release_anti_malware_devices_on_start_failure(func):
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        try:
            return func(self, vm_json, *args, **kwargs)
        except:
            AsiaInfoGVMWrapper(vm_json).release_devices_silent(offline_dpi=True)
            raise

    return wrapper


class AsiaInfoGVMWrapper:
    INVALID_AM_IDX = 0xFFFF

    def __init__(self, vm_json, remote_client=None):
        self.vm_json = vm_json
        self.vm_uuid = vm_json["uuid"]
        self.vsc_mgr = vs_controller.global_vs_conf_manager
        self.remote_client: elf_client.ElfApiClient | None = remote_client

    @staticmethod
    def has_valid_vmtools_in_cluster() -> bool:
        return get_latest_vmtools_version() >= 4001000  # 4.1.0

    @staticmethod
    def install_ds_driver(vm_uuid):
        guest_service.manage_guest_service(
            vm_uuid=vm_uuid,
            service_name=jc_vmtools_constants.AM_ASIAINFO_SERVICE_NAME,
            action=jc_vmtools_constants.SERVICE_INSTALL_ACTION,
            need_vmtools_iso=True,
        )

    @staticmethod
    def uninstall_ds_driver(vm_uuid):
        guest_service.manage_guest_service(
            vm_uuid=vm_uuid,
            service_name=jc_vmtools_constants.AM_ASIAINFO_SERVICE_NAME,
            action=jc_vmtools_constants.SERVICE_UNINSTALL_ACTION,
            need_vmtools_iso=False,
        )

    @staticmethod
    def filter_vms_with_ds_driver(vm_uuids):
        result = guest_service.batch_manage_guest_service(
            vm_uuids=vm_uuids,
            service_name=jc_vmtools_constants.AM_ASIAINFO_SERVICE_NAME,
            action=jc_vmtools_constants.SERVICE_STATUS_ACTION,
        )
        return [
            x["vm_uuid"]
            for x in result["status_list"]
            if x["status"] == svt_guest_service_wrapper.SVTGuestServiceWrapper.GUEST_SERVICE_STATUS_ACTIVE
        ]

    @staticmethod
    def get_gvms(query=None, fields=None):
        filters = {
            "type": elf_constants.KVM_VM,
            "resource_state": elf_constants.RESOURCE_IN_USE,
            "anti_malware.enable": True,
            "anti_malware.product": elf_constants.AM_PRODUCT_ASIAINFO,
        }
        if query is not None:
            filters.update(query)

        fields = fields or {}
        fields.update({"_id": 0})

        gvms = list(mongodb.resources.resource.find(filters, fields))
        return gvms

    @staticmethod
    def get_allocated_am_device_mappings_from_current_node():
        mapping = {}
        allocated_devs = vs_controller.dev_query()
        for dev in allocated_devs:
            am_idx = dev["idx"]
            vm_uuid = dev["user_id"]
            if am_idx != AsiaInfoGVMWrapper.INVALID_AM_IDX:
                mapping[vm_uuid] = am_idx
        return mapping

    def get_vsc_mgr(self):
        return self.vsc_mgr

    def svm_available(self) -> bool:
        vms = internal_product.query_vms_with_internal_product(
            [elf_constants.AM_PRODUCT_ASIAINFO], self.vm_json["node_ip"]
        )
        return len(vms) >= 1

    def am_enabled(self) -> bool:
        anti_malware = self.vm_json.get("anti_malware", {})
        return anti_malware.get("enable") is True and anti_malware.get("product") == elf_constants.AM_PRODUCT_ASIAINFO

    def is_vmtools_running(self) -> bool:
        svt_data = vmtools_wrapper.VMToolsWrapper.get_vm_svt_data(self.vm_uuid)
        if not svt_data:
            return False

        return svt_data["static_info"].get("ga_state") == vmtools_constants.SVT_ACTIVE

    def save_db(self):
        update_doc = {"$set": {"anti_malware": self.vm_json["anti_malware"]}}
        query_doc = {
            "uuid": self.vm_uuid,
            "type": elf_constants.KVM_VM,
            "resource_state": elf_constants.RESOURCE_IN_USE,
        }
        ret = mongodb.resources.resource.update(query_doc, update_doc)
        return ret.get("nModified") == 1

    def ensure_save_db(self):
        if not self.save_db():
            raise Exception("save anti_malware failed")

    def gen_devices_for_disabling_am(self, libvirt_json):
        return self.vsc_mgr.gen_devices_to_detach_for_disabling_am(libvirt_json)

    def gen_devices_for_enabling_am(self, libvirt_json, am_idx):
        return self.vsc_mgr.gen_devices_to_attach_for_enabling_am(libvirt_json, self.vm_uuid, am_idx)

    def online_dpi(self, remote=False):
        mac_list = []
        for nic in self.vm_json.get("nics", []):
            model = nic.get("model", elf_constants.VM_INTERFACE_VIRTIO)
            if model not in (elf_constants.VM_INTERFACE_E1000, elf_constants.VM_INTERFACE_VIRTIO):
                continue

            if nic["type"] not in (elf_constants.NIC_TYPE_VLAN, elf_constants.NIC_TYPE_VPC):
                continue

            mac_list.append(nic["mac_address"])

        if remote:
            self._dpi_dev_sync_remote(mac_list)
        else:
            vs_controller.dpi_dev_sync(self.vm_uuid, mac_list)

    def offline_dpi(self, remote=False):
        if remote:
            self._dpi_dev_sync_remote([])
        else:
            vs_controller.dpi_dev_sync(self.vm_uuid, mac_addr_list=[])

    def release_devices(self, offline_dpi=True):
        if offline_dpi:
            self.offline_dpi()

        if not self.am_enabled():
            return

        vs_controller.am_dev_release(self.vm_uuid)

    def release_devices_remote(self, offline_dpi=True):
        if offline_dpi:
            self.offline_dpi(remote=True)

        if not self.am_enabled():
            return

        self._am_dev_release_remote_silently()

    def release_devices_silent(self, offline_dpi=True):
        try:
            self.release_devices(offline_dpi)
        except Exception as e:
            logging.warning("[release_devices_silent] err: {}".format(str(e)))

    def allocate_devices(self, libvirt_json, online_dpi=True):
        if online_dpi:
            self.online_dpi()

        if not self.am_enabled():
            return []

        am_idx = vs_controller.am_dev_acquire(self.vm_uuid)
        am_devices = self.gen_devices_for_enabling_am(libvirt_json, am_idx)

        return am_devices

    def allocate_devices_remote(self, libvirt_json) -> bool:
        self.online_dpi(remote=True)

        if not self.am_enabled():
            return False

        new_index = self._am_dev_acquire_remote()
        self.vsc_mgr.replace_shm_in_libvirt_json(libvirt_json, new_index)
        return True

    def attach_devices(self, libvirt_domain, am_device_jsons):
        flags = libvirt.VIR_DOMAIN_AFFECT_LIVE
        for device_json in am_device_jsons:
            dev_xml = xmltodict.unparse(device_json)
            libvirt_domain.attachDeviceFlags(dev_xml, flags)
            logging.info("Attach device success: %s" % device_json)

    def detach_devices(self, libvirt_domain, am_device_jsons):
        flags = libvirt.VIR_DOMAIN_AFFECT_LIVE
        for device_json in am_device_jsons:
            dev_xml = xmltodict.unparse(device_json)
            libvirt_domain.detachDeviceFlags(dev_xml, flags)
            count = 0
            while True:
                if count > LIBVIRT_LIVE_DETACH_DEVICE_TIMEOUT:
                    raise jc_excpt.ZJobError(
                        "Timeout when detach device. if you are using linux,"
                        " try to modprobe acpiphp in guest and remove again",
                        py_error.JOB_DETACH_DEVICE_TIMEOUT,
                    )

                libvirt_xml = libvirt_domain.XMLDesc(0)
                libvirt_json = xmltodict.parse(libvirt_xml)
                if not self.vsc_mgr.am_device_exists_in_gvm_libvirt_json(libvirt_json, device_json):
                    break

                time.sleep(3)
                count += 3
            logging.info("Detach device success: %s" % device_json)

    def _dpi_dev_sync_remote(self, mac_list):
        remote_client = self._get_remote_client()
        try:
            remote_client.post(
                "/elf/vs_controller/dpi_dev_sync",
                json={"vm_uuid": self.vm_uuid, "mac_addr_list": mac_list},
                check_ec=True,
            )
        except elf_client.ElfRestException.ElfAPIException as e:
            logging.warning(f"Failed to sync dpi devices for GVM {self.vm_uuid}: {e}")
            raise

    def _am_dev_release_remote_silently(self):
        """
        Silently release the device of the target node after live migration fails
        """
        remote_client = self._get_remote_client()
        try:
            remote_client.post("/elf/vs_controller/am_dev_release", json={"vm_uuid": self.vm_uuid})
        except Exception as e:
            logging.exception(f"Failed to release AM device of node {remote_client.server}: {e}")

    def _am_dev_acquire_remote(self):
        """
        Acquire the device of the target node for GVM before live migration
        """
        remote_client = self._get_remote_client()

        try:
            result = remote_client.post(
                "/elf/vs_controller/am_dev_acquire", json={"vm_uuid": self.vm_uuid}, check_ec=True
            )
            return result["index"]
        except elf_client.ElfRestException.ElfAPIException as e:
            logging.warning(f"Failed to acquire AM device for GVM {self.vm_uuid}: {e}")
            self._am_dev_release_remote_silently()
            raise

    def _get_remote_client(self):
        if not self.remote_client:
            raise RuntimeError(
                "Remote client is not set for AsiaInfo GVM wrapper.",
            )
        return self.remote_client
