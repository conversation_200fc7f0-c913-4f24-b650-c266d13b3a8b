# Copyright (c) 2022 SMARTX, Inc.
# All rights reserved.
import functools

from smartx_app.elf.common import constants as elf_constants
from smartx_app.elf.common.resource_wrappers import pci_device
from smartx_app.elf.common.utils import vm_start
from smartx_proto.errors import pyerror_pb2 as py_error


class PNIC(pci_device.PCIDevice):
    _subtype = elf_constants.PASS_THROUGH_NIC

    def __init__(self, uuid, guest_pci_address):
        super().__init__(uuid, guest_pci_address)


class PNICHandler(pci_device.PCIDeviceHandler):
    _subtype = elf_constants.PASS_THROUGH_NIC

    _dev_assign_failed_ec = py_error.PNIC_ASSIGN_FAILED
    _dev_release_failed_ec = py_error.PNIC_RELEASE_FAILED

    def __init__(self, vm_json):
        super().__init__(vm_json)

    def _initialize_devices(self):
        return [PNIC(dev["uuid"], dev.get("pci_address", None)) for dev in list(self._matched_host_devs.values())]

    def _release_device(self, host_uuid, device_id):
        return self._tuna_client.release_nic(self.vm_uuid, host_uuid, device_id)

    def _allocate_device(self, device_id):
        self._tuna_client.allocate_nic(self.vm_uuid, self.host_uuid, device_id)
        return self._tuna_client.get_nic_info(self.host_uuid, device_id)

    def _get_device_assign_infos(self, host_uuid):
        return self._tuna_client.get_nic_assign_infos(host_uuid)

    def release_pnic_silent(self):
        return self.release_device_silent()

    def allocate_pnic(self):
        return self.allocate_device()

    def allocate_mock_pnic(self):
        return self.allocate_mock_pci_address()

    def vm_has_pnic(self):
        return self.vm_has_matched_device()

    def update_pnic_guest_pci_address(self, domain_json):
        return self.update_device_guest_pci_address(domain_json)

    def release_unexpected_pnics_by_tuna_record(self):
        return self.release_unexpected_devices_by_tuna_record()


def release_pnic_on_start_failure(func):
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        try:
            return func(self, vm_json, *args, **kwargs)
        except Exception:
            PNICHandler(vm_json).release_pnic_silent()
            raise

    return wrapper


def allocate_pnic_to_start(func):
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        pnic_handler = PNICHandler(vm_json)
        if pnic_handler.vm_has_pnic():
            if vm_json["status"] == elf_constants.VM_RUNNING:
                kwargs["pnics"] = pnic_handler.allocate_pnic()
            else:
                kwargs["pnics"] = pnic_handler.allocate_mock_pnic()

        try:
            return func(self, vm_json, *args, **kwargs)
        except Exception:
            if vm_start.need_release_device_on_exception_after_start(vm_json, kwargs["libvirt_conn"]):
                pnic_handler.release_pnic_silent()
            raise

    return wrapper
