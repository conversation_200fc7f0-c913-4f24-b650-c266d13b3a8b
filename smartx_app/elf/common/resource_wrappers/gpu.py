# Copyright (c) 2022 SMARTX, Inc.
# All rights reserved.
import functools

from smartx_app.elf.common import constants as elf_constants
from smartx_app.elf.common.resource_wrappers import pci_device
from smartx_app.elf.common.utils import vm_start
from smartx_proto.errors import pyerror_pb2 as py_error


class GPU(pci_device.PCIDevice):
    _subtype = elf_constants.PASS_THROUGH_GPU

    def __init__(self, uuid, guest_pci_address):
        super().__init__(uuid, guest_pci_address)


class GPUHandler(pci_device.PCIDeviceHandler):
    _subtype = elf_constants.PASS_THROUGH_GPU

    _dev_assign_failed_ec = py_error.GPU_ASSIGN_FAILED
    _dev_release_failed_ec = py_error.GPU_RELEASE_FAILED

    def __init__(self, vm_json):
        super().__init__(vm_json)

    def _initialize_devices(self):
        return [GPU(dev["uuid"], dev.get("pci_address", None)) for dev in list(self._matched_host_devs.values())]

    def _release_device(self, host_uuid, dev_uuid):
        return self._tuna_client.release_gpu(self.vm_uuid, host_uuid, dev_uuid)

    def _allocate_device(self, dev_uuid):
        self._tuna_client.allocate_gpu(self.vm_uuid, self.host_uuid, dev_uuid)
        return self._tuna_client.get_gpu_info(self.host_uuid, dev_uuid)

    def _get_device_assign_infos(self, host_uuid):
        gpu_assign_infos = self._tuna_client.get_gpu_assign_infos(host_uuid)
        pass_through_gpu_assign_infos = [
            x for x in gpu_assign_infos if x["assign_type"] == elf_constants.GPU_USAGE_PASS_THROUGH
        ]

        return pass_through_gpu_assign_infos

    def release_gpu_silent(self):
        return self.release_device_silent()

    def allocate_gpu(self):
        return self.allocate_device()

    def allocate_mock_gpu(self):
        return self.allocate_mock_pci_address()

    def vm_has_gpu(self):
        return self.vm_has_matched_device()

    def update_gpu_guest_pci_address(self, domain_json):
        return self.update_device_guest_pci_address(domain_json)

    def release_unexpected_gpus_by_tuna_record(self):
        return self.release_unexpected_devices_by_tuna_record()


def release_gpu_on_start_failure(func):
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        try:
            return func(self, vm_json, *args, **kwargs)
        except Exception:
            GPUHandler(vm_json).release_gpu_silent()
            raise

    return wrapper


def allocate_gpu_to_start(func):
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        gpu_handler = GPUHandler(vm_json)
        if gpu_handler.vm_has_gpu():
            if vm_json["status"] == elf_constants.VM_RUNNING:
                kwargs["gpus"] = gpu_handler.allocate_gpu()
            else:
                kwargs["gpus"] = gpu_handler.allocate_mock_gpu()

        try:
            return func(self, vm_json, *args, **kwargs)
        except Exception:
            if vm_start.need_release_device_on_exception_after_start(vm_json, kwargs["libvirt_conn"]):
                gpu_handler.release_gpu_silent()
            raise

    return wrapper
