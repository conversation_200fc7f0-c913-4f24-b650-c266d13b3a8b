# Copyright (c) 2013-2018, SMARTX
# All rights reserved.
import logging
import time

from common.config import resources
from common.mongo.db import mongodb
from smartx_app.elf.common.resources.base import ResourceException
from smartx_proto.errors import pyerror_pb2 as py_error


class VMAdditionalInfoWrapper:
    VM_MIGRATE_INFO_DEST_KEY = "migrate_dest_node"
    _collection = mongodb.resources.vm_additional_info

    @classmethod
    def bulk(cls):
        return cls._collection.initialize_unordered_bulk_op()

    @classmethod
    def load(cls, vm_uuid):
        filter_criteria = {"vm_uuid": vm_uuid}
        vm_additional_info_doc = cls._collection.find_one(filter_criteria)
        if vm_additional_info_doc:
            return vm_additional_info_doc
        raise ResourceException("vm({}) is not exist.".format(vm_uuid), py_error.VM_NOT_FOUND)

    @classmethod
    def load_by_domain_uuid(cls, domain_uuid):
        filter_criteria = {"domain_uuid": domain_uuid}
        vm_additional_info_doc = cls._collection.find_one(filter_criteria)
        if vm_additional_info_doc:
            return vm_additional_info_doc
        raise ResourceException("vm({}) is not exist.".format(domain_uuid), py_error.VM_NOT_FOUND)

    @classmethod
    def remove(cls, vm_uuid, f_name=None, db_session=None):
        filter_criteria = {"vm_uuid": vm_uuid}
        if f_name:
            cls._collection.update_one(filter_criteria, {"$unset": {f_name: ""}}, session=db_session)
        else:
            cls._collection.delete_one(filter_criteria, session=db_session)

    @classmethod
    def raw_query(cls, filter_criteria=None, project_criteria=None):
        project_criteria = project_criteria or {}
        project_criteria["_id"] = 0

        return cls._collection.find(filter_criteria or {}, project_criteria)

    @classmethod
    def query(cls, vm_uuid_list, project_fields=None):
        if not vm_uuid_list:
            return []

        filter_criteria = {"vm_uuid": {"$in": list(vm_uuid_list)}}
        project_criteria = None
        if project_fields:
            project_criteria = {"vm_uuid": 1}
            project_criteria.update({x: 1 for x in project_fields})

        return list(cls.raw_query(filter_criteria, project_criteria))

    @classmethod
    def add(cls, vm_uuid, f_name, f_value, bulk=None):
        filter_criteria = {"vm_uuid": vm_uuid}
        update_doc = {"$set": {f_name: f_value}}

        if bulk is None:
            cls._collection.update_one(filter_criteria, update_doc, upsert=True)
        else:
            bulk.find(filter_criteria).upsert().update_one(update_doc)

    @classmethod
    def add_multiple(cls, vm_uuid, kv_dict, db_session=None):
        filter_criteria = {"vm_uuid": vm_uuid}
        update_doc = {"$set": kv_dict}
        cls._collection.update_one(filter_criteria, update_doc, upsert=True, session=db_session)

    @classmethod
    def add_domain_uuid(cls, vm_uuid, libvirt_conn=None):
        import libvirt

        from smartx_app.elf.common.utils.libvirt_driver import libvirt_connection

        def _add(_conn):
            s = time.time()
            while True:
                try:
                    return cls.add(vm_uuid, "domain_uuid", _conn.lookupByName(vm_uuid).UUIDString())
                except libvirt.libvirtError as e:
                    if e.get_error_code() == libvirt.VIR_ERR_NO_DOMAIN or (time.time() - s) >= 10:
                        return None
                    else:
                        time.sleep(1)

        if libvirt_conn:
            return _add(libvirt_conn)

        with libvirt_connection() as conn:
            return _add(conn)

    @classmethod
    def update_all_domain_uuid(cls):
        from smartx_app.elf.common.utils.libvirt_driver import libvirt_connection

        with libvirt_connection() as conn:
            domains = conn.listAllDomains()
            if domains:
                details = []
                bulk = cls.bulk()

                for dom in domains:
                    dom_name = dom.name()
                    dom_uuid = dom.UUIDString()

                    cls.add(vm_uuid=dom_name, f_name="domain_uuid", f_value=dom_uuid, bulk=bulk)
                    details.append((dom_name, dom_uuid))

                bulk.execute()
                logging.info("update all domain uuid{}: {}.".format(len(details), details))

    @classmethod
    def set_migrate_dest_node(cls, vm_uuid, dest_node):
        cls.add(vm_uuid=vm_uuid, f_name=cls.VM_MIGRATE_INFO_DEST_KEY, f_value=dest_node)

    @classmethod
    def remove_migrate_dest_node(cls, vm_uuid):
        cls.remove(vm_uuid=vm_uuid, f_name=cls.VM_MIGRATE_INFO_DEST_KEY)

    @classmethod
    def get_migrating_vms_by_dest_node(cls, dest_node):
        cursor = cls.raw_query(
            filter_criteria={cls.VM_MIGRATE_INFO_DEST_KEY: dest_node},
            project_criteria={"vm_uuid": 1},
        )
        return {item["vm_uuid"] for item in cursor}


def sync_domain_qemu_version():
    r"""
    Sync the qemu version to DB for backward compatibility.
    :return: None
    """
    from smartx_app.common import constants as app_constants
    from smartx_app.elf.common.utils import libvirt_driver

    versions = libvirt_driver.list_domain_qemu_version()
    if not versions:
        return

    cursor = mongodb.resources.resource.find(
        {
            "uuid": {"$in": [x for x, _ in versions]},
            "type": app_constants.KVM_VM,
            "resource_state": resources.RESOURCE_IN_USE,
        },
        {"_id": 0, "uuid": 1},
    )
    valid_vms = {x["uuid"] for x in cursor}

    if not valid_vms:
        return

    bulk = VMAdditionalInfoWrapper.bulk()
    for vm_uuid, version in versions:
        if vm_uuid in valid_vms:
            VMAdditionalInfoWrapper.add(vm_uuid, "qemu-kvm", {"version": version}, bulk)
    bulk.execute()
