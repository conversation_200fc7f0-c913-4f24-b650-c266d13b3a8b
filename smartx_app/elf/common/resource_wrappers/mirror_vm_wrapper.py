from abc import abstractmethod
import copy
import logging
import uuid

from smartx_app.elf.common import constants as ec_constants
from smartx_app.elf.common.resource_wrappers import vm_wrapper, volume_wrapper
from smartx_app.elf.common.resource_wrappers import vpc_nic as vpc_nic_wrapper
from smartx_app.elf.common.resources import base as elf_resource_base
from smartx_app.elf.common.resources import multi_cluster_migrate
from smartx_app.elf.common.resources import volume as volume_resource
from smartx_app.elf.common.utils import cluster as cluster_utils
from smartx_app.elf.common.utils import migrate_across_cluster
from smartx_app.elf.common.utils import mirror_vm as mirror_vm_utils
from smartx_app.elf.common.utils import network as network_utils
from smartx_proto.errors import pyerror_pb2 as py_error


class MirrorVMWrapper(vm_wrapper.VMWrapper):
    @classmethod
    def get_mirror_vm(cls, vm_uuid):
        class _MirrorVMQuerier(vm_wrapper.VMQuerier):
            def generate_common_query_criteria(self):
                query = super().generate_common_query_criteria()
                query["resource_state"] = ec_constants.RESOURCE_HIDDEN
                return query

        return cls.get(vm_uuid, querier=_MirrorVMQuerier())

    @classmethod
    def switchover_vm_resources_config(cls, mirror_vm):
        vm_uuid = mirror_vm["uuid"]

        migrate_across_cluster.update_volume_skip_all_zero(
            volume_uuids=[disk["volume_uuid"] for disk in mirror_vm["disks"] if disk["type"] == "disk"],
            new_skip_all_zero_first_write=False,
            batch_volume_loader=MirrorVolumeWrapper.batch_query_mirror_volumes,
        )

        migrate_across_cluster.apply_disks_quota_policy_vhost(
            disks=mirror_vm["disks"],
            batch_volume_loader=MirrorVolumeWrapper.batch_query_mirror_volumes,
        )

        if any([nic["type"] == ec_constants.NIC_TYPE_VPC for nic in mirror_vm.get("nics")]):
            vpc_nic_wrapper.VPCNICHandler(mirror_vm).update_vpc_nic_cluster_uuid(cluster_utils.get_cluster_id())
            logging.info(
                "[migrate_post_handle_vpc_nics_cluster_uuid] Update cluster uuid of VPC NICs on VM({}) succeed".format(
                    vm_uuid
                )
            )

            # Once VPC NICs are updated, The task `kvm_vm_remove_unexpected_vpc_resource` will try to remove
            # them. If the task finds VPC NICs are belong to the mirror VM, it should skip to remove them.

        migrate_across_cluster.batch_config_vnic_qos(mirror_vm)

        # After the switchover of the mirror VM is completed, the CPU QoS of all active VMs need to be reset.
        migrate_across_cluster.reset_cpu_qos_after_migrate()

    @classmethod
    def load(cls, vm_uuid):
        vm_docs = cls.query_from_db(vm_uuid)
        if vm_docs:
            return cls(vm_docs[0])

        raise elf_resource_base.ResourceException(
            "The mirror VM({}) is not exist.".format(vm_uuid), py_error.MIRROR_VM_NOT_FOUND
        )

    @classmethod
    def _query_from_db(cls, query=None, project=None):
        current_query = {"type": ec_constants.KVM_VM, "resource_state": ec_constants.RESOURCE_HIDDEN}
        if query:
            current_query.update(query)

        current_project = {"_id": 0}
        if project:
            current_project.update(project)

        return cls.db.resource.find(current_query, current_project)


class MirrorVolumeWrapper(volume_wrapper.VolumeWrapper):
    @classmethod
    def batch_query_mirror_volumes(cls, volume_uuid_list, projection=None):
        class _MirrorVolumeQuerier(volume_wrapper.VolumeQuerier):
            def generate_common_query_criteria(self):
                query = super().generate_common_query_criteria()
                query["resource_state"] = ec_constants.RESOURCE_HIDDEN
                return query

        return cls.batch_query(volume_uuid_list=volume_uuid_list, projection=projection, querier=_MirrorVolumeQuerier())


class ResourceBuilder:
    _context = None

    def __init__(self, original_vm, client_token):
        self._original_vm = original_vm

        self._mirror_vm = copy.deepcopy(original_vm)
        self._mirror_vm["mirror_vm"] = {
            "client_token": client_token,
            "context": self._context,
        }

        self._mirror_volumes = []

    def build(self) -> tuple[dict, list]:
        mirror_vm_utils.check_mirror_vm_create(mirror_vm=self._mirror_vm)

        multi_cluster_migrate.MultiClusterMigrateInfoV2.make_fields_compatible_at_dest(
            vm_json=self._mirror_vm,
            # The disk_path_to_volume_uuid is used to fill the volume_uuid in the VM disk.
            # The volume_uuid had been in the mirror_vm_json["disks"].
            # So pass an empty dict here.
            disk_path_to_volume_uuid={},
        )

        return self._mirror_vm, self._mirror_volumes

    @abstractmethod
    def with_mirror_nics(self, mirror_nics):
        """
        Build nics for the mirror VM.
        """
        raise NotImplementedError()

    @abstractmethod
    def with_mirror_disks(self, mirror_disks):
        """
        Build disks and volume for the mirror VM.
        """
        raise NotImplementedError()


class ACMResourceBuilder(ResourceBuilder):
    _context = mirror_vm_utils.MIRROR_VM_CONTEXT_OS_ACM

    def with_mirror_nics(self, mirror_nics):
        # TODO(yu.fan): move `dest_gen_mirror_vm_nics` to here
        self._mirror_vm["nics"] = network_utils.dest_gen_mirror_vm_nics(
            original_vm_nics=self._original_vm["nics"], mirror_nics_map=mirror_nics
        )

        return self

    def with_mirror_disks(self, mirror_disks):
        self._check_mirror_disks(mirror_disks)

        disks, volumes = vm_wrapper.VMWrapper.build_disks(raw_disks=mirror_disks)

        self._mirror_vm["disks"] = disks
        self._mirror_volumes = volumes

        return self

    def with_libvirt_access_info(self, transport):
        self._mirror_vm["mirror_vm"]["libvirt_transport"] = transport
        return self

    def _check_mirror_disks(self, mirror_disks):
        if len(mirror_disks) != len(self._mirror_vm["disks"]):
            raise elf_resource_base.ResourceException(
                "The mirror disks are not specified for the mirror VM({}) .".format(self._mirror_vm["uuid"]),
                py_error.VM_MIGRATE_ACROSS_CLUSTER_CHECK_ERROR,
            )


class SMTXELFACMResourceBuilder(ACMResourceBuilder):
    _context = mirror_vm_utils.MIRROR_VM_CONTEXT_ELF_ACM


class ACMComputationResourceBuilder(ACMResourceBuilder):
    _context = mirror_vm_utils.MIRROR_VM_CONTEXT_ELF_ACM_COMPUTATION

    def with_mirror_disks(self, mirror_disks):
        """
        For computation migration, no storage resources will be created.
        we do not build the disks and volumes by the `mirror_disks`, instead:
        1. copy disks and volumes from the original VM;
        2. generate new volume uuid for the mirror volume;
        3. config the destination storage policy for the mirror volume.
        """
        self._check_mirror_disks(mirror_disks)

        disks, volumes = [], []
        original_disks = self._original_vm["disks"]
        original_volumes = self._original_vm["volumes"]
        for i, mirror_disk in enumerate(mirror_disks):
            boot = i + 1
            original_disk = original_disks[i]

            if original_disk["type"] == "cdrom":
                disk = {
                    "type": original_disk["type"],
                    "bus": original_disk["bus"],
                    "path": "",  # For now, the ISO is not supported to migrate.
                    "boot": boot,
                    "key": original_disk["key"],
                    "disabled": original_disk.get("disabled", False),
                    "storage_policy_uuid": None,
                    "quota_policy": None,
                    # "host": "", # For now, the ISO is not supported to migrate on the arch SMTX ELF.
                }

                disks.append(disk)
            else:
                original_volume = original_volumes.get(original_disk["volume_uuid"])
                if not original_volume:
                    raise elf_resource_base.ResourceException(
                        "The volume({}) of disk is not specified".format(original_disk["volume_uuid"]),
                        py_error.VM_MIGRATE_ACROSS_CLUSTER_CHECK_ERROR,
                    )
                # The conflict of the volume_uuid between two clusters, will
                # cause the volume management issue for the upper layer platform.
                # Re-generate the volume_uuid here to avoid the conflict.
                new_volume_uuid = str(uuid.uuid4())

                disk = {
                    "volume_uuid": new_volume_uuid,
                    "storage_policy_uuid": mirror_disk["storage_policy_uuid"],
                    "boot": boot,
                    "type": original_disk["type"],
                    "bus": original_disk["bus"],
                    "serial": original_disk["serial"],
                    "path": original_disk["path"],
                    "quota_policy": original_disk.get("quota_policy", None),
                }
                if "resident_in_cache" in original_disk:
                    disk["resident_in_cache"] = original_disk["resident_in_cache"]

                volume = volume_resource.Volume.create(original_volume)
                volume.uuid = new_volume_uuid
                volume.volume_uuid = new_volume_uuid
                volume.storage_policy_uuid = mirror_disk["storage_policy_uuid"]
                volume.zbs_volume_id = original_volume["zbs_volume_id"]

                disks.append(disk)
                volumes.append(volume)

        self._mirror_vm["disks"] = disks
        self._mirror_volumes = volumes
        self._mirror_vm.pop("volumes", None)

        return self

    def _check_mirror_disks(self, mirror_disks):
        super()._check_mirror_disks(mirror_disks)

        for disk in self._mirror_vm["disks"]:
            if disk["type"] == "cdrom":
                continue

            if disk["volume_uuid"] not in self._mirror_vm["volumes"]:
                raise elf_resource_base.ResourceException(
                    "The mirror volume({}) is not specified for the mirror disk({}) in the context ({}) .".format(
                        disk["volume_uuid"], disk["serial"], self._context
                    ),
                    py_error.VM_MIGRATE_ACROSS_CLUSTER_CHECK_ERROR,
                )


class ACMComputationColdResourceBuilder(ACMComputationResourceBuilder):
    _context = mirror_vm_utils.MIRROR_VM_CONTEXT_ELF_ACM_COMPUTATION_COLD
