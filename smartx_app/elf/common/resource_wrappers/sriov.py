# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
import copy
import functools
import itertools
import logging

from common.http import exceptions, tuna
from smartx_app.common.node import db
from smartx_app.elf.common import constants as elf_constants
from smartx_app.elf.common.resources import base as base_exception
from smartx_app.elf.common.utils import cmd
from smartx_proto.errors import pyerror_pb2 as py_error


class _SRIOVAssignID:
    def __init__(self, **kwargs):
        self.index = kwargs.get("index", -1)
        self._vm_id = kwargs.get("vm_id")
        self._mac_address = kwargs.get("mac_address")
        self._assign_id = kwargs.get("assign_id")
        self._split_char = "_"

    @property
    def vm_id(self):
        if not self._vm_id and self._assign_id:
            self._vm_id, self._mac_address = self._assign_id.split(self._split_char)
        return self._vm_id

    @property
    def mac_address(self):
        if not self._mac_address and self._assign_id:
            self._vm_id, self._mac_address = self._assign_id.split(self._split_char)
        return self._mac_address

    @property
    def assign_id(self):
        if not self._assign_id and self._vm_id and self._mac_address:
            self._assign_id = self._split_char.join([self._vm_id, self._mac_address])
        return self._assign_id


class _SRIOVNic:
    def __init__(self, nic):
        self.vlan_uuid = nic["vlan_uuid"]
        self.vlan_id = nic["vlans"][0]["vlan_id"]
        self.mac_address = nic["mac_address"].lower()
        self.model = elf_constants.VM_INTERFACE_SRIOV
        self.pf_id = nic["pf_id"]
        self.assigned_vf = None
        self.pci_address_in_vm = nic.get("pci_address")

    @property
    def domain_json(self):
        interface = {
            "source": {"address": self.assigned_vf.domain_pci_address},
            "@type": "hostdev",
            "@managed": "yes",
            "mac": {"@address": self.mac_address},
            "vlan": {"tag": {"@id": str(self.vlan_id)}},
        }
        if self.pci_address_in_vm:
            interface["address"] = self.pci_address_in_vm

        return interface


class VF:
    def __init__(self, vf):
        self.index = vf.get("index")  # index is not necessary when config vf vlan id.
        self.domain = vf.get("domain", "0000")
        self.bus = vf["bus"]
        self.slot = vf["slot"]
        self.function = vf["function"]
        self.sriov_assign_id = None
        if vf.get("assign_id"):
            self.sriov_assign_id = _SRIOVAssignID(index=self.index, assign_id=vf["assign_id"])

    @property
    def pci_address(self):
        return "{}:{}:{}.{}".format(self.domain, self.bus, self.slot, self.function)

    @property
    def domain_pci_address(self):
        return {
            "@type": "pci",
            "@domain": "0x" + self.domain,
            "@bus": "0x" + self.bus,
            "@slot": "0x" + self.slot,
            "@function": "0x" + self.function,
        }

    @staticmethod
    def _execute_cmd(command):
        ret = cmd.execute_cmd(command, timeout=10)
        if ret[0] != 0:
            msg = "Execute command [{0}] failed, return code is {1[0]}, stdout is {1[1]}, stderr is {1[2]}.".format(
                command, ret
            )
            logging.error(msg)
            raise base_exception.ResourceException(msg, user_code=py_error.CHANGE_SRIOV_VF_ERROR)
        return ret[1]

    def set_vf_vlan_id(self, vlan_id):
        # Get vf num
        command = (
            "ls -al /sys/bus/pci/devices/{0}/physfn/virtfn* | "
            "awk 'match($0, /virtfn([0-9]+) .*{0}$/, vf_num) {{print vf_num[1]}}'"
        ).format(self.pci_address)
        vf_num = int(self._execute_cmd(command).strip())

        # Get physical interface
        command = "ls /sys/bus/pci/devices/{}/physfn/net/".format(self.pci_address)
        interface = self._execute_cmd(command).strip()

        # Set vf vlan id
        command = "ip link set {} vf {} vlan {}".format(interface, vf_num, vlan_id)
        self._execute_cmd(command)


class _PF:
    def __init__(self, pf):
        self.name = pf["name"]
        self.pf_id = pf["uuid"]
        self.totalvfs = pf["totalvfs"]
        self.numvfs = pf["numvfs"]
        self.vfs = [VF(vf) for vf in pf["vfs"]]
        self.sriov_assign_ids = [_SRIOVAssignID(**assign_id) for assign_id in pf["assigned_vfs"]]
        self._match_assign_id()

    def _match_assign_id(self):
        m = {x.index: x for x in self.sriov_assign_ids}
        for vf in self.vfs:
            if vf.index in m:
                vf.sriov_assign_id = m[vf.index]

    def get_vm_assigned_vfs(self, vm_id):
        return [vf for vf in self.vfs if vf.sriov_assign_id and vf.sriov_assign_id.vm_id == vm_id]


class SRIOVHandler:
    def __init__(self, vm_json):
        self.vm_id = vm_json["uuid"]
        self._host_data_ip = vm_json["node_ip"]
        self._host_uuid = None
        self.sriov_nics = [
            _SRIOVNic(nic) for nic in vm_json["nics"] if nic.get("model") == elf_constants.VM_INTERFACE_SRIOV
        ]
        self._pf_assign_ids = None
        self._tuna_client = tuna.Client()
        self._vm_json = vm_json

    @property
    def host_uuid(self):
        if not self._host_uuid:
            self._host_uuid = db.query_host_by_data_ip(self._host_data_ip)["host_uuid"]
        return self._host_uuid

    @property
    def pf_assign_ids(self):
        if self._pf_assign_ids is None:
            self._pf_assign_ids = {}
            for nic in self.sriov_nics:
                sriov_assign_id = _SRIOVAssignID(vm_id=self.vm_id, mac_address=nic.mac_address)
                self._pf_assign_ids.setdefault(nic.pf_id, []).append(sriov_assign_id.assign_id)
        return copy.deepcopy(self._pf_assign_ids)

    def allocate_vfs(self):
        for pf_id, assign_ids in list(self.pf_assign_ids.items()):
            try:
                vf_pci_addresses = self._tuna_client.assign_vfs(self.host_uuid, pf_id, assign_ids)
                vfs = [VF(vf_pci_address) for vf_pci_address in vf_pci_addresses]
                if len(vfs) != len(assign_ids):
                    raise base_exception.ResourceException(
                        "Assigned vfs({}) num not match request assign ids({}).".format(vf_pci_addresses, assign_ids),
                        user_code=py_error.ASSIGNED_VFS_NUM_NOT_MATCH,
                    )

                vfs_mapping = {vf.sriov_assign_id.assign_id: vf for vf in vfs}
                for nic in self.sriov_nics:
                    if nic.pf_id == pf_id:
                        nic_assign_id = _SRIOVAssignID(vm_id=self.vm_id, mac_address=nic.mac_address)
                        nic.assigned_vf = vfs_mapping[nic_assign_id.assign_id]
            except Exception as e:
                logging.exception(
                    "Allocate vfs for vm({}) assign_ids({}) failed, error is {}.".format(self.vm_id, assign_ids, str(e))
                )
                # Raise the error is to let decorator allocate_vfs capture and then release the VF correctly
                raise

        return self.sriov_nics

    def allocate_mock_vfs(self):
        for nic in self.sriov_nics:
            nic.assigned_vf = VF({"bus": "ff", "slot": "1f", "function": "7"})
        return self.sriov_nics

    def release_vfs(self):
        for pf_id, assign_ids in list(self.pf_assign_ids.items()):
            try:
                self._tuna_client.release_vfs(self.host_uuid, pf_id, assign_ids)
            except Exception as e:
                logging.exception("Release vfs for vm({}) failed, error is {}.".format(self.vm_id, str(e)))

    def release_detached_vfs(self):
        try:
            pfs = [_PF(_pf) for _pf in self._tuna_client.get_host_pfs(self.host_uuid)]
            for pf in pfs:
                vm_assign_ids = self.pf_assign_ids.get(pf.pf_id, [])
                pf_assigned_ids = {vf.sriov_assign_id.assign_id for vf in pf.get_vm_assigned_vfs(self.vm_id)}
                detached_vfs = pf_assigned_ids - set(vm_assign_ids)
                if detached_vfs:
                    self._tuna_client.release_vfs(self.host_uuid, pf.pf_id, list(detached_vfs))
        except Exception as e:
            logging.exception("Release detach vfs for vm {} failed. error is {}.".format(self.vm_id, str(e)))

    def sync_vfs_with_domain(self, vf_pci_addresses):
        reserved_vfs = []  # Mark with mac address

        pfs = [_PF(_pf) for _pf in self._tuna_client.get_host_pfs(self.host_uuid)]
        for pf in pfs:
            vf_release_ids = []
            assigned_vfs = pf.get_vm_assigned_vfs(self.vm_id)
            for assigned_vf in assigned_vfs:
                if assigned_vf.domain_pci_address not in vf_pci_addresses:
                    vf_release_ids.append(assigned_vf.sriov_assign_id.assign_id)
                else:
                    reserved_vfs.append(assigned_vf.sriov_assign_id.mac_address)

            try:
                if vf_release_ids:
                    self._tuna_client.release_vfs(self.host_uuid, pf.pf_id, vf_release_ids)
            except Exception:
                logging.exception(
                    "Release vfs({}) of pf({}) for vm({}) failed.".format(pf.pf_id, vf_release_ids, self.vm_id)
                )

        return reserved_vfs

    @staticmethod
    def parse_vm_uuid_from_assign_id(assign_id: str) -> str:
        return _SRIOVAssignID(assign_id=assign_id).vm_id

    def _get_device_assign_infos(self, host_uuid) -> list:
        result = []

        nic_assign_infos = self._tuna_client.get_nic_assign_infos(host_uuid)
        sriov_nic_assign_infos = [x for x in nic_assign_infos if x["assign_type"] == elf_constants.NIC_USAGE_SRIOV]
        """ assign info format
        {
            "device_id": "ff966759-7ad2-50b7-8005-23bd304c07f9",
            "assign_type": "sriov",
            "assigned_vfs": [
                {
                    "index": 0,
                    "assign_id": "73f669c5-09b1-452f-ad72-87a83d0c6519_52:54:00:66:e6:c1"
                }
            ],
        }
        """
        for x in sriov_nic_assign_infos:
            for y in x["assigned_vfs"]:
                if y["assign_id"]:
                    vm_uuid = self.parse_vm_uuid_from_assign_id(y["assign_id"])
                    if vm_uuid == self.vm_id:
                        result.append(
                            {
                                "host_uuid": host_uuid,
                                "assign_id": y["assign_id"],
                                "device_uuid": x["device_id"],
                            }
                        )

        return result

    def release_unexpected_sriov_nics_by_tuna_record(self):
        host_uuids = [host["host_uuid"] for host in db.query_hosts()]
        tuna_assign_infos_by_host = {}
        for host_uuid in host_uuids:
            assign_info_of_one_host = self._get_device_assign_infos(host_uuid)
            tuna_assign_infos_by_host.setdefault(host_uuid, []).extend(assign_info_of_one_host)

        current_sriov_nic_assign_ids = []
        if self._vm_json["status"] in elf_constants.VM_ACTIVE_STATES:
            for device_uuid, assign_ids in self.pf_assign_ids.items():
                current_sriov_nic_assign_ids.extend([(device_uuid, x) for x in assign_ids])

        unexpected_assign_infos = {}
        for host_uuid, assign_infos in tuna_assign_infos_by_host.items():
            unexpected_assign_infos[host_uuid] = [
                x for x in assign_infos if (x["device_uuid"], x["assign_id"]) not in current_sriov_nic_assign_ids
            ]

        if not unexpected_assign_infos:
            return

        logging.info("VM({}) has unexpected sriov_nic assign_infos({})".format(self.vm_id, unexpected_assign_infos))
        for host_uuid, tuna_assign_info in unexpected_assign_infos.items():
            for pf_id, grouped in itertools.groupby(
                tuna_assign_info, key=lambda assign_info: assign_info["device_uuid"]
            ):
                assign_ids = [x["assign_id"] for x in list(grouped)]
                if not assign_ids:
                    continue
                try:
                    self._tuna_client.release_vfs(host_uuid, pf_id, assign_ids)
                except exceptions.RestfulClientError as e:
                    if e.user_code in (
                        py_error.DEVICE_NOT_FOUND,  # PNIC has been unmounted on host
                        py_error.DEVICE_NOT_ALLOCATED,  # PNIC released in last shutdown
                        py_error.DEVICE_USAGE_NOT_MATCH,  # PNIC usage has been set to 'pass-through'
                    ):
                        logging.info(
                            "Release SR-IOV NICs(host_uuid: {}, pf_id: {}, assign_ids: {}) got acceptable error code "
                            "{}".format(host_uuid, pf_id, assign_ids, str(e))
                        )
                    else:
                        raise base_exception.ResourceException(
                            "Release SR-IOV NICs (host_uuid: {}, pf_id: {}, assign_ids: {}) failed, "
                            "error is ({})".format(host_uuid, pf_id, assign_ids, str(e)),
                            py_error.SRIOV_NIC_RELEASE_FAILED,
                        )
                except Exception as e:
                    raise base_exception.ResourceException(
                        "Release SR-IOV NICs (host_uuid: {}, pf_id: {}, assign_ids: {}) failed, error is ({})".format(
                            host_uuid, pf_id, assign_ids, str(e)
                        ),
                        py_error.SRIOV_NIC_RELEASE_FAILED,
                    )


def release_vfs_on_failure(func):
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        try:
            return func(self, vm_json, *args, **kwargs)
        except Exception:
            SRIOVHandler(vm_json).release_vfs()
            raise

    return wrapper


def _get_vfs_from_domain(vm_domain):
    from smartx_app.elf.job_center.lib.converter.domain import domain_xml_to_libvirt_json

    libvirt_xml = vm_domain.XMLDesc(0)
    libvirt_json = domain_xml_to_libvirt_json(libvirt_xml)
    interfaces = libvirt_json["domain"]["devices"].get("interface", [])
    vfs = [nic for nic in interfaces if nic["@type"] == "hostdev"]
    vf_source_addresses = [vf["source"]["address"] for vf in vfs]
    vf_pci_addresses = [
        {
            "@type": "pci",
            "@domain": address.get("@domain"),
            "@bus": address.get("@bus"),
            "@slot": address.get("@slot"),
            "@function": address.get("@function"),
        }
        for address in vf_source_addresses
    ]
    return vf_pci_addresses


def _sync_vfs_with_vm_json(vm_uuid, receive_nics, reserved_vf_mac_addresses):
    from smartx_app.elf.common.resource_wrappers import vm_wrapper as vm

    vm_info = vm.VMWrapper.load(vm_uuid).vm_doc
    db_vm_nics = vm_info.get("nics", [])

    # We need to keep all the vfs that exist in the domain.
    # Since the failed operation may be to add vfs or remove vfs,
    # it is necessary to combine the incoming nics from the front end
    # and the saved nics in the database at the same time.
    reserved_vfs = {}
    for nic in db_vm_nics:
        if nic["mac_address"] in reserved_vf_mac_addresses:
            reserved_vfs[nic["mac_address"]] = nic
    # NOTE: This is temporarily unable to handle nic change failed.
    # That is to say, it is assumed that the incoming nics has the
    # same properties as the same nics in the database.
    for nic in receive_nics:
        if nic["mac_address"] not in reserved_vfs and nic["mac_address"] in reserved_vf_mac_addresses:
            reserved_vfs[nic["mac_address"]] = nic

    # The way to deal with virtual nics:
    # 1. Ignore incoming nics from the front end.
    # 2. Reserve all virtual nics in the database.
    new_vm_nics = []
    for nic in db_vm_nics:
        if nic.get("model") != elf_constants.VM_INTERFACE_SRIOV:
            new_vm_nics.append(nic)

    new_vm_nics.extend(list(reserved_vfs.values()))
    vm.VMWrapper.update_nics(vm_uuid, new_vm_nics)


def _allocate_vfs(sriov_handler, vm_status):
    allocate_real_vfs = vm_status == elf_constants.VM_RUNNING

    if allocate_real_vfs:
        vfs = sriov_handler.allocate_vfs()
    else:
        vfs = sriov_handler.allocate_mock_vfs()

    return allocate_real_vfs, vfs


def allocate_vfs_on_create(func):
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        sriov_handler = SRIOVHandler(vm_json)
        allocate_real_vfs, kwargs["vfs"] = _allocate_vfs(sriov_handler, vm_json["status"])

        try:
            return func(self, vm_json, *args, **kwargs)
        except Exception:
            if allocate_real_vfs:
                sriov_handler.release_vfs()
            raise

    return wrapper


def allocate_vfs_on_config(func):
    def _try_sync(vm_domain, sriov_handler, vm_json):
        try:
            vf_pci_addresses = _get_vfs_from_domain(vm_domain)
            reserved_vf_mac_addresses = sriov_handler.sync_vfs_with_domain(vf_pci_addresses)
            _sync_vfs_with_vm_json(vm_json["uuid"], vm_json.get("nics", []), reserved_vf_mac_addresses)
        except Exception as e:
            logging.warning("Sync vm({}) vfs failed. Error is {}".format(vm_json["uuid"], str(e)))

    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        import libvirt

        sriov_handler = SRIOVHandler(vm_json)
        vm_uuid = vm_json["uuid"]
        libvirt_conn = kwargs["libvirt_conn"]

        vm_domain = libvirt_conn.lookupByName(vm_uuid)
        vm_state = vm_domain.state()[0]
        allocate_real_vfs, kwargs["vfs"] = _allocate_vfs(sriov_handler, vm_json["status"])

        try:
            result = func(self, vm_json, *args, **kwargs)
        except Exception:
            if allocate_real_vfs:
                if vm_state == libvirt.VIR_DOMAIN_RUNNING:
                    _try_sync(vm_domain, sriov_handler, vm_json)
                else:
                    # Start vm depends on config vm, if config failed before starting vm,
                    # we need to release all vfs.
                    sriov_handler.release_vfs()
            raise
        else:
            if vm_state == libvirt.VIR_DOMAIN_RUNNING:
                sriov_handler.release_detached_vfs()
            return result

    return wrapper


def exclude_sriov_nics(nics):
    return [nic for nic in nics if nic.get("model") != elf_constants.VM_INTERFACE_SRIOV]


def get_assigned_but_unused_vfs():
    from smartx_app.elf.common.resource_wrappers import vm_wrapper as vm

    cluster_pfs = tuna.Client().get_cluster_pfs()
    assigned_but_unused_vfs = {}

    for host_uuid, pfs in list(cluster_pfs.items()):
        assigned_but_unused_vfs[host_uuid] = {}
        for _pf in pfs:
            pf_assigned_vfs = {}
            pf_assigned_but_unused_vfs = {}
            pf = _PF(_pf)
            for assign_id in pf.sriov_assign_ids:
                pf_assigned_vfs.setdefault(assign_id.vm_id, set()).add(assign_id.mac_address)

            for vm_id, mac_addresses in list(pf_assigned_vfs.items()):
                # If VM status is stopped or deleted, need to release all assigned VFs.
                pf_assigned_but_unused_vfs[vm_id] = mac_addresses
                try:
                    vm_json = vm.VMWrapper.load(vm_id).vm_doc
                    # The virtual machine may passively enter the SUSPENDED state due to some abnormal problems.
                    # In this case, the vf will not release.
                    if vm_json["status"] in [
                        elf_constants.VM_RUNNING,
                        elf_constants.VM_SUSPENDED,
                    ]:
                        vm_sriov_handler = SRIOVHandler(vm_json)
                        vm_mac_addresses = {
                            nic.mac_address for nic in vm_sriov_handler.sriov_nics if nic.pf_id == pf.pf_id
                        }
                        pf_assigned_but_unused_vfs[vm_id] = mac_addresses - vm_mac_addresses
                except base_exception.ResourceException as e:
                    if e.user_code != py_error.VM_NOT_FOUND:
                        raise
                    logging.info("VM {} may be deleted.".format(vm_id))

            if pf_assigned_but_unused_vfs:
                assigned_but_unused_vfs[host_uuid][pf] = pf_assigned_but_unused_vfs

    return assigned_but_unused_vfs


def release_assigned_but_unused_vfs(host_uuid, pf_id, vm_id, mac_address):
    from smartx_app.elf.common.resource_wrappers import vm_wrapper as vm

    release_id = _SRIOVAssignID(vm_id=vm_id, mac_address=mac_address)
    try:
        vm_json = vm.VMWrapper.load(vm_id).vm_doc
        vm_sriov_handler = SRIOVHandler(vm_json)
        if vm_json["status"] in [
            elf_constants.VM_RUNNING,
            elf_constants.VM_SUSPENDED,
        ]:
            for nic in vm_sriov_handler.sriov_nics:
                if pf_id == nic.pf_id and mac_address == nic.mac_address:
                    logging.warning("This VF is in-used, can not release!")
                    return False
    except base_exception.ResourceException as e:
        if e.user_code != py_error.VM_NOT_FOUND:
            raise
        logging.info("VM {} may be deleted.".format(vm_id))

    tuna_client = tuna.Client()
    tuna_client.release_vfs(host_uuid, pf_id, [release_id.assign_id])
    return True
