# Copyright (c) 2013-2021, SMARTX
# All rights reserved.

import abc
import functools
import logging

from common.http import exceptions, tuna
from smartx_app.common.node import db
from smartx_app.elf.common import constants as elf_constants
from smartx_app.elf.common.resources import base
from smartx_app.elf.job_center import constants as elf_jc_constants
from smartx_proto.errors import pyerror_pb2 as py_error


class USB(metaclass=abc.ABCMeta):
    def __init__(self, usb_uuid, usb_type, usb_host_id):
        # usb info from vm hostdev field.
        self.usb_id = usb_uuid
        self.type = usb_type
        self.host_id = usb_host_id

        # usb base info.
        self.name = None
        self.vendor_id = None
        self.product_id = None
        self.busnum = None
        self.devnum = None
        self.speed = None
        self.usb_type = None
        self.sys_path = None

        # usb assign info.
        self.assign_id = None
        self.host_ip = None
        self.server_port = None

        self._tuna_client = tuna.Client()

    @abc.abstractmethod
    def set_assign_info(self, assign_info):
        pass

    def update_assign_info(self):
        assign_info = self._tuna_client.get_usb_assign_info(self.host_id, self.usb_id)
        self.set_assign_info(assign_info)

    def update_base_info(self):
        usb = self._tuna_client.get_usb(self.host_id, self.usb_id)

        self.name = usb["name"]
        self.vendor_id = usb["vendor_id"]
        self.product_id = usb["product_id"]
        self.busnum = usb["busnum"]
        self.devnum = usb["devnum"]
        self.speed = usb["speed"]
        self.usb_type = usb["usb_type"]
        self.sys_path = usb["sys_path"]

    @abc.abstractmethod
    def allocate(self, assign_id):
        pass

    @abc.abstractmethod
    def release(self, assign_id):
        pass

    @abc.abstractmethod
    def gen_libvirt_json(self, bus, port=None):
        # Generate libvirt json, used to assemble the xml of the device.
        pass

    @property
    @abc.abstractmethod
    def address(self):
        pass

    @abc.abstractmethod
    def dump(self):
        # Generate vm json, store in vm["hostdevs"].
        pass

    @property
    @abc.abstractmethod
    def identity(self):
        pass


class PassThroughUSB(USB):
    def __init__(self, usb_uuid, usb_type, usb_host_id):
        super().__init__(usb_uuid, usb_type, usb_host_id)

    def set_assign_info(self, assign_info):
        self.assign_id = assign_info["assign_id"]

    def allocate(self, assign_id):
        assign_info = self._tuna_client.allocate_pass_through_usb(self.host_id, self.usb_id, assign_id)
        self.set_assign_info(assign_info)

    def release(self, assign_id):
        self._tuna_client.release_pass_through_usb(self.host_id, self.usb_id, assign_id)

    def gen_libvirt_json(self, bus, port=None):
        return {
            "source": {"address": self.address},
            "@mode": "subsystem",
            "@type": "usb",
            "alias": {"@name": "ua-{}_{}".format(self.host_id, self.usb_id)},
            "address": {"@type": "usb", "@bus": str(bus)}
            if port is None
            else {"@type": "usb", "@bus": str(bus), "@port": str(port)},
        }

    @property
    def address(self):
        return {"@bus": str(self.busnum or ""), "@device": str(self.devnum or "")}

    def dump(self):
        return {
            "uuid": self.usb_id,
            "type": elf_constants.PASS_THROUGH_USB,
            "usb_type": self.usb_type or "",
            "name": self.name or "",
            "host_id": self.host_id,
            "sys_path": self.sys_path or "",
            "address": self.address,
        }

    @property
    def identity(self):
        return hash((str(self.busnum), str(self.devnum)))


class OverNetworkUSB(USB):
    def __init__(self, usb_uuid, usb_type, usb_host_id):
        super().__init__(usb_uuid, usb_type, usb_host_id)

    def set_assign_info(self, assign_info):
        self.assign_id = assign_info["assign_id"]
        self.host_ip = assign_info["info"]["server"]
        self.server_port = assign_info["info"]["port"]

    def allocate(self, assign_id):
        assign_info = self._tuna_client.allocate_over_network_usb(self.host_id, self.usb_id, assign_id)
        self.set_assign_info(assign_info)

    def release(self, assign_id):
        self._tuna_client.release_over_network_usb(self.host_id, self.usb_id, assign_id)

    def gen_libvirt_json(self, bus, port=None):
        return {
            "source": self.address,
            "@bus": "usb",
            "@type": "tcp",
            "alias": {"@name": "ua-{}_{}".format(self.host_id, self.usb_id)},
            "address": {"@type": "usb", "@bus": str(bus)}
            if port is None
            else {"@type": "usb", "@bus": str(bus), "@port": str(port)},
        }

    @property
    def address(self):
        return {
            "@mode": "connect",
            "@host": self.host_ip,
            "@service": str(self.server_port),
            "reconnect": {"@enabled": "yes", "@timeout": str(elf_jc_constants.USB_RECONNECT_TIME)},
        }

    def dump(self):
        return {
            "uuid": self.usb_id,
            "type": elf_constants.OVER_NETWORK_USB,
            "host_id": self.host_id,
            "name": self.name or "",
        }

    @property
    def identity(self):
        return hash((self.host_ip, str(self.server_port)))


def _is_usb_device(hostdev):
    return hostdev["type"] in (
        elf_constants.PASS_THROUGH_USB,
        elf_constants.OVER_NETWORK_USB,
    )


def create_usb(usb_uuid, usb_type, usb_host_uuid):
    if usb_type == elf_constants.PASS_THROUGH_USB:
        return PassThroughUSB(usb_uuid, usb_type, usb_host_uuid)
    elif usb_type == elf_constants.OVER_NETWORK_USB:
        return OverNetworkUSB(usb_uuid, usb_type, usb_host_uuid)
    else:
        raise ValueError("{} not a valid usb type.".format(usb_type))


def create_usb_by_assign_info(assign_info):
    type_map = {
        elf_constants.TUNA_PASS_THROUGH_USB_TYPE: elf_constants.PASS_THROUGH_USB,
        elf_constants.TUNA_OVER_NETWORK_USB_TYPE: elf_constants.OVER_NETWORK_USB,
    }
    usb = create_usb(assign_info["device_id"], type_map.get(assign_info["assign_type"], None), assign_info["host_uuid"])
    usb.set_assign_info(assign_info)
    return usb


class USBHandler:
    def __init__(self, vm_json):
        self.vm_id = vm_json["uuid"]
        self._vm_json = vm_json
        self._usbs = None

    @property
    def usbs(self):
        if self._usbs is None:
            hostdevs = self._vm_json.get("hostdevs", [])
            self._usbs = [
                create_usb(usb["uuid"], usb["type"], usb["host_id"]) for usb in hostdevs if _is_usb_device(usb)
            ]

        return self._usbs

    def allocate_usbs(self):
        for usb in self.usbs:
            try:
                usb.allocate(self.vm_id)
            except Exception as e:
                raise base.ResourceException(
                    "Allocate USB({}) for VM({}) failed, error is ({})".format(usb.usb_id, self.vm_id, e),
                    py_error.USB_ASSIGN_FAILED,
                )

            usb.update_base_info()

        return self.usbs

    def release_unexpected_usbs_by_tuna_record(self):
        # Device assignment relationship of ELF needs to be synchronized with TUNA.
        # Examples of usage scenarios:
        # Release USBs in case some USBs release failed in last shutdown.
        # Hostdevs may be unmounted after shutdown.
        host_uuids = [host["host_uuid"] for host in db.query_hosts()]
        tuna_client = tuna.Client()
        all_tuna_assigned_usbs = []
        for host_uuid in host_uuids:
            tuna_assigned_usbs_of_host = [
                create_usb_by_assign_info(assign_info)
                for assign_info in tuna_client.get_usb_assign_infos(host_uuid)
                if assign_info["assign_id"]
            ]
            all_tuna_assigned_usbs.extend(tuna_assigned_usbs_of_host)

        # Need to determine the current state of the VM,
        # if it is on, then we need to clean up USBs that are not in vm_json
        # if it is off, just release all USBs in vm_json
        current_attached_usb_ids = []
        if self._vm_json["status"] in (
            elf_constants.VM_RUNNING,
            elf_constants.VM_SUSPENDED,
        ):
            current_attached_usb_ids = [usb.usb_id for usb in self.usbs]

        need_to_release_usbs = [
            usb
            for usb in all_tuna_assigned_usbs
            if usb.assign_id == self.vm_id and usb.usb_id not in current_attached_usb_ids
        ]
        if not need_to_release_usbs:
            return

        # release unexpected usbs
        logging.info("VM({}) has unexpected USBs({})".format(self.vm_id, need_to_release_usbs))
        for usb in need_to_release_usbs:
            try:
                usb.release(self.vm_id)
            except exceptions.RestfulClientError as e:
                if e.user_code not in (
                    py_error.DEVICE_NOT_FOUND,
                    py_error.DEVICE_NOT_ALLOCATED,
                    py_error.RELEASE_ID_NOT_MATCH,
                ):
                    raise base.ResourceException(
                        "Release USB({}) on host({}) for VM ({}) failed, error is ({})".format(
                            usb.usb_id, usb.host_id, self.vm_id, e
                        ),
                        py_error.USB_RELEASE_FAILED,
                    )
            except Exception as e:
                raise base.ResourceException(
                    "Release USB({}) on host({}) for VM ({}) failed, error is ({})".format(
                        usb.usb_id, usb.host_id, self.vm_id, e
                    ),
                    py_error.USB_RELEASE_FAILED,
                )

    def release_usbs_silent(self):
        for usb in self.usbs:
            try:
                usb.release(self.vm_id)
            except Exception as e:
                logging.warning("Release USB({}) for VM({}) failed, error is {}.".format(usb.usb_id, self.vm_id, e))

    def release_pass_through_usbs_silent(self):
        for usb in self.usbs:
            if usb.type == elf_constants.PASS_THROUGH_USB:
                try:
                    usb.release(self.vm_id)
                except Exception as e:
                    logging.warning(
                        "Release pass through USB({}) for VM({}) failed, error is {}.".format(usb.usb_id, self.vm_id, e)
                    )

    def release_detached_usbs(self):
        from smartx_app.elf.common.resource_wrappers import vm_wrapper

        vm = vm_wrapper.VMWrapper.get(self.vm_id)
        current_hostdevs = vm.get("hostdevs", [])
        current_usbs = [
            create_usb(usb["uuid"], usb["type"], usb["host_id"]) for usb in current_hostdevs if _is_usb_device(usb)
        ]

        expected_usb_ids = [usb.usb_id for usb in self.usbs]

        for current_usb in current_usbs:
            if current_usb.usb_id not in expected_usb_ids:
                try:
                    current_usb.release(self.vm_id)
                except Exception as e:
                    logging.warning(
                        "Release detached USB({}) for VM({}) failed, error is {}.".format(
                            current_usb.usb_id, self.vm_id, e
                        )
                    )


def _append_attached_hostdevs(vm_uuid, receive_usbs, domain_overnetwork_usbs):
    from smartx_app.elf.common.resource_wrappers import vm_wrapper as vm

    vm_info = vm.VMWrapper.get(vm_uuid)
    db_vm_update_time = vm_info.get("update_time", 0)
    db_vm_hostdevs = vm_info.get("hostdevs", [])
    db_vm_hostdev_uuids = [hostdev["uuid"] for hostdev in db_vm_hostdevs]

    overnetwork_usb_identities = [usb.identity for usb in domain_overnetwork_usbs]
    for usb in receive_usbs:
        # if this usb has already exists in db_vm["hostdevs"], do nothing.
        if usb.usb_id in db_vm_hostdev_uuids:
            continue

        if usb.identity in overnetwork_usb_identities:
            # if this usb not in db_vm["hostdevs"] but attached to the vm,
            # we should append this usb to db_vm["hostdevs"].
            db_vm_hostdevs.append({"uuid": usb.usb_id, "type": usb.type, "host_id": usb.host_id})
        else:
            # if this usb not in db_vm["hostdevs"] and has not been attached
            # to the vm, we need to release this usb.
            try:
                usb.release(vm_uuid)
            except Exception as e:
                logging.warning(
                    "Release USB({}) not attach to VM({}) failed, error is {}.".format(usb.usb_id, vm_uuid, e)
                )

    # Job has ensured that there will be no concurrency issues,
    # the update_time field is used for concurrency control to make the semantics clearer
    update_res = vm.VMWrapper.update_hostdevs(vm_uuid, db_vm_hostdevs, db_vm_update_time)
    if update_res:
        logging.info("Update VM({}) with hostdevs({}) successfully".format(vm_uuid, db_vm_hostdevs))
    else:
        logging.warning("Update VM({}) with hostdevs({}) failed".format(vm_uuid, db_vm_hostdevs))


def _try_append_attached_hostdevs(vm_domain, receive_usbs, vm_uuid):
    from smartx_app.elf.job_center.lib.converter import domain

    try:
        domain_overnetwork_usbs = domain.get_overnetwork_usbs_from_domain(vm_domain)
        _append_attached_hostdevs(vm_uuid, receive_usbs, domain_overnetwork_usbs)
    except Exception as e:
        logging.warning("Failed to append attached USBs to VM {}. Error is {}".format(vm_uuid, str(e)))


def release_usbs_on_start_failure(func):
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        try:
            return func(self, vm_json, *args, **kwargs)
        except Exception:
            USBHandler(vm_json).release_usbs_silent()
            raise

    return wrapper


def _allocate_usbs(usb_handler, vm_status, need_release_usbs=False):
    # We also need to allocate real usb devices when the VM is suspended.
    # Because during HA local recovery, the VM might be in the suspended state.
    # If we do not allocate the real usb devices, check_usb_allocaction_before_start will fail.
    allocate_real_usbs = vm_status in (elf_constants.VM_RUNNING, elf_constants.VM_SUSPENDED)
    usbs = []
    if allocate_real_usbs:
        # When HA is triggered, the VM on the original node does not
        # release the connection with the usbredir server, which causes
        # the newly created VM to fail to establish a connection with
        # the usbredir server normally, so the usb device cannot be seen
        # in the new VM.
        # Executing the release_usbs_silent func here is to ensure that
        # the usbredir server has not established a connection with any client.
        if need_release_usbs:
            usb_handler.release_usbs_silent()

        usbs = usb_handler.allocate_usbs()
    return allocate_real_usbs, usbs


def allocate_usbs_on_create(func):
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        usb_handler = USBHandler(vm_json)
        allocate_real_usbs = True

        try:
            allocate_real_usbs, kwargs["usbs"] = _allocate_usbs(usb_handler, vm_json["status"], need_release_usbs=True)
        except Exception as e:
            logging.warning("Allocate USBs when creating vm({}) failed, error is {}".format(vm_json["uuid"], e))

        try:
            return func(self, vm_json, *args, **kwargs)
        except Exception:
            if allocate_real_usbs:
                usb_handler.release_usbs_silent()
            raise

    return wrapper


def check_usb_allocaction_before_start(func):
    # After the VM triggers HA, if the usb attached to the VM is also on the faulty node,
    # the VM cannot be created on other nodes, because the creation need allocate the USB,
    # and in this case the allocation must fail.
    # In order to allow the VM to be successfully created, the creation will ignore the
    # error of USB allocation failure, but puts it in the start process to check whether
    # the USB is successfully allocated, if it is not allocated, it is not allowed to start,
    # and the USB can only be detached and start the VM again.
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        from smartx_app.elf.job_center.lib.converter import domain

        # Check if the usb is actually allocated.
        vm_uuid = vm_json["uuid"]
        over_network_usbs = [
            dev for dev in vm_json.get("hostdevs", []) if dev["type"] == elf_constants.OVER_NETWORK_USB
        ]

        if over_network_usbs:
            libvirt_conn = kwargs["libvirt_conn"]
            vm_domain = libvirt_conn.lookupByName(vm_uuid)
            vm_domain_xml = vm_domain.XMLDesc(0)
            libvirt_json = domain.domain_xml_to_libvirt_json(vm_domain_xml)
            if len(libvirt_json["domain"]["devices"].get("redirdev", [])) < len(over_network_usbs):
                raise base.ResourceException(
                    "Allocate USBs for VM({}) failed, VM will keep STOPPED status.".format(vm_json["uuid"]),
                    py_error.USB_ASSIGN_FAILED,
                )

        return func(self, vm_json, *args, **kwargs)

    return wrapper


def allocate_usbs_on_config(func):
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        import libvirt

        usb_handler = USBHandler(vm_json)
        vm_uuid = vm_json["uuid"]
        libvirt_conn = kwargs["libvirt_conn"]
        vm_domain = libvirt_conn.lookupByName(vm_uuid)
        vm_state = vm_domain.state()[0]
        allocate_real_usbs = True

        # If the VM and the USB are on the same node and the storage network down of this
        # node triggers HA, the VM will fail to be rebuilt on other nodes because the USB
        # cannot be allocated. Therefore, the VM is in the shutdown state, and the
        # scheduled task will trigger the startup operation later.
        #
        # In the starting operation in this scenario, if the USB release operation is not
        # performed in advance, the original usbredirserver will not be killed, causing
        # the newly started VM to connect to the original usbredirserver, and the USB
        # cannot be seen inside the VM, so the release operation also needs to be performed
        # before the VM is powered on.
        need_release_usbs = vm_state in (
            libvirt.VIR_DOMAIN_SHUTDOWN,
            libvirt.VIR_DOMAIN_SHUTOFF,
            libvirt.VIR_DOMAIN_CRASHED,
        )
        receive_usbs = []

        try:
            allocate_real_usbs, receive_usbs = _allocate_usbs(
                usb_handler, vm_json["status"], need_release_usbs=need_release_usbs
            )
            kwargs["usbs"] = receive_usbs
            result = func(self, vm_json, *args, **kwargs)
        except Exception as e:
            if allocate_real_usbs:
                if vm_state == libvirt.VIR_DOMAIN_RUNNING:
                    # When passing through multiple usb devices, there is a situation
                    # that some devices succeed, but other devices fail, we need to
                    # append these devices to the vm database.
                    _try_append_attached_hostdevs(vm_domain, receive_usbs, vm_uuid)
                else:
                    # Start vm depends on config vm, if config failed before starting vm,
                    # we need to release all usbs.
                    usb_handler.release_usbs_silent()

                if isinstance(e, libvirt.libvirtError):
                    if (
                        e.get_error_code() == libvirt.VIR_ERR_CONFIG_UNSUPPORTED
                        and "usb redirection is not supported" in e.get_error_message().lower()
                    ):
                        raise base.ResourceException(e.get_error_message(), py_error.USB_REDIRECT_NOT_SUPPORT)
            raise
        else:
            if vm_state == libvirt.VIR_DOMAIN_RUNNING:
                usb_handler.release_detached_usbs()
            return result

    return wrapper


class USBAssignInfoProxy:
    # ELF needs the proxy to reduce the number of requests to the TUNA API (usb assign info)
    # proxy table (host: usb assign infos)
    def __init__(self):
        self._tuna_client = tuna.Client()
        self._proxy_table = {}

    def fetch_usb_assign_info(self, host_id, usb_id):
        if host_id not in self._proxy_table:
            try:
                usb_assign_infos = self._tuna_client.get_usb_assign_infos(host_id)
            except Exception as e:
                logging.warning(
                    "[USB_assign_info_proxy] Get USB assign infos on node({}) failed, error={}".format(host_id, str(e))
                )
            else:
                self._proxy_table[host_id] = usb_assign_infos

        usb_assign_infos = self._proxy_table.get(host_id) or []
        for usb_assign_info in usb_assign_infos:
            if usb_assign_info["device_id"] == usb_id:
                return usb_assign_info
        return None
