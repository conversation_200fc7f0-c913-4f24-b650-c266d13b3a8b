# Copyright (c) 2013-2016, SMARTX
# All rights reserved.
from copy import deepcopy
import logging
import uuid

from common.config.resources import RESOURCE_IN_USE
from common.lib.time_utils import utc_now_timestamp
from common.mongo.db import mongodb
from job_center.common.constants import (
    JOB_PENDING,
    JOB_PROCESSING,
    JOB_TYPE_ACTION,
)
from job_center.common.exceptions import ZJobError
from job_center.config import CURRENT_NODE_IP, JOB_DB_NAME
from smartx_app.common.constants import CDROM, KVM_VOL_ISCSI
from smartx_app.common.node.db import query_host_by_data_ip
from smartx_app.elf.common import constants as elf_common_constants
from smartx_app.elf.common.code import API_VM_TEMPLATE_CREATE, API_VM_TEMPLATE_DELETE
from smartx_app.elf.common.constants import (
    DEFAULT_CPU_QOS_JSON,
    VM_CLOCK_OFFSET_UTC,
    VM_FIRMWARE_BIOS,
    VM_STOPPED,
    VM_VERSION_LATEST,
    VM_VERSION_MODE_AUTO,
    GuestOSType,
)
from smartx_app.elf.common.events.vm_template_events_wrapper import VMTemplateEventWrapper
from smartx_app.elf.common.resource_query.vm_template_querier import VMTemplateQuerier
from smartx_app.elf.common.resource_wrappers.vm_additional_info_manager import VMAdditionalInfoWrapper
from smartx_app.elf.common.resource_wrappers.vmtools_wrapper import VMToolsWrapper
from smartx_app.elf.common.resource_wrappers.volume_wrapper import VolumeWrapper
from smartx_app.elf.common.resources.attribute import Attribute
from smartx_app.elf.common.resources.base import ResourceException
from smartx_app.elf.common.resources.folder import Folder
from smartx_app.elf.common.resources.iscsi_volume_template import ISCSIVolumeTemplate
from smartx_app.elf.common.resources.vm_template import VMTemplate
from smartx_app.elf.common.resources.volume import Volume
from smartx_app.elf.common.resources.volume_template import VolumeTemplate
from smartx_app.elf.common.utils import cloud_init, resource
from smartx_app.elf.common.utils import disk as disk_util
from smartx_app.elf.common.utils import network as net_util
from smartx_app.elf.common.utils.disk import generate_disk_key
from smartx_app.elf.common.utils.libvirt_driver import libvirt_connection
from smartx_app.elf.job_center.constants import CLUSTER_DEFAULT_CPU_MODEL, DISK, KVM_VM, KVM_VM_TEMPLATE
from smartx_app.elf.job_center.lib.converter.domain import (
    define_domain,
    ensure_undefine_domain,
    update_nic_property_from_domain,
)
from smartx_proto.errors import pyerror_pb2 as py_error


class VMTemplateWrapper:
    resources_db = mongodb.resources

    def __init__(self, vm_template):
        self.vm_template = vm_template

    @classmethod
    def load(cls, vm_template_uuid):
        return cls(VMTemplate.load_template(vm_template_uuid))

    @staticmethod
    def fit_extra_fields(vm_template):
        if "cpu" not in vm_template:
            vm_template["cpu"] = {"topology": {"sockets": vm_template["vcpu"], "cores": 1}}
        if "nested_virtualization" not in vm_template:
            vm_template["nested_virtualization"] = False
        if "cpu_model" not in vm_template:
            vm_template["cpu_model"] = CLUSTER_DEFAULT_CPU_MODEL
        if "firmware" not in vm_template:
            vm_template["firmware"] = VM_FIRMWARE_BIOS
        if "cloud_init_supported" not in vm_template:
            vm_template["cloud_init_supported"] = False
        if "guest_os_type" not in vm_template:
            vm_template["guest_os_type"] = GuestOSType.UNKNOWN
        return vm_template

    @staticmethod
    def fit_volume_info(template):
        """fit disk.name for template disks"""
        if template:
            uuids = [x["volume_template_uuid"] for x in template["disks"] if x["type"] == "disk"]
            vols_mapping = {x["uuid"]: x for x in VolumeTemplate.batch_get_by_uuid(uuids)}
            for vol in template["disks"]:
                if vol["type"] == "disk" and vol["volume_template_uuid"] in vols_mapping:
                    vol_template_uuid = vol["volume_template_uuid"]
                    vol["name"] = vols_mapping[vol_template_uuid]["name"]
                    vol["size"] = vols_mapping[vol_template_uuid]["size"]

        return template

    @staticmethod
    def fit_nic_info(template):
        if template:
            resource.add_more_info_to_nic(template["nics"])
        return template

    @classmethod
    def query_from_db(cls, vm_template_uuid=None):
        if vm_template_uuid is not None:
            query = {"uuid": vm_template_uuid}
        else:
            query = None

        templates = [cls.fit_extra_fields(vm_template) for vm_template in cls._query_from_db(query)]
        VMToolsWrapper.apply_cdroms(*templates)
        return templates

    @classmethod
    def _query_from_db(cls, query):
        current_query = {"type": KVM_VM_TEMPLATE, "resource_state": RESOURCE_IN_USE}
        if query:
            current_query.update(query)
        return cls.resources_db.resource.find(current_query, {"_id": 0})

    @classmethod
    def new(
        cls,
        name,
        description,
        ha,
        vcpu,
        cpu,
        memory,
        nics,
        disks,
        nested_virtualization,
        template_uuid=None,
        cpu_model=None,
        firmware=VM_FIRMWARE_BIOS,
        quota_policy=None,
        clock_offset=VM_CLOCK_OFFSET_UTC,
        video_type=None,
        win_opt=False,
        cloud_init_supported=False,
        guest_os_type=None,
        sync_vm_time_on_resume=False,
        cpu_exclusive=None,
        local_ha_policy=elf_common_constants.LOCAL_HA_POLICY_DEFAULT,
    ):
        return cls(
            VMTemplate(
                name=name,
                description=description,
                ha=ha,
                vcpu=vcpu,
                cpu=cpu,
                memory=memory,
                nics=nics,
                disks=disks,
                nested_virtualization=nested_virtualization,
                template_uuid=template_uuid,
                cpu_model=cpu_model,
                firmware=firmware,
                quota_policy=quota_policy,
                clock_offset=clock_offset,
                video_type=video_type,
                win_opt=win_opt,
                cloud_init_supported=cloud_init_supported,
                guest_os_type=guest_os_type,
                sync_vm_time_on_resume=sync_vm_time_on_resume,
                cpu_exclusive=cpu_exclusive,
                local_ha_policy=local_ha_policy,
            )
        )

    @staticmethod
    def submit_job(description, resources, resource_group=None, job_id=None, event=None):
        from job_center.handler.leader.workers import job_submit

        return {
            "job_id": job_submit(
                user="",
                description=description,
                resources=resources,
                resource_group=resource_group,
                job_id=job_id,
                event=event,
            )
        }

    @classmethod
    def page_query(
        cls,
        count,
        skip_page,
        sort_criteria,
        filter_criteria,
        projection,
        start_uuid,
        end_uuid,
        skip_to_last,
        current_page=1,
    ):
        # Will set to the default value by the method:`fit_extra_fields` if
        # the query will not return these fields.
        if projection:
            projection += ",firmware,cpu_model,cpu,nested_virtualization"

        data = VMTemplateQuerier().page_query(
            count=count,
            skip_page=skip_page,
            sort_criteria=sort_criteria,
            filter_criteria=filter_criteria,
            projection=projection,
            start_uuid=start_uuid,
            end_uuid=end_uuid,
            skip_to_last=skip_to_last,
            current_page=current_page,
        )
        VMToolsWrapper.apply_cdroms(*data["entities"])
        list(map(cls.fit_extra_fields, data["entities"]))

        return data

    @classmethod
    def search(cls, text, limit=None, filter_criteria=None):
        vm_templates = VMTemplateQuerier().search(text, limit, filter_criteria)
        VMToolsWrapper.apply_cdroms(*vm_templates["entities"])
        return vm_templates

    @classmethod
    def get(cls, vm_template_uuid, fit_extra=True):
        vm_template = VMTemplateQuerier().get(vm_template_uuid)

        if vm_template and fit_extra:
            cls.fit_extra_fields(vm_template)
            cls.fit_volume_info(vm_template)
            cls.fit_nic_info(vm_template)

        return vm_template

    def _create_volumes_from_templates(self, disk_paths_del, update_disks, is_full_copy=False):
        volumes = {}
        for disk in self.vm_template.disks:
            if disk["type"] == "disk" and disk["path"] not in disk_paths_del:
                volume_template_uuid = disk["volume_template_uuid"]
                volume_template = VolumeTemplate.load(volume_template_uuid)
                volume = volume_template.create_volume()
                if is_full_copy and not volume.get("size_in_byte"):
                    volume["size_in_byte"] = volume_template.size

                update_disk = update_disks.get(volume_template_uuid)
                if update_disk:
                    if "name" in update_disk:
                        volume["name"] = update_disk["name"]
                    if volume["type"] == KVM_VOL_ISCSI and "storage_policy_uuid" in update_disk:
                        volume["storage_policy_uuid"] = update_disk["storage_policy_uuid"]
                    if "resident_in_cache" in update_disk:
                        volume["resident_in_cache"] = update_disk["resident_in_cache"]
                    if "encryption_algorithm" in update_disk:
                        volume["encryption_algorithm"] = update_disk["encryption_algorithm"]
                volumes[volume_template_uuid] = volume
        return volumes

    def _submit_create_vm(self, new_vm_attributes):
        is_full_copy = new_vm_attributes.get("is_full_copy", False)
        # handle vm template volume

        update_disks = {}
        disk_paths_del = []

        for d in new_vm_attributes.get("delete_disks", []):
            if d.get("path"):
                disk_paths_del.append(d["path"])

        for d in new_vm_attributes.get("modify_disks", []):
            if d["type"] == DISK:
                update_disks[d["volume_template_uuid"]] = d

        volumes = self._create_volumes_from_templates(disk_paths_del, update_disks, is_full_copy)

        dst_chunk_id = None
        if is_full_copy:
            if not new_vm_attributes.get("node_ip"):
                raise ZJobError(
                    "Execute full copy need to specify the target node", py_error.VM_FULL_COPY_WRONG_NODE_IP_CONFIG
                )
            dst_chunk_id = query_host_by_data_ip(new_vm_attributes.get("node_ip"))["chunk_id"]
        resources = {}

        # handle old volume
        volume_template_and_volume_map = {}
        for vol_template_uuid, volume in list(volumes.items()):
            if is_full_copy:
                volume["is_full_copy"] = True
                volume["dst_chunk_id"] = dst_chunk_id
            resources[volume["uuid"]] = volume
            volume_template_and_volume_map[vol_template_uuid] = volume

        # handle new vm template
        for disk in new_vm_attributes["new_disks"]:
            # create new volume
            if disk["type"] != CDROM and not disk.get("path"):
                # The view has been verified the parameters,
                # so we don't verify here.
                volume = VolumeWrapper.new(
                    name=disk["name"],
                    volume_type=KVM_VOL_ISCSI,
                    description=disk.get("description"),
                    size_in_byte=disk.get("size_in_byte"),
                    storage_policy_uuid=disk.get("storage_policy_uuid"),
                    src_export_id=disk.get("src_export_id"),
                    src_inode_id=disk.get("src_inode_id"),
                    src_target_name=disk.get("src_target_name"),
                    src_lun_id=disk.get("src_lun_id"),
                    new_size_in_byte=disk.get("new_size_in_byte"),
                    clone_before_create=disk.get("clone_before_create", False),
                    storage_cluster_uuid=disk.get("storage_cluster_uuid"),
                    resident_in_cache=disk.get("resident_in_cache", False),
                    encryption_algorithm=disk.get("encryption_algorithm"),
                ).volume
                disk["path"] = "@{%s/path}" % volume.uuid
                disk["volume_uuid"] = volume.uuid
                disk["storage_policy_uuid"] = volume.storage_policy_uuid
                disk["serial"] = volume.serial
                resources[volume.uuid] = volume.dumps()

        vm = self.vm_template.create_vm(
            new_vm_attributes, volume_template_and_volume_map, svt_images=VMToolsWrapper.get_images()
        )
        folder_uuid = new_vm_attributes.get("folder_uuid")
        if folder_uuid:
            folder = Folder.load(folder_uuid).info()
            vm["folder"] = folder

        # prepare cloud-init config
        vm.pop("cloud_init_supported", None)
        if new_vm_attributes.get("cloud_init"):
            vm["cloud_init"] = new_vm_attributes["cloud_init"]
            cloud_init.prepare_data_for_vm(vm)

        disk_util.ensure_cdrom_bus(vm["disks"], "submit_create_vm", res_uuid=vm["uuid"])

        resources[vm["uuid"]] = vm
        return resources

    def submit_create_vms_preview(self, new_vm_attributes, vm_names):
        resources = {}
        resource_group = {}

        valid_disks = []
        # CDROM deletion must submit key, guaranteed by schema
        delete_keys = (disk["key"] for disk in new_vm_attributes.get("delete_disks", []) if disk["type"] == CDROM)
        exist_keys = [
            disk["key"] for disk in self.vm_template.disks if disk["type"] == CDROM and disk["key"] not in delete_keys
        ]
        for disk in new_vm_attributes.get("new_disks", new_vm_attributes.get("disks", [])):
            # fill disk storage_policy_uuid and volume_uuid from existing volume
            if disk["type"] != CDROM:
                if disk.get("path"):
                    volumes = Volume.load_by_paths([disk["path"]])
                    # if it not exist or it's not a sharing vol, skip
                    if not (volumes and getattr(volumes[0], "sharing")):
                        logging.error("the given path(%s) doesn't exist,or it's not a sharing vol" % (disk["path"]))
                        continue
                    disk["storage_policy_uuid"] = volumes[0].storage_policy_uuid
                    disk["volume_uuid"] = volumes[0].uuid
                    disk["resident_in_cache"] = volumes[0].resident_in_cache
                    disk["serial"] = volumes[0].serial
            else:
                key = generate_disk_key(disk["type"], exist_keys)
                if not key:
                    raise ResourceException("CDRom number has exceeded the limit.", py_error.VM_CDROM_MAXIMUN)
                disk["key"] = key
                exist_keys.append(key)
            valid_disks.append(disk)
        new_vm_attributes["new_disks"] = valid_disks
        nic_holder = create_nic_holder(
            # must pop `nics`
            new_vm_attributes.pop("nics", None),
            len(vm_names),
        )

        cloud_init_holder = new_vm_attributes.pop("cloud_init", [])
        for vm_name in vm_names:
            new_vm_attributes_copy = deepcopy(new_vm_attributes)
            new_vm_attributes_copy["vm_name"] = vm_name

            # Ignore the redundant `cloud_init` configs if its length
            # is greater than the `vm_names`.
            if cloud_init_holder:
                new_vm_attributes_copy["cloud_init"] = cloud_init_holder.pop(0)

            if nic_holder is not None:
                new_vm_attributes_copy["nics"] = nic_holder.pop(0)

            vm_resources = self._submit_create_vm(new_vm_attributes_copy)
            resources.update(vm_resources)
            resource_group.update({str(uuid.uuid4()): list(vm_resources.keys())})

        return resources, resource_group

    def generate_resources_for_creation(self, vm_uuid=None):
        resources = {}
        exist_keys = [d.get("key", -1) for d in self.vm_template.disks if d["type"] == CDROM]
        for disk in self.vm_template.disks:
            if disk["type"] == "disk":
                if "path" in disk:
                    volume_template = VolumeTemplate.load_from_volume_path(disk["path"])
                else:
                    # The view has been verified the parameters,
                    # so we don't verify here.
                    volume_template = ISCSIVolumeTemplate(
                        name=disk.get("name", str(uuid.uuid4())),
                        description="convert from the other LUN or NFS file",
                        volume_uuid=None,
                        storage_policy_uuid=disk["storage_policy_uuid"],
                        src_export_id=disk.get("src_export_id"),
                        src_inode_id=disk.get("src_inode_id"),
                        src_target_name=disk.get("src_target_name"),
                        src_lun_id=disk.get("src_lun_id"),
                        new_size_in_byte=disk.get("new_size_in_byte"),
                        clone_before_create=disk.get("clone_before_create", False),
                        storage_cluster_uuid=disk.get("storage_cluster_uuid"),
                        alloc_even=True,
                        resident_in_cache=disk.get("resident_in_cache", False),
                        encryption_algorithm=elf_common_constants.TEMPORARY_VOLUME_ENCRYPTION_ALG_STORAGE_OBJECT,
                    )
                    disk["path"] = "@{%s/path}" % volume_template.uuid

                disk["volume_template_uuid"] = volume_template.uuid
                disk["size"] = "@{%s/size}" % volume_template.uuid
                disk["diff_size"] = "@{%s/diff_size}" % volume_template.uuid

                # resident_in_cache passed by user will be ignored
                disk["resident_in_cache"] = volume_template.resident_in_cache

                # nfs only
                disk["export_name"] = getattr(volume_template, "export_name", None)
                # No need to keep volume_uuid in the template
                disk.pop("volume_uuid", None)
                resources[volume_template.uuid] = volume_template.dumps()
            else:
                if not disk.get("key"):
                    key = generate_disk_key("cdrom", exist_keys)
                    if not key:
                        raise ResourceException("CDRom number has exceeded the limit.", py_error.VM_CDROM_MAXIMUN)
                    disk["key"] = key
                    exist_keys.append(key)

        vm_template = self.vm_template.dumps()
        vm_template["source_resource_uuid"] = vm_uuid

        # VM templates need to add nics.vlans from the DB
        net_util.ensure_vlan_for_nics(vm_template["nics"])

        # VM templates only keep cpu_exclusive.expected_enabled
        vm_template["cpu_exclusive"].pop("actual_enabled", None)
        vm_template["cpu_exclusive"].pop("vcpu_to_pcpu", None)

        # Do not keep the mac_address, interface_id, pci_address and queues in the template
        resource.pop_nic_attr(
            vm_template,
            (
                resource.VM_NIC_PCI_ADDRESS,
                resource.VM_NIC_INTERFACE_ID,
                resource.VM_NIC_MAC_ADDRESS,
                resource.VM_NIC_QUEUES,
            ),
        )
        resources[self.vm_template.uuid] = vm_template

        return resources

    def submit_create(self, user=None, vm_uuid=None):
        event = VMTemplateEventWrapper(user=user).event_vm_template_create(
            vm_template_obj=self.vm_template, vm_uuid=vm_uuid
        )
        resources = self.generate_resources_for_creation(vm_uuid)
        return self.submit_job(API_VM_TEMPLATE_CREATE, resources, event=event)

    def submit_delete(self, delete_volumes_permanently=True):
        resources = {}
        for disk in self.vm_template.disks:
            if disk["type"] == "disk":
                vol_template_json = VolumeTemplate.load(disk["volume_template_uuid"]).delete(
                    delete_permanently=delete_volumes_permanently
                )
                resources.setdefault(disk["volume_template_uuid"], vol_template_json)
        resources[self.vm_template.uuid] = self.vm_template.delete()
        return self.submit_job(API_VM_TEMPLATE_DELETE, resources)

    def update(self, name, description, cloud_init_supported):
        return self.resources_db.resource.update_one(
            {"uuid": self.vm_template.uuid},
            {"$set": {"name": name, "description": description, "cloud_init_supported": cloud_init_supported}},
        )

    def as_vm(self, vm_name):
        collection = self.resources_db.resource
        bulk = collection.initialize_unordered_bulk_op()

        count = 0
        vol_map = {}
        for i, d in enumerate(self.vm_template.disks):
            if d["type"] == "disk":
                vol_template_uuid = d["volume_template_uuid"]
                vol = VolumeWrapper.convert_to_volume(vol_template_uuid)
                vol_map[vol_template_uuid] = vol
                bulk.find({"uuid": vol["uuid"]}).replace_one(vol)
                count += 1
            else:
                image = VMToolsWrapper.match_image(d["path"])
                if not image:
                    d["path"] = ""

        vm_json = self.vm_template.dumps()
        vm_json.pop("cloud_init_supported", None)
        vm_json["type"] = KVM_VM
        vm_json["auto_schedule"] = False
        vm_json["boot_with_host"] = False

        for disk in vm_json["disks"]:
            if disk["type"] == "disk":
                for key in ["diff_size", "export_name", "size"]:
                    disk.pop(key)
                vol = vol_map[disk.pop("volume_template_uuid")]
                disk["path"] = vol["path"]
                disk["volume_uuid"] = vol["uuid"]
                # No need to keep resident_in_cache in the VM
                disk.pop("resident_in_cache", None)

        disk_util.ensure_cdrom_bus(vm_json["disks"], "as_vm", res_uuid=vm_json["uuid"])

        # guest_cpu_model, cluster_cpu_model, update_time, last_start_time will set when vm start
        # pci_address in nic will set when vm config
        for key in [
            "update_time",
            "last_start_time",
            "last_resume_time",
            "last_shutdown_time",
            "cluster_cpu_model",
            "guest_cpu_model",
        ]:
            vm_json[key] = None

        vm_json["vm_name"] = vm_name
        vm_json["create_time"] = utc_now_timestamp()
        vm_json["node_ip"] = CURRENT_NODE_IP
        vm_json["status"] = VM_STOPPED
        vm_json["hostdevs"] = []
        vm_json["cpu_exclusive"] = self.vm_template.cpu_exclusive
        vm_json["cpu_qos"] = DEFAULT_CPU_QOS_JSON
        vm_json["vm_version"] = VM_VERSION_LATEST
        vm_json["vm_version_mode"] = VM_VERSION_MODE_AUTO
        vm_json["migratable"] = True

        for x in vm_json["nics"]:
            x.pop("ip_address", None)

        net_util.ensure_nic(vm_json["nics"])

        with libvirt_connection() as libvirt_conn:
            # When converting to each other, only one problem is dealt with
            ensure_undefine_domain(libvirt_conn, vm_json["uuid"])
            define_domain(libvirt_conn, vm_json, None)
            # set `queues` to virtio NIC
            # set 'pci address' to all NICs. If a vm created and never edit,
            # when trigger HA, the vm nics pci address may change.
            # So, we need to save pci address when vm xml defined.
            update_nic_property_from_domain(libvirt_conn, vm_json)
            VMAdditionalInfoWrapper.add_domain_uuid(vm_json["uuid"], libvirt_conn)
        VMAdditionalInfoWrapper.add(vm_json["uuid"], "has_customized_smbios", True)

        collection.replace_one({"uuid": vm_json["uuid"]}, vm_json)

        if count:
            bulk.execute()

        Attribute.batch_change_resource_type(KVM_VM_TEMPLATE, vm_json["uuid"], KVM_VM)

    @classmethod
    def check_name_unique(cls, template_name):
        query = {"name": template_name, "type": KVM_VM_TEMPLATE, "resource_state": RESOURCE_IN_USE}
        vm_template = cls.resources_db.resource.find_one(query)
        if not vm_template:
            job = mongodb[JOB_DB_NAME].job.find(
                {
                    "description": {"$in": [API_VM_TEMPLATE_CREATE, API_VM_TEMPLATE_DELETE]},
                    "type": JOB_TYPE_ACTION,
                    "state": {"$in": [JOB_PROCESSING, JOB_PENDING]},
                },
                {"_id": 0, "resources": 1},
            )
            for j in job:
                for r in list(j["resources"].values()):
                    if r.get("type") == KVM_VM_TEMPLATE:
                        name = r.get("name")
                        if name and template_name == name:
                            raise ResourceException(
                                "A VM template with the same name({}) is creating or deleting.".format(template_name),
                                py_error.VM_TEMPLATE_DUPLICATED_NAME,
                            )
        else:
            raise ResourceException(
                "A VM template with the same name({}) already exists.".format(template_name),
                py_error.VM_TEMPLATE_DUPLICATED_NAME,
            )


def create_nic_holder(original_nics, vm_count):
    if original_nics is None:
        return None

    holder = [[] for _ in range(vm_count)]
    for nic in original_nics:
        ips = nic.get("ip_addresses", None)
        if ips:
            ips = deepcopy(ips)

        for x in holder:
            new_nic = deepcopy(nic)
            new_nic.pop("ip_addresses", None)

            if ips:
                new_nic["ip_address"] = ips.pop(0)

            x.append(new_nic)

    return holder
