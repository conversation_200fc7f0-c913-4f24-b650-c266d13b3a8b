# Copyright (c) 2022 SMARTX, Inc.
# All rights reserved.
import copy
import functools
import itertools
import logging
import uuid

from common.http import exceptions
from smartx_app.common.node import db
from smartx_app.elf.common import constants as elf_constants
from smartx_app.elf.common.resource_wrappers import pci_device
from smartx_app.elf.common.resources import base
from smartx_app.elf.common.utils import vm_start
from smartx_proto.errors import pyerror_pb2 as py_error


class VGPU(pci_device.PCIDevice):
    def __init__(self, vgpu_uuid, guest_pci_address, gpu_uuid, vgpu_type_id, related_vgpu_json):
        self._gpu_uuid = gpu_uuid
        self._mdev_uuid = None
        self._vgpu_type_id = vgpu_type_id
        self._related_vgpu_json = related_vgpu_json
        super().__init__(vgpu_uuid, guest_pci_address)

    @property
    def related_vgpu_json(self):
        # Should return origin reference of the vm_json, not a copy of it.
        return self._related_vgpu_json

    @property
    def gpu_uuid(self):
        return self._gpu_uuid

    @property
    def vgpu_type_id(self):
        return self._vgpu_type_id

    def set_mdev_uuid(self, mdev_uuid):
        # mdev_uuid is short for mediated device uuid.
        # For devices like vGPU, we use a uuid instead of PCI address
        # to refer to a device on the host.
        self._mdev_uuid = mdev_uuid

    @property
    def alias_name(self):
        return "ua-{}".format(self.uuid)

    @property
    def domain_json(self):
        # vGPU device xml format:
        # <hostdev mode='subsystem' type='mdev' model='vfio-pci'>
        #   <source>
        #     <address uuid='527b0bcd-6a68-4667-a074-6cdb9db76ae0'/>
        #   </source>
        # </hostdev>
        dev_domain_json = {
            "@mode": "subsystem",
            "@type": "mdev",
            "@model": "vfio-pci",
            "source": {"address": {"@uuid": self._mdev_uuid}},
            "alias": {"@name": self.alias_name},
        }

        if self.guest_pci_address:
            dev_domain_json["address"] = self.guest_pci_address
        return dev_domain_json


class VGPUHandler(pci_device.PCIDeviceHandler):
    _subtype = elf_constants.VGPU
    _dom_host_type = "mdev"

    def __init__(self, vm_json):
        super().__init__(vm_json)
        self._vgpus = self._devices

    def _initialize_devices(self):
        vgpu_json = sorted(
            (d for d in self._vm_json.get("hostdevs", []) if d["type"] == self._subtype), key=lambda x: x["gpu_uuid"]
        )

        vgpus = []
        # Index of vGPU from same GPU should begin with 0, so that unmount of certain vGPU won't
        # affect alias and guest PCI address of other vGPUs.
        index = -1
        for i in range(0, len(vgpu_json)):
            if i != 0 and vgpu_json[i]["gpu_uuid"] != vgpu_json[i - 1]["gpu_uuid"]:
                index = 0
            else:
                index += 1

            vgpu_uuid = self._gen_uuid(vgpu_json[i]["gpu_uuid"], index)
            vgpu = VGPU(
                vgpu_uuid,
                vgpu_json[i].get("guest_pci_address"),
                vgpu_json[i]["gpu_uuid"],
                vgpu_json[i]["vgpu_type_id"],
                vgpu_json[i],
            )
            vgpus.append(vgpu)

        return vgpus

    @property
    def _matched_host_devs(self):
        return {vgpu.uuid: vgpu.related_vgpu_json for vgpu in self._vgpus}

    def _gen_assign_id(self, vgpu_uuid):
        return "{}-{}".format(self.vm_uuid, vgpu_uuid)

    def gen_all_assign_ids(self):
        return [self._gen_assign_id(vgpu.uuid) for vgpu in self._vgpus]

    @staticmethod
    def get_vm_uuid_from_assign_id(assign_id):
        return assign_id[0:36]

    @staticmethod
    def get_gpu_uuid_from_assign_id(assign_id):
        return assign_id[37:73]

    def _release_vgpus(self, host_uuid, gpu_uuid, vgpu_type_id, assign_ids):
        self._tuna_client.release_vgpu(host_uuid, gpu_uuid, vgpu_type_id, assign_ids)

    def _get_device_assign_infos(self, host_uuid):
        vgpu_assign_infos = []
        gpu_assign_infos = self._tuna_client.get_gpu_assign_infos(host_uuid)
        for x in gpu_assign_infos:
            if x["assign_type"] != elf_constants.GPU_USAGE_VGPU:
                continue

            for assigned_vgpu in x["assigned_vgpus"]:
                if assigned_vgpu["assign_id"] == "":
                    continue

                assigned_vm_uuid = self.get_vm_uuid_from_assign_id(assigned_vgpu["assign_id"])
                if assigned_vm_uuid == self.vm_uuid:
                    vgpu_assign_infos.append(
                        {
                            "host_uuid": host_uuid,
                            "gpu_uuid": x["device_id"],
                            "assign_id": assigned_vgpu["assign_id"],
                            "vgpu_type_id": x["assign_vgpu_type"]["vgpu_type_id"],
                        }
                    )

        return vgpu_assign_infos

    def release_vgpus_silent(self):
        gpu_uuid_to_assign_ids = {}
        for vgpu in self._vgpus:
            gpu_uuid_to_assign_ids.setdefault(vgpu.gpu_uuid, []).append(self._gen_assign_id(vgpu.uuid))

        for gpu_uuid, assign_ids in list(gpu_uuid_to_assign_ids.items()):
            try:
                self._tuna_client.release_vgpu(self.host_uuid, gpu_uuid, self._vgpus[0].vgpu_type_id, assign_ids)
            except Exception as e:
                logging.warning("Release VGPU silent failed, error is ({})".format(str(e)))

    def vm_has_vgpu(self):
        return len(self._vgpus) > 0

    @staticmethod
    def _gen_uuid(gpu_uuid, index):
        return "{}-vgpu{}".format(gpu_uuid, index)

    def release_unexpected_vgpus_by_tuna_record(self):
        self.release_unexpected_devices_by_tuna_record()

    def release_unexpected_devices_by_tuna_record(self):
        # Release vGPU in case some vGPU release failed in last shutdown.
        # Hostdevs may be unmounted after shutdown, so assign relation should be
        # get from TUNA.
        # The VM may be migrated after detaching all hostdevs. So assign info among
        # all hosts should be gathered.
        host_uuids = [host["host_uuid"] for host in db.query_hosts()]
        vgpu_assign_infos = []
        for host_uuid in host_uuids:
            vgpu_assign_infos.extend(self._get_device_assign_infos(host_uuid))

        current_vgpu_assign_ids = []
        if self._vm_json["status"] in (
            elf_constants.VM_RUNNING,
            elf_constants.VM_SUSPENDED,
        ):
            current_vgpu_assign_ids = [self._gen_assign_id(v.uuid) for v in self._vgpus]

        unexpected_assign_infos = [x for x in vgpu_assign_infos if x["assign_id"] not in current_vgpu_assign_ids]

        if len(unexpected_assign_infos) == 0:
            return

        logging.info("VM({}) has unexpected assign_infos({})".format(self.vm_uuid, unexpected_assign_infos))

        gpu_uuid_to_assign_infos = {}
        for x in unexpected_assign_infos:
            gpu_uuid_to_assign_infos.setdefault(x["gpu_uuid"], []).append(x)

        for gpu_uuid, assign_infos in list(gpu_uuid_to_assign_infos.items()):
            host_uuid = assign_infos[0]["host_uuid"]

            # Unexpected_assign_infos may contain different vgpu_type_id. So assign_infos should
            # be grouped by vgpu_type_id first then released.
            for vgpu_type_id, grouped in itertools.groupby(
                assign_infos, key=lambda assign_info: assign_info["vgpu_type_id"]
            ):
                assign_ids = [x["assign_id"] for x in list(grouped)]
                try:
                    self._release_vgpus(host_uuid, gpu_uuid, vgpu_type_id, assign_ids)
                except exceptions.RestfulClientError as e:
                    if e.user_code not in (
                        py_error.DEVICE_NOT_FOUND,  # GPU of vGPU has been unmounted on host
                        py_error.DEVICE_NOT_ALLOCATED,  # GPU of vGPU released in last shutdown
                        py_error.DEVICE_USAGE_NOT_MATCH,  # GPU usage has been set to 'pass-through'
                    ):
                        raise base.ResourceException(
                            "Release vGPUs (host_uuid: {}, gpu_uuid: {}, assign_ids: {}) failed, "
                            "error is ({})".format(host_uuid, gpu_uuid, assign_ids, str(e)),
                            py_error.VGPU_RELEASE_FAILED,
                        )
                except Exception as e:
                    raise base.ResourceException(
                        "Release vGPUs (host_uuid: {}, gpu_uuid: {}, assign_ids: {}) failed, error is ({})".format(
                            host_uuid, gpu_uuid, assign_ids, str(e)
                        ),
                        py_error.VGPU_RELEASE_FAILED,
                    )

    def allocate_vgpu(self):
        gpu_uuid_to_assign_ids = {}
        for vgpu in self._vgpus:
            gpu_uuid_to_assign_ids.setdefault(vgpu.gpu_uuid, []).append(self._gen_assign_id(vgpu.uuid))

        assign_result = {}  # A dict, key is assign_id, value is mdev_uuid
        for gpu_uuid, assign_ids in list(gpu_uuid_to_assign_ids.items()):
            try:
                assign_result.update(
                    self._tuna_client.assign_vgpu(self.host_uuid, gpu_uuid, self._vgpus[0].vgpu_type_id, assign_ids)
                )
            except Exception as e:
                raise base.ResourceException(
                    "Allocate vGPU on GPU({}) of host({}) with vgpu_type_id={}, assign_ids={} failed, "
                    "error={}".format(gpu_uuid, self.host_uuid, self._vgpus[0].vgpu_type_id, assign_ids, str(e)),
                    py_error.VGPU_ASSIGN_FAILED,
                )

        for vgpu in self._vgpus:
            vgpu.set_mdev_uuid(assign_result[self._gen_assign_id(vgpu.uuid)])

        return copy.deepcopy(self._vgpus)

    def allocate_mock_vgpu(self):
        for vgpu in self._vgpus:
            vgpu.set_mdev_uuid(str(uuid.uuid4()))

        return copy.deepcopy(self._vgpus)


def release_vgpus_on_start_failure(func):
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        try:
            return func(self, vm_json, *args, **kwargs)
        except Exception:
            VGPUHandler(vm_json).release_vgpus_silent()
            raise

    return wrapper


def allocate_vgpus_to_start(func):
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        vgpu_handler = VGPUHandler(vm_json)
        if vgpu_handler.vm_has_vgpu():
            if vm_json["status"] == elf_constants.VM_RUNNING:
                kwargs["vgpus"] = vgpu_handler.allocate_vgpu()
            else:
                kwargs["vgpus"] = vgpu_handler.allocate_mock_vgpu()

        try:
            return func(self, vm_json, *args, **kwargs)
        except Exception:
            if vm_start.need_release_device_on_exception_after_start(vm_json, kwargs["libvirt_conn"]):
                vgpu_handler.release_vgpus_silent()
            raise

    return wrapper
