# Copyright (c) 2013-2023, SMARTX
# All rights reserved.
import copy
import functools
import logging

from common.http import network
from smartx_app.elf.common import constants as elf_common_constants
from smartx_app.elf.common.resources import base
from smartx_app.elf.common.utils import cluster
from smartx_app.elf.job_center.lib.converter import domain
from smartx_proto.errors import pyerror_pb2

VPC_RESOURCE_OPERATION_ERROR_REASON_ALREADY_EXISTS = "AlreadyExists"
VPC_RESOURCE_OPERATION_SUCCESS_CODE = (200, 201)


def check_operation_results(results, allowed_error_reasons, resource_count, raise_exception=False):
    """Inside VPC service, each resource is handled separately. So the result of each resource might be different.
    Part of the operations might succeed while others might fail. This function checks the code of each resource
    result to find out whether the whole operation is successful. If not, return all failed operations"""
    failed_operations = []
    failed_resources_and_reasons = {}

    # Because not all successful operations result contain a resource uuid, and the field names of resource uuid are
    # different among different responses, we cannot use a common function to compare resources and operation results by
    # resource uuid. Comparing the count of resources and results is enough.
    if resource_count != len(results):
        logging.warning(
            "The operation result count doesn't match resource count. Resource count={}, operations results count={}, "
            "results details={}".format(resource_count, len(results), results)
        )

        if raise_exception:
            raise Exception("The operation result count doesn't match resource count")

        return False

    for r in results:
        if r.get("code") in VPC_RESOURCE_OPERATION_SUCCESS_CODE:
            continue

        if r.get("reason") in allowed_error_reasons:
            logging.info(
                "Ignore VPC resource operation error because of reason {}, error details are {}".format(
                    r["reason"], r.get("error")
                )
            )
            continue

        failed_operations.append(r)
        # A failed operation result format:
        # {
        #     "code": 409,
        #     "error": {
        #         "ErrStatus": {
        #             "metadata": {},
        #             "status": "Failure",
        #             "message": "vnics.vpc.everoute.io \"gmy-test-vnic-uuid-1\" already exists",
        #             "reason": "AlreadyExists",
        #             "details": {
        #                 "name": "gmy-test-vnic-uuid-1",
        #                 "group": "vpc.everoute.io",
        #                 "kind": "vnics"
        #             },
        #             "code": 409
        #         }
        #     },
        #     "reason": "AlreadyExists",
        #     "message": "xxxxxxxxx"
        # }
        failed_resource_uuid = r.get("error", {}).get("ErrStatus", {}).get("details", {}).get("name", "")
        failed_resources_and_reasons[failed_resource_uuid] = r.get("reason")

    if failed_operations:
        error_msg = "Failed resource and corresponding reasons are {}".format(failed_resources_and_reasons)
        logging.warning(error_msg)
        logging.warning("Failure details: {}".format(failed_operations))

        if raise_exception:
            raise Exception(error_msg)

        return False

    return True


class VPCNICHandler:
    # Additional fields for VPC NIC which will not be stored in DB
    NIC_ADDITIONAL_FIELDS = (
        "vpc_uuid",
        "subnet_uuid",
        "static_ip_address",
        "floating_ip_uuid",
        "use_floating_ip",
        "skip_ip_validation",
        "vnic_group_snapshot_uuid",
        "vnic_snapshot_uuid",
    )

    def __init__(self, vm_json):
        self._vm_json = vm_json
        self._vpc_nics = [
            nic for nic in vm_json.get("nics", []) if nic.get("type") == elf_common_constants.NIC_TYPE_VPC
        ]
        self._vpc_nics_interface_ids = [n["interface_id"] for n in self._vpc_nics]
        self._vm_uuid = vm_json["uuid"]
        self._vnics_to_be_rollback = [
            nic for nic in self._vpc_nics if "vnic_group_snapshot_uuid" in nic and "vnic_snapshot_uuid" in nic
        ]

        # A VM cannot be in rollback and have nics to be created at the same time. Only if a VM is not in rollback,
        # it could have nics_to_be_created.
        self._nics_to_be_created = []
        if not self._vnics_to_be_rollback:
            self._nics_to_be_created = [nic for nic in self._vpc_nics if "vpc_uuid" in nic and "subnet_uuid" in nic]

        self._network_client = None

    @property
    def network_client(self):
        if self._network_client is None:
            self._network_client = network.Client()

        return self._network_client

    def _create_vpc_nics_on_demand(self):
        if not self._nics_to_be_created:
            return

        try:
            create_result = self.network_client.create_vpc_nics(
                cluster.get_cluster_id(),
                self._vm_uuid,
                [
                    {
                        "vpc_uuid": nic["vpc_uuid"],
                        "subnet_uuid": nic["subnet_uuid"],
                        "mac_addr": nic["mac_address"],
                        "ipv4_addr": nic.get("static_ip_address", ""),
                        "floating_ip_uuid": nic.get("floating_ip_uuid", ""),
                        "vnic_uuid": nic["interface_id"],
                        "vlan_uuid": nic["vlan_uuid"],
                        "ovsbr_name": nic["ovs"],
                        "skip_ip_validation": nic.get("skip_ip_validation", False),
                    }
                    for nic in self._nics_to_be_created
                ],
            )
            check_operation_results(
                create_result,
                [VPC_RESOURCE_OPERATION_ERROR_REASON_ALREADY_EXISTS],
                len(self._nics_to_be_created),
                raise_exception=True,
            )
        except Exception as e:
            raise base.ResourceException(
                "Create VPC NIC for VM({}) failed, parameter is {}, error is {}".format(
                    self._vm_uuid, self._nics_to_be_created, str(e)
                ),
                pyerror_pb2.JOB_CREATE_VPC_NIC_FAILED,
            )

    def delete_vpc_nics_on_create_fail_silent(self):
        self._delete_vpc_nics_silent(self._nics_to_be_created, "VM Creation")

    def _delete_vpc_nics_silent(self, vpc_nics, scene: str):
        if vpc_nics:
            interface_ids = [nic["interface_id"] for nic in vpc_nics]
            self._delete_vpc_nics_silent_by_interface_ids(interface_ids, scene)

    def _delete_vpc_nics_silent_by_interface_ids(self, interface_ids, scene: str):
        if not interface_ids:
            return

        try:
            operation_results = self.network_client.delete_vpc_nics(self._vm_uuid, interface_ids)
            check_operation_results(operation_results, [], len(interface_ids), raise_exception=True)
        except Exception as e:
            logging.warning(
                "Delete VPC NICs silent for VM({}) under '{}' failed, interface_ids=({}), error is {}".format(
                    self._vm_uuid, scene, interface_ids, str(e)
                )
            )

    def _rollback_vpc_nics(self):
        # Group VNIC by vnic_group_snapshot_uuid
        d = {}
        for n in self._vnics_to_be_rollback:
            d.setdefault(n["vnic_group_snapshot_uuid"], []).append(n["vnic_snapshot_uuid"])

        for vnic_group_snapshot_uuid, vnic_snapshot_uuids in d.items():
            try:
                operation_result = self.network_client.rollback_vpc_nics(vnic_group_snapshot_uuid, vnic_snapshot_uuids)
                check_operation_results(operation_result, [], len(vnic_snapshot_uuids), raise_exception=True)
            except Exception as e:
                raise base.ResourceException(
                    "Rollback vnic_snapshots({}) for VM({}) under vm_snapshot({}) failed, error={}".format(
                        vnic_snapshot_uuids, self._vm_uuid, vnic_group_snapshot_uuid, str(e)
                    ),
                    pyerror_pb2.JOB_ROLLBACK_NIC_SNAPSHOTS_FAILED,
                )

    def _get_nic_interface_ids_from_libvirt_domain(self, libvirt_conn):
        vm_domain = libvirt_conn.lookupByName(self._vm_uuid)
        libvirt_xml = vm_domain.XMLDesc(0)
        libvirt_json = domain.domain_xml_to_libvirt_json(libvirt_xml)
        interfaces = libvirt_json["domain"]["devices"].get("interface", [])

        return {n["virtualport"]["parameters"]["@interfaceid"] for n in interfaces}

    def prepare_vpc_nics(self):
        if self._vnics_to_be_rollback:
            self._rollback_vpc_nics()
        else:
            self._create_vpc_nics_on_demand()

    def sync_vpc_nics_from_vm_domain_silent(self, libvirt_conn):
        """
        Handle VPC NICs after kvm_vm_config failed. This function is used to handle failure during remove VPC NICs
        or delete VPC NICs in VM rollback.
        If kvm_vm_config failed, the VM DB record won't be updated by handle_follower_base. Some nics might be
        successfully attached/detached while others may not. So the vm_json may not be same as vm domain. Some VPC
        NIC could exist in vm domain but not in vm_json. If we do not consider them, the corresponding record in VPC
        service will be cleaned by cron job.
        If NIC changed after kvm_vm_config failed, VM DB data should be updated.
        :param libvirt_conn: Libvirt connection, used to get vm domain
        """

        from smartx_app.elf.common.resource_wrappers import vm_wrapper

        try:
            if not self.network_client.is_cluster_associated_to_vpc_service():
                return

            interface_ids_in_domain = self._get_nic_interface_ids_from_libvirt_domain(libvirt_conn)
            vpc_interface_ids_in_vpc_service = self.network_client.get_vpc_nics(self._vm_uuid).keys()
            vpc_interface_ids_to_be_deleted = [
                n for n in vpc_interface_ids_in_vpc_service if n not in interface_ids_in_domain
            ]
            if vpc_interface_ids_to_be_deleted:
                logging.info(
                    "[sync_vpc_nics_from_domain]Extra VPC NICs is allocated: {}, should be released".format(
                        vpc_interface_ids_to_be_deleted
                    )
                )
                self._delete_vpc_nics_silent_by_interface_ids(
                    vpc_interface_ids_to_be_deleted, "NIC removal or VM Rollback fail"
                )

            current_vm_in_db = vm_wrapper.VMWrapper.load(self._vm_uuid).vm_doc
            interface_ids_in_db = {n["interface_id"] for n in current_vm_in_db["nics"]}
            if interface_ids_in_db != interface_ids_in_domain:
                logging.info(
                    "[sync_vpc_nics_from_domain]Domain NICs is different from DB,interface_ids_in_db={}, "
                    "interface_ids_in_domain={}".format(interface_ids_in_db, interface_ids_in_domain)
                )
                # Every NIC in VM DB record and expected_vm_json may exist in VM domain.
                all_possible_nics = {n["interface_id"]: n for n in self._vm_json["nics"]}
                all_possible_nics.update({n["interface_id"]: n for n in current_vm_in_db["nics"]})

                nics_in_domain = [all_possible_nics[x] for x in interface_ids_in_domain]

                current_vm_in_db["nics"] = nics_in_domain
                domain.update_nic_property_from_domain(libvirt_conn, current_vm_in_db)

                vm_wrapper.VMWrapper.update_nics(self._vm_uuid, current_vm_in_db["nics"])
        except Exception as e:
            logging.warning("Sync VPC NICs from VM domain failed, error is {}".format(str(e)))

    def release_detached_vpc_nics_silent(self):
        """
        Handle VPC NICs after kvm_vm_config succeed. Because we don't know which VPC NICs are detached by only using
        vm_json, we have to compare vm_json and VPC NICs record in VPC service to know which VPC NICs are
        detached
        """
        try:
            if not self.network_client.is_cluster_associated_to_vpc_service():
                return

            nics_in_vpc_service = self.network_client.get_vpc_nics(self._vm_uuid).keys()
            nics_to_be_deleted = [n for n in nics_in_vpc_service if n not in self._vpc_nics_interface_ids]
            self._delete_vpc_nics_silent_by_interface_ids(nics_to_be_deleted, "NIC removal or VM Rollback succeed")
        except Exception as e:
            logging.warning("Detach VPC NICs failed, error is {}".format(str(e)))

    def delete_vpc_nics_on_vm_deletion(self):
        self._delete_vpc_nics_silent(self._vpc_nics, "VM deletion")

    def ensure_vpc_nic_mac_address(self):
        """In case mac address is inconsistent between VPC and ELF, use update VNIC API to ensure mac address each
        time the VM start
        """
        if not self._vpc_nics:
            return

        try:
            operation_result = self.network_client.update_vpc_nics_mac_addr(
                self._vm_uuid, [{"vnic_uuid": n["interface_id"], "mac_addr": n["mac_address"]} for n in self._vpc_nics]
            )
            check_operation_results(operation_result, [], len(self._vpc_nics), raise_exception=True)
        except Exception as e:
            raise base.ResourceException(
                "Ensure mac address of VPC NIC({}) on VM before start failed, error={}".format(self._vm_uuid, str(e)),
                pyerror_pb2.JOB_ENSURE_VPC_NIC_MAC_ADDR_BEFORE_START_FAILED,
            )

    def update_vpc_nic_cluster_uuid(self, cluster_uuid, retry_times=3):
        if not self._vpc_nics:
            return

        for i in range(0, retry_times):
            try:
                operation_result = self.network_client.update_vpc_nics_cluster_uuid(
                    self._vm_uuid, [{"vnic_uuid": n["interface_id"]} for n in self._vpc_nics], cluster_uuid
                )
                check_operation_results(operation_result, [], len(self._vpc_nics), raise_exception=True)
                return
            except Exception as e:
                logging.warning(
                    "Retry time: {}. Update cluster_uuid of VPC NIC({}) for VM({}) failed, error={}".format(
                        i + 1, self._vpc_nics, self._vm_uuid, str(e)
                    )
                )

        raise base.ResourceException(
            "Update cluster_uuid of VPC NIC({}) for VM({}) failed. Retry time exceeds".format(
                self._vpc_nics, self._vm_uuid
            ),
            pyerror_pb2.JOB_UPDATE_VPC_NIC_CLUSTER_UUID_FAILED,
        )

    def pop_additional_fields(self):
        result = {}
        for n in self._vpc_nics:
            for key in self.NIC_ADDITIONAL_FIELDS:
                if key in n:
                    result.setdefault(n["interface_id"], {}).update({key: n.pop(key)})

        return result

    def restore_additional_fields(self, additional_infos):
        if not additional_infos:
            return

        for n in self._vpc_nics:
            if n["interface_id"] in additional_infos:
                n.update(additional_infos[n["interface_id"]])


def create_vpc_nics_on_vm_create(func):
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        vpc_nic_handler = VPCNICHandler(vm_json)
        vpc_nic_additional_fields = {}
        try:
            vpc_nic_handler.prepare_vpc_nics()
            vpc_nic_additional_fields = vpc_nic_handler.pop_additional_fields()
            result = func(self, vm_json, *args, **kwargs)
            # Deep copy the result to return so that restore additional fields won't affect "nics" to return
            return copy.deepcopy(result)
        except Exception:
            vpc_nic_handler.delete_vpc_nics_on_create_fail_silent()
            raise
        finally:
            if vpc_nic_additional_fields:
                vpc_nic_handler.restore_additional_fields(vpc_nic_additional_fields)

    return wrapper


class VPCNICSnapshotHandler:
    def __init__(self, vm_snapshot_json):
        self._vm_snapshot_json = vm_snapshot_json
        self._vm_snapshot_uuid = vm_snapshot_json["uuid"]
        self._vpc_nics = [
            nic for nic in vm_snapshot_json.get("nics", []) if nic["type"] == elf_common_constants.NIC_TYPE_VPC
        ]
        self._network_client = None

    @property
    def network_client(self):
        if self._network_client is None:
            self._network_client = network.Client()

        return self._network_client

    def delete_vpc_nic_snapshots_silent(self):
        if not self._vpc_nics:
            return

        vpc_nic_snapshot_uuids = [nic["vnic_snapshot_uuid"] for nic in self._vpc_nics]

        try:
            operation_results = self.network_client.delete_vpc_nic_snapshots(
                self._vm_snapshot_uuid, vpc_nic_snapshot_uuids
            )
            check_operation_results(operation_results, [], len(vpc_nic_snapshot_uuids), raise_exception=True)
        except Exception as e:
            logging.warning(
                "Delete VPC NIC snapshots({}) failed, nic_snapshot_uuids={}, error={}".format(
                    self._vm_snapshot_json["uuid"], vpc_nic_snapshot_uuids, str(e)
                )
            )

    def create_vpc_nic_snapshot_info(self):
        if not self._vpc_nics:
            return

        try:
            operation_results = self.network_client.create_vpc_nic_snapshots(
                cluster.get_cluster_id(),
                self._vm_snapshot_json["uuid"],
                self._vm_snapshot_json["vm_uuid"],
                [
                    {"vnic_uuid": x["interface_id"], "vnic_snapshot_uuid": x["vnic_snapshot_uuid"]}
                    for x in self._vpc_nics
                ],
            )
            check_operation_results(
                operation_results,
                [VPC_RESOURCE_OPERATION_ERROR_REASON_ALREADY_EXISTS],
                len(self._vpc_nics),
                raise_exception=True,
            )
        except Exception as e:
            raise base.ResourceException(
                "Create NIC snapshot for vm_snapshot({}) failed, error={}".format(
                    self._vm_snapshot_json["uuid"], str(e)
                ),
                pyerror_pb2.JOB_CREATE_NIC_SNAPSHOTS_FAILED,
            )

        # Fill info for NIC in vm_snapshot_json
        nic_snapshot_infos = {
            x["vnic_uuid"]: {
                "vnic_snapshot_uuid": x["vnic_snapshot_uuid"],
                "vpc_uuid": x["vpc_uuid"],
                "subnet_uuid": x["subnet_uuid"],
                "floating_ip_uuid": x["floating_ip_uuid"],
            }
            for x in operation_results
        }
        for nic in self._vpc_nics:
            snapshot_info = nic_snapshot_infos[nic["interface_id"]]
            nic["vnic_snapshot_uuid"] = snapshot_info["vnic_snapshot_uuid"]
            nic["vpc_uuid"] = snapshot_info["vpc_uuid"]
            nic["subnet_uuid"] = snapshot_info["subnet_uuid"]
            nic["use_floating_ip"] = snapshot_info["floating_ip_uuid"] != ""


def create_vpc_nic_snapshots_on_vm_snapshot_creation(func):
    @functools.wraps(func)
    def wrapper(self, vm_snapshot_json, *args, **kwargs):
        handler = VPCNICSnapshotHandler(vm_snapshot_json)
        try:
            handler.create_vpc_nic_snapshot_info()
            return func(self, vm_snapshot_json, *args, **kwargs)
        except Exception:
            handler.delete_vpc_nic_snapshots_silent()
            raise

    return wrapper


def handle_vpc_nics_on_config(func):
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        vpc_vnic_handler = VPCNICHandler(vm_json)
        vpc_nic_additional_fields = {}
        try:
            vpc_vnic_handler.prepare_vpc_nics()
            vpc_nic_additional_fields = vpc_vnic_handler.pop_additional_fields()
            result = func(self, vm_json, *args, **kwargs)
        except Exception:
            vpc_vnic_handler.sync_vpc_nics_from_vm_domain_silent(kwargs["libvirt_conn"])
            raise
        else:
            vpc_vnic_handler.release_detached_vpc_nics_silent()
            # Deep copy the result to return so that restore additional fields won't affect "nics" to return
            return copy.deepcopy(result)
        finally:
            if vpc_nic_additional_fields:
                vpc_vnic_handler.restore_additional_fields(vpc_nic_additional_fields)

    return wrapper


def ensure_vpc_nic_mac_address_on_vm_start(func):
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        vpc_vnic_handler = VPCNICHandler(vm_json)
        vpc_vnic_handler.ensure_vpc_nic_mac_address()
        return func(self, vm_json, *args, **kwargs)

    return wrapper
