from collections import defaultdict
import copy

from common.config.resources import RESOURCE_IN_USE
from common.mongo.db import mongodb
from job_center.common import utils as jc_utils
from smartx_app.elf.common.constants import SNAPSHOT_DELETED, VM_RUNNING, VM_STOPPED
from smartx_app.elf.common.events.vm_events_wrapper import VMEventWrapper
from smartx_app.elf.common.resource_query.vm_snapshot_querier import VMS<PERSON>shotQuerier
from smartx_app.elf.common.resource_wrappers import sriov
from smartx_app.elf.common.resource_wrappers.vmtools_wrapper import VMToolsWrapper
from smartx_app.elf.common.resource_wrappers.volume_snapshot_wrapper import Snapshot
from smartx_app.elf.common.resource_wrappers.volume_wrapper import VolumeWrapper
from smartx_app.elf.common.resources.base import ResourceException
from smartx_app.elf.common.resources.folder import Folder
from smartx_app.elf.common.resources.volume_vm_relationship import filter_out_sharing_vol
from smartx_app.elf.common.utils import network, resource
from smartx_app.elf.job_center.constants import (
    BATCH_SNAPSHOT_CREATED,
    FS_CONSISTENT,
    FS_CONSISTENT_SNAPSHOT_CREATED,
    ISO_IMAGE,
    KVM_VM,
    KVM_VM_SNAPSHOT,
    KVM_VOL_ISCSI_SNAPSHOT,
    KVM_VOL_SNAPSHOT,
)
from smartx_app.elf.job_center.lib.converter.domain import (
    create_vm_snapshot,
    get_vm_snapshot_rebuild_resources,
    vm_snapshot_rollback,
)
from smartx_app.elf.job_center.lib.converter.storage import get_deleted_vol_to_rebuild, get_vol_snapshots_for_rollback
from smartx_proto.errors import pyerror_pb2 as py_error


class VMSnapshotWrapper:
    collection = mongodb.resources.resource

    @classmethod
    def page_query(
        cls, count, skip_page, sort_criteria, filter_criteria, projection, start_uuid, end_uuid, skip_to_last
    ):
        vm_snapshots = VMSnapshotQuerier().page_query(
            count=count,
            skip_page=skip_page,
            sort_criteria=sort_criteria,
            filter_criteria=filter_criteria,
            projection=projection,
            start_uuid=start_uuid,
            end_uuid=end_uuid,
            skip_to_last=skip_to_last,
        )
        cls.fit_additional_info(*vm_snapshots["entities"])
        VMToolsWrapper.apply_cdroms(*vm_snapshots["entities"])
        return vm_snapshots

    @classmethod
    def search(cls, text, limit=None, filter_criteria=None):
        vm_snapshots = VMSnapshotQuerier().search(text, limit, filter_criteria)
        cls.fit_additional_info(*vm_snapshots["entities"])
        VMToolsWrapper.apply_cdroms(*vm_snapshots["entities"])
        return vm_snapshots

    @classmethod
    def get(cls, vm_snapshot_uuid):
        snapshot = VMSnapshotQuerier().get(vm_snapshot_uuid)
        if snapshot is None:
            raise ResourceException(
                "VM snapshot {} is not found".format(vm_snapshot_uuid), py_error.VM_SNAPSHOT_NOT_FOUND
            )

        cls.fit_volume_info(snapshot)
        cls.fit_nic_info(snapshot)
        cls.fit_additional_info(snapshot)
        VMToolsWrapper.apply_cdroms(snapshot)
        return snapshot

    @staticmethod
    def fit_volume_info(snapshot):
        """fit disk.name disk.size to disks"""
        if snapshot:
            uuids = [x["snapshot_uuid"] for x in snapshot["disks"] if x["type"] == "disk"]
            vols_mapping = {x["uuid"]: x for x in Snapshot.batch_get_by_uuid(uuids)}
            for vol in snapshot["disks"]:
                if vol["type"] == "disk" and vol["snapshot_uuid"] in vols_mapping:
                    vol["name"] = vols_mapping[vol["snapshot_uuid"]]["name"]
                    vol["size"] = vols_mapping[vol["snapshot_uuid"]]["volume_size"]

        return snapshot

    @staticmethod
    def fit_nic_info(snapshot):
        if snapshot:
            resource.add_more_info_to_nic(snapshot["nics"])
        return snapshot

    @staticmethod
    def fit_additional_info(*snapshots):
        """
        This function is designed to be backward-compatible:
         - In mongodb, the "cloud_init_supported" field should not exist in the snapshot resource.
         - The earlier version returned this field to the front end, so it is defaulted to false for compatibility.
        """
        for snapshot in snapshots:
            snapshot["cloud_init_supported"] = False

    @classmethod
    def batch_query(cls, vm_snapshot_uuid_list):
        vm_snapshots = VMSnapshotQuerier().query(
            {"uuid": {"$in": vm_snapshot_uuid_list}}, top=len(vm_snapshot_uuid_list)
        )

        return vm_snapshots

    @classmethod
    def submit_job(cls, description, resources, job_id=None, event=None):
        from job_center.handler.leader.workers import job_submit

        returned_job_id = job_submit(user="", description=description, resources=resources, job_id=job_id, event=event)
        return {"job_id": returned_job_id}

    @classmethod
    def find(cls, snapshot_id=None):
        conditions = {
            "type": KVM_VM_SNAPSHOT,
            "resource_state": RESOURCE_IN_USE,
        }
        show = {
            "_id": 0,
        }
        if snapshot_id:
            conditions["uuid"] = snapshot_id
            result = cls.collection.find_one(conditions, show)
            if not result:
                raise ResourceException("VM snapshot not found", py_error.VM_SNAPSHOT_NOT_FOUND)
            return result
        else:
            return cls.collection.find(conditions, show)

    @classmethod
    def check_vm_snapshot_uuid_exists(cls, vm_snapshot_uuid):
        # Order matters
        if jc_utils.check_resource_lock(vm_snapshot_uuid):
            raise ResourceException(
                "The resource({}) is being operated.".format(vm_snapshot_uuid),
                py_error.VM_SNAPSHOT_DUPLICATED_UUID,
            )

        vm_snapshot = cls.collection.find_one({"uuid": vm_snapshot_uuid, "resource_state": RESOURCE_IN_USE}, {"_id": 0})
        if vm_snapshot:
            raise ResourceException(
                "The resource({}) has been already created.".format(vm_snapshot_uuid),
                py_error.VM_SNAPSHOT_DUPLICATED_UUID,
            )

    @classmethod
    def create(
        cls,
        vm_uuid,
        snapshot_name,
        description,
        consistent_type=None,
        batch_creation=False,
        job_id=None,
        vm_snapshot_uuid=None,
    ):
        vm_json = cls.collection.find_one(
            {"uuid": vm_uuid, "type": KVM_VM, "resource_state": RESOURCE_IN_USE}, {"_id": 0}
        )
        if not vm_json:
            raise ResourceException("VM not found", py_error.VM_NOT_FOUND)

        vm_json["disks"] = filter_out_sharing_vol(vm_json.get("disks", []))
        # Fix PYZBS-2713, skip importing update_time from vm to vm snapshot
        vm_json.pop("update_time", None)
        vm_json.pop("last_shutdown_time", None)
        vm_json.pop("last_start_time", None)
        vm_json.pop("last_resume_time", None)
        # remove passthrough devices
        vm_json.pop("hostdevs", [])
        # remove sriov nics
        vm_json["nics"] = sriov.exclude_sriov_nics(vm_json.get("nics", []))
        # remove cpu exclusive
        vm_json.pop("cpu_exclusive", None)
        vm_json.pop("vm_version", None)
        vm_json.pop("vm_version_mode", None)
        vm_json.pop("cpu_qos", None)
        vm_json.pop("anti_malware", None)
        vm_json.pop("migratable", None)
        resources = create_vm_snapshot(vm_json, snapshot_name, description, vm_snapshot_uuid)

        vm_snapshot_resource = next(x for x in resources if x["type"] == KVM_VM_SNAPSHOT)

        is_consistent_snapshot = vm_json["status"] == VM_RUNNING and consistent_type == FS_CONSISTENT
        if not batch_creation and not is_consistent_snapshot:
            return {r["uuid"]: r for r in resources}

        # try to batch request using one subtask
        volume_snapshot_resources = [x for x in resources if x["type"] in (KVM_VOL_SNAPSHOT, KVM_VOL_ISCSI_SNAPSHOT)]

        vm_snapshot_resource["_volume_snapshots"] = volume_snapshot_resources
        vm_snapshot_resource["_job_id"] = job_id

        # modify job resources for creating file system consistent snapshot
        if is_consistent_snapshot:
            # change vm snapshot status to split a new subtask for this resource
            vm_snapshot_resource["status"] = FS_CONSISTENT_SNAPSHOT_CREATED

            # for file system consistent snapshot, we need to use vmtools to freeze the VM,
            # and this operation must be performed on the host where the VM is located
            vm_snapshot_resource["_node_ip"] = vm_json.get("node_ip")

        status = FS_CONSISTENT_SNAPSHOT_CREATED if is_consistent_snapshot else BATCH_SNAPSHOT_CREATED
        for volume_snapshot_resource in volume_snapshot_resources:
            # change volume snapshot status to not split subtask for this resource
            # but still lock and unlock this resource for this job
            volume_snapshot_resource["status"] = status

        return {r["uuid"]: r for r in resources}

    @classmethod
    def update(cls, snapshot_id, name, description):
        if not name:
            raise ResourceException("Must specify the snapshot name", py_error.VM_SNAPSHOT_UPDATE_FAILED)

        update_doc = {"name": name}
        if description is not None:
            update_doc["description"] = description

        result = cls.collection.update_one(
            {
                "uuid": snapshot_id,
                "resource_state": RESOURCE_IN_USE,
                "type": KVM_VM_SNAPSHOT,
            },
            {"$set": update_doc},
        )

        # Reentrant allowed
        return result.matched_count == 1

    @classmethod
    def delete(cls, snapshot_id):
        vm_snapshot = cls.collection.find_one(
            {"uuid": snapshot_id, "type": KVM_VM_SNAPSHOT, "resource_state": RESOURCE_IN_USE}, {"_id": 0}
        )
        if not vm_snapshot:
            raise ResourceException("VM snapshot not found", py_error.VM_SNAPSHOT_NOT_FOUND)
        else:
            vm_snapshot["status"] = SNAPSHOT_DELETED
            delete_resources = [vm_snapshot]
            volume_snapshot_ids = [disk["snapshot_uuid"] for disk in vm_snapshot["disks"] if disk["type"] == "disk"]
            volume_snapshots = cls.collection.find(
                {
                    "uuid": {"$in": volume_snapshot_ids},
                    "type": {"$in": [KVM_VOL_SNAPSHOT, KVM_VOL_ISCSI_SNAPSHOT]},
                    "resource_state": RESOURCE_IN_USE,
                },
                {"_id": 0},
            )
            for snapshot in volume_snapshots:
                snapshot["status"] = SNAPSHOT_DELETED
                delete_resources.append(snapshot)

            return {r["uuid"]: r for r in delete_resources}

    @classmethod
    def bulk_delete(cls, uuids):
        vm_snapshots = cls.collection.find(
            {"uuid": {"$in": uuids}, "type": KVM_VM_SNAPSHOT, "resource_state": RESOURCE_IN_USE}, {"_id": 0}
        )
        vm_snapshots = list(vm_snapshots)
        if not set(uuids).issubset([s["uuid"] for s in vm_snapshots]):
            raise ResourceException("Some of VM snapshots are not found", py_error.VM_SNAPSHOT_NOT_FOUND)

        resources = []
        disks = []
        for snapshot in vm_snapshots:
            snapshot["status"] = SNAPSHOT_DELETED
            resources.append(snapshot)
            disks.extend([d["snapshot_uuid"] for d in snapshot["disks"] if d["type"] == "disk"])

        volume_snapshots = cls.collection.find(
            {
                "uuid": {"$in": disks},
                "type": {"$in": [KVM_VOL_SNAPSHOT, KVM_VOL_ISCSI_SNAPSHOT]},
                "resource_state": RESOURCE_IN_USE,
            },
            {"_id": 0},
        )
        for vs in volume_snapshots:
            vs["status"] = SNAPSHOT_DELETED
            resources.append(vs)

        return {r["uuid"]: r for r in resources}

    @classmethod
    def bulk_delete_from_vm_uuids(cls, vm_uuids):
        if not vm_uuids:
            return {}
        vm_snapshots = cls.collection.find(
            {"vm_uuid": {"$in": vm_uuids}, "type": KVM_VM_SNAPSHOT, "resource_state": RESOURCE_IN_USE},
            {"_id": 0, "uuid": 1},
        )
        vm_snapshots = list(vm_snapshots)

        rs = cls.bulk_delete([v["uuid"] for v in vm_snapshots])
        disk_mapping = {}
        resources = defaultdict(dict)
        vol_snapshots = []
        for r in list(rs.values()):
            if r["type"] == KVM_VM_SNAPSHOT:
                resources[r["vm_uuid"]][r["uuid"]] = r
                for d in r["disks"]:
                    if d["type"] == "disk":
                        disk_mapping[d["snapshot_uuid"]] = r["vm_uuid"]
            else:
                vol_snapshots.append(r)

        for r in vol_snapshots:
            vs_uuid = r["uuid"]
            vm_uuid = disk_mapping[vs_uuid]
            resources[vm_uuid][vs_uuid] = r

        return resources

    @staticmethod
    def _check_storage_cluster_consistency(vol_snapshots, vols):
        """
        TODO(jingming): After a storage migration, the VM's vol cannot correspond to the snapshot,
        so only the current vol storage cluster is checked to see if it matches the snapshot,
        and if it doesn't, rollback is disallowed,
        preventing the vol from being created on the source storage cluster.

        At the same time, only support VMs using vol from the same storage cluster,
        if the demand changes in the future, the patch needs to be followed up again.

        Args:
            vol_snapshots (list): list of volume snapshots ins
            vols (list): list of volumes
        """
        vol_snapshots_sc = set()
        for r in vol_snapshots:
            if sc_uuid := getattr(r, "storage_cluster_uuid", None):
                vol_snapshots_sc.add(sc_uuid)

        vol_sc = set()
        for r in vols:
            if sc_uuid := r.get("storage_cluster_uuid"):
                vol_sc.add(sc_uuid)

        if vol_snapshots_sc != vol_sc:
            raise ResourceException(
                f"Storage cluster of volume snapshot ({vol_snapshots_sc})" f" and volume ({vol_sc}) is not consistent",
                py_error.PRECHECK_ROLLBACK_STORAGE_CLUSTER_INCONSISTENCY,
            )

    @classmethod
    def rollback(cls, snapshot_id):
        vm_snapshot = cls.collection.find_one(
            {"uuid": snapshot_id, "type": KVM_VM_SNAPSHOT, "resource_state": RESOURCE_IN_USE}, {"_id": 0}
        )
        if not vm_snapshot:
            raise ResourceException("VM snapshot not found", py_error.VM_SNAPSHOT_NOT_FOUND)
        else:
            vm = cls.collection.find_one(
                {"uuid": vm_snapshot["vm_uuid"], "type": KVM_VM, "resource_state": RESOURCE_IN_USE}
            )
            if not vm:
                raise ResourceException("VM not found", py_error.VM_NOT_FOUND)

            if vm["status"] != VM_STOPPED:
                raise ResourceException(
                    "The operation only support for the stopped VM.", py_error.PRECHECK_VMS_STATE_UNMATCH
                )

            existing_iso = cls.collection.find(
                {
                    "type": ISO_IMAGE,
                    "resource_state": RESOURCE_IN_USE,
                    "path": {"$in": [disk["path"] for disk in vm_snapshot["disks"] if disk["type"] == "cdrom"]},
                }
            )
            existing_iso_path = [iso["path"] for iso in list(existing_iso) + VMToolsWrapper.get_images()]

            snapshot_disks = [_f for _f in [x.get("volume_uuid") for x in vm_snapshot.get("disks", [])] if _f]
            volumes = VolumeWrapper.batch_query(snapshot_disks)
            # 1. rollback volumes
            vol_snapshots = get_vol_snapshots_for_rollback(vm_snapshot, vm, volumes)
            rollback_resources = [s.rollback() for s in vol_snapshots]
            # 2. volume that deleted in snapshot should rebuild and mount
            if len(volumes) == len(snapshot_disks):
                deleted_vol_snapshots_list = []
            else:
                deleted_vol_snapshots_list = get_deleted_vol_to_rebuild(vm_snapshot, vm, volumes)
            cls._check_storage_cluster_consistency([*vol_snapshots, *deleted_vol_snapshots_list], volumes)
            rebuild_snapshot_vols_map = {s.uuid: s.rebuild() for s in deleted_vol_snapshots_list}
            rollback_resources.extend(list(rebuild_snapshot_vols_map.values()))

            # 3. rollback vm config
            rollback_resources.append(
                vm_snapshot_rollback(vm_snapshot, vm, existing_iso_path, volumes, rebuild_snapshot_vols_map)
            )

            return {r["uuid"]: r for r in rollback_resources}, vm_snapshot["name"]

    @classmethod
    def rebuild(cls, snapshot_id, data, user=None):
        vm_snapshot = cls.collection.find_one(
            {"uuid": snapshot_id, "type": KVM_VM_SNAPSHOT, "resource_state": RESOURCE_IN_USE}, {"_id": 0}
        )
        if not vm_snapshot:
            raise ResourceException("VM snapshot not found", py_error.VM_SNAPSHOT_NOT_FOUND)

        data.setdefault("nics", vm_snapshot["nics"])
        network.ensure_nic(data["nics"])
        network.ensure_vlan_for_nics(data["nics"])
        network.config_pci_address_for_vlan_nic_from_snapshot(vm_snapshot["nics"], data["nics"])

        existing_iso = cls.collection.find(
            {
                "type": ISO_IMAGE,
                "resource_state": RESOURCE_IN_USE,
                "path": {"$in": [disk["path"] for disk in vm_snapshot["disks"] if disk["type"] == "cdrom"]},
            }
        )
        existing_iso_path = [iso["path"] for iso in list(existing_iso) + VMToolsWrapper.get_images()]
        resources = get_vm_snapshot_rebuild_resources(vm_snapshot, existing_iso_path, data)
        vm = None
        folder = None
        folder_uuid = data.get("folder_uuid")
        if folder_uuid:
            folder = Folder.load(folder_uuid).info()
        for k in resources:
            if resources[k].get("type") == KVM_VM:
                vm = resources[k]
                if folder:
                    resources[k]["folder"] = folder
        event = VMEventWrapper(user=user).event_vm_rebuild(
            vm,
            vm_snapshot,
            data,
            existing_iso_path,
        )
        return resources, event

    @classmethod
    def fetch_all(cls, query_cond=None, fields_filter=None):
        q = copy.deepcopy(query_cond) if query_cond else {}
        q.update({"type": KVM_VM_SNAPSHOT, "resource_state": RESOURCE_IN_USE})

        f = copy.deepcopy(fields_filter) if fields_filter else {}
        f.update({"_id": 0})

        return cls.collection.find(q, f)
