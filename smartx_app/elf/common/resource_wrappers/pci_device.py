# Copyright (c) 2022, SMARTX
# All rights reserved.
import copy
import logging

from common.http import exceptions, tuna
from smartx_app.common.node import db
from smartx_app.elf.common import constants as elf_constants
from smartx_app.elf.common.resources import base
from smartx_proto.errors import pyerror_pb2 as py_error


class PCIAddress:
    def __init__(self, domain_hex_str, bus_hex_str, slot_hex_str, function_hex_str):
        if not domain_hex_str.startswith("0x"):
            domain_hex_str = "0x" + domain_hex_str
        if not bus_hex_str.startswith("0x"):
            bus_hex_str = "0x" + bus_hex_str
        if not slot_hex_str.startswith("0x"):
            slot_hex_str = "0x" + slot_hex_str
        if not function_hex_str.startswith("0x"):
            function_hex_str = "0x" + function_hex_str
        self._domain = domain_hex_str
        self._bus = bus_hex_str
        self._slot = slot_hex_str
        self._function = function_hex_str

    def dump(self):
        return {
            "@bus": self._bus,
            "@slot": self._slot,
            "@function": self._function,
            "@domain": self._domain,
        }

    def to_str(self):
        # Serialize the PCI address.
        # Used to compare two PCI address
        return "@domain: {}, @bus: {}, @slot: {}, @function: {}".format(
            int(self._domain, base=16), int(self._bus, base=16), int(self._slot, base=16), int(self._function, base=16)
        )


def create_pci_address_from_bus_location(bus_location):
    # bus_location format: 0000:2f:00.0
    # domain=0000, bus=2f, slot=00, function=0
    # Caller should catch bus_location format error
    tmp = str.split(bus_location, ":")
    domain = tmp[0]
    bus = tmp[1]
    tmp2 = str.split(tmp[2], ".")
    slot = tmp2[0]
    function = tmp2[1]

    return PCIAddress(domain, bus, slot, function)


class PCIDevice:
    SUPER_TYPE = elf_constants.PASS_THROUGH_PCI_DEVICE_SUPER_TYPE
    _subtype = SUPER_TYPE

    def __init__(self, uuid, guest_pci_address):
        self.uuid = uuid
        self.guest_pci_address = guest_pci_address
        self.host_pci_address = None

    def set_host_pci_address_by_bus_location(self, bus_location):
        self.host_pci_address = create_pci_address_from_bus_location(bus_location).dump()

    @staticmethod
    def get_device_uuid_from_xml_alias(xml_alias):
        # xml_alias format:
        # {"@name": "ua-<device_uuid>"}
        return xml_alias["@name"][3:]

    @property
    def alias_name(self):
        return "ua-{}".format(self.uuid)

    @property
    def domain_json(self):
        dev_domain_json = {
            "@mode": "subsystem",
            "@type": "pci",
            "@managed": "yes",
            "alias": {"@name": self.alias_name},
        }
        if self.guest_pci_address:
            dev_domain_json["address"] = self.guest_pci_address
        if self.host_pci_address:
            dev_domain_json["source"] = {"address": self.host_pci_address}
        return dev_domain_json


class PCIDeviceHandler:
    MOCK_PCI_ADDRESS = PCIAddress("0x0000", "0xff", "0x1f", "0x7").dump()

    SUPER_TYPE = elf_constants.PASS_THROUGH_PCI_DEVICE_SUPER_TYPE
    _subtype = SUPER_TYPE
    _dom_host_type = SUPER_TYPE
    _dom_guest_type = SUPER_TYPE

    _dev_assign_failed_ec = py_error.PCI_DEVICE_OPERATE_FAILED
    _dev_release_failed_ec = py_error.PCI_DEVICE_OPERATE_FAILED

    ASSIGN_ID_SPLITTER = "."

    def __init__(self, vm_json):
        self.vm_uuid = vm_json["uuid"]
        self._host_data_ip = vm_json["node_ip"]
        self._host_uuid = None
        self._tuna_client = tuna.Client()
        self._vm_json = vm_json
        self._devices = self._initialize_devices()

    @property
    def host_uuid(self):
        if not self._host_uuid:
            self._host_uuid = db.query_host_by_data_ip(self._host_data_ip)["host_uuid"]
        return self._host_uuid

    @property
    def vm_json(self):
        return copy.deepcopy(self._vm_json)

    @property
    def _matched_host_devs(self):
        return {dev["uuid"]: dev for dev in self._vm_json.get("hostdevs", []) if dev["type"] == self._subtype}

    def _initialize_devices(self):
        raise NotImplementedError()

    def _release_device(self, host_uuid, dev_uuid):
        raise NotImplementedError()

    def _allocate_device(self, dev_uuid):
        raise NotImplementedError()

    def _get_device_assign_infos(self, host_uuid):
        raise NotImplementedError()

    def release_device_silent(self):
        for dev in self._devices:
            try:
                self._release_device(self.host_uuid, dev.uuid)
            except Exception as e:
                logging.warning(
                    "Release PCI device(uuid: {}, type: {}) for VM({}) failed, error is ({})".format(
                        dev.uuid, self._subtype, self.vm_uuid, str(e)
                    )
                )

    def allocate_device(self):
        for dev in self._devices:
            try:
                dev_info = self._allocate_device(dev.uuid)
                dev.set_host_pci_address_by_bus_location(dev_info["bus_location"])
            except Exception as e:
                raise base.ResourceException(
                    "Allocate PCI device(uuid: {}, type: {}) for VM({}) failed, error is {}".format(
                        dev.uuid, self._subtype, self.vm_uuid, str(e)
                    ),
                    self._dev_assign_failed_ec,
                )

        return copy.deepcopy(self._devices)

    def allocate_mock_pci_address(self):
        # When assigning a PCI device to a VM that is shut down,
        # the PCI address in the VM cannot be determined, so a Mock PCI address is set in advance
        for dev in self._devices:
            dev.host_pci_address = self.MOCK_PCI_ADDRESS

        return copy.deepcopy(self._devices)

    def vm_has_matched_device(self):
        return len(self._matched_host_devs) > 0

    def update_device_guest_pci_address(self, domain_json):
        """
        # A PCI device pass-through xml example:
        # <hostdev mode = 'subsystem' type = 'pci' managed = 'yes' >
        #     <source>
        #         <address domain = '0x0000' bus = '0x58' slot = '0x00' function = '0x0'/>
        #     </source>
        #     <address type = 'pci' domain = '0x0000' bus = '0x00' slot = '0x0a' function ='0x0'/>
        #     <alias name="ua-<gpu_uuid>"/>
        # </hostdev>

        # A vgpu xml example:
        # <hostdev mode = 'subsystem' type = 'mdev' model = 'vfio-pci' >
        #     <source>
        #         <address uuid = '527b0bcd-6a68-4667-a074-6cdb9db76ae0'/>
        #     </source>
        #     <address type = 'pci' domain = '0x0000' bus = '0x00' slot = '0x0a' function ='0x0'/>
        #     <alias name="ua-<gpu_uuid>-vgpu<index>"/>
        # </hostdev>

        :param domain_json: dict of domain which contains devices field, and dict of devices contains hostdev
        :return: vm_json's dict, whose hostdevs field contains the pci_address field
        """
        if not self.vm_has_matched_device():
            return self.vm_json

        device_uuid_to_guest_pci_address = {}
        for host_dev in domain_json["domain"]["devices"]["hostdev"]:
            if host_dev["@type"] != self._dom_host_type:
                continue

            if not host_dev.get("alias") or not host_dev["alias"].get("@name"):
                continue

            device_uuid = PCIDevice.get_device_uuid_from_xml_alias(host_dev["alias"])
            guest_address = host_dev["address"]
            guest_pci_address = PCIAddress(
                guest_address["@domain"], guest_address["@bus"], guest_address["@slot"], guest_address["@function"]
            )
            device_uuid_to_guest_pci_address[device_uuid] = guest_pci_address.dump()

        for device_uuid, device_json in list(self._matched_host_devs.items()):
            guest_pci_address = device_uuid_to_guest_pci_address[device_uuid]
            guest_pci_address["@type"] = self._dom_guest_type
            device_json["pci_address"] = guest_pci_address

        return self.vm_json

    def _get_all_hosts_dev_assign_infos(self) -> list:
        host_uuids = [host["host_uuid"] for host in db.query_hosts()]
        devs_assign_infos = []
        for host_uuid in host_uuids:
            devs_assign_infos.extend(self._get_device_assign_infos(host_uuid))

        return devs_assign_infos

    def _release_pci_device_handler(self, release_func, *args, **kwargs):
        try:
            release_func(*args, **kwargs)
        except exceptions.RestfulClientError as e:
            if e.user_code not in (
                py_error.DEVICE_NOT_FOUND,  # PCI device has been unmounted on host
                py_error.DEVICE_NOT_ALLOCATED,  # PCI device released in last shutdown
                py_error.RELEASE_ID_NOT_MATCH,  # PCI device released in last shutdown and mounted to another VM
                py_error.DEVICE_USAGE_NOT_MATCH,  # API operation does not match the device usage
            ):
                raise base.ResourceException(
                    "Release PCI device(args: {} {}, type={}) for vm({}), failed, error is {}".format(
                        args, kwargs, self._subtype, self.vm_uuid, str(e)
                    ),
                    self._dev_release_failed_ec,
                )
        except Exception as e:
            raise base.ResourceException(
                "Release PCI device(args: {} {}, type={}) for vm({}) failed, error is ({})".format(
                    args, kwargs, self._subtype, self.vm_uuid, str(e)
                ),
                self._dev_release_failed_ec,
            )

    def release_unexpected_devices_by_tuna_record(self):
        # Release PCI devices in case some PCI devices release failed in last shutdown.
        # Hostdevs may be unmounted after shutdown, so assign relation should be
        # get from TUNA.
        # The VM may be migrated after detaching all hostdevs. So assign info among
        # all hosts should be gathered.
        devs_assign_infos = self._get_all_hosts_dev_assign_infos()

        # What is `current_mounted_devs` used for?
        # Assume a scenario:
        # A VM used to mount GPU1 on host h1. GPU1 release failed on shutdown.
        # The VM was migrated to host h2 and started with GPU2 mounted. Only GPU1
        # is unexpected and should be released.
        # In this scenario, current_mounted_devs=[gpu2_uuid]
        current_mounted_devs = []
        if self._vm_json["status"] in (
            elf_constants.VM_RUNNING,
            elf_constants.VM_SUSPENDED,
        ):
            current_mounted_devs = list(self._matched_host_devs.keys())

        vm_assigned_dev_infos = [
            (x["host_uuid"], x["device_id"])
            for x in devs_assign_infos
            if x["assign_id"] == self.vm_uuid and x["device_id"] not in current_mounted_devs
        ]
        if len(vm_assigned_dev_infos) == 0:
            return

        logging.info("VM({}) has unreleased PCI device({})".format(self.vm_uuid, vm_assigned_dev_infos))
        for x in vm_assigned_dev_infos:
            self._release_pci_device_handler(self._release_device, x[0], x[1])

    def _gen_assign_id(self, dev_uuid):
        # Default way of generating assign id for a PCI device
        return "{}{}{}".format(self.vm_uuid, self.ASSIGN_ID_SPLITTER, dev_uuid)

    @classmethod
    def parse_vm_uuid_from_assign_id(cls, assign_id):
        return assign_id.split(cls.ASSIGN_ID_SPLITTER)[0]

    @classmethod
    def parse_dev_uuid_from_assign_id(cls, assign_id):
        return assign_id.split(cls.ASSIGN_ID_SPLITTER)[1]
