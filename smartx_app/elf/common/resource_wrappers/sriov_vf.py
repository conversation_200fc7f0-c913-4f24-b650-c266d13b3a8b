# Copyright (c) 2024 SMARTX, Inc.
# All rights reserved.
import copy
import functools
import logging

from common.http import exceptions
from smartx_app.common.node import db
from smartx_app.elf.common import constants as elf_constants
from smartx_app.elf.common.resource_wrappers import pci_device
from smartx_app.elf.common.resources import base
from smartx_app.elf.common.utils import vm_start
from smartx_proto.errors import pyerror_pb2 as py_error


class VFDevice(pci_device.PCIDevice):
    def __init__(self, vf_uuid, guest_pci_address, pf_uuid, related_vf_device_json):
        self._pf_uuid = pf_uuid
        self._assign_id = None
        self._related_vf_device_json = related_vf_device_json
        super().__init__(vf_uuid, guest_pci_address)

    @property
    def pf_uuid(self):
        return self._pf_uuid

    @property
    def related_vf_device_json(self):
        # Should return origin reference of the vm_json, not a copy of it.
        return self._related_vf_device_json


class VFHandler(pci_device.PCIDeviceHandler):
    _subtype = elf_constants.PASS_THROUGH_VF_PCI

    _dev_assign_failed_ec = py_error.SRIOV_VF_ASSIGN_FAILED
    _dev_release_failed_ec = py_error.SRIOV_VF_RELEASE_FAILED

    def __init__(self, vm_json):
        super().__init__(vm_json)
        self._vf_pci_devices = self._devices

    def _initialize_devices(self):
        # The format for passing parameters from the front end is:
        # `hostdevs: [{"type": PASS_THROUGH_VF_PCI, "uuid": pf_uuid}]`,
        # where the `uuid` refers to the UUID of the physical function (PF)
        # where the virtual function (VF) is located.
        vf_pci_device_json = sorted(
            (d for d in self._vm_json.get("hostdevs", []) if d["type"] == self._subtype), key=lambda x: x["uuid"]
        )

        vf_pci_devices = []
        previous_pf_uuid = None
        index = 0

        for device in vf_pci_device_json:
            current_pf_uuid = device["uuid"]
            if current_pf_uuid != previous_pf_uuid:
                index = 0
                previous_pf_uuid = current_pf_uuid

            vf_uuid = self._gen_uuid(current_pf_uuid, index)
            vf_pci_device = VFDevice(vf_uuid, device.get("guest_pci_address"), current_pf_uuid, device)
            vf_pci_devices.append(vf_pci_device)

            index += 1

        return vf_pci_devices

    @property
    def _matched_host_devs(self):
        return {vf_device.uuid: vf_device.related_vf_device_json for vf_device in self._vf_pci_devices}

    def _gen_assign_id(self, vf_uuid):
        return "{}-{}".format(self.vm_uuid, vf_uuid)

    def gen_all_assign_ids(self):
        return [self._gen_assign_id(vf_pci_device.uuid) for vf_pci_device in self._vf_pci_devices]

    @staticmethod
    def get_vm_uuid_from_assign_id(assign_id):
        return assign_id[0:36]

    @staticmethod
    def get_pf_uuid_from_assign_id(assign_id):
        return assign_id[37:73]

    def _release_vf_pci_devices(self, host_uuid, pf_uuid, assign_ids):
        self._tuna_client.release_sriov_pcis(host_uuid, pf_uuid, assign_ids)

    def _get_device_assign_infos(self, host_uuid):
        vf_pci_device_assign_infos = []
        pci_assign_infos = self._tuna_client.get_pci_assign_infos(host_uuid)
        for x in pci_assign_infos:
            if x["assign_type"] != elf_constants.PCI_USAGE_SRIOV:
                continue

            for assigned_vf_pci_device in x["assigned_vfs"]:
                if assigned_vf_pci_device["assign_id"] == "":
                    continue

                assigned_vm_uuid = self.get_vm_uuid_from_assign_id(assigned_vf_pci_device["assign_id"])
                if assigned_vm_uuid == self.vm_uuid:
                    vf_pci_device_assign_infos.append(
                        {
                            "host_uuid": x["host_uuid"],
                            "pf_uuid": x["device_id"],
                            "assign_id": assigned_vf_pci_device["assign_id"],
                        }
                    )

        return vf_pci_device_assign_infos

    def release_vf_pci_devices_silent(self):
        pf_uuid_to_assign_ids = {}
        for vf_pci_device in self._vf_pci_devices:
            pf_uuid_to_assign_ids.setdefault(vf_pci_device.pf_uuid, []).append(self._gen_assign_id(vf_pci_device.uuid))

        for pf_uuid, assign_ids in list(pf_uuid_to_assign_ids.items()):
            try:
                self._tuna_client.release_sriov_pcis(self.host_uuid, pf_uuid, assign_ids)
            except Exception as e:
                logging.warning(
                    "Release VFs(assign ids: {}) of PCI({}) silent failed, error is ({})".format(
                        assign_ids, pf_uuid, str(e)
                    )
                )

    def vm_has_vf_pci_device(self):
        return len(self._vf_pci_devices) > 0

    @staticmethod
    def _gen_uuid(pf_uuid, index):
        return "{}-vf{}".format(pf_uuid, index)

    def release_unexpected_vf_pci_devices_by_tuna_record(self):
        self.release_unexpected_devices_by_tuna_record()

    def release_unexpected_devices_by_tuna_record(self):
        # Release VF_PCI in case some VF_PCI release failed in last shutdown.
        # Hostdevs may be unmounted after shutdown, so assign relation should be
        # get from TUNA.
        # The VM may be migrated after detaching all hostdevs. So assign info among
        # all hosts should be gathered.
        host_uuids = [host["host_uuid"] for host in db.query_hosts()]
        vf_pci_device_assign_infos = []
        for host_uuid in host_uuids:
            vf_pci_device_assign_infos.extend(self._get_device_assign_infos(host_uuid))

        current_vf_pci_device_assign_ids = []
        if self._vm_json["status"] in (
            elf_constants.VM_RUNNING,
            elf_constants.VM_SUSPENDED,
        ):
            current_vf_pci_device_assign_ids = [self._gen_assign_id(v.uuid) for v in self._vf_pci_devices]

        unexpected_assign_infos = [
            x for x in vf_pci_device_assign_infos if x["assign_id"] not in current_vf_pci_device_assign_ids
        ]

        if len(unexpected_assign_infos) == 0:
            return

        logging.info("VM({}) has unexpected assign_infos({})".format(self.vm_uuid, unexpected_assign_infos))

        pf_uuid_to_assign_infos = {}
        for x in unexpected_assign_infos:
            pf_uuid_to_assign_infos.setdefault(x["pf_uuid"], []).append(x)

        for pf_uuid, assign_infos in list(pf_uuid_to_assign_infos.items()):
            host_uuid = assign_infos[0]["host_uuid"]
            assign_ids = [x["assign_id"] for x in assign_infos]
            try:
                self._release_vf_pci_devices(host_uuid, pf_uuid, assign_ids)
            except exceptions.RestfulClientError as e:
                if e.user_code not in (
                    py_error.DEVICE_NOT_FOUND,  # VF_PCI has been detached from host
                    py_error.DEVICE_NOT_ALLOCATED,  # VF_PCI released in last shutdown
                    py_error.DEVICE_USAGE_NOT_MATCH,  # PCI usage has been set to 'pass-through'
                ):
                    raise base.ResourceException(
                        "Release VF_PCIs (host_uuid: {}, pf_uuid: {}, assign_ids: {}) failed, error is ({})".format(
                            host_uuid, pf_uuid, assign_ids, str(e)
                        ),
                        py_error.SRIOV_VF_RELEASE_FAILED,
                    )
            except Exception as e:
                raise base.ResourceException(
                    "Release VF_PCIs (host_uuid: {}, pf_uuid: {}, assign_ids: {}) failed, error is ({})".format(
                        host_uuid, pf_uuid, assign_ids, str(e)
                    ),
                    py_error.SRIOV_VF_RELEASE_FAILED,
                )

    def allocate_vf_pci_device(self):
        pf_uuid_to_vfs = {}
        for vf_pci_device in self._vf_pci_devices:
            pf_uuid_to_vfs.setdefault(vf_pci_device.pf_uuid, []).append(self._gen_assign_id(vf_pci_device.uuid))

        assign_results_map = {}
        for pf_uuid, assign_ids in list(pf_uuid_to_vfs.items()):
            try:
                assign_results = self._tuna_client.allocate_sriov_pcis(self.host_uuid, pf_uuid, assign_ids)
            except Exception as e:
                raise base.ResourceException(
                    "Allocate VF_PCI on PCI({}) of host({}), assign_ids={} failed, error={}".format(
                        pf_uuid, self.host_uuid, assign_ids, str(e)
                    ),
                    py_error.SRIOV_VF_ASSIGN_FAILED,
                )

            for assign_result in assign_results:
                assign_results_map[assign_result["assign_id"]] = assign_result

        for vf_pci_device in self._vf_pci_devices:
            vf_pci_device.set_host_pci_address_by_bus_location(
                assign_results_map[self._gen_assign_id(vf_pci_device.uuid)]["bus_location"]
            )

        return copy.deepcopy(self._vf_pci_devices)

    def allocate_mock_vf_pci_device(self):
        for vf_pci_device in self._vf_pci_devices:
            vf_pci_device.host_pci_address = self.MOCK_PCI_ADDRESS

        return copy.deepcopy(self._vf_pci_devices)


def release_vf_pci_devices_on_start_failure(func):
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        try:
            return func(self, vm_json, *args, **kwargs)
        except Exception:
            VFHandler(vm_json).release_vf_pci_devices_silent()
            raise

    return wrapper


def allocate_vf_pci_devices_to_start(func):
    @functools.wraps(func)
    def wrapper(self, vm_json, *args, **kwargs):
        vf_pci_device_handler = VFHandler(vm_json)
        if vf_pci_device_handler.vm_has_vf_pci_device():
            if vm_json["status"] == elf_constants.VM_RUNNING:
                kwargs["vf_pci_devices"] = vf_pci_device_handler.allocate_vf_pci_device()
            else:
                kwargs["vf_pci_devices"] = vf_pci_device_handler.allocate_mock_vf_pci_device()

        try:
            return func(self, vm_json, *args, **kwargs)
        except Exception:
            if vm_start.need_release_device_on_exception_after_start(vm_json, kwargs["libvirt_conn"]):
                vf_pci_device_handler.release_vf_pci_devices_silent()
            raise

    return wrapper
