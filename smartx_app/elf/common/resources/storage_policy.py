# Copyright (c) 2013-2016, SMARTX
# All rights reserved.
import logging
import time

from pymongo import errors

from common.config.resources import RESOURCE_IN_USE
from common.mongo.db import mongodb
from smartx_app.elf.common import constants as ec_constants
from smartx_app.elf.common.config import platform
from smartx_app.elf.common.resources import storage_policy_instance
from smartx_app.elf.common.resources.base import ResourceException
from smartx_proto.errors import pyerror_pb2 as py_error


class StoragePolicy:
    """
    This class works as a storage policy instance factory. It is responsible for both querying existing storage
    policies and creating new storage policies.
    """

    DEFAULT_STORAGE_POLICY_UUID = "dce52578-3824-4b34-97a2-af09950bb0ac"
    DEFAULT_STORAGE_POLICY_NAME = "default"
    DEFAULT_WHITELIST = "*/*"

    DEFAULT_ISO_STORAGE_POLICY_UUID = "7fd5f5ce-841c-4942-9327-babaf6c91cb2"
    DEFAULT_ISO_STORAGE_POLICY_NAME = "default_iso"

    resources_db = mongodb.resources

    SP_INSTANCE_CONSTRUCTOR = {
        ec_constants.STORAGE_POLICY_TYPE_REPLICA: storage_policy_instance.ReplicaStoragePolicy,
        ec_constants.STORAGE_POLICY_TYPE_EC: storage_policy_instance.ECStoragePolicy,
    }

    @classmethod
    def initialize(cls):
        cls.ensure_index()
        cls.initialize_default_if_not_exist()

    @classmethod
    def ensure_index(cls):
        try:
            indexes = cls.resources_db.storage_policy.index_information()
        except errors.OperationFailure:
            indexes = {}

        if "uuid_1" not in indexes:
            cls.resources_db.storage_policy.create_index("uuid", background=True, unique=True)

    @classmethod
    def initialize_default_if_not_exist(cls):
        try:
            cls.load(cls.DEFAULT_STORAGE_POLICY_UUID)
        except ResourceException:
            logging.info("Initialize the default storage policy({}).".format(cls.DEFAULT_STORAGE_POLICY_UUID))
            storage_policy_instance.ReplicaStoragePolicy(
                cls.DEFAULT_STORAGE_POLICY_NAME,
                this_storage_policy_uuid=cls.DEFAULT_STORAGE_POLICY_UUID,
                description="default storage policy",
                stripe_num=ec_constants.DEFAULT_STRIPE_NUM,
                **cls.get_default_settings(),
            ).update_to_db()

        try:
            cls.load(cls.DEFAULT_ISO_STORAGE_POLICY_UUID)
        except ResourceException:
            logging.info("Initialize the default ISO storage policy({}).".format(cls.DEFAULT_ISO_STORAGE_POLICY_UUID))
            storage_policy_instance.ReplicaStoragePolicy(
                cls.DEFAULT_ISO_STORAGE_POLICY_NAME,
                this_storage_policy_uuid=cls.DEFAULT_ISO_STORAGE_POLICY_UUID,
                description="default storage policy for ISO",
                stripe_num=ec_constants.DEFAULT_STRIPE_NUM,
                storage_pool_id="system",
                replica_num=3,
                thin_provision=True,
                whitelist=cls.DEFAULT_WHITELIST,
            ).update_to_db()

    @classmethod
    def get_default_settings(cls):
        from smartx_app.elf.common.utils.zbs_meta import get_preferred_zone

        return {
            "storage_pool_id": "system",
            "replica_num": 3 if get_preferred_zone()[0] or platform.is_in_elf() else 2,
            "thin_provision": True,
            "whitelist": cls.DEFAULT_WHITELIST,
        }

    @classmethod
    def check_settings(cls, settings):
        if settings.type == ec_constants.STORAGE_POLICY_TYPE_REPLICA and settings.replica_num == 1:
            raise ResourceException(
                "The 1 replica_num storage policy is no longer supported",
                py_error.STORAGE_POLICY_NOT_MATCH,
            )

    @classmethod
    def _gen_auto_created_sp_name(
        cls, settings: storage_policy_instance.ReplicaSettings | storage_policy_instance.ECSettings
    ):  # pragma: no cover
        if settings.type == ec_constants.STORAGE_POLICY_TYPE_REPLICA:
            return "replica-{}-{}-stripe{}-{}-{}".format(
                settings.replica_num,
                "thin" if settings.thin_provision else "thick",
                settings.stripe_num,
                settings.stripe_size // 1024,
                int(time.time() * 1000),
            )
        elif settings.type == ec_constants.STORAGE_POLICY_TYPE_EC:
            return "ec-{}-{}-{}-stripe{}-{}-{}".format(
                settings.ec_k,
                settings.ec_m,
                "thin" if settings.thin_provision else "thick",
                settings.stripe_num,
                settings.stripe_size // 1024,
                int(time.time() * 1000),
            )

        raise ResourceException(
            "Invalid storage policy type: {}".format(settings.type), py_error.STORAGE_POLICY_INVALID_TYPE
        )

    @classmethod
    def _create_from_settings(
        cls, settings: storage_policy_instance.ReplicaSettings | storage_policy_instance.ECSettings, force_create=False
    ):
        cls.check_settings(settings)

        settings_in_dict = settings._asdict()
        if not force_create:
            matching_policies = cls.query(criteria=settings_in_dict)

            matching_policies = [
                policy for policy in matching_policies if policy.uuid != cls.DEFAULT_ISO_STORAGE_POLICY_UUID
            ]

            if matching_policies:
                return matching_policies[0]

        sr = cls._construct(
            name=cls._gen_auto_created_sp_name(settings),
            description="auto-created",
            sp_type=settings_in_dict.pop("type"),
            **settings_in_dict,
        )

        sr.update_to_db()
        return sr

    @classmethod
    def create_from_lun(cls, target_name, lun_id, force_create=False, storage_cluster_uuid=None):
        """
        Try to create a new storage policy that its settings come from
        an exist LUN.

        :param target_name:          The name of target of the exist LUN.
        :param lun_id:               ID of the exist LUN.
        :param force_create:         First try to get an exist matching storage
                                     policy if it is False, otherwise directly
                                     create a new one.
        :param storage_cluster_uuid: The uuid of storage cluster.
        :return:                     The new or matching storage policy object.
        """
        settings = storage_policy_instance.get_settings_from_lun(
            target_name, lun_id, storage_cluster_uuid=storage_cluster_uuid
        )
        return cls._create_from_settings(settings, force_create)

    @classmethod
    def create_from_export(cls, export_id, inode_id=None, force_create=False):
        """
        Try to create a new storage policy that its settings come from
        an exist export.

        :param export_id:       The exist export id.
        :param inode_id:        The exist inode id.
        :param force_create:    First try to get an exist matching storage
                                policy if it is False, otherwise directly
                                create a new one.
        :return:                The new or matching storage policy object.
        """
        settings = storage_policy_instance.get_settings_from_export(export_id, inode_id=inode_id)
        return cls._create_from_settings(settings, force_create)

    @classmethod
    def load(cls, storage_policy_uuid):
        sr_policy_list = cls.query({"uuid": storage_policy_uuid})
        if len(sr_policy_list) == 0:
            raise ResourceException(
                "The storage policy({}) is not exist.".format(storage_policy_uuid), py_error.STORAGE_POLICY_NOT_FOUND
            )

        return sr_policy_list[0]

    @classmethod
    def query(cls, criteria=None):
        query = {"resource_state": RESOURCE_IN_USE}
        if criteria:
            query.update(criteria)

        return [
            # initialize_default_if_not_exist() will call query() when job-center-worker is restarted. But at that time,
            # the 'type' field of storage policy may not be added by the one time job yet. The existing storage
            # policy may not have "type" field
            cls._construct(
                sr_policy.pop("name"),
                this_storage_policy_uuid=sr_policy.pop("uuid"),
                sp_type=sr_policy.pop("type", ec_constants.STORAGE_POLICY_TYPE_REPLICA),
                **sr_policy,
            )
            for sr_policy in cls.resources_db.storage_policy.find(query, {"_id": 0})
        ]

    @staticmethod
    def _get_check_fields(
        src_policy: storage_policy_instance.BaseStoragePolicy,
        dst_policy: storage_policy_instance.BaseStoragePolicy,
    ):
        check_fields = (
            "whitelist",
            "stripe_num",
            "stripe_size",
            "thin_provision",
            "type",
        )
        if src_policy.type == dst_policy.type == ec_constants.STORAGE_POLICY_TYPE_REPLICA:
            return (*check_fields, "replica_num", "read_only")

        if src_policy.type == dst_policy.type == ec_constants.STORAGE_POLICY_TYPE_EC:
            return (*check_fields, "ec_k", "ec_m")

        return check_fields

    @staticmethod
    def _validate_immutable_fields(fields, mutable_fields, src_policy_uuid, dst_policy_uuid):
        if extra := fields - mutable_fields:
            raise ResourceException(
                f"Fields {', '.join(extra)} are not among the mutable fields {', '.join(mutable_fields)}, "
                f"src_storage_policy: {src_policy_uuid}, dst_storage_policy: {dst_policy_uuid}",
                py_error.STORAGE_POLICY_UNMATCH_IMMUTABLE_FIELD,
            )

    @classmethod
    def _diff_fields(
        cls,
        src_policy: storage_policy_instance.BaseStoragePolicy,
        dst_policy: storage_policy_instance.BaseStoragePolicy,
    ) -> set[str]:
        """
        Returns:
            diff result: {"<diff field>"}
        """
        result = set()
        for field in cls._get_check_fields(src_policy, dst_policy):
            src_value = getattr(src_policy, field)
            dst_value = getattr(dst_policy, field)
            if src_value != dst_value:
                if field == "whitelist" and set(src_value.split(",")) == set(dst_value.split(",")):
                    continue

                result.add(field)
        return result

    @classmethod
    def verify_change_for_modification(cls, src_uuid, dst_uuid):
        src_policy: storage_policy_instance.BaseStoragePolicy = cls.load(src_uuid)
        dst_policy: storage_policy_instance.BaseStoragePolicy = cls.load(dst_uuid)

        if src_policy.type != dst_policy.type:
            raise ResourceException(
                "The resiliency type of src_uuid({}) is {}, but the one of dst_uuid({}) is {}".format(
                    src_uuid, src_policy.type, dst_uuid, dst_policy.type
                ),
                py_error.STORAGE_POLICY_RESILIENCY_TYPE_CANNOT_CHANGE,
            )

        diff_result = cls._diff_fields(src_policy, dst_policy)
        if not diff_result:
            raise ResourceException(
                "Cannot be modified to configure a consistent storage policies",
                py_error.STORAGE_POLICY_CONFIG_NOT_CHANGE,
            )

        # Currently, there are only two types of storage policy
        if src_policy.type == ec_constants.STORAGE_POLICY_TYPE_REPLICA:
            mutable_fields = {"replica_num", "thin_provision"}
        else:
            mutable_fields = {"thin_provision"}
        cls._validate_immutable_fields(diff_result, mutable_fields, src_policy.uuid, dst_policy.uuid)

        if "replica_num" in diff_result and src_policy.replica_num > dst_policy.replica_num:
            raise ResourceException(
                "The replica_num of the target storage policy({}) "
                "must be higher than the source storage policy({})".format(dst_uuid, src_uuid),
                py_error.STORAGE_POLICY_INVALID_REPLICA_UPGRADE,
            )

        if "thin_provision" in diff_result and src_policy.thin_provision is False and dst_policy.thin_provision is True:
            raise ResourceException(
                "The provision can only be changed from thin to thick",
                py_error.STORAGE_POLICY_INVALID_THIN_PROVISION_CHANGE,
            )

    @classmethod
    def verify_change_for_creation(cls, src_uuid, dst_uuid, is_full_copy):
        src_policy: storage_policy_instance.BaseStoragePolicy = cls.load(src_uuid)
        dst_policy: storage_policy_instance.BaseStoragePolicy = cls.load(dst_uuid)

        cls.check_settings(dst_policy.settings)

        # Currently, there are only two types of storage policy
        if src_policy.type == dst_policy.type == ec_constants.STORAGE_POLICY_TYPE_REPLICA:
            mutable_fields = {"replica_num", "thin_provision"}
        elif src_policy.type == dst_policy.type == ec_constants.STORAGE_POLICY_TYPE_EC:
            mutable_fields = {"ec_k", "ec_m", "thin_provision"}
        elif src_policy.type != dst_policy.type:
            mutable_fields = {"type", "thin_provision"}
        else:
            # Reserved for the new storage type
            mutable_fields = set()

        diff_result = cls._diff_fields(src_policy, dst_policy)
        cls._validate_immutable_fields(diff_result, mutable_fields, src_policy.uuid, dst_policy.uuid)

        if not is_full_copy:
            if not diff_result:
                raise ResourceException(
                    "The storage policy can be changed to another one with the same config only in full copy mode",
                    py_error.FULL_COPY_REQUIRED,
                )

            if "type" in diff_result:
                raise ResourceException(
                    "The type of storage policy can be changed only in full copy mode",
                    py_error.FULL_COPY_REQUIRED,
                )

            if diff_result == {"thin_provision"} and not src_policy.thin_provision and dst_policy.thin_provision:
                raise ResourceException(
                    "The thin_provision can be changed from thick to thin only in full copy mode",
                    py_error.FULL_COPY_REQUIRED,
                )

            if src_policy.type == dst_policy.type == ec_constants.STORAGE_POLICY_TYPE_REPLICA and diff_result & {
                "replica_num"
            }:
                raise ResourceException(
                    "The replica_num can be changed only in full copy mode",
                    py_error.FULL_COPY_REQUIRED,
                )

            if src_policy.type == dst_policy.type == ec_constants.STORAGE_POLICY_TYPE_EC and diff_result & {
                "ec_k",
                "ec_m",
            }:
                raise ResourceException(
                    "The ec_k & ec_m can be changed only in full copy mode",
                    py_error.FULL_COPY_REQUIRED,
                )

    @classmethod
    def verify_storage_policy_is_consistent(
        cls,
        src_policy: storage_policy_instance.BaseStoragePolicy,
        dst_policy: storage_policy_instance.BaseStoragePolicy,
    ):
        diff_result = cls._diff_fields(src_policy, dst_policy)

        # we ignore whitelist field
        diff_result.discard("whitelist")
        return len(diff_result) == 0

    @classmethod
    def get_storage_policy_name(cls, storage_policy_uuid):
        if storage_policy_uuid == cls.DEFAULT_STORAGE_POLICY_UUID:
            return cls.DEFAULT_STORAGE_POLICY_NAME
        else:
            return cls.load(storage_policy_uuid).name

    @classmethod
    def _construct(
        cls,
        name,
        storage_pool_id=None,
        description=None,
        this_storage_policy_uuid=None,
        created_time=None,
        replica_num=3,
        thin_provision=True,
        whitelist=DEFAULT_WHITELIST,
        read_only=False,
        stripe_num=ec_constants.DEFAULT_STRIPE_NUM,
        stripe_size=256 * 1024,
        datastores=None,
        resource_state=None,
        modified_time=None,
        ts=None,
        sp_type=ec_constants.STORAGE_POLICY_TYPE_REPLICA,
        ec_k=None,
        ec_m=None,
    ) -> storage_policy_instance.BaseStoragePolicy:
        return cls.SP_INSTANCE_CONSTRUCTOR[sp_type](
            name,
            storage_pool_id=storage_pool_id,
            description=description,
            this_storage_policy_uuid=this_storage_policy_uuid,
            created_time=created_time,
            replica_num=replica_num,
            thin_provision=thin_provision,
            whitelist=whitelist,
            read_only=read_only,
            stripe_num=stripe_num,
            stripe_size=stripe_size,
            datastores=datastores,
            resource_state=resource_state,
            modified_time=modified_time,
            ts=ts,
            ec_k=ec_k,
            ec_m=ec_m,
        )

    @classmethod
    def new(
        cls,
        name,
        storage_pool_id=None,
        description=None,
        this_storage_policy_uuid=None,
        created_time=None,
        replica_num=3,
        thin_provision=True,
        whitelist=DEFAULT_WHITELIST,
        read_only=False,
        stripe_num=ec_constants.DEFAULT_STRIPE_NUM,
        stripe_size=256 * 1024,
        datastores=None,
        resource_state=None,
        modified_time=None,
        ts=None,
        sp_type=ec_constants.STORAGE_POLICY_TYPE_REPLICA,
        ec_k=None,
        ec_m=None,
    ) -> storage_policy_instance.BaseStoragePolicy:
        return cls._construct(
            name,
            storage_pool_id=storage_pool_id,
            description=description,
            this_storage_policy_uuid=this_storage_policy_uuid,
            created_time=created_time,
            replica_num=replica_num,
            thin_provision=thin_provision,
            whitelist=whitelist,
            read_only=read_only,
            stripe_num=stripe_num,
            stripe_size=stripe_size,
            datastores=datastores,
            resource_state=resource_state,
            modified_time=modified_time,
            ts=ts,
            sp_type=sp_type,
            ec_k=ec_k,
            ec_m=ec_m,
        )

    @classmethod
    def from_dict(cls, sp_dict) -> storage_policy_instance.BaseStoragePolicy:
        return cls._construct(
            name=sp_dict.get("name"),
            storage_pool_id=sp_dict.get("storage_pool_id"),
            description=sp_dict.get("description"),
            this_storage_policy_uuid=sp_dict.get("this_storage_policy_uuid"),
            created_time=sp_dict.get("created_time"),
            replica_num=sp_dict.get("replica_num", 3),
            thin_provision=sp_dict.get("thin_provision", True),
            whitelist=sp_dict.get("whitelist", cls.DEFAULT_WHITELIST),
            read_only=sp_dict.get("read_only", False),
            stripe_num=sp_dict.get("stripe_num", ec_constants.DEFAULT_STRIPE_NUM),
            stripe_size=sp_dict.get("stripe_size", 256 * 1024),
            datastores=sp_dict.get("datastores"),
            resource_state=sp_dict.get("resource_state"),
            modified_time=sp_dict.get("modified_time"),
            ts=sp_dict.get("ts"),
            sp_type=sp_dict.get("sp_type", ec_constants.STORAGE_POLICY_TYPE_REPLICA),
            ec_k=sp_dict.get("ec_k"),
            ec_m=sp_dict.get("ec_m"),
        )
