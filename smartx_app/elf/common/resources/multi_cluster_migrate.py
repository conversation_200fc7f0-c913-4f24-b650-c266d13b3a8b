# Copyright (c) 2013-2018, SMARTX
# All rights reserved.
import copy
import logging
import time
import uuid

from pymongo import errors
import xmltodict

from common.mongo.db import mongodb
from job_center.config import CURRENT_NODE_IP, RESOURCES_DB_NAME
from smartx_app.elf.common import constants as elf_common_constants
from smartx_app.elf.common.constants import (
    DEFAULT_CPU_EXCLUSIVE_JSON,
    DEFAULT_CPU_QOS_JSON,
    HA_PRIORITY_DEFAULT,
    MIGRATE_DEST,
    MIGRATE_SOURCE,
    NIC_TYPE_VLAN,
    RESOURCE_HIDDEN,
    RESOURCE_IN_USE,
    RESOURCE_REMOVED,
    VM_DELETED,
    VM_MIGRATED,
    VM_MIGRATED_PHF,
    VM_MIGRATING,
    VM_RUNNING,
)
from smartx_app.elf.common.resource_wrappers import vm_additional_info_manager
from smartx_app.elf.common.resources import base as elf_resource_base
from smartx_app.elf.common.utils import libvirt_driver
from smartx_app.elf.common.utils.upgrade_related import vm as vm_upgrade_related
from smartx_app.elf.job_center import constants as jc_constants
import smartx_app.elf.job_center.lib.converter.domain as domain_lib
from smartx_proto.errors import pyerror_pb2 as py_error


class MultiClusterMigrateInfo:
    collection = mongodb[RESOURCES_DB_NAME].multi_cluster_migrate_info
    resource_collection = mongodb[RESOURCES_DB_NAME].resource
    job_collection = mongodb.jobs.job
    _version = 1

    def __init__(
        self,
        id=None,  # noqa: A002
        vm_uuid=None,
        belong=None,
        migrate_state=None,
        current_node_ip=None,
        migrate_process=None,
        vm_json=None,
        migrate_time_elapsed=None,
        job_id=None,
        pre_handle_time=None,
        post_handlers=None,
        lun2volume=None,
        migrate_ip=None,
        remote_token=None,
        libvirt_transport=None,
    ):
        self.id = id or str(uuid.uuid4())
        self.vm_uuid = vm_uuid
        self.belong = belong
        self.migrate_state = migrate_state
        self.current_node_ip = current_node_ip
        self.migrate_process = migrate_process
        self.vm_json = vm_json
        self.migrate_time_elapsed = migrate_time_elapsed
        self.job_id = job_id
        self.pre_handle_time = pre_handle_time
        self.post_handlers = post_handlers
        self.lun2volume = lun2volume
        self.version = self._version
        self.migrate_ip = migrate_ip
        self.remote_token = remote_token
        self.libvirt_transport = libvirt_transport

    @classmethod
    def _get_version_compatible_filter(cls):
        return {"$in": [None, cls._version]}

    def dumps(self):
        data = copy.deepcopy(self.__dict__)
        for key in list(data.keys()):
            if not data.get(key):
                del data[key]
        return data

    def update(self):
        migrate_info = self.dumps()
        migrated_id = migrate_info.pop("id")
        ctime = int(time.time())
        update_query = {
            "$set": migrate_info,
            "$setOnInsert": {
                "id": migrated_id,
                "ctime": ctime,
                "heartbeat_time": ctime,
            },
        }
        self.collection.update(
            {
                "vm_uuid": migrate_info["vm_uuid"],
                "migrate_state": VM_MIGRATING,
                "version": self._get_version_compatible_filter(),
            },
            update_query,
            upsert=True,
        )

    @classmethod
    def bulk_update(cls, migrate_infos):
        if migrate_infos:
            bulk = cls.collection.initialize_unordered_bulk_op()
            for migrate_exist in migrate_infos:
                bulk.find({"id": migrate_exist["id"], "vm_uuid": migrate_exist["vm_uuid"]}).update_one(
                    {"$set": migrate_exist}
                )
            bulk.execute()

    @classmethod
    def query_vm_by_belong(cls, current_node_ip, belong):
        query = {
            "belong": belong,
            "migrate_state": VM_MIGRATING,
            "current_node_ip": current_node_ip,
            "version": cls._get_version_compatible_filter(),
        }

        return list(cls.collection.find(query, {"_id": 0}))

    @classmethod
    def query_incoming_vm_ids_by_transport(cls, current_node_ip, transport):
        query = {
            "belong": MIGRATE_DEST,
            "migrate_state": VM_MIGRATING,
            "current_node_ip": current_node_ip,
            "libvirt_transport": transport,
            "version": cls._get_version_compatible_filter(),
        }

        return list(cls.collection.find(query, {"_id": 0, "vm_uuid": 1}))

    @classmethod
    def insert_vm_resource(cls, vm_json, xml=None) -> bool:
        """
        ret: whether vm insertion is done or not, for now, failure case
        is only on concurrent upsert. Other types of failure would directly
        throw the exception and interrupt current process.
        """
        vm_json["node_ip"] = CURRENT_NODE_IP
        cls.sync_device_info(vm_json)
        inserted = cls.insert_vm_additional_info(vm_json["uuid"], xml=xml, cloud_init=vm_json.get("cloud_init"))
        if not inserted:
            return False

        vm_json.pop("cloud_init", None)
        try:
            cls.resource_collection.update({"uuid": vm_json["uuid"]}, vm_json, upsert=True)
        except errors.DuplicateKeyError as e:
            logging.warning(
                "[AcrossClusterLiveMigration]Skip inserting duplicate vm[{}]: {}".format(vm_json["uuid"], str(e))
            )
            return False

        return True

    @classmethod
    def insert_vm_additional_info(cls, vm_uuid, xml=None, cloud_init=None):
        if not xml:
            with libvirt_driver.libvirt_connection() as conn:
                domain = conn.lookupByName(vm_uuid)
                xml = domain.XMLDesc(0)
        libvirt_json = xmltodict.parse(xml)
        domain_uuid = libvirt_json["domain"]["uuid"]

        has_customized_smbios = False
        if libvirt_json["domain"]["os"].get("smbios", {}).get("@mode") == "sysinfo":
            has_customized_smbios = True

        vm_additional_info = {"domain_uuid": domain_uuid, "has_customized_smbios": has_customized_smbios}
        if cloud_init:
            vm_additional_info["cloud_init"] = cloud_init

        if vm_upgrade_related.is_domain_use_amd_vendor(libvirt_json):
            vm_additional_info[elf_common_constants.VAI_IS_AMD_VENDOR_FOR_HYGON_CPU] = True

        try:
            vm_additional_info_manager.VMAdditionalInfoWrapper.add_multiple(vm_uuid, vm_additional_info)
        except errors.DuplicateKeyError as e:
            logging.warning(
                "[AcrossClusterLiveMigration]Skip inserting duplicate vm_additional_info[{}]: {}".format(
                    vm_uuid, str(e)
                )
            )
            return False

        return True

    @classmethod
    def batch_query_by_migrate_id(cls, migrate_ids):
        query = {
            "id": {"$in": migrate_ids},
            "version": cls._get_version_compatible_filter(),
        }
        return list(cls.collection.find(query, {"_id": 0}))

    @classmethod
    def update_resource_removed(cls, resource_uuid):
        cls.resource_collection.update(
            {"uuid": resource_uuid}, {"$set": {"status": "deleted", "resource_state": RESOURCE_REMOVED}}
        )

    @classmethod
    def query_by_vm_uuid(cls, vm_uuid):
        query = {"vm_uuid": vm_uuid, "migrate_state": VM_MIGRATING, "version": cls._get_version_compatible_filter()}
        return cls.collection.find_one(query, {"_id": 0})

    @classmethod
    def batch_query_by_uuid(cls, vm_uuids):
        query = {
            "vm_uuid": {"$in": vm_uuids},
            "migrate_state": VM_MIGRATING,
            "version": cls._get_version_compatible_filter(),
        }
        return list(cls.collection.find(query, {"_id": 0}))

    @staticmethod
    def sync_device_info(vm_json):
        """
        Sync them to vm json to domain's data first.
        Some vms from older cluster may not have some fields in vm json that the new
        cluster expect to have.
        """
        with libvirt_driver.libvirt_connection() as conn:
            vm_json["nics"] = domain_lib.get_synced_nics_json(conn.lookupByName(vm_json["uuid"]).XMLDesc(0), vm_json)

    @staticmethod
    def make_fields_compatible(vm_json, disk_path_to_volume_uuid: dict[str:str]):
        # handle vm_json to make vm_json compatible in the dst cluster

        # 'cpu_exclusive' won't be kept when migrate across cluster.
        # So set 'cpu_exclusive' field to DEFAULT_CPU_EXCLUSIVE_JSON
        vm_json["cpu_exclusive"] = DEFAULT_CPU_EXCLUSIVE_JSON

        # After migrate across cluster, the cpu_qos should be set to default value
        # of the dst cluster
        vm_json["cpu_qos"] = DEFAULT_CPU_QOS_JSON

        # After migrate across cluster, the ha_priority should be set to default value
        # of the dst cluster
        vm_json["ha_priority"] = HA_PRIORITY_DEFAULT

        # VMs from cluster lower than SMTX OS 6.0.0 do not have 'type' in 'nic'. The type should be set to
        # NIC_TYPE_VLAN.
        for n in vm_json.get("nics", []):
            if "type" not in n:
                n["type"] = NIC_TYPE_VLAN

        # If the VM (version is lower than SMTX OS 6.0.0) does not have the boot_with_host configuration,
        # it will be set to the default value after migrate across cluster.
        vm_json["boot_with_host"] = vm_json.get("boot_with_host", False)

        # The quota_policy field may be missing in the `vm` or `vm.disk`.
        for disk in vm_json.get("disks", []):
            # The default quota_policy of cdrom is None, too.
            if "quota_policy" not in disk:
                disk["quota_policy"] = None
        if "quota_policy" not in vm_json:
            vm_json["quota_policy"] = None

        disks = [d for d in vm_json.get("disks", []) if d["type"] == "disk"]
        # 'volume_uuid' is in vm_json.disks since 6.0.0.
        if any("volume_uuid" not in d for d in disks):
            for d in disks:
                # The volume is not locked during migration, so it might be unexpectedly deleted.
                if d["path"] not in disk_path_to_volume_uuid:
                    logging.error(
                        "[AcrossClusterLiveMigration] Cannot find disk with path({}) for VM({})".format(
                            d["path"], vm_json["uuid"]
                        )
                    )
                    continue

                d["volume_uuid"] = disk_path_to_volume_uuid[d["path"]]

        # since 6.1.0, the cpu_model of the VM is automatically changed from skylake to the corresponding noMPX version
        if vm_json["cpu_model"] in jc_constants.INTEL_HIDDEN_CPU_MODELS:
            vm_json["cpu_model"] = jc_constants.SKYLAKE_AND_CORRESPONDING_NO_MPX_MODELS[vm_json["cpu_model"]]

        # For the VMs from the cluster lower than 6.0.0,
        # the vm_version and vm_version_mode should be set to the default value after migrate across cluster.
        if not vm_json.get("vm_version"):
            vm_json["vm_version"] = elf_common_constants.VM_VERSION_LEGACY
        if not vm_json.get("vm_version_mode"):
            vm_json["vm_version_mode"] = elf_common_constants.VM_VERSION_MODE_AUTO

        # VMs from cluster lower than 6.3.0 do not have `migratable`
        vm_json["migratable"] = True

        # VMs from cluster lower than 6.3.0 do not have `anti_malware`
        vm_json["anti_malware"] = elf_common_constants.DEFAULT_ANTI_MALWARE

        # If local_ha_policy is not in the supported range, it is set to the default value.
        if vm_json.get("local_ha_policy") not in elf_common_constants.SUPPORTED_LOCAL_HA_POLICY:
            vm_json["local_ha_policy"] = elf_common_constants.LOCAL_HA_POLICY_DEFAULT

    @classmethod
    def get_post_handlers_if_exists(cls, vm_uuid=None, belong=MIGRATE_DEST):
        query_condition = {
            "migrate_state": VM_MIGRATED,
            "belong": belong,
            "post_handlers": {"$exists": True},
            "$where": "this.post_handlers.length>0",
            "version": cls._get_version_compatible_filter(),
        }
        if vm_uuid is not None:
            query_condition.update({"vm_uuid": vm_uuid})
        project_filter = {"_id": 0, "vm_uuid": 1, "post_handlers": 1}

        result = {}
        for x in list(cls.collection.find(query_condition, project_filter)):
            result.setdefault(x["vm_uuid"], set()).update(x["post_handlers"])

        return result

    @classmethod
    def get_post_handlers(cls, vm_uuid):
        handlers = cls.get_post_handlers_if_exists(vm_uuid)
        return handlers[vm_uuid] if handlers else set()

    @classmethod
    def query_post_handle_failed_vms(cls, vm_uuids):
        if not vm_uuids:
            return []

        # A vm_uuid may have multiple records. Use the latest record. If the migrate_state of the latest record is not
        # VM_MIGRATED_PHF, no need to handle this vm_uuid.
        post_handle_failed_vms = []
        latest_migrate_infos = list(
            cls.collection.aggregate(
                [
                    {
                        "$match": {
                            "belong": MIGRATE_SOURCE,
                            "vm_uuid": {"$in": vm_uuids},
                            "version": cls._get_version_compatible_filter(),
                        }
                    },
                    {"$sort": {"vm_uuid": 1, "ctime": -1}},
                    {"$group": {"_id": "$vm_uuid", "migrate_state": {"$first": "$migrate_state"}}},
                ]
            )
        )
        for x in latest_migrate_infos:
            if x["migrate_state"] == VM_MIGRATED_PHF:
                post_handle_failed_vms.append(x["_id"])
        return post_handle_failed_vms

    @classmethod
    def remove_post_handler(cls, vm_uuid, handler, belong=MIGRATE_DEST):
        cls.collection.update(
            {
                "vm_uuid": vm_uuid,
                "migrate_state": VM_MIGRATED,
                "belong": belong,
                "version": cls._get_version_compatible_filter(),
            },
            {"$pull": {"post_handlers": handler}},
            multi=True,
        )

    @classmethod
    def update_heartbeat(cls, vm_uuid):
        cls.collection.update(
            {"vm_uuid": vm_uuid, "migrate_state": VM_MIGRATING, "belong": MIGRATE_DEST},
            {"$set": {"heartbeat_time": int(time.time())}},
        )

    @staticmethod
    def add_version_to_migrating_info():
        """
        This static method updates the migrate_info documents that are in the migrating state
        and do not have a version field. It sets the version field to 1.
        """
        # Fetch VMs in the migrating state without a version field
        migrating_infos = list(
            MultiClusterMigrateInfo.collection.find(
                {"migrate_state": VM_MIGRATING, "version": {"$exists": False}}, {"_id": 1, "vm_uuid": 1}
            )
        )

        if not migrating_infos:
            logging.info("No migrate_info documents need updating.")
            return

        migrate_info_ids = [info["_id"] for info in migrating_infos]
        # Perform the update to set version=1 where it is missing
        result = MultiClusterMigrateInfo.collection.update_many(
            {"_id": {"$in": migrate_info_ids}}, {"$set": {"version": 1}}
        )
        logging.info(f"Updated {result.modified_count} migrate_info documents with version=1")
        for info in migrating_infos:
            logging.info(f"Updated migrate_info for VM({info['vm_uuid']}) with version=1")


class MultiClusterMigrateInfoV2(MultiClusterMigrateInfo):
    _version = 2
    MIGRATE_STATE_PREPARING = "preparing"
    MIGRATE_STATE_MIGRATING = "migrating"
    MIGRATE_STATE_POSTING = "posting"
    MIGRATE_STATE_FAILED = "failed"
    MIGRATE_STATE_MIGRATED = "migrated"

    def __init__(
        self,
        id=None,  # noqa: A002
        vm_uuid=None,
        belong=None,
        migrate_state=None,
        current_node_ip=None,
        migrate_process=None,
        vm_json=None,
        migrate_time_elapsed=None,
        job_id=None,
        pre_handle_time=None,
        post_handlers=None,
        lun2volume=None,
        migrate_ip=None,
        remote_token=None,
        context=None,
    ):
        super().__init__(
            id,
            vm_uuid,
            belong,
            migrate_state,
            current_node_ip,
            migrate_process,
            vm_json,
            migrate_time_elapsed,
            job_id,
            pre_handle_time,
            post_handlers,
            lun2volume,
            migrate_ip,
            remote_token,
        )

        self.context = context

    @classmethod
    def _get_version_compatible_filter(cls):
        return cls._version

    @staticmethod
    def make_fields_compatible_at_dest(vm_json, disk_path_to_volume_uuid):
        return MultiClusterMigrateInfo.make_fields_compatible(vm_json, disk_path_to_volume_uuid)

    @classmethod
    def switchover_mirror_vm(cls, mirror_vm, current_vm_status=VM_MIGRATING, switched_vm_status=VM_RUNNING):
        """
        This method is used to switch the meta of the mirror VM to the normal VM.
        It replaces the method `insert_vm_resource` in v1
        """
        vm_uuid = mirror_vm["uuid"]
        with libvirt_driver.libvirt_connection() as conn:
            domain = conn.lookupByName(vm_uuid)
            xml = domain.XMLDesc(0)

        vm_switched = {
            "status": switched_vm_status,
            "resource_state": RESOURCE_IN_USE,
            # From sync_device_info in v1
            "nics": domain_lib.get_synced_nics_json(xml, mirror_vm),
        }
        vm_additional_info = cls._gen_switchover_additional_info(domain_xml=xml)

        with mongodb.start_session() as db_session:
            with db_session.start_transaction():
                result_update_vm = cls.resource_collection.update_one(
                    {"uuid": vm_uuid, "resource_state": RESOURCE_HIDDEN, "status": current_vm_status},
                    {"$set": vm_switched},
                    session=db_session,
                )
                if result_update_vm.modified_count == 0:
                    raise elf_resource_base.ResourceException(
                        "The mirror VM {} is switching".format(vm_uuid), py_error.MIRROR_VM_INVALID_OPERATION_SWITCHING
                    )

                cls.resource_collection.update_many(
                    {
                        "uuid": {"$in": [disk["volume_uuid"] for disk in mirror_vm["disks"] if disk["type"] == "disk"]},
                        "resource_state": RESOURCE_HIDDEN,
                    },
                    {"$set": {"resource_state": RESOURCE_IN_USE}},
                    session=db_session,
                )

                vm_additional_info_manager.VMAdditionalInfoWrapper.add_multiple(
                    vm_uuid=vm_uuid,
                    kv_dict=vm_additional_info,
                    db_session=db_session,
                )
                vm_additional_info_manager.VMAdditionalInfoWrapper.remove(
                    vm_uuid=vm_uuid, f_name="mirror_vm", db_session=db_session
                )

        mirror_vm.update(vm_switched)

    @classmethod
    def _gen_switchover_additional_info(cls, domain_xml):
        """
        It replaces the method `insert_vm_additional_info` in v1
        """
        libvirt_json = xmltodict.parse(domain_xml)
        domain_uuid = libvirt_json["domain"]["uuid"]

        has_customized_smbios = False
        if libvirt_json["domain"]["os"].get("smbios", {}).get("@mode") == "sysinfo":
            has_customized_smbios = True

        vm_additional_info = {
            "domain_uuid": domain_uuid,
            "has_customized_smbios": has_customized_smbios,
        }

        if vm_upgrade_related.is_domain_use_amd_vendor(libvirt_json):
            vm_additional_info[elf_common_constants.VAI_IS_AMD_VENDOR_FOR_HYGON_CPU] = True

        return vm_additional_info

    @classmethod
    def query_by_job(cls, job_id):
        return cls.collection.find_one({"job_id": job_id, "version": cls._version}, {"_id": 0})

    @classmethod
    def create_migrate_info(cls, job_id, migrate_info):
        migrate_info_from_db = cls.query_by_job(job_id)
        if migrate_info_from_db:
            logging.warning(
                "The migrate info already exists in the db: job_id={}, vm_uuid={}, migrate_info={}".format(
                    job_id, migrate_info["vm_uuid"], migrate_info
                )
            )
            return

        migrate_info.update({"job_id": job_id})
        MultiClusterMigrateInfoV2(**migrate_info).update()

    @classmethod
    def batch_update_resource_removed(cls, resource_uuids):
        cls.resource_collection.update_many(
            {"uuid": {"$in": resource_uuids}},
            {"$set": {"status": VM_DELETED, "resource_state": RESOURCE_REMOVED}},
        )

    @classmethod
    def update_migrate_info(cls, job_id, db_session=None, **kwargs):
        allowed_fields = (
            "migrate_state",
            "next_migrate_state",
            "mirror_vm",
            "migrate_ip",
            "libvirt_access_info",
            "detect_zeroes",
            "enable_tls",
        )
        for key in list(kwargs.keys()):
            if key not in allowed_fields:
                raise ValueError("Forbid to update field: {}".format(key))

        cls.collection.update_one({"job_id": job_id}, {"$set": kwargs}, session=db_session)

    @classmethod
    def list_incomplete_migrations(cls, node_ips):
        middle_migrate_state = [cls.MIGRATE_STATE_PREPARING, cls.MIGRATE_STATE_MIGRATING, cls.MIGRATE_STATE_POSTING]
        return list(
            cls.collection.find(
                {
                    "migrate_state": {"$in": middle_migrate_state},
                    "version": cls._version,
                    "current_node_ip": {"$in": node_ips},
                    "belong": MIGRATE_SOURCE,
                },
                {"_id": 0},
            )
        )

    @classmethod
    def finalize_unhandled_migration(cls, vm_uuid, job_id, migrate_state, vm_status=None):
        with mongodb.start_session() as session:
            with session.start_transaction():
                cls.collection.update_one(
                    {"vm_uuid": vm_uuid, "job_id": job_id},
                    {"$set": {"migrate_state": migrate_state, "next_migrate_state": ""}},
                    session=session,
                )

                if vm_status:
                    cls.resource_collection.update_one(
                        {"uuid": vm_uuid, "status": VM_MIGRATING},
                        {"$set": {"status": vm_status}},
                        session=session,
                    )

    ##################################################
    # Manage the resource of the mirror VM
    @classmethod
    def delete_mirror_vm(cls, vm_uuid):
        with mongodb.start_session() as db_session:
            with db_session.start_transaction():
                cls.resource_collection.delete_one(
                    {"uuid": vm_uuid, "resource_state": RESOURCE_HIDDEN, "status": VM_MIGRATING},
                    session=db_session,
                )
                vm_additional_info_manager.VMAdditionalInfoWrapper.remove(vm_uuid=vm_uuid, db_session=db_session)
