# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
import uuid

from common.config.resources import RESOURCE_IN_USE, RESOURCE_REMOVED
from common.mongo.db import mongodb
from smartx_app.elf.common.constants import (
    DEFAULT_CPU_EXCLUSIVE_JSON,
    DEFAULT_CPU_QOS_JSON,
    HA_PRIORITY_DEFAULT,
    LOCAL_HA_POLICY_DEFAULT,
    NIC_TYPE_VLAN,
    TEMPLATE_CREATED,
    TEMPLATE_DELETED,
    VM_CLOCK_OFFSET_UTC,
    VM_FIRMWARE_BIOS,
    VM_VERSION_LATEST,
    VM_VERSION_MODE_AUTO,
    GuestOSType,
)
from smartx_app.elf.common.resources import storage_policy
from smartx_app.elf.common.resources.base import ResourceBase, ResourceException
from smartx_app.elf.common.utils import network
from smartx_app.elf.common.utils.internal_product import asiainfo
from smartx_app.elf.job_center.constants import (
    CDRO<PERSON>,
    CLUSTER_DEFAULT_CPU_MODEL,
    DISK,
    ISO_IMAGE,
    KVM_VM,
    KVM_VM_TEMPLATE,
)
from smartx_app.elf.job_center.lib.converter import storage
from smartx_proto.errors import pyerror_pb2 as py_error


class VMTemplate(ResourceBase):
    resources_db = mongodb.resources
    type = KVM_VM_TEMPLATE

    def __init__(
        self,
        name,
        description,
        template_uuid=None,
        ha=False,
        vcpu=1,
        cpu=None,
        memory=1 << 30,
        nics=None,
        disks=None,
        status=TEMPLATE_CREATED,
        resource_state=RESOURCE_IN_USE,
        create_time=None,
        nested_virtualization=None,
        cpu_model=CLUSTER_DEFAULT_CPU_MODEL,
        firmware=VM_FIRMWARE_BIOS,
        quota_policy=None,
        clock_offset=VM_CLOCK_OFFSET_UTC,
        video_type=None,
        win_opt=False,
        cloud_init_supported=False,
        guest_os_type=None,
        sync_vm_time_on_resume=False,
        unique_size=0,
        cpu_exclusive=None,
        local_ha_policy=LOCAL_HA_POLICY_DEFAULT,
    ):
        self.uuid = template_uuid or str(uuid.uuid4())
        self.name = name
        self.description = description
        self.ha = ha
        self.vcpu = vcpu
        self.cpu = cpu
        self.memory = memory

        # Set default value for disks
        self.disks = disks or []
        for disk in self.disks:
            if "quota_policy" not in disk:
                disk["quota_policy"] = None

        # Keep compatible if caller does not pass 'type' in nics
        if nics:
            for n in nics:
                if "type" not in n:
                    n["type"] = NIC_TYPE_VLAN
        self.nics = nics

        self.resource_state = resource_state
        self.create_time = create_time
        self.status = status
        self.nested_virtualization = nested_virtualization
        self.cpu_model = cpu_model
        self.firmware = firmware
        self.quota_policy = quota_policy
        self.clock_offset = clock_offset
        self.video_type = video_type
        self.win_opt = win_opt
        self.cloud_init_supported = cloud_init_supported
        self.guest_os_type = guest_os_type or GuestOSType.UNKNOWN
        self.sync_vm_time_on_resume = sync_vm_time_on_resume
        self.unique_size = unique_size
        self.cpu_exclusive = cpu_exclusive or DEFAULT_CPU_EXCLUSIVE_JSON
        self.local_ha_policy = local_ha_policy

    @classmethod
    def load_template(cls, template_uuid):
        template = cls.resources_db.resource.find_one(
            {"uuid": template_uuid, "type": KVM_VM_TEMPLATE, "resource_state": RESOURCE_IN_USE}, {"_id": 0}
        )
        if not template:
            raise ResourceException("VM template not found", py_error.VM_TEMPLATE_NOT_FOUND)

        default_cpu_topo = {"topology": {"cores": 1, "sockets": template["vcpu"]}}
        return cls(
            name=template.get("name"),
            description=template.get("description"),
            template_uuid=template.get("uuid"),
            ha=template.get("ha"),
            vcpu=template.get("vcpu"),
            cpu=template.get("cpu", default_cpu_topo),
            memory=template.get("memory"),
            resource_state=template.get("resource_state"),
            create_time=template.get("create_time"),
            nics=template.get("nics"),
            disks=template.get("disks"),
            status=template.get("status"),
            nested_virtualization=template.get("nested_virtualization"),
            cpu_model=template.get("cpu_model"),
            firmware=template.get("firmware", VM_FIRMWARE_BIOS),
            quota_policy=template.get("quota_policy"),
            clock_offset=template.get("clock_offset", VM_CLOCK_OFFSET_UTC),
            video_type=template.get("video_type"),
            win_opt=template.get("win_opt", False),
            cloud_init_supported=template.get("cloud_init_supported", False),
            guest_os_type=template.get("guest_os_type", GuestOSType.UNKNOWN),
            sync_vm_time_on_resume=template.get("sync_vm_time_on_resume", False),
            unique_size=template.get("unique_size", 0),
            cpu_exclusive=template.get("cpu_exclusive"),
            local_ha_policy=template.get("local_ha_policy", LOCAL_HA_POLICY_DEFAULT),
        )

    def delete(self):
        template = self.dumps()
        template["status"] = TEMPLATE_DELETED
        template["resource_state"] = RESOURCE_REMOVED
        return template

    def set_volume_template_uuid(self, path, volume_template_uuid, export_name):
        """invoke when create vm template from vm"""
        for disk in self.disks:
            if disk["path"] == path:
                disk["volume_template_uuid"] = volume_template_uuid
                disk["export_name"] = export_name
                disk["size"] = "@{%s/size}" % volume_template_uuid
                disk["diff_size"] = "@{%s/diff_size}" % volume_template_uuid

    def create_vm(self, new_vm_attr, volume_template_and_volume_map, svt_images=None):
        vm = self.dumps()
        vm["type"] = KVM_VM

        update_disks = {}
        update_cdroms = {}
        delete_disks = []
        for disk in new_vm_attr.get("modify_disks", []):
            if disk["type"] == DISK:
                update_disks[disk["volume_template_uuid"]] = disk
            else:
                # Compatible with cases where disabled is not passed
                update_cdroms[disk["key"]] = (disk["path"], disk.get("disabled", None))
        for disk in new_vm_attr.get("delete_disks", []):
            if disk["type"] == DISK:
                delete_disks.append(disk["path"])
            else:
                delete_disks.append(disk["key"])

        disks = []
        boot = 1
        for _, disk in enumerate(vm["disks"]):
            if disk["type"] == CDROM:
                if disk["key"] not in delete_disks:
                    path, disabled = update_cdroms.get(disk["key"], (disk["path"], None))
                    if path:
                        image = self.resources_db.resource.find_one(
                            {"resource_state": RESOURCE_IN_USE, "path": path, "type": ISO_IMAGE}
                        )
                        if not image and svt_images:
                            matched_images = [x for x in svt_images if x["path"] == path]
                            if matched_images:
                                image = matched_images[0]
                        if not image:
                            path = ""
                    disk["path"] = path
                    disk["boot"] = boot
                    if disabled is not None:
                        disk["disabled"] = disabled
                    else:
                        # Default False
                        disk["disabled"] = disk.get("disabled", False)
                    boot += 1
                    disks.append(disk)
            else:
                if disk["path"] not in delete_disks:
                    volume_template_uuid = disk.pop("volume_template_uuid", None)
                    expected_vol = volume_template_and_volume_map[volume_template_uuid]
                    disk.pop("export_name", None)

                    # 1. `size` and `diff_size` are not necessary parameters
                    #    in vm json.
                    # 2. now, `size` corresponds to `size_in_byte` in the
                    #    iscsi volume model, it will cause confusion in vm,
                    #    so pop it to compatible with previous data.
                    disk.pop("size", None)
                    disk.pop("diff_size", None)
                    disk["volume_uuid"] = expected_vol["uuid"]
                    disk["serial"] = expected_vol["serial"]
                    disk["path"] = "@{%s/path}" % expected_vol["uuid"]

                    update_disk = update_disks.get(volume_template_uuid)
                    if update_disk:
                        disk["bus"] = update_disk["bus"]
                        if "quota_policy" in update_disk:
                            disk["quota_policy"] = update_disk["quota_policy"]

                    # validator will verify that the full copy required
                    if expected_vol.get("storage_policy_uuid"):
                        disk["storage_policy_uuid"] = expected_vol["storage_policy_uuid"]
                    else:
                        # NFS Vol does not have a storage_policy_uuid field
                        # but the VM disk needs a storage_policy_uuid field
                        # so set it to DEFAULT_STORAGE_POLICY_UUID for NFS Vol
                        disk["storage_policy_uuid"] = storage_policy.StoragePolicy.DEFAULT_STORAGE_POLICY_UUID

                    disk["boot"] = boot
                    boot += 1
                    disks.append(disk)

        vm["disks"] = disks

        # For backward compatibility
        for disk in new_vm_attr.get("new_disks", new_vm_attr.get("disks", [])):
            if disk["type"] == CDROM:
                # `key` are already ensured in submit_create_vms_preview
                # `path` is a schema required
                vm["disks"].append(
                    storage.construct_vm_cdrom(
                        boot=len(vm["disks"]) + 1,
                        path=disk["path"],
                        key=disk["key"],
                        disabled=disk.get("disabled", False),
                        bus=disk.get("bus"),
                    )
                )
            else:
                # `path`, `volume_uuid` and `storage_policy_uuid` are already ensured in _submit_create_vm
                # `bus` is a schema required
                # `quota_policy` not required
                vm["disks"].append(
                    storage.construct_vm_volume_with_path(
                        boot=len(vm["disks"]) + 1,
                        path=disk["path"],
                        volume_uuid=disk["volume_uuid"],
                        serial=disk["serial"],
                        bus=disk["bus"],
                        storage_policy_uuid=disk["storage_policy_uuid"],
                        quota_policy=disk.get("quota_policy"),
                    )
                )

        vm["nics"] = network.normalize_nic(vm["nics"], new_vm_attr.get("nics"))

        vm["vm_name"] = new_vm_attr.get("vm_name") or self.name

        # handle other attributes
        # Creating a vm from a template does not allow setting advanced options
        for attr in [
            "status",
            "ha",
            "boot_with_host",
            "auto_schedule",
            "nested_virtualization",
            "node_ip",
            "vcpu",
            "cpu",
            "memory",
            "description",
            "firmware",
            "hostdevs",
            "placement_groups",
            "internal_product",
        ]:
            value = new_vm_attr.get(attr, None)
            if value is not None:
                vm[attr] = value

        vm["ha_priority"] = new_vm_attr.get("ha_priority", HA_PRIORITY_DEFAULT)

        if "quota_policy" in new_vm_attr:
            vm["quota_policy"] = new_vm_attr["quota_policy"]

        vm["source_resource_uuid"] = vm.pop("uuid")
        vm["uuid"] = str(uuid.uuid4())
        vm["create_time"] = None
        vm["cpu_exclusive"] = new_vm_attr.get("cpu_exclusive", DEFAULT_CPU_EXCLUSIVE_JSON)
        vm["vm_version"] = new_vm_attr.get("vm_version", VM_VERSION_LATEST)
        vm["vm_version_mode"] = new_vm_attr.get("vm_version_mode", VM_VERSION_MODE_AUTO)
        vm["cpu_qos"] = new_vm_attr.get("cpu_qos", DEFAULT_CPU_QOS_JSON)
        vm["migratable"] = new_vm_attr.get("migratable", True)

        asiainfo.config_on_create(vm)
        return vm
