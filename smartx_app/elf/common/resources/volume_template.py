# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
import uuid

from common.config.constant import (
    DEFAULT_ZBS_VOLUME_POOL_NAME,
    DEFAULT_ZBS_VOLUME_TEMPLATE_POOL_NAME,
    VOL_CREATED,
    VOL_DELETED,
)
from common.config.resources import RESOURCE_IN_USE, RESOURCE_REMOVED
from common.mongo.db import mongodb
from smartx_app.elf.common import constants as elf_common_constants
from smartx_app.elf.common.resources.base import ResourceBase, ResourceException
from smartx_app.elf.common.resources.iscsi_volume_template import ISCSIVolumeTemplate
from smartx_app.elf.common.resources.volume import NFSVolume
from smartx_app.elf.job_center.constants import (
    KVM_VOL,
    KVM_VOL_ISCSI,
    KVM_VOL_ISCSI_TEMPLATE,
    KVM_VOL_TEMPLATE,
    KVM_VOL_TEMPLATE_SUPER,
)
from smartx_proto.errors import pyerror_pb2 as py_error


class NFSVolumeTemplate(ResourceBase):
    super_type = KVM_VOL_TEMPLATE_SUPER
    type = KVM_VOL_TEMPLATE
    resources_db = mongodb.resources

    def __init__(
        self,
        name,
        description,
        volume_uuid,
        diff_size=None,
        size=None,
        this_template_uuid=None,
        status=VOL_CREATED,
        resource_state=RESOURCE_IN_USE,
        create_time=None,
        export_name=DEFAULT_ZBS_VOLUME_TEMPLATE_POOL_NAME,
        from_export_name=None,
        volume_size=None,
        alloc_even=None,
        unique_size=None,
    ):
        self.uuid = this_template_uuid or str(uuid.uuid4())
        self.export_name = export_name
        self.name = name
        self.description = description
        self.volume_uuid = volume_uuid
        self.from_export_name = from_export_name
        self.status = status
        self.resource_state = resource_state
        self.create_time = create_time
        self.size = size
        self.diff_size = diff_size
        self.volume_size = volume_size
        self.alloc_even = alloc_even
        self.unique_size = unique_size
        # the `resident_in_cache` is always false for nfs volume template
        self.resident_in_cache = False
        # the `encryption_algorithm` is always plaintext for nfs volume template
        self.encryption_algorithm = elf_common_constants.VOLUME_ENCRYPTION_ALG_PLAINTEXT

    @classmethod
    def load_volume_template(cls, this_volume_uuid):
        template = cls.resources_db.resource.find_one(
            {"uuid": this_volume_uuid, "type": KVM_VOL_TEMPLATE, "resource_state": RESOURCE_IN_USE}, {"_id": 0}
        )
        if not template:
            raise ResourceException("Volume template not found", py_error.VOLUME_TEMPLATE_NOT_FOUND)

        return cls(
            name=template.get("name"),
            description=template.get("description"),
            this_template_uuid=template.get("uuid"),
            volume_uuid=template.get("volume_uuid"),
            status=template.get("status"),
            resource_state=template.get("resource_state"),
            create_time=template.get("create_time"),
            size=template.get("size"),
            export_name=template.get("export_name"),
            from_export_name=template.get("from_export_name"),
            diff_size=template.get("diff_size"),
            volume_size=template.get("volume_size"),
            unique_size=template.get("unique_size"),
        )

    @classmethod
    def load_from_volume_path(cls, path):
        volume = cls.resources_db.resource.find_one({"path": path, "type": KVM_VOL})
        if not volume:
            raise ResourceException("Volume not found", py_error.VOLUME_NOT_FOUND)
        return cls(
            name=volume.get("name"),
            description=volume.get("description"),
            volume_uuid=volume.get("uuid"),
            from_export_name=(volume.get("export_name") or DEFAULT_ZBS_VOLUME_POOL_NAME),
            volume_size=volume.get("size"),
            alloc_even=True,
        )

    def delete(self, delete_permanently=True):
        """
        :param delete_permanently: for compatible with ISCSIVolumeTemplate
        """
        volume_json = self.dumps()
        volume_json["status"] = VOL_DELETED
        volume_json["resource_state"] = RESOURCE_REMOVED
        return volume_json

    def create_volume(self):
        volume_template = self.dumps()
        volume = NFSVolume(
            name=volume_template.get("name"),
            description=volume_template.get("description"),
            volume_uuid=volume_template.get("uuid"),
            status=volume_template.get("status"),
            from_export_name=volume_template.get("export_name"),
            size=volume_template.get("volume_size"),
        )
        volume.generate_properties()

        return volume.dumps()


class VolumeTemplate:
    _VOLUME_TEMPLATE_LOADERS = {
        KVM_VOL_ISCSI_TEMPLATE: ISCSIVolumeTemplate.load_volume_template,
        KVM_VOL_TEMPLATE: NFSVolumeTemplate.load_volume_template,
    }

    _VOLUME_TEMPLATE_PATH_LOADERS = {
        KVM_VOL_ISCSI: ISCSIVolumeTemplate.load_from_volume_path,
        KVM_VOL: NFSVolumeTemplate.load_from_volume_path,
    }

    resources_db = mongodb.resources

    def __init__(self, volume_template):
        self.volume_template = volume_template

    @classmethod
    def load(cls, volume_template_uuid):
        volume_template = cls.resources_db.resource.find_one(
            {
                "uuid": volume_template_uuid,
                "type": {"$in": [KVM_VOL_ISCSI_TEMPLATE, KVM_VOL_TEMPLATE]},
                "resource_state": RESOURCE_IN_USE,
            },
            {"type": 1},
        )

        if volume_template is None:
            raise ResourceException(
                "Volume template [{}] is not exist. ".format(volume_template_uuid),
                py_error.VOLUME_TEMPLATE_NOT_FOUND,
            )

        return cls._VOLUME_TEMPLATE_LOADERS[volume_template.pop("type")](volume_template_uuid)

    @classmethod
    def batch_get_by_uuid(cls, uuids):
        volume_templates = cls.resources_db.resource.find(
            {
                "uuid": {"$in": uuids},
                "type": {"$in": [KVM_VOL_ISCSI_TEMPLATE, KVM_VOL_TEMPLATE]},
                "resource_state": RESOURCE_IN_USE,
            }
        )

        if volume_templates is None:
            raise ResourceException(
                "Volume template [{}] is not exist.".format(uuids), py_error.VOLUME_TEMPLATE_NOT_FOUND
            )

        return volume_templates

    @classmethod
    def load_from_volume_path(cls, path):
        volume = cls.resources_db.resource.find_one(
            {"path": path, "type": {"$in": [KVM_VOL_ISCSI, KVM_VOL]}}, {"type": 1}
        )
        if volume is None:
            raise ResourceException("Volume({}) is not exist.".format(path), py_error.VOLUME_NOT_FOUND)

        return cls._VOLUME_TEMPLATE_PATH_LOADERS[volume.pop("type")](path)
