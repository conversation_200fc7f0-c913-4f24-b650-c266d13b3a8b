# Copyright (c) 2013-2017, SMARTX
# All rights reserved.
from collections import defaultdict
import copy
import logging
import re
import time
import uuid

from common.config.resources import RESOURCE_IN_USE
from common.mongo.db import mongodb
from smartx_app.elf.common.resources.base import ResourceException
from smartx_app.elf.common.utils import cluster, zbs_meta
from smartx_app.elf.job_center.constants import KVM_VM
from smartx_proto.errors import pyerror_pb2 as py_error

logger = logging.getLogger(__file__)


class VMToHostRule:
    MUST_BE = "must_be"
    MUST_NOT_BE = "must_not_be"
    PREFER = "prefer"
    PREFER_NOT = "prefer_not"

    All = (MUST_BE, MUST_NOT_BE, PREFER, PREFER_NOT)


class VMToVMRule:
    NULL = "null"
    MUST_DIFFERENT = "must_different"
    MUST_SAME = "must_same"
    PREFER_DIFFERENT = "prefer_different"
    PREFER_SAME = "prefer_same"

    All = (NULL, MUST_DIFFERENT, MUST_SAME, PREFER_DIFFERENT, PREFER_SAME)
    MUST = (MUST_DIFFERENT, MUST_SAME)
    PREFER = (PREFER_DIFFERENT, PREFER_SAME)


class PlacementGroup:
    MAX_COUNT = 1024
    SORT_FIELDS_SUPPORT = ("name", "associated_vms", "description", "created_at", "enable")
    REGEX_SEARCH_FIELDS_SUPPORT = ("uuid", "name", "description")
    DEFAULT_SORT_FIELD = "created_at"
    ASSOCIATED_VMS_FIELD = "associated_vms"

    placement_group_coll = mongodb.resources.placement_group
    resource_coll = mongodb.resources.resource

    @staticmethod
    def _get_zone_to_host_uuids() -> dict[str, list[str]]:
        """
        :return: {zone_name(defined in ELF module): [host_uuid,]}
        """
        from smartx_app.common.node.db import query_hosts

        zone_to_host_uuids = defaultdict(list)
        for host in query_hosts():
            zone_name = host.get("zone_name")
            # Only zone_names recognized by ELF are valid
            if name := cluster.translate_zone_name(zone_name):
                zone_to_host_uuids[name].append(host["host_uuid"])

        return dict(zone_to_host_uuids)

    def __init__(
        self,
        placement_group_uuid,
        placement_group_name,
        vm_host_rules,
        vm_vm_rule=VMToVMRule.NULL,
        description=None,
        created_at=None,
        modified_at=None,
        associated_vms=None,
        enable=True,
    ):
        self.uuid = placement_group_uuid or str(uuid.uuid4())
        self.name = placement_group_name
        self.policy = {"vm_host_rules": vm_host_rules, "vm_vm_rule": vm_vm_rule}
        self.description = description
        self.created_at = created_at or int(time.time())
        self.modified_at = modified_at
        self.enable = enable
        self.__associated_vms = associated_vms

    @classmethod
    def total_count(cls):
        return cls.placement_group_coll.find({}).count()

    @classmethod
    def check_conflict_vm_host_rules(cls, vm_host_rules):
        mapping = {x["flag"]: x["hosts"] for x in vm_host_rules}
        must_be = set(mapping.get(VMToHostRule.MUST_BE, []))
        must_not_be = set(mapping.get(VMToHostRule.MUST_NOT_BE, []))
        priority_is = set(mapping.get(VMToHostRule.PREFER, []))
        priority_is_not = set(mapping.get(VMToHostRule.PREFER_NOT, []))

        if must_be and (must_be.issubset(must_not_be) or must_be.issubset(priority_is_not)):
            raise ResourceException("Rule of `must be` conflict.", py_error.PLACEMENT_GROUP_RULE_CONFLICT)

        if priority_is and (priority_is.issubset(must_not_be) or priority_is.issubset(priority_is_not)):
            raise ResourceException("Rule of `priority is` conflict.", py_error.PLACEMENT_GROUP_RULE_CONFLICT)

        return vm_host_rules

    @classmethod
    def merge_vm_vm_rules(cls, vm_vm_rules):
        rules = {rule for rule in vm_vm_rules if rule != VMToVMRule.NULL}

        if not rules:
            return VMToVMRule.NULL

        if len(rules) == 1:
            return rules.pop()

        def _no_conflict(conflict_rules_list):
            for conflict_rules in conflict_rules_list:
                if all([x in rules for x in conflict_rules]):
                    return False
            else:
                return True

        if len(rules) == 2 and _no_conflict((VMToVMRule.MUST, VMToVMRule.PREFER)):
            for rule in rules:
                if rule in VMToVMRule.MUST:
                    return rule

        raise ResourceException(
            "VM_TO_VM rule conflict, rules: {}".format(", ".join(rules)), py_error.PLACEMENT_GROUP_RULE_CONFLICT
        )

    @classmethod
    def merge_vm_host_rules(cls, vm_host_rules):
        """
        :param vm_host_rules: The list of vm host rules
        :return: dict, the `key` is vm host rule flag, the `value` is host list.
        eg.
        {
            "must_be": ["xx-xxx", "uu-uu"],
            "must_not_be": ["xx-xxx", "uu-uu"]
        }
        """
        rules = {x: [] for x in VMToHostRule.All}
        for rule in vm_host_rules:
            rules[rule["flag"]].append(set(rule["hosts"]))

        def _handle_rule(flag, func):
            if rules[flag]:
                t = rules[flag][0]
                for i in range(1, len(rules[flag])):
                    t = func(t, rules[flag][i])
                rules[flag] = t
            else:
                rules.pop(flag)

        _handle_rule(VMToHostRule.MUST_BE, lambda x, y: x & y)
        _handle_rule(VMToHostRule.PREFER, lambda x, y: x & y)
        _handle_rule(VMToHostRule.MUST_NOT_BE, lambda x, y: x | y)
        _handle_rule(VMToHostRule.PREFER_NOT, lambda x, y: x | y)
        cls.check_conflict_vm_host_rules([{"flag": k, "hosts": v} for k, v in list(rules.items())])

        return rules

    @classmethod
    def merge_rules(cls, placement_groups, zone_to_host_uuids: dict[str, list[str]] | None = None):
        """
        :param placement_groups: The list of placement group.
        :param zone_to_host_uuids: A mapping dictionary of zone name to list of host UUIDs
        :return: dict, eg.
        {
            "vm_host_rules": {
                "must_be": ["xx-xxx", "uu-uu"],
                "must_not_be": ["xx-xxx", "uu-uu"]
            },
            "vm_vm_rule": "same"
        }
        """
        vm_vm_rule = cls.merge_vm_vm_rules([grp.vm_vm_rule for grp in placement_groups])

        vm_host_rules = []
        for grp in placement_groups:
            vm_host_rules.extend(grp.normalize_hosts(grp.vm_host_rules, zone_to_host_uuids))
        vm_host_rules = cls.merge_vm_host_rules(vm_host_rules)

        return {"vm_host_rules": vm_host_rules, "vm_vm_rule": vm_vm_rule}

    @classmethod
    def _load(cls, criteria):
        docs = cls.query(criteria)
        if docs:
            return docs[0]

        raise ResourceException(
            "Placement group({}) is not found.".format(criteria), py_error.PLACEMENT_GROUP_NOT_FOUND
        )

    @classmethod
    def load(cls, placement_group_uuid):
        docs = cls.query({"uuid": placement_group_uuid})
        if docs:
            return docs[0]

        raise ResourceException(
            "Placement group({}) is not found.".format(placement_group_uuid), py_error.PLACEMENT_GROUP_NOT_FOUND
        )

    @classmethod
    def load_by_name(cls, placement_group_name):
        docs = cls.query({"name": placement_group_name})
        return docs[0] if docs else None

    @classmethod
    def get_associated_vms(cls, placement_group_uuid_list):
        if not placement_group_uuid_list:
            return {}

        associated_vms = {
            x["_id"]: x["vms"]
            for x in cls.resource_coll.aggregate(
                [
                    {
                        "$match": {
                            "type": KVM_VM,
                            "resource_state": RESOURCE_IN_USE,
                            "placement_groups": {"$in": placement_group_uuid_list},
                        }
                    },
                    {"$project": {"_id": 0, "uuid": 1, "placement_groups": 1}},
                    {"$unwind": "$placement_groups"},
                    {"$group": {"_id": "$placement_groups", "vms": {"$push": "$uuid"}}},
                ]
            )
        }

        for x in placement_group_uuid_list:
            associated_vms.setdefault(x, [])

        return associated_vms

    @classmethod
    def _build_placement_groups(cls, docs, associated_vms):
        return [
            cls(
                placement_group_uuid=doc["uuid"],
                placement_group_name=doc["name"],
                vm_host_rules=doc["policy"]["vm_host_rules"],
                vm_vm_rule=doc["policy"]["vm_vm_rule"],
                description=doc["description"],
                created_at=doc["created_at"],
                modified_at=doc["modified_at"],
                enable=doc["enable"],
                associated_vms=associated_vms[doc["uuid"]],
            )
            for doc in docs
        ]

    @classmethod
    def _page_query_by_associated_vms(cls, count, skip_count, reverse=False):
        all_groups = [x["uuid"] for x in cls.placement_group_coll.find({}, {"_id": 0, "uuid": 1})]
        associated_vms = [(k, v) for k, v in list(cls.get_associated_vms(all_groups).items())]
        # sorted by vm count
        associated_vms.sort(key=lambda item: item[1], reverse=reverse)
        associated_vms = associated_vms[skip_count : skip_count + count]
        docs = list(cls.placement_group_coll.find({"uuid": {"$in": [x[0] for x in associated_vms]}}, {"_id": 0}))

        return docs, dict(associated_vms)

    @classmethod
    def _page_query_common(cls, count, skip_count, sort_field, sort_order):
        docs = list(
            cls.placement_group_coll.find({}, {"_id": 0}).sort([(sort_field, sort_order)]).skip(skip_count).limit(count)
        )
        associated_vms = cls.get_associated_vms([x["uuid"] for x in docs])

        return docs, associated_vms

    @classmethod
    def _resolve_sort_criteria(cls, sort_criteria):
        if sort_criteria:
            if sort_criteria in cls.SORT_FIELDS_SUPPORT:
                return sort_criteria, 1

            if sort_criteria.startswith("-") and sort_criteria[1:] in cls.SORT_FIELDS_SUPPORT:
                return sort_criteria[1:], -1

        return cls.DEFAULT_SORT_FIELD, -1

    @classmethod
    def page_query(cls, count=50, current_page=1, sort_criteria=None):
        count = 50 if (count <= 0 or count > cls.MAX_COUNT) else count
        current_page = 1 if current_page <= 0 else current_page
        skip_count = (current_page - 1) * count
        sort_field, sort_order = cls._resolve_sort_criteria(sort_criteria)
        total_count = cls.total_count()

        if total_count:
            if sort_field == cls.ASSOCIATED_VMS_FIELD:
                docs, associated_vms = cls._page_query_by_associated_vms(count, skip_count, sort_order == -1)
                entities = sorted(
                    cls._build_placement_groups(docs, associated_vms),
                    key=lambda x: len(x.associated_vms),
                    reverse=(sort_order == -1),
                )
            else:
                docs, associated_vms = cls._page_query_common(count, skip_count, sort_field, sort_order)
                entities = sorted(
                    cls._build_placement_groups(docs, associated_vms),
                    key=lambda x: getattr(x, sort_field),
                    reverse=(sort_order == -1),
                )
        else:
            entities = []

        return {
            # Note: `entities` is not list of dict
            "entities": entities,
            "start_uuid": entities[0].uuid if entities else None,
            "end_uuid": entities[-1].uuid if entities else None,
            "filter_criteria": {},
            "sort_criteria": "{}{}".format("" if sort_order > 0 else "-", sort_field),
            "count": count,
            "page_entities": len(entities),
            "total_entities": total_count,
        }

    @classmethod
    def query(cls, criteria, with_associated_vms=False):
        docs = list(cls.placement_group_coll.find(criteria, {"_id": 0}))
        if with_associated_vms:
            associated_vms = cls.get_associated_vms([x["uuid"] for x in docs])
        else:
            associated_vms = {x["uuid"]: None for x in docs}

        return cls._build_placement_groups(docs, associated_vms)

    @classmethod
    def search(cls, text, limit=1000):
        def _g(search_field):
            return {
                search_field: {
                    "$regex": re.sub(r"(\*|\.|\?|\+|\$|\^|\[|\]|\(|\)|\{|\}|\||\\|/)", r"\\\1", text),
                    "$options": "i",
                }
            }

        total_count = cls.total_count()
        if total_count:
            query_criteria = {"$or": [_g(x) for x in cls.REGEX_SEARCH_FIELDS_SUPPORT]}
            docs = list(
                cls.placement_group_coll.find(query_criteria, {"_id": 0}).limit(
                    cls.MAX_COUNT if (limit > cls.MAX_COUNT or limit < 0) else limit
                )
            )
            entities = cls._build_placement_groups(docs, cls.get_associated_vms([x["uuid"] for x in docs]))
        else:
            entities = []

        return {"filter_criteria": {}, "total_entities": len(entities), "value": text, "entities": entities}

    @classmethod
    def normalize_hosts(cls, vm_host_rules, zone_to_host_uuids: dict[str, list[str]] | None = None):
        """
        We do not need caller compatibility with availability_zone,
        so here we convert availability_zone to the backward-compatible hosts syntax

        :param vm_host_rules: placement group vm host rules
        :param zone_to_host_uuids: A mapping dictionary of zone name to list of host UUIDs
        """
        if zone_to_host_uuids is None:
            zone_to_host_uuids = cls._get_zone_to_host_uuids()

        vm_host_rules = copy.deepcopy(vm_host_rules)

        for rule in vm_host_rules:
            availability_zone = rule.pop("availability_zone", None)
            if not availability_zone:
                continue

            hosts = zone_to_host_uuids.get(availability_zone, [])
            if not hosts:
                logging.warning(
                    f"[normalize_hosts] "
                    f"The specified `{availability_zone}` zone in the rule contains no hosts. rule details: {rule}"
                )
            rule["hosts"] = hosts

        return vm_host_rules

    @property
    def has_associated_vm(self):
        return (
            self.resource_coll.find_one(
                {"resource_state": RESOURCE_IN_USE, "type": KVM_VM, "placement_groups": self.uuid}
            )
            is not None
        )

    @property
    def associated_vms(self):
        if self.__associated_vms is None:
            cursor = self.resource_coll.find(
                {"resource_state": RESOURCE_IN_USE, "type": KVM_VM, "placement_groups": self.uuid},
                {"_id": 0, "uuid": 1},
            )
            self.__associated_vms = [x["uuid"] for x in cursor]

        return tuple(self.__associated_vms)

    @property
    def vm_host_rules(self):
        return copy.deepcopy(self.policy["vm_host_rules"])

    @property
    def vm_vm_rule(self):
        return copy.deepcopy(self.policy["vm_vm_rule"])

    def dumps(self, with_associated_vms=False):
        result = copy.deepcopy(self.__dict__)
        for x in list(result.keys()):
            if x.startswith("_"):
                result.pop(x)

        if with_associated_vms:
            result.setdefault("associated_vms", self.associated_vms)

        return result

    def _check_vm_host_rules(self):
        rules = self.vm_host_rules

        if len(rules) > 4:  # or len(rules) == 0:
            raise ResourceException(
                "The count of vm_host_rules must be between 0 and 4.", py_error.PLACEMENT_GROUP_COUNT_OUT_OF_RANGE
            )

        if len(rules) > len({x.get("flag", None) for x in rules}):
            raise ResourceException("The vm_host_rules have duplicate rules.", py_error.PLACEMENT_GROUP_DUPLICATE_RULE)

        vm_host_rule_flags = [x for x in VMToHostRule.All]
        for x in rules:
            if x.get("flag", None) not in vm_host_rule_flags:
                raise ResourceException(
                    "`{}` has the invalid flag.".format(x), py_error.PLACEMENT_GROUP_RULE_INVALID_FLAG
                )

            if x.get("availability_zone"):
                is_stretched, _ = zbs_meta.get_preferred_zone()
                if not is_stretched:
                    raise ResourceException(
                        "The `availability_zone` can only be specified in stretched cluster.",
                        py_error.PLACEMENT_GROUP_RULE_INVALID,
                    )

            elif not x.get("hosts", None):
                raise ResourceException(
                    "{} has an empty `hosts` field.".format(x), py_error.PLACEMENT_GROUP_RULE_EMPTY_HOSTS
                )

        return rules

    def _check_vm_vm_rule(self):
        rule = self.vm_vm_rule
        if rule not in VMToVMRule.All:
            raise ResourceException("`{}` is an invalid flag.".format(rule), py_error.PLACEMENT_GROUP_RULE_INVALID_FLAG)

        return rule

    def validate(self):
        self._check_vm_host_rules()
        self._check_vm_vm_rule()
        self.check_conflict_vm_host_rules(self.normalize_hosts(self.vm_host_rules))

    def _change_associated_vms(self, vm_uuid_list, operator):
        count = 0
        bulk = self.resource_coll.initialize_ordered_bulk_op()

        for vm_uuid in vm_uuid_list:
            bulk.find({"resource_state": RESOURCE_IN_USE, "type": KVM_VM, "uuid": vm_uuid}).update_one(
                {operator: {"placement_groups": self.uuid}}
            )
            count += 1

            if count >= 1000:
                bulk.execute()
                count = 0
                bulk = self.resource_coll.initialize_ordered_bulk_op()

        if count > 0:
            bulk.execute()

    def add_associated_vms(self, vm_uuid_list):
        self._change_associated_vms(vm_uuid_list, "$addToSet")

    def remove_associated_vms(self, vm_uuid_list=None):
        """
        :param vm_uuid_list:    The list of vm uuid. Will remove all
                                associated vms if it is None.
        :return:                None
        """
        self._change_associated_vms(self.associated_vms if vm_uuid_list is None else vm_uuid_list, "$pull")

    def delete(self):
        self.remove_associated_vms()
        result = self.placement_group_coll.delete_one({"uuid": self.uuid})

        if result.deleted_count:
            logging.info("delete placement group({}).".format(self.uuid))
            return True
        else:
            return False

    def _get_modified_at_to_update(self):
        return self.created_at if self.modified_at is None else int(time.time())

    def insert_or_update(self):
        from pymongo import errors

        self.validate()
        if self.total_count() >= self.MAX_COUNT:
            raise ResourceException(
                "The maximum({}) of placement groups is reached.".format(self.MAX_COUNT),
                py_error.PLACEMENT_GROUP_MAXIMUM_REACHED,
            )

        modified_at = self._get_modified_at_to_update()
        try:
            result = self.placement_group_coll.update_one(
                {"uuid": self.uuid},
                {
                    "$set": {
                        "name": self.name,
                        "policy": self.policy,
                        "modified_at": modified_at,
                        "description": self.description,
                        "enable": self.enable,
                    },
                    "$setOnInsert": {"created_at": self.created_at},
                },
                upsert=True,
            )
        except errors.DuplicateKeyError as e:
            raise ResourceException(str(e), py_error.PLACEMENT_GROUP_DUPLICATE_NAME)
        else:
            if result.modified_count or result.upserted_id:
                self.modified_at = modified_at
                return True
            else:
                return False
