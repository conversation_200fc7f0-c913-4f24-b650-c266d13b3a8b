# Copyright (c) 2013-2022, SMARTX
# All rights reserved.
import base64
from collections import namedtuple
import logging

import scrypt

from common.mongo import db
from smartx_app.elf.common import constants as elf_constants
from smartx_app.elf.common.resources import base
from smartx_app.elf.job_center import constants
from smartx_proto.errors import pyerror_pb2 as py_error

_AccessInfo = namedtuple("AccessInfo", ["ip", "ports", "username", "password"])


class StorageCluster(base.ResourceBase):
    type = constants.STORAGE_CLUSTER
    resources_db = db.mongodb.resources
    collection = resources_db.storage_cluster

    @staticmethod
    def encode_password(password: str) -> str:
        return base64.b64encode(scrypt.encrypt(password, elf_constants.ENCRYPT_SALT, maxtime=0.1)).decode("utf-8")

    @staticmethod
    def decode_password(cipher: str) -> str:
        return scrypt.decrypt(base64.b64decode(cipher.encode("utf-8")), elf_constants.ENCRYPT_SALT)

    @classmethod
    def initialize(cls):
        cls.ensure_index()

    @classmethod
    def ensure_index(cls):
        cls.collection.create_index("uuid", unique=True, background=True)

    @classmethod
    def load(cls, uuid):
        storage_clusters = cls.query({"uuid": uuid})
        if len(storage_clusters) == 0:
            raise base.ResourceException(
                "The storage_cluster({}) is not exist.".format(uuid), py_error.STORAGE_CLUSTER_NOT_FOUND
            )

        return storage_clusters[0]

    @classmethod
    def batch_load(cls, uuid_list):
        uuid_list = list(set(uuid_list))
        storage_clusters = cls.query({"uuid": {"$in": uuid_list}})
        if len(storage_clusters) != len(uuid_list):
            exist_sc_uuid_list = [sc.uuid for sc in storage_clusters]
            non_exist_sc_uuid_list = [sc_uuid for sc_uuid in uuid_list if sc_uuid not in exist_sc_uuid_list]
            raise base.ResourceException(
                "The storage_clusters({}) are not exist.".format(non_exist_sc_uuid_list),
                py_error.STORAGE_CLUSTER_NOT_FOUND,
            )

        return storage_clusters

    @classmethod
    def _query(cls, criteria=None):
        query = {"type": cls.type}
        projection = {"_id": 0, "type": 0}
        if criteria:
            query.update(criteria)

        return [cls(**storage_cluster) for storage_cluster in cls.collection.find(query, projection)]

    @classmethod
    def query(cls, criteria=None):
        query = {"resource_state": elf_constants.RESOURCE_IN_USE}
        if criteria:
            query.update(criteria)
        return cls._query(query)

    @classmethod
    def query_include_removing(cls, criteria=None):
        query = {"resource_state": {"$in": [elf_constants.RESOURCE_IN_USE, elf_constants.RESOURCE_REMOVING]}}
        if criteria:
            query.update(criteria)
        return cls._query(query)

    @classmethod
    def is_exist(cls, uuid):
        r = cls.collection.find_one({"uuid": uuid}, {"_id": 0, "uuid": 1, "resource_state": 1})
        if r and r["resource_state"] == elf_constants.RESOURCE_REMOVED:
            logging.info(f"delete resource {uuid} that is already in the `removed` state")
            cls.collection.delete_one({"uuid": uuid, "resource_state": elf_constants.RESOURCE_REMOVED})
            return False
        return bool(r)

    def __init__(
        self,
        name,
        uuid,
        access_info: dict,
        status=constants.STORAGE_CLUSTER_CREATED,
        description=None,
        created_time=None,
        modified_time=None,
        is_raw_password=False,
        resource_state=None,
    ):
        self.uuid = uuid
        self.name = name
        self.description = description
        self.status = status
        self.created_time = created_time
        self.modified_time = modified_time
        # splitter include_current_json requires this field
        self.resource_state = resource_state or elf_constants.RESOURCE_IN_USE

        if is_raw_password:
            access_info["password"] = self.encode_password(access_info["password"])

        self.access_info: _AccessInfo = _AccessInfo(**access_info)

    def dumps(self):
        result = super().dumps()
        result["access_info"] = self.access_info._asdict()
        return result

    def update_access_info(self, ip, username, password):
        self.access_info = _AccessInfo(
            ip,
            self.access_info.ports,
            username,
            self.encode_password(password),
        )

    def _save_to_db(self):
        self.collection.update({"uuid": self.uuid}, {"$set": self.dumps()})

    def update_status(self, status):
        self.status = status
        self._save_to_db()

    def update_resource_state(self, resource_state):
        if self.resource_state != resource_state:
            self.resource_state = resource_state
            self._save_to_db()

    def update_db_access_account(self, username, password):
        self.collection.update_one(
            {"uuid": self.uuid},
            {"$set": {"access_info.username": username, "access_info.password": self.encode_password(password)}},
        )
