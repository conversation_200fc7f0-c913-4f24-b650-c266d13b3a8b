import copy
import logging
import time
import uuid

from common.config.resources import RESOURCE_IN_USE, RESOURCE_REMOVED
from common.mongo.db import mongodb
from job_center.config import RESOURCES_DB_NAME
from smartx_app.elf.common.resource_wrappers.volume_wrapper import VolumeWrapper
from smartx_app.elf.job_center.constants import KVM_VM, KVM_VOL, KVM_VOL_ISCSI


class VolVMRelation:
    collection = mongodb[RESOURCES_DB_NAME].vol_vm_association
    resource_collection = mongodb.resources.resource
    stale_mounting_collection = mongodb.resources.stale_mounting
    STALE_MOUNTING = "STALE_MOUNTING"

    @classmethod
    def add_stale_volumes(cls, volume_uuids):
        cls.stale_mounting_collection.update_one(
            {"_id": cls.STALE_MOUNTING}, {"$addToSet": {"volumes": {"$each": volume_uuids}}}, upsert=True
        )

    @classmethod
    def remove_stale_volumes(cls, volume_uuids):
        cls.stale_mounting_collection.update_one(
            {"_id": cls.STALE_MOUNTING}, {"$pull": {"volumes": {"$in": volume_uuids}}}
        )

    @classmethod
    def get_all_stale_volumes(cls):
        doc = cls.stale_mounting_collection.find_one({"_id": cls.STALE_MOUNTING})
        if doc:
            return doc.get("volumes", [])
        return None

    @classmethod
    def recycle_stale_volumes(cls):
        volume_uuids = cls.get_all_stale_volumes()
        if volume_uuids:
            mounting_vols = []
            non_mounting_vols = []

            mounting_stats = cls.get_mounting_vm_stats_by_volume_uuid(volume_uuids)

            for vol_uuid in volume_uuids:
                if mounting_stats.get(vol_uuid, None):
                    mounting_vols.append(vol_uuid)
                else:
                    non_mounting_vols.append(vol_uuid)

            if mounting_vols:
                logging.info("Update the `mounting` to True: {}".format(mounting_vols))
                cls.update_vol_mounting(mounting_vols, True)

            if non_mounting_vols:
                logging.info("Update the `mounting` to False: {}".format(non_mounting_vols))
                cls.update_vol_mounting(non_mounting_vols, False)

            logging.info("Remove the stale volumes: {}".format(volume_uuids))
            cls.remove_stale_volumes(volume_uuids)

    @classmethod
    def bulk(cls):
        return cls.collection.initialize_unordered_bulk_op()

    @classmethod
    def create(cls, volume_id, vm_id, boot_order, mount_point, bus="virtio", bulk=None):
        """we do not execute `bulk.execute()` if `bulk` is given"""
        conditions = {
            "volume_id": volume_id,
            "vm_id": vm_id,
        }

        update_fields = {
            "$set": {
                "id": str(uuid.uuid4()),
                "resource_state": RESOURCE_IN_USE,
                "volume_id": volume_id,
                "vm_id": vm_id,
                "create_at": time.time(),
                "boot_order": boot_order,
                "mount_point": mount_point,
                "bus": bus,
            }
        }

        if bulk is not None:
            bulk.find(conditions).upsert().update_one(update_fields)
        else:
            cls.collection.update_one(
                conditions,
                update_fields,
                upsert=True,
            )

    @classmethod
    def delete(cls, volume_id=None, vm_id=None, just_one=True):
        if volume_id is None and vm_id is None and not just_one:
            logging.warning("you're trying to remove all active records in VolVMRelation collection.")

        conditions = {
            "resource_state": RESOURCE_IN_USE,
        }

        if volume_id:
            conditions["volume_id"] = volume_id

        if vm_id:
            conditions["vm_id"] = vm_id

        update_fields = {
            "$set": {"resource_state": RESOURCE_REMOVED},
        }
        if just_one:
            cls.collection.update_one(
                conditions,
                update_fields,
            )
        else:
            cls.collection.update_many(
                conditions,
                update_fields,
            )

    @classmethod
    def is_mounting(cls, volume_id):
        return bool(cls.collection.find_one({"volume_id": volume_id, "resource_state": RESOURCE_IN_USE}))

    @classmethod
    def filter_by_volume_id(cls, volume_id):
        return cls.collection.find({"volume_id": volume_id, "resource_state": RESOURCE_IN_USE})

    @classmethod
    def filter_by_vm_id(cls, vm_id):
        return cls.collection.find({"vm_id": vm_id, "resource_state": RESOURCE_IN_USE})

    @classmethod
    def get_mounting_count(cls, volume_uuid_list):
        """Note: Since only the sharing volume is recorded, the mounting count
        of the non-sharing is always 0.
        """
        cursor = cls.collection.aggregate(
            [
                {"$match": {"resource_state": RESOURCE_IN_USE, "volume_id": {"$in": volume_uuid_list}}},
                {"$group": {"_id": "$volume_id", "count": {"$sum": 1}}},
            ]
        )

        result = {x["_id"]: x["count"] for x in cursor}
        for x in volume_uuid_list:
            result.setdefault(x, 0)

        return result

    @classmethod
    def reset_vol_mounting(cls, vm_uuid):
        """Just reset the `mounting` value of volumes of vm."""
        from smartx_app.elf.common.resource_wrappers.vm_wrapper import VMWrapper

        vms = VMWrapper.query_from_db(vm_uuid)
        if vms:
            volume_uuids = [x["volume_uuid"] for x in vms[0]["disks"] if x["type"] == "disk"]
            if not volume_uuids:
                return

            # Don't care the non-sharing volume, which mounting_count must be 0.
            mounting_stats = cls.get_mounting_count(volume_uuids)

            not_mounting_vols = [
                vol_uuid for vol_uuid, mounting_count in list(mounting_stats.items()) if mounting_count <= 1
            ]

            cls.update_vol_mounting(not_mounting_vols, False)

    @classmethod
    def update_vol_mounting(cls, volume_uuids, mounting):
        cls.resource_collection.update_many(
            {
                "type": {"$in": [KVM_VOL, KVM_VOL_ISCSI]},
                "resource_state": RESOURCE_IN_USE,
                "uuid": {"$in": volume_uuids},
            },
            {"$set": {"mounting": mounting}},
        )

    @classmethod
    def ensure_vol_mounting(cls, force=False):
        query = {"type": {"$in": [KVM_VOL_ISCSI, KVM_VOL]}, "resource_state": RESOURCE_IN_USE}

        def _initialized():
            _query = copy.deepcopy(query)
            _query.update({"mounting": {"$exists": True}})
            _project = {"uuid": 1, "_id": 0}

            return bool(cls.resource_collection.find_one(_query, _project))

        def _scan():
            return cls.resource_collection.aggregate(
                [
                    {"$match": {"type": KVM_VM, "resource_state": RESOURCE_IN_USE}},
                    {"$project": {"disks.path": 1, "uuid": 1, "_id": 0}},
                    {"$unwind": "$disks"},
                    {"$group": {"_id": "$uuid", "disks": {"$push": "$disks.path"}}},
                ]
            )

        def _s(paths, mounting=True):
            _query = copy.deepcopy(query)
            if paths:
                _query.update({"path": {"$in": paths}})

            _update = {"$set": {"mounting": mounting}}
            cls.resource_collection.update_many(_query, _update)

        if force or not _initialized():
            _s(None, False)
            volumes_to_update = []

            for vm in _scan():
                volumes_to_update.extend(vm["disks"])
                if len(volumes_to_update) >= 50:
                    _s(volumes_to_update)
                    volumes_to_update = []
            else:
                if volumes_to_update:
                    _s(volumes_to_update)

            del volumes_to_update

    @classmethod
    def ensure_vol_mounting_by_volume_uuid(cls):
        vm_disk_info = list(
            cls.resource_collection.aggregate(
                [
                    {"$match": {"type": KVM_VM, "resource_state": RESOURCE_IN_USE}},
                    {"$project": {"disks.volume_uuid": 1, "uuid": 1, "_id": 0}},
                    {"$unwind": "$disks"},
                    {"$group": {"_id": "$uuid", "volume_uuids": {"$push": "$disks.volume_uuid"}}},
                ]
            )
        )
        mounted_volume_uuids = [vol_uuid for vm in vm_disk_info for vol_uuid in vm.get("volume_uuids", [])]

        with mongodb.start_session() as session:
            with session.start_transaction():
                cls.resource_collection.update_many(
                    {
                        "type": {"$in": [KVM_VOL_ISCSI, KVM_VOL]},
                        "resource_state": RESOURCE_IN_USE,
                    },
                    {"$set": {"mounting": False}},
                    session=session,
                )
                logging.info("Firstly set mounting status of all volumes to False")

                cls.resource_collection.update_many(
                    {
                        "type": {"$in": [KVM_VOL_ISCSI, KVM_VOL]},
                        "resource_state": RESOURCE_IN_USE,
                        "uuid": {"$in": mounted_volume_uuids},
                    },
                    {"$set": {"mounting": True}},
                    session=session,
                )
                logging.info("Then set mounting status of following volumes to True:\n{}".format(mounted_volume_uuids))

    @classmethod
    def get_mounting_vm_stats_by_volume_uuid(cls, volume_uuids):
        cursor = cls.resource_collection.aggregate(
            [
                {
                    "$match": {
                        "type": KVM_VM,
                        "resource_state": RESOURCE_IN_USE,
                        "disks.volume_uuid": {"$in": volume_uuids},
                    },
                },
                {"$project": {"_id": 0, "disks.volume_uuid": 1, "uuid": 1, "vm_name": 1}},
                {"$unwind": "$disks"},
                {"$group": {"_id": "$disks.volume_uuid", "vms": {"$push": {"uuid": "$uuid", "name": "$vm_name"}}}},
                # filter out cdrom
                {"$match": {"_id": {"$in": volume_uuids}}},
            ]
        )

        return {x["_id"]: x["vms"] for x in cursor}

    @classmethod
    def clean_removed_relations(cls):
        result = cls.collection.delete_many({"resource_state": RESOURCE_REMOVED})
        if result.deleted_count:
            logging.info(
                "[VolVMRelation::clean_removed_relations] Successfully deleted {} records with `removed` state".format(
                    result.deleted_count
                )
            )


def sync_vm_vol_relation(vm_json, just_delete=False):
    """
    first, we delete all active items associate with the vm
    then, reinsert items
    """
    VolVMRelation.reset_vol_mounting(vm_json["uuid"])
    VolVMRelation.delete(vm_id=vm_json["uuid"], just_one=False)

    if not just_delete:
        vol2disk = {v["volume_uuid"]: v for v in vm_json["disks"] if v["type"] == "disk"}
        if not vol2disk:
            return

        sharing_disks = {
            vol["uuid"]: vol2disk[vol["uuid"]]
            for vol in VolumeWrapper.batch_query(list(vol2disk.keys()))
            if vol.get("sharing", False)
        }

        VolVMRelation.update_vol_mounting(list(vol2disk.keys()), True)
        if sharing_disks:
            bulk = VolVMRelation.bulk()
            for vol_uuid, disk in list(sharing_disks.items()):
                VolVMRelation.create(
                    vol_uuid, vm_json["uuid"], disk.get("boot"), disk.get("path"), disk.get("bus"), bulk=bulk
                )
            bulk.execute()


def filter_out_sharing_vol_by_path(disks):
    """
    the functionality is the same to filter_out_sharing_vol, except that it's filtered by volume path
    """
    # corner case: when create the empty VM template, the fisheye can not pass volume_uuid
    # the tower UI has no entry to create empty VM template
    vol_paths = [disk["path"] for disk in disks if disk["type"] == "disk"]
    sharing_vol_paths = {
        vol["path"]
        for vol in VolumeWrapper.batch_query_by_path(vol_paths, {"_id": 0, "sharing": 1, "path": 1})
        if vol.get("sharing", False)
    }
    return [disk for disk in disks if disk["type"] != "disk" or disk["path"] not in sharing_vol_paths]


def filter_out_sharing_vol(disks):
    """
    it returns non-shared volumes in disks including exclusive volumes and cdroms
    """
    vol_uuids = [disk["volume_uuid"] for disk in disks if disk["type"] == "disk"]
    sharing_vol_uuids = {vol["uuid"] for vol in VolumeWrapper.batch_query(vol_uuids) if vol.get("sharing", False)}
    return [disk for disk in disks if disk["type"] != "disk" or disk["volume_uuid"] not in sharing_vol_uuids]


def get_mounted_vms(volume_uuid):
    return list(
        mongodb.resources.resource.find(
            {"type": KVM_VM, "resource_state": RESOURCE_IN_USE, "disks": {"$elemMatch": {"volume_uuid": volume_uuid}}},
            {"_id": 0},
        )
    )
