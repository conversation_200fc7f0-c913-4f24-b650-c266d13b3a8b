# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
import uuid

from common.config.constant import VOL_CREATED, VOL_DELETED
from common.config.resources import RESOURCE_HIDDEN, RESOURCE_IN_USE, RESOURCE_REMOVED
from common.mongo.db import mongodb
from smartx_app.elf.common import constants as elf_common_constants
from smartx_app.elf.common.resources.base import ResourceBase, ResourceException
from smartx_app.elf.common.resources.iscsi_volume import ISCSIVolume
from smartx_app.elf.common.utils import volume_properties as volume_properties_util
from smartx_app.elf.common.utils.resource import byte_to_GiB
from smartx_app.elf.job_center.constants import KVM_VOL, KVM_VOL_ISCSI, KVM_VOL_SUPER
from smartx_proto.errors import pyerror_pb2 as py_error


class Volume:
    @staticmethod
    def _create(volume):
        if volume["type"] == KVM_VOL:
            return NFSVolume(
                name=volume.get("name"),
                description=volume.get("description"),
                this_volume_uuid=volume.get("uuid"),
                volume_uuid=volume.get("volume_uuid"),
                zbs_snapshot_uuid=volume.get("zbs_snapshot_uuid"),
                status=volume.get("status"),
                resource_state=volume.get("resource_state"),
                create_time=volume.get("create_time"),
                path=volume.get("path"),
                # compatible with the old volume data
                size_in_byte=volume.get("size_in_byte", volume["size"] * 2**30),
                mounting=volume.get("mounting", False),
                vendor=volume.get("vendor", elf_common_constants.SCSI_DISK_VENDOR_V2),
                product=volume.get("product", elf_common_constants.SCSI_DISK_VENDOR_V2),
                wwn=volume.get("wwn", ""),
                serial=volume.get("serial", ""),
            )
        else:
            return ISCSIVolume(
                name=volume.get("name"),
                description=volume.get("description"),
                size_in_byte=volume.get("size_in_byte"),
                this_volume_uuid=volume.get("uuid"),
                path=volume.get("path"),
                status=volume.get("status"),
                lun_id=volume.get("lun_id"),
                target_name=volume.get("target_name"),
                resource_state=volume.get("resource_state"),
                zbs_snapshot_uuid=volume.get("zbs_snapshot_uuid"),
                src_volume_uuid=volume.get("src_volume_uuid"),
                storage_policy_uuid=volume.get("storage_policy_uuid"),
                storage_cluster_uuid=volume.get("storage_cluster_uuid"),
                create_time=volume.get("create_time"),
                sharing=volume.get("sharing", False),
                mounting=volume.get("mounting", False),
                vendor=volume.get("vendor", elf_common_constants.SCSI_DISK_VENDOR_V2),
                product=volume.get("product", elf_common_constants.SCSI_DISK_VENDOR_V2),
                wwn=volume.get("wwn", ""),
                serial=volume.get("serial", ""),
                resident_in_cache=volume.get("resident_in_cache", False),
                resident_in_cache_percentage=volume.get("resident_in_cache_percentage", 0),
                encryption_algorithm=volume.get(
                    "encryption_algorithm", elf_common_constants.VOLUME_ENCRYPTION_ALG_PLAINTEXT
                ),
            )

    @staticmethod
    def create(volume):
        return Volume._create(volume)

    @staticmethod
    def load_by_paths(paths):
        cursor = mongodb.resources.resource.find(
            {"path": {"$in": paths}, "type": {"$in": [KVM_VOL, KVM_VOL_ISCSI]}, "resource_state": RESOURCE_IN_USE},
            {"_id": 0},
        )
        return [Volume._create(doc) for doc in cursor]

    @staticmethod
    def load(volume_uuid):
        volume = mongodb.resources.resource.find_one(
            {"uuid": volume_uuid, "type": {"$in": [KVM_VOL, KVM_VOL_ISCSI]}, "resource_state": RESOURCE_IN_USE},
            {"_id": 0},
        )
        if not volume:
            raise ResourceException("Volume is not found", py_error.VOLUME_NOT_FOUND)
        return Volume._create(volume)

    @staticmethod
    def load_hidden_volume(volume_uuid):
        volume = mongodb.resources.resource.find_one(
            {"uuid": volume_uuid, "type": {"$in": [KVM_VOL, KVM_VOL_ISCSI]}, "resource_state": RESOURCE_HIDDEN},
            {"_id": 0},
        )
        if not volume:
            raise ResourceException("The volume %s is not found" % volume_uuid, py_error.VOLUME_NOT_FOUND)

        return Volume._create(volume)

    @staticmethod
    def load_multi(volume_uuid_list, allow_partial=False):
        if not volume_uuid_list:
            return []

        volume_uuid_list = set(volume_uuid_list)
        volume_list = list(
            mongodb.resources.resource.find(
                {
                    "uuid": {"$in": list(volume_uuid_list)},
                    "resource_state": RESOURCE_IN_USE,
                    "type": {"$in": [KVM_VOL, KVM_VOL_ISCSI]},
                },
                {"_id": 0},
            )
        )

        if not allow_partial and len(volume_list) < len(volume_uuid_list):
            raise ResourceException(
                "Volume({}) aren't found.".format(", ".join(volume_uuid_list - {x["uuid"] for x in volume_list})),
                py_error.VOLUME_NOT_FOUND,
            )

        return [Volume._create(x) for x in volume_list]


class NFSVolume(ResourceBase):
    super_type = KVM_VOL_SUPER
    type = KVM_VOL

    def __init__(
        self,
        name,
        description,
        size=None,
        size_in_byte=None,
        this_volume_uuid=None,
        volume_uuid=None,
        path=None,
        status=VOL_CREATED,
        zbs_snapshot_uuid=None,
        resource_state=RESOURCE_IN_USE,
        create_time=None,
        zbs_volume_id=None,
        from_export_name=None,
        **compatible_iscsi_fields,
    ):
        """

        :param name:
        :param description:
        :param size:                the size in GiB, no need if `size_in_byte`
                                    is specified.
        :param size_in_byte:        the size in bytes, no need if `size` is
                                    specified.
        :param this_volume_uuid:
        :param volume_uuid:
        :param path:
        :param status:
        :param zbs_snapshot_uuid:
        :param zbs_volume_id: volume id in zbs
        :param resource_state:
        :param create_time:
        :param from_export_name:
        :param compatible_iscsi_fields:it's just for compatible with ISCSIVolume
        """
        self.uuid = this_volume_uuid or str(uuid.uuid4())
        self.name = name
        self.description = description
        self.volume_uuid = volume_uuid
        self.zbs_snapshot_uuid = zbs_snapshot_uuid
        self.zbs_volume_id = zbs_volume_id
        self.status = status
        self.resource_state = resource_state
        self.create_time = create_time
        self.path = path
        self.from_export_name = from_export_name
        self.sharing = False
        self.mounting = compatible_iscsi_fields.get("mounting", False)
        self.size_in_byte = self.size = None
        self.vendor = compatible_iscsi_fields.get("vendor", elf_common_constants.SCSI_DISK_VENDOR_V2)
        self.product = compatible_iscsi_fields.get("product", elf_common_constants.SCSI_DISK_PRODUCT_V2)
        self.wwn = compatible_iscsi_fields.get("wwn", "")
        self.serial = compatible_iscsi_fields.get("serial", "")

        if size_in_byte is not None:
            self.size_in_byte = size_in_byte
            self.size = int(byte_to_GiB(size_in_byte))
        elif size is not None:
            self.size_in_byte = size * 2**30  # GiB to byte
            self.size = size
        # NFSVolume doesn't support encryption
        self.encryption_algorithm = elf_common_constants.VOLUME_ENCRYPTION_ALG_PLAINTEXT

    @property
    def storage_policy_uuid(self):
        """Compatible with ISCSIVolume and avoid to dump
        the `storage_policy_uuid` field.
        """
        return None

    @classmethod
    def load_volume(cls, this_volume_uuid):
        volume = mongodb.resources.resource.find_one(
            {"uuid": this_volume_uuid, "type": KVM_VOL, "resource_state": RESOURCE_IN_USE}, {"_id": 0}
        )
        if not volume:
            raise ResourceException("Volume snapshot not found", py_error.VOLUME_NOT_FOUND)

        return cls(
            name=volume.get("name"),
            description=volume.get("description"),
            this_volume_uuid=volume.get("uuid"),
            volume_uuid=volume.get("volume_uuid"),
            zbs_snapshot_uuid=volume.get("zbs_snapshot_uuid"),
            zbs_volume_id=volume.get("zbs_volume_id"),
            status=volume.get("status"),
            resource_state=volume.get("resource_state"),
            create_time=volume.get("create_time"),
            path=volume.get("path"),
            # compatible with the old volume data
            size_in_byte=volume.get("size_in_byte", volume["size"] * 2**30),
            from_export_name=volume.get("from_export_name"),
            mounting=volume.get("mounting", False),
            vendor=volume.get("vendor", elf_common_constants.SCSI_DISK_VENDOR_V2),
            product=volume.get("product", elf_common_constants.SCSI_DISK_VENDOR_V2),
            wwn=volume.get("wwn", ""),
            serial=volume.get("serial", ""),
        )

    def delete(self, delete_permanently=True):
        """
        :param delete_permanently: for compatible with ISCSIVolume
        """
        volume_json = self.dumps()
        volume_json["status"] = VOL_DELETED
        volume_json["resource_state"] = RESOURCE_REMOVED
        return volume_json

    def clone(self, this_volume_uuid=None, name=None, description=None, **kwargs):
        volume = NFSVolume(
            name=name or self.name,
            this_volume_uuid=this_volume_uuid,
            description=description or self.description,
            size_in_byte=self.size_in_byte,
            volume_uuid=self.uuid,
        )
        volume.generate_properties()

        return volume.dumps()

    def generate_properties(self):
        self.vendor, self.product, self.serial, self.wwn = volume_properties_util.generate_properties(self.uuid)
