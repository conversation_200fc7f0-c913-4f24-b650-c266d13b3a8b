# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
import uuid

from common.config.resources import RESOURCE_IN_USE, RESOURCE_REMOVED
from common.mongo.db import mongodb
from smartx_app.elf.common import constants as elf_common_constants
from smartx_app.elf.common.constants import SNAPSHOT_CREATED, SNAPSHOT_DELETED
from smartx_app.elf.common.resources.base import ResourceBase, ResourceException
from smartx_app.elf.common.resources.iscsi_snapshot import ISCSIVolumeSnapshot
from smartx_app.elf.common.resources.volume import NFSVolume
from smartx_app.elf.job_center.constants import (
    KVM_VOL_ISCSI_SNAPSHOT,
    KVM_VOL_SNAPSHOT,
    KVM_VOL_SNAPSHOT_SUPER,
)
from smartx_proto.errors import pyerror_pb2 as py_error


class Snapshot:
    @staticmethod
    def _create(snapshot):
        if snapshot["type"] == KVM_VOL_SNAPSHOT:
            return VolumeSnapshot(
                name=snapshot.get("name"),
                description=snapshot.get("description"),
                snapshot_uuid=snapshot.get("uuid"),
                volume_uuid=snapshot.get("volume_uuid"),
                zbs_snapshot_uuid=snapshot.get("zbs_snapshot_uuid"),
                status=snapshot.get("status"),
                resource_state=snapshot.get("resource_state"),
                create_time=snapshot.get("create_time"),
                volume_size=snapshot.get("volume_size"),
                size=snapshot.get("size"),
                volume_name=snapshot.get("volume_name"),
                vm_snapshot=snapshot.get("vm_snapshot"),
                sharing=snapshot.get("sharing", False),
            )
        else:
            return ISCSIVolumeSnapshot(
                name=snapshot.get("name"),
                description=snapshot.get("description"),
                snapshot_uuid=snapshot.get("uuid"),
                volume_uuid=snapshot.get("volume_uuid"),
                zbs_snapshot_uuid=snapshot.get("zbs_snapshot_uuid"),
                status=snapshot.get("status"),
                resource_state=snapshot.get("resource_state"),
                create_time=snapshot.get("create_time"),
                volume_size=snapshot.get("volume_size"),
                size=snapshot.get("size"),
                volume_name=snapshot.get("volume_name"),
                vm_snapshot=snapshot.get("vm_snapshot"),
                target_name=snapshot.get("target_name"),
                lun_id=snapshot.get("lun_id"),
                storage_policy_uuid=snapshot.get("storage_policy_uuid"),
                sharing=snapshot.get("sharing", False),
                storage_cluster_uuid=snapshot.get("storage_cluster_uuid"),
                resident_in_cache=snapshot.get("resident_in_cache", False),
                encryption_algorithm=snapshot.get(
                    "encryption_algorithm", elf_common_constants.VOLUME_ENCRYPTION_ALG_PLAINTEXT
                ),
            )

    @staticmethod
    def load_snapshots_by_volume_uuid(volume_uuid, is_vm_snapshot=False):
        query = {"volume_uuid": volume_uuid, "resource_state": RESOURCE_IN_USE, "super_type": KVM_VOL_SNAPSHOT_SUPER}
        if is_vm_snapshot:
            # Warning:
            # Not currently used, but it may be scanned the wide slice of the
            # `super_type_1_resource_state_1_vm_snapshot_1_volume_uuid_1_uuid_1`
            # index.
            query["vm_snapshot"] = {"$ne": None}
        else:
            query["vm_snapshot"] = {"$type": 10}

        cursor = mongodb.resources.resource.find(query, {"_id": 0})
        return [Snapshot._create(doc) for doc in cursor]

    @staticmethod
    def load(snapshot_uuid):
        snapshot = mongodb.resources.resource.find_one(
            {
                "uuid": snapshot_uuid,
                "resource_state": RESOURCE_IN_USE,
                "type": {"$in": [KVM_VOL_SNAPSHOT, KVM_VOL_ISCSI_SNAPSHOT]},
            },
            {"_id": 0},
        )
        if not snapshot:
            raise ResourceException("Volume snapshot is not found.", py_error.VOLUME_SNAPSHOT_NOT_FOUND)
        return Snapshot._create(snapshot)

    @staticmethod
    def batch_get_by_uuid(snapshot_uuids):
        snapshots = mongodb.resources.resource.find(
            {
                "uuid": {"$in": snapshot_uuids},
                "resource_state": RESOURCE_IN_USE,
                "type": {"$in": [KVM_VOL_SNAPSHOT, KVM_VOL_ISCSI_SNAPSHOT]},
            },
            {"_id": 0},
        )
        if not snapshots:
            raise ResourceException("Volume snapshot is not found.", py_error.VOLUME_SNAPSHOT_NOT_FOUND)
        return snapshots

    @staticmethod
    def load_multi(snapshot_uuid_list):
        if not snapshot_uuid_list:
            return []

        snapshot_uuid_list = set(snapshot_uuid_list)
        snapshots = list(
            mongodb.resources.resource.find(
                {
                    "uuid": {"$in": list(snapshot_uuid_list)},
                    "resource_state": RESOURCE_IN_USE,
                    "type": {"$in": [KVM_VOL_SNAPSHOT, KVM_VOL_ISCSI_SNAPSHOT]},
                },
                {"_id": 0},
            )
        )

        if len(snapshots) < len(snapshot_uuid_list):
            raise ResourceException(
                "Volume snapshots({}) aren't found.".format(
                    ", ".join(snapshot_uuid_list - {x["uuid"] for x in snapshots})
                ),
                py_error.VOLUME_SNAPSHOT_NOT_FOUND,
            )

        return [Snapshot._create(x) for x in snapshots]


class VolumeSnapshot(ResourceBase):
    super_type = KVM_VOL_SNAPSHOT_SUPER
    type = KVM_VOL_SNAPSHOT

    def __init__(
        self,
        name,
        description,
        volume_uuid,
        volume_size,
        volume_name,
        snapshot_uuid=None,
        status=SNAPSHOT_CREATED,
        zbs_snapshot_uuid=None,
        resource_state=None,
        size=None,
        create_time=None,
        vm_snapshot=None,
        sharing=False,
    ):
        self.uuid = snapshot_uuid or str(uuid.uuid4())
        self.name = name
        self.description = description
        self.volume_uuid = volume_uuid
        self.zbs_snapshot_uuid = zbs_snapshot_uuid
        self.status = status
        self.resource_state = resource_state
        self.create_time = create_time
        self.volume_size = volume_size
        self.size = size
        self.volume_name = volume_name
        self.vm_snapshot = vm_snapshot
        self.sharing = sharing
        # the `resident_in_cache` is always false for nfs volume snapshot
        self.resident_in_cache = False
        # the `encryption_algorithm` is always plaintext for nfs volume snapshot
        self.encryption_algorithm = elf_common_constants.VOLUME_ENCRYPTION_ALG_PLAINTEXT

    @classmethod
    def load_snapshot(cls, snapshot_uuid):
        snapshot = mongodb.resources.resource.find_one(
            {"uuid": snapshot_uuid, "type": KVM_VOL_SNAPSHOT, "resource_state": RESOURCE_IN_USE}, {"_id": 0}
        )
        if not snapshot:
            raise ResourceException("Volume snapshot not found", py_error.VOLUME_SNAPSHOT_NOT_FOUND)

        return cls(
            name=snapshot.get("name"),
            description=snapshot.get("description"),
            snapshot_uuid=snapshot.get("uuid"),
            volume_uuid=snapshot.get("volume_uuid"),
            zbs_snapshot_uuid=snapshot.get("zbs_snapshot_uuid"),
            status=snapshot.get("status"),
            resource_state=snapshot.get("resource_state"),
            create_time=snapshot.get("create_time"),
            volume_size=snapshot.get("volume_size"),
            size=snapshot.get("size"),
            volume_name=snapshot.get("volume_name"),
            vm_snapshot=snapshot.get("vm_snapshot"),
        )

    def delete(self):
        snapshot = self.dumps()
        snapshot["status"] = SNAPSHOT_DELETED
        snapshot["resource_state"] = RESOURCE_REMOVED
        return snapshot

    def rebuild(self, this_volume_uuid=None, name=None, description=None):
        volume = NFSVolume(
            name=name or self.volume_name,
            this_volume_uuid=this_volume_uuid,
            description=description,
            size_in_byte=self.volume_size,
            zbs_snapshot_uuid=self.zbs_snapshot_uuid,
        )
        volume.generate_properties()

        return volume.dumps()

    def rollback(self):
        volume = NFSVolume.load_volume(self.volume_uuid)
        volume.zbs_snapshot_uuid = self.zbs_snapshot_uuid
        volume.size_in_byte = self.volume_size
        return volume.dumps()
