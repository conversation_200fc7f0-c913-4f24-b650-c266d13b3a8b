# Copyright (c) 2013-2015, SMARTX
# All rights reserved.

from common.config.resources import RESOURCE_IN_USE
from common.mongo.db import mongodb
from job_center.config import RESOURCES_DB_NAME
from smartx_app.elf.common.resources.base import ResourceBase
from smartx_app.elf.job_center.constants import ISO_IMAGE

resource_db = mongodb[RESOURCES_DB_NAME]
from common.event.event_message import build_event  # noqa: E402, F401


class ISOImage(ResourceBase):
    type = ISO_IMAGE

    def __init__(
        self,
        description,
        name,
        file_name,
        time,
        uuid,
        path,
        os,
        size,
        zbs_volume_id=None,
        resource_state=RESOURCE_IN_USE,
    ):
        self.description = description
        self.name = name
        self.file_name = file_name
        self.time = time
        self.uuid = uuid
        self.path = path
        self.os = os
        self.size = size
        self.resource_state = resource_state
        self.zbs_volume_id = zbs_volume_id


def get_iso_name(path):
    images = resource_db.resource.find_one({"type": ISO_IMAGE, "path": path}, {"name": 1, "_id": 0})

    return images
