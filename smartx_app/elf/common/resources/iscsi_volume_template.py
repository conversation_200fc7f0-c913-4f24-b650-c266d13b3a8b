# Copyright (c) 2013-2016, SMARTX
# All rights reserved.
import uuid

from common.config.constant import VOL_CREATED, VOL_DELETED
from common.config.resources import RESOURCE_IN_USE, RESOURCE_REMOVED
from common.lib.time_utils import utc_now_timestamp
from common.mongo.db import mongodb
from smartx_app.elf.common import constants as elf_common_constants
from smartx_app.elf.common.resources.base import ResourceBase, ResourceException
from smartx_app.elf.common.resources.iscsi_volume import ISCSIVolume
from smartx_app.elf.job_center.constants import KVM_VOL_ISCSI, KVM_VOL_ISCSI_TEMPLATE, KVM_VOL_TEMPLATE_SUPER
from smartx_proto.errors import pyerror_pb2 as py_error


class ISCSIVolumeTemplate(ResourceBase):
    super_type = KVM_VOL_TEMPLATE_SUPER
    type = KVM_VOL_ISCSI_TEMPLATE
    resources_db = mongodb.resources

    def __init__(
        self,
        name,
        description,
        volume_uuid,
        size=None,
        diff_size=None,
        this_template_uuid=None,
        status=None,
        resource_state=None,
        create_time=None,
        storage_policy_uuid=None,
        src_target_name=None,
        src_lun_id=None,
        src_export_id=None,
        src_inode_id=None,
        new_size_in_byte=None,
        clone_before_create=None,
        alloc_even=None,
        unique_size=None,
        storage_cluster_uuid: str | None = None,
        resident_in_cache=False,
        encryption_algorithm=None,
    ):
        """
        Create a iscsi volume template instance.

        :param name:                template name.
        :param description:         template description
        :param volume_uuid:         template volume uuid(clone form it)
        :param size:                template volume size
        :param diff_size:           template volume diff size from clone
        :param unique_size:         template volume unique size from clone
        :param this_template_uuid:  this template uuid
        :param status:              template status, default is VOL_CREATED
        :param resource_state:      resource state, default is RESOURCE_IN_USE
        :param create_time:         created time
        :param storage_policy_uuid  storage policy uuid
        :param src_lun_id           volume will create from a exist LUN if
                                    the src_lun_id and the src_target_name
                                    are specified.
        :param src_target_name      ditto
        :param src_export_id        volume will create from a exist NFS file
                                    if the src_export_id and the src_inode_id
                                    are specified.
        :param src_inode_id         ditto
        :param new_size_in_byte     nonsense, if the volume won't create
                                    from a exist LUN or NFS file. We will extend
                                    the volume if it is specified and it is
                                    greater than the size of the src LUN or NFS
                                    file.
        :param clone_before_create  nonsense, if the volume won't create
                                    from a exist LUN or NFS file. Clone the
                                    src LUN or NFS file if it is True,
                                    otherwise convert directly
        """
        self.uuid = this_template_uuid or str(uuid.uuid4())
        self.name = name
        self.description = description
        self.volume_uuid = volume_uuid
        self.status = status or VOL_CREATED
        self.resource_state = resource_state or RESOURCE_IN_USE
        self.create_time = create_time or utc_now_timestamp()
        self.size = size
        self.diff_size = diff_size
        self.unique_size = unique_size
        self.storage_policy_uuid = storage_policy_uuid
        self.src_target_name = src_target_name
        self.src_lun_id = src_lun_id
        self.src_export_id = src_export_id
        self.src_inode_id = src_inode_id
        self.clone_before_create = clone_before_create
        self.alloc_even = alloc_even
        self.storage_cluster_uuid = storage_cluster_uuid
        self.resident_in_cache = resident_in_cache
        self.encryption_algorithm = encryption_algorithm or elf_common_constants.VOLUME_ENCRYPTION_ALG_PLAINTEXT

        if new_size_in_byte is not None:
            self.new_size_in_byte = new_size_in_byte

    @classmethod
    def load_volume_template(cls, template_uuid):
        template = cls.resources_db.resource.find_one(
            {"uuid": template_uuid, "type": cls.type, "resource_state": RESOURCE_IN_USE}, {"_id": 0, "type": 0}
        )

        if not template:
            raise ResourceException(
                "ISCSI Volume template({}) is not exist.".format(template_uuid), py_error.VOLUME_TEMPLATE_NOT_FOUND
            )

        return cls(
            this_template_uuid=template["uuid"],
            name=template["name"],
            description=template["description"],
            volume_uuid=template["volume_uuid"],
            size=template["size"],
            diff_size=template["diff_size"],
            unique_size=template.get("unique_size", template["diff_size"]),
            status=template["status"],
            resource_state=template["resource_state"],
            create_time=template["create_time"],
            storage_policy_uuid=template["storage_policy_uuid"],
            alloc_even=template.get("alloc_even"),
            storage_cluster_uuid=template.get("storage_cluster_uuid"),
            resident_in_cache=template.get("resident_in_cache", False),
            encryption_algorithm=template.get(
                "encryption_algorithm", elf_common_constants.VOLUME_ENCRYPTION_ALG_PLAINTEXT
            ),
        )

    @classmethod
    def load_from_volume_path(cls, path):
        volume = cls.resources_db.resource.find_one(
            {"path": path, "type": KVM_VOL_ISCSI, "resource_state": RESOURCE_IN_USE}, {"_id": 0, "type": 0}
        )

        if not volume:
            raise ResourceException("ISCSI Volume({}) is not exist.".format(path), py_error.VOLUME_NOT_FOUND)

        return cls(
            name=volume["name"],
            description=volume["description"],
            volume_uuid=volume["uuid"],
            storage_policy_uuid=volume["storage_policy_uuid"],
            storage_cluster_uuid=volume.get("storage_cluster_uuid"),
            alloc_even=True,
            resident_in_cache=volume.get("resident_in_cache", False),
            encryption_algorithm=volume.get(
                "encryption_algorithm", elf_common_constants.VOLUME_ENCRYPTION_ALG_PLAINTEXT
            ),
        )

    def delete(self, delete_permanently=True):
        template_json = self.dumps()
        template_json["status"] = VOL_DELETED
        template_json["resource_state"] = RESOURCE_REMOVED
        template_json["_delete_permanently"] = delete_permanently
        return template_json

    def create_volume(self):
        volume = ISCSIVolume(
            name=self.name,
            description=self.description,
            src_volume_uuid=self.uuid,
            storage_policy_uuid=self.storage_policy_uuid,
            storage_cluster_uuid=self.storage_cluster_uuid,
            resident_in_cache=self.resident_in_cache,
            encryption_algorithm=self.encryption_algorithm,
        )
        volume.generate_properties()

        return volume.dumps()
