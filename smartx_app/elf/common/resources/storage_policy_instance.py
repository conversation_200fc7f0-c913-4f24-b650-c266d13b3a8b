# Copyright (c) 2013-2016, SMARTX
# All rights reserved.
import collections
import copy
import logging
import time
import uuid

from common.config.resources import RESOURCE_IN_USE, RESOURCE_REMOVED
from common.lib.time_utils import utc_now_timestamp
from common.mongo.db import mongodb
from smartx_app.elf.common import constants as ec_constants
from smartx_app.elf.common.config import platform
from smartx_app.elf.common.resources.base import ResourceException
from smartx_app.elf.common.utils import storage_cluster as sc_utils
from smartx_app.elf.common.utils import zbs_iscsi
from smartx_app.elf.job_center.constants import (
    KVM_VM_SNAPSHOT,
    KVM_VM_TEMPLATE,
    KVM_VOL_ISCSI,
    KVM_VOL_ISCSI_SNAPSHOT,
)
from smartx_proto.errors import pyerror_pb2 as py_error
from zbs.iscsi.client import ZbsISCSI
from zbs.lib.zexception import ZException
from zbs.proto import common_pb2 as zbs_common_pb
from zbs.proto import error_pb2 as zbs_error

ReplicaSettings = collections.namedtuple(
    "ReplicaSettings", ["storage_pool_id", "replica_num", "thin_provision", "stripe_num", "stripe_size", "type"]
)
ECSettings = collections.namedtuple(
    "ECSettings",
    ["storage_pool_id", "thin_provision", "ec_k", "ec_m", "stripe_num", "stripe_size", "type"],
)

_ZBS_TARGET_CREATE_RESILIENCY_TYPE_REPLICA = "REPLICA"
_ZBS_TARGET_CREATE_RESILIENCY_TYPE_EC = "EC"
_ZBS_TARGET_CREATE_EC_ALGORITHM_RS = "RS"  # The only EC algorithm ZBS currently supports.

ELF_PROTECTED_LABEL = "ELF_PROTECTED"
ELF_PROTECTED_VALUE = "TRUE"


def get_settings_from_lun(target_name, lun_id, storage_cluster_uuid=None):
    client = zbs_iscsi.ZbsClientWrapper(sc_utils.init_zbs_iscsi_client(storage_cluster_uuid))
    pool = client.target_get(target_name).pool
    lun = client.lun_get(target_name, lun_id)

    if lun.resiliency_type == zbs_common_pb.RT_EC:
        return ECSettings(
            storage_pool_id=pool.storage_pool_id,
            thin_provision=lun.thin_provision,
            ec_k=lun.ec_param.k,
            ec_m=lun.ec_param.m,
            stripe_num=lun.stripe_num,
            stripe_size=lun.stripe_size,
            type=ec_constants.STORAGE_POLICY_TYPE_EC,
        )
    else:
        # lun.resiliency_type == zbs_common_pb.RT_REPLICA
        return ReplicaSettings(
            storage_pool_id=pool.storage_pool_id,
            replica_num=lun.replica_num,
            thin_provision=lun.thin_provision,
            stripe_num=lun.stripe_num,
            stripe_size=lun.stripe_size,
            type=ec_constants.STORAGE_POLICY_TYPE_REPLICA,
        )


def get_settings_from_export(export_id, inode_id=None):
    from zbs.nfs import client as nfs_client

    nfs_client = nfs_client.ZbsNFS("")
    pool = nfs_client.pool_show_by_id(export_id)

    if inode_id is not None:
        inode = nfs_client.inode_show(inode_id)
        if inode.pool_id != export_id:
            raise ResourceException(
                "The inode({}) is not in the export({}).".format(inode_id, export_id),
                py_error.STORAGE_POLICY_INVALID_VALUES,
            )

        source = nfs_client.volume_show_by_id(inode.volume_id).volume
    else:
        source = pool

    return ReplicaSettings(
        pool.storage_pool_id,
        source.replica_num,
        source.thin_provision,
        source.stripe_num,
        source.stripe_size,
        ec_constants.STORAGE_POLICY_TYPE_REPLICA,
    )


class BaseStoragePolicy:
    SP_COLLECTION = mongodb.resources.storage_policy
    RESOURCE_COLLECTION = mongodb.resources.resource

    def __init__(
        self,
        name,
        storage_pool_id=None,
        description=None,
        this_storage_policy_uuid=None,
        created_time=None,
        thin_provision=True,
        whitelist="*/*",
        datastores=None,
        resource_state=None,
        modified_time=None,
        ts=None,
        stripe_num=ec_constants.DEFAULT_STRIPE_NUM,
        stripe_size=256 * 1024,
        *args,
        **kwargs,
    ):
        # base info
        self.uuid = this_storage_policy_uuid or str(uuid.uuid4())
        self.name = name
        self.description = description
        self.storage_pool_id = storage_pool_id
        self.created_time = created_time or utc_now_timestamp()
        self.modified_time = modified_time
        self.datastores = datastores or []
        self.resource_state = resource_state or RESOURCE_IN_USE
        self.thin_provision = thin_provision
        self.whitelist = whitelist
        self.stripe_num = stripe_num
        self.stripe_size = stripe_size

        # timestamp in microsecond as an optimistic lock
        self.ts = ts

        self.type = None

    def query_volumes(self, fields=None, limit=5):
        return self._query_associated_objects(None, fields, KVM_VOL_ISCSI, limit)

    def query_volume_snapshots(self, fields=None, limit=5, select_flag=0):
        if select_flag == 1:
            # not volume snapshot of vm snapshot.
            # query matches documents that either contain the `vm_snapshot`
            # field whose value is null or that do not contain the
            # `vm_snapshot` field.
            query = {"vm_snapshot": None}
        elif select_flag == 2:
            # volume snapshot of vm snapshot.
            # with a field called `vm_snapshot` and a non-null value.
            query = {"vm_snapshot": {"$ne": None}}
        else:
            # all volume snapshot
            query = None

        return self._query_associated_objects(query, fields, KVM_VOL_ISCSI_SNAPSHOT, limit)

    def query_vm_snapshots(self, fields=None, limit=5):
        return self._query_associated_objects(None, fields, KVM_VM_SNAPSHOT, limit)

    def query_vm_templates(self, fields=None, limit=5):
        return self._query_associated_objects(None, fields, KVM_VM_TEMPLATE, limit)

    def _query_associated_objects(self, filter_criteria, fields, resource_type, limit):
        query = {"resource_state": RESOURCE_IN_USE, "type": resource_type}
        if resource_type in (KVM_VM_TEMPLATE, KVM_VM_SNAPSHOT):
            query.update({"disks.storage_policy_uuid": self.uuid})
        else:
            query.update({"storage_policy_uuid": self.uuid})

        if filter_criteria:
            query.update(filter_criteria)

        projection = {"_id": 0}
        if fields:
            projection.update({x: 1 for x in fields})

        return list(self.RESOURCE_COLLECTION.find(query, projection).limit(limit))

    def _has_associated_resources(self):
        return (
            self.query_volumes(fields=["uuid", "name"])
            or self.query_volume_snapshots(fields=["uuid", "name"])
            or self.query_vm_templates(fields=["uuid", "name"])
        )

    def remove_from_db(self):
        if self._has_associated_resources():
            raise ResourceException(
                "The storage policy({}) is not empty.".format(self.uuid), py_error.STORAGE_POLICY_NOT_EMPTY
            )

        self.SP_COLLECTION.update_one({"uuid": self.uuid}, {"$set": {"resource_state": RESOURCE_REMOVED}})

    def update_to_db(self):
        update_data = self.dumps(raw=True)
        created_time = update_data.pop("created_time")
        upsert = update_data.pop("modified_time") is None
        ts = update_data.pop("ts")

        current_filter = {"uuid": self.uuid}
        if ts is not None:
            current_filter.update({"ts": ts})
        else:
            current_filter.update({"ts": {"$exists": False}})

        update_data["ts"] = int(time.time() * 10**6)
        update_data["modified_time"] = utc_now_timestamp()
        current_update = {"$set": update_data, "$setOnInsert": {"created_time": created_time}}

        try:
            old_sp = self.SP_COLLECTION.find_one({"uuid": self.uuid})
        except ResourceException:
            old_sp = None

        if not platform.is_in_elf() and old_sp and old_sp["whitelist"] != self.whitelist:
            # Under SMTX OS, when whitelist changed, the target should also be updated.
            # Under SMTX ELF, targets are not managed by ELF, so they do not need to be updated.
            meta = ZbsISCSI("")
            for pool_name in self.datastores:
                meta.target_update(pool_name, pool_name, None, None, whitelist=self.whitelist)

        result = self.SP_COLLECTION.update_one(current_filter, current_update, upsert=upsert)

        if result.modified_count or result.upserted_id:
            self.modified_time, self.ts = update_data["modified_time"], update_data["ts"]
            return True
        else:
            return False

    def add_target(self, target_name, immediately=True):
        if target_name not in self.datastores:
            self.datastores.append(target_name)
            if immediately and not self.update_to_db():
                raise ResourceException(
                    "The storage policy has been modified by others.", py_error.STORAGE_POLICY_HAS_BEEN_MODIFIED
                )

    def delete_all_targets(self):
        if self._has_associated_resources():
            raise ResourceException(
                "The storage policy({}) is not empty.".format(self.uuid), py_error.STORAGE_POLICY_NOT_EMPTY
            )

        # Only under SMTXOS, the deletion targets scenario exists
        # As a result, the arguemnt meta_ip can be an empty string in the ZbsISCSI constructor
        iscsi_client = ZbsISCSI("")
        for target_name in self.datastores:
            try:
                iscsi_client.target_delete(target_name)
            except ZException as e:
                if e.ec_name == zbs_error.ErrorCode.Name(zbs_error.ENotEmpty):
                    raise ResourceException(
                        "The target({}) in the storage policy({}) is not empty.".format(target_name, self.uuid),
                        py_error.STORAGE_POLICY_NOT_EMPTY,
                    )

                if e.ec_name != zbs_error.ErrorCode.Name(zbs_error.ENotFound):
                    raise

            logging.info(
                "[delete_all_targets] The target({}) from the storage policy({}) has been deleted".format(
                    target_name,
                    self.uuid,
                )
            )

    def dumps(self, raw=False):
        data = copy.deepcopy(self.__dict__)
        if raw:
            return data

        data.pop("ts", None)
        return data

    def is_match_lun(self, target_name, lun_id, storage_cluster_uuid=None):
        """
        Whether have the same settings with the specified LUN.

        :param target_name:             The name of target of the exist LUN.
        :param lun_id:                  ID of the exist LUN.
        :param storage_cluster_uuid:    The uuid of storage cluster.
        :return:                        True or False.
        """
        raise NotImplementedError()

    def is_match_export(self, export_id, inode_id=None):
        """
        Whether the sp has the same settings as the specified LUN.

        :param export_id:       ID of the export.
        :param inode_id:        ID of the inode.
        :return:                True or False.
        """
        raise NotImplementedError()

    @property
    def settings(self):
        raise NotImplementedError()

    def event_message_common_details(self):
        return {
            "storage_policy_name": self.name,
            "white_list": self.whitelist,
            "strip_num": self.stripe_num,
            "strip_size": self.stripe_size,
        }

    def event_message_type_specific_details(self):
        raise NotImplementedError()

    def gen_target_create_parameters(self):
        return {
            "thin_provision": self.thin_provision,
            "whitelist": self.whitelist,
            "storage_pool_id": self.storage_pool_id,
            "labels": {ELF_PROTECTED_LABEL: ELF_PROTECTED_VALUE},
        }

    def gen_target_create_by_requirement_parameters(self):
        return {
            "stripe_num": self.stripe_num,
            "stripe_size": self.stripe_size,
            "thin_provision": self.thin_provision,
        }

    def gen_update_parameters_from_lun(self, current_lun):
        kwargs = {}
        if current_lun.thin_provision != self.thin_provision:
            kwargs["thin_provision"] = self.thin_provision

        return kwargs

    def priority_space_usage_ratio(self):
        raise NotImplementedError()


class ReplicaStoragePolicy(BaseStoragePolicy):
    EVENT_MESSAGE_DETAIL_KEY = "REPLICA_SP_PARAMETER"

    def __init__(
        self,
        name,
        storage_pool_id=None,
        description=None,
        this_storage_policy_uuid=None,
        created_time=None,
        thin_provision=True,
        whitelist="*/*",
        datastores=None,
        resource_state=None,
        modified_time=None,
        ts=None,
        stripe_num=ec_constants.DEFAULT_STRIPE_NUM,
        stripe_size=256 * 1024,
        *args,
        **kwargs,
    ):
        super().__init__(
            name,
            storage_pool_id=storage_pool_id,
            description=description,
            this_storage_policy_uuid=this_storage_policy_uuid,
            created_time=created_time,
            thin_provision=thin_provision,
            whitelist=whitelist,
            datastores=datastores,
            resource_state=resource_state,
            modified_time=modified_time,
            ts=ts,
            stripe_num=stripe_num,
            stripe_size=stripe_size,
            *args,
            **kwargs,
        )

        self.replica_num = kwargs.get("replica_num", 3)
        self.read_only = kwargs.get("read_only", False)
        self.type = ec_constants.STORAGE_POLICY_TYPE_REPLICA

    def _is_match_replica_settings(self, settings: ReplicaSettings):
        return (
            settings.type == self.type
            and settings.storage_pool_id == self.storage_pool_id
            and settings.thin_provision == self.thin_provision
            and settings.replica_num == self.replica_num
            and settings.stripe_num == self.stripe_num
            and settings.stripe_size == self.stripe_size
        )

    def is_match_lun(self, target_name, lun_id, storage_cluster_uuid=None):
        """
        Whether the sp has the same settings as the specified LUN.

        :param target_name:             The name of target of the LUN.
        :param lun_id:                  ID of the LUN.
        :param storage_cluster_uuid:    The uuid of storage cluster.
        :return:                        True or False.
        """
        settings = get_settings_from_lun(target_name, lun_id, storage_cluster_uuid)
        return self._is_match_replica_settings(settings)

    def is_match_export(self, export_id, inode_id=None):
        """
        Whether the sp has the same settings as the specified LUN.

        :param export_id:       ID of the export.
        :param inode_id:        ID of the inode.
        :return:                True or False.
        """
        settings = get_settings_from_export(export_id, inode_id)
        return self._is_match_replica_settings(settings)

    @property
    def settings(self):
        return ReplicaSettings(
            storage_pool_id=self.storage_pool_id,
            replica_num=self.replica_num,
            thin_provision=self.thin_provision,
            stripe_num=self.stripe_num,
            stripe_size=self.stripe_size,
            type=self.type,
        )

    def event_message_type_specific_details(self):
        return {"replica_num": self.replica_num}

    def gen_target_create_parameters(self):
        parameters = super().gen_target_create_parameters()
        parameters.update(
            {"resiliency_type": _ZBS_TARGET_CREATE_RESILIENCY_TYPE_REPLICA, "replica_num": self.replica_num}
        )
        return parameters

    def gen_target_create_by_requirement_parameters(self):
        parameters = super().gen_target_create_by_requirement_parameters()
        parameters.update(
            {"resiliency_type": _ZBS_TARGET_CREATE_RESILIENCY_TYPE_REPLICA, "replica_num": self.replica_num}
        )
        return parameters

    def gen_update_parameters_from_lun(self, current_lun):
        kwargs = super().gen_update_parameters_from_lun(current_lun)
        if current_lun.replica_num != self.replica_num:
            kwargs["replica_num"] = self.replica_num
        return kwargs

    def priority_space_usage_ratio(self):
        return self.replica_num


class ECStoragePolicy(BaseStoragePolicy):
    EVENT_MESSAGE_DETAIL_KEY = "EC_SP_PARAMETER"

    def __init__(
        self,
        name,
        storage_pool_id=None,
        description=None,
        this_storage_policy_uuid=None,
        created_time=None,
        thin_provision=True,
        whitelist="*/*",
        datastores=None,
        resource_state=None,
        modified_time=None,
        ts=None,
        stripe_num=ec_constants.DEFAULT_STRIPE_NUM,
        stripe_size=256 * 1024,
        *args,
        **kwargs,
    ):
        super().__init__(
            name,
            storage_pool_id=storage_pool_id,
            description=description,
            this_storage_policy_uuid=this_storage_policy_uuid,
            created_time=created_time,
            thin_provision=thin_provision,
            whitelist=whitelist,
            datastores=datastores,
            resource_state=resource_state,
            modified_time=modified_time,
            ts=ts,
            stripe_num=stripe_num,
            stripe_size=stripe_size,
            *args,
            **kwargs,
        )

        self.ec_k = kwargs["ec_k"]
        self.ec_m = kwargs["ec_m"]
        self.type = ec_constants.STORAGE_POLICY_TYPE_EC

    def _is_match_ec_settings(self, settings: ECSettings):
        return (
            settings.type == self.type
            and settings.storage_pool_id == self.storage_pool_id
            and settings.thin_provision == self.thin_provision
            and settings.ec_k == self.ec_k
            and settings.ec_m == self.ec_m
            and settings.stripe_num == self.stripe_num
            and settings.stripe_size == self.stripe_size
        )

    def is_match_lun(self, target_name, lun_id, storage_cluster_uuid=None):
        """
        Whether the sp has the same settings as the specified LUN.

        :param target_name:             The name of target of the LUN.
        :param lun_id:                  ID of the LUN.
        :param storage_cluster_uuid:    The uuid of storage cluster.
        :return:                        True or False.
        """
        settings = get_settings_from_lun(target_name, lun_id, storage_cluster_uuid)

        return self._is_match_ec_settings(settings)

    def is_match_export(self, export_id, inode_id=None):
        # Export with EC won't exist
        return False

    @property
    def settings(self):
        return ECSettings(
            storage_pool_id=self.storage_pool_id,
            thin_provision=self.thin_provision,
            ec_k=self.ec_k,
            ec_m=self.ec_m,
            stripe_num=self.stripe_num,
            stripe_size=self.stripe_size,
            type=self.type,
        )

    def event_message_type_specific_details(self):
        return {"ec_k": self.ec_k, "ec_m": self.ec_m}

    def gen_target_create_parameters(self):
        parameters = super().gen_target_create_parameters()
        parameters.update(
            {
                "resiliency_type": _ZBS_TARGET_CREATE_RESILIENCY_TYPE_EC,
                "ec_k": self.ec_k,
                "ec_m": self.ec_m,
                "ec_algo": _ZBS_TARGET_CREATE_EC_ALGORITHM_RS,
            }
        )
        return parameters

    def gen_target_create_by_requirement_parameters(self):
        parameters = super().gen_target_create_by_requirement_parameters()
        parameters.update(
            {
                "resiliency_type": _ZBS_TARGET_CREATE_RESILIENCY_TYPE_EC,
                "ec_k": self.ec_k,
                "ec_m": self.ec_m,
                "ec_algo": _ZBS_TARGET_CREATE_EC_ALGORITHM_RS,
            }
        )
        return parameters

    def priority_space_usage_ratio(self):
        return 2 if self.ec_m == 1 else 3
