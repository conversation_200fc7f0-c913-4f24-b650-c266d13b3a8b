# Copyright (c) 2013-2017, SMARTX
# All rights reserved.
import os


def gen():
    from grpc_tools import protoc

    # Generates protocol messages and gRPC stubs.
    cwd = os.getcwd()
    try:
        os.chdir(os.path.dirname(os.path.realpath(__file__)))
        protoc.main(
            (
                "",
                "-I./protos",
                "--python_out=./protos",
                "./protos/license.proto",
            )
        )
    finally:
        if cwd != os.getcwd():
            os.chdir(cwd)


if __name__ == "__main__":
    gen()
