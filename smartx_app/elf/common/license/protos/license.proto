// SMTX ELF license proto is also used by code in other repos.

syntax = "proto2";
package smtx_elf_license;


enum SoftwareEdition {
   STANDARD = 1;
   ENTERPRISE = 2;
   ENTERPRISE_EISOO = 3;  // deprecated
   ESSENTIAL = 4;
   COMMUNITY = 5;
   EXPRESS = 6;
   ENTERPRISE_PLUS = 7;
}

enum LicenseType {
   TRIAL = 1;
   PERPETUAL = 2;
   SUBSCRIPTION = 3;
}

enum PricingType {
   // for compatibility, it means the max number of hosts
   PRICING_TYPE_UNKNOWN = 1;
   CPU_SLOT_NUM = 2;
   VM_NUM = 3;
}

message SMTXELFLicense {
   required string product_name = 1;
   required string serial = 2;
   required SoftwareEdition software_edition = 3;
   required LicenseType license_type = 4;
   required uint64 sign_date = 5;
   required uint64 period = 6;
   required PricingType pricing_type =7;
   required uint32 max_host_num = 8;
}

message SMTXELFLicenseCertificate {
   required SMTXELFLicense license = 1;
   required string signature_hex = 2;
}
