import base64
import copy
import functools
import logging
import time

import bson
from kazoo import exceptions as kazoo_exceptions
from OpenSSL import crypto
import scrypt

from common.http import tuna
from common.lib import cfg
from common.mongo.db import mongodb
from google.protobuf import descriptor, json_format
from smartx_app.elf.common.config import platform
from smartx_app.elf.common.license.protos import license_pb2
from smartx_app.elf.common.resources import base
from smartx_app.elf.common.utils import cluster
from smartx_proto.errors import pyerror_pb2 as py_error
from zbs.lib import zk
from zbs.meta import client as zbs_client
from zbs.proto import common_pb2

PUBLIC_KEY = """-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDXykUkRH9Cq+PEHkH/CrSpofap
PTPVLp3xdgc+aJSaW4Yz31kdiMR73sgBlm8VNu17oxrt8NrxO/0LbbLTKG9HtAyd
iYpiGXklnP/20dFsqpjXN/sTksKIpU6RiUXYJf2IulPD/Hkt8JD92j3OD+bVrRJG
OArk7fhQtpAspHHBdQIDAQAB
-----END PUBLIC KEY-----"""


LICENSE_STORAGE_ENCRYPT_KEY = "1IcEncE_3nCrPt"
SMTX_ELF_PRODUCT_NAME = "SMTX ELF"


class SMTXELFLicenseManager:
    PRODUCT_NAME = "SMTX ELF"
    TEMPORARY_LICENSE_PERIOD = 30 * 24 * 60 * 60
    TEMPORARY_LICENSE_HOST_NUM = 255
    SIGN_DATE_PATH_IN_ZK = (
        "c2NyeXB0AA4AAAAIAAAAAd+4YTqt5nr3Uo8jeebVE+oTfSuNMcAQ9iIrZSbNRAHcDprwCMptQu/ViPBHGWYThCIfxZ5Cj5XdSJ/5yzwU/X8c0"
        "NbjR4FDuMLB3wdgcZKEIEOkdMtnCrA2VVUvDNVbTHDWSe7CPkFQDAmHrIzxyFFjXU8V3gQSw01oXinhmIBb8zkajOdQMwkAYTdW7MVkwntXrq"
        "DI424Z"
    )
    RECORD_OBJECT_ID = bson.ObjectId("6530912a6327b044d2f691a1")
    DB_NAME = "smartx"
    COLLECTION_NAME = "smtx_elf_license"

    def __init__(self, public_key: str = PUBLIC_KEY):
        self._public_key = crypto.load_publickey(crypto.FILETYPE_PEM, public_key)
        self._cert = crypto.X509()
        self._cert.set_pubkey(self._public_key)
        self._certificate: license_pb2.SMTXELFLicenseCertificate = None
        self._license_mtime: int = 0

    @property
    def _license(self) -> license_pb2.SMTXELFLicense:
        if self._certificate is None:
            self._load()

        return self._certificate.license

    def _get_temporary_license_sign_date(self):
        path = scrypt.decrypt(base64.b64decode(self.SIGN_DATE_PATH_IN_ZK), LICENSE_STORAGE_ENCRYPT_KEY)
        # To avoid zk bug, a new instance should be created every time and stopped explicitly.
        zk_client = None
        try:
            zk_client = zk.Zookeeper()
            license_sign_date = int(zk_client.zk_get(path))
            return license_sign_date
        finally:
            if zk_client:
                zk_client.stop()

    def _verify_license_content(self, elf_license: license_pb2.SMTXELFLicense):
        if elf_license.product_name != self.PRODUCT_NAME:
            raise base.ResourceException("License product name illegal", py_error.SMTX_ELF_NEW_LICENSE_FORMAT_ERROR)

        if elf_license.serial != cluster.get_cluster_id():
            raise base.ResourceException("License serial unmatch", py_error.SMTX_ELF_NEW_LICENSE_SERIAL_UNMATCH)

    def _verify_temporary_license(self, elf_license: license_pb2.SMTXELFLicense):
        self._verify_license_content(elf_license)
        self._verify_temporary_license_sign_date(elf_license)

    def _verify_temporary_license_sign_date(self, elf_license: license_pb2.SMTXELFLicense):
        if self._get_temporary_license_sign_date() != elf_license.sign_date:
            raise base.ResourceException("Temporary license sign_date unmatch", py_error.SMTX_ELF_LICENSE_INVALID)

    def _verify_signature(self, certificate: license_pb2.SMTXELFLicenseCertificate):
        elf_license = certificate.license
        signature = certificate.signature_hex
        try:
            crypto.verify(self._cert, bytes.fromhex(signature), elf_license.SerializeToString(), "md5")
        except Exception as e:
            raise base.ResourceException(
                "Verify official license failed, error={}".format(str(e)),
                py_error.SMTX_ELF_LICENSE_INVALID,
            )

    def _verify_certificate(self, certificate: license_pb2.SMTXELFLicenseCertificate):
        self._verify_signature(certificate)
        self._verify_license_content(certificate.license)

    def _get_license_period(self):
        period = self._license.period
        if self._certificate.signature_hex == "":
            period = self.TEMPORARY_LICENSE_PERIOD
        return period

    def _validate_expiration_of_specific_types(self, license_types: set[license_pb2.LicenseType]):
        self._load()
        period = self._get_license_period()
        license_type = self._license.license_type
        if self._certificate.signature_hex == "":
            license_type = license_pb2.LicenseType.TRIAL

        if license_type not in license_types:
            return

        if int(time.time()) - self._license.sign_date > period:
            raise base.ResourceException(
                "SMTX ELF {} license has expired. Sign_date={}, period={}".format(
                    license_pb2.LicenseType.Name(license_type), self._license.sign_date, period
                ),
                py_error.SMTX_ELF_LICENSE_EXPIRED,
            )

    def validate_can_operate_compute_resources(self):
        self._validate_expiration_of_specific_types({license_pb2.LicenseType.TRIAL})

    def validate_can_associate_storage_cluster(self):
        self._validate_expiration_of_specific_types(
            {license_pb2.LicenseType.TRIAL, license_pb2.LicenseType.SUBSCRIPTION}
        )

    def _load(self):
        elf_license = mongodb[self.DB_NAME][self.COLLECTION_NAME].find_one({"_id": self.RECORD_OBJECT_ID})
        if not elf_license:
            raise base.ResourceException("License record not found", py_error.SMTX_ELF_LICENSE_INVALID)

        try:
            if elf_license["mtime"] != self._license_mtime:
                encrypted_certificate_content = elf_license["certificate"]
                bare_certificate_content = base64.b64decode(encrypted_certificate_content)
                elf_certificate = license_pb2.SMTXELFLicenseCertificate()
                elf_certificate.ParseFromString(bare_certificate_content)

                if elf_certificate.signature_hex == "":
                    self._verify_temporary_license(elf_certificate.license)
                else:
                    self._verify_certificate(elf_certificate)

                self._certificate = elf_certificate
                self._license_mtime = elf_license["mtime"]
        except Exception as e:
            raise base.ResourceException(
                "Load license failed, error={}".format(str(e)), py_error.SMTX_ELF_LICENSE_INVALID
            )

    def _save(self, elf_certificate: license_pb2.SMTXELFLicenseCertificate, mtime=None):
        db_info = {
            "certificate": base64.b64encode(elf_certificate.SerializeToString()).decode("utf-8"),
            "mtime": mtime or int(time.time()),
            "_id": self.RECORD_OBJECT_ID,
        }
        mongodb[self.DB_NAME][self.COLLECTION_NAME].update_one(
            {"_id": self.RECORD_OBJECT_ID}, {"$set": db_info}, upsert=True
        )

    def show(self) -> dict:
        self._load()
        return self._dump_license(self._license)

    def show_in_protobuf(self) -> license_pb2.SMTXELFLicense:
        self._load()
        return copy.deepcopy(self._license)

    @staticmethod
    def _dump_license(elf_license: license_pb2.SMTXELFLicense):
        result = json_format.MessageToDict(
            elf_license, including_default_value_fields=True, preserving_proto_field_name=True
        )
        # According to https://protobuf.dev/programming-guides/proto3/#json, MessageToDict convert int64,uint64 and
        # fixed64 in proto to string in python. The value should be converted to int manually.
        for field in license_pb2.SMTXELFLicense.DESCRIPTOR.fields:
            if field.type in (
                descriptor.FieldDescriptor.TYPE_UINT64,
                descriptor.FieldDescriptor.TYPE_INT64,
                descriptor.FieldDescriptor.TYPE_FIXED64,
            ):
                field_name = field.name
                result[field_name] = int(result[field_name])

        return result

    def parse(self, certificate_hex_str: str):
        elf_certificate = license_pb2.SMTXELFLicenseCertificate()
        try:
            elf_certificate.ParseFromString(bytes.fromhex(certificate_hex_str))
            self._verify_signature(elf_certificate)
        except Exception as e:
            raise base.ResourceException(
                "Parse certificate failed, error={}".format(str(e)),
                py_error.SMTX_ELF_NEW_LICENSE_FORMAT_ERROR,
            )

        return self._dump_license(elf_certificate.license)

    def update(self, certificate_hex_str: str):
        new_certificate = license_pb2.SMTXELFLicenseCertificate()
        try:
            new_certificate.ParseFromString(bytes.fromhex(certificate_hex_str))
        except Exception as e:
            logging.warning("Parse new certificate from str failed, error={}".format(str(e)))
            raise base.ResourceException("New certificate format error", py_error.SMTX_ELF_NEW_LICENSE_FORMAT_ERROR)

        self._verify_new_certificate(new_certificate)
        self._save(new_certificate)

        logging.info("Successfully updated SMTX ELF license")

    def _verify_new_certificate(self, new_certificate):
        try:
            self._verify_certificate(new_certificate)
        except base.ResourceException as e:
            # when the certificate fails to verify, we should throw the SMTX_ELF_NEW_LICENSE_FORMAT_ERROR exception
            if e.user_code == py_error.SMTX_ELF_LICENSE_INVALID:
                raise base.ResourceException(
                    "Verify new certificate failed, error={}".format(str(e)), py_error.SMTX_ELF_NEW_LICENSE_FORMAT_ERROR
                )

            raise

        current_host_num = tuna.Client().get_management_hosts_summary()["num_hosts"]
        if new_certificate.license.max_host_num < current_host_num:
            raise base.ResourceException(
                "Host num of new certificate {} is less than current host num {}".format(
                    new_certificate.license.max_host_num, current_host_num
                ),
                py_error.SMTX_ELF_NEW_LICENSE_HOST_NUM_LESS_THAN_CURRENT,
            )

        time_now = int(time.time())
        if new_certificate.license.sign_date + new_certificate.license.period < time_now:
            logging.warning(
                "New license already expired, sign_date={}, period={}, time_now={}".format(
                    new_certificate.license.sign_date, new_certificate.license.period, time_now
                )
            )
            raise base.ResourceException("New license already expired", py_error.SMTX_ELF_NEW_LICENSE_ALREADY_EXPIRED)

    def generate_temporary_certificate(self) -> bool:
        if not platform.is_in_elf():
            return False

        # Check no certificate exists
        exist_certificate = mongodb[self.DB_NAME][self.COLLECTION_NAME].find_one({"_id": self.RECORD_OBJECT_ID})
        if exist_certificate:
            raise base.ResourceException(
                "SMTX ELF Certificate already exists", py_error.SMTX_ELF_LICENSE_ALREADY_EXISTS
            )

        try:
            sign_date = self._get_temporary_license_sign_date()
        except kazoo_exceptions.NoNodeError:
            raise base.ResourceException(
                "No cluster deploy finish time found", py_error.SMTX_ELF_LICENSE_GENERATE_FAILED_NO_SIGN_DATE
            )

        # generate_temporary_certificate is called from by executing 'elf-tool' CLI during deployment. At that time,
        # tuna-rest-server cannot be accessed. So cluster_uuid should be queried from local config file, instead of
        # tuna client
        temporary_certificate = license_pb2.SMTXELFLicenseCertificate(
            license=license_pb2.SMTXELFLicense(
                product_name=SMTX_ELF_PRODUCT_NAME,
                serial=cfg.DeployConfigManager().get_cluster_uuid(),
                software_edition=license_pb2.SoftwareEdition.ENTERPRISE,
                license_type=license_pb2.LicenseType.TRIAL,
                sign_date=sign_date,
                period=self.TEMPORARY_LICENSE_PERIOD,
                pricing_type=license_pb2.PricingType.PRICING_TYPE_UNKNOWN,
                max_host_num=self.TEMPORARY_LICENSE_HOST_NUM,
            ),
            signature_hex="",
        )
        self._save(temporary_certificate)
        return True

    def check_valid(self):
        self._validate_expiration_of_specific_types(
            {license_pb2.LicenseType.TRIAL, license_pb2.LicenseType.SUBSCRIPTION}
        )


class License:
    # virtual
    def check_valid(self):
        raise Exception("Not implemented")


global_smtx_elf_license_manager = None
if platform.is_in_elf():
    global_smtx_elf_license_manager = SMTXELFLicenseManager()


class SmartXOSLicense(License):
    def __init__(self, os_license):
        self._license = os_license
        self._expire_time = self._get_license_expire_time(os_license)
        self._next_refresh_time = self._expire_time

    @classmethod
    def _get_license(cls):
        client = zbs_client.ZbsMeta()
        os_license = client.license_show()

        return os_license

    @classmethod
    def _get_license_expire_time(cls, os_license):
        if os_license.license_type in (common_pb2.TRIAL, common_pb2.PERPETUAL):
            return os_license.sign_date + os_license.period
        elif os_license.license_type == common_pb2.SUBSCRIPTION:
            return os_license.subscription_start_date + os_license.subscription_period
        else:
            raise Exception("Unhandled license type {}".format(os_license.license_type))

    def _refresh(self, current_time):
        if current_time < self._next_refresh_time:
            return

        self._license = self._get_license()
        self._expire_time = self._get_license_expire_time(self._license)

        if current_time > self._expire_time:
            self._next_refresh_time = current_time + 60
        else:
            self._next_refresh_time = self._expire_time

    def check_valid(self):
        current_time = int(time.time())

        self._refresh(current_time)

        if current_time > self._expire_time:
            raise base.ResourceException("[License] License outdated", py_error.LICENSE_INSTALLED_IS_EXPIRED)

    @staticmethod
    def load() -> License:
        client = zbs_client.ZbsMeta()
        os_license = client.license_show()

        return SmartXOSLicense(os_license=os_license)


# Note that this class is NOT THREAD SAFE
class LicenseAccessController:
    def __init__(self):
        self._license = None
        self._initiated = False

    def _init_once(self):
        if self._initiated:
            return

        if platform.is_in_elf():
            self._license = global_smtx_elf_license_manager
        else:
            self._license = SmartXOSLicense.load()

        self._initiated = True

    def check_license_valid(self):
        self._init_once()
        self._license.check_valid()


global_license_access_controller = LicenseAccessController()


def validate_license(fn):
    @functools.wraps(fn)
    def wrapper(*args, **kwargs):
        global_license_access_controller.check_license_valid()

        return fn(*args, **kwargs)

    return wrapper
