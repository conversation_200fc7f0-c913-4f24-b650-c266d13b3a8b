# Copyright (c) 2013-2023, SMARTX
# All rights reserved.
import configparser
import fcntl
import logging
import os


class StorageClusterConfigManager:
    """
    Provides the ability to read the storage cluster from the config file and write back the config

    e.g. format
    [<zbs cluster uuid>]
    ip = ***********
    blockd_port = 10206
    iscsi_port = 3260

    Attributes:
        is_dirty (bool): Is the storage clusters different from the config file

    """

    # storage cluster
    OLD_STORAGE_CLUSTER_CONFIG = "/etc/smartx/storage_cluster.conf"
    STORAGE_CLUSTER_CONFIG = "/etc/elfvirt/storage_cluster.conf"

    def __init__(self):
        self._config = None
        self._is_dirty = False
        self._read_config()

    @property
    def storage_clusters(self):
        """
        Returns:
            [<storage_cluster uuid>, <storage_cluster uuid2>]
        """
        return self._config.sections()

    def _read_config(self):
        cfg_str = ""
        if not os.path.isfile(self.STORAGE_CLUSTER_CONFIG):
            logging.info(
                f"[StorageClusterConfigManager] Config file {self.STORAGE_CLUSTER_CONFIG} not found,"
                f" will create a new one when saving"
            )
        else:
            with open(self.STORAGE_CLUSTER_CONFIG) as f:
                fcntl.flock(f, fcntl.LOCK_SH)
                cfg_str = f.read()

        parser = configparser.RawConfigParser()
        parser.read_string(cfg_str)
        self._config = parser
        self._is_dirty = False

    def _save_config(self):
        with open(self.STORAGE_CLUSTER_CONFIG, "w+") as f:
            try:
                fcntl.flock(f, fcntl.LOCK_EX | fcntl.LOCK_NB)
                self._config.write(f)

                # If you're starting with a buffered Python file object f,
                # first do f.flush(), and then do os.fsync(f.fileno()),
                # to ensure that all internal buffers associated with f are written to disk.
                f.flush()
                os.fsync(f.fileno())
            except (BlockingIOError, OSError):
                raise RuntimeError(
                    f"[StorageClusterConfigManager] Failed to read config file {self.STORAGE_CLUSTER_CONFIG}"
                )

    def add_storage_cluster(self, uuid: str, storage_cluster_access_info: dict):
        """
        Args:
            uuid (str): uuid of the storage cluster
            storage_cluster_access_info (dict): {"ip": <ip>, "ports": <ports>}
        """
        if self._config.has_section(uuid):
            raise RuntimeError(f"[StorageClusterConfigManager] Storage cluster {uuid} already exists")

        self._config.add_section(uuid)
        self._config.set(uuid, "ip", storage_cluster_access_info["ip"])
        for key, value in storage_cluster_access_info["ports"].items():
            self._config.set(uuid, key, value)
        self._is_dirty = True

    def remove_storage_cluster(self, uuid: str):
        """
        Args:
            uuid (str): uuid of the storage cluster
        """
        if self._config.has_section(uuid):
            self._config.remove_section(uuid)
            self._is_dirty = True

    def update_storage_cluster_field(self, uuid, key, value):
        """
        Args:
            key (str): key of the storage cluster ini, "<uuid>.<field>"
            value (str): value of the storage cluster
        """
        if not self._config.has_section(uuid):
            raise RuntimeError(f"[StorageClusterConfigManager] Storage cluster {uuid} does not exist")

        if self._config[uuid][key] != value:
            self._config[uuid][key] = value
            self._is_dirty = True

    def save(self) -> bool:
        """
        Returns:
            bool: True if the config file is updated
        """
        if self._is_dirty:
            self._save_config()
            logging.info(f"[StorageClusterConfigManager] Storage cluster config is saved as {self.storage_clusters}")
            self._is_dirty = False
            return True
        return False

    @classmethod
    def rename_old_config(cls):
        if os.path.exists(cls.OLD_STORAGE_CLUSTER_CONFIG):
            os.rename(cls.OLD_STORAGE_CLUSTER_CONFIG, cls.STORAGE_CLUSTER_CONFIG)
            return True
        return False
