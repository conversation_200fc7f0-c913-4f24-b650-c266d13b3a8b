# Copyright (c) 2013-2023, SMARTX
# All rights reserved.
import json
import os


class BoostConfigManager:
    BOOST_MODE_FILE = "/etc/zbs/.boostenabled"

    def __init__(self):
        self._vhost_enabled = None
        self._vhost_io_permission_enabled = None

    @staticmethod
    def read_file(path):
        with open(path) as f:
            return f.read().strip()

    def is_boost_enabled(self):
        if self._vhost_enabled is None:
            self._vhost_enabled = False
            if os.path.isfile(self.BOOST_MODE_FILE):
                if self.read_file(self.BOOST_MODE_FILE) == "true":
                    self._vhost_enabled = True

        return self._vhost_enabled

    def is_vhost_io_permission_enabled(self):
        if self._vhost_io_permission_enabled is None:
            if is_in_elf():
                # In disaggregated arch, the boost mode has not been supported.
                # So we should consider that in the later version.
                self._vhost_io_permission_enabled = False
            else:
                # In hyperconvergence arch, with enabling boost mode,
                # the default zbs version has been supported `vhost io permission`.
                self._vhost_io_permission_enabled = self.is_boost_enabled()

        return self._vhost_io_permission_enabled


class DeployConfigManager:
    HOST_PLATFORM_PATH = "/etc/zbs/platform"

    # Definition copied from smartx_upgrade/upgrade_cluster/constants.py
    # SMTX OS
    PLATFORM_KVM = "kvm"
    # SMTX ZBS
    PLATFORM_SAN = "san"
    # SMTX ELF
    PLATFORM_ELF = "elf"

    def __init__(self):
        self._platform = ""
        self._is_in_kvm_or_san = None

    @staticmethod
    def read_config_file(path):
        with open(path) as f:
            return json.load(f)

    def is_in_kvm_or_san(self):
        if self._is_in_kvm_or_san is None:
            self._is_in_kvm_or_san = self.get_platform() in [
                DeployConfigManager.PLATFORM_KVM,
                DeployConfigManager.PLATFORM_SAN,
            ]
        return self._is_in_kvm_or_san

    def is_in_elf(self):
        return self.get_platform() == DeployConfigManager.PLATFORM_ELF

    # smartx_upgrade/upgrade_cluster/utils/platform_info.py
    def get_platform(self):
        if not self._platform:
            if os.path.isfile(self.HOST_PLATFORM_PATH):
                with open(self.HOST_PLATFORM_PATH) as f:
                    self._platform = f.read().strip()
        return self._platform


class CDPConfigManager:
    def __init__(self):
        self._cdp_enabled = None

    def is_cdp_enabled(self):
        if self._cdp_enabled is None:
            # In disaggregated arch, it is no `cdp` usage scenario.
            # The check enabled by cdp requires zbs support, so we do not
            # consider it temporarily.
            #
            # In hyperconvergence arch, the default zbs version
            # has been enabled `cdp`.
            self._cdp_enabled = is_in_elf() is False

        return self._cdp_enabled


_bcm = BoostConfigManager()
boost_enabled = _bcm.is_boost_enabled
vhost_io_permission_enabled = _bcm.is_vhost_io_permission_enabled


_dcm = DeployConfigManager()
is_in_kvm_or_san = _dcm.is_in_kvm_or_san
is_in_elf = _dcm.is_in_elf

_cdp_cm = CDPConfigManager()
cdp_enabled = _cdp_cm.is_cdp_enabled
