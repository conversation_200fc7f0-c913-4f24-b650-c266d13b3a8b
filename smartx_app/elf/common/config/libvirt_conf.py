# Copyright (c) 2013, elfvirt
# All rights reserved.
import configparser
import hashlib
import logging
import os


class LibvirtConfig:
    LIBVIRTD_CONFIG_FILE = "/etc/libvirt/libvirtd.conf"
    CACERT = "/etc/pki/CA/cacert.pem"
    CONFIG_SECTION = "libvirt"
    TRANSPORTS = ("tls", "tcp")

    def __init__(self):
        self._config = None
        self._read_config()
        self._cert_hash = None
        self._transports = None
        self._default_transport = None

    def _read_config(self):
        if not os.path.isfile(self.LIBVIRTD_CONFIG_FILE):
            raise Exception("[Libvirt] Config file {} not found".format(self.LIBVIRTD_CONFIG_FILE))

        self._config = configparser.ConfigParser()
        with open(self.LIBVIRTD_CONFIG_FILE) as f:
            # We add a dummy section title so that it could be parsed as ini.
            config = "[{}]\n".format(self.CONFIG_SECTION) + f.read()
            self._config.read_string(config)

    @property
    def supported_transports(self):
        if self._transports is None:
            _transports = []

            for transport in self.TRANSPORTS:
                if self._config.getint(self.CONFIG_SECTION, "listen_{}".format(transport), fallback=0) == 1:
                    _transports.append(transport)

            self._transports = _transports

        return self._transports

    @property
    def tcp_port(self):
        return self._config.get(self.CONFIG_SECTION, "tcp_port", fallback="16509").strip('"')

    @property
    def sasl_info(self):
        # Auth methods like md5 or scram-sha-256 are not supported yet.
        return None

    @property
    def cacert_hash(self):
        if not self._cert_hash:
            try:
                with open(self.CACERT) as f:
                    self._cert_hash = hashlib.md5(f.read().encode("utf-8")).hexdigest()
            except Exception as e:
                logging.error("Failed to read {}: {}".format(self.CACERT, str(e)))

        return self._cert_hash

    def tls_supported(self):
        return "tls" in self.supported_transports

    @property
    def default_transport(self):
        if not self._default_transport:
            self._default_transport = "tls" if self.tls_supported() else "tcp"

        return self._default_transport

    @property
    def default_libvirt_access_info(self):
        return {
            "transport": self.default_transport,
            "sasl": None,
            # In case, if supported, sasl should be formatted like:
            # {
            #     "mech": <mech type>,
            #     "username": None,
            #     "password": None,
            # }
        }


global_libvirt_conf = LibvirtConfig()
