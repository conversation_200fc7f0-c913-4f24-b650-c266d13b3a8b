# Copyright (c) 2013-2017, SMARTX
# All rights reserved.
from common.config.resources import RESOURCE_IN_USE
from smartx_app.elf.common.resource_query.base import AttributeQuerierMixin, BaseQuerier, PagingMixin
from smartx_app.elf.common.utils import resource
from smartx_app.elf.common.utils.resource import pop_nic_pci_address
from smartx_app.elf.job_center.constants import KVM_VM, KVM_VM_SNAPSHOT


class VMSnapshotQuerier(PagingMixin, AttributeQuerierMixin, BaseQuerier):
    def __init__(self):
        BaseQuerier.__init__(
            self,
            resource_super_type=None,
            resource_types=[KVM_VM_SNAPSHOT],
            regex_search_fields_support=["name"],
            sort_fields_support=["name", "vm_uuid", ("unique_size", int), ("create_time", int)],
        )
        self.default_sort_criteria = "-create_time"

    def _try_init_unique_size(self):
        doc = self.collection.find_one(
            {"type": KVM_VM_SNAPSHOT, "resource_state": RESOURCE_IN_USE, "unique_size": {"$exists": True}}
        )

        if doc is None:
            self.collection.update_many(
                {"type": KVM_VM_SNAPSHOT, "resource_state": RESOURCE_IN_USE, "unique_size": {"$exists": False}},
                {"$set": {"unique_size": 0}},
            )

        return not bool(doc)

    def _get_vms(self, vm_uuid_list):
        cursor = self.collection.find(
            {"type": KVM_VM, "resource_state": RESOURCE_IN_USE, "uuid": {"$in": vm_uuid_list}},
            {"_id": 0, "uuid": 1, "vm_name": 1},
        )

        return {x["uuid"]: x["vm_name"] for x in cursor}

    def apply_stats(self, resources):
        if resources:
            list(map(pop_nic_pci_address, resources))

            vms = self._get_vms([x["vm_uuid"] for x in resources])
            for res in resources:
                if res["vm_uuid"] in vms:
                    res["vm_name"] = vms.get(res["vm_uuid"], None)
                else:
                    # vm has been deleted
                    res["vm_uuid"] = res["vm_name"] = None

                resource.normalize_nic(res["nics"])

        return super().apply_stats(resources)

    def ensure_paging_indexes(self, use_super_type=False):
        self._try_init_unique_size()
        super().ensure_paging_indexes(False)

    def _page_query(
        self,
        count=50,
        skip_page=1,
        sort_criteria=None,
        filter_criteria=None,
        projection=None,
        start_uuid=None,
        end_uuid=None,
        skip_to_last=False,
        **kwargs,
    ):
        # The `vm_uuid` field is required for statistics.
        if projection:
            projection += ",vm_uuid"

        return super()._page_query(
            count=count,
            skip_page=skip_page,
            sort_criteria=sort_criteria,
            filter_criteria=filter_criteria,
            projection=projection,
            start_uuid=start_uuid,
            end_uuid=end_uuid,
            skip_to_last=skip_to_last,
            **kwargs,
        )
