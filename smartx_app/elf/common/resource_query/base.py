# Copyright (c) 2013-2017, SMARTX
# All rights reserved.
import logging

from common.config.resources import RESOURCE_IN_USE
from common.mongo.db import mongodb
from smartx_app.elf.common.resources.attribute import Attribute
from smartx_app.elf.common.resources.base import ResourceException
from smartx_app.elf.common.resources.folder import Folder
from smartx_app.elf.common.utils.mongo import generate_regex_text_query_criteria
from smartx_proto.errors import pyerror_pb2 as py_error

logger = logging.getLogger(__file__)


class FieldConverter:
    def __init__(self, fields):
        if fields is None:
            self.field_names = ()
            self.field_converters = {}
        else:
            self.field_names = []
            self.field_converters = {}
            for field in fields:
                if isinstance(field, tuple | list):
                    self.field_names.append(field[0])
                    self.field_converters.setdefault(field[0], self.get_bool if field[1] is bool else field[1])
                else:
                    self.field_names.append(field)

    @classmethod
    def get_bool(cls, value):
        return True if value and value.lower() == "true" else False

    @classmethod
    def default(cls, value):
        return value

    def convert(self, raw_values):
        if raw_values:
            return {k: self.field_converters.get(k, self.default)(v) for k, v in list(raw_values.items())}

        return raw_values


class CriteriaResolver:
    # To support building `{"$ne": None}` query criteria internally.
    NOT_NULL = "NotNull-EB3E67C0-69AE-4998-B8B0-FCB470B21BC8"

    def __init__(
        self,
        sort_fields_support,
        filter_fields_locked,
        projection_fields_omitted,
        auto_protect_field=True,
    ):
        self.sort_fields_support = sort_fields_support or ()
        self.filter_fields_locked = filter_fields_locked or ()
        self.projection_fields_omitted = projection_fields_omitted or ("_id",)
        self.auto_protect_field = auto_protect_field

    def resolve_sort_criteria(self, sort_criteria, sort_field_default=None):
        """
        :param sort_criteria:       The sort criteria, eg. `name`(asc by name)
                                    or `-name`(desc by name).
        :param sort_field_default:
        :return:
        """
        if sort_criteria:
            if sort_criteria in self.sort_fields_support:
                return sort_criteria, 1

            if sort_criteria.startswith("-") and sort_criteria[1:] in self.sort_fields_support:
                return sort_criteria[1:], -1

        return sort_field_default, 1

    def resolve_filter_criteria(self, filter_criteria, sort_fields_only=False):
        """
        :param filter_criteria:   The filter criteria, eg. `status=running;name=vm`
        :param sort_fields_only:  The filter filed must be a sort field.
        :return:
        """
        resolve_filter_criteria = {}
        if filter_criteria:
            for x in filter_criteria.split(";"):
                name, _, value = x.partition("=")
                if name not in self.filter_fields_locked:
                    # warning:
                    # 1. don't care the type of the value, all as string.
                    # 2. not support the empty string.
                    if value == self.NOT_NULL:
                        resolve_filter_criteria.setdefault(name, {"$ne": None})
                    elif value == "":
                        resolve_filter_criteria.setdefault(name, {"$type": 10})
                    else:
                        resolve_filter_criteria.setdefault(name, value)

        if sort_fields_only and resolve_filter_criteria:
            resolve_filter_criteria = {
                k: v for k, v in list(resolve_filter_criteria.items()) if k in self.sort_fields_support
            }

        return resolve_filter_criteria

    def resolve_projection(self, projection):
        """
        :param projection:  which fields return in the result, eg. `uuid, name`
        :return:
        """
        resolve_projection = {x: 0 for x in self.projection_fields_omitted}
        if projection:
            for x in projection.split(","):
                resolve_projection.setdefault(x.strip(), 1)

            if self.auto_protect_field:
                # Must return the `uuid` filed.
                resolve_projection["uuid"] = 1

        return resolve_projection


class BaseQuerier:
    SEARCH_MAX_COUNT = 1000
    PROJECTION_FIELDS_OMITTED = ("_id",)

    def __init__(self, resource_super_type, resource_types, regex_search_fields_support, sort_fields_support):
        self.resource_super_type = resource_super_type
        self.resource_types = resource_types or []
        self.regex_search_fields_support = regex_search_fields_support or []
        # sort_fields_support: ["field_name_1", ("field_name_2", int)]
        self.sort_fields_support = sort_fields_support

        self._collection = None

    @property
    def collection(self):
        if self._collection is None:
            self._collection = mongodb.resources.resource

        return self._collection

    def generate_common_query_criteria(self):
        query_criteria = {"resource_state": RESOURCE_IN_USE}
        if self.resource_super_type:
            query_criteria["super_type"] = self.resource_super_type
        elif self.resource_types:
            if len(self.resource_types) > 1:
                query_criteria["type"] = {"$in": list(self.resource_types)}
            else:
                query_criteria["type"] = self.resource_types[0]
        else:
            logging.warning("`resource_types` of {} is not specified.".format(type(self)))

        return query_criteria

    def generate_common_query_projection(self):
        return {"_id": 0}

    def generate_regex_text_query_criteria(self, search_fields, text):
        query_criteria = self.generate_common_query_criteria()
        query_criteria.update(generate_regex_text_query_criteria(search_fields, text))
        return query_criteria

    def _search(self, text, limit, filter_criteria=None):
        if not text:
            return []

        projection = {"_id": 0}
        query_criteria = self.generate_regex_text_query_criteria(self.regex_search_fields_support, text)
        if filter_criteria:
            query_criteria.update(filter_criteria)

        result = list(self.collection.find(query_criteria, projection).limit(limit))

        logging.info(
            "(regex)fuzzy_search.find({}, {}).limit({}) return {} docs".format(
                query_criteria, projection, limit, len(result)
            )
        )

        return result

    def search(self, text, limit=None, filter_criteria=None):
        try:
            sort_field_converter = FieldConverter(self.sort_fields_support or ("uuid",))
            c_resolver = CriteriaResolver(
                sort_fields_support=sort_field_converter.field_names,
                filter_fields_locked=("super_type", "type", "resource_state"),
                projection_fields_omitted=self.PROJECTION_FIELDS_OMITTED,
            )
            filter_criteria = sort_field_converter.convert(
                c_resolver.resolve_filter_criteria(filter_criteria, sort_fields_only=True)
            )

            if not (limit and 0 < limit <= self.SEARCH_MAX_COUNT):
                limit = self.SEARCH_MAX_COUNT

            resources = self._search(text, limit, filter_criteria=filter_criteria)
            if resources:
                self.apply_stats(resources)

            return {
                "limit": limit,
                "filter_criteria": filter_criteria,
                "total_entities": len(resources),
                "value": text,
                "type": self.resource_types,
                "entities": resources,
            }
        except Exception as e:
            msg = "Resource search exception: {}".format(str(e))
            logging.exception(msg)
            raise ResourceException(msg, py_error.RESOURCE_SEARCH_ERROR)

    def generate_query_projection(self):
        return self.generate_common_query_projection()

    def query(self, query_criteria, projection=None, top=50):
        """
        :param query_criteria:  filter criteria
        :param projection:      projection
        :param top:             the maximum number of results to return.
        :return:
        """
        try:
            if projection:
                current_projection = self.generate_query_projection()
                current_projection.update(projection)
            else:
                current_projection = self.generate_common_query_projection()

            query = self.generate_common_query_criteria()
            for k, v in list(query_criteria.items()):
                query.setdefault(k, v)

            resources = list(self.collection.find(query, current_projection).limit(top))

            if resources:
                self.apply_stats(resources)

            return resources
        except Exception as e:
            msg = "Resource query exception: {}".format(str(e))
            logging.exception(msg)
            raise ResourceException(msg, py_error.RESOURCE_QUERY_ERROR)

    def get(self, res_uuid):
        result = self.query({"uuid": res_uuid})
        return result[0] if result else None

    def apply_stats(self, resources):
        return resources


class AttributeQuerierMixin:
    def _search_from_attribute(self, attribute_key, filter_criteria):
        projection = {"_id": 0}
        ids = Attribute.batch_get_resource_id_by_key([attribute_key], self.resource_types[0])
        folder_uuid = None
        query_criteria = self.generate_common_query_criteria()

        if filter_criteria:
            if "folder_uuid" in filter_criteria:
                folder_uuid = filter_criteria.pop("folder_uuid")
            query_criteria.update(filter_criteria)

        if folder_uuid:
            folder_vms_uuid = Folder.load(folder_uuid).relation()["vm_uuids"]
            ids = list(set(ids) & set(folder_vms_uuid))

        query_criteria["uuid"] = {"$in": ids}
        return list(self.collection.find(query_criteria, projection))

    def search(self, text, limit=None, filter_criteria=None):
        try:
            sort_field_converter = FieldConverter(self.sort_fields_support or ("uuid",))
            c_resolver = CriteriaResolver(
                sort_fields_support=sort_field_converter.field_names,
                filter_fields_locked=("super_type", "type", "resource_state"),
                projection_fields_omitted=self.PROJECTION_FIELDS_OMITTED,
            )
            filter_criteria = sort_field_converter.convert(
                c_resolver.resolve_filter_criteria(filter_criteria, sort_fields_only=True)
            )

            if not (limit and 0 < limit <= self.SEARCH_MAX_COUNT):
                limit = self.SEARCH_MAX_COUNT

            result = []
            if text[0] in Attribute.get_keys():
                result = self._search_from_attribute(text[0], filter_criteria)
                limit -= len(result)

            result += self._search(text, limit, filter_criteria=filter_criteria)

            record = []
            resources = []
            for r in result:
                if r["uuid"] not in record:
                    resources.append(r)
                    record.append(r["uuid"])

            if resources:
                self.apply_stats(resources)

            return {
                "limit": limit,
                "filter_criteria": filter_criteria,
                "total_entities": len(resources),
                "value": text,
                "type": self.resource_types,
                "entities": resources,
            }
        except Exception as e:
            msg = "Resource search exception: {}".format(str(e))
            logging.exception(msg)
            raise ResourceException(msg, py_error.RESOURCE_SEARCH_ERROR)


class _PagingExecutor:
    """Just used by :class:`PagingExecutor` to perform the paging query."""

    MAX_COUNT = 1024
    MAX_SKIP_COUNT = 5000
    GREATER_THAN = "$gt"
    LESS_THAN = "$lt"
    SORT_FIELD_DEFAULT = "uuid"
    FILTER_FIELDS_LOCKED = ("super_type", "type", "resource_state")
    PROJECTION_FIELDS_OMITTED = ("_id",)

    def __init__(
        self,
        collection,
        resource_super_type,
        resource_type,
        sort_fields_support=None,
        count=50,
        skip_page=1,
        sort_criteria=None,
        filter_criteria=None,
        projection=None,
        start_uuid=None,
        end_uuid=None,
    ):
        if not any([resource_super_type, resource_type]):
            raise ValueError("Must specify one of [resource_super_type, resource_type].")

        self.collection = collection or mongodb.resources.resource
        self.resource_super_type = resource_super_type
        self.resource_type = resource_type

        self.count = count if count < self.MAX_COUNT else self.MAX_COUNT
        self.skip_page = (
            skip_page
            if abs(skip_page) * self.count < self.MAX_SKIP_COUNT
            else self.MAX_SKIP_COUNT // self.count * (skip_page // abs(skip_page))
        )
        self.start_uuid = start_uuid
        self.end_uuid = end_uuid

        sort_field_converter = FieldConverter(sort_fields_support or (self.SORT_FIELD_DEFAULT,))
        c_resolver = CriteriaResolver(
            sort_fields_support=sort_field_converter.field_names,
            filter_fields_locked=self.FILTER_FIELDS_LOCKED,
            projection_fields_omitted=self.PROJECTION_FIELDS_OMITTED,
        )
        self.projection = c_resolver.resolve_projection(projection)
        self.filter_criteria = sort_field_converter.convert(
            c_resolver.resolve_filter_criteria(filter_criteria, sort_fields_only=True)
        )
        self.sort_field, self.sort_order = c_resolver.resolve_sort_criteria(
            sort_criteria, sort_field_converter.field_names[0]
        )

        if self.filter_criteria and self.sort_field not in self.filter_criteria:
            logging.warning(
                "`sort_field`({}) not in `filter_criteria`({})".format(self.sort_field, self.filter_criteria)
            )

    def page_query(self):
        filter_criteria = self._generate_query_criteria()[0]
        total_entities = self.collection.find(filter_criteria).count()

        if total_entities > 0:
            docs = self._page_query()
        else:
            docs = []

        return {
            "entities": docs,
            "start_uuid": docs[0]["uuid"] if docs else None,
            "end_uuid": docs[-1]["uuid"] if docs else None,
            "filter_criteria": filter_criteria,
            "sort_criteria": "{}{}".format("" if self.sort_order > 0 else "-", self.sort_field),
            "count": self.count,
            "page_entities": len(docs),
            "total_entities": total_entities,
        }

    def _page_query(self):
        if not any([self.resource_type, self.resource_super_type]):
            return []

        def _reverse_if_necessary(resources):
            if self.skip_page < 0:
                resources.reverse()
            return resources

        query_criteria = self._resolve_query_criteria()
        sort_criteria = self._generate_sort_criteria()

        logging.info(
            "prepared paging query criteria is {} and sort criteria is {}".format(query_criteria, sort_criteria)
        )

        for _ in range(3):
            docs = []
            skip_count = (abs(self.skip_page) - 1) * self.count

            # query and merge
            for index, query in enumerate(query_criteria):
                limit_count = self.count - len(docs)
                if limit_count > 0:
                    # Need to skip
                    if len(docs) == 0:
                        docs.extend(
                            _reverse_if_necessary(
                                list(
                                    self.collection.find(query, self.projection)
                                    .sort(sort_criteria)
                                    .skip(skip_count)
                                    .limit(limit_count)
                                )
                            )
                        )
                        logging.info(
                            "paging-execute find({}, {}).sort({}).skip({}).limit({}) return {} docs".format(
                                query,
                                self.projection,
                                sort_criteria,
                                skip_count,
                                limit_count,
                                limit_count - (self.count - len(docs)),
                            )
                        )

                        # Skip all records that match the query criteria,
                        # so recalculate the `skip_count`.
                        if len(docs) == 0 and index + 1 < len(query_criteria):
                            skip_count -= self.collection.find(query).count()
                            if skip_count < 0:
                                logging.warning("The collection has changed between the two operations, so retry it")
                                break
                    else:
                        # Don't need to skip.
                        docs.extend(
                            _reverse_if_necessary(
                                list(
                                    self.collection.find(query, self.projection).sort(sort_criteria).limit(limit_count)
                                )
                            )
                        )

                        logging.info(
                            "paging-execute find({}, {}).sort({}).limit({}) return {} docs".format(
                                query,
                                self.projection,
                                sort_criteria,
                                limit_count,
                                limit_count - (self.count - len(docs)),
                            )
                        )
            else:
                return docs
        else:
            return []

    def _generate_sort_criteria(self):
        if self.skip_page < 0:
            current_sort_order = -1 if self.sort_order == 1 else 1
        else:
            current_sort_order = self.sort_order

        if self.sort_field != "uuid":
            return [(self.sort_field, current_sort_order), ("uuid", current_sort_order)]
        else:
            return [(self.sort_field, current_sort_order)]

    def _get_comparison_operator(self):
        if self.skip_page > 0 and self.end_uuid is not None:
            return (
                self.GREATER_THAN if self.sort_order > 0 else self.LESS_THAN,
                self.end_uuid,
            )

        if self.skip_page < 0 and self.start_uuid is not None:
            return (
                self.LESS_THAN if self.sort_order > 0 else self.GREATER_THAN,
                self.start_uuid,
            )

        return None, None

    def _generate_query_criteria(self, other_criteria=None):
        if self.resource_super_type:

            def _g(criteria):
                q = {"super_type": self.resource_super_type, "resource_state": RESOURCE_IN_USE}
                q.update(self.filter_criteria)

                if criteria:
                    q.update(criteria)

                return q

        else:

            def _g(criteria):
                q = {"type": self.resource_type, "resource_state": RESOURCE_IN_USE}
                q.update(self.filter_criteria)

                if criteria:
                    q.update(criteria)

                return q

        return list(map(_g, other_criteria or [None]))

    def _resolve_query_criteria(self):
        operator, location_uuid = self._get_comparison_operator()
        if operator and location_uuid:
            location_doc = self.collection.find_one({"uuid": location_uuid}, {"_id": 0, self.sort_field: 1})

            if location_doc:
                return self._generate_query_criteria(
                    [
                        {self.sort_field: location_doc[self.sort_field], "uuid": {operator: location_uuid}},
                        {self.sort_field: {operator: location_doc[self.sort_field]}},
                    ]
                )

        return self._generate_query_criteria()


class PagingMixin:
    """Mixin for :class:`BaseQuerier` subclasses.  Classes that inherit from
    this mixin will automatically get a :method:`page_query` method that
    provides paging-query interface.
    """

    def ensure_paging_indexes(self, use_super_type=False):
        from pymongo import ASCENDING

        for field in self.sort_fields_support:
            if isinstance(field, tuple | list):
                field_name = field[0]
            else:
                field_name = field

            if field_name in ("type", "resource_state", "uuid"):
                continue

            self.collection.create_index(
                [
                    ("super_type" if use_super_type else "type", ASCENDING),
                    ("resource_state", ASCENDING),
                    (field_name, ASCENDING),
                    ("uuid", ASCENDING),
                ],
                background=True,
                sparse=True,
            )

    def _page_query(
        self,
        count=50,
        skip_page=1,
        sort_criteria=None,
        filter_criteria=None,
        projection=None,
        start_uuid=None,
        end_uuid=None,
        skip_to_last=False,
        **kwargs,
    ):
        # Jump to the last page that is get the first page of reverse order.
        if skip_to_last and skip_page > 0:
            original_sort_criteria = sort_criteria or getattr(self, "default_sort_criteria", "uuid")
            if original_sort_criteria[:1] == "-":
                current_sort_criteria = original_sort_criteria[1:]
            else:
                current_sort_criteria = "-" + original_sort_criteria

            data = _PagingExecutor(
                collection=self.collection,
                resource_super_type=self.resource_super_type,
                resource_type=self.resource_types[0],
                sort_fields_support=self.sort_fields_support,
                count=count,
                skip_page=1,
                sort_criteria=current_sort_criteria,
                filter_criteria=filter_criteria,
                projection=projection,
                start_uuid=None,
                end_uuid=None,
            ).page_query()

            # Get the correct count of docs of the last page.
            last_page_count = data["total_entities"] - (data["total_entities"] // count * count)
            if last_page_count > 0:
                data["entities"] = data["entities"][:last_page_count]
                data["page_entities"] = last_page_count

            # Adjust the result.
            data["sort_criteria"] = original_sort_criteria
            data["entities"].reverse()
            if data["entities"]:
                data["start_uuid"] = data["entities"][0]["uuid"]
                data["end_uuid"] = data["entities"][-1]["uuid"]
            else:
                data["end_uuid"] = data["start_uuid"] = None
        else:
            data = _PagingExecutor(
                collection=self.collection,
                resource_super_type=self.resource_super_type,
                resource_type=self.resource_types[0],
                sort_fields_support=self.sort_fields_support,
                count=count,
                # Jump to the first page.
                skip_page=1 if skip_to_last else skip_page,
                sort_criteria=sort_criteria or self.default_sort_criteria,
                filter_criteria=filter_criteria,
                projection=projection,
                start_uuid=start_uuid,
                end_uuid=end_uuid,
            ).page_query()

        self.apply_stats(data["entities"])

        return data

    def page_query(
        self,
        count=50,
        skip_page=1,
        sort_criteria=None,
        filter_criteria=None,
        projection=None,
        start_uuid=None,
        end_uuid=None,
        skip_to_last=False,
        **kwargs,
    ):
        try:
            return self._page_query(
                count=count,
                skip_page=skip_page,
                sort_criteria=sort_criteria,
                filter_criteria=filter_criteria,
                projection=projection,
                start_uuid=start_uuid,
                end_uuid=end_uuid,
                skip_to_last=skip_to_last,
                **kwargs,
            )
        except Exception as e:
            msg = "Resource paging query exception: {}".format(str(e))
            logging.exception(msg)
            raise ResourceException(msg, py_error.RESOURCE_PAGING_QUERY_ERROR)


class FullTextSearchMixin:
    """If mixed in before the :class:`BaseQuerier` subclasses,
    FullTextSearchMixin will change the search behavior of it to use full-text
    search.
    """

    def generate_full_text_query_criteria(self, text):
        if not isinstance(text, tuple | list):
            text = [text]

        query_criteria = self.generate_common_query_criteria()
        # The text operator treats most punctuation in the string as
        # delimiters, except a hyphen-minus (-) that negates term or
        # an escaped double quotes \" that specifies a phrase.
        # ref: https://docs.mongodb.com/manual/reference/operator/
        # query/text/#search-field

        query_criteria.update(
            {
                "$text": {
                    "$search": " ".join(['"{}"'.format(g) for g in text]),
                    # not support in v2.6
                    # "$caseSensitive": False
                }
            }
        )
        return query_criteria

    def _search(self, text, limit, **kwargs):
        if not text:
            return []

        if not isinstance(text, list):
            text = [text]

        # filtering is not support
        query_criteria = self.generate_full_text_query_criteria(text)
        projection = {"_id": 0, "score": {"$meta": "textScore"}}
        sort_criteria = [("score", {"$meta": "textScore"})]

        query = self.collection.find(query_criteria, projection)

        if sort_criteria:
            query = query.sort(sort_criteria)
        result = list(query.limit(limit))
        logging.info(
            "(full-text)fuzzy_search.find({}, {}).sort({}).limit({}) return {} docs".format(
                query_criteria, projection, sort_criteria, limit, len(result)
            )
        )

        return result


class PagingParameterExtractor:
    MAX_COUNT = _PagingExecutor.MAX_COUNT
    MAX_SKIP_COUNT = _PagingExecutor.MAX_SKIP_COUNT
    # sugar does not support to check the type of
    # query parameter, so set as `string` type.
    PAGING_PARAMETERS_SCHEMA = {
        "count": {"type": "integer", "minimum": 1, "maximum": _PagingExecutor.MAX_COUNT},
        "skip_page": {"type": "integer"},
        "sort_criteria": {"type": "string", "pattern": r"\S+"},
        "filter_criteria": {"type": "string", "pattern": r"\S+"},
        "projection": {"type": "string", "pattern": r"\S+"},
        "start_uuid": {"type": "string", "pattern": r"\S+"},
        "end_uuid": {"type": "string", "pattern": r"\S+"},
    }

    def __init__(self, default_count=50, default_skip_page=1):
        self.default_count = default_count
        self.default_skip_page = default_skip_page

    def extract(self, data):
        count = data.get("count", self.default_count)
        skip_page = data.get("skip_page", self.default_skip_page)

        if count > self.MAX_COUNT or count < 1:
            count = self.default_count

        if skip_page == 0:
            skip_page = self.default_skip_page

        if count * abs(skip_page) > self.MAX_SKIP_COUNT:
            skip_page = skip_page // abs(skip_page) * (self.MAX_SKIP_COUNT // count)
            skip_to_last = True
        else:
            skip_to_last = False

        return {
            "count": count,
            "skip_page": skip_page,
            "sort_criteria": data.get("sort_criteria", None),
            "filter_criteria": data.get("filter_criteria", None),
            "projection": data.get("projection", None),
            "start_uuid": data.get("start_uuid", None),
            "end_uuid": data.get("end_uuid", None),
            "skip_to_last": skip_to_last,
        }
