# Copyright (c) 2013-2017, SMARTX
# All rights reserved.
import logging

from common.config.resources import RESOURCE_IN_USE
from common.mongo.db import mongodb
from smartx_app.common import constants
from smartx_app.elf.common.resource_query.base import (
    Attribute<PERSON><PERSON>ier<PERSON><PERSON><PERSON>,
    BaseQuerier,
    CriteriaResolver,
    FieldConverter,
    PagingMixin,
)
from smartx_app.elf.common.resource_wrappers.vm_additional_info_manager import VMAdditionalInfoWrapper
from smartx_app.elf.common.resources import base
from smartx_app.elf.common.resources.attribute import Attribute
from smartx_app.elf.common.resources.folder import Folder
from smartx_app.elf.common.utils import cloud_init, pci_address, resource
from smartx_app.elf.common.utils.dhcp import fill_in_ip_for_vms
from smartx_app.elf.job_center.constants import KVM_VM
from smartx_proto.errors import pyerror_pb2 as py_error


class VMMixedPageQuery:
    MAX_COUNT = 1024
    MAX_SKIP_COUNT = 5000

    def __init__(
        self,
        collection,
        resource_type,
        sort_fields_support,
        attribute_key_sort_field,
        count=50,
        skip_page=1,
        sort_criteria=None,
        filter_criteria=None,
        projection=None,
        start_uuid=None,
        end_uuid=None,
        current_page=0,
        skip_to_last=False,
    ):
        if not resource_type:
            raise ValueError("Must specify one of resource_type.")

        self.collection = collection or mongodb.resources.resource
        self.resource_type = resource_type

        self.count = count if count < self.MAX_COUNT else self.MAX_COUNT
        self.skip_page = (
            skip_page
            if abs(skip_page) * self.count < self.MAX_SKIP_COUNT
            else self.MAX_SKIP_COUNT // self.count * (skip_page // abs(skip_page))
        )
        self.start_uuid = start_uuid
        self.end_uuid = end_uuid
        self.attribute_key_sort_field = attribute_key_sort_field

        sort_field_converter = FieldConverter(sort_fields_support + attribute_key_sort_field)

        c_resolver = CriteriaResolver(
            sort_fields_support=sort_field_converter.field_names,
            filter_fields_locked=("super_type", "type", "resource_state"),
            projection_fields_omitted=("_id",),
        )
        self.projection = c_resolver.resolve_projection(projection)
        self.filter_criteria = sort_field_converter.convert(
            c_resolver.resolve_filter_criteria(filter_criteria, sort_fields_only=True)
        )
        self.sort_field, self.sort_order = c_resolver.resolve_sort_criteria(
            sort_criteria, sort_field_converter.field_names[0]
        )

        if self.filter_criteria and self.sort_field not in self.filter_criteria:
            logging.warning(
                "`sort_field`({}) not in `filter_criteria`({})".format(self.sort_field, self.filter_criteria)
            )
        # Starting on page 0
        self.current_page = current_page if current_page == 0 else current_page - 1
        self.skip_to_last = skip_to_last

    def _generate_query_criteria(self, other_criteria=None):
        def _g(criteria):
            q = {"type": self.resource_type, "resource_state": RESOURCE_IN_USE}
            q.update(self.filter_criteria)

            if criteria:
                q.update(criteria)

            return q

        return list(map(_g, other_criteria or [None]))

    def _generate_sort_criteria(self):
        if self.skip_page < 0:
            current_sort_order = -1 if self.sort_order == 1 else 1
        else:
            current_sort_order = self.sort_order

        if self.sort_field != "uuid":
            return [(self.sort_field, current_sort_order), ("uuid", current_sort_order)]
        else:
            return [(self.sort_field, current_sort_order)]

    def page_query(self):
        sort_criteria = self._generate_sort_criteria()
        query_criteria = self._generate_query_criteria()

        logging.info(
            "prepared paging query criteria is {} and sort criteria is {}".format(query_criteria, sort_criteria)
        )

        attribute_key_sort_criteria = None
        if len(sort_criteria) > 1 and sort_criteria[0][0] in self.attribute_key_sort_field:
            attribute_key_sort_criteria = sort_criteria.pop(0)

        folder_uuid = None
        # Folder without additional sorting
        # query all uuid with sort
        query = query_criteria[0]
        if "folder_uuid" in query:
            folder_uuid = query.pop("folder_uuid")

        docs = self.collection.find(query, {"_id": 0, "uuid": 1}).sort(sort_criteria)
        vms = [x["uuid"] for x in docs]

        if folder_uuid:
            vms = Folder.load(folder_uuid).intersection(vms, keep_order=True)

        if attribute_key_sort_criteria:
            vms = Attribute.merge_sort_by_key(
                key=attribute_key_sort_criteria[0],
                resource_type=KVM_VM,
                reverse=not bool(attribute_key_sort_criteria[1]),
                vm_uuids=vms,
            )

        total_entities = len(vms)

        if self.skip_to_last and self.skip_page > 0:
            last_page_size = total_entities % self.count
            vms_uuid = vms[-last_page_size:]
        else:
            try:
                if self.skip_page > 0 and self.end_uuid is not None:
                    ind = vms.index(self.end_uuid) + 1
                    vms_uuid = vms[ind : ind + self.count]
                elif self.skip_page < 0 and self.start_uuid is not None:
                    ind = vms.index(self.start_uuid)
                    start_ind = ind - self.count
                    if start_ind < 0:
                        start_ind = 0
                        ind = self.count
                    vms_uuid = vms[start_ind:ind]
                else:
                    index = self.current_page + self.skip_page
                    index = max(index, 1)
                    vms_uuid = vms[(index - 1) * self.count : index * self.count]
            except ValueError:
                index = self.current_page + self.skip_page
                index = max(index, 1)
                vms_uuid = vms[(index - 1) * self.count : index * self.count]

        docs = list(self.collection.find({"uuid": {"$in": vms_uuid}}, self.projection))
        docs.sort(key=lambda x: vms_uuid.index(x["uuid"]))

        return {
            "entities": docs,
            "start_uuid": docs[0]["uuid"] if docs else None,
            "end_uuid": docs[-1]["uuid"] if docs else None,
            "filter_criteria": query_criteria[0],
            "sort_criteria": "{}{}".format("" if self.sort_order > 0 else "-", self.sort_field),
            "count": self.count,
            "page_entities": len(docs),
            "total_entities": total_entities,
        }


class VMQuerier(PagingMixin, AttributeQuerierMixin, BaseQuerier):
    def __init__(self):
        BaseQuerier.__init__(
            self,
            resource_super_type=None,
            resource_types=[KVM_VM],
            regex_search_fields_support=["uuid", "vm_name", "node_ip", "description", "nics.mac_address"],
            sort_fields_support=[
                "uuid",
                "vm_name",
                ("vcpu", int),
                ("memory", int),
                ("create_time", int),
                "node_ip",
                "status",
                "folder_uuid",
            ],
        )
        self.default_sort_criteria = "-create_time"

    def apply_stats(self, resources):
        if resources:
            list(map(pci_address.translate_nic_pci_addr_to_bdf_str_for_vm, resources))
            fill_in_ip_for_vms(resources)
            apply_missing_queues(resources)

            vm_uuid_list = [x["uuid"] for x in resources]
            internal_flags = {
                v["vm_uuid"]: v.get("internal", False)
                for v in VMAdditionalInfoWrapper.query(vm_uuid_list, ["internal"])
            }

            for x in resources:
                x["internal"] = internal_flags.get(x["uuid"], False)
                # set `name` for config drive ISO
                for c in x["disks"]:
                    if c["type"] == constants.CDROM and cloud_init.is_config_drive_iso(c["path"]):
                        c["name"] = constants.CONFIG_DRIVE_ISO

                resource.normalize_nic(x["nics"])

        return super().apply_stats(resources)

    def get_vm_uuid_list(self, filter_criteria):
        query = self.generate_common_query_criteria()
        if filter_criteria:
            query.update(filter_criteria)

        return [x["uuid"] for x in self.collection.find(query, {"_id": 0, "uuid": 1})]

    def get_vms(self, filter_criteria, projection):
        projection = projection or {"_id": 0}
        query = self.generate_common_query_criteria()
        if filter_criteria:
            query.update(filter_criteria)

        return list(self.collection.find(query, projection))

    def _page_query(
        self,
        count=50,
        skip_page=1,
        sort_criteria=None,
        filter_criteria=None,
        projection=None,
        start_uuid=None,
        end_uuid=None,
        skip_to_last=False,
        **kwargs,
    ):
        filter_criteria = convert_bios_uuid_to_vm_uuid(filter_criteria)

        folder_query = False
        if filter_criteria and "folder_uuid" in filter_criteria:
            folder_query = True

        attribute_query = False
        attribute_keys = Attribute.get_keys()
        if sort_criteria and (
            sort_criteria in attribute_keys or (sort_criteria.startswith("-") and sort_criteria[1:] in attribute_keys)
        ):
            attribute_query = True

        if folder_query or attribute_query:
            data = VMMixedPageQuery(
                collection=self.collection,
                resource_type=self.resource_types[0],
                sort_fields_support=self.sort_fields_support,
                attribute_key_sort_field=attribute_keys,
                count=count,
                # Jump to the first page.
                skip_page=1 if skip_to_last else skip_page,
                sort_criteria=sort_criteria or self.default_sort_criteria,
                filter_criteria=filter_criteria,
                projection=projection,
                start_uuid=start_uuid,
                end_uuid=end_uuid,
                current_page=kwargs.get("current_page", 1),
                skip_to_last=skip_to_last,
            ).page_query()
            self.apply_stats(data["entities"])
            return data
        else:
            return super()._page_query(
                count=count,
                skip_page=skip_page,
                sort_criteria=sort_criteria,
                filter_criteria=filter_criteria,
                projection=projection,
                start_uuid=start_uuid,
                end_uuid=end_uuid,
                skip_to_last=skip_to_last,
                **kwargs,
            )

    def _search(self, text, limit, filter_criteria=None):
        if not text:
            return []

        projection = {"_id": 0}
        query_criteria = self.generate_regex_text_query_criteria(self.regex_search_fields_support, text)

        folder_uuid = None
        if filter_criteria:
            if "folder_uuid" in filter_criteria:
                folder_uuid = filter_criteria.pop("folder_uuid")
            query_criteria.update(filter_criteria)

        if folder_uuid:
            folder_vms_uuid = Folder.load(folder_uuid).relation()["vm_uuids"]
            query_criteria.update({"uuid": {"$in": folder_vms_uuid}})

        result = list(self.collection.find(query_criteria, projection).limit(limit))

        logging.info(
            "(regex)fuzzy_search.find({}, {}).limit({}) return {} docs".format(
                query_criteria, projection, limit, len(result)
            )
        )

        return result


# TODO(yaodong): It is better to synchronize this field when upgrading.
def apply_missing_queues(vms):
    """Set 1 or adaptive queue size as default for backward compatibility."""
    from smartx_app.elf.common.constants import VM_INTERFACE_VIRTIO, VM_RUNNING, VM_SUSPENDED
    from smartx_app.elf.common.utils import network as net_utils

    for vm in vms:
        status, nics = vm["status"], vm.get("nics", [])
        if nics:
            for nic in nics:
                if "model" not in nic:
                    nic["model"] = VM_INTERFACE_VIRTIO

                # The pre-VM hasn't been reconfigured.
                if nic.get("model") == VM_INTERFACE_VIRTIO:
                    if "queues" not in nic:
                        if status in (VM_SUSPENDED, VM_RUNNING):
                            nic["queues"] = 1
                        else:
                            # Next start will be configured to adaptive queues.
                            nic["queues"] = net_utils.calc_adaptive_virtio_net_queue_size(vm.get("vcpu", 1))
                else:
                    nic["queues"] = None


def convert_bios_uuid_to_vm_uuid(filter_criteria):
    """
    If filter criteria contains bios_uuid criterion,
    then the bios_uuid (domain_uuid) need to be converted to uuid (vm) for query.
    :param filter_criteria:   The filter criteria, e.g. `status=running;bios_uuid=a6269c12-b008-4a6e-8c34-7a63a9bce163`
    :return: The filter criteria converted result, e.g. `status=running;uuid=065f30b6-e819-4f91-ab2f-4c8159fc0449`
    """
    if filter_criteria is None:
        return None

    criteria = []
    for criterion in filter_criteria.split(";"):
        name, _, value = criterion.partition("=")
        if name == "bios_uuid":
            query_result = list(
                VMAdditionalInfoWrapper.raw_query(
                    filter_criteria={"domain_uuid": value}, project_criteria={"vm_uuid": 1}
                )
            )
            if query_result and "vm_uuid" in query_result[0]:
                criteria.append("uuid={}".format(query_result[0]["vm_uuid"]))
            else:
                raise base.ResourceException(
                    "The domain_uuid ({}) is not exist.".format(value), py_error.VM_NOT_FOUND_BY_BIOS_UUID
                )
        else:
            criteria.append(criterion)

    return ";".join(criteria)
