# Copyright (c) 2013-2017, SMARTX
# All rights reserved.
import logging

from common.config.resources import RESOURCE_IN_USE
from smartx_app.elf.common.resource_query.base import BaseQuerier, FullTextSearchMixin, PagingMixin
from smartx_app.elf.job_center.constants import (
    KVM_VOL,
    KVM_VOL_ISCSI,
    KVM_VOL_ISCSI_SNAPSHOT,
    KVM_VOL_SNAPSHOT,
    KVM_VOL_SNAPSHOT_SUPER,
    KVM_VOL_SUPER,
)


class CommonVolumeQuerier(PagingMixin, BaseQuerier):
    def __init__(self):
        BaseQuerier.__init__(
            self,
            resource_super_type=KVM_VOL_SUPER,
            resource_types=[KVM_VOL, KVM_VOL_ISCSI],
            regex_search_fields_support=["name"],
            sort_fields_support=[
                "name",
                ("sharing", bool),
                ("size_in_byte", int),
                ("create_time", int),
                ("mounting", bool),
                "storage_policy_uuid",
            ],
        )
        self.default_sort_criteria = "-create_time"

    def _add_super_type(self):
        for types, super_type in [
            ([KVM_VOL, KVM_VOL_ISCSI], KVM_VOL_SUPER),
            ([KVM_VOL_SNAPSHOT, KVM_VOL_ISCSI_SNAPSHOT], KVM_VOL_SNAPSHOT_SUPER),
        ]:
            doc = self.collection.find_one(
                {"resource_state": RESOURCE_IN_USE, "type": {"$in": types}, "super_type": {"$exists": True}},
                {"_id": 0, "uuid": 1},
            )

            if doc is None:
                self.collection.update_many(
                    {"resource_state": RESOURCE_IN_USE, "type": {"$in": types}, "super_type": {"$exists": False}},
                    {"$set": {"super_type": super_type}},
                )

    def get_snapshot_stats(self, vol_uuid_list):
        if not vol_uuid_list:
            return {}

        cursor = self.collection.aggregate(
            [
                {
                    "$match": {
                        "super_type": KVM_VOL_SNAPSHOT_SUPER,
                        "resource_state": RESOURCE_IN_USE,
                        "vm_snapshot": {"$type": 10},
                        "volume_uuid": {"$in": vol_uuid_list},
                    }
                },
                {"$project": {"_id": 0, "volume_uuid": 1}},
                {"$group": {"_id": "$volume_uuid", "count": {"$sum": 1}}},
            ]
        )

        return {x["_id"]: x["count"] for x in cursor}

    def generate_query_projection(self):
        """volume's field that must be provided in the query, mainly used in the apply_stats method"""
        base_projection = self.generate_common_query_projection()
        base_projection.update({"uuid": 1, "path": 1, "size_in_byte": 1, "size": 1, "mounting": 1, "sharing": 1})
        return base_projection

    def apply_stats(self, resources):
        from smartx_app.elf.common.resources.volume_vm_relationship import VolVMRelation

        if resources:
            snapshot_stats = self.get_snapshot_stats([v["uuid"] for v in resources])
            mounting_vm_stats = VolVMRelation.get_mounting_vm_stats_by_volume_uuid([v["uuid"] for v in resources])
            stale_vol_uuids = []
            for v in resources:
                v["sharing"] = v.get("sharing", False)
                v["snapshot_count"] = snapshot_stats.get(v["uuid"], 0)
                v["mounting_vm_list"] = mounting_vm_stats.get(v["uuid"], [])

                mounting = bool(v["mounting_vm_list"])
                if v.get("mounting", None) != mounting:
                    stale_vol_uuids.append(v["uuid"])
                v["mounting"] = mounting

            # Save as the stale volume, the cron job `recycle_stale_volumes`
            # will sync the `mounting` field for the volume and then remove
            # it from the stale volumes.
            if stale_vol_uuids:
                try:
                    VolVMRelation.add_stale_volumes(stale_vol_uuids)
                except Exception as e:
                    logging.exception("Failed to add the stale volumes:{}, error={}".format(stale_vol_uuids, str(e)))

        return super().apply_stats(resources)

    def ensure_paging_indexes(self, use_super_type=False):
        if use_super_type:
            self._add_super_type()

        super().ensure_paging_indexes(use_super_type)

    def _page_query(
        self,
        count=50,
        skip_page=1,
        sort_criteria=None,
        filter_criteria=None,
        projection=None,
        start_uuid=None,
        end_uuid=None,
        skip_to_last=False,
        **kwargs,
    ):
        # The `path` field is required for statistics.
        if projection:
            projection += ",path"

        return super()._page_query(
            count=count,
            skip_page=skip_page,
            sort_criteria=sort_criteria,
            filter_criteria=filter_criteria,
            projection=projection,
            start_uuid=start_uuid,
            end_uuid=end_uuid,
            skip_to_last=skip_to_last,
            **kwargs,
        )

    def _search(self, text, limit, filter_criteria=None):
        from smartx_app.elf.common.resources.base import ResourceException
        from smartx_proto.errors import pyerror_pb2 as py_error

        if filter_criteria:
            for field in filter_criteria:
                if field not in ("sharing", "mounting"):
                    raise ResourceException(
                        "Only support for filtering by `sharing` and `mounting`(current_filter_by={})".format(
                            list(filter_criteria.keys())
                        ),
                        py_error.RESOURCE_SEARCH_NOT_SUPPORT,
                    )
            else:
                # The `sharing` is a prefix index field.
                filter_criteria.setdefault("sharing", False)

        # A little ugly, search becomes a query to return the default result.
        if not text:
            query_criteria = self.generate_common_query_criteria()
            query_criteria.update(filter_criteria)
            return self.query(query_criteria, top=limit)
        return super()._search(text, limit, filter_criteria)


class VolumeQuerier(FullTextSearchMixin, CommonVolumeQuerier):
    pass
