# Copyright (c) 2013-2017, SMARTX
# All rights reserved.
import logging

from smartx_app.common.constants import KVM_VM_TEMPLATE
from smartx_app.elf.common.resource_query.base import (
    AttributeQuerierMixin,
    BaseQuerier,
    CriteriaResolver,
    PagingMixin,
)
from smartx_app.elf.common.utils import resource

_LOGGER = logging.getLogger(__name__)


class VMTemplateQuerier(PagingMixin, AttributeQuerierMixin, BaseQuerier):
    def __init__(self):
        BaseQuerier.__init__(
            self,
            resource_super_type=None,
            resource_types=[KVM_VM_TEMPLATE],
            regex_search_fields_support=["name"],
            sort_fields_support=["name", ("vcpu", int), ("memory", int), ("create_time", int)],
        )
        self.default_sort_criteria = "-create_time"

    def _page_query_from_usage(self, count, current_page, sort_criteria, filter_criteria, projection):
        vm_templates = []
        current_filter_criteria = self.generate_common_query_criteria()
        total_entities = self.collection.find(current_filter_criteria).count()
        criteria_resolver = CriteriaResolver(
            sort_fields_support=self.sort_fields_support,
            filter_fields_locked=("type", "resource_state"),
            projection_fields_omitted=None,
        )
        projection = criteria_resolver.resolve_projection(projection)

        if filter_criteria:
            current_filter_criteria.update(
                criteria_resolver.resolve_filter_criteria(filter_criteria, sort_fields_only=True)
            )

        if count > 0:
            ordered = -1 if sort_criteria[0] == "-" else 1
            # Since diff_size is not completely removed,
            # and there is no guarantee that unique_size exists, it is left unchanged.
            cursor = self.collection.aggregate(
                [
                    {"$match": current_filter_criteria},
                    {"$project": {"_id": 0, "uuid": 1, "disks.diff_size": 1}},
                    {"$unwind": "$disks"},
                    {"$group": {"_id": "$uuid", "diff_size": {"$sum": "$disks.diff_size"}, "disk_amount": {"$sum": 1}}},
                    {"$sort": {"diff_size": ordered, "_id": ordered}},
                    {"$skip": count * (current_page - 1)},
                    {"$limit": count},
                ]
            )
            docs = [(x["_id"], x["diff_size"], x["disk_amount"]) for x in cursor]
            _LOGGER.debug("diff_size sorted docs: {}, count={}, current_page={}".format(docs, count, current_page))

            if docs:
                q = {"uuid": {"$in": [x[0] for x in docs]}}
                q.update(current_filter_criteria)
                maps = {x["uuid"]: x for x in self.collection.find(q, projection)}
                for x, _, _ in docs:
                    if x in maps:
                        vm_templates.append(maps[x])

        return {
            "entities": vm_templates,
            "start_uuid": vm_templates[0]["uuid"] if vm_templates else None,
            "end_uuid": vm_templates[-1]["uuid"] if vm_templates else None,
            "filter_criteria": current_filter_criteria,
            "sort_criteria": sort_criteria,
            "count": count,
            "page_entities": len(vm_templates),
            "total_entities": total_entities,
        }

    def _page_query(
        self,
        count=50,
        skip_page=1,
        sort_criteria=None,
        filter_criteria=None,
        projection=None,
        start_uuid=None,
        end_uuid=None,
        skip_to_last=False,
        **kwargs,
    ):
        if sort_criteria and sort_criteria in ("diff_size", "-diff_size"):
            return self._page_query_from_usage(
                count=count,
                current_page=kwargs.pop("current_page", 1),
                sort_criteria=sort_criteria,
                filter_criteria=filter_criteria,
                projection=projection,
            )

        return super()._page_query(
            count=count,
            skip_page=skip_page,
            sort_criteria=sort_criteria,
            filter_criteria=filter_criteria,
            projection=projection,
            start_uuid=start_uuid,
            end_uuid=end_uuid,
            skip_to_last=skip_to_last,
            **kwargs,
        )

    def apply_stats(self, resources):
        if resources:
            for x in resources:
                resource.normalize_nic(x["nics"])

        return super().apply_stats(resources)
