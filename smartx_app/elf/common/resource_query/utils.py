# Copyright (c) 2013-2017, SMARTX
# All rights reserved.
import csv
from io import StringIO
import logging
import threading

from flask import make_response
import gevent
import libvirt

from common.config.resources import RESOURCE_IN_USE
from common.mongo.db import mongodb
from smartx_app.elf.common.config import libvirt_conf
from smartx_app.elf.common.constants import OVER_NETWORK_USB, VM_RUNNING
from smartx_app.elf.common.utils import libvirt_driver, to_str


def add_sharing_field_to_nfs_volume(include_snapshot=True, force=False, res_collection=None):
    from smartx_app.elf.job_center.constants import KVM_VOL, KVM_VOL_SNAPSHOT

    res_collection = res_collection or mongodb.resources.resource

    def _need_to_add():
        query = {"resource_state": RESOURCE_IN_USE, "type": KVM_VOL}

        if res_collection.find(query).count() == 0:
            return False

        query["sharing"] = {"$exists": True}
        return res_collection.find_one(query) is None

    def _add_sharing(res_type):
        res_collection.update_many(
            {"resource_state": RESOURCE_IN_USE, "type": res_type, "sharing": {"$exists": False}},
            {"$set": {"sharing": False}},
        )

    if force or _need_to_add():
        _add_sharing(KVM_VOL)
        if include_snapshot:
            _add_sharing(KVM_VOL_SNAPSHOT)


def find_running_vms(node_ip):
    from smartx_app.elf.job_center.constants import KVM_VM

    vms_list = mongodb.resources.resource.find(
        {"type": KVM_VM, "resource_state": RESOURCE_IN_USE, "node_ip": node_ip, "status": VM_RUNNING}, {"_id": 0}
    )
    return list(vms_list)


class ResourceConvertor:
    def __init__(self, resources):
        self.resources = resources

    def csv_response(self, file_name, field_definitions):
        field_definitions = [x for x in field_definitions if "label" in x]
        fieldnames = [to_str(x["label"]) for x in field_definitions]
        out_f = StringIO()
        writer = csv.writer(out_f)
        writer.writerow(fieldnames)
        for resource in self.resources:
            row = []
            for field_definition in field_definitions:
                try:
                    field = field_definition.get("field") or field_definition["label"]
                    if callable(field):
                        row.append(field(resource))
                    else:
                        row.append(resource.get(field, ""))
                except (KeyError, IndexError, TypeError):
                    row.append("")
            writer.writerow([to_str(s) for s in row])
        out_f.seek(0)
        response = make_response(out_f.getvalue())
        response.headers["Content-Disposition"] = "attachment; filename={}.csv".format(file_name)
        response.headers["Content-Type"] = "text/csv"
        return response


def get_running_doms_on_nodes(node_ip_list, timeout=5):
    """
    Take node ip list and concurrently connect to the libvirtd on those
    nodes to fetch all running domains' uuid.

    return: set of running dom uuids
    """

    def _get_running_doms_on_node(node_ip):
        doms = set()
        try:
            with gevent.Timeout(timeout):
                with libvirt_driver.libvirt_connection(
                    "qemu+{}://{}/system".format(
                        libvirt_conf.global_libvirt_conf.default_transport,
                        node_ip,
                    )
                ) as conn:
                    all_doms_list = conn.listAllDomains()
                    for dom in all_doms_list:
                        if dom.state()[0] == libvirt.VIR_DOMAIN_RUNNING:
                            doms.add(dom.name())
        except gevent.Timeout:
            logging.info("Timeout to get doms on node {}".format(node_ip))
        except libvirt.libvirtError as e:
            logging.warning("Error when getting doms on node {}: {}".format(node_ip, str(e)))
        finally:
            node_dom_map[node_ip] = doms
            logging.info("Obtained doms for {}: {}".format(node_ip, doms))

    node_dom_map = {}
    t_list = []
    ret_set = set()

    for node_ip in node_ip_list:
        t = threading.Thread(target=_get_running_doms_on_node, args=(node_ip,))
        t.daemon = True
        t.start()
        t_list.append(t)

    for t in t_list:
        # Make sure the threads do not block, so we still set
        # a final timeout which is double of the timeout
        t.join(timeout * 2)

    for _, dom_set in list(node_dom_map.items()):
        ret_set.update(dom_set)

    return ret_set


def get_domain_state_timeout(dom_name, node_ip, timeout=5):
    try:
        with gevent.Timeout(timeout):
            with libvirt_driver.libvirt_connection(
                "qemu+{}://{}/system".format(libvirt_conf.global_libvirt_conf.default_transport, node_ip)
            ) as conn:
                dom = conn.lookupByName(dom_name)
                return dom.state()[0]
    except gevent.Timeout:
        logging.warning("Timeout when access libvirt on {}".format(node_ip))
        return None
    except libvirt.libvirtError as e:
        logging.warning("Error when access libvirt on {}: {}".format(node_ip, e))
        return None


def get_vms_with_over_network_usb(node_ip):
    """
    Get the virtual machine with the over network USB device mounted
    under the current node by node IP

    return: list of running VM (part fields) which mounted the over network USB and their hostdevs.
    The reason why only the required fields are returned is that the scheduled task will call this function,
    if all fields of VM are returned, the CPU pressure may be too high.
    """
    from smartx_app.elf.job_center.constants import KVM_VM

    vms_with_over_network_usb = mongodb.resources.resource.find(
        {
            "type": KVM_VM,
            "resource_state": RESOURCE_IN_USE,
            "node_ip": node_ip,
            "status": VM_RUNNING,
            "hostdevs.type": OVER_NETWORK_USB,
        },
        {"_id": 0, "uuid": 1, "hostdevs": 1, "update_time": 1, "create_time": 1},
    )

    return list(vms_with_over_network_usb)
