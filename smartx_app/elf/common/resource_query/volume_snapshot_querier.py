# Copyright (c) 2013-2017, SMARTX
# All rights reserved.
from common.config.resources import RESOURCE_IN_USE
from smartx_app.elf.common.resource_query.base import BaseQuerier, CriteriaResolver, FullTextSearchMixin, PagingMixin
from smartx_app.elf.job_center.constants import (
    KVM_VOL_ISCSI_SNAPSHOT,
    KVM_VOL_SNAPSHOT,
    KVM_VOL_SNAPSHOT_SUPER,
)


class VolumeSnapshotQuerier(FullTextSearchMixin, PagingMixin, BaseQuerier):
    def __init__(self, from_vm_snapshot=False):
        BaseQuerier.__init__(
            self,
            resource_super_type=KVM_VOL_SNAPSHOT_SUPER,
            resource_types=[KVM_VOL_SNAPSHOT, KVM_VOL_ISCSI_SNAPSHOT],
            regex_search_fields_support=None,
            sort_fields_support=[
                "name",
                "volume_uuid",
                ("unique_size", int),
                ("create_time", int),
                ("sharing", bool),
                "vm_snapshot",
            ],
        )
        self.default_sort_criteria = "-create_time"

        # type1 volume snapshots: snapshots created directly from volume.
        # type2 volume snapshots: snapshots created along with a VM snapshot.
        # Both type1 and type2 have a 'super_type'='KVM_VOLUME_SNAPSHOT_SUPER' field in database.
        # For type1, 'vm_snapshot'=null. For type2, 'vm_snapshot'=vm_snapshot_uuid.
        # ***************
        # If from_vm_snapshot=False, the querier only queries type1 snapshots. Otherwise, the querier queries type2
        # snapshots.
        self._vm_snapshot_filter_criteria = "vm_snapshot="
        if from_vm_snapshot:
            self._vm_snapshot_filter_criteria = f"{self._vm_snapshot_filter_criteria}{CriteriaResolver.NOT_NULL}"

    def _search(self, text, limit, **kwargs):
        current_limit = limit
        volumes = None

        # Limit the `limit` to `limit * 16`
        for i in range(5):
            current_limit *= 2**i
            result = super()._search(text, current_limit, **kwargs)
            volumes = [x for x in result if x.get("vm_snapshot") is None]

            if len(result) < current_limit or len(volumes) >= limit:
                return volumes[:limit]
        else:
            return volumes

    def _get_volumes(self, volume_uuid_list):
        cursor = self.collection.find(
            {"uuid": {"$in": volume_uuid_list}, "resource_state": RESOURCE_IN_USE}, {"_id": 0, "uuid": 1, "name": 1}
        )

        return {x["uuid"]: x for x in cursor}

    def apply_stats(self, resources):
        volumes = self._get_volumes([x["volume_uuid"] for x in resources])

        for snapshot in resources:
            snapshot.pop("vm_snapshot", None)
            if snapshot["volume_uuid"] in volumes:
                # The src volume name may have been modified.
                snapshot["volume_name"] = volumes[snapshot["volume_uuid"]]["name"]
            else:
                # The src volume has been deleted.
                snapshot["volume_uuid"] = snapshot["volume_name"] = None

        return resources

    def ensure_paging_indexes(self, use_super_type=False):
        from pymongo import ASCENDING

        for field in self.sort_fields_support:
            if isinstance(field, tuple | list):
                field_name = field[0]
            else:
                field_name = field

            if field_name in ("type", "resource_state", "uuid", "vm_snapshot"):
                continue

            self.collection.create_index(
                [
                    ("super_type" if use_super_type else "type", ASCENDING),
                    ("resource_state", ASCENDING),
                    ("vm_snapshot", ASCENDING),
                    (field_name, ASCENDING),
                    ("uuid", ASCENDING),
                ],
                background=True,
                sparse=True,
            )

    def _page_query(
        self,
        count=50,
        skip_page=1,
        sort_criteria=None,
        filter_criteria=None,
        projection=None,
        start_uuid=None,
        end_uuid=None,
        skip_to_last=False,
        **kwargs,
    ):
        if filter_criteria:
            filter_criteria = ";".join([self._vm_snapshot_filter_criteria, filter_criteria])
        else:
            filter_criteria = self._vm_snapshot_filter_criteria

        # The `path` field is required for statistics.
        if projection:
            projection += ",volume_uuid"

        return super()._page_query(
            count=count,
            skip_page=skip_page,
            sort_criteria=sort_criteria,
            filter_criteria=filter_criteria,
            projection=projection,
            start_uuid=start_uuid,
            end_uuid=end_uuid,
            skip_to_last=skip_to_last,
            **kwargs,
        )
