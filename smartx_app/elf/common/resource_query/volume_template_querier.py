# Copyright (c) 2013-2017, SMARTX
# All rights reserved.

# -*- coding: utf-8 -*-
# Copyright (c) 2013-2017, SMARTX
# All rights reserved.
from smartx_app.elf.common import constants as elf_common_constants
from smartx_app.elf.common.constants import KVM_VOL_ISCSI_TEMPLATE, KVM_VOL_TEMPLATE, KVM_VOL_TEMPLATE_SUPER
from smartx_app.elf.common.resource_query.base import BaseQuerier, PagingMixin


class VolumeTemplateQuerier(PagingMixin, BaseQuerier):
    """
    Queries for all type of volumes
    """

    def __init__(self):
        BaseQuerier.__init__(
            self,
            resource_super_type=KVM_VOL_TEMPLATE_SUPER,
            resource_types=[KVM_VOL_TEMPLATE, KVM_VOL_ISCSI_TEMPLATE],
            regex_search_fields_support=["name"],
            sort_fields_support=["name", ("size", int), ("create_time", int), "storage_policy_uuid"],
        )
        self.default_sort_criteria = "-create_time"

    def cursor_query(self, query_cond=None, field_filter=None):
        """
        :param query_cond: condition for query
        :param field_filter:  filter for fields

        :rtype pymongo.cursor.Cursor
        """
        default_filter = {"_id": 0}
        if field_filter is not None:
            default_filter.update(field_filter)

        query_condition = self.generate_common_query_criteria()
        if query_cond:
            query_condition.update(query_cond)

        resources = self.collection.find(query_condition, default_filter)

        return resources

    def ensure_paging_indexes(self, use_super_type=False):
        if use_super_type:
            self._add_super_type()

        super().ensure_paging_indexes(use_super_type)

    def page_query(
        self,
        count=50,
        skip_page=1,
        sort_criteria=None,
        filter_criteria=None,
        projection=None,
        start_uuid=None,
        end_uuid=None,
        skip_to_last=False,
        **kwargs,
    ):
        data = super().page_query(
            count, skip_page, sort_criteria, filter_criteria, projection, start_uuid, end_uuid, skip_to_last, **kwargs
        )

        return data

    def get(self, res_uuid):
        volume_template = super().get(res_uuid)
        if not volume_template:
            return None

        return volume_template

    def _add_super_type(self):
        doc = self.collection.find_one(
            {"type": {"$in": self.resource_types}, "super_type": {"$exists": False}},
            {"_id": 0, "uuid": 1},
        )

        if doc:
            self.collection.update_many(
                {"type": {"$in": self.resource_types}, "super_type": {"$exists": False}},
                {"$set": {"super_type": self.resource_super_type}},
            )

    def apply_stats(self, resources):
        for volume_template in resources:
            if not volume_template.get("encryption_algorithm"):
                volume_template["encryption_algorithm"] = elf_common_constants.VOLUME_ENCRYPTION_ALG_PLAINTEXT

        return super().apply_stats(resources)
