# Copyright (c) 2013-2022, SMARTX
# All rights reserved.

from common.event import event_message
from smartx_app.elf.common.events import constants as ev_constants
from smartx_app.elf.common.events import message_i18n_elf as i18n
from smartx_app.elf.job_center import constants as jc_constants


class DataPortEventWrapper:
    def __init__(self, user=None, user_type="USER"):
        self.user = user
        self.user_name = user.get("username", "-") if user else None
        self.user_role = user.get("Role") if user else None
        self.user_type = user_type

    @event_message.event_enclosure
    def event_dataport_start_conversion(self, dataport):
        return event_message.build_event(
            event_name=ev_constants.EVENT_DATA_PORT_CONVERT,
            user=self.user_name,
            user_role=self.user_role,
            user_type=self.user_type,
            data=None,
            resources={dataport["uuid"]: jc_constants.DATA_PORT},
            event_mod=i18n.event_message.get(ev_constants.EVENT_DATA_PORT_CONVERT),
        )

    @event_message.event_enclosure
    def event_dataport_delete(self, dataport):
        return event_message.build_event(
            event_name=ev_constants.EVENT_DATA_PORT_DELETE,
            user=self.user_name,
            user_role=self.user_role,
            user_type=self.user_type,
            data=None,
            resources={dataport["uuid"]: jc_constants.DATA_PORT},
            event_mod=i18n.event_message.get(ev_constants.EVENT_DATA_PORT_DELETE),
        )
