# Copyright (c) 2013-2015, SMARTX
# All rights reserved.

from common.event.event_message import EventMessage, build_event, event_enclosure, generate_events
from smartx_app.elf.common.events.constants import (
    EVENT_ADD_ATTRIBUTE_VALUE,
    EVENT_DELETE_ATTRIBUTE,
    EVENT_DELETE_ATTRIBUTE_VALUE,
    EVENT_EDIT_ATTRIBUTE,
    EVENT_EDIT_ATTRIBUTE_VALUE,
    EVENT_NEW_ATTRIBUTE,
)
import smartx_app.elf.common.events.message_i18n_elf as i18n


class AttributeEventWrapper:
    def __init__(self, user, event_state=None, data=None, detail=None):
        self.user = user
        self.user_name = user.get("username", "-")
        self.user_role = user.get("Role")
        self.event_state = event_state or "DONE"
        self.data = data
        self.detail = detail

    @event_enclosure
    def event_attribute_add_key(self, attribute_key, attribute_values):
        data = {"attribute_key": attribute_key}
        message_arg = {"attribute_key": attribute_key}
        resources = {}
        detail = EventMessage.update_detail(
            event_detail_tmp=i18n.event_detail.get("ATTRIBUTE"),
            **{"attribute_key": attribute_key, "attribute_values": ", ".join(attribute_values)},
        )
        event_arg = build_event(
            event_name=EVENT_NEW_ATTRIBUTE,
            user=self.user_name,
            user_role=self.user_role,
            data=data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(EVENT_NEW_ATTRIBUTE),
            **message_arg,
        )
        return generate_events(event_state=self.event_state, **event_arg)

    @event_enclosure
    def event_attribute_edit_key(self, old_attribute_key, new_attribute_key):
        data = {"old_attribute_key": old_attribute_key}
        message_arg = {"old_attribute_key": old_attribute_key}
        resources = {}
        detail = EventMessage.update_detail(
            event_detail_tmp=i18n.event_detail.get("ATTRIBUTE_KEY_CHANGED"),
            **{"old_attribute_key": old_attribute_key, "new_attribute_key": new_attribute_key},
        )
        event_arg = build_event(
            event_name=EVENT_EDIT_ATTRIBUTE,
            user=self.user_name,
            user_role=self.user_role,
            data=data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(EVENT_EDIT_ATTRIBUTE),
            **message_arg,
        )
        return generate_events(event_state=self.event_state, **event_arg)

    @event_enclosure
    def event_attribute_delete_key(self, attribute_key):
        data = {"attribute_key": attribute_key}
        message_arg = {"attribute_key": attribute_key}
        resources = {}
        detail = {"zh_CN": "None", "en_US": "None"}
        event_arg = build_event(
            event_name=EVENT_DELETE_ATTRIBUTE,
            user=self.user_name,
            user_role=self.user_role,
            data=data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(EVENT_DELETE_ATTRIBUTE),
            **message_arg,
        )
        return generate_events(event_state=self.event_state, **event_arg)

    @event_enclosure
    def event_attribute_add_value(self, attribute_key, attribute_value):
        data = {"attribute_key": attribute_key, "attribute_value": attribute_value}
        message_arg = {"attribute_key": attribute_key, "attribute_value": attribute_value}
        resources = {}
        detail = {"zh_CN": "None", "en_US": "None"}
        event_arg = build_event(
            event_name=EVENT_ADD_ATTRIBUTE_VALUE,
            user=self.user_name,
            user_role=self.user_role,
            data=data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(EVENT_ADD_ATTRIBUTE_VALUE),
            **message_arg,
        )
        return generate_events(event_state=self.event_state, **event_arg)

    @event_enclosure
    def event_attribute_edit_value(self, attribute_key, old_attribute_value, new_attribute_value):
        data = {
            "attribute_key": attribute_key,
            "old_attribute_value": old_attribute_value,
            "new_attribute_value": new_attribute_value,
        }
        message_arg = {"attribute_key": attribute_key, "attribute_value": old_attribute_value}
        resources = {}
        detail = EventMessage.update_detail(
            event_detail_tmp=i18n.event_detail.get("ATTRIBUTE_VALUE_CHANGED"),
            **{"old_attribute_value": old_attribute_value, "new_attribute_value": new_attribute_value},
        )
        event_arg = build_event(
            event_name=EVENT_EDIT_ATTRIBUTE_VALUE,
            user=self.user_name,
            user_role=self.user_role,
            data=data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(EVENT_EDIT_ATTRIBUTE_VALUE),
            **message_arg,
        )
        return generate_events(event_state=self.event_state, **event_arg)

    @event_enclosure
    def event_attribute_delete_value(self, attribute_key, attribute_value):
        data = {"attribute_key": attribute_key, "attribute_value": attribute_value}
        message_arg = {"attribute_key": attribute_key, "attribute_value": attribute_value}
        resources = {}
        detail = {"zh_CN": "None", "en_US": "None"}
        event_arg = build_event(
            event_name=EVENT_DELETE_ATTRIBUTE_VALUE,
            user=self.user_name,
            user_role=self.user_role,
            data=data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(EVENT_DELETE_ATTRIBUTE_VALUE),
            **message_arg,
        )
        return generate_events(event_state=self.event_state, **event_arg)
