# Copyright (c) 2013-2015, SMARTX
# All rights reserved.

from common.event.event_message import EventMessage, build_event, event_enclosure
from smartx_app.elf.common.events import message_i18n_elf as i18n
from smartx_app.elf.common.events.constants import EVENT_EDIT_CLUSTER_CPU_MODEL
from smartx_app.elf.common.utils import cluster
from smartx_app.elf.job_center.constants import CLUSTER


class CPUModelEventWrapper:
    def __init__(self, user):
        self.user_name = user.get("username", "-")
        self.user_role = user.get("Role")

    @event_enclosure
    def event_cpu_model_edit(self, old_model=None, new_model=None):
        cpu_info = {"cluster_id": cluster.get_cluster_id(), "old_model": old_model, "new_model": new_model}
        resources = {cpu_info["cluster_id"]: CLUSTER}

        detail = EventMessage.update_detail(details=i18n.event_detail.get(EVENT_EDIT_CLUSTER_CPU_MODEL), **cpu_info)

        return build_event(
            event_name=EVENT_EDIT_CLUSTER_CPU_MODEL,
            user=self.user_name,
            user_role=self.user_role,
            data=cpu_info,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(EVENT_EDIT_CLUSTER_CPU_MODEL),
            **cpu_info,
        )
