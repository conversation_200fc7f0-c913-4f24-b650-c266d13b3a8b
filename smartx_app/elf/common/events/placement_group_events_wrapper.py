# Copyright (c) 2013-2015, SMARTX
# All rights reserved.

from common.config.resources import RESOURCE_IN_USE
from common.event.event_message import (
    EventMessage,
    build_event,
    event_enclosure,
    generate_events,
)
from common.mongo.db import mongodb
from smartx_app.elf.common.events.constants import (
    EVENT_CREATE_PLACEMENT_GROUP,
    EVENT_DELETE_PLACEMENT_GROUP,
    EVENT_EDIT_PLACEMENT_GROUP,
)
import smartx_app.elf.common.events.message_i18n_elf as i18n
from smartx_app.elf.job_center.constants import KVM_VM


class PlacementGroupEventWrapper:
    def __init__(self, obj, user, vm_rule, host_rule, event_state=None, data=None):
        self.obj = obj
        self.user_name = user.get("username", "-")
        self.user_role = user.get("Role")
        self.vm_rule = vm_rule
        self.host_rule = host_rule
        self.event_state = event_state
        self.data = data

    def placement_group_event_splicing(self, event_name):
        storage_policy_data = {"placement_group_name": self.obj.name, "placement_group_id": self.obj.uuid}
        message_arg = {"placement_group_name": self.obj.name}
        detail = {"zh_CN": "None", "en_US": "None"}
        resources = {self.obj.uuid: None}
        event_arg = build_event(
            event_name=event_name,
            user=self.user_name,
            user_role=self.user_role,
            data=storage_policy_data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(event_name),
            **message_arg,
        )
        return generate_events(event_state=self.event_state, **event_arg)

    @event_enclosure
    def event_placement_group_create(self):
        storage_policy_data = {"placement_group_name": self.obj.name, "placement_group_id": self.obj.uuid}
        message_arg = {"placement_group_name": self.obj.name}
        resources = {self.obj.uuid: None}
        detail = self.placement_group_create()
        event_arg = build_event(
            event_name=EVENT_CREATE_PLACEMENT_GROUP,
            user=self.user_name,
            user_role=self.user_role,
            data=storage_policy_data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(EVENT_CREATE_PLACEMENT_GROUP),
            **message_arg,
        )
        return generate_events(event_state=self.event_state, **event_arg)

    @event_enclosure
    def event_placement_group_update(self):
        storage_policy_data = {"placement_group_name": self.obj.name, "placement_group_id": self.obj.uuid}
        message_arg = {"placement_group_name": self.obj.name}
        resources = {self.obj.uuid: None}
        detail = self.placement_group_edit()
        event_arg = build_event(
            event_name=EVENT_EDIT_PLACEMENT_GROUP,
            user=self.user_name,
            user_role=self.user_role,
            data=storage_policy_data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(EVENT_EDIT_PLACEMENT_GROUP),
            **message_arg,
        )
        return generate_events(event_state=self.event_state, **event_arg)

    @event_enclosure
    def event_placement_group_delete(self):
        return self.placement_group_event_splicing(EVENT_DELETE_PLACEMENT_GROUP)

    def get_vm_names(self, vm_uuids):
        return [
            x["vm_name"]
            for x in mongodb.resources.resource.find(
                {"uuid": {"$in": vm_uuids}, "resource_state": RESOURCE_IN_USE, "type": KVM_VM}, {"_id": 0, "vm_name": 1}
            )
        ]

    def generate_policy_detail(self, policy):
        detail = None
        if policy.get("vm_vm_rule") == self.vm_rule.PREFER_DIFFERENT:
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("PREFER_DIFFERENT"))
        if policy.get("vm_vm_rule") == self.vm_rule.PREFER_SAME:
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("PREFER_SAME"))
        if policy.get("vm_vm_rule") == self.vm_rule.MUST_DIFFERENT:
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("MUST_DIFFERENT"))
        if policy.get("vm_vm_rule") == self.vm_rule.MUST_SAME:
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("MUST_SAME"))
        if policy.get("vm_host_rules") is not None and len(policy.get("vm_host_rules")) != 0:
            for rules in policy["vm_host_rules"]:
                s = [
                    x["name"]
                    for x in mongodb.smartx.host_infos.find(
                        {"host_uuid": {"$in": rules.get("hosts", [])}}, {"_id": 0, "name": 1}
                    )
                ]
                hosts = {"hosts": ", ".join(s)}
                if rules.get("flag") == self.host_rule.MUST_BE:
                    detail = EventMessage.update_detail(detail, i18n.event_detail.get("MUST_BE"), **hosts)
                if rules.get("flag") == self.host_rule.MUST_NOT_BE:
                    detail = EventMessage.update_detail(detail, i18n.event_detail.get("MUST_NOT_BE"), **hosts)
                if rules.get("flag") == self.host_rule.PREFER:
                    detail = EventMessage.update_detail(detail, i18n.event_detail.get("PREFER"), **hosts)
                if rules.get("flag") == self.host_rule.PREFER_NOT:
                    detail = EventMessage.update_detail(detail, i18n.event_detail.get("PREFER_NOT"), **hosts)
        if not detail:
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("PLACEMENT_GROUP_POLICY_EMPTY"))
        return detail

    def placement_group_create(self):
        detail = i18n.event_detail.get("CREATE_PLACEMENT_GROUP_INIT")
        name = {"placement_group_name": self.obj.name}
        detail = EventMessage.update_detail(detail, **name)
        if self.data.get("associated_vms"):
            names = {"vm_names": ", ".join(self.get_vm_names(self.data.get("associated_vms")))}
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("PLACEMENT_MEMBERS"), **names)
        if self.obj.enable is False:
            detail = EventMessage.update_detail(
                detail,
                i18n.event_detail.get("PLACEMENT_POLICY_OFF"),
            )
            return detail
        detail = EventMessage.update_detail(detail, self.generate_policy_detail(self.obj.policy))
        return detail

    def placement_group_edit(self):
        detail = i18n.event_detail.get("EDIT_INIT")
        if self.data.get("name") is not None and self.data["name"] != self.obj.name:
            name_arg = {"old_name": self.obj.name, "new_name": self.data["name"]}
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("PLACEMENT_GROUP_NAME"), **name_arg)
        if self.data.get("enable") is not None and self.data["enable"] != self.obj.enable:
            if self.data.get("enable") is True:
                detail = EventMessage.update_detail(detail, i18n.event_detail.get("PLACEMENT_GROUP_POLICY_TO_ON"))
            else:
                detail = EventMessage.update_detail(detail, i18n.event_detail.get("PLACEMENT_GROUP_POLICY_TO_OFF"))
        if "description" in self.data and self.data["description"] != self.obj.description:
            description_arg = {"new_description": self.data["description"]}
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("DESCRIPTION_CHANGED"), **description_arg)
        if self.data.get("associated_vms") is not None and list(self.obj.associated_vms) != self.data["associated_vms"]:
            names = {
                "old_names": ", ".join(self.get_vm_names(self.obj.associated_vms)),
                "new_names": ", ".join(self.get_vm_names(self.data.get("associated_vms"))),
            }
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("PLACEMENT_GROUP_VM"), **names)
        if self.data.get("policy") is not None and self.obj.policy != self.data.get("policy"):
            policy_arg = {
                "old_policy_zh": self.generate_policy_detail(self.obj.policy)["zh_CN"],
                "old_policy_en": self.generate_policy_detail(self.obj.policy)["en_US"],
                "new_policy_zh": self.generate_policy_detail(self.data.get("policy"))["zh_CN"],
                "new_policy_en": self.generate_policy_detail(self.data.get("policy"))["en_US"],
            }
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("PLACEMENT_GROUP_POLICY"), **policy_arg)
        return detail
