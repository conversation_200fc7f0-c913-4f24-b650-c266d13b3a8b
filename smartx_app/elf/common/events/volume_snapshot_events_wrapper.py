# Copyright (c) 2013-2015, SMARTX
# All rights reserved.

from common.event.event_message import build_event, event_enclosure
from smartx_app.elf.common.events.constants import (
    EVENT_CREATE_VOLUME_SNAPSHOT,
    EVENT_DELETE_VOLUME_SNAPSHOT,
    EVENT_VOLUME_ROLLBACK,
)
import smartx_app.elf.common.events.message_i18n_elf as i18n


class VolumeSnapshotEventWrapper:
    def __init__(self, user, uuid, name, detail=None, snapshot_type=None):
        self.user_name = user.get("username", "-")
        self.user_role = user.get("Role")
        self.uuid = uuid
        self.name = name
        self.detail = detail
        self.snapshot_type = snapshot_type

    def volume_snapshot_event_splicing(self, event_name):
        volume_snapshot_data = {"volume_snapshot_name": self.name, "volume_snapshot_id": self.uuid}
        message_arg = {"volume_snapshot_name": self.name}
        resources = {self.uuid: self.snapshot_type}
        return build_event(
            event_name=event_name,
            user=self.user_name,
            user_role=self.user_role,
            data=volume_snapshot_data,
            resources=resources,
            detail=self.detail,
            event_mod=i18n.event_message.get(event_name),
            **message_arg,
        )

    @event_enclosure
    def event_volume_snapshot_delete(self):
        return self.volume_snapshot_event_splicing(EVENT_DELETE_VOLUME_SNAPSHOT)

    @event_enclosure
    def event_volume_snapshot_create(self):
        return self.volume_snapshot_event_splicing(EVENT_CREATE_VOLUME_SNAPSHOT)

    @event_enclosure
    def event_volume_snapshot_rollback(self, volume_name):
        volume_snapshot_data = {"volume_snapshot_name": self.name, "volume_snapshot_id": self.uuid}
        message_arg = {"volume_snapshot_name": self.name, "volume_name": volume_name}
        resources = {self.uuid: self.snapshot_type}
        return build_event(
            event_name=EVENT_VOLUME_ROLLBACK,
            user=self.user_name,
            user_role=self.user_role,
            data=volume_snapshot_data,
            resources=resources,
            detail=self.detail,
            event_mod=i18n.event_message.get(EVENT_VOLUME_ROLLBACK),
            **message_arg,
        )
