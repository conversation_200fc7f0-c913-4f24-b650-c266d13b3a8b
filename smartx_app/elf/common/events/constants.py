# Copyright (c) 2013-2015, SMARTX
# All rights reserved.

EVENT_CREATE_VM = "CREATE_VM"

EVENT_DELETE_VM = "DELETE_VM"

EVENT_BATCH_DELETE_VM = "BATCH_DELETE_VM"

EVENT_EDIT_VM = "EDIT_VM"

EVENT_EDIT_VM_VOLUME = "EDIT_VM_VOLUME"

EVENT_EDIT_VM_NIC = "EDIT_VM_NIC"

EVENT_EDIT_VM_DEVICE = "EDIT_VM_DEVICE"

EVENT_AUTO_DETACH_VM_USB = "AUTO_DETACH_VM_USB"

EVENT_START_VM = "START_VM"

EVENT_BOOT_VM_WITH_HOST = "BOOT_VM_WITH_HOST"

EVENT_BATCH_START_VM = "BATCH_START_VM"

EVENT_SHUTDOWN_VM = "SHUTDOWN_VM"

EVENT_BATCH_SHUTDOWN_VM = "BATCH_SHUTDOWN_VM"

EVENT_FORCE_SHUTDOWN_VM = "FORCE_SHUTDOWN_VM"

EVENT_BATCH_FORCE_SHUTDOWN_VM = "BATCH_FORCE_SHUTDOWN_VM"

EVENT_REBOOT_VM = "REBOOT_VM"

EVENT_BATCH_REBOOT_VM = "BATCH_REBOOT_VM"

EVENT_FORCE_REBOOT_VM = "FORCE_REBOOT_VM"

EVENT_BATCH_FORCE_REBOOT_VM = "BATCH_FORCE_REBOOT_VM"

EVENT_SUSPEND_VM = "SUSPEND_VM"

EVENT_BATCH_SUSPEND_VM = "BATCH_SUSPEND_VM"

EVENT_RESUME_VM = "RESUME_VM"

EVENT_BATCH_RESUME_VM = "BATCH_RESUME_VM"

EVENT_CLONE_VM = "CLONE_VM"

EVENT_MIGRATE_VM = "MIGRATE_VM"

EVENT_BATCH_MIGRATE_VM = "BATCH_MIGRATE_VM"

EVENT_VM_SELF_FENCE_PAUSE = "VM_SELF_FENCE_PAUSE"

EVENT_VM_SELF_FENCE_STOP = "VM_SELF_FENCE_STOP"

EVENT_VM_HA = "VM_HA"

EVENT_VM_LOCAL_HA = "VM_LOCAL_HA"

EVENT_VM_NETWORK_HA = "VM_NETWORK_HA"

EVENT_VM_OS_HA = "VM_OS_HA"

EVENT_VM_STATE_CHANGE = "VM_STATE_CHANGE"

EVENT_CREATE_VM_SNAPSHOT = "CREATE_VM_SNAPSHOT"

EVENT_CREATE_VM_TEMPLATE = "CREATE_VM_TEMPLATE"

EVENT_CREATE_VM_FROM_TEMPLATE = "CREATE_VM_FROM_TEMPLATE"

EVENT_BATCH_CREATE_VM_FROM_TEMPLATE = "BATCH_CREATE_VM_FROM_TEMPLATE"

EVENT_VM_ROLLBACK = "VM_ROLLBACK"

EVENT_REBUILD_VM_FROM_SNAPSHOT = "REBUILD_VM_FROM_SNAPSHOT"

EVENT_CREATE_VOLUME = "CREATE_VOLUME"

EVENT_CREATE_VOLUME_FROM_STORAGE_OBJ = "CREATE_VOLUME_FROM_OBJ"

EVENT_EDIT_VOLUME = "EDIT_VOLUME"

EVENT_UPLOAD_VOLUME = "UPLOAD_VOLUME"

EVENT_DELETE_VOLUME = "DELETE_VOLUME"

EVENT_CLONE_VOLUME = "CLONE_VOLUME"

EVENT_CREATE_VOLUME_SNAPSHOT = "CREATE_VOLUME_SNAPSHOT"

EVENT_DELETE_VOLUME_SNAPSHOT = "DELETE_VOLUME_SNAPSHOT"

EVENT_VOLUME_ROLLBACK = "VOLUME_ROLLBACK"

EVENT_REBUILD_VOLUME_FROM_SNAPSHOT = "REBUILD_VOLUME_FROM_SNAPSHOT"

EVENT_CREATE_STORAGE_POLICY = "CREATE_STORAGE_POLICY"

EVENT_EDIT_STORAGE_POLICY = "EDIT_STORAGE_POLICY"

EVENT_DELETE_STORAGE_POLICY = "DELETE_STORAGE_POLICY"

EVENT_DELETE_STORAGE_POLICY_TARGETS = "DELETE_STORAGE_POLICY_TARGETS"


EVENT_UPLOAD_ISO = "UPLOAD_ISO"

EVENT_EDIT_ISO = "EDIT_ISO"

EVENT_DELETE_ISO = "DELETE_ISO"

EVENT_CREATE_PLACEMENT_GROUP = "CREATE_PLACEMENT_GROUP"

EVENT_EDIT_PLACEMENT_GROUP = "EDIT_PLACEMENT_GROUP"

EVENT_DELETE_PLACEMENT_GROUP = "DELETE_PLACEMENT_GROUP"

EVENT_EDIT_CLUSTER_CPU_MODEL = "EDIT_CLUSTER_CPU_MODEL"

EVENT_SET_FOLDER = "SET_FOLDER"

EVENT_CHANGE_FOLDER = "CHANGE_FOLDER"

EVENT_MOVE_OUT_FOLDER = "MOVE_OUT_FOLDER"

EVENT_CREATE_FOLDER = "CREATE_FOLDER"

EVENT_EDIT_FOLDER = "EDIT_FOLDER"

EVENT_DELETE_FOLDER = "DELETE_FOLDER"

EVENT_VM_BIND_ATTRIBUTE = "VM_BIND_ATTRIBUTE"

EVENT_VM_UNBIND_ATTRIBUTE = "VM_UNBIND_ATTRIBUTE"

EVENT_NEW_ATTRIBUTE = "NEW_ATTRIBUTE"

EVENT_EDIT_ATTRIBUTE = "EDIT_ATTRIBUTE"

EVENT_DELETE_ATTRIBUTE = "DELETE_ATTRIBUTE"

EVENT_ADD_ATTRIBUTE_VALUE = "ADD_ATTRIBUTE_VALUE"

EVENT_EDIT_ATTRIBUTE_VALUE = "EDIT_ATTRIBUTE_VALUE"

EVENT_DELETE_ATTRIBUTE_VALUE = "DELETE_ATTRIBUTE_VALUE"

EVENT_AS_TEMPLATE = "AS_VM_TEMPLATE"

EVENT_AS_VM = "AS_VM"

EVENT_ADD_DEVICE = "ADD_DEVICE"

EVENT_REMOVE_DEVICE = "REMOVE_DEVICE"


EVENT_TYPE_HOST = "HOST"

EVENT_ENABLE_CPU_EXCLUSIVE = "ENABLE_CPU_EXCLUSIVE"

EVENT_DATA_PORT_CONVERT = "START_DATA_PORT_CONVERSION"

EVENT_DATA_PORT_DELETE = "DATA_PORT_DELETE"

EVENT_ENABLE_CPU_QOS_RESERVATION = "ENABLE_CPU_QOS_RESERVATION"
