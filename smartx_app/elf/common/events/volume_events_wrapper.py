# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
from common.event.event_message import EventMessage, build_event, event_enclosure
from smartx_app.elf.common.events import message_i18n_elf as i18n
from smartx_app.elf.common.events.constants import (
    EVENT_CLONE_VOLUME,
    EVENT_CREATE_VOLUME,
    EVENT_CREATE_VOLUME_FROM_STORAGE_OBJ,
    EVENT_DELETE_VOLUME,
    EVENT_EDIT_VOLUME,
    EVENT_REBUILD_VOLUME_FROM_SNAPSHOT,
    EVENT_UPLOAD_VOLUME,
)
from smartx_app.elf.common.resources.storage_policy import StoragePolicy
from smartx_app.elf.common.utils import storage_cluster as sc_utils
from smartx_app.elf.common.utils import zbs_iscsi
from smartx_app.elf.job_center.constants import KVM_VOL, KVM_VOL_ISCSI
from zbs.nfs.client import ZbsNFS


class VolumeEventWrapper:
    def __init__(
        self,
        user=None,
        uuid=None,
        name=None,
        detail=None,
        volume_snapshot_name=None,
        volume_type=None,
        storage_policy_uuid=None,
    ):
        self.user = user
        self.user_name = user.get("username", "-") if user else None
        self.user_role = user.get("Role") if user else None
        self.uuid = uuid
        self.name = name
        self.detail = detail
        self.volume_snapshot_name = volume_snapshot_name
        self.volume_type = volume_type
        self.storage_policy_uuid = storage_policy_uuid

    def volume_event_splicing(self, event_name, detail=None):
        volume_data = {"volume_name": self.name, "volume_id": self.uuid}
        message_arg = {"volume_name": self.name}
        resources = {self.uuid: self.volume_type}
        return build_event(
            event_name=event_name,
            user=self.user_name,
            user_role=self.user_role,
            data=volume_data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(event_name),
            **message_arg,
        )

    def _event_volume_rebuild(self):
        volume_data = {"volume_name": self.name, "volume_id": self.uuid}
        message_arg = {"volume_name": self.name, "volume_snapshot_name": self.volume_snapshot_name}
        resources = {self.uuid: self.volume_type}
        return build_event(
            event_name=EVENT_REBUILD_VOLUME_FROM_SNAPSHOT,
            user=self.user_name,
            user_role=self.user_role,
            data=volume_data,
            resources=resources,
            detail=self.detail,
            event_mod=i18n.event_message.get(EVENT_REBUILD_VOLUME_FROM_SNAPSHOT),
            **message_arg,
        )

    @event_enclosure
    def event_volume_create(self, volume):
        if volume.__dict__.get("new_size_in_byte"):
            size = volume.new_size_in_byte
        else:
            size = volume.size_in_byte
        volume_arg = {
            "vol_name": volume.name,
            "vol_size": size >> 30,
            "storage_policy_name": StoragePolicy.get_storage_policy_name(volume.storage_policy_uuid),
            "encryption_algorithm": volume.encryption_algorithm,
        }
        if volume.sharing:
            detail = EventMessage.update_detail(i18n.event_detail.get("CREATE_VOLUME_SHARED"), **volume_arg)
        else:
            detail = EventMessage.update_detail(i18n.event_detail.get("CREATE_VOLUME"), **volume_arg)

        return self.volume_event_splicing(EVENT_CREATE_VOLUME, detail)

    @event_enclosure
    def event_volume_create_from_storage_obj(self, volumes=None):
        detail = None
        resources = {}
        for volume in volumes:
            if volume.src_target_name is not None:
                iscsi_client = zbs_iscsi.ZbsClientWrapper(sc_utils.init_zbs_iscsi_client(volume.storage_cluster_uuid))
                lun = iscsi_client.lun_get(pool_name=volume.src_target_name, lun_id=volume.src_lun_id)
                datastore = volume.src_target_name
                obj_name = lun.name
                obj_type = "ISCSI LUN"
                resources.update({volume.uuid: KVM_VOL_ISCSI})
            else:
                export = ZbsNFS("").pool_show_by_id(volume.src_export_id)
                node = ZbsNFS("").inode_show(volume.src_inode_id)
                datastore = export.name
                obj_name = node.name
                obj_type = "NFS FILE"
                resources.update({volume.uuid: KVM_VOL})
            if volume.__dict__.get("new_size_in_byte"):
                size = volume.new_size_in_byte
            else:
                size = volume.size_in_byte
            volume_arg = {
                "vol_name": volume.name,
                "obj_type": obj_type,
                "datastore": datastore,
                "obj_name": obj_name,
                "vol_size": size >> 30,
                "storage_policy_name": StoragePolicy.get_storage_policy_name(volume.storage_policy_uuid),
            }

            if volume.sharing:
                detail = EventMessage.update_detail(
                    i18n.event_detail.get("CREATE_VOLUME_FROM_STORAGE_OBJ_SHARED"), **volume_arg
                )
            else:
                detail = EventMessage.update_detail(
                    detail, i18n.event_detail.get("CREATE_VOLUME_FROM_STORAGE_OBJ"), **volume_arg
                )
        volume_data = {
            "volume_name": ", ".join([volume.name for volume in volumes]),
            "volume_id": ", ".join([volume.uuid for volume in volumes]),
        }
        message_arg = {"volume_num": len([volume.name for volume in volumes])}
        return build_event(
            event_name=EVENT_CREATE_VOLUME_FROM_STORAGE_OBJ,
            user=self.user_name,
            user_role=self.user_role,
            data=volume_data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(EVENT_CREATE_VOLUME_FROM_STORAGE_OBJ),
            **message_arg,
        )

    @event_enclosure
    def event_volume_update(
        self, vol_json=None, name=None, description=None, size_in_byte=None, storage_policy_uuid=None
    ):
        detail = i18n.event_detail.get("EDIT_INIT")
        if name and vol_json["name"] != name:
            name_arg = {"old_name": vol_json["name"], "new_name": name}
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("VOLUME_NAME_CHANGED"), **name_arg)

        if size_in_byte and size_in_byte != vol_json["size_in_byte"]:
            size_arg = {"old_size": vol_json["size_in_byte"] >> 30, "new_size": size_in_byte >> 30}
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("VOLUME_SIZE_CHANGED"), **size_arg)

        if description and vol_json["description"] != description:
            description_arg = {"new_description": description}
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("DESCRIPTION_CHANGED"), **description_arg)

        if storage_policy_uuid and self.storage_policy_uuid != storage_policy_uuid:
            storage_policy_arg = {
                "old_name": StoragePolicy.get_storage_policy_name(self.storage_policy_uuid),
                "new_name": StoragePolicy.get_storage_policy_name(storage_policy_uuid),
            }
            detail = EventMessage.update_detail(
                detail, i18n.event_detail.get("VOLUME_STORAGE_POLICY_CHANGED"), **storage_policy_arg
            )
        return self.volume_event_splicing(EVENT_EDIT_VOLUME, detail)

    @event_enclosure
    def event_volume_delete(self):
        return self.volume_event_splicing(EVENT_DELETE_VOLUME)

    @event_enclosure
    def event_volume_upload(self, volume):
        volume_arg = {
            "vol_name": volume["image_name"],
            "vol_size": volume["size_in_byte"] >> 30,
            "storage_policy_name": StoragePolicy.get_storage_policy_name(
                volume["storage_policy_uuid"] or StoragePolicy.DEFAULT_STORAGE_POLICY_UUID
            ),
        }
        if volume.get("sharing", False):
            detail = EventMessage.update_detail(i18n.event_detail.get("UPLOAD_VOLUME_SHARED"), **volume_arg)
        else:
            detail = EventMessage.update_detail(i18n.event_detail.get("UPLOAD_VOLUME"), **volume_arg)
        return self.volume_event_splicing(EVENT_UPLOAD_VOLUME, detail)

    @event_enclosure
    def event_volume_clone(self, src_volume_name):
        volume_data = {"volume_name": self.name, "volume_id": self.uuid}
        message_arg = {"src_volume_name": src_volume_name, "volume_name": self.name}
        resources = {self.uuid: self.volume_type}
        return build_event(
            event_name=EVENT_CLONE_VOLUME,
            user=self.user_name,
            user_role=self.user_role,
            data=volume_data,
            resources=resources,
            event_mod=i18n.event_message.get(EVENT_CLONE_VOLUME),
            **message_arg,
        )

    @event_enclosure
    def event_volume_rebuild(self, snapshot, volume):
        if snapshot.sharing:
            detail = i18n.event_detail.get("REBUILD_VOLUME_SHARE")
        else:
            detail = i18n.event_detail.get("REBUILD_VOLUME")
        storage_policy_name = None
        if volume.get("storage_policy_uuid"):
            storage_policy_name = StoragePolicy.get_storage_policy_name(volume["storage_policy_uuid"])
        volume_arg = {
            "volume_name": volume["name"],
            "size": snapshot.volume_size >> 30,
            "storage_policy": storage_policy_name,
            "description": volume["description"],
        }
        detail = EventMessage.update_detail(detail, **volume_arg)
        return VolumeEventWrapper(
            user=self.user,
            uuid=volume["uuid"],
            name=volume["name"],
            volume_type=volume["type"],
            volume_snapshot_name=snapshot.name,
            detail=detail,
        )._event_volume_rebuild()
