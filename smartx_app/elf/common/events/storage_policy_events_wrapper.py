# Copyright (c) 2013-2018, SMARTX
# All rights reserved.

from common.event.event_message import EventMessage, build_event, event_enclosure, generate_events
from smartx_app.elf.common.events.constants import (
    EVENT_CREATE_STORAGE_POLICY,
    EVENT_DELETE_STORAGE_POLICY,
    EVENT_DELETE_STORAGE_POLICY_TARGETS,
    EVENT_EDIT_STORAGE_POLICY,
)
import smartx_app.elf.common.events.message_i18n_elf as i18n


class StoragePolicyEventWrapper:
    def __init__(self, obj, user, event_state=None, data=None):
        self.obj = obj
        self.user_name = user.get("username", "-")
        self.user_role = user.get("Role")
        self.event_state = event_state
        self.data = data

    def storage_policy_event_splicing(self, event_name, detail=None):
        storage_policy_data = {"storage_policy_name": self.obj.name, "storage_policy_id": self.obj.uuid}
        message_arg = {"storage_policy_name": self.obj.name}
        resources = {self.obj.uuid: None}
        if detail is None:
            detail = {"zh_CN": "None", "en_US": "None"}
        event_arg = build_event(
            event_name=event_name,
            user=self.user_name,
            user_role=self.user_role,
            data=storage_policy_data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(event_name),
            **message_arg,
        )
        return generate_events(event_state=self.event_state, **event_arg)

    @event_enclosure
    def event_storage_policy_create(self):
        detail = self.generate_create_event_detail()
        return self.storage_policy_event_splicing(event_name=EVENT_CREATE_STORAGE_POLICY, detail=detail)

    @event_enclosure
    def event_storage_policy_update(self):
        detail = self.generate_update_event_detail()
        return self.storage_policy_event_splicing(event_name=EVENT_EDIT_STORAGE_POLICY, detail=detail)

    @event_enclosure
    def event_storage_policy_delete(self):
        return self.storage_policy_event_splicing(EVENT_DELETE_STORAGE_POLICY)

    @event_enclosure
    def event_storage_policy_targets_delete(self):
        return self.storage_policy_event_splicing(EVENT_DELETE_STORAGE_POLICY_TARGETS)

    def generate_create_event_detail(self):
        detail = i18n.event_detail.get("CREATE_STORAGE_POLICY_INIT")
        detail = EventMessage.update_detail(details=detail, **self.obj.event_message_common_details())
        detail = EventMessage.update_detail(
            details=detail,
            event_detail_tmp=i18n.event_detail.get(self.obj.EVENT_MESSAGE_DETAIL_KEY),
            **self.obj.event_message_type_specific_details(),
        )

        if self.obj.thin_provision:
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("THIN_CONFIG_ON"))
        else:
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("THIN_CONFIG_OFF"))
        return detail

    def generate_update_event_detail(self):
        if (
            self.data.get("whitelist") == self.obj.whitelist
            and self.data.get("name") == self.obj.name
            and self.data.get("description") == self.obj.description
        ):
            return None
        detail = i18n.event_detail.get("MODIFY")
        detail = EventMessage.update_detail(detail)
        if self.data.get("whitelist") and self.data.get("whitelist") != self.obj.whitelist:
            whitelist_arg = {"old_white_list": self.obj.whitelist, "new_white_list": self.data.get("whitelist")}
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("WHITE_LIST_CHANGED"), **whitelist_arg)
        if self.data.get("name") and self.data.get("name") != self.obj.name:
            name = {"old_name": self.obj.name, "new_name": self.data.get("name")}
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("STORAGE_POLICY_NAME_CHANGED"), **name)
        if self.data.get("description") and self.data.get("description") != self.obj.description:
            description = {"new_description": self.data.get("description")}
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("DESCRIPTION_CHANGED"), **description)
        return detail
