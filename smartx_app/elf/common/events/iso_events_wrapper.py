# Copyright (c) 2013-2015, SMARTX
# All rights reserved.


from common.event.event_message import EventMessage, build_event, event_enclosure
from smartx_app.elf.common.events.constants import EVENT_DELETE_ISO, EVENT_EDIT_ISO, EVENT_UPLOAD_ISO
import smartx_app.elf.common.events.message_i18n_elf as i18n


class ISOEventWrapper:
    def __init__(self, user, image=None):
        self.user_name = user.get("username", "-")
        self.user_role = user.get("Role")
        self.image = image

    def iso_event_splicing(self, event_name, detail=None):
        image_data = {"iso_name": self.image["name"], "iso_id": self.image["uuid"]}
        message_arg = {"iso_name": self.image["name"]}
        if detail is None:
            detail = {"zh_CN": "None", "en_US": "None"}
        resources = {self.image["uuid"]: self.image["type"]}
        return build_event(
            event_name=event_name,
            user=self.user_name,
            user_role=self.user_role,
            data=image_data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(event_name),
            **message_arg,
        )

    @event_enclosure
    def event_iso_delete(self):
        return self.iso_event_splicing(EVENT_DELETE_ISO)

    @event_enclosure
    def event_iso_upload(self):
        iso_arg = {
            "iso_name": self.image["name"],
            "description": self.image["description"] if self.image["description"] != "" else "-",
        }
        detail = EventMessage.update_detail(i18n.event_detail.get("CREATE_ISO"), **iso_arg)
        return self.iso_event_splicing(EVENT_UPLOAD_ISO, detail)

    @event_enclosure
    def event_iso_update(self, update):
        detail = i18n.event_detail.get("EDIT_INIT")
        if update.get("name") and update["name"] != self.image["name"]:
            name_arg = {"old_name": self.image["name"], "new_name": update.get("name")}
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("ISO_NAME_CHANGED"), **name_arg)
        if self.image["description"] != update.get("description"):
            if update.get("description"):
                description_arg = {"new_description": update.get("description")}
            else:
                description_arg = {"new_description": "-"}
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("DESCRIPTION_CHANGED"), **description_arg)
        return self.iso_event_splicing(EVENT_EDIT_ISO, detail)
