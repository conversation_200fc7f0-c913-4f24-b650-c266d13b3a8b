# Copyright (c) 2013-2015, SMARTX
# All rights reserved.

import copy

from common.event.event_message import EventMessage, build_event, event_enclosure, generate_events
from smartx_app.common.constants import CDROM
from smartx_app.elf.common import constants as elf_common_constants
from smartx_app.elf.common.constants import (
    PASS_THROUGH_GPU,
    PASS_THROUGH_NIC,
    PASS_THROUGH_PCI,
    PASS_THROUGH_VF_PCI,
    VM_INTERFACE_SRIOV,
    VM_INTERFACE_UP,
    VM_INTERFACE_VIRTIO,
)
from smartx_app.elf.common.events.constants import (
    EVENT_AS_TEMPLATE,
    EVENT_AUTO_DETACH_VM_USB,
    EVENT_BATCH_DELETE_VM,
    EVENT_BATCH_FORCE_REBOOT_VM,
    EVENT_BATCH_FORCE_SHUTDOWN_VM,
    EVENT_BATCH_MIGRATE_VM,
    EVENT_BATCH_REBOOT_VM,
    EVENT_BATCH_RESUME_VM,
    EVENT_BATCH_SHUTDOWN_VM,
    EVENT_BATCH_START_VM,
    EVENT_BATCH_SUSPEND_VM,
    EVENT_BOOT_VM_WITH_HOST,
    EVENT_CHANGE_FOLDER,
    EVENT_CLONE_VM,
    EVENT_CREATE_VM,
    EVENT_DELETE_VM,
    EVENT_EDIT_VM,
    EVENT_EDIT_VM_DEVICE,
    EVENT_EDIT_VM_NIC,
    EVENT_EDIT_VM_VOLUME,
    EVENT_ENABLE_CPU_EXCLUSIVE,
    EVENT_ENABLE_CPU_QOS_RESERVATION,
    EVENT_FORCE_REBOOT_VM,
    EVENT_FORCE_SHUTDOWN_VM,
    EVENT_MIGRATE_VM,
    EVENT_MOVE_OUT_FOLDER,
    EVENT_REBOOT_VM,
    EVENT_REBUILD_VM_FROM_SNAPSHOT,
    EVENT_RESUME_VM,
    EVENT_SET_FOLDER,
    EVENT_SHUTDOWN_VM,
    EVENT_START_VM,
    EVENT_SUSPEND_VM,
    EVENT_VM_BIND_ATTRIBUTE,
    EVENT_VM_HA,
    EVENT_VM_LOCAL_HA,
    EVENT_VM_NETWORK_HA,
    EVENT_VM_OS_HA,
    EVENT_VM_ROLLBACK,
    EVENT_VM_SELF_FENCE_PAUSE,
    EVENT_VM_SELF_FENCE_STOP,
    EVENT_VM_STATE_CHANGE,
    EVENT_VM_UNBIND_ATTRIBUTE,
)
import smartx_app.elf.common.events.message_i18n_elf as i18n
from smartx_app.elf.common.resource_wrappers import vmtools_wrapper
from smartx_app.elf.common.resource_wrappers.volume_wrapper import Volume, VolumeWrapper
from smartx_app.elf.common.resources import storage_policy
from smartx_app.elf.common.resources.folder import Folder
from smartx_app.elf.common.resources.iso_image import get_iso_name
from smartx_app.elf.common.resources.volume_snapshot import Snapshot
from smartx_app.elf.common.resources.volume_template import VolumeTemplate
from smartx_app.elf.common.utils import pci_device_cache
from smartx_app.elf.common.utils.network import random_mac_addr
from smartx_app.elf.job_center.constants import (
    DISK,
    KVM_VM,
    KVM_VM_SNAPSHOT,
    KVM_VOL,
    KVM_VOL_ISCSI,
    KVM_VOL_ISCSI_SNAPSHOT,
    KVM_VOL_SNAPSHOT,
)
from smartx_app.network.common.utils.vds_utils import get_vlan


class VMEventWrapper:
    def __init__(self, user=None, vm_uuid=None, vm_name=None, detail=None, vm_snapshot_name=None, user_type="USER"):
        self.user = user
        self.user_name = user.get("username", "-") if user else None
        self.user_role = user.get("Role") if user else None
        self.vm_uuid = vm_uuid
        self.vm_name = vm_name
        self.detail = detail
        self.vm_snapshot_name = vm_snapshot_name
        self.user_type = user_type

    @staticmethod
    def get_iso_name(iso_path):
        if not iso_path:
            return {}
        iso = get_iso_name(iso_path)
        if iso:
            return iso
        else:
            svt_images = vmtools_wrapper.VMToolsWrapper.get_images(path=iso_path)
            if svt_images:
                return {"name": svt_images[0].get("name")}
        return {}

    @event_enclosure
    def event_vm_splicing(self, event_name, message_arg=None):
        vm_data = {"vm_id": self.vm_uuid, "vm_name": self.vm_name}
        message_arg = message_arg or {"vm_name": self.vm_name}
        resources = {self.vm_uuid: KVM_VM}
        return build_event(
            event_name=event_name,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_data,
            resources=resources,
            detail=self.detail,
            user_type=self.user_type,
            event_mod=i18n.event_message.get(event_name),
            **message_arg,
        )

    def event_batch_vm_splicing(self, vms, event_name, batch_resources=None, failed=None):
        vm_name = []
        vm_id = []
        resources = {}
        batch = {}
        num_arg = {"vm_num": len(vms)}
        detail = EventMessage.update_detail(i18n.event_detail.get(event_name), **num_arg)
        batch_resources = batch_resources if batch_resources else {}
        for vm in vms:
            resources.update({vm.vm_doc["uuid"]: KVM_VM})
            vm_id.append(vm.vm_doc["uuid"])
            vm_name.append(vm.vm_doc["vm_name"])
            batch, detail = self.generate_batch_detail(
                failed=failed,
                uuid=vm.vm_doc["uuid"],
                batch=batch,
                name=vm.vm_doc["vm_name"],
                i18n=i18n.event_detail.get("BATCH_VM_NAME"),
                detail_vm=detail,
                done_detail={"zh_CN": vm.vm_doc["vm_name"], "en_US": vm.vm_doc["vm_name"]},
            )
            detail = self.generate_delete_detail(detail, batch_resources.get(vm.vm_doc["uuid"]))
        vm_data = {"vm_id": ",".join(vm_id), "vm_name": ",".join(vm_name)}
        message_arg = {"vm_num": len(vm_name)}
        return build_event(
            event_name=event_name,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_data,
            resources=resources,
            detail=detail,
            batch=batch,
            event_mod=i18n.event_message.get(event_name),
            **message_arg,
        )

    @event_enclosure
    def event_vm_start(self):
        return self.event_vm_splicing(EVENT_START_VM)

    @event_enclosure
    def event_boot_vm_with_host(self, node_hostname):
        message_arg = {
            "vm_name": self.vm_name,
            "node_hostname": node_hostname,
        }
        return self.event_vm_splicing(EVENT_BOOT_VM_WITH_HOST, message_arg)

    @event_enclosure
    def event_vm_stop(self):
        return self.event_vm_splicing(EVENT_SHUTDOWN_VM)

    def _event_vm_create(self):
        return self.event_vm_splicing(EVENT_CREATE_VM)

    @event_enclosure
    def event_vm_update(self):
        return self.event_vm_splicing(EVENT_EDIT_VM)

    def _event_vm_edit_nics(self):
        return self.event_vm_splicing(EVENT_EDIT_VM_NIC)

    def _event_vm_edit_devices(self):
        return self.event_vm_splicing(EVENT_EDIT_VM_DEVICE)

    def _event_vm_edit_disks(self):
        return self.event_vm_splicing(EVENT_EDIT_VM_VOLUME)

    def _event_vm_auto_detach_usbs(self):
        return self.event_vm_splicing(EVENT_AUTO_DETACH_VM_USB)

    @staticmethod
    def generate_delete_detail(detail, resources):
        if not resources:
            return detail
        vmss, vols, vss = [], [], []
        for r in list(resources.values()):
            t = r["type"]
            if t == KVM_VM_SNAPSHOT:
                vmss.append(r["name"])
            elif t in [KVM_VOL_ISCSI_SNAPSHOT, KVM_VOL_SNAPSHOT]:
                vss.append(r["name"])
            elif t in [KVM_VOL_ISCSI, KVM_VOL]:
                vols.append(r["name"])
        if vmss:
            detail = EventMessage.update_detail(
                detail, i18n.event_detail.get("DELETE_VM_SNAPSHOT"), delete_vm_snapshots=", ".join(vmss)
            )
        if vols:
            detail = EventMessage.update_detail(
                detail, i18n.event_detail.get("DELETE_VOLUME"), delete_volumes=", ".join(vols)
            )
        if vss:
            detail = EventMessage.update_detail(
                detail, i18n.event_detail.get("DELETE_VOLUME_SNAPSHOT"), delete_volume_snapshots=", ".join(vss)
            )
        return detail

    @event_enclosure
    def event_vm_delete(self, resources):
        detail = {"zh_CN": "", "en_US": ""}
        self.detail = self.generate_delete_detail(detail, resources)
        return self.event_vm_splicing(EVENT_DELETE_VM)

    @event_enclosure
    def event_vm_pause(self):
        return self.event_vm_splicing(EVENT_SUSPEND_VM)

    @event_enclosure
    def event_vm_resume(self):
        return self.event_vm_splicing(EVENT_RESUME_VM)

    @event_enclosure
    def event_vm_migrate(self, old_host=None, new_host=None):
        vm_data = {"vm_id": self.vm_uuid, "vm_name": self.vm_name}
        message_arg = {"vm_name": self.vm_name, "old_host": old_host, "new_host": new_host}
        resources = {self.vm_uuid: KVM_VM}
        return build_event(
            event_name=EVENT_MIGRATE_VM,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_data,
            resources=resources,
            detail=self.detail,
            user_type=self.user_type,
            event_mod=i18n.event_message.get(EVENT_MIGRATE_VM),
            **message_arg,
        )

    @event_enclosure
    def event_vm_reboot(self):
        return self.event_vm_splicing(EVENT_REBOOT_VM)

    @event_enclosure
    def event_vm_force_stop(self):
        return self.event_vm_splicing(EVENT_FORCE_SHUTDOWN_VM)

    @event_enclosure
    def event_vm_force_reboot(self):
        return self.event_vm_splicing(EVENT_FORCE_REBOOT_VM)

    @event_enclosure
    def event_vm_clone(self, vm_doc, disks, is_full_copy, folder_uuid, modify_disks, delete_disks, cpu_exclusive):
        vm_disks = []
        update_disks = {d["path"]: d for d in modify_disks if d["type"] == DISK}
        update_cdrom = {d["key"]: d["path"] for d in modify_disks if d["type"] == CDROM}
        delete_disks = [d.get("path", d.get("key")) for d in delete_disks]

        for d in disks:
            if d.get("key", d.get("path")) in delete_disks:
                continue
            else:
                if d["type"] == DISK:
                    disk = {"size_in_byte": d.get("size_in_byte", d.get("new_size_in_byte", 0))}
                    for k in ("path", "boot", "quota_policy", "type", "name", "bus", "storage_policy_uuid"):
                        disk[k] = d.get(k)

                    update_disk = update_disks.get(d.get("path"))
                    if update_disk:
                        disk["bus"] = update_disk["bus"]
                        disk["name"] = update_disk["name"]
                        if "storage_policy_uuid" in update_disk:
                            disk["storage_policy_uuid"] = update_disk["storage_policy_uuid"]
                    vm_disks.append(disk)

                else:
                    path = update_cdrom.get(d["key"])
                    vm_disks.append(
                        {
                            "bus": d.get("bus"),
                            "disabled": d.get("disabled"),
                            "key": d.get("key"),
                            "path": path if path else d.get("path"),
                            "boot": d.get("boot"),
                            "type": d.get("type"),
                        }
                    )

        detail = i18n.event_detail.get("CLONE_VM_INIT")
        detail = self.get_vm_detail(vm_doc, vm_disks, detail, folder_uuid=folder_uuid or "")
        if is_full_copy:
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("CREATE_MODE_FULL_COPY"))
        else:
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("CREATE_MODE_NORMAL"))

        pci_devs = [
            d
            for d in vm_doc.get("hostdevs", [])
            if d["type"] in (PASS_THROUGH_GPU, PASS_THROUGH_NIC, PASS_THROUGH_PCI, PASS_THROUGH_VF_PCI)
        ]
        if pci_devs:
            pci_dev_info_cache = pci_device_cache.PCIDeviceInfoCache()
            detail = self.update_pci_devices_event_message_detail(
                pci_devs, vm_doc["node_ip"], pci_dev_info_cache, detail
            )

        return VMEventWrapper(
            user=self.user, vm_uuid=vm_doc["uuid"], vm_name=vm_doc["vm_name"], detail=detail
        ).event_vm_splicing(EVENT_CLONE_VM)

    @staticmethod
    def update_pci_devices_event_message_detail(pci_devs, data_ip, pci_dev_info_cache, event_message_detail):
        for pci_dev in pci_devs:
            pci_dev_info = pci_dev_info_cache.get_pci_device_info(data_ip, pci_dev["uuid"], pci_dev["type"])
            name = {"name": pci_dev["uuid"]}
            if pci_dev_info and "name" in pci_dev_info:
                name = {"name": "{}({})".format(pci_dev_info["name"], pci_dev["uuid"])}
            event_message_detail = EventMessage.update_detail(
                event_message_detail, i18n.event_detail.get(pci_dev["type"]), **name
            )

        return event_message_detail

    @event_enclosure
    def event_vm_rollback(self):
        vm_data = {"vm_id": self.vm_uuid, "vm_name": self.vm_name}
        message_arg = {"vm_name": self.vm_name, "vm_snapshot_name": self.vm_snapshot_name}
        resources = {self.vm_uuid: KVM_VM}
        return build_event(
            event_name=EVENT_VM_ROLLBACK,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_data,
            resources=resources,
            detail=self.detail,
            event_mod=i18n.event_message.get(EVENT_VM_ROLLBACK),
            **message_arg,
        )

    def _event_vm_rebuild(self):
        vm_data = {"vm_id": self.vm_uuid, "vm_name": self.vm_name}
        message_arg = {"vm_name": self.vm_name, "vm_snapshot_name": self.vm_snapshot_name}
        resources = {self.vm_uuid: KVM_VM}
        return build_event(
            event_name=EVENT_REBUILD_VM_FROM_SNAPSHOT,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_data,
            resources=resources,
            detail=self.detail,
            event_mod=i18n.event_message.get(EVENT_REBUILD_VM_FROM_SNAPSHOT),
            **message_arg,
        )

    @event_enclosure
    def event_vm_status_changed(self, old_state, new_state, host_name):
        vm_data = {"vm_name": self.vm_name, "vm_id": self.vm_uuid}
        message_arg = {"vm_name": self.vm_name, "old_state": old_state, "new_state": new_state, "host_name": host_name}
        resources = {self.vm_uuid: KVM_VM}
        detail = {"zh_CN": "None", "en_US": "None"}
        return build_event(
            event_name=EVENT_VM_STATE_CHANGE,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_data,
            resources=resources,
            detail=detail,
            user_type="SYSTEM",
            event_mod=i18n.event_message.get(EVENT_VM_STATE_CHANGE),
            **message_arg,
        )

    @event_enclosure
    def event_vm_self_fence_splicing(self, event_name, host_name, fence_time):
        vm_data = {"vm_name": self.vm_name, "vm_id": self.vm_uuid, "fence_time": fence_time}
        message_arg = {"host_name": host_name, "vm_name": self.vm_name}
        resources = {self.vm_uuid: KVM_VM}
        detail = {"zh_CN": "None", "en_US": "None"}
        return build_event(
            event_name=event_name,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_data,
            resources=resources,
            detail=detail,
            user_type="SYSTEM",
            event_mod=i18n.event_message.get(event_name),
            **message_arg,
        )

    @event_enclosure
    def event_vm_self_fence_pause(self, host_name, fence_time):
        return self.event_vm_self_fence_splicing(EVENT_VM_SELF_FENCE_PAUSE, host_name, fence_time)

    @event_enclosure
    def event_vm_self_fence_stop(self, host_name, fence_time):
        return self.event_vm_self_fence_splicing(EVENT_VM_SELF_FENCE_STOP, host_name, fence_time)

    @event_enclosure
    def event_vm_ha(
        self,
        rebuild_host_name,
        origin_host_name=None,
        is_local=False,
        is_local_boot=False,
        asiainfo_gvm_disable_am=False,
    ):
        if is_local:
            event_name = EVENT_VM_LOCAL_HA
            message_arg = {"vm_name": self.vm_name, "rebuild_host_name": rebuild_host_name}
        else:
            event_name = EVENT_VM_HA
            message_arg = {
                "vm_name": self.vm_name,
                "rebuild_host_name": rebuild_host_name,
                "origin_host_name": origin_host_name,
            }

        vm_data = {"vm_name": self.vm_name, "vm_id": self.vm_uuid}
        # The operation field is used for boot HA,
        # and its current possible values are either boot or not exist.
        if is_local_boot:
            vm_data["operation"] = "boot"

        resources = {self.vm_uuid: KVM_VM}
        detail = {"zh_CN": "None", "en_US": "None"}

        event_mod = i18n.event_message.get(event_name)
        if asiainfo_gvm_disable_am:
            event_mod = EventMessage.merge_event_mod(event_mod, i18n.event_message.get("DISABLE_AM_FOR_HA"))

        return build_event(
            event_name=event_name,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_data,
            resources=resources,
            detail=detail,
            user_type="SYSTEM",
            event_mod=event_mod,
            **message_arg,
        )

    @event_enclosure
    def event_vm_network_ha(self, dest_host_name, origin_host_name, action_type="VM_NETWORK_HA_MIGRATE"):
        """
        :param dest_host_name: Destination host name
        :param origin_host_name: Origin host name
        :param action_type: Action type for network HA, should be one of:
            - 'VM_NETWORK_HA_MIGRATE' (live migration)
            - 'VM_NETWORK_HA_REBUILD' (rebuild)
        """
        vm_data = {"vm_name": self.vm_name, "vm_id": self.vm_uuid}
        # Prepare multi-language action_desc with None check
        action_i18n = i18n.event_message.get(action_type) or {}
        action_desc = {"zh_CN": action_i18n.get("zh_CN", ""), "en_US": action_i18n.get("en_US", "")}
        # Fill multi-language placeholders in event_mod
        event_mod = EventMessage.fill_multilang_placeholders(
            i18n.event_message.get(EVENT_VM_NETWORK_HA), action_desc=action_desc
        )
        message_arg = {
            "vm_name": self.vm_name,
            "dest_host_name": dest_host_name,
            "origin_host_name": origin_host_name,
        }

        return build_event(
            event_name=EVENT_VM_NETWORK_HA,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_data,
            resources={self.vm_uuid: KVM_VM},
            detail={"zh_CN": "None", "en_US": "None"},
            user_type="SYSTEM",
            event_mod=event_mod,
            **message_arg,
        )

    @event_enclosure
    def event_vm_os_ha(self, host_name, screenshot_host_uuid, screenshot_file_uuid):
        vm_data = {
            "vm_name": self.vm_name,
            "vm_id": self.vm_uuid,
            "screenshot_host_uuid": screenshot_host_uuid,
            "screenshot_file_uuid": screenshot_file_uuid,
        }
        message_arg = {
            "vm_name": self.vm_name,
            "host_name": host_name,
        }

        return build_event(
            event_name=EVENT_VM_OS_HA,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_data,
            resources={self.vm_uuid: KVM_VM},
            detail={"zh_CN": "None", "en_US": "None"},
            user_type="SYSTEM",
            event_mod=i18n.event_message.get(EVENT_VM_OS_HA),
            **message_arg,
        )

    @event_enclosure
    def event_vm_edit(self, vm_doc, data):
        from smartx_app.elf.common.utils import cpu_qos

        detail = i18n.event_detail.get("EDIT_VM_INIT")
        if vm_doc["vm_name"] != data.get("vm_name", None) and data.get("vm_name", None) is not None:
            name = {"old_name": vm_doc["vm_name"], "new_name": data.get("vm_name")}
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("VM_NAME_CHANGED"), **name)

        if vm_doc["ha"] != data.get("ha", None) and data.get("ha", None) is not None:
            if data.get("ha") is True:
                detail = EventMessage.update_detail(detail, i18n.event_detail.get("HA_TO_ON"))
            else:
                detail = EventMessage.update_detail(detail, i18n.event_detail.get("HA_TO_OFF"))

        if vm_doc["vcpu"] != data.get("vcpu", None) and data.get("vcpu", None) is not None:
            vcpu = {"old_vcpu": vm_doc["vcpu"], "new_vcpu": data["vcpu"]}
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("VCPU_CHANGED"), **vcpu)

        if data.get("cpu", None) is not None:
            if vm_doc["cpu"]["topology"]["cores"] != data["cpu"]["topology"]["cores"]:
                cores = {"old_core": vm_doc["cpu"]["topology"]["cores"], "new_core": data["cpu"]["topology"]["cores"]}
                detail = EventMessage.update_detail(detail, i18n.event_detail.get("CPU_SLOT_CORE_CHANGED"), **cores)

            if vm_doc["cpu"]["topology"]["sockets"] != data["cpu"]["topology"]["sockets"]:
                sockets = {
                    "old_slot_num": vm_doc["cpu"]["topology"]["sockets"],
                    "new_slot_num": data["cpu"]["topology"]["sockets"],
                }
                detail = EventMessage.update_detail(detail, i18n.event_detail.get("CPU_SLOT_CHANGED"), **sockets)

        if data.get("memory", None) is not None and vm_doc["memory"] != data.get("memory", None):
            memory = {
                "old_mem": float(vm_doc["memory"]) / (1024 * 1024 * 1024),
                "new_mem": float(data["memory"]) / (1024 * 1024 * 1024),
            }
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("MEMORY_CHANGED"), **memory)

        if data.get("description", None) is not None and vm_doc["description"] != data.get("description", None):
            description = {"new_description": data["description"]}
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("DESCRIPTION_CHANGED"), **description)

        if data.get("clock_offset", None) is not None and vm_doc["clock_offset"] != data.get("clock_offset", None):
            clock_offset = {"old_clock_offset": vm_doc["clock_offset"], "new_clock_offset": data["clock_offset"]}
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("CLOCK_OFFSET_CHANGED"), **clock_offset)

        if data.get("nested_virtualization", None) is not None and vm_doc["nested_virtualization"] != data.get(
            "nested_virtualization", None
        ):
            if data["nested_virtualization"] is True:
                detail = EventMessage.update_detail(detail, i18n.event_detail.get("NESTED_VIRTUALIZATION_ON"))
            else:
                detail = EventMessage.update_detail(detail, i18n.event_detail.get("NESTED_VIRTUALIZATION_OFF"))

        if data.get("cpu_model", None) is not None and vm_doc["cpu_model"] != data.get("cpu_model", None):
            cpu_model = {"old_cpu_model": vm_doc["cpu_model"], "new_cpu_model": data["cpu_model"]}
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("CPU_MODEL_CHANGED"), **cpu_model)

        if data.get("win_opt", None) is not None and vm_doc["win_opt"] != data.get("win_opt", None):
            if data["win_opt"] is True:
                detail = EventMessage.update_detail(detail, i18n.event_detail.get("WIN_OPT_ON"))
            else:
                detail = EventMessage.update_detail(detail, i18n.event_detail.get("WIN_OPT_OFF"))

        if data.get("firmware", None) is not None and vm_doc["firmware"] != data.get("firmware", None):
            firmware = {"old_firmware": vm_doc["firmware"], "new_firmware": data["firmware"]}
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("FIRMWARE_CHANGED"), **firmware)

        if data.get("sync_vm_time_on_resume") is not None:
            sync_time = {
                "old_sync_opt": vm_doc.get("sync_vm_time_on_resume"),
                "new_sync_opt": data.get("sync_vm_time_on_resume"),
            }
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("SYNC_VM_TIME_ON_RESUME"), **sync_time)

        origin_cpu_exclusive = vm_doc.get("cpu_exclusive", elf_common_constants.DEFAULT_CPU_EXCLUSIVE_JSON)
        if (
            data.get("cpu_exclusive", {}).get("expected_enabled") is not None
            and origin_cpu_exclusive["expected_enabled"] != data["cpu_exclusive"]["expected_enabled"]
        ):
            cpu_exclusive_change = {
                "old_status": origin_cpu_exclusive["expected_enabled"],
                "new_status": data["cpu_exclusive"]["expected_enabled"],
            }
            detail = EventMessage.update_detail(
                detail, i18n.event_detail.get("CPU_EXCLUSIVE_CHANGED"), **cpu_exclusive_change
            )

        origin_cpu_qos = vm_doc.get("cpu_qos", elf_common_constants.DEFAULT_CPU_QOS_JSON)
        updated_cpu_qos = data.get("cpu_qos")
        if updated_cpu_qos is not None and not cpu_qos.are_cpu_qos_equal(origin_cpu_qos, updated_cpu_qos):
            cpu_qos_change = {
                "old_shares": origin_cpu_qos["shares"],
                "new_shares": updated_cpu_qos["shares"],
                "old_reservation_hz": origin_cpu_qos["reservation_hz"],
                "new_reservation_hz": updated_cpu_qos["reservation_hz"],
                "old_limit_hz": origin_cpu_qos["limit_hz"],
                "new_limit_hz": updated_cpu_qos["limit_hz"],
            }
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("CPU_QOS_CHANGED"), **cpu_qos_change)

        return VMEventWrapper(
            user=self.user, vm_uuid=vm_doc["uuid"], vm_name=vm_doc["vm_name"], detail=detail
        ).event_vm_update()

    @event_enclosure
    def event_vm_edit_nics(self, vm_doc, current_nics, expectd_nics):
        detail = i18n.event_detail.get("EDIT_NICS_INIT")
        expected_nics_mapping = {}
        current_nic_mac = [current_nic["mac_address"] for current_nic in current_nics]
        for expectd_nic in expectd_nics:
            if expectd_nic.get("mac_address", None) is None or expectd_nic["mac_address"] not in current_nic_mac:
                if not expectd_nic.get("mac_address"):
                    expectd_nic["mac_address"] = random_mac_addr()
                mac = {"mac": expectd_nic["mac_address"], "model": expectd_nic.get("model", VM_INTERFACE_VIRTIO)}
                detail = EventMessage.update_detail(detail, i18n.event_detail.get("ATTACH_NIC"), **mac)
            expected_nics_mapping[expectd_nic["mac_address"]] = expectd_nic
        for current_nic in current_nics:
            if current_nic["mac_address"] not in expected_nics_mapping:
                mac = {"mac": current_nic["mac_address"]}
                detail = EventMessage.update_detail(detail, i18n.event_detail.get("DETACH_NIC"), **mac)
            else:
                expectd_nic = expected_nics_mapping.get(current_nic["mac_address"], {})
                if expectd_nic:
                    # Check whether the br has been replaced
                    if (
                        current_nic["ovs"] != expectd_nic["ovs"]
                        and current_nic.get("model") != VM_INTERFACE_SRIOV
                        and expectd_nic.get("model") != VM_INTERFACE_SRIOV
                    ):
                        br = {
                            "mac": current_nic["mac_address"],
                            "old_br": current_nic["ovs"],
                            "new_br": expectd_nic["ovs"],
                        }
                        detail = EventMessage.update_detail(detail, i18n.event_detail.get("MODIFY_NIC_BR"), **br)
                    if current_nic["vlan_uuid"] != expectd_nic["vlan_uuid"]:
                        br = {
                            "mac": current_nic["mac_address"],
                            "old_vlan": get_vlan(current_nic["vlan_uuid"]).get("name"),
                            "new_vlan": get_vlan(expectd_nic["vlan_uuid"]).get("name"),
                        }
                        detail = EventMessage.update_detail(detail, i18n.event_detail.get("MODIFY_NIC_VLAN"), **br)
                    if (current_nic.get("ip_address") or expectd_nic.get("ip_address")) and (
                        current_nic.get("ip_address") != expectd_nic.get("ip_address")
                    ):
                        br = {
                            "mac": current_nic["mac_address"],
                            "old_ip": current_nic.get("ip_address", ""),
                            "new_ip": expectd_nic.get("ip_address", ""),
                        }
                        detail = EventMessage.update_detail(detail, i18n.event_detail.get("MODIFY_NIC_IP"), **br)
                    if current_nic.get("link", VM_INTERFACE_UP) != expectd_nic.get("link", VM_INTERFACE_UP):
                        br = {"mac": current_nic["mac_address"]}
                        detail = EventMessage.update_detail(
                            detail,
                            i18n.event_detail.get(
                                "MODIFY_NIC_UP" if expectd_nic.get("link") == VM_INTERFACE_UP else "MODIFY_NIC_DOWN"
                            ),
                            **br,
                        )
                    if current_nic.get("mirror") != expectd_nic.get("mirror"):
                        br = {"mac": current_nic["mac_address"]}
                        detail = EventMessage.update_detail(
                            detail,
                            i18n.event_detail.get(
                                "MODIFY_NIC_MIRROR_SET" if expectd_nic.get("mirror") else "MODIFY_NIC_MIRROR_REMOVE"
                            ),
                            **br,
                        )

        return VMEventWrapper(
            user=self.user, vm_uuid=vm_doc["uuid"], vm_name=vm_doc["vm_name"], detail=detail
        )._event_vm_edit_nics()

    @event_enclosure
    def event_vm_edit_devices(
        self, vm_doc, attached_devices_names=None, detached_devices_names=None, job_description=None
    ):
        from smartx_app.elf.common import code

        detail = i18n.event_detail.get("EDIT_DEVICES_INIT")
        for device_name in attached_devices_names:
            name = {"name": device_name}
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("ATTACH_DEVICE"), **name)
        for device_name in detached_devices_names:
            name = {"name": device_name}
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("DETACH_DEVICE"), **name)

        event_wrapper = VMEventWrapper(
            user=self.user,
            vm_uuid=vm_doc["uuid"],
            vm_name=vm_doc["vm_name"],
            detail=detail,
            user_type=self.user_type,
        )

        if job_description == code.API_OVER_NETWORK_USB_AUTO_DETACH:
            return event_wrapper._event_vm_auto_detach_usbs()

        return event_wrapper._event_vm_edit_devices()

    @event_enclosure
    def event_vm_as_template(self, name, cloud_init_supported=False):
        vm_data = {"vm_name": self.vm_name, "vm_id": self.vm_uuid}
        message_arg = {"vm_name": self.vm_name, "vm_template_name": name, "cloud_init_supported": cloud_init_supported}
        resources = {self.vm_uuid: KVM_VM}
        detail = {"zh_CN": "None", "en_US": "None"}
        return build_event(
            event_name=EVENT_AS_TEMPLATE,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(EVENT_AS_TEMPLATE),
            **message_arg,
        )

    @staticmethod
    def _vm_edit_quota_policy(old_quota_policy, quota_policy):
        def _get_iops_detail(_quota_policy):
            if _quota_policy and _quota_policy.get("max_iops_policy"):
                iops_policy = _quota_policy["max_iops_policy"]
                iops = {"iops": _quota_policy["max_iops"]}
                # schema ensures that the iops_policy is either "dynamic" or "hard"
                if iops_policy == "dynamic":
                    old_iops = EventMessage.update_detail(i18n.event_detail.get("IOPS_QUOTA_DYNAMIC"), None, **iops)
                else:
                    old_iops = EventMessage.update_detail(i18n.event_detail.get("IOPS_QUOTA_HARD"), None, **iops)
                iops_cn = old_iops["zh_CN"]
                iops_us = old_iops["en_US"]
            else:
                iops_cn = i18n.event_detail.get("NO_QUOTA")["zh_CN"]
                iops_us = i18n.event_detail.get("NO_QUOTA")["en_US"]
            return iops_cn, iops_us

        def _get_bandwidth_detail(_quota_policy):
            if _quota_policy and _quota_policy.get("max_bandwidth_policy"):
                bandwidth_policy = _quota_policy["max_bandwidth_policy"]
                bandwidth = {"bandwidth": _quota_policy["max_bandwidth"] // 1000000}
                # schema ensures that the bandwidth_policy is either "dynamic" or "hard"
                if bandwidth_policy == "dynamic":
                    new_bandwidth = EventMessage.update_detail(
                        i18n.event_detail.get("BANDWIDTH_QUOTA_DYNAMIC"), None, **bandwidth
                    )
                else:
                    new_bandwidth = EventMessage.update_detail(
                        i18n.event_detail.get("BANDWIDTH_QUOTA_HARD"), None, **bandwidth
                    )
                bandwidth_cn = new_bandwidth["zh_CN"]
                bandwidth_us = new_bandwidth["en_US"]
            else:
                bandwidth_cn = i18n.event_detail.get("NO_QUOTA")["zh_CN"]
                bandwidth_us = i18n.event_detail.get("NO_QUOTA")["en_US"]
            return bandwidth_cn, bandwidth_us

        quota_policy_detail = None
        if (
            not old_quota_policy
            or not quota_policy
            or old_quota_policy.get("max_iops_policy") != quota_policy.get("max_iops_policy")
            or old_quota_policy.get("max_iops") != quota_policy.get("max_iops")
        ):
            old_iops_cn, old_iops_us = _get_iops_detail(old_quota_policy)
            new_iops_cn, new_iops_us = _get_iops_detail(quota_policy)
            if old_iops_cn != new_iops_cn:
                iops_args = {
                    "old_iops_cn": old_iops_cn,
                    "old_iops_us": old_iops_us,
                    "new_iops_cn": new_iops_cn,
                    "new_iops_us": new_iops_us,
                }
                quota_policy_detail = EventMessage.update_detail(
                    details=quota_policy_detail,
                    event_detail_tmp=i18n.event_detail.get("IO_QUOTA_VOLUME_IOPS_CHANGE"),
                    **iops_args,
                )

        if (
            not old_quota_policy
            or not quota_policy
            or old_quota_policy.get("max_bandwidth_policy") != quota_policy.get("max_bandwidth_policy")
            or old_quota_policy.get("max_bandwidth") != quota_policy.get("max_bandwidth")
        ):
            old_bandwidth_cn, old_bandwidth_us = _get_bandwidth_detail(old_quota_policy)
            new_bandwidth_cn, new_bandwidth_us = _get_bandwidth_detail(quota_policy)
            if new_bandwidth_cn != old_bandwidth_cn:
                bandwidth_args = {
                    "old_bandwidth_cn": old_bandwidth_cn,
                    "old_bandwidth_us": old_bandwidth_us,
                    "new_bandwidth_cn": new_bandwidth_cn,
                    "new_bandwidth_us": new_bandwidth_us,
                }
                quota_policy_detail = EventMessage.update_detail(
                    details=quota_policy_detail,
                    event_detail_tmp=i18n.event_detail.get("IO_QUOTA_VOLUME_BANDWIDTH_CHANGE"),
                    **bandwidth_args,
                )

        return quota_policy_detail

    @event_enclosure
    def event_vm_edit_disks(self, vm_doc, current_disks, expected_disks, del_paths, quota_policy=None):
        attach_detail = None
        modify_detail = None
        detach_detail = None
        delete_detail = None
        iso_null_detail = None
        quota_policy_detail = None
        vols_expected = VolumeWrapper.get_mountable_by_paths(
            paths=[disk["path"] for disk in expected_disks if disk["type"] == DISK and disk.get("path") is not None],
            vm_uuid=vm_doc["uuid"],
        )
        if vm_doc.get("quota_policy") != quota_policy:
            quota_policy_detail = self._vm_edit_quota_policy(vm_doc.get("quota_policy"), quota_policy)

        current_all_disk_paths = []
        current_cdrom_boot_orders = []
        for current_disk in current_disks:
            current_all_disk_paths.append(current_disk["path"])
            if current_disk["type"] == CDROM:
                current_cdrom_boot_orders.append(current_disk["boot"])
        path_to_vol = {vol.path: vol for vol in vols_expected}
        for index, expected_disk in enumerate(expected_disks):
            if not expected_disk.get("path") and expected_disk["type"] == DISK:
                if "size_in_byte" not in expected_disk:
                    disk_size = expected_disk.get("new_size_in_byte", 0)
                else:
                    disk_size = expected_disk.get("size_in_byte", 0)
                storage_policy_name = None
                if expected_disk.get("storage_policy_uuid"):
                    storage_policy_name = storage_policy.StoragePolicy.get_storage_policy_name(
                        expected_disk["storage_policy_uuid"]
                    )
                volume_arg = {
                    "boot": index + 1,
                    "size": disk_size >> 30,
                    "bus": expected_disk["bus"],
                    "storage_policy": storage_policy_name,
                    "vol_name": expected_disk["name"],
                }
                attach_detail = EventMessage.update_detail(
                    details=attach_detail, event_detail_tmp=i18n.event_detail.get("ATTACH_VOLUME"), **volume_arg
                )
                if expected_disk.get("quota_policy"):
                    iops_policy = expected_disk["quota_policy"].get("max_iops_policy")
                    if iops_policy:
                        iops = {"iops": expected_disk["quota_policy"]["max_iops"]}
                        if iops_policy == "dynamic":
                            attach_detail = EventMessage.update_detail(
                                attach_detail, i18n.event_detail.get("IO_QUOTA_VM_IOPS_DYNAMIC"), **iops
                            )
                        elif iops_policy == "hard":
                            attach_detail = EventMessage.update_detail(
                                attach_detail, i18n.event_detail.get("IO_QUOTA_VM_IOPS_HARD"), **iops
                            )
                    bandwidth_policy = expected_disk["quota_policy"].get("max_bandwidth_policy")
                    if bandwidth_policy:
                        bandwidth = {"bandwidth": expected_disk["quota_policy"]["max_bandwidth"] // 1000000}
                        if bandwidth_policy == "dynamic":
                            attach_detail = EventMessage.update_detail(
                                attach_detail, i18n.event_detail.get("IO_QUOTA_VM_BANDWIDTH_DYNAMIC"), **bandwidth
                            )
                        elif bandwidth_policy == "hard":
                            attach_detail = EventMessage.update_detail(
                                attach_detail, i18n.event_detail.get("IO_QUOTA_VM_BANDWIDTH_HARD"), **bandwidth
                            )
            else:
                if expected_disk["path"] not in current_all_disk_paths and expected_disk["type"] == DISK:
                    storage_policy_name = None
                    vol = path_to_vol[expected_disk["path"]]
                    if expected_disk.get("storage_policy_uuid"):
                        storage_policy_name = storage_policy.StoragePolicy.get_storage_policy_name(
                            expected_disk["storage_policy_uuid"]
                        )
                    volume_arg = {
                        "boot": index + 1,
                        "size": expected_disk.get("size_in_byte", 0) >> 30,
                        "bus": expected_disk["bus"],
                        "storage_policy": storage_policy_name,
                        "vol_name": vol.name,
                    }
                    if vol.sharing:
                        attach_detail = EventMessage.update_detail(
                            details=attach_detail,
                            event_detail_tmp=i18n.event_detail.get("ATTACH_VOLUME_SHARE"),
                            **volume_arg,
                        )
                    else:
                        attach_detail = EventMessage.update_detail(
                            details=attach_detail, event_detail_tmp=i18n.event_detail.get("ATTACH_VOLUME"), **volume_arg
                        )
                    if expected_disk.get("quota_policy"):
                        iops_policy = expected_disk["quota_policy"].get("max_iops_policy")
                        if iops_policy:
                            iops = {"iops": expected_disk["quota_policy"]["max_iops"]}
                            if iops_policy == "dynamic":
                                attach_detail = EventMessage.update_detail(
                                    attach_detail, i18n.event_detail.get("IO_QUOTA_VM_IOPS_DYNAMIC"), **iops
                                )
                            elif iops_policy == "hard":
                                attach_detail = EventMessage.update_detail(
                                    attach_detail, i18n.event_detail.get("IO_QUOTA_VM_IOPS_HARD"), **iops
                                )
                        bandwidth_policy = expected_disk["quota_policy"].get("max_bandwidth_policy")
                        if bandwidth_policy:
                            bandwidth = {"bandwidth": expected_disk["quota_policy"]["max_bandwidth"] // 1000000}
                            if bandwidth_policy == "dynamic":
                                attach_detail = EventMessage.update_detail(
                                    attach_detail, i18n.event_detail.get("IO_QUOTA_VM_BANDWIDTH_DYNAMIC"), **bandwidth
                                )
                            elif bandwidth_policy == "hard":
                                attach_detail = EventMessage.update_detail(
                                    attach_detail, i18n.event_detail.get("IO_QUOTA_VM_BANDWIDTH_HARD"), **bandwidth
                                )
                if expected_disk["type"] == CDROM:
                    # new empty cdrom
                    if expected_disk["path"] == "":
                        if index + 1 not in current_cdrom_boot_orders:
                            cdrom_arg = {"boot": index + 1, "iso_name": "-"}
                            iso_null_detail = EventMessage.update_detail(
                                details=iso_null_detail,
                                event_detail_tmp=i18n.event_detail.get("ATTACH_CDROM"),
                                **cdrom_arg,
                            )
                    # new cdrom with iso
                    else:
                        if expected_disk["path"] not in current_all_disk_paths:
                            iso_name = self.get_iso_name(expected_disk["path"])
                            cdrom_arg = {"boot": index + 1, "iso_name": iso_name.get("name") if iso_name else "-"}
                            attach_detail = EventMessage.update_detail(
                                details=attach_detail,
                                event_detail_tmp=i18n.event_detail.get("ATTACH_CDROM"),
                                **cdrom_arg,
                            )
                for current_disk in current_disks:
                    if (
                        expected_disk["type"] == CDROM
                        and expected_disk["path"] != ""
                        and expected_disk["path"] == current_disk["path"]
                        and index + 1 != current_disk["boot"]
                    ):
                        cdrom_arg = {"old_boot": current_disk["boot"], "new_boot": index + 1}
                        modify_detail = EventMessage.update_detail(
                            details=modify_detail,
                            event_detail_tmp=i18n.event_detail.get("MODIFY_CDROM_BOOT"),
                            **cdrom_arg,
                        )
                    if (
                        expected_disk["type"] == CDROM
                        and expected_disk["path"] != ""
                        and expected_disk["path"] == current_disk["path"]
                        and index + 1 == current_disk["boot"]
                        and expected_disk.get("disabled", False) != current_disk.get("disabled", False)
                    ):
                        cdrom_arg = {"old_boot": current_disk["boot"]}
                        modify_detail = EventMessage.update_detail(
                            details=modify_detail,
                            event_detail_tmp=i18n.event_detail.get(
                                "MODIFY_CDROM_DISABLED"
                                if expected_disk.get("disabled", False)
                                else "MODIFY_CDROM_AVAILABLE"
                            ),
                            **cdrom_arg,
                        )
                    if (
                        expected_disk["path"] == current_disk["path"]
                        and expected_disk["type"] == DISK
                        and (
                            index + 1 != current_disk["boot"]
                            or expected_disk["bus"] != current_disk["bus"]
                            or path_to_vol[expected_disk["path"]].size_in_byte != expected_disk.get("size_in_byte")
                            or expected_disk.get("quota_policy") != current_disk.get("quota_policy")
                            or "storage_policy_uuid" in expected_disk
                        )
                    ):
                        vol = path_to_vol[expected_disk["path"]]
                        old_boot = {"old_boot": current_disk["boot"], "vol_name": vol.name}
                        modify_detail = EventMessage.update_detail(
                            details=modify_detail, event_detail_tmp=i18n.event_detail.get("MODIFY_VOLUME"), **old_boot
                        )
                        if index + 1 != current_disk["boot"]:
                            boot_args = {"old_boot": current_disk["boot"], "new_boot": index + 1}
                            modify_detail = EventMessage.update_detail(
                                details=modify_detail,
                                event_detail_tmp=i18n.event_detail.get("MODIFY_BOOT"),
                                **boot_args,
                            )
                        if expected_disk["bus"] != current_disk["bus"]:
                            bus_args = {"old_bus": current_disk["bus"], "new_bus": expected_disk["bus"]}
                            modify_detail = EventMessage.update_detail(
                                details=modify_detail,
                                event_detail_tmp=i18n.event_detail.get("MODIFY_VOLUME_BUS"),
                                **bus_args,
                            )
                        if "size_in_byte" in expected_disk and vol.size_in_byte != expected_disk["size_in_byte"]:
                            size_args = {
                                "old_size": vol.size_in_byte >> 30,
                                "new_size": expected_disk["size_in_byte"] >> 30,
                            }
                            modify_detail = EventMessage.update_detail(
                                details=modify_detail,
                                event_detail_tmp=i18n.event_detail.get("MODIFY_VOLUME_SIZE"),
                                **size_args,
                            )
                        if (
                            "storage_policy_uuid" in expected_disk
                            and vol.storage_policy_uuid != expected_disk["storage_policy_uuid"]
                        ):
                            _args = {
                                "old_name": storage_policy.StoragePolicy.get_storage_policy_name(
                                    vol.storage_policy_uuid
                                ),
                                "new_name": storage_policy.StoragePolicy.get_storage_policy_name(
                                    expected_disk["storage_policy_uuid"]
                                ),
                            }
                            modify_detail = EventMessage.update_detail(
                                details=modify_detail,
                                event_detail_tmp=i18n.event_detail.get("MODIFY_VOLUME_STORAGE_POLICY"),
                                **_args,
                            )
                        if current_disk.get("quota_policy") != expected_disk.get("quota_policy"):
                            if (
                                not current_disk.get("quota_policy")
                                or not expected_disk.get("quota_policy")
                                or current_disk["quota_policy"].get("max_iops_policy")
                                != expected_disk["quota_policy"].get("max_iops_policy")
                                or current_disk["quota_policy"].get("max_iops")
                                != expected_disk["quota_policy"].get("max_iops")
                            ):
                                if not current_disk.get("quota_policy") or not current_disk["quota_policy"].get(
                                    "max_iops_policy"
                                ):
                                    old_iops_cn = i18n.event_detail.get("NO_QUOTA")["zh_CN"]
                                    old_iops_us = i18n.event_detail.get("NO_QUOTA")["en_US"]
                                else:
                                    iops_policy = current_disk["quota_policy"].get("max_iops_policy")
                                    iops = {"iops": current_disk["quota_policy"]["max_iops"]}
                                    if iops_policy == "dynamic":
                                        old_iops = EventMessage.update_detail(
                                            i18n.event_detail.get("IOPS_QUOTA_DYNAMIC"), None, **iops
                                        )
                                    elif iops_policy == "hard":
                                        old_iops = EventMessage.update_detail(
                                            i18n.event_detail.get("IOPS_QUOTA_HARD"), None, **iops
                                        )
                                    old_iops_cn = old_iops["zh_CN"]
                                    old_iops_us = old_iops["en_US"]
                                if not expected_disk.get("quota_policy") or not expected_disk["quota_policy"].get(
                                    "max_iops_policy"
                                ):
                                    new_iops_cn = i18n.event_detail.get("NO_QUOTA")["zh_CN"]
                                    new_iops_us = i18n.event_detail.get("NO_QUOTA")["en_US"]
                                else:
                                    iops_policy = expected_disk["quota_policy"].get("max_iops_policy")

                                    iops = {"iops": expected_disk["quota_policy"]["max_iops"]}
                                    if iops_policy == "dynamic":
                                        new_iops = EventMessage.update_detail(
                                            i18n.event_detail.get("IOPS_QUOTA_DYNAMIC"), None, **iops
                                        )
                                    elif iops_policy == "hard":
                                        new_iops = EventMessage.update_detail(
                                            i18n.event_detail.get("IOPS_QUOTA_HARD"), None, **iops
                                        )
                                    new_iops_cn = new_iops["zh_CN"]
                                    new_iops_us = new_iops["en_US"]
                                if old_iops_cn != new_iops_cn:
                                    iops_args = {
                                        "old_iops_cn": old_iops_cn,
                                        "old_iops_us": old_iops_us,
                                        "new_iops_cn": new_iops_cn,
                                        "new_iops_us": new_iops_us,
                                    }
                                    modify_detail = EventMessage.update_detail(
                                        details=modify_detail,
                                        event_detail_tmp=i18n.event_detail.get("IO_QUOTA_VOLUME_IOPS_CHANGE"),
                                        **iops_args,
                                    )
                            if (
                                not current_disk.get("quota_policy")
                                or not expected_disk.get("quota_policy")
                                or current_disk["quota_policy"].get("max_bandwidth_policy")
                                != expected_disk["quota_policy"].get("max_bandwidth_policy")
                                or current_disk["quota_policy"].get("max_bandwidth")
                                != expected_disk["quota_policy"].get("max_bandwidth")
                            ):
                                if not current_disk.get("quota_policy") or not current_disk["quota_policy"].get(
                                    "max_bandwidth_policy"
                                ):
                                    old_bandwidth_cn = i18n.event_detail.get("NO_QUOTA")["zh_CN"]
                                    old_bandwidth_us = i18n.event_detail.get("NO_QUOTA")["en_US"]
                                else:
                                    bandwidth_policy = current_disk["quota_policy"].get("max_bandwidth_policy")
                                    bandwidth = {"bandwidth": current_disk["quota_policy"]["max_bandwidth"] // 1000000}
                                    if bandwidth_policy == "dynamic":
                                        old_bandwidth = EventMessage.update_detail(
                                            i18n.event_detail.get("BANDWIDTH_QUOTA_DYNAMIC"), None, **bandwidth
                                        )
                                    elif bandwidth_policy == "hard":
                                        old_bandwidth = EventMessage.update_detail(
                                            i18n.event_detail.get("BANDWIDTH_QUOTA_HARD"), None, **bandwidth
                                        )
                                    old_bandwidth_cn = old_bandwidth["zh_CN"]
                                    old_bandwidth_us = old_bandwidth["en_US"]
                                if not expected_disk.get("quota_policy") or not expected_disk["quota_policy"].get(
                                    "max_bandwidth_policy"
                                ):
                                    new_bandwidth_cn = i18n.event_detail.get("NO_QUOTA")["zh_CN"]
                                    new_bandwidth_us = i18n.event_detail.get("NO_QUOTA")["en_US"]
                                else:
                                    bandwidth_policy = expected_disk["quota_policy"].get("max_bandwidth_policy")

                                    bandwidth = {"bandwidth": expected_disk["quota_policy"]["max_bandwidth"] // 1000000}
                                    if bandwidth_policy == "dynamic":
                                        new_bandwidth = EventMessage.update_detail(
                                            i18n.event_detail.get("BANDWIDTH_QUOTA_DYNAMIC"), None, **bandwidth
                                        )
                                    elif bandwidth_policy == "hard":
                                        new_bandwidth = EventMessage.update_detail(
                                            i18n.event_detail.get("BANDWIDTH_QUOTA_HARD"), None, **bandwidth
                                        )
                                    new_bandwidth_cn = new_bandwidth["zh_CN"]
                                    new_bandwidth_us = new_bandwidth["en_US"]
                                if new_bandwidth_cn != old_bandwidth_cn:
                                    bandwidth_args = {
                                        "old_bandwidth_cn": old_bandwidth_cn,
                                        "old_bandwidth_us": old_bandwidth_us,
                                        "new_bandwidth_cn": new_bandwidth_cn,
                                        "new_bandwidth_us": new_bandwidth_us,
                                    }
                                    modify_detail = EventMessage.update_detail(
                                        details=modify_detail,
                                        event_detail_tmp=i18n.event_detail.get("IO_QUOTA_VOLUME_BANDWIDTH_CHANGE"),
                                        **bandwidth_args,
                                    )

        if attach_detail is not None:
            attach_detail = EventMessage.update_detail(
                details=i18n.event_detail.get("ATTACH"), event_detail_tmp=attach_detail
            )
        if modify_detail is not None:
            modify_detail = EventMessage.update_detail(
                details=i18n.event_detail.get("MODIFY"), event_detail_tmp=modify_detail
            )
        if quota_policy_detail is not None:
            quota_policy_detail = EventMessage.update_detail(
                details=i18n.event_detail.get("IO_QUOTA_VM"), event_detail_tmp=quota_policy_detail
            )
        detail = EventMessage.update_detail(
            details=i18n.event_detail.get("EDIT_DISKS_INIT"), event_detail_tmp=quota_policy_detail
        )
        detail = EventMessage.update_detail(details=detail, event_detail_tmp=attach_detail)
        detail = EventMessage.update_detail(details=detail, event_detail_tmp=modify_detail)
        detail = EventMessage.update_detail(details=detail, event_detail_tmp=iso_null_detail)
        vols_current = VolumeWrapper.get_mountable_by_paths(
            paths=[disk["path"] for disk in current_disks if disk["type"] == DISK and disk.get("path") is not None],
            vm_uuid=vm_doc["uuid"],
        )
        path_to_vol_current = {vol.path: vol for vol in vols_current}
        for current_disk in current_disks:
            if current_disk["path"] not in [
                expected_disk["path"] for expected_disk in expected_disks if expected_disk.get("path") is not None
            ]:
                if current_disk["type"] == "cdrom":
                    iso_name = self.get_iso_name(current_disk["path"])
                    detach_args = {"boot": current_disk["boot"], "iso_name": iso_name.get("name") if iso_name else "-"}
                    if current_disk["path"] in del_paths:
                        delete_detail = EventMessage.update_detail(
                            details=delete_detail, event_detail_tmp=i18n.event_detail.get("DETACH_CDROM"), **detach_args
                        )
                    else:
                        detach_detail = EventMessage.update_detail(
                            details=detach_detail, event_detail_tmp=i18n.event_detail.get("DETACH_CDROM"), **detach_args
                        )
                else:
                    vol = path_to_vol_current[current_disk["path"]]
                    detach_args = {"boot": current_disk["boot"], "vol_name": vol.name}
                    if current_disk["path"] in del_paths:
                        delete_detail = EventMessage.update_detail(
                            details=delete_detail,
                            event_detail_tmp=i18n.event_detail.get("DETACH_VOLUME"),
                            **detach_args,
                        )
                    else:
                        detach_detail = EventMessage.update_detail(
                            details=detach_detail,
                            event_detail_tmp=i18n.event_detail.get("DETACH_VOLUME"),
                            **detach_args,
                        )
        if detach_detail is not None:
            detach_detail = EventMessage.update_detail(
                details=i18n.event_detail.get("DETACH"), event_detail_tmp=detach_detail
            )
        detail = EventMessage.update_detail(details=detail, event_detail_tmp=detach_detail)
        if delete_detail is not None:
            delete_detail = EventMessage.update_detail(
                details=i18n.event_detail.get("DELETED"), event_detail_tmp=delete_detail
            )
        detail = EventMessage.update_detail(details=detail, event_detail_tmp=delete_detail)
        return VMEventWrapper(
            user=self.user, vm_uuid=vm_doc["uuid"], vm_name=vm_doc["vm_name"], detail=detail
        )._event_vm_edit_disks()

    def get_vm_detail(self, data, disks, detail, vm_uuid=None, folder_uuid=None):
        if data.get("ha"):
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("HA_ON"))
        else:
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("HA_OFF"))
        if folder_uuid:
            folder = Folder.load(folder_uuid).name
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("FOLDER_SET"), **{"folder": folder})
        elif folder_uuid == "":
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("FOLDER_NOT_SET"))
        vcpu_num = {"vcpu_num": data.get("vcpu")}
        detail = EventMessage.update_detail(detail, i18n.event_detail.get("VCPU_NUM"), **vcpu_num)
        if data.get("cpu"):
            slot_num = {"slot_num": data["cpu"]["topology"]["sockets"]}
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("CPU_SLOT_NUM"), **slot_num)
        memory = {"mem": float(data.get("memory", 0)) / (1024 * 1024 * 1024)}
        detail = EventMessage.update_detail(detail, i18n.event_detail.get("MEMORY"), **memory)

        if data.get("quota_policy"):
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("IO_QUOTA_VM"))
            iops_policy = data["quota_policy"].get("max_iops_policy")
            if iops_policy:
                iops = {"iops": data["quota_policy"]["max_iops"]}
                if iops_policy == "dynamic":
                    detail = EventMessage.update_detail(
                        detail, i18n.event_detail.get("IO_QUOTA_VM_IOPS_DYNAMIC"), **iops
                    )
                elif iops_policy == "hard":
                    detail = EventMessage.update_detail(detail, i18n.event_detail.get("IO_QUOTA_VM_IOPS_HARD"), **iops)
            bandwidth_policy = data["quota_policy"].get("max_bandwidth_policy")
            if bandwidth_policy:
                bandwidth = {"bandwidth": data["quota_policy"]["max_bandwidth"] // 1000000}
                if bandwidth_policy == "dynamic":
                    detail = EventMessage.update_detail(
                        detail, i18n.event_detail.get("IO_QUOTA_VM_BANDWIDTH_DYNAMIC"), **bandwidth
                    )
                elif bandwidth_policy == "hard":
                    detail = EventMessage.update_detail(
                        detail, i18n.event_detail.get("IO_QUOTA_VM_BANDWIDTH_HARD"), **bandwidth
                    )
        else:
            if len([disk for disk in disks if disk.get("quota_policy")]) == 0:
                detail = EventMessage.update_detail(detail, i18n.event_detail.get("IO_QUOTA_NO"))
            else:
                detail = EventMessage.update_detail(detail, i18n.event_detail.get("IO_QUOTA_VOLUME"))

        vols_exist = Volume.load_by_paths(
            paths=[disk["path"] for disk in disks if disk["type"] == DISK and disk.get("path") is not None],
        )
        path_to_vol = {vol.path: vol for vol in vols_exist}
        for index, disk in enumerate(disks):
            if disk["type"] == "cdrom":
                iso_name = "-"
                if self.get_iso_name(disk["path"]):
                    iso_name = self.get_iso_name(disk["path"]).get("name", "-")
                cdrom_arg = {"boot": index + 1, "iso_name": iso_name}
                detail = EventMessage.update_detail(detail, i18n.event_detail.get("ATTACH_CDROM"), **cdrom_arg)
            else:
                event_op = "ATTACH_VOLUME"
                if disk.get("volume_template_uuid"):
                    vol = VolumeTemplate.load(disk["volume_template_uuid"])
                    disk_size = int(vol.size)
                    disk_name = disk.get("name") if disk.get("name") else vol.name
                elif disk.get("path"):
                    vol = path_to_vol[disk["path"]]
                    disk_size = int(vol.size_in_byte)
                    disk_name = disk.get("name") if disk.get("name") else vol.name
                    if vol.sharing:
                        event_op = "ATTACH_VOLUME_SHARE"
                else:
                    disk_name = disk["name"]
                    if "size_in_byte" not in disk:
                        disk_size = disk.get("new_size_in_byte", 0)
                    else:
                        disk_size = disk.get("size_in_byte", 0)
                storage_policy_name = None
                if disk.get("storage_policy_uuid"):
                    storage_policy_name = storage_policy.StoragePolicy.get_storage_policy_name(
                        disk["storage_policy_uuid"]
                    )
                volume_arg = {
                    "boot": index + 1,
                    "size": disk_size >> 30,
                    "bus": disk["bus"],
                    "storage_policy": storage_policy_name,
                    "vol_name": disk_name,
                }
                detail = EventMessage.update_detail(detail, i18n.event_detail.get(event_op), **volume_arg)
                if disk.get("quota_policy"):
                    iops_policy = disk["quota_policy"].get("max_iops_policy")
                    if iops_policy:
                        iops = {"iops": disk["quota_policy"]["max_iops"]}
                        if iops_policy == "dynamic":
                            detail = EventMessage.update_detail(
                                detail, i18n.event_detail.get("IO_QUOTA_VM_IOPS_DYNAMIC"), **iops
                            )
                        elif iops_policy == "hard":
                            detail = EventMessage.update_detail(
                                detail, i18n.event_detail.get("IO_QUOTA_VM_IOPS_HARD"), **iops
                            )
                    bandwidth_policy = disk["quota_policy"].get("max_bandwidth_policy")
                    if bandwidth_policy:
                        bandwidth = {"bandwidth": disk["quota_policy"]["max_bandwidth"] // 1000000}
                        if bandwidth_policy == "dynamic":
                            detail = EventMessage.update_detail(
                                detail, i18n.event_detail.get("IO_QUOTA_VM_BANDWIDTH_DYNAMIC"), **bandwidth
                            )
                        elif bandwidth_policy == "hard":
                            detail = EventMessage.update_detail(
                                detail, i18n.event_detail.get("IO_QUOTA_VM_BANDWIDTH_HARD"), **bandwidth
                            )
        for index, nic in enumerate(data["nics"]):
            vlan_name = None
            if get_vlan(nic["vlan_uuid"]):
                vlan_name = get_vlan(nic["vlan_uuid"]).get("name")
            nic_arg = {
                "num": index,
                "vlan_name": vlan_name,
                "model": nic.get("model", VM_INTERFACE_VIRTIO),
            }
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("NIC"), **nic_arg)
            detail = EventMessage.update_detail(
                detail, i18n.event_detail.get("NIC_UP" if nic.get("link") == VM_INTERFACE_UP else "NIC_DOWN"), **nic_arg
            )
            if nic.get("mirror"):
                detail = EventMessage.update_detail(detail, i18n.event_detail.get("NIC_MIRROR"))

        firmware = {"firmware": data.get("firmware")}
        detail = EventMessage.update_detail(detail, i18n.event_detail.get("FIRMWARE"), **firmware)

        if data.get("cpu_exclusive", {}).get("expected_enabled", False):
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("CPU_EXCLUSIVE_ON"))
        else:
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("CPU_EXCLUSIVE_OFF"))
        return detail

    @event_enclosure
    def event_vm_create(self, vm_doc, data):
        detail = i18n.event_detail.get("CREATE_VM_INIT")
        detail = self.get_vm_detail(vm_doc, data["disks"], detail, folder_uuid=data.get("folder_uuid") or "")
        return VMEventWrapper(
            user=self.user, vm_uuid=vm_doc["uuid"], vm_name=vm_doc["vm_name"], detail=detail
        )._event_vm_create()

    def generate_rebuild_vm_detail(self, vm_snapshot, data, existing_iso, detail):
        disks = []
        update_disks = {d["snapshot_uuid"]: d for d in data.get("modify_disks", []) if d["type"] == DISK}
        for disk in copy.deepcopy(vm_snapshot["disks"]):
            if disk["type"] == DISK:
                volume = Snapshot.load(disk["snapshot_uuid"])
                disk.update({"size_in_byte": volume.volume_size, "name": volume.volume_name, "sharing": volume.sharing})
                update_disk = update_disks.get(disk["snapshot_uuid"])
                if update_disk:
                    if "storage_policy_uuid" in update_disk:
                        disk["storage_policy_uuid"] = update_disk["storage_policy_uuid"]
                    disk["name"] = update_disk["name"]
                disks.append(disk)
            elif disk["type"] == "cdrom":
                if disk["path"] and disk["path"] not in existing_iso:
                    disk["path"] = ""
                disks.append(disk)
        new_disks = data.get("new_disks", data.get("disks", []))
        if new_disks:
            exist_vol_paths = []
            for index, vol in enumerate(new_disks):
                if "deleted" in vol:
                    disks[index]["name"] = vol["name"]
                else:
                    if vol["type"] == DISK and not vol.get("path"):
                        disks.append(vol)
                    else:
                        if vol["type"] == DISK:
                            exist_vol_paths.append(vol["path"])
                        disks.append(vol)
            vols_current = VolumeWrapper.get_mountable_by_paths(paths=exist_vol_paths, vm_uuid=None)

            path_to_vol_current = {vol.path: vol for vol in vols_current}
            for x in [x for x in disks if x.get("path")]:
                if x["path"] in path_to_vol_current:
                    x.update(
                        {
                            "size_in_byte": (path_to_vol_current[x["path"]].__dict__.get("size_in_byte", 0)),
                            "name": (path_to_vol_current[x["path"]].__dict__.get("name")),
                            "sharing": path_to_vol_current[x["path"]].sharing,
                        }
                    )
        for index, disk in enumerate(disks):
            if disk["type"] == DISK:
                storage_policy_name = None
                if disk.get("storage_policy_uuid"):
                    storage_policy_name = storage_policy.StoragePolicy.get_storage_policy_name(
                        disk["storage_policy_uuid"]
                    )
                volume_arg = {
                    "boot": index + 1,
                    "size": disk.get("size_in_byte", 0) >> 30,
                    "bus": disk.get("bus"),
                    "storage_policy": storage_policy_name,
                    "vol_name": disk.get("name"),
                }
                if disk.get("sharing"):
                    detail = EventMessage.update_detail(
                        detail, i18n.event_detail.get("ATTACH_VOLUME_SHARE"), **volume_arg
                    )
                else:
                    detail = EventMessage.update_detail(detail, i18n.event_detail.get("ATTACH_VOLUME"), **volume_arg)
            elif disk.get("type") == "cdrom":
                if disk.get("path"):
                    iso_name = self.get_iso_name(disk["path"]).get("name", "-")
                else:
                    iso_name = None
                cdrom_arg = {"boot": index + 1, "iso_name": iso_name}
                detail = EventMessage.update_detail(detail, i18n.event_detail.get("ATTACH_CDROM"), **cdrom_arg)
        if data.get("nics"):
            for index, nic in enumerate(data.get("nics")):
                vlan_name = None
                if get_vlan(nic["vlan_uuid"]):
                    vlan_name = get_vlan(nic["vlan_uuid"]).get("name")
                nic_arg = {"num": index, "vlan_name": vlan_name, "model": nic.get("model", VM_INTERFACE_VIRTIO)}
                detail = EventMessage.update_detail(detail, i18n.event_detail.get("NIC"), **nic_arg)
        return detail

    @event_enclosure
    def event_vm_rebuild(self, vm, vm_snapshot, data, existing_iso):
        detail = i18n.event_detail.get("CREATE_VM_INIT")
        if vm["ha"]:
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("HA_ON"))
        else:
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("HA_OFF"))
        folder_uuid = data.get("folder_uuid")
        if folder_uuid:
            folder = Folder.load(folder_uuid).name
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("FOLDER_SET"), **{"folder": folder})
        else:
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("FOLDER_NOT_SET"))
        vcpu_num = {"vcpu_num": vm["vcpu"]}
        detail = EventMessage.update_detail(detail, i18n.event_detail.get("VCPU_NUM"), **vcpu_num)
        if vm.get("cpu"):
            slot_num = {"slot_num": vm["cpu"]["topology"]["sockets"]}
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("CPU_SLOT_NUM"), **slot_num)
        memory = {"mem": float(vm.get("memory", 0)) / (1024 * 1024 * 1024)}
        detail = EventMessage.update_detail(detail, i18n.event_detail.get("MEMORY"), **memory)
        detail = self.generate_rebuild_vm_detail(vm_snapshot, data, existing_iso, detail)
        if data.get("is_full_copy", False):
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("CREATE_MODE_FULL_COPY"))
        else:
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("CREATE_MODE_NORMAL"))

        if data.get("cpu_exclusive", {}).get("expected_enabled", False):
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("CPU_EXCLUSIVE_ON"))
        else:
            detail = EventMessage.update_detail(detail, i18n.event_detail.get("CPU_EXCLUSIVE_OFF"))

        pci_devs = [
            d
            for d in data.get("hostdevs", [])
            if d["type"] in (PASS_THROUGH_GPU, PASS_THROUGH_NIC, PASS_THROUGH_PCI, PASS_THROUGH_VF_PCI)
        ]
        if pci_devs:
            pci_dev_info_cache = pci_device_cache.PCIDeviceInfoCache()
            detail = self.update_pci_devices_event_message_detail(pci_devs, vm["node_ip"], pci_dev_info_cache, detail)

        return VMEventWrapper(
            user=self.user, vm_uuid=vm["uuid"], vm_name=vm["vm_name"], detail=detail, vm_snapshot_name=vm["name"]
        )._event_vm_rebuild()

    @event_enclosure
    def event_vm_batch_delete(self, vms, batch_resources):
        failed = EventMessage.update_detail(i18n.event_detail.get("BATCH_DELETE_VM_FAILED"))
        return self.event_batch_vm_splicing(
            vms=vms, batch_resources=batch_resources, event_name=EVENT_BATCH_DELETE_VM, failed=failed
        )

    @event_enclosure
    def event_vm_batch_start(self, vms):
        failed = EventMessage.update_detail(i18n.event_detail.get("BATCH_START_VM_FAILED"))
        return self.event_batch_vm_splicing(vms=vms, event_name=EVENT_BATCH_START_VM, failed=failed)

    @event_enclosure
    def event_vm_batch_pause(self, vms):
        failed = EventMessage.update_detail(i18n.event_detail.get("BATCH_SUSPEND_VM_FAILED"))
        return self.event_batch_vm_splicing(vms=vms, event_name=EVENT_BATCH_SUSPEND_VM, failed=failed)

    @event_enclosure
    def event_vm_batch_resume(self, vms):
        failed = EventMessage.update_detail(i18n.event_detail.get("BATCH_RESUME_VM_FAILED"))
        return self.event_batch_vm_splicing(vms=vms, event_name=EVENT_BATCH_RESUME_VM, failed=failed)

    @event_enclosure
    def event_vm_batch_migrate(self, resource_list, scheduled_ips):
        vm_names = []
        vm_ids = []
        resources = {}
        detail = None
        batch = {}
        failed = EventMessage.update_detail(i18n.event_detail.get("BATCH_MIGRATE_VM_FAILED"))
        for resource in resource_list:
            resources.update({resource["uuid"]: KVM_VM})
            vm_names.append(resource["vm_name"])
            vm_ids.append(resource["uuid"])
            migrate_arg = {
                "vm_name": resource["vm_name"],
                "old_host": resource["node_ip"],
                "new_host": scheduled_ips.get(resource["uuid"]),
            }
            done_detail = EventMessage.update_detail(i18n.event_detail.get("BATCH_MIGRATE_VM"), **migrate_arg)
            batch, detail = self.generate_batch_detail(
                failed=failed,
                uuid=resource["uuid"],
                batch=batch,
                name=resource["vm_name"],
                i18n=i18n.event_detail.get("BATCH_VM_NAME"),
                detail_vm=detail,
                done_detail=done_detail,
            )
        vm_data = {"vm_id": ",".join(vm_ids), "vm_name": ",".join(vm_names)}
        message_arg = {
            "vm_num": len(vm_names),
        }

        return build_event(
            event_name=EVENT_BATCH_MIGRATE_VM,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_data,
            resources=resources,
            detail=detail,
            batch=batch,
            event_mod=i18n.event_message.get(EVENT_BATCH_MIGRATE_VM),
            **message_arg,
        )

    @event_enclosure
    def event_vm_batch_stop(self, vms):
        failed = EventMessage.update_detail(i18n.event_detail.get("BATCH_SHUTDOWN_VM_FAILED"))
        return self.event_batch_vm_splicing(vms=vms, event_name=EVENT_BATCH_SHUTDOWN_VM, failed=failed)

    @event_enclosure
    def event_vm_batch_force_stop(self, vms):
        failed = EventMessage.update_detail(i18n.event_detail.get("BATCH_FORCE_SHUTDOWN_VM_FAILED"))
        return self.event_batch_vm_splicing(vms=vms, event_name=EVENT_BATCH_FORCE_SHUTDOWN_VM, failed=failed)

    @event_enclosure
    def event_vm_batch_reboot(self, vms):
        failed = EventMessage.update_detail(i18n.event_detail.get("BATCH_REBOOT_VM_FAILED"))
        return self.event_batch_vm_splicing(vms=vms, event_name=EVENT_BATCH_REBOOT_VM, failed=failed)

    @event_enclosure
    def event_vm_batch_force_reboot(self, vms):
        failed = EventMessage.update_detail(i18n.event_detail.get("BATCH_FORCE_REBOOT_VM_FAILED"))
        return self.event_batch_vm_splicing(vms=vms, event_name=EVENT_BATCH_FORCE_REBOOT_VM, failed=failed)

    def generate_batch_detail(
        self, failed=None, uuid=None, batch=None, name=None, i18n=None, detail_vm=None, done_detail=None
    ):
        name_arg = {"vm_name": name}
        vm_failed = None
        if failed:
            vm_failed = EventMessage.update_detail(failed, **name_arg)
        batch.update({uuid: {"done": done_detail, "failed": vm_failed}})
        vm_uuid = copy.deepcopy(uuid)
        vm_uuid = "u" + vm_uuid[:8] + vm_uuid[9:13] + vm_uuid[14:18] + vm_uuid[19:23] + vm_uuid[24:]
        arg = {"vm_name": "${{{}}}".format(vm_uuid)}
        vm_detail = EventMessage.update_detail(i18n, **arg)
        detail_vm = EventMessage.batch_detail_kwargs_splicing(detail_vm, vm_detail)
        return batch, detail_vm

    @event_enclosure
    def event_vm_change_folder(self, old_folder_name, new_folder_name):
        vm_data = {"vm_id": self.vm_uuid, "vm_name": self.vm_name}
        message_arg = {"vm_name": self.vm_name, "old_folder_name": old_folder_name, "new_folder_name": new_folder_name}
        resources = {self.vm_uuid: KVM_VM}
        detail = {"zh_CN": "None", "en_US": "None"}
        event_arg = build_event(
            event_name=EVENT_CHANGE_FOLDER,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(EVENT_CHANGE_FOLDER),
            **message_arg,
        )
        return generate_events(event_state="DONE", **event_arg)

    @event_enclosure
    def event_vm_set_folder(self, new_folder_name):
        vm_data = {"vm_id": self.vm_uuid, "vm_name": self.vm_name}
        message_arg = {"vm_name": self.vm_name, "new_folder_name": new_folder_name}
        resources = {self.vm_uuid: KVM_VM}
        detail = {"zh_CN": "None", "en_US": "None"}
        event_arg = build_event(
            event_name=EVENT_SET_FOLDER,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(EVENT_SET_FOLDER),
            **message_arg,
        )
        return generate_events(event_state="DONE", **event_arg)

    @event_enclosure
    def event_vm_move_out_folder(self, old_folder_name):
        vm_data = {"vm_id": self.vm_uuid, "vm_name": self.vm_name}
        message_arg = {"vm_name": self.vm_name, "old_folder_name": old_folder_name}
        resources = {self.vm_uuid: KVM_VM}
        detail = {"zh_CN": "None", "en_US": "None"}
        event_arg = build_event(
            event_name=EVENT_MOVE_OUT_FOLDER,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(EVENT_MOVE_OUT_FOLDER),
            **message_arg,
        )
        return generate_events(event_state="DONE", **event_arg)

    @event_enclosure
    def event_vm_bind_attribute(self, attribute_key, attribute_value):
        vm_data = {"vm_id": self.vm_uuid, "vm_name": self.vm_name}
        message_arg = {"vm_name": self.vm_name, "attribute_key": attribute_key}
        resources = {self.vm_uuid: KVM_VM}
        detail = EventMessage.update_detail(
            event_detail_tmp=i18n.event_detail.get("ATTRIBUTE"),
            **{"attribute_key": attribute_key, "attribute_values": attribute_value},
        )
        event_arg = build_event(
            event_name=EVENT_VM_BIND_ATTRIBUTE,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(EVENT_VM_BIND_ATTRIBUTE),
            **message_arg,
        )
        return generate_events(event_state="DONE", **event_arg)

    @event_enclosure
    def event_vm_unbind_attribute(self, attribute_key, attribute_values):
        vm_data = {"vm_id": self.vm_uuid, "vm_name": self.vm_name}
        message_arg = {"vm_name": self.vm_name, "attribute_key": attribute_key}
        resources = {self.vm_uuid: KVM_VM}
        detail = EventMessage.update_detail(
            event_detail_tmp=i18n.event_detail.get("ATTRIBUTE"),
            **{"attribute_key": attribute_key, "attribute_values": attribute_values.join(", ")},
        )
        event_arg = build_event(
            event_name=EVENT_VM_UNBIND_ATTRIBUTE,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(EVENT_VM_UNBIND_ATTRIBUTE),
            **message_arg,
        )
        return generate_events(event_state="DONE", **event_arg)

    @event_enclosure
    def event_vm_enable_cpu_exclusive(self):
        vm_data = {"vm_id": self.vm_uuid, "vm_name": self.vm_name}
        message_arg = {"vm_name": self.vm_name}
        resources = {self.vm_uuid: KVM_VM}
        return build_event(
            event_name=EVENT_ENABLE_CPU_EXCLUSIVE,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_data,
            resources=resources,
            detail=self.detail,
            user_type="SYSTEM",
            event_mod=i18n.event_message.get(EVENT_ENABLE_CPU_EXCLUSIVE),
            **message_arg,
        )

    @event_enclosure
    def event_vm_enable_cpu_qos_reservation(self):
        vm_data = {"vm_id": self.vm_uuid, "vm_name": self.vm_name}
        message_arg = {"vm_name": self.vm_name}
        resources = {self.vm_uuid: KVM_VM}
        return build_event(
            event_name=EVENT_ENABLE_CPU_QOS_RESERVATION,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_data,
            resources=resources,
            detail=self.detail,
            user_type="SYSTEM",
            event_mod=i18n.event_message.get(EVENT_ENABLE_CPU_QOS_RESERVATION),
            **message_arg,
        )
