# Copyright (c) 2013-2015, SMARTX
# All rights reserved.

from common.event.event_message import EventMessage, build_event, event_enclosure, generate_events
from smartx_app.elf.common.events.constants import (
    EVENT_CREATE_FOLDER,
    EVENT_DELETE_FOLDER,
    EVENT_EDIT_FOLDER,
)
import smartx_app.elf.common.events.message_i18n_elf as i18n


class FolderEventWrapper:
    def __init__(self, obj, user, event_state=None, data=None, detail=None):
        self.obj = obj
        self.user = user
        self.user_name = user.get("username", "-")
        self.user_role = user.get("Role")
        self.event_state = event_state or "DONE"
        self.data = data
        self.detail = detail

    @event_enclosure
    def event_folder_create(self):
        data = {"folder_name": self.obj.name, "folder_id": self.obj.uuid}
        message_arg = {"folder_name": self.obj.name}
        resources = {self.obj.uuid: None}
        detail = {"zh_CN": "None", "en_US": "None"}
        event_arg = build_event(
            event_name=EVENT_CREATE_FOLDER,
            user=self.user_name,
            user_role=self.user_role,
            data=data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(EVENT_CREATE_FOLDER),
            **message_arg,
        )
        return generate_events(event_state=self.event_state, **event_arg)

    @event_enclosure
    def event_folder_update(self, old_folder_name):
        data = {"folder_name": self.obj.name, "folder_id": self.obj.uuid}
        message_arg = {"folder_name": old_folder_name}
        resources = {self.obj.uuid: None}
        detail = EventMessage.update_detail(
            event_detail_tmp=i18n.event_detail.get(EVENT_EDIT_FOLDER),
            **{"old_folder": old_folder_name, "new_folder": self.obj.name},
        )
        event_arg = build_event(
            event_name=EVENT_EDIT_FOLDER,
            user=self.user_name,
            user_role=self.user_role,
            data=data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(EVENT_EDIT_FOLDER),
            **message_arg,
        )
        return generate_events(event_state=self.event_state, **event_arg)

    @event_enclosure
    def event_folder_delete(self):
        data = {"folder_name": self.obj.name, "folder_id": self.obj.uuid}
        message_arg = {"folder_name": self.obj.name}
        resources = {self.obj.uuid: None}
        detail = {"zh_CN": "None", "en_US": "None"}
        event_arg = build_event(
            event_name=EVENT_DELETE_FOLDER,
            user=self.user_name,
            user_role=self.user_role,
            data=data,
            resources=resources,
            detail=detail,
            event_mod=i18n.event_message.get(EVENT_DELETE_FOLDER),
            **message_arg,
        )
        return generate_events(event_state=self.event_state, **event_arg)
