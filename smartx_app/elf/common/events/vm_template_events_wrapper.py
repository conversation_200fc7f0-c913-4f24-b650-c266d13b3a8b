# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
import copy

from common.event.event_message import EventMessage, build_event, event_enclosure
from smartx_app.common.constants import CDROM
from smartx_app.elf.common import constants as elf_constants
from smartx_app.elf.common.events import message_i18n_elf as i18n
from smartx_app.elf.common.events.constants import (
    EVENT_AS_VM,
    EVENT_BATCH_CREATE_VM_FROM_TEMPLATE,
    EVENT_CREATE_VM_FROM_TEMPLATE,
    EVENT_CREATE_VM_TEMPLATE,
)
from smartx_app.elf.common.events.vm_events_wrapper import VMEventWrapper
from smartx_app.elf.common.utils import pci_device_cache
from smartx_app.elf.job_center.constants import DISK, KVM_VM, KVM_VM_TEMPLATE


class VMTemplateEventWrapper:
    def __init__(self, user=None, uuid=None, name=None, detail=None, vm_name=None, vm_uuid=None):
        self.user = user
        self.user_name = user.get("username", "-") if user else None
        self.user_role = user.get("Role") if user else None
        self.uuid = uuid
        self.name = name
        self.detail = detail
        self.vm_name = vm_name
        self.vm_uuid = vm_uuid

    def vm_template_event_splicing(self, event_name):
        vm_template_data = {"vm_template_id": self.uuid, "vm_template_name": self.name}
        message_arg = {"vm_template_name": self.name}
        resources = {self.uuid: KVM_VM_TEMPLATE}
        return build_event(
            event_name=event_name,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_template_data,
            resources=resources,
            detail=self.detail,
            event_mod=i18n.event_message.get(event_name),
            **message_arg,
        )

    def _event_vm_template_create_vm(self):
        vm_template_data = {"vm_template_name": self.name, "vm_template_id": self.uuid}
        message_arg = {"vm_template_name": self.name, "vm_name": self.vm_name}
        resources = {self.vm_uuid: KVM_VM}
        return build_event(
            event_name=EVENT_CREATE_VM_FROM_TEMPLATE,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_template_data,
            resources=resources,
            detail=self.detail,
            event_mod=i18n.event_message.get(EVENT_CREATE_VM_FROM_TEMPLATE),
            **message_arg,
        )

    def _event_vm_template_create(self):
        return self.vm_template_event_splicing(EVENT_CREATE_VM_TEMPLATE)

    @event_enclosure
    def event_vm_template_as_vm(self, vm_name):
        vm_template_data = {"vm_template_name": self.name, "vm_template_id": self.uuid}
        message_arg = {"vm_template_name": self.name, "vm_name": vm_name}
        resources = {self.vm_uuid: KVM_VM}
        return build_event(
            event_name=EVENT_AS_VM,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_template_data,
            resources=resources,
            detail=self.detail,
            event_mod=i18n.event_message.get(EVENT_AS_VM),
            **message_arg,
        )

    @event_enclosure
    def event_vm_template_create(self, vm_template_obj, vm_uuid=None):
        detail = i18n.event_detail.get("CREATE_VM_TEMPLATE_INIT")
        vm_name = {"vm_template_name": vm_template_obj.name}
        detail = EventMessage.update_detail(detail, **vm_name)
        vm_template = vm_template_obj.__dict__
        detail = VMEventWrapper(user=self.user).get_vm_detail(vm_template, vm_template["disks"], detail, vm_uuid)
        return VMTemplateEventWrapper(
            user=self.user, uuid=vm_template.get("uuid"), name=vm_template.get("name"), detail=detail
        )._event_vm_template_create()

    @event_enclosure
    def event_vm_template_create_vm(self, vm_template_obj, data_map, resources, folder_uuid):
        vms = {
            resource["uuid"]: resource["vm_name"] for resource in list(resources.values()) if resource["type"] == KVM_VM
        }

        if not vms:
            return None

        vm_template = vm_template_obj.__dict__
        vm_uuid = None

        if len(vms) > 1:
            detail_merged = {"zh_CN": "\n", "en_US": "\n"}
            seq = "\n"
        else:
            detail_merged = copy.deepcopy(i18n.event_detail.get("CREATE_VM_INIT"))
            seq = ""

        for uuid in resources:
            if resources[uuid]["type"] == KVM_VM:
                data = data_map[uuid]
                disks = copy.deepcopy(vm_template["disks"])
                vm_disks = []
                update_disks = {d["volume_template_uuid"]: d for d in data.get("modify_disks", []) if d["type"] == DISK}
                update_cdrom = {d["key"]: d["path"] for d in data.get("modify_disks", []) if d["type"] == CDROM}
                delete_disks = [d.get("path", d.get("key")) for d in data.get("delete_disks", [])]

                for d in disks:
                    if d.get("key", d.get("path")) in delete_disks:
                        continue
                    else:
                        if d["type"] == DISK:
                            update_disk = update_disks.get(d["volume_template_uuid"])
                            if update_disk:
                                d["bus"] = update_disk["bus"]
                                d["name"] = update_disk["name"]
                                if "storage_policy_uuid" in update_disk:
                                    d["storage_policy_uuid"] = update_disk["storage_policy_uuid"]
                        else:
                            path = update_cdrom.get(d["path"])
                            if path:
                                d["path"] = path
                        vm_disks.append(d)
                vm_disks.extend(data.get("new_disks", data.get("disks", [])))
                vm_name = resources[uuid]["vm_name"]
                detail = {"zh_CN": "虚拟机名:%s\n" % vm_name, "en_US": "virtual machines name:%s\n" % vm_name}
                detail = VMEventWrapper(user=self.user).get_vm_detail(
                    resources[uuid], vm_disks, detail, folder_uuid=folder_uuid or ""
                )
                if data.get("is_full_copy", False):
                    detail = EventMessage.update_detail(detail, i18n.event_detail.get("CREATE_MODE_FULL_COPY"))
                else:
                    detail = EventMessage.update_detail(detail, i18n.event_detail.get("CREATE_MODE_NORMAL"))

                pci_devs = [
                    d
                    for d in data.get("hostdevs", [])
                    if d["type"]
                    in (
                        elf_constants.PASS_THROUGH_GPU,
                        elf_constants.PASS_THROUGH_NIC,
                        elf_constants.PASS_THROUGH_PCI,
                        elf_constants.PASS_THROUGH_VF_PCI,
                    )
                ]
                if pci_devs:
                    pci_dev_info_cache = pci_device_cache.PCIDeviceInfoCache()
                    detail = VMEventWrapper.update_pci_devices_event_message_detail(
                        pci_devs, data["node_ip"], pci_dev_info_cache, detail
                    )

                detail_merged["zh_CN"] = "{}{}{}".format(detail_merged["zh_CN"], detail["zh_CN"], seq)
                detail_merged["en_US"] = "{}{}{}".format(detail_merged["en_US"], detail["en_US"], seq)
                vm_uuid = uuid

        if len(vms) == 1:
            return VMTemplateEventWrapper(
                user=self.user,
                uuid=vm_template.get("uuid"),
                name=vm_template.get("name"),
                vm_name=vms[vm_uuid],
                vm_uuid=vm_uuid,
                detail=detail_merged,
            )._event_vm_template_create_vm()
        else:
            return VMTemplateEventWrapper(
                user=self.user, name=vm_template.get("name"), uuid=vm_template.get("uuid"), detail=detail_merged
            )._event_vm_template_batch_create_vm(vms)

    def _event_vm_template_batch_create_vm(self, vms):
        failed = EventMessage.update_detail(i18n.event_detail.get("BATCH_CREATE_VM_FROM_TEMPLATE_FAILED"))
        batch = {}
        message_arg = {"vm_template_name": self.name, "vm_num": len(vms)}
        vm_template_data = {"vm_template_name": self.name, "vm_template_id": self.uuid}
        resources = {}
        detail_vm = EventMessage.update_detail(
            i18n.event_detail.get(EVENT_BATCH_CREATE_VM_FROM_TEMPLATE), **message_arg
        )
        for uuid, vm_name in list(vms.items()):
            batch, detail_vm = VMEventWrapper(user=self.user).generate_batch_detail(
                failed=failed,
                uuid=uuid,
                batch=batch,
                name=vm_name,
                i18n=i18n.event_detail.get("BATCH_VM_NAME_TEMPLATE"),
                detail_vm=detail_vm,
                done_detail={"zh_CN": vm_name, "en_US": vm_name},
            )
            resources.update({uuid: KVM_VM})
        detail = EventMessage.update_detail(detail_vm, self.detail)
        return build_event(
            event_name=EVENT_BATCH_CREATE_VM_FROM_TEMPLATE,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_template_data,
            resources=resources,
            detail=detail,
            batch=batch,
            event_mod=i18n.event_message.get(EVENT_BATCH_CREATE_VM_FROM_TEMPLATE),
            **message_arg,
        )
