# Copyright (c) 2013-2015, SMARTX
# All rights reserved.

event_message = {
    "CREATE_VM": {"zh_CN": "创建虚拟机 ${vm_name}。", "en_US": "Created VM ${vm_name}."},
    "DELETE_VM": {"zh_CN": "删除虚拟机 ${vm_name}。", "en_US": "Deleted VM ${vm_name}."},
    "BATCH_DELETE_VM": {"zh_CN": "批量删除 ${vm_num} 个虚拟机。", "en_US": "Deleted ${vm_num} VMs in bulk."},
    "EDIT_VM": {"zh_CN": "编辑虚拟机 ${vm_name}。", "en_US": "Edited VM ${vm_name}."},
    "EDIT_VM_VOLUME": {"zh_CN": "编辑虚拟机 ${vm_name} 的虚拟盘。", "en_US": "Edited virtual disk of VM ${vm_name}."},
    "EDIT_VM_NIC": {"zh_CN": "编辑虚拟机 ${vm_name} 的虚拟网卡。", "en_US": "Edited virtual NIC of VM ${vm_name}."},
    "EDIT_VM_DEVICE": {
        "zh_CN": "编辑虚拟机 ${vm_name} 的直通设备。",
        "en_US": "Edited pass-through device of VM ${vm_name}.",
    },
    "START_VM": {"zh_CN": "开机虚拟机 ${vm_name}。", "en_US": "Powered on VM ${vm_name}."},
    "BATCH_START_VM": {"zh_CN": "批量开机 ${vm_num} 个虚拟机。", "en_US": "Started ${vm_num} VMs in bulk."},
    "SHUTDOWN_VM": {"zh_CN": "关机虚拟机 ${vm_name}。", "en_US": "Shut down VM ${vm_name}."},
    "BATCH_SHUTDOWN_VM": {"zh_CN": "批量关机 ${vm_num} 个虚拟机。", "en_US": "Shutdown ${vm_num} VMs in bulk."},
    "FORCE_SHUTDOWN_VM": {"zh_CN": "强制关机虚拟机 ${vm_name}。", "en_US": "Powered off VM ${vm_name}."},
    "BATCH_FORCE_SHUTDOWN_VM": {
        "zh_CN": "批量强制关机 ${vm_num} 个虚拟机。",
        "en_US": "Powered off ${vm_num} VMs in bulk.",
    },
    "REBOOT_VM": {"zh_CN": "重启虚拟机 ${vm_name}。", "en_US": "Reboot VM ${vm_name}."},
    "BATCH_REBOOT_VM": {"zh_CN": "批量重启 ${vm_num} 个虚拟机。", "en_US": "Reboot ${vm_num} VMs in bulk."},
    "FORCE_REBOOT_VM": {"zh_CN": "强制重启虚拟机 ${vm_name}。", "en_US": "Restarted VM ${vm_name}."},
    "BATCH_FORCE_REBOOT_VM": {"zh_CN": "批量强制重启 ${vm_num} 个虚拟机。", "en_US": "Rstarted ${vm_num} VMs in bulk."},
    "SUSPEND_VM": {"zh_CN": "暂停虚拟机 ${vm_name}。", "en_US": "Suspended VM ${vm_name}."},
    "BATCH_SUSPEND_VM": {"zh_CN": "批量暂停 ${vm_num} 个虚拟机。", "en_US": "Suspended ${vm_num} VMs in bulk."},
    "RESUME_VM": {"zh_CN": "恢复虚拟机 ${vm_name}。", "en_US": "Recovered VM ${vm_name}."},
    "BATCH_RESUME_VM": {"zh_CN": "批量恢复 ${vm_num} 个虚拟机。", "en_US": "Recovered ${vm_num} VMs in bulk."},
    "CLONE_VM": {"zh_CN": "克隆虚拟机 ${vm_name}。", "en_US": "Cloned VM ${vm_name}."},
    "MIGRATE_VM": {
        "zh_CN": "将虚拟机 ${vm_name} 从 ${old_host} 迁移到了 ${new_host}。",
        "en_US": "Migrated VM ${vm_name} from host ${old_host} to host ${new_host}.",
    },
    "BATCH_MIGRATE_VM": {"zh_CN": "批量迁移 ${vm_num} 个虚拟机。", "en_US": "Migrated ${vm_num} VMs in bulk."},
    "VM_SELF_FENCE_PAUSE": {
        "zh_CN": "因主机 ${host_name} 异常，暂停虚拟机 ${vm_name}。",
        "en_US": "Paused VM ${vm_name} due to host ${host_name} failure.",
    },
    "VM_SELF_FENCE_STOP": {
        "zh_CN": "因主机 ${host_name} 异常，关闭虚拟机 ${vm_name}。",
        "en_US": "Stopped VM ${vm_name} due to host ${host_name} failure.",
    },
    "VM_HA": {
        "zh_CN": "主机 ${origin_host_name} 上的虚拟机 ${vm_name} 触发高可用，在主机 ${rebuild_host_name} 上重建。",
        "en_US": "VM ${vm_name} on host ${origin_host_name} triggered HA and was rebuilt on host ${rebuild_host_name}.",
    },
    "VM_LOCAL_HA": {
        "zh_CN": "主机 ${rebuild_host_name} 上的虚拟机 ${vm_name} 在当前主机上重建。",
        "en_US": "VM ${vm_name} on host ${rebuild_host_name} was rebuilt on the current host.",
    },
    "VM_NETWORK_HA": {
        "zh_CN": "主机 ${origin_host_name} 上的虚拟机 ${vm_name} 因虚拟机网络故障触发高可用，${action_desc}至主机 ${dest_host_name}。",
        "en_US": "VM ${vm_name} on host ${origin_host_name} was ${action_desc} to host ${dest_host_name} due to network HA.",
    },
    "VM_NETWORK_HA_MIGRATE": {
        "zh_CN": "热迁移",
        "en_US": "migrated",
    },
    "VM_NETWORK_HA_REBUILD": {
        "zh_CN": "重建",
        "en_US": "rebuilt",
    },
    "VM_OS_HA": {
        "zh_CN": "主机 ${host_name} 上的虚拟机 ${vm_name} 因操作系统故障在当前主机上重启。",
        "en_US": "VM ${vm_name} on host ${host_name} was rebooted on the current host due to OS failure.",
    },
    "VM_STATE_CHANGE": {
        "zh_CN": "主机 ${host_name} 上的虚拟机 ${vm_name} 的状态从客户机系统内部触发，由 ${old_state} 变成 ${new_state}。",
        "en_US": "The state change of the VM ${vm_name} on host ${host_name} was triggered inside the guest OS, from"
        " ${old_state} to ${new_state}.",
    },
    "CREATE_VM_SNAPSHOT": {
        "zh_CN": "创建虚拟机 ${vm_name} 的虚拟机快照 ${vm_snapshot_name}。",
        "en_US": "Created snapshot ${vm_snapshot_name} of VM ${vm_name}.",
    },
    "CREATE_VM_TEMPLATE": {
        "zh_CN": "创建虚拟机模板 ${vm_template_name}。",
        "en_US": "Created VM template ${vm_template_name}.",
    },
    "CREATE_VM_FROM_TEMPLATE": {
        "zh_CN": "从模板 ${vm_template_name} 创建虚拟机 ${vm_name}。",
        "en_US": "Created VM ${vm_name} from template ${vm_template_name}.",
    },
    "BATCH_CREATE_VM_FROM_TEMPLATE": {
        "zh_CN": "从模板 ${vm_template_name} 创建 ${vm_num} 个虚拟机。",
        "en_US": "Create ${vm_num} VMs from template ${vm_template_name}",
    },
    "VM_ROLLBACK": {
        "zh_CN": "将虚拟机 ${vm_name} 回滚至快照 ${vm_snapshot_name}。",
        "en_US": "Rolled back VM ${vm_name} to snapshot ${vm_snapshot_name}.",
    },
    "REBUILD_VM_FROM_SNAPSHOT": {
        "zh_CN": "从虚拟机快照 ${vm_snapshot_name} 重建出虚拟机 ${vm_name}。",
        "en_US": "Re-created VM ${vm_name} from VM snapshot ${vm_snapshot_name}.",
    },
    "CREATE_VOLUME": {
        "zh_CN": "创建空白虚拟卷 ${volume_name}。",
        "en_US": "Created blank virtual volume ${volume_name}.",
    },
    "CREATE_VOLUME_FROM_OBJ": {
        "zh_CN": "从存储对象创建了 ${volume_num} 个虚拟卷。",
        "en_US": "Created ${volume_num} virtual volumes from storage objects.",
    },
    "EDIT_VOLUME": {"zh_CN": "编辑虚拟卷 ${volume_name}。", "en_US": "Edited virtual volume ${volume_name}."},
    "UPLOAD_VOLUME": {"zh_CN": "上传虚拟卷 ${volume_name}。", "en_US": "Uploaded virtual volume ${volume_name}."},
    "DELETE_VOLUME": {"zh_CN": "删除虚拟卷 ${volume_name}。", "en_US": "Deleted virtual volume ${volume_name}."},
    "CLONE_VOLUME": {
        "zh_CN": "将虚拟卷 ${src_volume_name} 克隆为虚拟卷 ${volume_name}。",
        "en_US": "Cloned virtual volume ${src_volume_name} to virtual volume ${volume_name}.",
    },
    "CREATE_VOLUME_SNAPSHOT": {
        "zh_CN": "创建虚拟卷快照 ${volume_snapshot_name}。",
        "en_US": "Created virtual volume snapshot ${volume_snapshot_name}.",
    },
    "DELETE_VOLUME_SNAPSHOT": {
        "zh_CN": "删除虚拟卷快照 ${volume_snapshot_name}。",
        "en_US": "Deleted virtual volume snapshot ${volume_snapshot_name}.",
    },
    "VOLUME_ROLLBACK": {
        "zh_CN": "将虚拟卷 ${volume_name} 回滚至虚拟卷快照 ${volume_snapshot_name}。",
        "en_US": "Rolled back virtual volume ${volume_name} to virtual volume snapshot ${volume_snapshot_name}.",
    },
    "REBUILD_VOLUME_FROM_SNAPSHOT": {
        "zh_CN": "从虚拟卷快照 ${volume_snapshot_name} 重建出虚拟卷 ${volume_name}。",
        "en_US": "Re-created virtual volume ${volume_name} from virtual volume snapshot ${volume_snapshot_name}.",
    },
    "CREATE_STORAGE_POLICY": {
        "zh_CN": "创建存储策略 ${storage_policy_name}。",
        "en_US": "Created storage policy ${storage_policy_name}.",
    },
    "EDIT_STORAGE_POLICY": {
        "zh_CN": "编辑存储策略 ${storage_policy_name}。",
        "en_US": "Edited storage policy ${storage_policy_name}.",
    },
    "DELETE_STORAGE_POLICY": {
        "zh_CN": "删除存储策略 ${storage_policy_name}。",
        "en_US": "Deleted storage policy ${storage_policy_name}.",
    },
    "DELETE_STORAGE_POLICY_TARGETS": {
        "zh_CN": "删除存储策略 ${storage_policy_name} 中所有存储目标。",
        "en_US": "Deleted all targets from storage policy ${storage_policy_name}.",
    },
    "UPLOAD_ISO": {"zh_CN": "创建 ISO 映像 ${iso_name}。", "en_US": "Created ISO image ${iso_name}."},
    "EDIT_ISO": {"zh_CN": "编辑 ISO 映像 ${iso_name}。", "en_US": "Edited ISO image ${iso_name}."},
    "DELETE_ISO": {"zh_CN": "删除 ISO 映像 ${iso_name}。", "en_US": "Deleted ISO image ${iso_name}."},
    "CREATE_PLACEMENT_GROUP": {
        "zh_CN": "创建虚拟机放置组 ${placement_group_name}。",
        "en_US": "Created VM placement group ${placement_group_name}.",
    },
    "EDIT_PLACEMENT_GROUP": {
        "zh_CN": "编辑虚拟机放置组 ${placement_group_name}。",
        "en_US": "Edited VM placement group ${placement_group_name}.",
    },
    "DELETE_PLACEMENT_GROUP": {
        "zh_CN": "删除虚拟机放置组 ${placement_group_name}。",
        "en_US": "Deleted VM placement group ${placement_group_name}.",
    },
    "EDIT_CLUSTER_CPU_MODEL": {
        "zh_CN": "编辑集群虚拟机 CPU 模型兼容性。",
        "en_US": "Edited cluster VM CPU compatibility",
    },
    "SET_FOLDER": {
        "zh_CN": "虚拟机 ${vm_name} 加入组 ${new_folder_name}。",
        "en_US": "Added VM ${vm_name} to group ${new_folder_name}.",
    },
    "CHANGE_FOLDER": {
        "zh_CN": "虚拟机 ${vm_name} 从组 ${old_folder_name} 移动到组 ${new_folder_name}。",
        "en_US": "Moved VM ${vm_name} from group ${old_folder_name} to group ${new_folder_name}.",
    },
    "MOVE_OUT_FOLDER": {
        "zh_CN": "虚拟机 ${vm_name} 从组 ${old_folder_name} 移出。",
        "en_US": "Removed VM ${vm_name} from group ${old_folder_name}.",
    },
    "CREATE_FOLDER": {"zh_CN": "创建组 ${folder_name}。", "en_US": "Created group ${folder_name}."},
    "EDIT_FOLDER": {"zh_CN": "编辑组 ${folder_name}。", "en_US": "Edited group ${folder_name}."},
    "DELETE_FOLDER": {"zh_CN": "删除组 ${folder_name}。", "en_US": "Deleted group ${folder_name}."},
    "VM_BIND_ATTRIBUTE": {
        "zh_CN": "虚拟机 ${vm_name} 添加自定义属性 ${attribute_key}。",
        "en_US": "Added custom attribute ${attribute_key} to VM ${vm_name}.",
    },
    "VM_UNBIND_ATTRIBUTE": {
        "zh_CN": "虚拟机 ${vm_name} 移除自定义属性 ${attribute_key}。",
        "en_US": "Removed custom attribute ${attribute_key} of VM ${vm_name}.",
    },
    "NEW_ATTRIBUTE": {
        "zh_CN": "创建自定义属性 ${attribute_key}。",
        "en_US": "Created custom attribute ${attribute_key}.",
    },
    "EDIT_ATTRIBUTE": {
        "zh_CN": "编辑自定义属性名 ${old_attribute_key}。",
        "en_US": "Edited custom attribute name ${old_attribute_key}.",
    },
    "DELETE_ATTRIBUTE": {
        "zh_CN": "删除自定义属性 ${attribute_key}。",
        "en_US": "Deleted custom attribute ${attribute_key}.",
    },
    "ADD_ATTRIBUTE_VALUE": {
        "zh_CN": "创建自定义属性 ${attribute_key} 的属性值 ${attribute_value}。",
        "en_US": "Created attribute value ${attribute_value} for custom attribute ${attribute_key}.",
    },
    "EDIT_ATTRIBUTE_VALUE": {
        "zh_CN": "编辑自定义属性 ${attribute_key} 的属性值 ${attribute_value}。",
        "en_US": "Edited attribute value ${attribute_value} of custom attribute ${attribute_key}.",
    },
    "DELETE_ATTRIBUTE_VALUE": {
        "zh_CN": "删除自定义属性 ${attribute_key} 的属性值 ${attribute_value}。",
        "en_US": "Deleted attribute value ${attribute_value} of custom attribute ${attribute_key}.",
    },
    "AS_VM_TEMPLATE": {
        "zh_CN": "将虚拟机 ${vm_name} 转化为虚拟机模板 ${vm_template_name}，\
        模板是否支持 cloud-init：${cloud_init_supported}。",
        "en_US": "Converted VM ${vm_name} to VM template ${vm_template_name}, \
        is template support cloud-init: ${cloud_init_supported}.",
    },
    "AS_VM": {
        "zh_CN": "将虚拟机模板 ${vm_template_name} 转化为虚拟机 ${vm_name}。",
        "en_US": "Converted VM template ${vm_template_name} to VM ${vm_name} .",
    },
    "ADD_DEVICE": {
        "zh_CN": "主机 ${hostname} 增加可插拔设备 ${pass_through_name}。",
        "en_US": "Add pluggable device ${pass_through_name} to host ${hostname} .",
    },
    "REMOVE_DEVICE": {
        "zh_CN": "主机 ${hostname} 移除可插拔设备 ${pass_through_name}。",
        "en_US": "Remove pluggable device ${pass_through_name} from host ${hostname} .",
    },
    "ENABLE_CPU_EXCLUSIVE": {
        "zh_CN": "因高可用导致独占失效的虚拟机 ${vm_name} 的 CPU 独占功能恢复生效。",
        "en_US": "The virtual machine ${vm_name} resumes the CPU exclusive function that was disabled "
        "due to high availability.",
    },
    "ENABLE_CPU_QOS_RESERVATION": {
        "zh_CN": "因高可用导致预留失效的虚拟机 ${vm_name} 的 CPU QoS 预留功能恢复生效。",
        "en_US": "The virtual machine ${vm_name} resumes the CPU QoS reservation function that was disabled "
        "due to high availability.",
    },
    "START_DATA_PORT_CONVERSION": {"zh_CN": "开始数据转换", "en_US": "Start conversion"},
    "DATA_PORT_DELETE": {"zh_CN": "开始删除数据接口", "en_US": "Delete data port"},
    "AUTO_DETACH_VM_USB": {
        "zh_CN": "虚拟机 ${vm_name} 自动卸载 USB 设备",
        "en_US": "The VM ${vm_name} automatically detaches the USB device",
    },
    "BOOT_VM_WITH_HOST": {
        "zh_CN": "主机 ${node_hostname} 上的虚拟机 ${vm_name} 在当前主机上随主机开机。",
        "en_US": "The VM ${vm_name} on host ${node_hostname} starts along with its host.",
    },
    "DISABLE_AM_FOR_HA": {
        "zh_CN": "由于主机 ${rebuild_host_name} 不满足防病毒要求，已为虚拟机关闭防病毒相关功能",
        "en_US": "Because the host ${rebuild_host_name} does not meet requirement, anti malware has been cancelled",
    },
}

event_detail = {
    "CREATE_VM_INIT": {"zh_CN": "创建：\n", "en_US": "Created:\n"},
    "EDIT_VM_INIT": {"zh_CN": "编辑虚拟机：\n", "en_US": "Edited VM:\n"},
    "EDIT_NICS_INIT": {"zh_CN": "编辑虚拟网卡：\n", "en_US": "Edited nic:\n"},
    "EDIT_DEVICES_INIT": {"zh_CN": "编辑透传设备：\n", "en_US": "Edited pass-through device:\n"},
    "EDIT_DISKS_INIT": {"zh_CN": "编辑虚拟盘：\n", "en_US": "Edited virtual disk:\n"},
    "CREATE_VM_TEMPLATE_INIT": {
        "zh_CN": "创建虚拟机模板 ${vm_template_name}：\n",
        "en_US": "Created VM template ${vm_template_name}: \n",
    },
    "CREATE_PLACEMENT_GROUP_INIT": {
        "zh_CN": "创建：\n${placement_group_name} \n",
        "en_US": "Created: \n${placement_group_name} \n",
    },
    "EDIT_INIT": {"zh_CN": "编辑：\n", "en_US": "Edited: \n"},
    "CLONE_VM_INIT": {"zh_CN": "克隆：\n", "en_US": "Cloned: \n"},
    "HA_ON": {"zh_CN": "高可用：启用 \n", "en_US": "HA: Enabled \n"},
    "HA_OFF": {"zh_CN": "高可用：禁用 \n", "en_US": "HA: Disabled \n"},
    "HA_TO_OFF": {"zh_CN": "高可用：启用 -> 禁用 \n", "en_US": "HA: Enabled -> disabled \n"},
    "HA_TO_ON": {"zh_CN": "高可用：禁用 -> 启用 \n", "en_US": "HA: disabled -> Enabled \n"},
    "SYNC_VM_TIME_ON_RESUME": {
        "zh_CN": "恢复时同步主机时间：${old_sync_opt} -> ${new_sync_opt} \n",
        "en_US": "Synchronize with host time on resume: ${old_sync_opt} -> ${new_sync_opt} \n",
    },
    "VCPU_NUM": {"zh_CN": "CPU：${vcpu_num} vCPU \n", "en_US": "CPU: ${vcpu_num} vCPUs \n"},
    "VCPU_CHANGED": {
        "zh_CN": "CPU：${old_vcpu} vCPU -> ${new_vcpu} vCPU \n",
        "en_US": "CPU: ${old_vcpu} vCPUs -> ${new_vcpu} vCPUs \n",
    },
    "CPU_SLOT_NUM": {"zh_CN": "插槽数：${slot_num} \n", "en_US": "Sockets: ${slot_num} \n"},
    "CPU_SLOT_CORE_CHANGED": {
        "zh_CN": "每插槽核数：${old_core} -> ${new_core} \n",
        "en_US": "Cores per socket: ${old_core} -> ${new_core} \n",
    },
    "CPU_SLOT_CHANGED": {
        "zh_CN": "插槽数：${old_slot_num} -> ${new_slot_num} \n",
        "en_US": "Sockets: ${old_slot_num} -> ${new_slot_num} \n",
    },
    "MEMORY": {"zh_CN": "内存：${mem} GiB \n", "en_US": "Memory: ${mem} GiB \n"},
    "MEMORY_CHANGED": {
        "zh_CN": "内存：${old_mem} GiB -> ${new_mem} GiB\n",
        "en_US": "Memory: ${old_mem} GiB -> ${new_mem} GiB\n",
    },
    "DESCRIPTION_CHANGED": {"zh_CN": "描述：${new_description} \n", "en_US": "Description: ${new_description} \n"},
    "VM_NAME_CHANGED": {
        "zh_CN": "虚拟机名称：${old_name} -> ${new_name} \n",
        "en_US": "VM name: ${old_name} -> ${new_name} \n",
    },
    "CLOCK_OFFSET_CHANGED": {
        "zh_CN": "时钟偏移：${old_clock_offset} -> ${new_clock_offset} \n",
        "en_US": "clock offset: ${old_clock_offset} -> ${new_clock_offset} \n",
    },
    "NESTED_VIRTUALIZATION_OFF": {
        "zh_CN": "嵌套虚拟化：启用 -> 禁用 \n",
        "en_US": "nested virtualization: on -> off \n",
    },
    "NESTED_VIRTUALIZATION_ON": {
        "zh_CN": "嵌套虚拟化：禁用 -> 启用 \n",
        "en_US": "nested virtualization: off -> on \n",
    },
    "CPU_MODEL_CHANGED": {
        "zh_CN": "CPU模式：${old_cpu_model} -> ${new_cpu_model} \n",
        "en_US": "CPU model: ${old_cpu_model} -> ${new_cpu_model} \n",
    },
    "WIN_OPT_OFF": {"zh_CN": "Windows优化：启用 -> 不启用 \n", "en_US": "Windows optimization: on -> off \n"},
    "WIN_OPT_ON": {"zh_CN": "Windows优化：不启用 -> 启用 \n", "en_US": "Windows optimization: off -> on \n"},
    "FIRMWARE": {"zh_CN": "固件：${firmware} \n", "en_US": "firmware: ${firmware} \n"},
    "FIRMWARE_CHANGED": {
        "zh_CN": "固件：${old_firmware} -> ${new_firmware} \n",
        "en_US": "firmware: ${old_firmware} -> ${new_firmware} \n",
    },
    "NIC": {
        "zh_CN": "nic${num}：网络：${vlan_name} 模式：${model} \n",
        "en_US": "nic${num}: network: ${vlan_name} model: ${model} \n",
    },
    "NIC_UP": {"zh_CN": "           连接状态：连接 \n", "en_US": "           link state: up\n"},
    "NIC_DOWN": {"zh_CN": "           连接状态：断开 \n", "en_US": "           link state: down\n"},
    "NIC_MIRROR": {"zh_CN": "           镜像模式 \n", "en_US": "           mirror mode\n"},
    "ATTACH_NIC": {"zh_CN": "添加网卡：${mac} 模式：${model} \n", "en_US": "Attach nic: ${mac} model: ${model} \n"},
    "DETACH_NIC": {"zh_CN": "移除网卡：${mac} \n", "en_US": "Detach nic: ${mac} \n"},
    "ATTACH_DEVICE": {"zh_CN": "添加设备：${name} \n", "en_US": "Attach device: ${name} \n"},
    "DETACH_DEVICE": {"zh_CN": "移除设备：${name} \n", "en_US": "Detach device: ${name} \n"},
    "MODIFY_NIC_BR": {
        "zh_CN": "修改网卡 ${mac}：虚拟分布式交换机 ${old_br} -> ${new_br} \n",
        "en_US": "Modify nic ${mac}: switching vm network of vds ${old_br} -> ${new_br} \n",
    },
    "MODIFY_NIC_VLAN": {
        "zh_CN": "修改网卡 ${mac}：虚拟机网络 ${old_vlan} -> ${new_vlan} \n",
        "en_US": "Modify nic ${mac}: switching vm network ${old_vlan} -> ${new_vlan} \n",
    },
    "MODIFY_NIC_IP": {
        "zh_CN": "修改网卡 ${mac}：IP ${old_ip} -> ${new_ip} \n",
        "en_US": "Modify nic ${mac}: modify IP ${old_ip} -> ${new_ip} \n",
    },
    "MODIFY_NIC_UP": {"zh_CN": "修改网卡 ${mac}：连接状态：连接 \n", "en_US": "Modify nic ${mac}: link state: up\n"},
    "MODIFY_NIC_DOWN": {
        "zh_CN": "修改网卡 ${mac}：连接状态：断开 \n",
        "en_US": "Modify nic ${mac}: link state: down\n",
    },
    "MODIFY_NIC_MIRROR_SET": {"zh_CN": "修改网卡 ${mac}：镜像模式 \n", "en_US": "Modify nic ${mac}: mirror mode \n"},
    "MODIFY_NIC_MIRROR_REMOVE": {
        "zh_CN": "修改网卡 ${mac}：关闭镜像模式 \n",
        "en_US": "Modify nic ${mac}: remove mirror mode \n",
    },
    "ATTACH": {"zh_CN": "新增：\n", "en_US": "Added: \n"},
    "ATTACH_VOLUME": {
        "zh_CN": "虚拟盘${boot}（虚拟卷）：\n    启动顺序：${boot}\n    虚拟卷名：${vol_name}\n    容量：${size} GiB\n"
        "    总线：${bus}\n    存储策略：${storage_policy}\n",
        "en_US": "Virtual disk ${boot} (virtual volume): \n    Boot sequence: ${boot}\n    Virtual volume name:"
        " ${vol_name}\n    Capacity: ${size} GiB\n    Bus: ${bus}\n    Storage_policy: ${storage_policy}\n",
    },
    "ATTACH_VOLUME_SHARE": {
        "zh_CN": "虚拟盘${boot}（共享虚拟卷）：\n    启动顺序：${boot}\n    虚拟卷名：${vol_name}\n    容量：${size} GiB\n"
        "    总线：${bus}\n    存储策略：${storage_policy} \n",
        "en_US": "Virtual disk ${boot} (shared volume): \n    Boot sequence: ${boot}\n    Virtual volume name:"
        " ${vol_name}\n    Capacity: ${size} GiB\n    Bus: ${bus}\n    Storage policy: ${storage_policy} \n",
    },
    "REBUILD_VOLUME": {
        "zh_CN": "重建：\n虚拟卷名称：${volume_name} \n容量：${size} GiB \n存储策略：${storage_policy} \n描述："
        "${description} \n类型：虚拟卷 \n",
        "en_US": "Rebuild: \nvolume name : ${volume_name} \nCapacity: ${size} GiB \nstorage policy: "
        "${storage_policy}\ndescription: ${description} \ntype: volume \n",
    },
    "REBUILD_VOLUME_SHARE": {
        "zh_CN": "重建：\n虚拟卷名称：${volume_name}\n容量：${size} GiB\n存储策略：${storage_policy}\n描述："
        "${description}\n类型：共享虚拟卷\n ",
        "en_US": "Rebuild:\nvolume name : ${volume_name}\nCapacity: ${size} GiB\nstorage policy: ${storage_policy}"
        "\ndescription: ${description} \ntype: Shared volume \n",
    },
    "ATTACH_CDROM": {
        "zh_CN": "虚拟盘${boot}（CDROM）：\n    启动顺序：${boot}\n    ISO：${iso_name} \n",
        "en_US": "Virtual disk ${boot} (CDROM): \n    Boot sequence: ${boot}\n    ISO: ${iso_name} \n",
    },
    "DETACH": {"zh_CN": "卸载：\n", "en_US": "Uninstalled: \n"},
    "DELETED": {"zh_CN": "删除：\n", "en_US": "Deleted: \n"},
    "DETACH_VOLUME": {
        "zh_CN": "虚拟盘${boot}（虚拟卷）：\n    虚拟卷名：${vol_name} \n",
        "en_US": "Virtual disk ${boot} (VOLUME): \n    Virtual vol name: ${vol_name} \n",
    },
    "DETACH_CDROM": {
        "zh_CN": "虚拟盘${boot}（CDROM）：\n    ISO：${iso_name} \n",
        "en_US": "Virtual disk ${boot} (CDROM): \n    ISO: ${iso_name} \n",
    },
    "MODIFY": {"zh_CN": "修改：\n", "en_US": "Modified: \n"},
    "MODIFY_VOLUME": {
        "zh_CN": "虚拟盘${old_boot}（虚拟卷）：\n    虚拟卷名：${vol_name} \n",
        "en_US": "Virtual disk ${old_boot} (VOLUME): \n    Virtual vol name: ${vol_name} \n",
    },
    "MODIFY_BOOT": {
        "zh_CN": "    启动顺序：${old_boot} -> ${new_boot} \n",
        "en_US": "    Boot sequence: ${old_boot} -> ${new_boot} \n",
    },
    "MODIFY_VOLUME_SIZE": {
        "zh_CN": "    容量：${old_size} GiB -> ${new_size} GiB \n",
        "en_US": "    Capacity: ${old_size} GiB -> ${new_size} GiB \n",
    },
    "MODIFY_VOLUME_STORAGE_POLICY": {
        "zh_CN": "    存储策略：${old_name} -> ${new_name} \n",
        "en_US": "    Storage policy: ${old_name} -> ${new_name} \n",
    },
    "MODIFY_VOLUME_BUS": {
        "zh_CN": "    总线：${old_bus} -> ${new_bus} \n",
        "en_US": "    Bus: ${old_bus} -> ${new_bus} \n",
    },
    "MODIFY_CDROM_BOOT": {
        "zh_CN": "虚拟盘${old_boot}（CDROM）：\n    启动顺序：${old_boot} -> ${new_boot} \n",
        "en_US": "Virtual disk ${old_boot} (CDROM): \n    Boot sequence: ${old_boot} -> ${new_boot} \n",
    },
    "MODIFY_CDROM_AVAILABLE": {
        "zh_CN": "虚拟盘${old_boot}（CDROM）：\n    启用 \n",
        "en_US": "Virtual disk ${old_boot} (CDROM): \n    Enable \n",
    },
    "MODIFY_CDROM_DISABLED": {
        "zh_CN": "虚拟盘${old_boot}（CDROM）：\n    禁用 \n",
        "en_US": "Virtual disk ${old_boot} (CDROM): \n    Disable \n",
    },
    "PLACEMENT_MEMBERS": {"zh_CN": "放置组成员：${vm_names} \n", "en_US": "Placement group members : ${vm_names} \n"},
    "PREFER_DIFFERENT": {"zh_CN": "优先放置在不同的主机 \n", "en_US": "Prioritize placement on different hosts \n"},
    "PREFER_SAME": {"zh_CN": "优先放置在相同的主机 \n", "en_US": "Prioritize placement on the same host \n"},
    "MUST_DIFFERENT": {"zh_CN": "必须放置在不同的主机 \n", "en_US": "Must be placed on different hosts \n"},
    "MUST_SAME": {"zh_CN": "必须放置在相同的主机 \n", "en_US": "Must be placed on the same host \n"},
    "PLACEMENT_POLICY_OFF": {"zh_CN": "策略：禁用\n", "en_US": "Policy: off\n"},
    "MUST_BE": {"zh_CN": "必须放置在如下主机：${hosts}\n", "en_US": "Must be placed on the following host: ${hosts}\n"},
    "MUST_NOT_BE": {
        "zh_CN": "必须不放置在如下主机：${hosts}\n",
        "en_US": "Must not be placed on the following host: ${hosts}\n",
    },
    "PREFER": {
        "zh_CN": "优先放置在如下主机：${hosts}\n",
        "en_US": "Priority is placed on the following hosts: ${hosts}\n",
    },
    "PREFER_NOT": {
        "zh_CN": "优先不放置在如下主机：${hosts}\n",
        "en_US": "Priority is not placed on the following host: ${hosts}\n",
    },
    "PLACEMENT_GROUP_NAME": {
        "zh_CN": "放置组名称：${old_name} -> ${new_name}\n",
        "en_US": "Placement group name: ${old_name} -> ${new_name}\n",
    },
    "PLACEMENT_GROUP_POLICY_TO_ON": {
        "zh_CN": "放置组策略 ： 禁用 -> 启用\n",
        "en_US": "Placement group policy: off -> on\n",
    },
    "PLACEMENT_GROUP_POLICY_TO_OFF": {
        "zh_CN": "放置组策略 ： 启用 -> 禁用\n",
        "en_US": "Placement group policy: on -> off\n",
    },
    "PLACEMENT_GROUP_POLICY_EMPTY": {"zh_CN": "无\n", "en_US": "Empty\n"},
    "PLACEMENT_GROUP_VM": {
        "zh_CN": "放置组虚拟机：${old_names} -> ${new_names}\n",
        "en_US": "Placement group: ${old_names} -> ${new_names}\n",
    },
    "PLACEMENT_GROUP_POLICY": {
        "zh_CN": "策略：\n${old_policy_zh} -> \n${new_policy_zh}",
        "en_US": "Policy: \n${old_policy_en} -> \n${new_policy_en}",
    },
    "CREATE_STORAGE_POLICY_INIT": {
        "zh_CN": "创建：\n存储策略名：${storage_policy_name}\n"
        "白名单：${white_list}\n条带数：${strip_num}\n条带大小：${strip_size}\n",
        "en_US": "Created: \nStorage policy name: ${storage_policy_name}\n"
        "Whitelist: ${white_list}\nNumber of stripes: ${strip_num}\nStrip size: ${strip_size}\n",
    },
    "REPLICA_SP_PARAMETER": {
        "zh_CN": "冗余策略：副本\n副本数：${replica_num}\n",
        "en_US": "Resiliency type: Replica\nReplication factor: ${replica_num}\n",
    },
    "EC_SP_PARAMETER": {
        "zh_CN": "冗余策略：EC\nEC 配置：${ec_k}+${ec_m}\n",
        "en_US": "Resiliency type: EC\nEC settings: ${ec_k}+${ec_m}\n",
    },
    "THIN_CONFIG_ON": {"zh_CN": "精简配置：启用\n", "en_US": "Thin provisioned: Enabled\n"},
    "THIN_CONFIG_OFF": {"zh_CN": "精简配置：禁用\n", "en_US": "Thin provisioned: Disabled\n"},
    "THIN_CONFIG_TO_ON": {"zh_CN": "精简配置：禁用 -> 启用\n", "en_US": "Thin provisioned: Disabled -> Enabled\n"},
    "THIN_CONFIG_TO_OFF": {"zh_CN": "精简配置：启用 -> 禁用 \n", "en_US": "Thin Provisioning: Enabled -> Disabled \n"},
    "WHITE_LIST_CHANGED": {
        "zh_CN": "白名单：${old_white_list} -> ${new_white_list}\n",
        "en_US": "Whitelist: ${old_white_list} -> ${new_white_list}\n",
    },
    "STORAGE_POLICY_NAME_CHANGED": {
        "zh_CN": "存储策略名称：${old_name} -> ${new_name} \n",
        "en_US": "Storage policy name: ${old_name} -> ${new_name} \n",
    },
    "BATCH_MIGRATE_VM": {
        "zh_CN": "${vm_name}：\n    所属主机：${old_host} -> ${new_host}",
        "en_US": "${vm_name}:\n    Host computer: ${old_host} -> ${new_host}",
    },
    "BATCH_MIGRATE_VM_FAILED": {"zh_CN": "${vm_name}：\n    迁移失败\n", "en_US": "${vm_name}:\n    Migrated failed\n"},
    "CREATE_VOLUME": {
        "zh_CN": "创建：\n虚拟卷名：${vol_name}\n容量：${vol_size} GiB\n类型：虚拟卷\n存储策略："
        "${storage_policy_name}\n 数据加密：${encryption_algorithm}\n",
        "en_US": "Added: \nVirtual volume name: ${vol_name}\nCapacity: ${vol_size}GiB\nType: virtual volume\n"
        "Storage policy: ${storage_policy_name}\n Data encryption: ${encryption_algorithm}\n",
    },
    "CREATE_VOLUME_SHARED": {
        "zh_CN": "创建：\n虚拟卷名：${vol_name}\n容量：${vol_size} GiB\n类型：共享虚拟卷\n存储策略："
        "${storage_policy_name}\n",
        "en_US": "Added: \nVirtual volume name: ${vol_name}\nCapacity: ${vol_size} GiB\nType: shared volume\n"
        "Storage policy: ${storage_policy_name}\n",
    },
    "VOLUME_NAME_CHANGED": {
        "zh_CN": "虚拟卷名：${old_name} -> ${new_name}\n",
        "en_US": "Virtual volume name: ${old_name} -> ${new_name}\n",
    },
    "VOLUME_SIZE_CHANGED": {
        "zh_CN": "容量：${old_size} GiB -> ${new_size} GiB \n",
        "en_US": "Capacity: ${old_size} GiB -> ${new_size} GiB \n",
    },
    "VOLUME_STORAGE_POLICY_CHANGED": {
        "zh_CN": "存储策略：${old_name} -> ${new_name} \n",
        "en_US": "Storage policy: ${old_name} -> ${new_name} \n",
    },
    "CREATE_VOLUME_FROM_STORAGE_OBJ": {
        "zh_CN": "虚拟卷 ${vol_name}：\n    源存储对象类型：${obj_type}\n    源存储对象数据存储：${datastore}\n    源存储对象名："
        " ${obj_name}\n    容量：${vol_size} GiB\n    类型：虚拟卷\n    存储策略："
        "${storage_policy_name}\n",
        "en_US": "Virtual volume ${vol_name}:\n    Source storage object type: ${obj_type}\n    Source storage object"
        " datastore: ${datastore}\n    Source storage object name: ${obj_name}\n    Capacity: "
        "${vol_size}GiB\n    Type: virtual volume\n    Storage policy: ${storage_policy_name}\n",
    },
    "CREATE_VOLUME_FROM_STORAGE_OBJ_SHARED": {
        "zh_CN": "虚拟卷 ${vol_name}：\n    源存储对象类型：${obj_type}\n    源存储对象数据存储：${datastore}\n    源存储对象名："
        " ${obj_name}\n    容量：${vol_size} GiB\n    类型：共享虚拟卷\n    存储策略："
        "${storage_policy_name}\n",
        "en_US": "Virtual volume ${vol_name}:\n    Source storage object type: ${obj_type}\n    Source storage object"
        " datastore: ${datastore}\n    Source storage object name: ${obj_name}\n    Capacity: "
        "${vol_size}GiB\n    Type: shared volume\n    Storage policy: ${storage_policy_name}\n",
    },
    "UPLOAD_VOLUME": {
        "zh_CN": "上传：\n虚拟卷名：${vol_name}\n容量：${vol_size} GiB\n类型：虚拟卷\n存储策略："
        "${storage_policy_name}\n",
        "en_US": "Upload: \nvolume name: ${vol_name}\nCapacity: ${vol_size}GiB\ntype: Virtual vol\nStorage policy:"
        " ${storage_policy_name}\n",
    },
    "UPLOAD_VOLUME_SHARED": {
        "zh_CN": "上传：\n虚拟卷名：${vol_name}\n容量：${vol_size} GiB\n类型：共享虚拟卷\n存储策略："
        "${storage_policy_name}\n",
        "en_US": "Upload: \nvolume name: ${vol_name}\nCapacity: ${vol_size} GiB\ntype: Shared virtual vol\n"
        "Storage policy: ${storage_policy_name}\n",
    },
    "BATCH_DELETE_VM": {"zh_CN": "删除的 ${vm_num} 个虚拟机：\n", "en_US": "Deleted ${vm_num} VMs: \n"},
    "BATCH_DELETE_VM_FAILED": {"zh_CN": "${vm_name}：\n    删除失败", "en_US": "${vm_name}:\n    Deleted failed"},
    "BATCH_START_VM": {"zh_CN": "开机的 ${vm_num} 个虚拟机：\n", "en_US": "Started ${vm_num} VMs: \n"},
    "BATCH_START_VM_FAILED": {"zh_CN": "${vm_name}：\n    开机失败", "en_US": "${vm_name}:\n    Started failed"},
    "BATCH_SHUTDOWN_VM": {"zh_CN": "关机的 ${vm_num} 个虚拟机：\n", "en_US": "Shut down ${vm_num} VMs:\n"},
    "BATCH_SHUTDOWN_VM_FAILED": {"zh_CN": "${vm_name}：\n    关机失败", "en_US": "${vm_name}:\n    Shut down failed"},
    "BATCH_FORCE_SHUTDOWN_VM": {"zh_CN": "强制关机的 ${vm_num} 个虚拟机：\n", "en_US": "Powered off ${vm_num} VMs: \n"},
    "BATCH_FORCE_SHUTDOWN_VM_FAILED": {
        "zh_CN": "${vm_name}：\n    强制关机失败",
        "en_US": "${vm_name}:\n    Powered off failed",
    },
    "BATCH_FORCE_REBOOT_VM": {"zh_CN": "强制重启的 ${vm_num} 个虚拟机：\n", "en_US": "Restarted ${vm_num} VMs: \n"},
    "BATCH_FORCE_REBOOT_VM_FAILED": {
        "zh_CN": "${vm_name}：\n    强制重启失败",
        "en_US": "${vm_name}:\n    Restarted failed",
    },
    "BATCH_SUSPEND_VM": {"zh_CN": "暂停的 ${vm_num} 个虚拟机：\n", "en_US": "Suspended ${vm_num} VMs: \n"},
    "BATCH_SUSPEND_VM_FAILED": {"zh_CN": "${vm_name}：\n    暂停失败", "en_US": "${vm_name}:\n    Suspended failed"},
    "BATCH_RESUME_VM": {"zh_CN": "恢复的 ${vm_num} 个虚拟机：\n", "en_US": "Recovered ${vm_num} VMs: \n"},
    "BATCH_RESUME_VM_FAILED": {"zh_CN": "${vm_name}：\n    恢复失败", "en_US": "${vm_name}:\n    Recovered failed"},
    "BATCH_REBOOT_VM": {"zh_CN": "重启的 ${vm_num} 个虚拟机：\n", "en_US": "Reboot to ${vm_num} VMs: \n"},
    "BATCH_REBOOT_VM_FAILED": {"zh_CN": "${vm_name}：\n    重启失败", "en_US": "${vm_name}:\n    Reboot failed"},
    "BATCH_VM_NAME": {"zh_CN": "${vm_name}\n", "en_US": "${vm_name}\n"},
    "BATCH_CREATE_VM_FROM_TEMPLATE": {
        "zh_CN": "从模板创建 ${vm_num} 个虚拟机：\n虚拟机名：\n",
        "en_US": "Create ${vm_num} virtual machines from templates:\nvirtual machines name:\n",
    },
    "BATCH_VM_NAME_TEMPLATE": {"zh_CN": "    ${vm_name}\n", "en_US": "    ${vm_name}\n"},
    "BATCH_CREATE_VM_FROM_TEMPLATE_FAILED": {
        "zh_CN": "    ${vm_name}：\n    创建失败",
        "en_US": "    ${vm_name}:\n    Created failed",
    },
    "CREATE_ISO": {
        "zh_CN": "创建：\nISO 映像名：${iso_name}\n描述：${description}",
        "en_US": "Created:\nISO image name: ${iso_name}\nDescription: ${description}",
    },
    "ISO_NAME_CHANGED": {
        "zh_CN": "ISO 映像名：${old_name} -> ${new_name} \n",
        "en_US": "ISO image name: ${old_name} -> ${new_name} \n",
    },
    "EDIT_CLUSTER_CPU_MODEL": {
        "zh_CN": "CPU 模型兼容性：${old_model} -> ${new_model}",
        "en_US": "CPU Model Compatibility: ${old_model} -> ${new_model}",
    },
    "CREATE_MODE_NORMAL": {"zh_CN": "创建方法：快速克隆创建\n", "en_US": "Create mode: COW\n"},
    "CREATE_MODE_FULL_COPY": {"zh_CN": "创建方法：完全拷贝克隆创建\n", "en_US": "Create mode: FULL COPY\n"},
    "IO_QUOTA_NO": {"zh_CN": "I/O 限制：不限制\n", "en_US": "IO limit: Do not apply\n"},
    "IO_QUOTA_VM": {"zh_CN": "I/O 限制：对虚拟机整体限制\n", "en_US": "IO limit: Use VM-level I/O limit\n"},
    "IO_QUOTA_VOLUME": {"zh_CN": "I/O 限制：对虚拟卷分别限制\n", "en_US": "IO limit: Use volume-specific I/O limit \n"},
    "IO_QUOTA_VM_IOPS_DYNAMIC": {
        "zh_CN": "    虚拟机整体 IOPS 限制：${iops}（动态）\n",
        "en_US": "    VM-level IPOS limit: ${iops} (dynamic)\n",
    },
    "IO_QUOTA_VM_IOPS_HARD": {
        "zh_CN": "    虚拟机整体 IOPS 限制：${iops}（强制）\n",
        "en_US": "    VM-level IPOS limit: ${iops} (hard)\n",
    },
    "IO_QUOTA_VM_BANDWIDTH_DYNAMIC": {
        "zh_CN": "    虚拟机整体带宽限制：${bandwidth} Mbps（动态）\n",
        "en_US": "    VM-level bandwidth limit: ${bandwidth} Mbps (dynamic) \n",
    },
    "IO_QUOTA_VM_BANDWIDTH_HARD": {
        "zh_CN": "    虚拟机整体带宽限制：${bandwidth} Mbps（强制）\n",
        "en_US": "    VM-level bandwidth limit: ${bandwidth} Mbps (hard) \n",
    },
    "IO_QUOTA_VOLUME_IOPS_DYNAMIC": {
        "zh_CN": "    IOPS 限制：${iops}（动态）\n",
        "en_US": "    IPOS limit: ${iops} (dynamic)\n",
    },
    "IO_QUOTA_VOLUME_IOPS_HARD": {
        "zh_CN": "    IOPS 限制：${iops}（强制）\n",
        "en_US": "    IPOS limit: ${iops} (hard)\n",
    },
    "IO_QUOTA_VOLUME_BANDWIDTH_DYNAMIC": {
        "zh_CN": "    带宽限制：${bandwidth} Mbps（动态）\n",
        "en_US": "    Bandwidth limit: ${bandwidth} Mbps (dynamic) \n",
    },
    "IO_QUOTA_VOLUME_BANDWIDTH_HARD": {
        "zh_CN": "    带宽限制：${bandwidth} Mbps（强制）\n",
        "en_US": "    Bandwidth limit: ${bandwidth} Mbps (hard) \n",
    },
    "IO_QUOTA_VOLUME_IOPS_CHANGE": {
        "zh_CN": "    IOPS 限制：${old_iops_cn} -> ${new_iops_cn} \n",
        "en_US": "    IPOS limit: ${old_iops_us} -> ${new_iops_us} \n",
    },
    "NO_QUOTA": {"zh_CN": "无限制", "en_US": "Do not apply"},
    "IOPS_QUOTA_DYNAMIC": {"zh_CN": "${iops}（动态）", "en_US": "${iops} (dynamic)"},
    "IOPS_QUOTA_HARD": {"zh_CN": "${iops}（强制）", "en_US": "${iops} (hard)"},
    "BANDWIDTH_QUOTA_DYNAMIC": {"zh_CN": "${bandwidth} Mbps（动态）", "en_US": "${bandwidth} Mbps (dynamic) "},
    "BANDWIDTH_QUOTA_HARD": {"zh_CN": "${bandwidth} Mbps（强制）", "en_US": "${bandwidth} Mbps (hard) "},
    "IO_QUOTA_VOLUME_BANDWIDTH_CHANGE": {
        "zh_CN": "    带宽限制：${old_bandwidth_cn} -> ${new_bandwidth_cn} \n",
        "en_US": "    Bandwidth limit: ${old_bandwidth_us} -> ${new_bandwidth_us} \n",
    },
    "FOLDER_SET": {"zh_CN": "组：${folder} \n", "en_US": "Group: ${folder} \n"},
    "FOLDER_NOT_SET": {"zh_CN": "不分组 \n", "en_US": "Not Set \n"},
    "EDIT_FOLDER": {
        "zh_CN": "组名：${old_folder} -> ${new_folder} \n",
        "en_US": "Group name: ${old_folder} -> ${new_folder} \n",
    },
    "ATTRIBUTE": {
        "zh_CN": "${attribute_key}：${attribute_values} \n",
        "en_US": "${attribute_key}: ${attribute_values} \n",
    },
    "ATTRIBUTE_KEY_CHANGED": {
        "zh_CN": "属性名：${old_attribute_key} -> ${new_attribute_key} \n",
        "en_US": "Attribute name: ${old_attribute_key} ->  ${new_attribute_key} \n",
    },
    "ATTRIBUTE_VALUE_CHANGED": {
        "zh_CN": "属性值：${old_attribute_value} -> ${new_attribute_value} \n",
        "en_US": "Attribute name: ${old_attribute_value} -> ${new_attribute_value} \n",
    },
    "DELETE_VOLUME": {
        "zh_CN": "连同删除虚拟卷：${delete_volumes} \n",
        "en_US": "Also delete the volumes: ${delete_volumes} \n",
    },
    "DELETE_VOLUME_SNAPSHOT": {
        "zh_CN": "连同删除虚拟卷快照：${delete_volume_snapshots} \n",
        "en_US": "Also delete the volume snapshots: ${delete_volume_snapshots} \n",
    },
    "DELETE_VM_SNAPSHOT": {
        "zh_CN": "连同删除虚拟机快照：${delete_vm_snapshots} \n",
        "en_US": "Also delete the vm snapshots: ${delete_vm_snapshots} \n",
    },
    "CPU_EXCLUSIVE_ON": {"zh_CN": "CPU 独占：启用 \n", "en_US": "CPU exclusive: Enabled \n"},
    "CPU_EXCLUSIVE_OFF": {"zh_CN": "CPU 独占：未启用 \n", "en_US": "CPU exclusive: Disabled \n"},
    "CPU_EXCLUSIVE_CHANGED": {
        "zh_CN": "CPU 独占：${old_status} -> ${new_status} \n",
        "en_US": "CPU exclusive: ${old_status} -> ${new_status} \n",
    },
    "PASS_THROUGH_GPU": {"zh_CN": "透传 GPU：${name} \n", "en_US": "pass-through GPU: ${name} \n"},
    "PASS_THROUGH_NIC": {"zh_CN": "透传 NIC：${name} \n", "en_US": "pass-through NIC: ${name} \n"},
    "PASS_THROUGH_PCI": {
        "zh_CN": "透传 PCI 设备：${name} \n",
        "en_US": "pass-through PCI device: ${name} \n",
    },
    "PASS_THROUGH_VF_PCI": {
        "zh_CN": "透传 SR-IOV PCI 设备：${name} \n",
        "en_US": "pass-through SR-IOV PCI Device: ${name} \n",
    },
    "CPU_QOS_CHANGED": {
        "zh_CN": "CPU QoS："
        "份额：${old_shares} -> ${new_shares}，"
        "预留（单位：赫兹）：${old_reservation_hz} -> ${new_reservation_hz}，"
        "限制（单位：赫兹）：${old_limit_hz} -> ${new_limit_hz} \n",
        "en_US": "CPU QoS："
        "shares: ${old_shares} -> ${new_shares}, "
        "reservation(Unit: Hertz): ${old_reservation_hz} -> ${new_reservation_hz}, "
        "limit(Unit: Hertz): ${old_limit_hz} -> ${new_limit_hz} \n",
    },
}
