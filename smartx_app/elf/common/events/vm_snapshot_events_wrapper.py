# Copyright (c) 2013-2015, SMARTX
# All rights reserved.

from common.event.event_message import build_event, event_enclosure
from smartx_app.elf.common.events.constants import EVENT_CREATE_VM_SNAPSHOT
import smartx_app.elf.common.events.message_i18n_elf as i18n
from smartx_app.elf.job_center.constants import KVM_VM, KVM_VM_SNAPSHOT  # noqa: F401


class VMSnapshotEventWrapper:
    def __init__(self, user, uuid, name, vm_uuid=None, detail=None, vm_name=None, consistent_type=None):
        self.user_name = user.get("username", "-")
        self.user_role = user.get("Role")
        self.uuid = uuid
        self.name = name
        self.detail = detail
        self.vm_name = vm_name
        self.vm_uuid = vm_uuid
        self.consistent_type = consistent_type

    @event_enclosure
    def vm_snapshot_event_splicing(self, event_name):
        vm_snapshot_data = {
            "vm_id": self.vm_uuid,
            "vm_snapshot_name": self.name,
            "vm_snapshot_id": self.uuid,
            "vm_snapshot_consistent_type": self.consistent_type,
        }
        message_arg = {"vm_name": self.vm_name, "vm_snapshot_name": self.name}
        resources = {self.vm_uuid: KVM_VM}
        if self.detail is None:
            self.detail = {"zh_CN": "None", "en_US": "None"}
        return build_event(
            event_name=event_name,
            user=self.user_name,
            user_role=self.user_role,
            data=vm_snapshot_data,
            resources=resources,
            detail=self.detail,
            event_mod=i18n.event_message.get(event_name),
            **message_arg,
        )

    @event_enclosure
    def event_vm_snapshot_create(self):
        return self.vm_snapshot_event_splicing(EVENT_CREATE_VM_SNAPSHOT)
