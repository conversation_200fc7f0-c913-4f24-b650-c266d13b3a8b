# Copyright (c) 2013-2025, SMARTX
# All rights reserved.

import tabulate


def reset_elf_volume_mounting(args):
    from smartx_app.elf.common.resources.volume_vm_relationship import VolVMRelation

    VolVMRelation.ensure_vol_mounting(force=True)


def sync_elf_volume_template_unique_size(args):
    from smartx_app.elf.job_center.scheduler.volume_template import VolumeTemplateUniqueSizeUpdater

    VolumeTemplateUniqueSizeUpdater().run()


def show_volume_properties(args):
    volume_uuid = args.volume_uuid

    volume = _load_volume(volume_uuid)
    if not volume:
        print("Volume does not exist")
        return

    properties, label = volume.show_volume_properties()
    print(f"Volume properties={properties}")
    print(f"Lun labels={label}")


def update_volume_properties(args):
    from smartx_app.elf.common import constants

    volume_uuid = args.volume_uuid
    vendor = args.vendor
    product = args.product
    serial = args.serial
    wwn = args.wwn

    volume = _load_volume(volume_uuid)
    if not volume:
        print("Volume does not exist")
        return

    if volume.volume.type == constants.KVM_VOL:
        print("Updating properties is not supported for legacy NFS volumes")
        return

    vms_mounting_the_volume = volume.get_mounting_vms()
    active_vms = [vm for vm in vms_mounting_the_volume if vm["status"] in constants.VM_ACTIVE_STATES]
    if active_vms:
        table_headers = ("UUID", "Name", "Status")
        table_content = [(vm["uuid"], vm["vm_name"], vm["status"]) for vm in active_vms]
        print("Update failed!")
        print("Cannot change properties for the volume because the following VMs mounting the volume are active:")
        print(tabulate.tabulate(table_content, table_headers, tablefmt="grid"))
        return

    volume.update_iscsi_volume_properties(vendor, product, serial, wwn)
    print("Update succeed")
    new_properties, new_labels = _load_volume(volume_uuid).show_volume_properties()
    print(f"New volume properties={new_properties}")
    print(f"New LUN labels={new_labels}")


def _load_volume(volume_uuid):
    from smartx_app.elf.common.resource_wrappers import volume_wrapper
    from smartx_app.elf.common.resources import base
    from smartx_proto.errors import pyerror_pb2 as py_error

    try:
        volume = volume_wrapper.VolumeWrapper.load(volume_uuid)
    except base.ResourceException as e:
        if e.user_code == py_error.VOLUME_NOT_FOUND:
            return None
        else:
            raise

    return volume
