# Copyright (c) 2024, SMARTX
# All rights reserved.

import logging

from job_center.common import init_job
from smartx_app.elf.common import constants
from smartx_app.elf.common.config import platform

_LOG_PREFIX = "[post_upgrade_cluster]"


def execute_without_exception(func, args=(), kwargs=None):
    if kwargs is None:
        kwargs = {}

    # noinspection PyBroadException
    try:
        return func(*args, **kwargs)
    except Exception:
        logging.exception(f"{_LOG_PREFIX}An exception occurred while executing `{func.__name__}`:")


def execute_propagate_exception(func, args=(), kwargs=None):
    """
    Upgrading a cluster already supports breakpoint retries,
    and we allow errors to be raised to interrupt the cluster upgrade
    if we want the handle to be completed before the cluster is upgraded.
    """
    if kwargs is None:
        kwargs = {}

    # noinspection PyBroadException
    try:
        return func(*args, **kwargs)
    except Exception:
        logging.exception(f"{_LOG_PREFIX}An exception occurred while executing `{func.__name__}`:")
        raise


def update_current_node(_):
    """Update operations executed on the node during the post-phase, affecting only the node itself."""
    # noinspection PyBroadException
    execute_without_exception(_update_current_node)

    # ================================================================
    # Always before the end of the job, to avoid the risk of
    # interrupting the other upgrade process.
    # ================================================================
    execute_propagate_exception(_update_node_throw_exception)


def update_cluster(_):
    """Update operations executed on the node during the post-phase, affecting the cluster."""
    execute_without_exception(_update_cluster)

    # ================================================================
    # Always before the end of the job, to avoid the risk of
    # interrupting the other upgrade process.
    # ================================================================
    execute_propagate_exception(_update_cluster_throw_exception)


# In _update_current_node, please wrap the executed function `func`
# with execute_without_exception(func, args, kwargs) to avoid
# interrupting subsequent operations.
def _update_current_node():  # pragma: no cover
    execute_without_exception(setup_cluster_capabilities)
    execute_without_exception(insert_vm_additional_info)
    execute_without_exception(add_is_amd_vendor_for_hygon_cpu)


def _update_node_throw_exception():  # pragma: no cover
    add_scsi_info_to_volumes()


# In _update_cluster, please wrap the executed function `func`
# with execute_without_exception(func, args, kwargs) to avoid
# interrupting subsequent operations.
def _update_cluster():
    execute_without_exception(clean_residual_vm_configurations)
    execute_without_exception(add_volume_uuid_to_cluster_vms)
    execute_without_exception(add_cpu_exclusive_to_cluster_vms)
    execute_without_exception(add_nics_link_to_vms)
    execute_without_exception(fix_storage_policy_uuid_on_vm_snapshots)
    execute_without_exception(add_cpu_qos_to_cluster_vms)
    execute_without_exception(setup_default_volume_encryption_algorithm)
    execute_without_exception(remove_cloud_init_supported_from_cluster_vms)
    execute_without_exception(set_default_disk_quota_policy)
    execute_without_exception(add_anti_malware_to_vms)
    execute_without_exception(set_default_migratable)
    execute_without_exception(set_default_local_ha_policy)
    execute_without_exception(set_default_placement_group_availability_zone)


def _update_cluster_throw_exception():
    convert_and_replace_nfs_image()
    fix_invalid_host_ha_config()
    sync_volume_properties_to_zbs_vol()
    ensure_volume_properties_for_boost()


def setup_cluster_capabilities():  # pragma: no cover
    from smartx_app.elf.common.resources import cluster_capability

    cluster_capability.initialize()
    cluster_capability.ClusterCapability.update_on_demand()
    cluster_capability.HACapability.update_on_demand()


@init_job.call_once_per_cluster("fix_storage_policy_uuid_on_vm_snapshots")
def fix_storage_policy_uuid_on_vm_snapshots():
    from smartx_app.elf.common.utils.upgrade_related import vm_snapshot

    vm_snapshot.fix_storage_policy_uuid_on_vm_snapshots()


@init_job.call_once_per_cluster("clean_residual_vm_configurations")
def clean_residual_vm_configurations():
    from smartx_app.elf.common.resource_wrappers import vmtools_wrapper

    vmtools_wrapper.clean_residual_vm_configurations()


@init_job.call_once_per_cluster("add_volume_uuid_to_cluster_vms")
def add_volume_uuid_to_cluster_vms():
    from smartx_app.elf.common.utils.upgrade_related import vm

    vm.add_volume_uuid_to_cluster_vms()


@init_job.call_once_per_cluster("add_anti_malware_to_vms")
def add_anti_malware_to_vms():
    from smartx_app.elf.common.utils.upgrade_related import vm

    vm.add_anti_malware()


@init_job.call_once_per_cluster("add_cpu_exclusive_to_cluster_vms")
def add_cpu_exclusive_to_cluster_vms():
    from smartx_app.elf.common.utils.upgrade_related import vm

    vm.add_cpu_exclusive()


@init_job.call_once_per_cluster("add_cpu_qos_to_cluster_vms")
def add_cpu_qos_to_cluster_vms():
    from smartx_app.elf.common.utils.upgrade_related import vm

    vm.add_cpu_qos()


@init_job.call_once_per_cluster("add_nics_link_to_vms")
def add_nics_link_to_vms():
    from smartx_app.elf.common.utils.upgrade_related import vm

    vm.add_nics_link_to_vms()


@init_job.call_once_per_node("add_scsi_info_to_volumes_after_cluster_upgrade")
def add_scsi_info_to_volumes():
    from smartx_app.elf.job_center.hook import scsi_info_compatible_helper

    scsi_info_compatible_helper.SCSICompatibleInfoUpdateHelper().add_scsi_info_to_volumes()


@init_job.call_once_per_node("is_amd_vendor_for_hygon_cpu")
def add_is_amd_vendor_for_hygon_cpu():
    from smartx_app.elf.common.utils.upgrade_related import vm

    vm.add_is_amd_vendor_for_hygon_cpu(constants.CURRENT_NODE_IP)


def convert_and_replace_nfs_image():
    if constants.ELF_PRODUCT_NAME == constants.ELF_PRODUCT_NAME_SMARTX and platform.is_in_kvm_or_san():
        _convert_and_replace_nfs_image()


@init_job.call_once_per_cluster("convert_and_replace_nfs_image")
def _convert_and_replace_nfs_image():
    from smartx_app.elf.common.utils import zbs_iscsi
    from smartx_app.elf.common.utils.upgrade_related import image
    from zbs.nfs import client as zbs_nfs_client

    iscsi_client = zbs_iscsi.ZbsClientWrapper()
    nfs_client = zbs_nfs_client.ZbsNFS()
    image.batch_convert_to_lun(iscsi_client, nfs_client)
    image.batch_replace_resource_image_path(nfs_client)


@init_job.call_once_per_cluster("setup_default_volume_encryption_algorithm")
def setup_default_volume_encryption_algorithm():
    from smartx_app.elf.common.utils.upgrade_related import volume

    volume.setup_default_volume_encryption_algorithm()


@init_job.call_once_per_cluster("remove_cloud_init_supported_from_cluster_vms")
def remove_cloud_init_supported_from_cluster_vms():
    from smartx_app.elf.common.utils.upgrade_related import vm

    vm.remove_cloud_init_supported()


@init_job.call_once_per_node("insert_vm_additional_info")
def insert_vm_additional_info():  # pragma: no cover
    from smartx_app.elf.common.utils.upgrade_related import vm

    vm.add_vm_additional_info(constants.CURRENT_NODE_IP)


@init_job.call_once_per_cluster("fix_invalid_host_ha_config")
def fix_invalid_host_ha_config():
    from smartx_app.elf.common.utils.upgrade_related import ha

    ha.fix_invalid_host_ha_config()


@init_job.call_once_per_cluster("sync_volume_properties_to_zbs_vol")
def sync_volume_properties_to_zbs_vol():  # pragma: no cover
    from smartx_app.elf.common.utils.upgrade_related import vm, volume

    # We only care about the boost status of the cluster
    # when it is upgraded for the first time.
    # If it is switch to boost and upgraded next time,
    # there is no need to call it again.
    if not platform.boost_enabled():
        vm.sync_disk_serial_to_volume()

        # We will first set the serial for all volumes mounted on the VM,
        # then fill in the serial numbers of the remaining unmounted vol
        volume.ensure_vol_serial()
        volume.sync_volume_properties_to_zbs_vol()


@init_job.call_once_per_cluster("set_default_disk_quota_policy")
def set_default_disk_quota_policy():
    from smartx_app.elf.common.utils.upgrade_related import vm

    vm.set_default_disk_quota_policy()


@init_job.call_once_per_cluster("ensure_volume_properties_for_boost")
def ensure_volume_properties_for_boost():  # pragma: no cover
    from smartx_app.elf.common.utils.upgrade_related import volume

    if not platform.boost_enabled():
        return

    volume.ensure_volume_properties_for_boost()


@init_job.call_once_per_cluster("set_default_migratable")
def set_default_migratable():
    from smartx_app.elf.common.utils.upgrade_related import vm

    vm.set_default_migratable()


@init_job.call_once_per_cluster("set_default_local_ha_policy")
def set_default_local_ha_policy():
    from smartx_app.elf.common.utils.upgrade_related import ha

    ha.set_default_local_ha_policy()


@init_job.call_once_per_cluster("set_default_placement_group_availability_zone")
def set_default_placement_group_availability_zone():
    from smartx_app.elf.common.utils.upgrade_related import placement_group

    placement_group.set_default_placement_group_availability_zone()
