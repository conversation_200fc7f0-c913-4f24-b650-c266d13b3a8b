# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
import copy
import io
import sys
from unittest import mock
import uuid

import pytest

from common.mongo.db import mongodb
from smartx_app.elf.cmd import main
from smartx_app.elf.common import constants
from smartx_app.elf.common.resources import cluster_capability, storage_policy
from smartx_app.elf.common.utils import cpu_pin as cpu_pin_util


def test_nested_kvm_module_loaded():
    mock_open = mock.mock_open(read_data="Y")
    mock_get_kvm_module_name = mock.Mock()
    mock_get_kvm_module_name.return_value = "kvm_intel"
    with (
        mock.patch("builtins.open", mock_open),
        mock.patch("smartx_app.elf.common.utils.virtualization.get_kvm_module_name", mock_get_kvm_module_name),
    ):
        from smartx_app.elf.cmd.main import _nested_kvm_module_loaded

        assert _nested_kvm_module_loaded()

    mock_open = mock.mock_open(read_data="N")
    with (
        mock.patch("builtins.open", mock_open),
        mock.patch("smartx_app.elf.common.utils.virtualization.get_kvm_module_name", mock_get_kvm_module_name),
    ):
        from smartx_app.elf.cmd.main import _nested_kvm_module_loaded

        assert not _nested_kvm_module_loaded()

    mock_open = mock.mock_open(read_data="1")
    with (
        mock.patch("builtins.open", mock_open),
        mock.patch("smartx_app.elf.common.utils.virtualization.get_kvm_module_name", mock_get_kvm_module_name),
    ):
        from smartx_app.elf.cmd.main import _nested_kvm_module_loaded

        assert _nested_kvm_module_loaded()

    mock_open = mock.mock_open(read_data="0")
    with (
        mock.patch("builtins.open", mock_open),
        mock.patch("smartx_app.elf.common.utils.virtualization.get_kvm_module_name", mock_get_kvm_module_name),
    ):
        from smartx_app.elf.cmd.main import _nested_kvm_module_loaded

        assert not _nested_kvm_module_loaded()

    mock_open = mock.mock_open(read_data="somethingnotsupported")
    with (
        mock.patch("builtins.open", mock_open),
        mock.patch("smartx_app.elf.common.utils.virtualization.get_kvm_module_name", mock_get_kvm_module_name),
    ):
        from smartx_app.elf.cmd.main import _nested_kvm_module_loaded

        assert not _nested_kvm_module_loaded()


def test_module_enable_file():
    with mock.patch("builtins.open") as mock_open:
        from smartx_app.elf.cmd.main import _module_enable_file

        _module_enable_file()
        assert mock_open.called


def test_module_disable_file():
    with mock.patch("builtins.open") as mock_open:
        from smartx_app.elf.cmd.main import _module_disable_file

        _module_disable_file()
        assert mock_open.called


def test_execute_kvm_module_unload():
    with mock.patch("smartx_app.elf.cmd.main.execute") as mock_execute:
        from smartx_app.elf.cmd.main import execute_kvm_module_unload

        mock_execute.return_value = [0, "", ""]
        assert execute_kvm_module_unload()

        mock_execute.return_value = [-1, "", "cmd error"]
        assert not execute_kvm_module_unload()


def test_execute_kvm_module_load():
    with mock.patch("smartx_app.elf.cmd.main.execute") as mock_execute:
        from smartx_app.elf.cmd.main import execute_kvm_module_load

        mock_execute.return_value = [0, "", ""]
        assert execute_kvm_module_load()

        mock_execute.return_value = [-1, "", "cmd error"]
        assert not execute_kvm_module_load()


def test_elf_nest_kvm_module_load():
    with (
        mock.patch("smartx_app.elf.cmd.main._nested_kvm_module_loaded") as mock_nested_kvm_module_loaded,
        mock.patch("smartx_app.elf.cmd.main.execute_kvm_module_unload") as mock_execute_kvm_module_unload,
        mock.patch("smartx_app.elf.cmd.main.execute_kvm_module_load") as mock_execute_kvm_module_load,
        mock.patch("smartx_app.elf.cmd.main._module_enable_file") as mock_module_enable_file,
        mock.patch("smartx_app.elf.common.utils.virtualization.is_x86_64") as mock_is_x86_64,
        mock.patch("smartx_app.elf.common.utils.virtualization.has_active_domains") as has_active_domains_mock,
    ):
        from smartx_app.elf.cmd.main import elf_nest_kvm_module_load, load_kvm_module_name

        mock_is_x86_64.return_value = True
        has_active_domains_mock.return_value = False

        load_kvm_module_name()

        # 1. Test module is loaded
        mock_nested_kvm_module_loaded.return_value = True
        tmp_captured_output = io.StringIO()
        sys.stdout = tmp_captured_output
        elf_nest_kvm_module_load(None)
        sys.stdout = sys.__stdout__
        assert mock_module_enable_file.called  # Ensure the file is configured correctly
        assert "nested module already loaded\n" in tmp_captured_output.getvalue()
        assert not mock_execute_kvm_module_unload.called
        assert not mock_execute_kvm_module_load.called

        # 2. Test module is not loaded
        mock_nested_kvm_module_loaded.side_effect = [False, True]
        mock_execute_kvm_module_unload.return_value = True
        tmp_captured_output = io.StringIO()
        sys.stdout = tmp_captured_output
        elf_nest_kvm_module_load(None)
        sys.stdout = sys.__stdout__
        assert "with nested options is loaded\n" in tmp_captured_output.getvalue()
        assert mock_execute_kvm_module_load.called
        assert mock_module_enable_file.called

        # 3. Test module is not loaded, but there are active domain
        mock_nested_kvm_module_loaded.reset_mock()
        mock_execute_kvm_module_unload.reset_mock()
        mock_execute_kvm_module_load.reset_mock()
        mock_nested_kvm_module_loaded.side_effect = None
        mock_nested_kvm_module_loaded.return_value = False
        has_active_domains_mock.return_value = True
        mock_execute_kvm_module_unload.return_value = True
        mock_execute_kvm_module_load.return_value = True
        tmp_captured_output = io.StringIO()
        sys.stdout = tmp_captured_output
        elf_nest_kvm_module_load(None)
        sys.stdout = sys.__stdout__
        assert mock_nested_kvm_module_loaded.called
        assert has_active_domains_mock.called
        assert "Active domains exist, cannot operate" in tmp_captured_output.getvalue()
        assert not mock_execute_kvm_module_unload.called
        assert not mock_execute_kvm_module_load.called


def test_elf_nest_kvm_module_show():
    with (
        mock.patch("smartx_app.elf.cmd.main._nested_kvm_module_loaded") as mock_nested_kvm_module_loaded,
        mock.patch("smartx_app.elf.common.utils.virtualization.is_x86_64") as mock_is_x86_64,
    ):
        from smartx_app.elf.cmd.main import elf_nest_kvm_module_show, load_kvm_module_name

        mock_is_x86_64.return_value = True

        load_kvm_module_name()

        mock_nested_kvm_module_loaded.return_value = True
        tmp_captured_output = io.StringIO()
        sys.stdout = tmp_captured_output
        elf_nest_kvm_module_show(None)
        sys.stdout = sys.__stdout__
        assert "nested module already loaded.\n" in tmp_captured_output.getvalue()

        mock_nested_kvm_module_loaded.return_value = False
        tmp_captured_output = io.StringIO()
        sys.stdout = tmp_captured_output
        elf_nest_kvm_module_show(None)
        sys.stdout = sys.__stdout__
        assert "nested module is not loaded.\n" in tmp_captured_output.getvalue()


def test_elf_nest_kvm_module_disable():
    with (
        mock.patch("smartx_app.elf.cmd.main._nested_kvm_module_loaded") as mock_nested_kvm_module_loaded,
        mock.patch("smartx_app.elf.cmd.main.execute_kvm_module_unload") as mock_execute_kvm_module_unload,
        mock.patch("smartx_app.elf.cmd.main.execute_kvm_module_load") as mock_execute_kvm_module_load,
        mock.patch("smartx_app.elf.cmd.main._module_disable_file") as mock_module_disable_file,
        mock.patch("smartx_app.elf.common.utils.virtualization.is_x86_64") as mock_is_x86_64,
        mock.patch("smartx_app.elf.common.utils.virtualization.has_active_domains") as has_active_domains_mock,
    ):
        from smartx_app.elf.cmd.main import elf_nest_kvm_module_disable, load_kvm_module_name

        mock_is_x86_64.return_value = True
        has_active_domains_mock.return_value = False

        load_kvm_module_name()

        # 1. Test module is loaded
        mock_nested_kvm_module_loaded.return_value = False
        tmp_captured_output = io.StringIO()
        sys.stdout = tmp_captured_output
        elf_nest_kvm_module_disable(None)
        sys.stdout = sys.__stdout__
        assert mock_module_disable_file.called  # Ensure the file is configured correctly
        assert "nested module is not loaded, don't need disable.\n" in tmp_captured_output.getvalue()
        assert not mock_execute_kvm_module_unload.called
        assert not mock_execute_kvm_module_load.called

        # 2. Test module is not loaded, successfully disabled
        mock_nested_kvm_module_loaded.side_effect = [True, False]
        mock_execute_kvm_module_unload.return_value = True
        tmp_captured_output = io.StringIO()
        sys.stdout = tmp_captured_output
        elf_nest_kvm_module_disable(None)
        sys.stdout = sys.__stdout__
        assert "load with nested option disabled\n" in tmp_captured_output.getvalue()
        assert mock_execute_kvm_module_load.called
        assert mock_module_disable_file.called

        mock_nested_kvm_module_loaded.side_effect = None
        mock_nested_kvm_module_loaded.return_value = True
        mock_execute_kvm_module_unload.return_value = True
        tmp_captured_output = io.StringIO()
        sys.stdout = tmp_captured_output
        elf_nest_kvm_module_disable(None)
        sys.stdout = sys.__stdout__
        assert "module failed.\n" in tmp_captured_output.getvalue()
        assert mock_execute_kvm_module_load.called
        assert mock_module_disable_file.called

        # 3. Test module is not loaded, but there are active domain
        mock_nested_kvm_module_loaded.return_value = True
        has_active_domains_mock.return_value = True
        mock_execute_kvm_module_unload.reset_mock()
        mock_execute_kvm_module_unload.return_value = True
        tmp_captured_output = io.StringIO()
        sys.stdout = tmp_captured_output
        elf_nest_kvm_module_disable(None)
        sys.stdout = sys.__stdout__
        assert has_active_domains_mock.called
        assert "Active domains exist, cannot operate" in tmp_captured_output.getvalue()
        assert not mock_execute_kvm_module_unload.called


def test_collect_assigned_but_unused_vfs(capsys):
    from smartx_app.elf.cmd import main as elf_cmd
    from smartx_app.elf.common.resource_wrappers import sriov
    from smartx_app.elf.common.resource_wrappers import vm_wrapper as vm
    from smartx_app.elf.common.resources import base as base_exception
    from smartx_proto.errors import pyerror_pb2 as py_error

    pf = sriov._PF(
        {
            "totalvfs": 32,
            "numvfs": 4,
            "vfs": [
                {"index": 0, "domain": "0000", "bus": "86", "slot": "00", "function": "2"},
                {"index": 1, "domain": "0000", "bus": "86", "slot": "00", "function": "3"},
            ],
            "assigned_vfs": [
                {"index": 0, "assign_id": "vm-uuid_52:54:00:20:56:4e"},
                {"index": 1, "assign_id": "vm-uuid_52:54:00:20:56:4f"},
                {"index": 2, "assign_id": "vvvv_xxxx"},
            ],
            "uuid": "pf_id",
            "name": "eth1",
        }
    )
    with (
        mock.patch(
            "smartx_app.elf.common.resource_wrappers.sriov.get_assigned_but_unused_vfs"
        ) as mock_get_assigned_but_unused_vfs,
        mock.patch.object(vm.VMWrapper, "load") as mock_load,
    ):
        mock_get_assigned_but_unused_vfs.return_value = {
            "host_uuid": {pf: {"vm_uuid": {"52:54:00:20:56:4e", "52:54:00:20:56:4f"}}}
        }
        mock_load.return_value = mock.Mock(vm_doc={"uuid": "vm_uuid", "vm_name": "test_sriov"})

        elf_cmd.collect_assigned_but_unused_vfs(None)
        out = capsys.readouterr().out
        assert "eth1" in out

        mock_load.side_effect = base_exception.ResourceException(user_code=py_error.VM_NOT_FOUND)
        elf_cmd.collect_assigned_but_unused_vfs(None)
        out = capsys.readouterr().out
        assert "eth1" in out


def test_release_assigned_but_unused_vfs():
    from smartx_app.elf.cmd import main as elf_cmd

    with mock.patch(
        "smartx_app.elf.common.resource_wrappers.sriov.release_assigned_but_unused_vfs"
    ) as mock_release_assigned_but_unused_vfs:
        args = mock.Mock(host_uuid="host_uuid", pf_uuid="pf_uuid", vm_uuid="vm_uuid", mac_address="52:54:00:20:56:4e")
        elf_cmd.release_assigned_but_unused_vfs(args)
        assert mock_release_assigned_but_unused_vfs.called


def test_init_licnese():
    from smartx_app.elf.cmd import main as elf_cmd
    from smartx_app.elf.common.resources import base
    from smartx_proto.errors import pyerror_pb2 as py_error

    # case 1
    with mock.patch(
        "smartx_app.elf.common.license.license_manager.global_smtx_elf_license_manager"
    ) as mock_license_manager:
        mock_license_manager.generate_temporary_certificate = mock.Mock()
        mock_license_manager.generate_temporary_certificate.return_value = True

        args = mock.Mock()
        elf_cmd.init_license(args)

        assert mock_license_manager.generate_temporary_certificate.called

    # case 2
    with mock.patch(
        "smartx_app.elf.common.license.license_manager.global_smtx_elf_license_manager"
    ) as mock_license_manager:
        mock_license_manager.generate_temporary_certificate = mock.Mock()
        mock_license_manager.generate_temporary_certificate.return_value = False

        args = mock.Mock()
        elf_cmd.init_license(args)

        assert mock_license_manager.generate_temporary_certificate.called

    # case 3
    with mock.patch(
        "smartx_app.elf.common.license.license_manager.global_smtx_elf_license_manager"
    ) as mock_license_manager:
        mock_license_manager.generate_temporary_certificate = mock.Mock()
        mock_license_manager.generate_temporary_certificate.side_effect = base.ResourceException(
            "no sign date", py_error.SMTX_ELF_LICENSE_GENERATE_FAILED_NO_SIGN_DATE
        )

        args = mock.Mock()

        with pytest.raises(base.ResourceException) as exc_info:
            elf_cmd.init_license(args)
        assert mock_license_manager.generate_temporary_certificate.called
        assert exc_info.value.user_code == py_error.SMTX_ELF_LICENSE_GENERATE_FAILED_NO_SIGN_DATE


def test_read_host_storage_heartbeat_info():
    from smartx_app.elf.monitor.test import elf_mock

    with elf_mock.mock_valid_cluster_uuid():
        from smartx_app.elf.cmd import main as elf_cmd
        from smartx_app.elf.common.utils import elf_iscsi
        from smartx_app.elf.monitor import storage_heartbeat

        with (
            mock.patch("smartx_app.elf.monitor.storage_dao.HostHBStorageDAO.load_storages") as mock_get_ha_config,
            mock.patch.object(elf_iscsi, "ISCSILunClient") as mock_elf_iscsi_client,
            mock.patch.object(storage_heartbeat, "global_storage_hb_manager"),
        ):
            args = mock.Mock(host_uuid="host_uuid")

            mock_iscsi = mock.Mock()
            mock_iscsi.block_size = 512
            mock_elf_iscsi_client.return_value = mock_iscsi

            mock_get_ha_config.return_value = {
                "storage1": {
                    storage_heartbeat.HB_ISCSI_STORE_KEY: "0.0.0.0:3260,iqn.2016-02.com.smartx:system:ha-test/2",
                    storage_heartbeat.HB_PARTITION_LBA_KEY: 0,
                    storage_heartbeat.HB_PARTITION_SIZE_KEY: 512,
                }
            }

            elf_cmd.read_host_storage_heartbeat_info(args)

            mock_iscsi.read.assert_called_with(block_index=0, read_len=512)


def test_set_vm_version():
    from common.http.restful_client import Client
    from smartx_app.elf.cmd import main

    with mock.patch.object(Client, "put") as put_mock:
        put_mock.return_value = {
            "ec": "EOK",
            "data": "job_id",
        }

        class Args:
            def __init__(self):
                self.vm_uuid = "vm_uuid"
                self.vm_version = "vm_version"

        main.set_vm_version(Args())


def test_set_txqueuelen_for_vm():
    from smartx_app.elf.cmd import main

    class Args:
        def __init__(self):
            self.vm_uuid = "vm_uuid"
            self.txqueuelen = 1500

    with mock.patch("smartx_app.elf.cmd.vm.set_txqueuelen_for_vm") as mock_set_txqueuelen_for_vm:
        mock_set_txqueuelen_for_vm.return_value = (True, "test-job-id", [])
        main.set_txqueuelen_for_vm(Args())

        assert mock_set_txqueuelen_for_vm.called


def test_show_txqueuelen_for_vm():
    from smartx_app.elf.cmd import main

    class Args:
        def __init__(self):
            self.vm_uuid = "vm_uuid"

    with mock.patch("smartx_app.elf.cmd.vm.show_txqueuelen_for_vm") as mock_show_txqueuelen_for_vm:
        main.show_txqueuelen_for_vm(Args())

        assert mock_show_txqueuelen_for_vm.called


def test_set_txqueuelen_for_cluster():
    from smartx_app.elf.cmd import main

    class Args:
        def __init__(self, txqueuelen):
            self.txqueuelen = txqueuelen

    with mock.patch.object(cluster_capability.NicTxqueuelenCapability, "set") as set_mock:
        main.set_txqueuelen_for_cluster(Args(1500))

        assert set_mock.called

    with mock.patch.object(cluster_capability.NicTxqueuelenCapability, "set") as set_mock:
        main.set_txqueuelen_for_cluster(Args(100))

        assert not set_mock.called


def test_show_txqueuelen_for_cluster():
    from smartx_app.elf.cmd import main

    with mock.patch.object(cluster_capability.NicTxqueuelenCapability, "get") as get_mock:
        main.show_txqueuelen_for_cluster(None)

        assert get_mock.called


def test_sync_txqueuelen_for_cluster():
    from smartx_app.elf.cmd import main

    with mock.patch("smartx_app.elf.cmd.vm.sync_txqueuelen_for_cluster") as sync_txqueuelen_for_cluster_mock:
        sync_txqueuelen_for_cluster_mock.return_value = (True, "test_job_id", [])

        main.sync_txqueuelen_for_cluster(None)

        assert sync_txqueuelen_for_cluster_mock.called


def test_enable_cpu_pin():
    from smartx_app.elf.cmd import main
    from smartx_app.elf.common.resource_wrappers import vm_additional_info_manager, vm_wrapper

    class Args:
        def __init__(self):
            self.vm_uuid = "vm_uuid"
            self.pcpu_list = "1,5-7"

    with (
        mock.patch.object(vm_additional_info_manager.VMAdditionalInfoWrapper, "add") as mock_add,
        mock.patch.object(vm_wrapper.VMWrapper, "load") as mock_load,
    ):
        mock_load.return_value = mock.Mock(vm_doc={"vcpu": 4})
        main.cpu_pin(Args())

        assert mock_add.called
        assert mock_load.called


def test_disable_cpu_pin():
    from smartx_app.elf.cmd import main
    from smartx_app.elf.common.resource_wrappers import vm_additional_info_manager

    class Args:
        def __init__(self):
            self.vm_uuid = "vm_uuid"
            self.pcpu_list = ""

    with mock.patch.object(vm_additional_info_manager.VMAdditionalInfoWrapper, "remove") as mock_remove:
        main.cpu_pin(Args())
        assert mock_remove.called


def test_show_cpu_pin():
    from smartx_app.elf.cmd import main
    from smartx_app.elf.common.resource_wrappers import vm_additional_info_manager, vm_wrapper

    class Args:
        def __init__(self):
            self.vm_uuid = "vm_uuid"

    with (
        mock.patch.object(vm_additional_info_manager.VMAdditionalInfoWrapper, "load") as mock_load_vm_additional_info,
        mock.patch.object(vm_wrapper.VMWrapper, "load") as mock_load_vm,
        mock.patch("smartx_app.common.node.db.get_host_ips") as mock_get_host_ips,
    ):
        mock_load_vm_additional_info.return_value = {
            "manual_pin_pcpus": [0, 1],
        }
        vm_doc = {
            "vm_name": "vm-test",
            "vcpu": 2,
            "memory": 4 * 1024 * 1024 * 1024,
            "cpu_exclusive": {
                "expected_enabled": True,
                "actual_enabled": True,
                "vcpu_to_pcpu": {"0": "0", "1": "1"},
            },
            "node_ip": "********",
        }
        mock_load_vm.return_value = mock.Mock(vm_doc=vm_doc)
        mock_get_host_ips.return_value = [
            {"data_ip": "********", "host_uuid": "host_uuid1", "management_ip": "***********"}
        ]

        main.show_cpu_pin(Args())
        assert mock_load_vm.called
        assert mock_load_vm_additional_info.called
        assert mock_get_host_ips.called


def test_show_cpu_pin_on_cluster():
    from smartx_app.elf.cmd import main

    with mock.patch.object(cpu_pin_util.CPUPinController, "show_cpu_pin_on_cluster") as mock_show_cpu_pin:
        main.show_cpu_pin_on_cluster(None)
        assert mock_show_cpu_pin.called


# make lint happy
def test_create_parser():
    from smartx_app.elf.cmd import main

    main.create_parser()


def test_init_elf_cmd_log():
    from smartx_app.elf.cmd import main

    with (
        mock.patch("logging.getLogger") as mock_get_logger,
        mock.patch("logging.handlers.RotatingFileHandler") as mock_rfh,
        mock.patch("logging.StreamHandler") as mock_sh,
    ):
        mock_get_logger.return_value = mock.Mock()

        main.init_elf_cmd_log(logfile="logfile")
        assert mock_rfh.called
        assert not mock_sh.called

        mock_rfh.reset_mock()
        mock_sh.reset_mock()

        main.init_elf_cmd_log()
        assert not mock_rfh.called
        assert mock_sh.called


def test_remove_node():
    from smartx_app.elf.cmd import main

    with mock.patch("smartx_app.elf.common.ha.config.remove_node_from_elf_cluster") as mock_remove_node:
        args = mock.Mock()
        args.host_uuid = "uuid1"

        main.remove_node(args)

        mock_remove_node.assert_called_with("uuid1")


def test_calc_image_md5():
    from smartx_app.elf.cmd import main
    from smartx_app.elf.http.common import upload

    class Args:
        def __init__(self):
            self.image_uuid = "image_uuid_xxx"

    with mock.patch.object(upload, "calc_image_md5", autospec=True) as mock_calc_image_md5:
        mock_calc_image_md5.return_value = "cur_md5"
        assert main._calc_image_md5(Args()) is None
        assert mock_calc_image_md5.called
        mock_calc_image_md5.assert_called_with("image_uuid_xxx")


def test_update_image_md5():
    from smartx_app.elf.cmd import main
    from smartx_app.elf.http.common import upload

    class Args:
        def __init__(self):
            self.image_uuid = "image_uuid_xxx"

    with mock.patch.object(upload, "update_image_md5", autospec=True) as mock_update_image_md5:
        mock_update_image_md5.return_value = True
        assert main._update_image_md5(Args()) is None
        assert mock_update_image_md5.called
        mock_update_image_md5.assert_called_with("image_uuid_xxx")


def test_get_by_zbs_volume_id():
    from smartx_app.elf.cmd import main

    iscsi_volume_uuid = str(uuid.uuid4())
    path = "iscsi://iqn.2016-02.com.smartx:system:zbs-iscsi-datastore-1672415428055x/246"
    zbs_volume_id = str(uuid.uuid4())

    iscsi_volume = {
        "uuid": iscsi_volume_uuid,
        "resource_state": constants.RESOURCE_IN_USE,
        "type": constants.KVM_VOL_ISCSI,
        "create_time": 1462338311,
        "path": path,
        "sharing": False,
        "storage_policy_uuid": storage_policy.StoragePolicy.DEFAULT_STORAGE_POLICY_UUID,
        "zbs_volume_id": zbs_volume_id,
        "super_type": constants.KVM_VOL_SUPER,
    }

    mongodb.resources.resource.insert(copy.deepcopy(iscsi_volume))

    args = mock.Mock(zbs_volume_id=zbs_volume_id)
    result = main.show_by_zbs_volume_id(args)
    for k, v in iscsi_volume.items():
        assert v == result[k]

    mongodb.resources.resource.remove({"uuid": iscsi_volume_uuid})


def test_list_encrypted_resources():
    from smartx_app.elf.cmd import main
    from smartx_app.elf.common.utils import resource as resource_utils

    with mock.patch.object(resource_utils, "list_encrypted_resources") as mock_list:
        encrypted_resources = [
            {
                "uuid": "uuid1",
                "name": "name1",
                "encryption_algorithm": resource_utils.constants.VOLUME_ENCRYPTION_ALG_STORAGE_AES256_CTR,
                "type": resource_utils.constants.KVM_VOL_ISCSI,
            },
            {
                "uuid": "uuid2",
                "name": "name2",
                "encryption_algorithm": resource_utils.constants.VOLUME_ENCRYPTION_ALG_STORAGE_AES256_CTR,
                "type": resource_utils.constants.KVM_VOL_ISCSI,
            },
        ]
        mock_list.side_effect = [
            encrypted_resources,
            encrypted_resources,
            [],
        ]

        main.list_encrypted_resources(mock.Mock(output_format=None))
        main.list_encrypted_resources(mock.Mock(output_format="json"))
        main.list_encrypted_resources(mock.Mock())


@mock.patch("smartx_app.elf.common.utils.volume_properties.get_acm_check_value")
@mock.patch("builtins.print")
def test_show_acm_volume_properties_check(mock_print, mock_get_acm_check_value):
    mock_get_acm_check_value.return_value = True
    main.show_acm_volume_properties_check(mock.Mock())
    mock_print.assert_called_with("Current check status=enable")

    mock_print.reset_mock()
    mock_get_acm_check_value.return_value = False
    main.show_acm_volume_properties_check(mock.Mock())
    mock_print.assert_called_with("Current check status=disable")


@mock.patch("smartx_app.elf.common.utils.volume_properties.update_acm_check_value")
def test_update_acm_volume_properties_check(mock_update_acm_check_value):
    args = mock.Mock()
    args.status = "enable"
    main.update_acm_volume_properties_check(args)
    mock_update_acm_check_value.assert_called_with(True)

    mock_update_acm_check_value.reset_mock()
    args.status = "disable"
    main.update_acm_volume_properties_check(args)
    mock_update_acm_check_value.assert_called_with(False)


def test_copy_volume_capability_cmd():
    import time

    from common.config import constant
    from common.mongo import db
    from smartx_app.elf.cmd.main import copy_volume_set_task_max_bps, copy_volume_set_total_tasks_max_bps
    from smartx_app.elf.common import constants as elf_common_constants

    collection = db.mongodb[constant.DEFAULT_DB_NAME][elf_common_constants.ELF_CLUSTER_CAPABILITIES_COLLECTIONS]

    collection.delete_many({})
    collection.insert_one(
        {
            "type": elf_common_constants.ELF_COPY_VOL_MAX_BITPS_TYPE,
            "total_tasks_max_bps": elf_common_constants.ELF_COPY_VOL_DEFAULT_TOTAL_TASKS_MAX_BITPS,
            "task_max_bps": elf_common_constants.ELF_COPY_VOL_DEFAULT_TASK_MAX_BITPS,
            "mtime": int(time.time()),
        }
    )
    # Test set_total_tasks_max_bps
    args = mock.Mock(value="500")
    copy_volume_set_total_tasks_max_bps(args)
    bps_value = 500 * (1 << 20) * 8
    result = collection.find_one({"type": elf_common_constants.ELF_COPY_VOL_MAX_BITPS_TYPE})
    assert result["total_tasks_max_bps"] == bps_value

    # Test set_total_tasks_max_bps less than task_max_bps
    args = mock.Mock(value="100")
    copy_volume_set_total_tasks_max_bps(args)
    result = collection.find_one({"type": elf_common_constants.ELF_COPY_VOL_MAX_BITPS_TYPE})
    assert result["total_tasks_max_bps"] == bps_value  # Should remain unchanged

    # Test set_task_max_bps
    args = mock.Mock(value="50")
    copy_volume_set_task_max_bps(args)
    bps_value = 50 * (1 << 20) * 8
    result = collection.find_one({"type": elf_common_constants.ELF_COPY_VOL_MAX_BITPS_TYPE})
    assert result["task_max_bps"] == bps_value

    # Test task_max_bps exceeds total_tasks_max_bps
    args = mock.Mock(value="1000")
    copy_volume_set_task_max_bps(args)
    result = collection.find_one({"type": elf_common_constants.ELF_COPY_VOL_MAX_BITPS_TYPE})
    assert result["task_max_bps"] == bps_value  # Should remain unchanged

    # Test enable/disable
    main.copy_volume_enable(None)
    result = list(collection.find({"type": elf_common_constants.ELF_COPY_VOL_MAX_BITPS_TYPE}))
    assert len(result) == 1
    assert result[0]["enabled"] is True

    main.copy_volume_disable(None)
    result = list(collection.find({"type": elf_common_constants.ELF_COPY_VOL_MAX_BITPS_TYPE}))
    assert len(result) == 1
    assert result[0]["enabled"] is False

    collection.delete_many({})


def test_copy_volume_show_capability():
    from io import StringIO
    import time

    from common.config import constant
    from common.mongo import db
    from smartx_app.elf.cmd.main import copy_volume_show_capability
    from smartx_app.elf.common import constants as elf_common_constants

    collection = db.mongodb[constant.DEFAULT_DB_NAME][elf_common_constants.ELF_CLUSTER_CAPABILITIES_COLLECTIONS]

    collection.delete_many({})

    collection.insert_one(
        {
            "type": elf_common_constants.ELF_COPY_VOL_MAX_BITPS_TYPE,
            "total_tasks_max_bps": 200 * (1 << 20) * 8,
            "task_max_bps": 50 * (1 << 20) * 8,
            "enabled": False,
            "mtime": int(time.time()),
        }
    )

    captured_output = StringIO()
    sys.stdout = captured_output

    # Test with mocked capabilities
    copy_volume_show_capability(None)

    sys.stdout = sys.__stdout__

    output = captured_output.getvalue()

    collection.delete_many({})

    assert "enabled: False" in output
    assert "total_tasks_max_bps: 200 MB/s" in output
    assert "task_max_bps: 50 MB/s" in output


def test_update_migration_caps_tls():
    from smartx_app.elf.cmd import main
    from smartx_app.elf.common.resources import cluster_capability

    with mock.patch.object(cluster_capability.MigrationClusterCapability, "load") as load:
        main.update_migration_caps_tls(True)
        assert load.return_value.update.called


def test_list_migration_caps():
    from smartx_app.elf.cmd import main
    from smartx_app.elf.common.resources import cluster_capability

    with mock.patch.object(cluster_capability.MigrationClusterCapability, "load") as load:
        main.list_migration_caps(None)
        assert load.called


def test_execute_guest_command_capability_cmd():
    import time

    from common.config import constant
    from common.mongo import db
    from smartx_app.elf.cmd.main import (
        execute_guest_command_disable,
        execute_guest_command_enable,
    )
    from smartx_app.elf.common import constants as elf_common_constants

    collection = db.mongodb[constant.DEFAULT_DB_NAME][elf_common_constants.ELF_CLUSTER_CAPABILITIES_COLLECTIONS]

    # Clean up any existing data
    collection.delete_many({"type": elf_common_constants.ELF_EXECUTE_GUEST_COMMAND_TYPE})

    # Insert initial test data
    collection.insert_one(
        {
            "type": elf_common_constants.ELF_EXECUTE_GUEST_COMMAND_TYPE,
            "enabled": False,
            "mtime": int(time.time()),
        }
    )

    # Test enable command
    execute_guest_command_enable(None)
    result = collection.find_one({"type": elf_common_constants.ELF_EXECUTE_GUEST_COMMAND_TYPE})
    assert result["enabled"] is True

    # Test disable command
    execute_guest_command_disable(None)
    result = collection.find_one({"type": elf_common_constants.ELF_EXECUTE_GUEST_COMMAND_TYPE})
    assert result["enabled"] is False

    # Clean up
    collection.delete_many({"type": elf_common_constants.ELF_EXECUTE_GUEST_COMMAND_TYPE})


def test_execute_guest_command_show_capability():
    from io import StringIO
    import time

    from common.config import constant
    from common.mongo import db
    from smartx_app.elf.cmd.main import execute_guest_command_show_capability
    from smartx_app.elf.common import constants as elf_common_constants

    collection = db.mongodb[constant.DEFAULT_DB_NAME][elf_common_constants.ELF_CLUSTER_CAPABILITIES_COLLECTIONS]

    # Clean up any existing data
    collection.delete_many({"type": elf_common_constants.ELF_EXECUTE_GUEST_COMMAND_TYPE})

    # Test with enabled capability
    collection.insert_one(
        {
            "type": elf_common_constants.ELF_EXECUTE_GUEST_COMMAND_TYPE,
            "enabled": True,
            "mtime": int(time.time()),
        }
    )

    captured_output = StringIO()
    sys.stdout = captured_output

    execute_guest_command_show_capability(None)

    sys.stdout = sys.__stdout__

    output = captured_output.getvalue()
    assert "enabled: True" in output

    # Clean up
    collection.delete_many({"type": elf_common_constants.ELF_EXECUTE_GUEST_COMMAND_TYPE})

    # Test with disabled capability
    collection.insert_one(
        {
            "type": elf_common_constants.ELF_EXECUTE_GUEST_COMMAND_TYPE,
            "enabled": False,
            "mtime": int(time.time()),
        }
    )

    captured_output = StringIO()
    sys.stdout = captured_output

    execute_guest_command_show_capability(None)

    sys.stdout = sys.__stdout__

    output = captured_output.getvalue()
    assert "enabled: False" in output

    # Clean up
    collection.delete_many({"type": elf_common_constants.ELF_EXECUTE_GUEST_COMMAND_TYPE})


def test_execute_guest_command_show_capability_with_default():
    from io import StringIO

    from smartx_app.elf.cmd.main import execute_guest_command_show_capability

    # Test when capability doesn't exist in database (should use default)
    with mock.patch(
        "smartx_app.elf.common.resources.cluster_capability.ExecuteGuestCommandCapability.query"
    ) as mock_query:
        mock_query.return_value = None

        captured_output = StringIO()
        sys.stdout = captured_output

        execute_guest_command_show_capability(None)

        sys.stdout = sys.__stdout__

        output = captured_output.getvalue()
        assert "enabled: False" in output  # Should show default value


def test_set_execute_guest_command_capability():
    from io import StringIO

    from smartx_app.elf.cmd.main import _set_execute_guest_command_capability
    from smartx_app.elf.common.resources import cluster_capability

    # Test successful update
    with mock.patch.object(cluster_capability.ExecuteGuestCommandCapability, "update") as mock_update:
        mock_update.return_value = True

        captured_output = StringIO()
        sys.stdout = captured_output

        _set_execute_guest_command_capability(True)

        sys.stdout = sys.__stdout__

        output = captured_output.getvalue()
        assert "Execute guest command capability enabled" in output
        mock_update.assert_called_once_with({"enabled": True})

    # Test failed update
    with mock.patch.object(cluster_capability.ExecuteGuestCommandCapability, "update") as mock_update:
        mock_update.return_value = False

        captured_output = StringIO()
        sys.stdout = captured_output

        _set_execute_guest_command_capability(False)

        sys.stdout = sys.__stdout__

        output = captured_output.getvalue()
        assert "Failed to update execute guest command capability" in output
        mock_update.assert_called_once_with({"enabled": False})
