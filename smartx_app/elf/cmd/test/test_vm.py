# Copyright (c) 2013-2025, elfvirt
# All rights reserved.
from unittest import mock
import uuid

from smartx_app.elf.cmd import vm


def test_set_txqueuelen_for_vm():
    with (
        mock.patch("common.http.restful_client.Client.post") as post_mock,
        mock.patch("smartx_app.elf.cmd.utils.wait_job") as wait_job_mock,
    ):
        job_id = str(uuid.uuid4())
        post_mock.return_value = {"ec": "EOK", "data": {"job_id": job_id}}
        wait_job_mock.return_value = (True, [])

        ok, ret_job_id, ec_list = vm.set_txqueuelen_for_vm("test-vm-uuid", 1500)

        assert post_mock.called
        assert wait_job_mock.called

        assert ok is True
        assert job_id == ret_job_id
        assert len(ec_list) == 0


def test_show_txqueuelen_for_vm():
    with mock.patch(
        "smartx_app.elf.common.utils.vnic.get_txqueuelen_config_for_vm"
    ) as get_txqueuelen_config_for_vm_mock:
        get_txqueuelen_config_for_vm_mock.return_value = (
            True,
            [
                {
                    "mac_address": "00:11:22:33:44:55",
                    "nic_type": "virtio",
                    "vm_expected_txqueuelen": 1500,
                    "cluster_expected_txqueuelen": 1200,
                    "actual_txqueuelen": 1200,
                    "vnet_name": "vnet100",
                },
                {
                    "mac_address": "00:11:22:33:44:56",
                    "nic_type": "sriov",
                    "vm_expected_txqueuelen": None,
                    "cluster_expected_txqueuelen": None,
                    "actual_txqueuelen": 1500,
                    "vnet_name": None,
                },
                {
                    "mac_address": "00:11:22:33:44:57",
                    "nic_type": "e1000",
                    "vm_expected_txqueuelen": 1500,
                    "cluster_expected_txqueuelen": 1200,
                    "actual_txqueuelen": None,
                    "vnet_name": None,
                },
            ],
        )

        vm.show_txqueuelen_for_vm("test-vm-uuid")

        assert get_txqueuelen_config_for_vm_mock.called


def test_sync_txqueuelen_for_cluster():
    with (
        mock.patch("smartx_app.elf.cmd.utils.wait_job") as wait_job_mock,
        mock.patch("smartx_app.elf.cmd.utils.submit_one_time_task") as submit_one_time_task_mock,
        mock.patch("smartx_app.common.node.db.get_host_ips") as get_host_ips_mock,
    ):
        get_host_ips_mock.return_value = [{"data_ip": "*******"}, {"data_ip": "*******"}]
        wait_job_mock.return_value = (True, [])
        submit_one_time_task_mock.return_value = "test_job_id"

        vm.sync_txqueuelen_for_cluster()

        assert wait_job_mock.called
        assert submit_one_time_task_mock.called
        assert get_host_ips_mock.called
