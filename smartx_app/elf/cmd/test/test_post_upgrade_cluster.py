# Copyright (c) 2024, SMARTX
# All rights reserved.
from unittest import mock

import pytest

from common.mongo.db import mongodb
from smartx_app.elf.cmd import post_upgrade_cluster
from smartx_app.elf.common import constants as elf_common_constants
from smartx_app.elf.common.config import platform
from smartx_app.elf.common.utils import zbs_iscsi
from smartx_app.elf.common.utils.upgrade_related import image
from smartx_app.elf.job_center.hook import scsi_info_compatible_helper
from zbs.nfs import client as zbs_nfs_client


def test_update_current_node():
    with (
        mock.patch("smartx_app.elf.cmd.post_upgrade_cluster._update_current_node") as _update_current_node_mock,
        mock.patch(
            "smartx_app.elf.cmd.post_upgrade_cluster._update_node_throw_exception"
        ) as _update_node_throw_exception_mock,
    ):
        _update_current_node_mock.return_value = None
        _update_current_node_mock.configure_mock(**{"__name__": "_update_current_node_mock"})
        assert post_upgrade_cluster.update_current_node(None) is None
        assert _update_current_node_mock.called

        _update_current_node_mock.side_effect = ValueError()
        assert post_upgrade_cluster.update_current_node(None) is None
        assert _update_current_node_mock.called

        _update_node_throw_exception_mock.__name__ = ""
        _update_node_throw_exception_mock.side_effect = ValueError()
        with pytest.raises(ValueError):
            post_upgrade_cluster.update_current_node(None)


def test_update_cluster():
    with (
        mock.patch("smartx_app.elf.cmd.post_upgrade_cluster._update_cluster") as _update_cluster_mock,
        mock.patch(
            "smartx_app.elf.cmd.post_upgrade_cluster._update_cluster_throw_exception"
        ) as _update_cluster_throw_exception_mock,
    ):
        _update_cluster_mock.return_value = None
        _update_cluster_mock.configure_mock(**{"__name__": "_update_current_node_mock"})
        assert post_upgrade_cluster.update_cluster(None) is None
        assert _update_cluster_mock.called

        _update_cluster_mock.side_effect = ValueError()
        assert post_upgrade_cluster.update_cluster(None) is None
        assert _update_cluster_mock.called

        _update_cluster_throw_exception_mock.__name__ = ""
        _update_cluster_throw_exception_mock.side_effect = ValueError()
        with pytest.raises(ValueError):
            post_upgrade_cluster.update_cluster(None)


def test_execute_without_exception():
    paras = []

    def _x():
        pass

    def _xx(a, b=None):
        paras.extend([a, b])

    def _e():
        raise RuntimeError()

    assert post_upgrade_cluster.execute_without_exception(_x) is None
    assert post_upgrade_cluster.execute_without_exception(_e) is None
    assert len(paras) == 0

    assert post_upgrade_cluster.execute_without_exception(_xx, ("1",), {"b": "2"}) is None
    assert len(paras) == 2
    assert paras[0] == "1"
    assert paras[1] == "2"

    with pytest.raises(RuntimeError):
        post_upgrade_cluster.execute_propagate_exception(_e)


@pytest.fixture
def setup_job_mark_coll():
    init_job_mark_coll = mongodb.jobs.init_job_mark
    yield init_job_mark_coll
    init_job_mark_coll.drop()


@mock.patch(
    "smartx_app.elf.common.resource_wrappers.vmtools_wrapper.clean_residual_vm_configurations", return_value=None
)
def test_clean_residual_vm_configurations(clean_residual_vm_configurations_mock, setup_job_mark_coll):
    post_upgrade_cluster.clean_residual_vm_configurations()
    assert setup_job_mark_coll.find_one({"name": "clean_residual_vm_configurations"}) is not None
    assert clean_residual_vm_configurations_mock.called


@mock.patch("smartx_app.elf.common.utils.upgrade_related.vm.add_anti_malware", return_value=None)
def test_add_anti_malware_to_vms(add_anti_malware_mock, setup_job_mark_coll):
    post_upgrade_cluster.add_anti_malware_to_vms()
    assert setup_job_mark_coll.find_one({"name": "add_anti_malware_to_vms"}) is not None
    assert add_anti_malware_mock.called


@mock.patch.object(
    scsi_info_compatible_helper.SCSICompatibleInfoUpdateHelper, "add_scsi_info_to_volumes", return_value=None
)
def test_add_scsi_info_to_volumes(mock_add_scsi_info_to_volumes, setup_job_mark_coll):
    post_upgrade_cluster.add_scsi_info_to_volumes()

    assert setup_job_mark_coll.find_one({"name": "add_scsi_info_to_volumes_after_cluster_upgrade"}) is not None
    assert mock_add_scsi_info_to_volumes.called


@mock.patch("smartx_app.elf.common.utils.upgrade_related.vm.add_nics_link_to_vms", return_value=None)
def test_add_nics_link_to_vms(add_nics_link_to_vms_mock, setup_job_mark_coll):
    post_upgrade_cluster.add_nics_link_to_vms()

    assert setup_job_mark_coll.find_one({"name": "add_nics_link_to_vms"}) is not None
    assert add_nics_link_to_vms_mock.called


@mock.patch("smartx_app.elf.common.utils.upgrade_related.vm.add_cpu_qos", return_value=None)
def test_add_cpu_qos_to_cluster_vms(add_cpu_qos_mock, setup_job_mark_coll):
    post_upgrade_cluster.add_cpu_qos_to_cluster_vms()

    assert setup_job_mark_coll.find_one({"name": "add_cpu_qos_to_cluster_vms"}) is not None
    assert add_cpu_qos_mock.called


@mock.patch.object(elf_common_constants, "ELF_PRODUCT_NAME", new=elf_common_constants.ELF_PRODUCT_NAME_SMARTX)
@mock.patch.object(platform, "is_in_kvm_or_san", return_value=True)
@mock.patch.object(post_upgrade_cluster, "_convert_and_replace_nfs_image")
def test_call_convert_and_replace_nfs_image(mock_convert, mock_is_in_kvm_or_san):
    post_upgrade_cluster.convert_and_replace_nfs_image()

    assert mock_convert.called


@mock.patch.object(image, "batch_convert_to_lun")
@mock.patch.object(zbs_nfs_client, "ZbsNFS")
@mock.patch.object(zbs_iscsi, "ZbsClientWrapper")
def test_convert_and_replace_nfs_image(mock_zbs_iscsi, mock_zbs_nfs_client, mock_image, setup_job_mark_coll):
    post_upgrade_cluster._convert_and_replace_nfs_image()

    assert mock_zbs_iscsi.called
    assert mock_zbs_nfs_client.called
    assert setup_job_mark_coll.find_one({"name": "convert_and_replace_nfs_image"}) is not None
    mock_image.assert_called_once_with(mock_zbs_iscsi.return_value, mock_zbs_nfs_client.return_value)


@mock.patch(
    "smartx_app.elf.common.utils.upgrade_related.volume.setup_default_volume_encryption_algorithm", return_value=None
)
def test_setup_default_volume_encryption_algorithm(setup_default_volume_encryption_algorithm, setup_job_mark_coll):
    post_upgrade_cluster.setup_default_volume_encryption_algorithm()

    assert setup_job_mark_coll.find_one({"name": "setup_default_volume_encryption_algorithm"}) is not None
    assert setup_default_volume_encryption_algorithm.called


@mock.patch("smartx_app.elf.common.utils.upgrade_related.vm.remove_cloud_init_supported", return_value=None)
def test_remove_cloud_init_supported_from_cluster_vms(remove_cloud_init_supported_mock, setup_job_mark_coll):
    post_upgrade_cluster.remove_cloud_init_supported_from_cluster_vms()

    assert setup_job_mark_coll.find_one({"name": "remove_cloud_init_supported_from_cluster_vms"}) is not None
    assert remove_cloud_init_supported_mock.called


@mock.patch("smartx_app.elf.common.utils.upgrade_related.ha.fix_invalid_host_ha_config", return_value=None)
def test_fix_invalid_host_ha_config(fix_invalid_host_ha_config_mock, setup_job_mark_coll):
    post_upgrade_cluster.fix_invalid_host_ha_config()

    assert setup_job_mark_coll.find_one({"name": "fix_invalid_host_ha_config"}) is not None
    assert fix_invalid_host_ha_config_mock.called


@mock.patch.object(platform, "boost_enabled", return_value=False)
@mock.patch("smartx_app.elf.common.utils.upgrade_related.vm.sync_disk_serial_to_volume", return_value=None)
@mock.patch("smartx_app.elf.common.utils.upgrade_related.volume.ensure_vol_serial", return_value=None)
@mock.patch("smartx_app.elf.common.utils.upgrade_related.volume.sync_volume_properties_to_zbs_vol", return_value=None)
def test_sync_volume_properties_to_zbs_vol(
    sync_volume_properties_to_zbs_vol_mock,
    ensure_vol_serial_mock,
    sync_disk_serial_to_volume_mock,
    mock_boost_enabled,
    setup_job_mark_coll,
):
    post_upgrade_cluster.sync_volume_properties_to_zbs_vol()

    init_job_mark = setup_job_mark_coll.find_one({"name": "sync_volume_properties_to_zbs_vol"})
    assert init_job_mark
    assert init_job_mark["nodes"][0]["host_uuid"] == "random_node_in_cluster"

    assert sync_disk_serial_to_volume_mock.called
    assert ensure_vol_serial_mock.called
    assert sync_volume_properties_to_zbs_vol_mock.called
    assert mock_boost_enabled.called


@mock.patch("smartx_app.elf.common.utils.upgrade_related.vm.set_default_disk_quota_policy", return_value=None)
def test_set_default_disk_quota_policy(set_default_disk_quota_policy_mock, setup_job_mark_coll):
    post_upgrade_cluster.set_default_disk_quota_policy()

    assert setup_job_mark_coll.find_one({"name": "set_default_disk_quota_policy"}) is not None
    assert set_default_disk_quota_policy_mock.called


@mock.patch("smartx_app.elf.common.utils.upgrade_related.vm.set_default_migratable", return_value=None)
def test_set_default_migratable(set_default_migratable_mock, setup_job_mark_coll):
    post_upgrade_cluster.set_default_migratable()
    assert setup_job_mark_coll.find_one({"name": "set_default_migratable"}) is not None
    assert set_default_migratable_mock.called


@mock.patch("smartx_app.elf.common.utils.upgrade_related.ha.set_default_local_ha_policy", return_value=None)
def test_set_default_local_ha_policy(set_default_local_ha_policy_mock, setup_job_mark_coll):
    post_upgrade_cluster.set_default_local_ha_policy()

    assert setup_job_mark_coll.find_one({"name": "set_default_local_ha_policy"}) is not None
    assert set_default_local_ha_policy_mock.called


@mock.patch("smartx_app.elf.cmd.post_upgrade_cluster.ensure_volume_properties_for_boost")
@mock.patch("smartx_app.elf.cmd.post_upgrade_cluster.sync_volume_properties_to_zbs_vol")
@mock.patch("smartx_app.elf.cmd.post_upgrade_cluster.fix_invalid_host_ha_config")
@mock.patch("smartx_app.elf.cmd.post_upgrade_cluster.convert_and_replace_nfs_image")
def test_update_cluster_throw_exception(
    mock_convert,
    mock_fix_ha,
    mock_sync_vol,
    mock_ensure_vol,
):
    post_upgrade_cluster._update_cluster_throw_exception()

    mock_fix_ha.assert_called_once()
    mock_sync_vol.assert_called_once()
    mock_ensure_vol.assert_called_once()
    mock_convert.assert_called_once()


@mock.patch("smartx_app.elf.common.utils.upgrade_related.placement_group.set_default_placement_group_availability_zone")
def test_set_default_placement_group_availability_zone(
    set_default_placement_group_availability_zone_mock, setup_job_mark_coll
):
    post_upgrade_cluster.set_default_placement_group_availability_zone()

    assert setup_job_mark_coll.find_one({"name": "set_default_placement_group_availability_zone"}) is not None
    assert set_default_placement_group_availability_zone_mock.called
