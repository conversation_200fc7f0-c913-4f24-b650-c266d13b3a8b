# Copyright (c) 2013-2025, SMARTX
# All rights reserved.

from unittest import mock

from smartx_app.elf.cmd import volume
from smartx_app.elf.common.resource_wrappers import volume_wrapper
from smartx_app.elf.common.resources import base
from smartx_app.elf.job_center import constants
from smartx_proto.errors import pyerror_pb2 as py_error


@mock.patch.object(volume_wrapper.VolumeWrapper, "load", autospec=True)
def test_load_volume(mock_volume_load):
    mock_volume_load.side_effect = base.ResourceException("", py_error.VOLUME_NOT_FOUND)
    assert volume._load_volume("test_volume_uuid") is None

    volume_result = mock.Mock(uuid="test_uuid", name="test_name")
    mock_volume_load.side_effect = None
    mock_volume_load.return_value = volume_result
    assert volume._load_volume("test_volume_uuid") == volume_result


@mock.patch.object(volume_wrapper.VolumeWrapper, "show_volume_properties", autospec=True)
@mock.patch("smartx_app.elf.cmd.volume._load_volume")
@mock.patch("builtins.print")
def test_show_volume_properties(mock_print, mock_load_volume, mock_show_volume_properties):
    # 1. Test volume not exist
    mock_load_volume.return_value = None
    volume.show_volume_properties(mock.Mock(volume_uuid="test_volume_uuid"))
    mock_print.assert_any_call("Volume does not exist")
    assert not mock_show_volume_properties.called

    # 2. Test volume xist
    expected_volume = volume_wrapper.VolumeWrapper.new("volume_name", 1 * 2**30, "")
    expected_properties = {
        "vendor": "test_vendor",
        "product": "test_product",
        "serial": "test_serial",
        "wwn": "test_wwn",
    }
    expected_labels = expected_properties
    mock_load_volume.return_value = expected_volume
    mock_show_volume_properties.return_value = (expected_properties, expected_labels)

    volume.show_volume_properties(mock.Mock(volume_uuid="test_volume_uuid"))

    mock_print.assert_any_call(f"Volume properties={expected_properties}")
    mock_print.assert_any_call(f"Lun labels={expected_labels}")


@mock.patch.object(volume_wrapper.VolumeWrapper, "update_iscsi_volume_properties", autospec=True)
@mock.patch("smartx_app.elf.cmd.volume._load_volume")
@mock.patch("builtins.print")
def test_try_update_properties_for_nfs_volume(mock_print, mock_load_volume, mock_update_volume_properties):
    expected_volume = volume_wrapper.VolumeWrapper.new("volume_name", 1 * 2**30, "")
    mock_load_volume.return_value = expected_volume

    volume.update_volume_properties(
        mock.Mock(volume_uuid="test_volume_uuid", vendor="new_vendor", product="new_product")
    )

    mock_print.assert_any_call("Updating properties is not supported for legacy NFS volumes")
    assert not mock_update_volume_properties.called


@mock.patch.object(volume_wrapper.VolumeWrapper, "show_volume_properties", autospec=True)
@mock.patch.object(volume_wrapper.VolumeWrapper, "update_iscsi_volume_properties", autospec=True)
@mock.patch("smartx_app.elf.cmd.volume._load_volume")
@mock.patch.object(volume_wrapper.VolumeWrapper, "get_mounting_vms", autospec=True)
def test_update_properties_for_iscsi_volume_mounted_by_vm(
    mock_get_mounting_vms, mock_load_volume, mock_update_volume_properties, mock_show_volume_properties
):
    expected_volume = volume_wrapper.VolumeWrapper.new(
        "volume_name", 1 * 2**30, "", volume_type=constants.KVM_VOL_ISCSI
    )
    mock_get_mounting_vms.return_value = [{"uuid": "vm_uuid_1", "status": constants.VM_RUNNING, "vm_name": "test_vm"}]
    mock_load_volume.return_value = expected_volume
    expected_properties = {
        "vendor": "test_vendor",
        "product": "test_product",
        "serial": "test_serial",
        "wwn": "test_wwn",
    }
    expected_labels = expected_properties
    mock_load_volume.return_value = expected_volume
    mock_show_volume_properties.return_value = (expected_properties, expected_labels)

    volume.update_volume_properties(
        mock.Mock(volume_uuid="test_volume_uuid", vendor="new_vendor", product="new_product")
    )

    assert not mock_update_volume_properties.called
    assert not mock_show_volume_properties.called


@mock.patch.object(volume_wrapper.VolumeWrapper, "show_volume_properties", autospec=True)
@mock.patch.object(volume_wrapper.VolumeWrapper, "update_iscsi_volume_properties", autospec=True)
@mock.patch("smartx_app.elf.cmd.volume._load_volume")
@mock.patch.object(volume_wrapper.VolumeWrapper, "get_mounting_vms", autospec=True)
def test_update_properties_for_iscsi_volume(
    mock_get_mounting_vms, mock_load_volume, mock_update_volume_properties, mock_show_volume_properties
):
    expected_volume = volume_wrapper.VolumeWrapper.new(
        "volume_name", 1 * 2**30, "", volume_type=constants.KVM_VOL_ISCSI
    )
    mock_get_mounting_vms.return_value = [{"uuid": "vm_uuid_1", "status": constants.VM_STOPPED, "vm_name": "test_vm"}]
    mock_load_volume.return_value = expected_volume
    expected_properties = {
        "vendor": "test_vendor",
        "product": "test_product",
        "serial": "test_serial",
        "wwn": "test_wwn",
    }
    expected_labels = expected_properties
    mock_load_volume.return_value = expected_volume
    mock_show_volume_properties.return_value = (expected_properties, expected_labels)

    volume.update_volume_properties(
        mock.Mock(volume_uuid="test_volume_uuid", vendor="new_vendor", product="new_product")
    )

    assert mock_update_volume_properties.called
    assert mock_show_volume_properties.called
