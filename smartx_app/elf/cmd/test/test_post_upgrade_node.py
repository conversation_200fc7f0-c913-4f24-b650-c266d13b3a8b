# Copyright (c) 2023, SMARTX
# All rights reserved.
from unittest import mock

from smartx_app.elf.cmd import post_upgrade_node


def test_update_current_node():
    with mock.patch("smartx_app.elf.cmd.post_upgrade_node._update_current_node") as _update_current_node_mock:
        _update_current_node_mock.return_value = None
        assert post_upgrade_node.update_current_node(None) is None
        assert _update_current_node_mock.called

        _update_current_node_mock.side_effect = ValueError()
        _update_current_node_mock.configure_mock(**{"__name__": "_update_current_node_mock"})
        assert post_upgrade_node.update_current_node(None) is None
        assert _update_current_node_mock.called


def test_execute_without_exception():
    paras = []

    def _x():
        pass

    def _xx(a, b=None):
        paras.extend([a, b])

    def _e():
        raise RuntimeError()

    assert post_upgrade_node.execute_without_exception(_x) is None
    assert post_upgrade_node.execute_without_exception(_e) is None
    assert len(paras) == 0
    assert post_upgrade_node.execute_without_exception(_xx, ("1",), {"b": "2"}) is None
    assert len(paras) == 2
    assert paras[0] == "1"
    assert paras[1] == "2"
