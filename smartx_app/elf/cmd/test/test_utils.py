# Copyright (c) 2013-2025, elfvirt
# All rights reserved.
from unittest import mock

from smartx_app.elf.cmd import utils


def test_wait_job():
    with (
        mock.patch("common.http.restful_client.Client.get") as mock_get,
        mock.patch("time.sleep", return_value=None) as mock_sleep,
    ):
        mock_get.side_effect = [
            {"ec": "EOK", "data": {"job": {"state": "pending"}}},
            {"ec": "EOK", "data": {"job": {"state": "done"}}},
        ]

        ok, ec_list = utils.wait_job("test_job_id")

        assert ok is True
        assert len(ec_list) == 0
        assert mock_sleep.called
        assert mock_get.called


def test_submit_one_time_task():
    from job_center.api import proxy

    with mock.patch.object(proxy.Proxy, "job_submit") as mock_job_submit:
        job_id = utils.submit_one_time_task("test-description", {})

        assert job_id
        assert mock_job_submit.called
