# Copyright (c) 2013-2025, elfvirt
# All rights reserved.
import logging
import time
import uuid

from common.http import restful_client
from job_center.common import constants as jc_consts


def wait_job(job_id, client=None, timeout_seconds=180):
    client = client or restful_client.Client.from_config(timeout=30)

    def get_job():
        url = client.gen_url("/jobs/{}".format(job_id))
        result = client.get(url)
        client.raise_for_ec(result, "Get job({}) failed: {}".format(job_id, result.get("error")))
        return result["data"]["job"]

    SLEEP_TIME = 5
    RETRY_TIMES = int(timeout_seconds / SLEEP_TIME)

    for _ in range(RETRY_TIMES):
        job = get_job()

        if job and job["state"] == jc_consts.JOB_DONE:
            return True, []
        elif job and job["state"] == jc_consts.JOB_FAILED:
            return False, list({task["error_code"] for task in job["task_list"] if "error_code" in task})
        else:
            # the job submitted by proxy may not persistent
            time.sleep(SLEEP_TIME)
            logging.info("Wait for the job({}) to be completed".format(job_id))

    return False, ["WAIT_JOB_TIMEOUT"]


def submit_one_time_task(description, one_time_task):
    from job_center.api import proxy

    job_id = str(uuid.uuid4())
    proxy.Proxy(name="elf-tool").job_submit(
        user="", description=description, one_time_task=one_time_task, job_id=job_id
    )

    return job_id
