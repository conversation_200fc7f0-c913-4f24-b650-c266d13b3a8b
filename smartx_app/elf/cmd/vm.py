# Copyright (c) 2013-2025, elfvirt
# All rights reserved.
import tabulate

from common.http import restful_client
from smartx_app.common.node import db
from smartx_app.elf.cmd import utils
from smartx_app.elf.common import code
from smartx_app.elf.common.utils import vnic
from smartx_app.elf.job_center import constants as jc_constants


def set_txqueuelen_for_vm(vm_uuid, txqueuelen):
    client = restful_client.Client.from_config(timeout=30)

    url = client.gen_url("/vms/{}/update_nic_txqueuelen".format(vm_uuid))
    result = client.post(url, json={"vm_txqueuelen": txqueuelen})
    client.raise_for_ec(result, "Set VM NIC txqueuelen failed. message: {}".format(result.get("error")))

    job_id = result["data"]["job_id"]
    ok, ec_list = utils.wait_job(job_id, client)
    return ok, job_id, ec_list


def show_txqueuelen_for_vm(vm_uuid):
    show_details, conf = vnic.get_txqueuelen_config_for_vm(vm_uuid)

    table_headers = (
        "MAC Address",
        "NIC Type",
        "Vnet Name",
        "VM Txqueuelen",
        "Cluster Txqueuelen",
        "Actual Txqueuelen",
    )
    table_content = [
        (
            nic["mac_address"],
            nic["nic_type"],
            nic["vnet_name"] or "N/A",
            nic["vm_expected_txqueuelen"] or "N/A",
            nic["cluster_expected_txqueuelen"] or "N/A",
            nic["actual_txqueuelen"] or "N/A",
        )
        for nic in conf
    ]

    if not show_details:
        print("The VM({}) is not running or located on other nodes".format(vm_uuid))
    print(tabulate.tabulate(table_content, table_headers, tablefmt="grid"))


def sync_txqueuelen_for_cluster():
    # Use the specified uuid to avoid concurrency
    TASK_RESOURCE_UUID = "a085952d-fb06-475b-90dc-79d4b9a59475"

    all_node_ips = [host["data_ip"] for host in db.get_host_ips()]

    # one time task has no internal dependencies, so it's guaranteed that each node will execute this task
    # in other words, if one node fails to execute, it will not cause other nodes to skip the task
    one_time_task = {
        TASK_RESOURCE_UUID: {
            "data": {},  # runtime data must non-null
            "hosts": all_node_ips,
            "name": jc_constants.TASK_VM_SYNC_NIC_TXQUEUELEN_WITHIN_NODE,
        }
    }

    job_id = utils.submit_one_time_task(code.API_VM_SYNC_NIC_TXQUEUELEN_WITHIN_NODE, one_time_task)
    ok, ec_list = utils.wait_job(job_id)
    return ok, job_id, ec_list
