# Copyright (c) 2024, SMARTX
# All rights reserved.
import logging

_LOG_PREFIX = "[post_upgrade_node]"


def execute_without_exception(func, args=(), kwargs=None):
    if kwargs is None:
        kwargs = {}

    # noinspection PyBroadException
    try:
        return func(*args, **kwargs)
    except Exception:
        logging.exception(f"{_LOG_PREFIX}An exception occurred while executing `{func.__name__}`:")


def update_current_node(_):
    """Update operations executed on the node during
    the post-phase (before upgrading the JC worker),
    affecting only the node itself."""
    execute_without_exception(_update_current_node)


# In _update_current_node, please wrap the executed function `func`
# with execute_without_exception(func, args, kwargs) to avoid
# interrupting subsequent operations.
def _update_current_node():
    pass
