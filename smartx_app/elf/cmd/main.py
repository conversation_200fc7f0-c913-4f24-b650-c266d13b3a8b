# Copyright (c) 2013-2018, SMARTX
# All rights reserved.

import json
import logging
import logging.handlers
import sys
import traceback
import warnings

from common.config.constant import ZBS_CONFIG_FILE
from common.lib.cfg import Config
from common.lib.utils.cmd_tools import execute
from smartx_app.elf.cmd import post_upgrade_cluster, post_upgrade_node
from smartx_app.elf.cmd import volume as volume_cmd
from smartx_app.elf.common import constants as elf_common_constant
from smartx_app.elf.common.utils import virtualization, volume
from smartx_app.elf.common.utils import volume_properties as volume_properties_util

warnings.filterwarnings("ignore", category=DeprecationWarning)

kvm_module_name = None


def load_kvm_module_name():
    global kvm_module_name
    if kvm_module_name is None and virtualization.is_x86_64():
        kvm_module_name = virtualization.get_kvm_module_name()


def elf_nested_kvm_enable(args):
    cfg = Config(ZBS_CONFIG_FILE)
    secs = cfg.sections()
    if "elf" not in secs:
        cfg.add_section("elf")
    cfg.set("elf", "nested_kvm", "true")
    cfg.save()


def elf_nested_kvm_disable(args):
    cfg = Config(ZBS_CONFIG_FILE)
    secs = cfg.sections()
    if "elf" not in secs:
        cfg.add_section("elf")
    cfg.set("elf", "nested_kvm", "false")
    cfg.save()


def _nested_kvm_module_loaded():
    path = "/sys/module/{}/parameters/nested".format(kvm_module_name)
    with open(path) as fp:
        flag = fp.read()
        if flag.strip() in ["Y", "1"]:
            return True
        else:
            return False


def execute_kvm_module_unload():
    return_code, out, err = execute("modprobe -r {}".format(kvm_module_name))
    if return_code != 0:
        print(out)
        print(err)
        return False
    else:
        return True


def execute_kvm_module_load():
    return_code, out, err = execute("modprobe {}".format(kvm_module_name))
    if return_code != 0:
        print(out)
        print(err)
        return False
    else:
        return True


def _module_enable_file():
    with open("/etc/modprobe.d/kvm-nested.conf", "w") as fp:
        fp.write("options {} nested=1".format(kvm_module_name))


def _module_disable_file():
    with open("/etc/modprobe.d/kvm-nested.conf", "w") as fp:
        fp.write("options {} nested=0".format(kvm_module_name))


def elf_nest_kvm_module_load(args):
    module_is_loaded = _nested_kvm_module_loaded()
    if module_is_loaded:
        _module_enable_file()
        print("{} nested module already loaded".format(kvm_module_name))
        return
    else:
        if virtualization.has_active_domains():
            print(f"Active domains exist, cannot operate {kvm_module_name} module.")
            return

        _module_enable_file()
        result = execute_kvm_module_unload()
        if result:
            execute_kvm_module_load()
        module_is_loaded = _nested_kvm_module_loaded()
        if module_is_loaded:
            print("{} with nested options is loaded".format(kvm_module_name))


def elf_nest_kvm_module_show(args):
    module_is_loaded = _nested_kvm_module_loaded()
    if module_is_loaded:
        print("{} nested module already loaded.".format(kvm_module_name))
    else:
        print("{} nested module is not loaded.".format(kvm_module_name))


def elf_nest_kvm_module_disable(args):
    module_is_loaded = _nested_kvm_module_loaded()
    if not module_is_loaded:
        _module_disable_file()
        print("{} nested module is not loaded, don't need disable.".format(kvm_module_name))
        return
    else:
        if virtualization.has_active_domains():
            print(f"Active domains exist, cannot operate {kvm_module_name} module.")
            return

        _module_disable_file()
        result = execute_kvm_module_unload()
        if result:
            execute_kvm_module_load()
        if not _nested_kvm_module_loaded():
            print("{} load with nested option disabled".format(kvm_module_name))
        else:
            print("Disable {} module failed.".format(kvm_module_name))


def add_elf_volume_cmd(parser):
    cmd = parser.add_cmd("elf_volume", help="Elf volume tool.")
    cmd.add_subcmd(
        "reset_mounting",
        volume_cmd.reset_elf_volume_mounting,
        help="Reset the value of `mounting` field for elf volume.",
    )
    cmd.add_subcmd(
        "sync_elf_volume_template_unique_size",
        volume_cmd.sync_elf_volume_template_unique_size,
        help="Sync the unique size of elf volume template.",
    )
    cmd.add_subcmd(
        "sync_shared_volumes_allowlist",
        help="Sync the shared volumes' allowlist if they are legacy state",
        func=sync_shared_volume_allowlist,
    )

    subcmd = cmd.add_subcmd(
        "show_by_zbs_volume_id",
        help="Show volume info by zbs volume id",
        func=show_by_zbs_volume_id,
    )
    subcmd.add_argument("zbs_volume_id", help="zbs volume id")

    sub_cmd = cmd.add_subcmd(
        "show_properties",
        volume_cmd.show_volume_properties,
        help="Show volume properties and labels of the corresponding LUN",
    )
    sub_cmd.add_argument("volume_uuid")

    sub_cmd = cmd.add_subcmd(
        "update_properties",
        volume_cmd.update_volume_properties,
        help="Update volume properties and labels of the corresponding LUN",
    )
    sub_cmd.add_argument("volume_uuid")
    sub_cmd.add_argument("--vendor", type=str)
    sub_cmd.add_argument("--product", type=str)
    sub_cmd.add_argument("--serial", type=str)
    sub_cmd.add_argument("--wwn", type=str)


def _calc_image_md5(args):
    from smartx_app.elf.http.common import upload

    cur_md5 = upload.calc_image_md5(args.image_uuid)
    print(f"MD5: {cur_md5}")


def _update_image_md5(args):
    from smartx_app.elf.http.common import upload

    updated = upload.update_image_md5(args.image_uuid)
    print(updated)


def add_elf_image_cmd(parser):  # pragma: no cover
    cmd = parser.add_cmd("elf_image", help="ELF image tool.")
    sub_cmd = cmd.add_subcmd("calc_image_md5", _calc_image_md5, help="Calculate the MD5 checksum of the image.")
    sub_cmd.add_argument("image_uuid", help="The UUID of the image.")

    sub_cmd = cmd.add_subcmd(
        "update_image_md5", _update_image_md5, help="Recalculate and update the MD5 checksum of the image."
    )
    sub_cmd.add_argument("image_uuid", help="The UUID of the image.")


def add_upgrade_cluster_cmd(parser):
    cmd = parser.add_cmd("upgrade_cluster", help="ELF command-line tools for upgrading cluster.")
    cmd.add_subcmd(
        "post_update_current_node",
        post_upgrade_cluster.update_current_node,
        help="Update operations executed on the node during the post-phase, affecting only the node itself.",
    )
    cmd.add_subcmd(
        "post_update_cluster",
        post_upgrade_cluster.update_cluster,
        help="Update operations executed on the node during the post-phase, affecting the cluster.",
    )


def add_upgrade_node_cmd(parser):
    cmd = parser.add_cmd("upgrade_node", help="ELF command-line tools for upgrading node.")
    cmd.add_subcmd(
        "post_update_current_node",
        post_upgrade_node.update_current_node,
        help="Update operations executed on the node during the post-phase, affecting only the node itself.",
    )


def show_cpu_pin_on_cluster(args):
    from smartx_app.elf.common.utils import cpu_pin as cpu_pin_util

    _set_logger_silent()

    cpu_pin_util.CPUPinController.show_cpu_pin_on_cluster()


def set_txqueuelen_for_cluster(args):
    from smartx_app.elf.common.resources import cluster_capability

    cluster_txqueuelen = int(args.txqueuelen)
    if (
        cluster_txqueuelen < elf_common_constant.NIC_TXQUEUELEN_MIN
        or cluster_txqueuelen > elf_common_constant.NIC_TXQUEUELEN_MAX
    ):
        print(
            "Failed. Txqueuelen allowed range: [{},{}], but got {}".format(
                elf_common_constant.NIC_TXQUEUELEN_MIN, elf_common_constant.NIC_TXQUEUELEN_MAX, cluster_txqueuelen
            )
        )
        return

    if cluster_capability.NicTxqueuelenCapability.set(cluster_txqueuelen):
        print("OK.")
    else:
        print("Failed. Please retry")


def show_txqueuelen_for_cluster(args):
    from smartx_app.elf.common.resources import cluster_capability

    cluster_txqueuelen = cluster_capability.NicTxqueuelenCapability.get()
    print(cluster_txqueuelen or "N/A")


def sync_txqueuelen_for_cluster(args):
    from smartx_app.elf.cmd import vm

    ok, job_id, ec_list = vm.sync_txqueuelen_for_cluster()
    if not ok:
        print("Failed. The job({}) has error codes: {}".format(job_id, ec_list))
    else:
        print("OK.")


def add_elf_cluster_cmd(parser):
    cmd = parser.add_cmd("elf_cluster", help="Elf cluster tool.")
    sub_cmd = cmd.add_subcmd(
        "remove_node",
        help="Elf command-line tools for removing the Elf info of the node.",
        func=remove_node,
    )
    sub_cmd.add_argument("host_uuid", help="Host UUID of the node to be removed.")

    cmd.add_subcmd(
        "initialize_cluster",
        help="Initialize the cluster.",
        func=initialize_cluster,
    )

    cmd.add_subcmd(
        "show_cpu_pin",
        help="Show cpu_pin info",
        func=show_cpu_pin_on_cluster,
    )

    cmd.add_subcmd(
        "show_acm_volume_properties_check",
        help="Show current status of volume properties check after across cluster migration",
        func=show_acm_volume_properties_check,
    )

    sub_cmd = cmd.add_subcmd(
        "update_acm_volume_properties_check",
        help="Update enable status of volume properties check after across cluster migration",
        func=update_acm_volume_properties_check,
    )
    sub_cmd.add_argument("status", choices=["enable", "disable"])

    set_txqueuelen_cmd = cmd.add_subcmd(
        "set_txqueuelen",
        help=(
            "Configure the global default txqueuelen (transmit queue length) applied to "
            "all VM vNICs unless explicitly overridden"
        ),
        func=set_txqueuelen_for_cluster,
    )
    set_txqueuelen_cmd.add_argument("txqueuelen", type=int, help="Value of NIC txqueuelen")

    cmd.add_subcmd(
        "show_txqueuelen",
        help=(
            "Show the global default txqueuelen (transmit queue length) applied to "
            "all VM vNICs unless explicitly overridden"
        ),
        func=show_txqueuelen_for_cluster,
    )

    cmd.add_subcmd(
        "sync_txqueuelen",
        help="Sync the global default txqueuelen (transmit queue length) to all VM vNICs unless explicitly overridden",
        func=sync_txqueuelen_for_cluster,
    )


def initialize_cluster(args):  # pragma: no cover
    from smartx_app.elf.common.resources import cluster_capability

    cluster_capability.initialize()


def remove_node(args):
    from smartx_app.elf.common.ha import config

    logging.info("ELF related info of node {} will be removed from the cluster.".format(args.host_uuid))

    config.remove_node_from_elf_cluster(args.host_uuid)


def renew_ips(args):
    from smartx_app.elf.common.utils import dhcp

    dhcp.renew_ip_for_all_vms()


def remove_vm_tools(args):
    from smartx_app.elf.common.resource_wrappers.vmtools_wrapper import VMToolsWrapper

    VMToolsWrapper.reset_guest_info(args.vm_uuid)


def set_vm_version(args):
    from common.http import restful_client
    from smartx_app.elf.common import constants

    client = restful_client.Client.from_config(timeout=30)

    vm_uuid = args.vm_uuid
    vm_version = args.vm_version

    url = client.gen_url("/vms/{}".format(vm_uuid))
    result = client.put(
        url,
        json={
            "vm_version": vm_version,
            "vm_version_mode": constants.VM_VERSION_MODE_FIXED,
        },
    )
    client.raise_for_ec(result, "Set vm version failed. message: {}".format(result.get("error")))

    print("OK. Check fisheye or database to make sure whether job is finished")
    print("Job ID: {}", result["data"])


def init_license(_):
    from smartx_app.elf.common.license import license_manager
    from smartx_app.elf.common.resources import base

    manager = license_manager.global_smtx_elf_license_manager
    if manager is None:
        raise Exception("API available in smtx-elf")

    try:
        ok = manager.generate_temporary_certificate()
    except base.ResourceException as e:
        print("Failed. Error message: {}".format(str(e)))
        raise
    else:
        if ok:
            print("OK. Generate a trial license.")
        else:
            print("Failed. API available in smtx-elf")


def add_license_cmd(parser):
    cmd = parser.add_cmd("license", help="Elf license tool.")
    cmd.add_subcmd("init", init_license, help="Init elf license.")


def cpu_pin(args):
    from smartx_app.elf.common.utils import cpu_pin as cpu_pin_util

    """the command usages:
    elf-tool elf_vm cpu_pin ${vm_uuid} "0,5,10-20"
    elf-tool elf_vm cpu_pin ${vm_uuid} ""
    """
    vm_uuid = args.vm_uuid
    pcpu_list = args.pcpu_list or []

    if not pcpu_list:
        cpu_pin_util.CPUPinController.disable_cpu_pin(vm_uuid)
    else:
        cpu_pin_util.CPUPinController.enable_cpu_pin(vm_uuid, pcpu_list)

    print("OK. This config takes effect in the next schedule of the VM")


def _set_logger_silent():
    # keep the output clean by setting higher level log
    logger = logging.getLogger()
    logger.setLevel(logging.ERROR)


def show_cpu_pin(args):
    from smartx_app.elf.common.utils import cpu_pin as cpu_pin_util

    _set_logger_silent()

    vm_uuid = args.vm_uuid
    cpu_pin_util.CPUPinController.show_cpu_pin(vm_uuid)


def set_txqueuelen_for_vm(args):
    from smartx_app.elf.cmd import vm

    ok, job_id, ec_list = vm.set_txqueuelen_for_vm(args.vm_uuid, args.txqueuelen)
    if not ok:
        print("Failed. The job({}) has error codes: {}".format(job_id, ec_list))
    else:
        print("OK.")


def show_txqueuelen_for_vm(args):
    from smartx_app.elf.cmd import vm

    vm.show_txqueuelen_for_vm(args.vm_uuid)


def add_elf_vm_cmd(parser):
    cmd = parser.add_cmd("elf_vm", help="Elf VM tool.")
    cmd.add_subcmd("renew_ips", renew_ips, help="Renew IPs for all VMs.")
    sub_cmd = cmd.add_subcmd(
        "remove_vm_tools",
        remove_vm_tools,
        help=(
            "Remove VM Tools data from cluster for a VM. "
            "It is required to uninstall VM Tools inside the VM before running this command."
        ),
    )
    sub_cmd.add_argument("vm_uuid", help="UUID of VM")

    set_vm_version_cmd = cmd.add_subcmd(
        "set_vm_version",
        set_vm_version,
        help=("Set vm version"),
    )
    set_vm_version_cmd.add_argument("vm_uuid", help="UUID of VM")
    set_vm_version_cmd.add_argument("vm_version", help="VM version, value: elf-legacy, elf-1.0")

    cpu_pin_cmd = cmd.add_subcmd(
        "cpu_pin",
        cpu_pin,
        help=("Pin physical cpus"),
    )

    cpu_pin_cmd.add_argument("vm_uuid", help="UUID of VM")
    cpu_pin_cmd.add_argument(
        "pcpu_list",
        help=(
            "Physical cpus formated by cpuset, example: 0,5,10-20. "
            "The pcpu_list will be sorted. If it's empty, manual cpu pin will be disable."
        ),
    )

    show_cpu_pin_cmd = cmd.add_subcmd(
        "show_cpu_pin",
        show_cpu_pin,
        help=("Show cpu_pin info"),
    )

    show_cpu_pin_cmd.add_argument("vm_uuid", help="UUID of VM")

    set_txqueuelen_cmd = cmd.add_subcmd(
        "set_txqueuelen",
        set_txqueuelen_for_vm,
        help=("Set NIC txqueuelen"),
    )

    set_txqueuelen_cmd.add_argument("vm_uuid", help="UUID of VM")
    set_txqueuelen_cmd.add_argument("txqueuelen", type=int, help="Value of NIC txqueuelen")

    show_txqueuelen_cmd = cmd.add_subcmd(
        "show_txqueuelen",
        show_txqueuelen_for_vm,
        help=("Show NIC txqueuelen"),
    )

    show_txqueuelen_cmd.add_argument("vm_uuid", help="UUID of VM")


def update_all_domain_uuid(args):
    from smartx_app.elf.common.resource_wrappers.vm_additional_info_manager import VMAdditionalInfoWrapper

    VMAdditionalInfoWrapper.update_all_domain_uuid()


def sync_domain_qemu_version(args):
    from smartx_app.elf.common.resource_wrappers import vm_additional_info_manager

    vm_additional_info_manager.sync_domain_qemu_version()


def _add_key_to_cdrom(args):
    from smartx_app.elf.common.utils.disk import add_key_to_cdrom

    add_key_to_cdrom()
    logging.info("Add key to cdrom executed successfully.")


def collect_assigned_but_unused_vfs(args):
    from smartx_app.elf.common.resource_wrappers import sriov
    from smartx_app.elf.common.resource_wrappers import vm_wrapper as vm
    from smartx_app.elf.common.resources import base as base_exception
    from smartx_proto.errors import pyerror_pb2 as py_error

    assigned_but_unused_vfs = sriov.get_assigned_but_unused_vfs()
    has_assigned_but_unused_vfs = False

    for host_uuid, pfs in list(assigned_but_unused_vfs.items()):
        for pf, vms in list(pfs.items()):
            for vm_id, mac_addresses in list(vms.items()):
                if mac_addresses:
                    has_assigned_but_unused_vfs = True
                    vm_json = {}
                    try:
                        vm_json = vm.VMWrapper.load(vm_id).vm_doc
                    except base_exception.ResourceException as e:
                        if e.user_code != py_error.VM_NOT_FOUND:
                            raise
                    print("\nWARNING, Unreleased vf detected:")
                    print("    {}({}) of NODE({})".format(pf.name, pf.pf_id, host_uuid))
                    print("Associated VM is:")
                    print("    {}({}), MAC addresses are:".format(vm_json.get("vm_name"), vm_id))
                    for mac_address in mac_addresses:
                        print("    - {}".format(mac_address))

    if has_assigned_but_unused_vfs:
        print("\nIf you want to fix it, please recheck and then execute:")
        print("    elf-tool sriov release_assigned_but_unused_vfs <host_uuid> <pf_uuid> <vm_uuid> <mac_address>")
    else:
        print("No unreleased vf detected!")


def release_assigned_but_unused_vfs(args):
    from smartx_app.elf.common.resource_wrappers import sriov

    mac_address = args.mac_address.lower()
    released = sriov.release_assigned_but_unused_vfs(args.host_uuid, args.pf_uuid, args.vm_uuid, mac_address)
    if released:
        print("Vf released, please check again.")


def read_host_storage_heartbeat_info(args):
    import json

    from smartx_app.elf.common.utils import elf_iscsi
    from smartx_app.elf.monitor import constants, storage_dao, storage_heartbeat

    host_hb_storages = storage_dao.HostHBStorageDAO(host_uuid=args.host_uuid).load_storages()

    message = "\nStorage Heartbeat Info for Host: {}\n".format(args.host_uuid)

    for storage_uuid, ha_config in host_hb_storages.items():
        hb_agent = storage_heartbeat.StorageHBAgent(storage_uuid)

        if not hb_agent.is_host_storage_heartbeat_configured(ha_config):
            raise Exception("Host {} heartbeat is not properly configured: {}".format(args.host_uuid, ha_config))

        lun_info = hb_agent.parse_iscsi_hb_store(ha_config[storage_heartbeat.HB_ISCSI_STORE_KEY])
        iscsi_client = None

        try:
            iscsi_client = elf_iscsi.ISCSILunClient(
                portal=lun_info["portal"],
                target_iqn=lun_info["target_iqn"],
                lun=lun_info["lun"],
                initiator=constants.DEFAULT_INITIATOR_NAME,
            )

            checksum_raw = iscsi_client.read(
                block_index=ha_config[storage_heartbeat.HB_PARTITION_LBA_KEY],
                # One block is enough to get heartbeat
                read_len=iscsi_client.block_size,
            )
            checksum = checksum_raw.decode("utf-8").rstrip("\0")

            message += "Storage[{}]: {}\nLast Storage Heartbeat Checksum: {}\n\n".format(
                storage_uuid,
                json.dumps(ha_config),
                checksum,
            )

        finally:
            if iscsi_client is not None:
                iscsi_client.destroy()

    print(message)


def sync_shared_volume_allowlist(args):
    from smartx_app.elf.common.utils import sync_shared_volume

    sync_shared_volume.sync_shared_volume_allowlist()


def show_by_zbs_volume_id(args):
    from smartx_app.elf.common.resource_query import volume_querier

    querier = volume_querier.VolumeQuerier()
    volumes = querier.query({"zbs_volume_id": args.zbs_volume_id})
    if not volumes:
        print("Volume not exists")
        return None
    else:
        print(json.dumps(volumes[0], indent=4))
        return volumes[0]


def list_encrypted_resources(args):
    from smartx_app.elf.common.utils import resource as resource_utils

    def _format_table(_encrypted_resources):
        resource_type = ""
        out_table = ""
        for resource in _encrypted_resources:
            if resource_type != resource["type"]:
                resource_type = resource["type"]
                out_table += "\nType: {}\n".format(resource_type)

            out_table += "\tencryption_algorithm={}\tUUID={}\tName={}\n".format(
                resource["encryption_algorithm"], resource["uuid"], resource["name"]
            )
        return out_table

    encrypted_resources = resource_utils.list_encrypted_resources()
    out = ""
    encrypted_resources.sort(key=lambda x: (x["type"], x["encryption_algorithm"]))
    if args.output_format and args.output_format.lower() == "json":
        out = json.dumps(encrypted_resources or [], indent=4)
    else:
        if not encrypted_resources:
            out = "No encrypted resources found."
        else:
            out = _format_table(encrypted_resources)

    print(out)


def add_key_to_cdrom_cmd(parser):
    parser.add_cmd("add_key_to_cdrom", help="Assign a key to all cdrom devices.", func=_add_key_to_cdrom)


def add_elf_vm_additional_info_cmd(parser):
    cmd = parser.add_cmd("elf_vm_additional_info", help="Elf vm additional info tool.")
    cmd.add_subcmd(
        "update_all_domain_uuid", update_all_domain_uuid, help="Update the domain uuid for all VMs on the host."
    )
    cmd.add_subcmd(
        "sync_domain_qemu_version",
        sync_domain_qemu_version,
        help="Update the qemu version for all active domains on the host.",
    )


def add_smartx_vm_monitor_cmd(parser):
    from smartx_app.elf.monitor.monitor_config import (
        disable_readonly_ha,
        enable_readonly_ha,
        mongo_check_status,
        nfs_check_status,
        read_host_heartbeat_checksums,
        reset_host_readonly_status_if_recovered,
        submit_vm_check,
    )

    cmd = parser.add_cmd("monitor", help="elf-vm-monitor check status")
    cmd.add_subcmd(
        "nfs_check_status", func=nfs_check_status, help="using nfs write to check if nfs status is normal or not"
    )
    cmd.add_subcmd(
        "mongo_check_status",
        func=mongo_check_status,
        help="using mongodb write to check if mongo status is normal or not",
    )
    cmd.add_subcmd(
        "nfs_heartbeat_checksums", func=read_host_heartbeat_checksums, help="read all host's nfs heartbeat checksums"
    )
    cmd.add_subcmd(
        "reset_readonly_status",
        func=reset_host_readonly_status_if_recovered,
        help="reset host readonly status to false if possible",
    )
    cmd.add_subcmd(
        "enable_readonly_ha",
        func=enable_readonly_ha,
        help="enable readonly ha feature for this cluster",
    )
    cmd.add_subcmd(
        "disable_readonly_ha",
        func=disable_readonly_ha,
        help="disable readonly ha feature for this cluster",
    )
    cmd.add_subcmd(
        "submit_vm_check",
        func=submit_vm_check,
        help="submit vm check task to job center",
    )


def add_elf_nested_kvm_config_cmd(parser):
    cmd = parser.add_cmd("elf_nested_kvm_config", help="Elf nested KVM setting")
    cmd.add_subcmd("enable", func=elf_nested_kvm_enable, help="Enable Elf nested KVM config for elf")
    cmd.add_subcmd("disable", func=elf_nested_kvm_disable, help="Disable Elf nested KVM config for elf")


def add_elf_nested_kvm_module_cmd(parser):
    cmd = parser.add_cmd("elf_nested_kvm_module", help="{} system module".format(kvm_module_name))
    (cmd.add_subcmd("load", func=elf_nest_kvm_module_load, help="Load {} nested module".format(kvm_module_name)),)
    cmd.add_subcmd("show", func=elf_nest_kvm_module_show, help="Show {} nested module state.".format(kvm_module_name))
    cmd.add_subcmd(
        "disable", func=elf_nest_kvm_module_disable, help="Disable {} nested module.".format(kvm_module_name)
    )


def add_collect_assigned_but_unused_vfs_cmd(cmd):
    cmd.add_subcmd(
        "collect_assigned_but_unused_vfs",
        help="Collect allocated but unused vfs.",
        func=collect_assigned_but_unused_vfs,
    )


def add_release_assigned_but_unused_vfs_cmd(cmd):
    sub_cmd = cmd.add_subcmd(
        "release_assigned_but_unused_vfs",
        help="Release allocated but unused vfs.",
        func=release_assigned_but_unused_vfs,
    )
    sub_cmd.add_argument("host_uuid", help="UUID of the node where the VM is located.")
    sub_cmd.add_argument("pf_uuid", help="UUID of the PF where the VF is located.")
    sub_cmd.add_argument("vm_uuid", help="UUID of the VM which you want to release VF.")
    sub_cmd.add_argument("mac_address", help="MAC address of the VF which you want to release.")


def show_acm_volume_properties_check(args):
    check_enabled = volume_properties_util.get_acm_check_value()
    status = "enable" if check_enabled is True else "disable"
    print("Current check status={}".format(status))


def update_acm_volume_properties_check(args):
    enable = True if args.status == "enable" else False
    volume_properties_util.update_acm_check_value(enable)
    print("Update succeed")


def add_sriov_cmd(parser):
    cmd = parser.add_cmd("sriov", help="SR-IOV relative command")
    add_collect_assigned_but_unused_vfs_cmd(cmd)
    add_release_assigned_but_unused_vfs_cmd(cmd)


def add_read_host_storage_heartbeat(parser):
    cmd = parser.add_cmd(
        "read_host_storage_heartbeat",
        help="Read storage heartbeat checksum of specified host",
        func=read_host_storage_heartbeat_info,
    )
    cmd.add_argument("host_uuid", help="UUID of host to read heartbeat.")


def add_elf_resource_cmd(parser):
    cmd = parser.add_cmd("resource", help="Elf resource tool.")

    subcmd_list_encrypted_resources = cmd.add_subcmd(
        "list_encrypted_resources",
        help="List all encrypted resources in the cluster.",
        func=list_encrypted_resources,
    )
    subcmd_list_encrypted_resources.add_argument(
        "--output-format",
        help="Output format: table or json. Default is the table format.",
        default=None,
    )


def copy_volume_set_total_tasks_max_bps(args):
    from smartx_app.elf.common.resources import cluster_capability

    field_name = "total_tasks_max_bps"
    bitps_value = int(args.value) * (1 << 20) * 8  # MB/s -> bit/s

    try:
        volume.check_copy_volume_max_bitps_value(total_tasks_max_bitps=bitps_value)
    except volume.CopyVolumeMaxBpsInvalid as e:
        print(e)
        return

    if cluster_capability.CopyVolumeMaxBpsCapability.update({field_name: bitps_value}):
        print(f"Set {field_name} to {args.value} MB/s")
    else:
        print(f"Failed to set {field_name}")


def copy_volume_set_task_max_bps(args):
    from smartx_app.elf.common.resources import cluster_capability

    field_name = "task_max_bps"
    bitps_value = int(args.value) * (1 << 20) * 8  # MB/s -> bit/s

    try:
        volume.check_copy_volume_max_bitps_value(task_max_bitps=bitps_value)
    except volume.CopyVolumeMaxBpsInvalid as e:
        print(e)
        return

    if cluster_capability.CopyVolumeMaxBpsCapability.update({field_name: bitps_value}):
        print(f"Set {field_name} to {args.value} MB/s")
    else:
        print(f"Failed to set {field_name}")


def copy_volume_show_capability(args):
    from smartx_app.elf.common import constants
    from smartx_app.elf.common.resources import cluster_capability

    capabilities = cluster_capability.CopyVolumeMaxBpsCapability.query()
    enabled = capabilities.get("enabled", False)
    total_tasks_max_bitps = capabilities.get(
        "total_tasks_max_bps", constants.ELF_COPY_VOL_DEFAULT_TOTAL_TASKS_MAX_BITPS
    )
    task_max_bitps = capabilities.get("task_max_bps", constants.ELF_COPY_VOL_DEFAULT_TASK_MAX_BITPS)

    def bitps_to_mbps(bps):
        return bps // (1 << 20) // 8

    print(f"enabled: {enabled}")
    print(f"total_tasks_max_bps: {bitps_to_mbps(total_tasks_max_bitps)} MB/s")
    print(f"task_max_bps: {bitps_to_mbps(task_max_bitps)} MB/s")


def copy_volume_enable(args):
    from smartx_app.elf.common.resources import cluster_capability

    updated = cluster_capability.CopyVolumeMaxBpsCapability.update({"enabled": True})
    if updated:
        print("Copy volume speed limit enabled.")
    else:
        print("Failed to enable copy volume speed limit.")


def copy_volume_disable(args):
    from smartx_app.elf.common.resources import cluster_capability

    updated = cluster_capability.CopyVolumeMaxBpsCapability.update({"enabled": False})
    if updated:
        print("Copy volume speed limit disabled.")
    else:
        print("Failed to disable copy volume speed limit.")


def add_copy_volume_capability_cmd(parser):
    copy_volume_cmd = parser.add_cmd("copy_volume", help="Copy volume capability related settings.")

    set_total_tasks_max_bps_cmd = copy_volume_cmd.add_subcmd(
        "set_total_tasks_max_bps",
        help="Set the total tasks max BPS for copy volume (unit: MB/s).",
        func=copy_volume_set_total_tasks_max_bps,
    )
    set_total_tasks_max_bps_cmd.add_argument("value", type=int, help="Value for total tasks max BPS (unit: MB/s).")

    set_task_max_bps_cmd = copy_volume_cmd.add_subcmd(
        "set_task_max_bps",
        help="Set the task max BPS for copy volume (unit: MB/s).",
        func=copy_volume_set_task_max_bps,
    )
    set_task_max_bps_cmd.add_argument("value", type=int, help="Value for task max BPS (unit: MB/s).")

    copy_volume_cmd.add_subcmd(
        "show_capability",
        help="Show the current copy volume capabilities.",
        func=copy_volume_show_capability,
    )

    copy_volume_cmd.add_subcmd(
        "enable",
        help="Enable copy volume speed limit.",
        func=copy_volume_enable,
    )
    copy_volume_cmd.add_subcmd(
        "disable",
        help="Disable copy volume speed limit.",
        func=copy_volume_disable,
    )


def _set_execute_guest_command_capability(enabled):
    from smartx_app.elf.common.resources import cluster_capability

    updated = cluster_capability.ExecuteGuestCommandCapability.update({"enabled": enabled})

    if updated:
        status = "enabled" if enabled else "disabled"
        print(f"Execute guest command capability {status}")
    else:
        print("Failed to update execute guest command capability")


def execute_guest_command_enable(args):
    _set_execute_guest_command_capability(True)


def execute_guest_command_disable(args):
    _set_execute_guest_command_capability(False)


def execute_guest_command_show_capability(args):
    from smartx_app.elf.common import constants
    from smartx_app.elf.common.resources import cluster_capability

    capabilities = cluster_capability.ExecuteGuestCommandCapability.query()
    enabled = (
        capabilities.get("enabled", constants.ELF_EXECUTE_GUEST_COMMAND_DEFAULT_ENABLED) if capabilities else False
    )

    print(f"enabled: {enabled}")


def add_execute_guest_command_capability_cmd(parser):
    execute_guest_command_cmd = parser.add_cmd(
        "execute_guest_command", help="Execute guest command capability related settings."
    )

    execute_guest_command_cmd.add_subcmd(
        "enable",
        help="Enable execute guest command capability.",
        func=execute_guest_command_enable,
    )

    execute_guest_command_cmd.add_subcmd(
        "disable",
        help="Disable execute guest command capability.",
        func=execute_guest_command_disable,
    )

    execute_guest_command_cmd.add_subcmd(
        "show_capability",
        help="Show the current execute guest command capability.",
        func=execute_guest_command_show_capability,
    )


def update_migration_caps_tls(sts):
    from smartx_app.elf.common import constants
    from smartx_app.elf.common.resources import cluster_capability

    caps = cluster_capability.MigrationClusterCapability.load()
    caps.update({constants.MIGRATION_CAPS_TLS: sts})
    print("Ok")


def list_migration_caps(args):
    from smartx_app.elf.common.resources import cluster_capability

    caps = cluster_capability.MigrationClusterCapability.load()
    for key in cluster_capability.MIGRATION_CLUSTER_CAPABILITIES:
        print("{}: {}".format(key, caps.get(key)))


def add_elf_migration_caps_cmd(parser):
    cmd = parser.add_cmd("migration_caps", help="Adjust migration capabilities")

    cmd.add_subcmd(
        "enable_migration_with_tls",
        help="enable migration with tls",
        func=lambda args: update_migration_caps_tls(True),
    )

    cmd.add_subcmd(
        "disable_migration_with_tls",
        help="disable migration with tls",
        func=lambda args: update_migration_caps_tls(False),
    )

    cmd.add_subcmd(
        "list",
        help="list all migration capabilities",
        func=list_migration_caps,
    )


def create_parser():
    from common.lib.parser import ToolParser

    parser = ToolParser()
    parser.init_default_func()  # pragma: no cover

    parser.parser.add_argument("--logfile", dest="logfile", help="log file for output", default=None)

    add_smartx_vm_monitor_cmd(parser)
    add_elf_nested_kvm_module_cmd(parser)
    add_elf_nested_kvm_config_cmd(parser)
    add_elf_volume_cmd(parser)
    add_elf_vm_cmd(parser)
    add_elf_vm_additional_info_cmd(parser)
    add_key_to_cdrom_cmd(parser)
    add_sriov_cmd(parser)
    add_read_host_storage_heartbeat(parser)
    add_license_cmd(parser)
    add_upgrade_cluster_cmd(parser)
    add_upgrade_node_cmd(parser)
    add_elf_cluster_cmd(parser)
    add_elf_image_cmd(parser)
    add_elf_resource_cmd(parser)
    add_copy_volume_capability_cmd(parser)
    add_execute_guest_command_capability_cmd(parser)
    add_elf_migration_caps_cmd(parser)
    return parser


def init_elf_cmd_log(logfile=None):
    level = logging.INFO

    logger = logging.getLogger()
    formatter = logging.Formatter("%(asctime)s, %(levelname)s, %(filename)s:%(lineno)d, %(message)s")
    handler = None

    if logfile:
        handler = logging.handlers.RotatingFileHandler(logfile, maxBytes=3 * 1024 * 1024, backupCount=2)
    else:
        handler = logging.StreamHandler(sys.stdout)

    handler.setFormatter(formatter)

    logger.addHandler(handler)
    logger.setLevel(level)


def main():
    load_kvm_module_name()

    parser = create_parser()
    args = parser.parse()

    init_elf_cmd_log(logfile=args.logfile)

    try:
        parser.run()
    except Exception as e:
        message = traceback.format_exc()
        if args.verbose:
            logging.exception(message)
        else:
            logging.error(str(e))
        sys.exit(1)


if __name__ == "__main__":
    pass
