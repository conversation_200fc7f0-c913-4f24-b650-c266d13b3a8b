# Copyright (c) 2013-2022, SMARTX
# All rights reserved.
import unittest
from unittest import mock

from job_center.handler.follower import base
from smartx_app.elf.common.config import platform
from smartx_app.elf.common.tests.utils import fake as fake_utils
from smartx_app.elf.common.utils import storage_cluster as sc_utils
from smartx_app.elf.common.utils import zbs_iscsi
from zbs.iscsi import client
from zbs.nfs import client as zbs_nfs_client
from smartx_app.elf.actions.wrapper import tasks as tasks_wrapper

def test_helper_func(*args, **kwargs):
    pass

class TestTaskWrapper(unittest.TestCase):
    def test_include_iscsi_conn(self):
        ins = mock.Mock()
        with mock.patch.object(zbs_iscsi, "ZbsClientWrapper", return_value=ins):

            @tasks_wrapper.include_iscsi_conn
            def test_func(*args, **kwargs):
                return kwargs["iscsi_client"]

            vol = fake_utils.fake_iscsi_volume()
            vol.pop("storage_cluster_uuid")
            self.assertEqual(test_func(test_helper_func, vol), ins)

    def test_include_iscsi_conn_without_iscsi_volume(self):
        ins = mock.Mock()
        with mock.patch.object(zbs_iscsi, "ZbsClientWrapper", return_value=ins):

            @tasks_wrapper.include_iscsi_conn
            def test_func(*args, **kwargs):
                return kwargs["iscsi_client"]

            vol = fake_utils.fake_nfs_volume()
            # catch ValueError
            with self.assertRaises(ValueError):
                test_func(test_helper_func, vol)

    def test_conn_with_storage_cluster(self):
        with mock.patch.object(client, "ZbsISCSI"), mock.patch.object(sc_utils, "init_zbs_iscsi_client") as mock_init:

            @tasks_wrapper.include_iscsi_conn
            def test_func(*args, **kwargs):
                return kwargs

            vol = fake_utils.fake_iscsi_volume()
            test_func(test_helper_func, vol)
            mock_init.assert_called_once_with(vol["storage_cluster_uuid"])

    def test_nfs_client_with_kvm_platform(self):
        with mock.patch.object(platform, "is_in_kvm_or_san", return_value=True), mock.patch.object(
            zbs_nfs_client, "ZbsNFS"
        ) as mock_zbs:

            @tasks_wrapper.include_nfs_conn
            def test_func(*args, **kwargs):
                return kwargs["nfs_client"]

            test_func(test_helper_func)
            assert mock_zbs.assert_called
