from functools import wraps
import logging

import libvirt

from smartx_app.elf.common.config import platform
from smartx_app.elf.common.utils import storage_cluster, zbs_iscsi
from smartx_app.elf.job_center import constants
from zbs.nfs.client import ZbsNFS


def include_libvirt_conn(func):
    """Decorator to include libvirt connection before invoke async task

    :param func: the async task
    :return: function after wrap
    """
    from smartx_app.elf.common.utils.libvirt_driver import libvirt_connection

    @wraps(func)
    def wrapper(self, *args, **kwargs):
        with libvirt_connection() as conn:
            kwargs["libvirt_conn"] = conn
            try:
                return func(self, *args, **kwargs)
            except libvirt.libvirtError as e:
                logging.error("Got libvirt error: %s" % str(e))
                raise

    return wrapper


def include_nfs_conn(func):
    """Decorator to include nfs connection before invoke async task

    :param func: the async task
    :return: function after wrap
    """

    @wraps(func)
    def wrapper(self, *args, **kwargs):
        nfs_client = None
        if platform.is_in_kvm_or_san():
            nfs_client = ZbsNFS()

        kwargs["nfs_client"] = nfs_client
        return func(self, *args, **kwargs)

    return wrapper


def include_iscsi_conn(func):
    """Decorator to include iscsi connection before invoke async task

    :param func: the async task
    :return: function after wrap
    """

    @wraps(func)
    def wrapper(self, resource, *args, **kwargs):
        if resource.get("type") not in (
            constants.KVM_VOL_ISCSI,
            constants.KVM_VOL_ISCSI_SNAPSHOT,
            constants.KVM_VOL_ISCSI_TEMPLATE,
        ):
            raise ValueError("resource type is not iscsi volume/snapshot/template")

        kwargs["iscsi_client"] = zbs_iscsi.ZbsClientWrapper(
            storage_cluster.init_zbs_iscsi_client(resource.get("storage_cluster_uuid"))
        )
        return func(self, resource, *args, **kwargs)

    return wrapper
