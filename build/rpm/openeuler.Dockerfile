FROM registry.smtx.io/pyzbs-ci/openeuler:20.03 AS builder

# configure yum repo
ARG ARCH
RUN rm -f /etc/yum.repos.d/*
COPY ./build/sources/repo/openEuler.repo /etc/yum.repos.d/

# install common softwares
RUN dnf clean all && yum update -y \
    && dnf install -y rpm-build git make tar sudo which tree gcc gcc-c++ libcap-devel libnl3-devel openssl-devel \
    dbus-devel glib2-devel wget libX11 tk tcl rsync sshpass bash ntp ethtool dnf-plugins-core ethtool \
    iproute iputils vim \
    && dnf clean all -q

# install libvirt and libvirt-devel
ARG ELF_LIBVIRT_VERSION_OE2003
RUN dnf install -y libvirt-${ELF_LIBVIRT_VERSION_OE2003} libvirt-devel-${ELF_LIBVIRT_VERSION_OE2003} \
    && dnf clean all -q

ARG SMARTX_PYTHON3_VERSION
RUN dnf install tuna-python3-${SMARTX_PYTHON3_VERSION} -y && yum clean all
ENV PATH=/root/.local/bin:$PATH
ARG PYTHON3_COMMAND
RUN ${PYTHON3_COMMAND} -m pip config --user set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
RUN ${PYTHON3_COMMAND} -m pip config --user set global.trusted-host pypi.tuna.tsinghua.edu.cn
# install and configure zbs and pyzbs dependencies every release
ARG POETRY_VERSION=1.6.1
RUN curl -sSL https://install.python-poetry.org > install-poetry.py \
    && ${PYTHON3_COMMAND} install-poetry.py --version ${POETRY_VERSION} \
    && rm -f install-poetry.py
RUN poetry self add "poetry-dynamic-versioning[plugin]" \
    && poetry config virtualenvs.in-project true
ARG RUFF_VERSION
RUN ${PYTHON3_COMMAND} -m pip install --user ruff==${RUFF_VERSION}
WORKDIR /pyzbs


FROM registry.smtx.io/pyzbs-ci/pyzbs-builder-openeuler:latest AS unittest
# install common softwares
RUN dnf install -y zookeeper-3.5.9 nfs-utils glibc ansible qemu-kvm-ev libaio dmidecode ipmitool genisoimage \
    && dnf clean all

# modify sudoers
RUN sed -i '/Defaults    requiretty/d' /etc/sudoers

# configure zookeeper
COPY ./build/test/conf/zoo.cfg /etc/zookeeper/zoo.cfg

# Below are the part that needs to be re-executed every time the image
# is built. Add a timestamp arg to invalidate the part below from last
# cached data.
ARG TIMESTAMP
ARG ZBS_VERSION
ARG ZBS_CLIENT_PY3_VERSION
RUN dnf install -y \
    libzbs-devel-${ZBS_VERSION} \
    zbs-${ZBS_VERSION} \
    libzbs-${ZBS_VERSION} \
    zbs-client-py3-${ZBS_CLIENT_PY3_VERSION}
