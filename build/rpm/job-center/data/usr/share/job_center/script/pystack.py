#!/usr/local/venv/tuna/bin/python3

import argparse
import distutils.spawn
import subprocess

RAW_SCRIPTS = """
import sys
import traceback


def _inject_dump_greenlets():
    import gc
    from greenlet import greenlet

    print("[debugging]: Dumping Greenlets....", file=sys.stderr)
    objs = []
    for ob in gc.get_objects():
        try:
            if ob and isinstance(ob, greenlet):
                objs.append(ob)
        except ReferenceError:
            continue
        except Exception as e:
            print(f"Exception: {e}", file=sys.stderr)
    for ob in objs:
        print("[debugging] ---------------------------------", file=sys.stderr)
        print("".join(traceback.format_stack(ob.gr_frame)), file=sys.stderr)


def _itervalues(d):
    return getattr(d, "itervalues", d.values)()


def _inject_dump_threads():
    import sys

    print("[debugging] Dumping threads....", file=sys.stderr)
    for ob in _itervalues(sys._current_frames()):
        print("[debugging] ---------------------------------", file=sys.stderr)
        print("".join(traceback.format_stack(ob)), file=sys.stderr)



def _inject_main():
    _inject_dump_threads()
    _inject_dump_greenlets()



_inject_main()
"""

INJECT_SCRIPTS = RAW_SCRIPTS.strip().replace("\n", "\\\\n").replace(r'"', r"\"")


def find_debugger(name):
    debugger = distutils.spawn.find_executable(name)
    if not debugger:
        raise Exception(f"Could not find {name} in your PATH environment variable")
    return debugger


def make_gdb_args(pid, inject):
    arguments = [
        find_debugger("gdb"),
        "-p",
        str(pid),
        "-batch",
        "-ex=call (void *) PyGILState_Ensure()",
        f"-ex=call (void) PyRun_SimpleString(\"exec('{inject}')\")",
        "-ex=call (void) PyGILState_Release((void *) $1)",
    ]
    print("\n".join(arguments))
    return arguments


def print_inject(pid):
    """Executes a file in a running Python process."""

    args = make_gdb_args(pid, INJECT_SCRIPTS)
    process = subprocess.Popen(args, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    out, err = process.communicate()
    print(f"out:\n{out}\n")
    print(f"err:\n{err}\n")
    print(f"rc: {process.returncode}\n")


parser = argparse.ArgumentParser(description="pystack info", formatter_class=argparse.ArgumentDefaultsHelpFormatter)
parser.add_argument("--pid", "-p", type=int, required=True, dest="pid", help="pid")
args = parser.parse_args()

if __name__ == "__main__":
    print_inject(pid=args.pid)
