import fcntl
import logging
import os
import subprocess
import sys
import time

import psutil

from common.config.constant import NODE_ROLE_MASTER
from common.lib.cfg import DeployConfigManager
from common.mongo.db import mongodb
from job_center.common.constants import WORKER_DISCONNECTED_UNEXPECTED

PID_LOCK = "/var/run/restart_job_center_if_hang.pid"

job_center_timeout = 120

job_center_worker_service = "job-center-worker.service"
job_center_worker_log_path = "/var/log/zbs/job-center-worker.INFO"
job_center_scheduler_service = "job-center-scheduler.service"
job_center_scheduler_log_path = "/var/log/zbs/job-center-scheduler.INFO"


DEPLOYMENT_ENV_FILE = "/etc/zbs/.deployment_env"
ENV_TEST = "test"


def restart_service_if_hang(service: str, log_file_path: str) -> int:
    logging.info(f"{service} log file last modify time check ...")

    if not os.path.isfile(log_file_path):
        logging.info(f"{log_file_path} log file is not exist, try to restart {service}")
        restart_service(service)
        return 0

    free_blocks = os.statvfs(log_file_path).f_bavail
    if free_blocks == 0:
        logging.info(f"{log_file_path} log file is full, skip check")
        return 0

    log_modify_time = os.path.getmtime(log_file_path)
    if time.time() - log_modify_time > job_center_timeout:
        logging.info(
            f"{log_file_path} log file is not modified in {job_center_timeout} seconds, try to restart {service}"
        )
        restart_service(service)
        return 0

    logging.info(f"{service} log file last modify time is ok")


def check_service_is_active(service: str) -> bool:
    try:
        subprocess.check_output(["systemctl", "is-active", service])
    except subprocess.CalledProcessError:
        logging.info(f"{service} is not active, skip check")
        return False
    return True


def restart_service(service: str):
    logging.info(f"[HANG] {service} is hang, try to restart")

    try:
        pid = get_pid_by_systemd(service)

        # When service just started, maybe still in bootstrapping. It seems that dump stack at this moment
        # has higher crush rate. We should consider service is not ready yet and do not restart actually.
        start_duration = time.time() - psutil.Process(pid).create_time()  # timestamp
        if start_duration < 30:  # seconds
            logging.info(f"[HANG] {service} just started in {start_duration} seconds, restart not executed")
            return

        # Dump stacks by using gdb to inject python script into target process may cause
        # target process crash, and new core dump will generated every time.
        # This debug approach should not be applied to product environments.
        if pid > 1 and get_deployment_env() == ENV_TEST:
            output_service_stack(pid)
    except Exception as e:
        logging.exception(e)

    res = subprocess.run(["systemctl", "restart", service], check=False)
    if res.returncode != 0:
        logging.error(f"[HANG] restart {service} failed")
    logging.info(f"[HANG] restart {service} success")


def get_pid_by_systemd(service: str) -> int:
    logging.info(f"start to get {service} pid by systemd")

    res = subprocess.run(
        [f"systemctl show -p MainPID {service}"],
        shell=True,
        stdout=subprocess.PIPE,
        check=False,
    )
    if res.returncode != 0:
        logging.error(f"get {service} pid by systemd failed: {res.stderr}")
        return -1

    pid = int(res.stdout.decode("utf-8").strip().split("=")[1])
    if pid > 0:
        logging.info(f"get {service} pid by systemd success: {pid}")
    return pid


def get_deployment_env() -> str:
    if os.path.exists(DEPLOYMENT_ENV_FILE):
        with open(DEPLOYMENT_ENV_FILE) as f:
            return f.read().strip()

    return ""


def output_service_stack(pid: int):
    logging.info(f"start to output {job_center_worker_service} pid: {pid} stack")
    try:
        res = subprocess.run(
            ["/opt/smtx/bin/python3", "/usr/share/job_center/script/pystack.py", f"--pid={pid}"],
            timeout=30,
            check=False,
        )
        if res.returncode != 0:
            logging.error(f"output {job_center_worker_service} stack failed: {res.stderr}")
        logging.info(f"output {job_center_worker_service} stack success: {res.stdout}")
    except subprocess.TimeoutExpired:
        logging.error(f"output {job_center_worker_service} stack timeout")
    except Exception as e:
        logging.exception(e)


def check_jc_worker_heartbeat() -> bool:
    node_uuid = DeployConfigManager().get_node_uuid()
    if not node_uuid:
        logging.error("host_uuid is empty, return without restart")
        return True

    host = mongodb.job_center.worker.find_one({"host_uuid": node_uuid})
    if host is None:
        logging.warning(f"host_uuid {node_uuid} is not exist in job_center.worker")
        return False
    if host["state"] == WORKER_DISCONNECTED_UNEXPECTED and int(time.time()) - host["mtime"] > job_center_timeout:
        logging.warning(
            f"host_uuid {node_uuid} lost connected over {job_center_timeout} seconds, "
            f"state: {host['state']}, last_mtime: {host['mtime']}"
        )
        return False

    logging.info(f"{job_center_worker_service} heartbeat is active")
    return True


def setup_logger(log_file, log_level, max_bytes=0, backup_count=0):
    if log_file:
        from logging.handlers import RotatingFileHandler

        handler = RotatingFileHandler(log_file, maxBytes=max_bytes, backupCount=backup_count)
    else:
        handler = logging.StreamHandler(sys.stdout)

    handler.setFormatter(logging.Formatter("[%(asctime)s: %(levelname)s/%(processName)s] %(message)s"))
    handler.setLevel(logging.getLevelName(log_level))

    logger = logging.getLogger()
    logger.setLevel(logging.getLevelName(log_level))
    for hdlr in logger.handlers:
        logger.removeHandler(hdlr)
    logger.addHandler(handler)


def acquire_lock(fd):
    try:
        fcntl.lockf(fd, fcntl.LOCK_EX | fcntl.LOCK_NB)
        return True
    except Exception as e:
        logging.error("failed to acquire lock {}: {}".format(PID_LOCK, e))
        return False


def main():
    logging.info("--------------------")
    with open(PID_LOCK, "w") as fd:
        if not acquire_lock(fd):
            logging.info("another instance is running, exit")
            sys.exit(1)
        fd.write(str(os.getpid()))

        try:
            # check job-center-worker service
            # if job-center-worker heartbeat is not exist or lost timeout, restart job-center-worker service
            if check_service_is_active(job_center_worker_service):
                logging.info(f"{job_center_worker_service} heartbeat check ...")
                if not check_jc_worker_heartbeat():
                    restart_service(job_center_worker_service)
                    for i in range(1, 7):
                        time.sleep(30)
                        logging.info(f"{job_center_worker_service} heartbeat re-check after restart {i *30} seconds")
                        if not check_jc_worker_heartbeat():
                            logging.info(
                                f"{job_center_worker_service} heartbeat is not active after restart {i *30} seconds"
                            )
                            continue
                        break
        except Exception as e:
            logging.exception(e)

        # if job-center-worker service heartbeat is existed and not lost timeout,
        # then check job-center-worker log file
        if check_service_is_active(job_center_worker_service):
            restart_service_if_hang(job_center_worker_service, job_center_worker_log_path)

        # check job-center-scheduler service on master node
        if DeployConfigManager().get_node_role() == NODE_ROLE_MASTER and check_service_is_active(
            job_center_scheduler_service
        ):
            logging.info(f"current node is {NODE_ROLE_MASTER}, try to check {job_center_scheduler_service} service")
            restart_service_if_hang(job_center_scheduler_service, job_center_scheduler_log_path)


if __name__ == "__main__":
    setup_logger("/var/log/zbs/restart_job_center_if_hang.log", "INFO", 50 * 2**20, 2)
    try:
        main()
    except Exception as ex:
        logging.exception(ex)
