# Turn off the brp-python-bytecompile automagic
%global _python_bytecompile_extra 0
%define _build_id_links none
%undefine __brp_python_bytecompile
%undefine __python
%undefine __python3

%define pyshort %(echo "%{python3_version}" | cut -d. -f1-2)
# ==================
# Top-level metadata
# ==================

%define use_el7 ("%{?dist}" == ".el7.centos")
%define use_ky10 ("%{?dist}" == ".ky10")
%define use_oe2003 ("%{?dist}" == ".oe1")
%define use_tl3 ("%{?dist}" == ".tl3")
%define use_systemd (0%{?fedora} && 0%{?fedora} >= 18) || (0%{?rhel} && 0%{?rhel} >= 7) || (0%{?suse_version} == 1315) || %{use_oe2003} || %{use_tl3}

%if %{use_tl3}
# disable shebang mangling of python scripts
%undefine __brp_mangle_shebangs
%endif

Name:       tuna-job-center
Version:    %{RPM_VERSION}
Release:    %{?GITTAG}%{?dist}
Summary:    Job Center

Group:      System Environment/Compute
License:    Proprietary

# ==================================
# Conditionals controlling the build
# ==================================

# =====================
# General global macros
# =====================
%define pyshort %(echo "%{python3_version}" | cut -d. -f1-2)
%global venv_path /usr/local/venv/job-center
%global venv_bin %{venv_path}/bin
%global venv_site %{venv_path}/lib/python%{pyshort}/site-packages

# =======================
# Build-time requirements
# =======================
BuildRequires:  which
BuildRequires:  tar
BuildRequires:  make

# provided by zbs
Requires:       zbs-client-py3

Obsoletes:  smartx-job-center


# ==========================================
# Descriptions, and metadata for subpackages
# ==========================================
# provided by Centos and EPEL
AutoReq:    no

%description
Job Center


# ======================================================
# The prep phase of the build:
# ======================================================
%prep

# ======================================================
# Configuring and building the code:
# ======================================================
%build

# ======================================================
# Installing the built code:
# ======================================================
%install

# copy venv code
mkdir -p %{buildroot}%{venv_path}
cp -ar %{_builddir}/* %{buildroot}%{venv_path}

# link consle scripts
mkdir -p %{buildroot}%{_bindir}
ln -sfT %{venv_bin}/job-center %{buildroot}%{_bindir}/job-center

# copy system config
mkdir -p %{buildroot}%{_datadir}
mkdir -p %{buildroot}%{_sysconfdir}
cp -r %{_sourcedir}/etc/* %{buildroot}%{_sysconfdir}/
cp -r %{_sourcedir}/usr/share/* %{buildroot}%{_datadir}/


%if %{use_systemd}
mkdir -p %{buildroot}%{_unitdir}
cp -r %{_sourcedir}/systemd/* %{buildroot}%{_unitdir}
rm -rf %{buildroot}%{_sysconfdir}/init/*
%endif


# copy venv config file to system dir
mkdir -p %{buildroot}%{_sysconfdir}/fluent-bit/
cp -r %{venv_site}/job_center/fluent-bit/parser_job_center.conf %{buildroot}%{_sysconfdir}/fluent-bit/
cp -r %{venv_site}/job_center/fluent-bit/input_job_center.conf %{buildroot}%{_sysconfdir}/fluent-bit/

%files
%{venv_path}/

%{_bindir}/job-center
%{_sysconfdir}/cron.d/restart_job_center_if_hang
%{_sysconfdir}/fluent-bit/input_job_center.conf
%{_sysconfdir}/fluent-bit/parser_job_center.conf
%{_sysconfdir}/logrotate.tuna.d/job-center-workerlog
%{_sysconfdir}/rsyslog.d/job_center.conf

%{_datadir}/job_center/script/restart_job_center_if_hang.py
%{_datadir}/job_center/script/pystack.py


%if %{use_systemd}
%config(noreplace) %{_unitdir}/job-center-worker.service
%config(noreplace) %{_unitdir}/job-center-scheduler.service
%else
%{_sysconfdir}/init/job-center-worker.conf
%{_sysconfdir}/init/job-center-scheduler.conf
%endif

%post


# ======================================================
# Finally, the changelog:
# ======================================================
%changelog

%(cd %{workspace_dir} && git log -n 50 --format="* %cd %aN%n- (%h) %s%d%n" --date=local | sed -r 's/[0-9]+:[0-9]+:[0-9]+ //')
