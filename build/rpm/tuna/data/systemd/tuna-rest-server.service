[Unit]
Description=Tuna rest api server
After=network.target mongod.service

[Service]
Environment="GRPC_ENABLE_FORK_SUPPORT=0"
ExecStart=/usr/local/venv/tuna/bin/smtx-gunicorn -n tuna-rest-server -b 127.0.0.1:10411 -k gevent tuna.server.main:flask_app -w 1 --timeout 120 --pid /var/run/tuna_rest.pid --log-config /usr/share/tuna/config/tuna_rest/logging.conf --access-logformat '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(L)s' --capture-output --error-logfile /var/log/zbs/tuna-rest.INFO
Restart=on-failure

[Install]
WantedBy=multi-user.target
