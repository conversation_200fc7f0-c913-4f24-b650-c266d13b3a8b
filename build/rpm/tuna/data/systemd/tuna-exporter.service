[Unit]
Description=tuna exporter api server
After=network.target

[Service]
Environment="GRPC_ENABLE_FORK_SUPPORT=0"
EnvironmentFile=/etc/sysconfig/tuna-exporter
ExecStart=/usr/local/venv/tuna/bin/smtx-gunicorn  -n tuna-exporter -b ${BIND_IP}:10404 -k gevent tuna.exporter.main:flask_app -w 1 --timeout 60 --pid /var/run/tuna-exporter.pid --log-config /usr/share/tuna/config/tuna-exporter/logging.conf --access-logformat '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(L)s' --capture-output --error-logfile /var/log/zbs/tuna-exporter.INFO
Restart=on-failure

[Install]
WantedBy=multi-user.target
