[Unit]
Description=tuna log collector rest api server
After=network.target mongod.service

[Service]
EnvironmentFile=/etc/sysconfig/log-collector
ExecStart=/usr/local/venv/tuna/bin/smtx-gunicorn -n log-collector -b 127.0.0.1:10406 -b ${BIND_IP}:10406 -k gevent log_collector.rest.main:app -w 1 --timeout 30 --pid /var/run/log_collector.pid --log-config /usr/share/tuna/config/log_collector/logging.conf --access-logformat '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(L)s' --capture-output --error-logfile /var/log/zbs/log-collector.INFO
Restart=on-failure

[Install]
WantedBy=multi-user.target
