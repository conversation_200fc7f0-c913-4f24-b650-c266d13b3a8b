[Unit]
Description=disk health guard
After=network.target mongod.service zbs-chunkd.service

[Service]
Environment="GRPC_ENABLE_FORK_SUPPORT=0"
EnvironmentFile=/etc/sysconfig/disk-healthd
ExecStart=/usr/local/venv/tuna/bin/smtx-gunicorn \
    -n disk-healthd -b ${BIND_IP}:10415 -k gevent -w 1 \
    --timeout 60 'disk_healthd.main:gunicorn_entry()' \
    --pid /var/run/disk-healthd.pid \
    --log-config /usr/share/disk_healthd/gunicorn_logging.conf \
    --access-logformat '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(L)s' \
    --capture-output --error-logfile /var/log/zbs/disk-healthd/disk-healthd.log

Restart=on-failure

[Install]
WantedBy=multi-user.target
