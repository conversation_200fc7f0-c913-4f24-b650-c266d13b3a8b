#!/usr/bin/env bash
set -e

if [[ ! -f /etc/zbs/support_intel_dsa ]]; then
    echo "Intel DSA is not supported on this node."
    exit 0
fi

support_intel_dsa=$(cat /etc/zbs/support_intel_dsa)
if [[ $support_intel_dsa != "true" ]]; then
    echo "Intel DSA is not supported on this node."
    exit 0
fi

# Add vfio-pci driver module
modprobe vfio-pci

# Get DSA devices
dsa_bdf=$(lspci -nnn | grep "8086:0b25" | awk '{print $1}' | paste -sd ' ')

# Bind vfio-pci driver
/usr/share/tuna/file/script/dpdk-devbind.py --bind=vfio-pci $dsa_bdf

# Check the status of the binding
/usr/share/tuna/file/script/dpdk-devbind.py -s | grep "'Device 0b25' drv=vfio-pci"
