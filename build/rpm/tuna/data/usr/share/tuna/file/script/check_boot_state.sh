#!/bin/bash

# ref: https://www.freedesktop.org/software/systemd/man/systemctl.html

state=`systemctl is-system-running`
case $state in
  initializing|starting|stopping)
    # system is in booting or shutdown state
    exit 1
    ;;
  *)
    # others, may be follow state:
    # running: The system is fully operational.
    # degraded: The system is operational but one or more units failed.
    # maintenance: The rescue or emergency target is active.
    # offline: The manager is not running. Specifically, this is the operational state if an incompatible program is running as system manager (PID 1).
    # unknown: The operational state could not be determined, due to lack of resources or another error cause.
    exit 0
    ;;
esac
