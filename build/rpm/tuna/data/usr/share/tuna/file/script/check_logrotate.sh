#!/usr/bin/env bash

# TUNA-1332
# TUNA-1642
# https://bugzilla.redhat.com/show_bug.cgi?id=447022

LOGROTATE_CONFIG_PATH="/etc/logrotate.conf"
LOGROTATE_STATUS="/var/lib/logrotate/logrotate.status"
LEGACY_LOGROTATE_STATUS="/var/lib/logrotate.status"
declare -a ERR_MSGS=(
                     "bad top"
                     "too long"
                    )


function check_result {
    local result=$1
    for msg in "${ERR_MSGS[@]}";do
        echo $result |grep "$msg"
        if [[ $? -eq "0" ]];then
            return 0
        fi
    done
    return 1
}

result=$(logrotate -d $LOGROTATE_CONFIG_PATH 2>&1)
check_result "$result"
delete=$?

if [[ $delete -eq "0" ]];then
    if [[ -f $LOGROTATE_STATUS ]];then
        rm -f $LOGROTATE_STATUS
    fi
    if [[ -f $LEGACY_LOGROTATE_STATUS ]];then
        rm -f $LEGACY_LOGROTATE_STATUS
    fi
fi


# TUNA-1758
LOGROTATE_TUNA_CONFIG_PATH="/etc/logrotate.tuna.d/"
LOGROTATE_TUNA_STATUS="/var/lib/logrotate/logrotate.tuna.status"

result=$(logrotate -s $LOGROTATE_TUNA_STATUS -d $LOGROTATE_TUNA_CONFIG_PATH 2>&1)
check_result "$result"
delete=$?

if [[ $delete -eq "0" ]];then
    if [[ -f $LOGROTATE_TUNA_STATUS ]];then
        rm -f $LOGROTATE_TUNA_STATUS
    fi
fi
