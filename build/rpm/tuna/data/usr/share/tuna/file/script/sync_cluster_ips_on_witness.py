#!/usr/local/venv/tuna/bin/python

import logging
from logging.handlers import RotatingFileHandler

from tuna.node import is_witness
from tuna.node.tool.sync_cluster import sync_cluster_ips


def setup_logger(log_name, log_file):
    formatter = logging.Formatter("[%(asctime)s: %(levelname)s] %(message)s")
    handler = RotatingFileHandler(log_file, maxBytes=5000000, backupCount=1)  # max 5mb
    handler.setFormatter(formatter)
    logger = logging.getLogger(log_name)
    logger.setLevel(logging.getLevelName(logging.INFO))
    logger.addHandler(handler)
    logger.propagate = False
    return logger


def sync_cluster_ips_on_witness():
    logger = setup_logger(log_name=None, log_file="/var/log/zbs/witness_sync_cluster_ips.log")
    if not is_witness():
        logger.info("not witness, skip sync cluster ips")
        return
    try:
        sync_cluster_ips()
    except Exception as e:
        logger.exception(e)


if __name__ == "__main__":
    sync_cluster_ips_on_witness()
