#!/usr/local/venv/tuna/bin/python

import logging
from logging.handlers import RotatingFileHandler
import os
import tempfile

from tuna.config.config_manager import ZBSConfig

MINUTES = [0, 15, 30, 45]
HOURS = [0, 1, 2, 3, 4, 5, 6, 23]
DAYS = ["Sat", "Sun"]
TOTAL = len(MINUTES) * len(HOURS) * len(DAYS)

RAID_CHECK_CRON_PATH = "/etc/cron.d/raid-check"
RAID_CHECK_CRON_CMD = "/usr/sbin/raid-check"
MD_CHECK_CRON_PATH = "/etc/cron.d/mdcheck"
MD_CHECK_CRON_CMD = '/usr/sbin/mdcheck --duration "5 hours"'
SHELL_PATH_CONF = "SHELL=/bin/bash\nPATH=/sbin:/bin:/usr/sbin:/usr/bin\n"

if os.path.exists(RAID_CHECK_CRON_PATH):
    CHECK_CRON_PATH = RAID_CHECK_CRON_PATH
    CHECK_CRON_CMD = RAID_CHECK_CRON_CMD
else:
    CHECK_CRON_PATH = MD_CHECK_CRON_PATH
    CHECK_CRON_CMD = MD_CHECK_CRON_CMD


def setup_logger(log_name, log_file):
    formatter = logging.Formatter("[%(asctime)s: %(levelname)s] %(message)s")
    handler = RotatingFileHandler(log_file, maxBytes=5000000, backupCount=1)  # max 5mb
    handler.setFormatter(formatter)
    logger = logging.getLogger(log_name)
    logger.setLevel(logging.getLevelName(logging.INFO))
    logger.addHandler(handler)
    logger.propagate = False
    return logger


def arrange_nodes(master_ips, cluster_ips):
    # Disperse storage evenly into master:
    # m1 m2 m3
    # m1 s1 m2 m3
    # m1 s1 s2 s3 m2 s4 s5 m3 s6 s7 m4 s8 s9 m5
    master_ips = sorted(master_ips)
    storage_ips = sorted([item for item in cluster_ips if item not in master_ips])
    if not storage_ips:
        return sorted(cluster_ips)

    res = []
    per_m_s = len(storage_ips) // (len(master_ips) - 1)
    ext_m_s = len(storage_ips) % (len(master_ips) - 1)
    while master_ips or storage_ips:
        res.append(master_ips.pop(0))
        for _ in range(per_m_s):
            if storage_ips:
                res.append(storage_ips.pop(0))
        if ext_m_s > 0:
            res.append(storage_ips.pop(0))
            ext_m_s -= 1

    return res


def generate_cron_date(index):
    day = index // (len(HOURS) * len(MINUTES))
    hour = index % (len(HOURS) * len(MINUTES)) // len(MINUTES)
    minute = index % len(MINUTES)
    return "{}{} {} * * {} root {}\n".format(SHELL_PATH_CONF, MINUTES[minute], HOURS[hour], DAYS[day], CHECK_CRON_CMD)


def shuffle_raid_check_cron():
    """
    shuffle raid-check's crond exec time to avoid several nodes exec raid-check at same time,
    which will cause zk fsync-ing wal high latency.
    """
    try:
        config = ZBSConfig()
        current_ip = config.get_data_ip()
        master_ips = sorted(config.get_master_ips())
        cluster_ips = sorted(config.get_cluster_storage_ips())
    except Exception as e:
        logging.info("error occurs when try to read cluster ips: {}".format(e))
        return

    if current_ip not in cluster_ips:
        logging.info("{} not belong to cluster storage ip".format(current_ip))
        return

    arranged_ips = arrange_nodes(master_ips, cluster_ips)
    current_index = int(float(arranged_ips.index(current_ip)) / (len(arranged_ips) - 1) * (TOTAL - 1))
    crond_content = generate_cron_date(current_index)

    try:
        with open(CHECK_CRON_PATH) as f:
            cur_content = f.read()
            if cur_content == crond_content:
                return

        # avoid emptying the config file when no disk space left
        with tempfile.NamedTemporaryFile(mode="w+", dir=os.path.dirname("/tmp/"), delete=False) as tmp_file:
            tmp_file.write(crond_content)
        file_mode = 0o644
        os.chmod(tmp_file.name, file_mode)
        os.rename(tmp_file.name, CHECK_CRON_PATH)
        logging.info("raid-check cron file updated with {}.".format(crond_content))
    except Exception as e:
        logging.info("error occurs when try to write cron file: {}".format(e))


if __name__ == "__main__":
    setup_logger(log_name=None, log_file="/var/log/zbs/generate_raid_check_crontable.log")
    shuffle_raid_check_cron()
