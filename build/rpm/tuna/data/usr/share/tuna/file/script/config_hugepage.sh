#!/usr/bin/env bash

cur=`dirname $0`

function config_hugepage {
    echo "config hugepage."
    echo "execute command: 'zbs-deploy-manage config_hugepage'"
    zbs-deploy-manage config_hugepage || exit 1
}

exec > >(tee -a /var/log/zbs/tuna-config-hugepage.log) 2>&1

echo "$(date) config hugepage start"

if [[ ! -f /etc/zbs/.zbsenabled ]]; then
    echo "cluster not deployed, do not config_hugepage."
    exit 0
fi
config_hugepage

echo "$(date) config hugepage finish"
