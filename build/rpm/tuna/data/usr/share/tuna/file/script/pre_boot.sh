#!/usr/bin/env bash

cur=`dirname $0`
. $cur/utils.sh


function config_core_pattern {
    if is_xen7 || is_incloud45; then
        # don't overwrite xenserver's sysctl.conf but set core pattern here
        echo "|/usr/share/tuna/file/script/coredump.sh %e %p %t" > /proc/sys/kernel/core_pattern
    fi
    return 0
}

function config_raid_devices {
    raid_device_state_paths=$(find /sys/devices/virtual/block/md*/md/dev-* -type f -name 'state')
    for path in ${raid_device_state_paths}; do
        echo "$(date) set failfast option for raid disk ${path}"
        echo 'failfast' > ${path}
        if [ -f ${path/state/block}/../queue/io_timeout ]; then
            echo "$(date) set ${path} io_timeout value to 10000ms"
            echo '10000' > ${path/state/block}/../queue/io_timeout
        elif [ -f ${path/state/block}/../device/timeout ]; then
            echo "$(date) set ${path} io_timeout value to 10s"
            echo '10' > ${path/state/block}/../device/timeout
        fi
    done
}

function restart_cgred {
    echo "sleep 120s before restart cgred.service"
    sleep 120

    echo "systemctl restart cgred.service"
    systemctl restart cgred.service
}

function tuning_kernel_parameters {
    echo "tuning kernel parameters..."

    # guarantee runtime for non-rt-tasks on all CPUs.
    echo "disable RT_RUNTIME_SHARE"
    echo NO_RT_RUNTIME_SHARE > /sys/kernel/debug/sched_features
}

echo "$(date) os pre init start"


if [[ ! -f /etc/zbs/.zbsenabled ]]; then
    config_network
fi
compare_and_modify_nic_config_macaddr
config_core_pattern
config_raid_devices
tuning_kernel_parameters
restart_cgred

echo "$(date) os pre init finish"
