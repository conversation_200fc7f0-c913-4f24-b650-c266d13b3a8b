#!/usr/bin/env bash

set -eo pipefail

# ---- functions ----
function adjust_time() {
    time=$2
    find "$1" -type f -print0 | while IFS= read -r -d '' file; do
        if ! mtime=$(stat -c "%Y" "$file"); then
            continue
        fi

        if [ "$mtime" -gt "$time" ]; then
            orig_time=$(date -d "@$mtime" "+%Y-%m-%d %H:%M:%S")
            if touch -m -d "@$time" "$file"; then
                new_time=$(date -d "@$(stat -c "%Y" "$file")" "+%Y-%m-%d %H:%M:%S")
                printf "%-45s %s -> %s\n" "$file" "$orig_time" "$new_time"
            fi
        fi
    done
}

# ---- main -----
echo "init time synchronize"

# 1. initial time sync
zbs-node init_sync_time

# 2. adjust time of systemd unit conf files if them ahead of current time...
current_time=$(date +%s)
adjust_time "/etc/systemd/system" $current_time

# 3. reload
systemctl daemon-reload

echo "systemd unit conf modify time adjust. Done"
