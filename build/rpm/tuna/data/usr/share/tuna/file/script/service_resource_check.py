#!/usr/local/venv/tuna/bin/python

import fcntl
import glob
import logging
from logging.handlers import RotatingFileHandler
import os
from subprocess import getstatusoutput
import sys
from time import sleep

from psutil import NoSuchProcess, Process
from systemd_dbus.exceptions import SystemdError
from systemd_dbus.manager import Manager as SystemdDBusManager

PID_LOCK = "/var/run/service_resource_check.pid"

SERVICE_MEMORY_LIMIT = {
    "elf-exporter.service": 0.5 * (1024**3),
    "tuna-exporter.service": 0.5 * (1024**3),
    "zbs-rest-server.service": 1 * (1024**3),
    "tuna-rest-server.service": 0.5 * (1024**3),
    "snmpd.service": 200 * (1024**2),
    "envoy.service": 400 * (1024**2),
    "elf-rest-server.service": 0.5 * (1024**3),
}

SERVICE_FD_LIMIT = {
    "elf-exporter.service": 800,
    "tuna-exporter.service": 800,
    "zbs-rest-server.service": 800,
    "tuna-rest-server.service": 800,
    "elf-rest-server.service": 800,
    "job-center-worker.service": 800,
    "octopus.service": 800,
    "siren.service": 800,
}

CONTAINER_SERVICES = {"envoy.service"}


def list_services_with_pinfo():
    units = []
    for service_name in list(set(SERVICE_MEMORY_LIMIT.keys()) | set(SERVICE_FD_LIMIT.keys())):
        service_info = ServiceInfo(service_name).info()
        if service_info is None:
            logging.warning("get service {} info failed".format(service_name))
            continue
        units.append(service_info)
        sleep(1)

    return units


class ServiceInfo:
    manager = SystemdDBusManager()

    def __init__(self, service_name):
        self.service_name = service_name

    def info(self) -> dict[str, int | str | list[dict]] | None:
        unit = self.get_unit_info()
        if not unit:
            return None

        pinfo = self.get_process_info(unit["current_pid"])
        if not pinfo:
            return None

        if self.service_name in CONTAINER_SERVICES:
            self.append_detached_container_process_info(pinfo)

        unit.update(pinfo)
        return unit

    def get_unit_info(self):
        try:
            unit = self.manager.get_service(self.service_name)
            if unit is None:
                return None

            properties = unit.properties
            return {
                "service_name": str(properties.Id),
                "current_pid": int(properties.MainPID),
            }
        except SystemdError as e:
            if e.name == "NoSuchUnit":
                logging.info("Service {} is disabled".format(self.service_name))
            else:
                logging.exception("Error on get {} unit info: {}".format(self.service_name, e))
            return None
        except Exception as e:
            logging.exception("Error on get {} unit info: {}".format(self.service_name, e))
            return None

    def get_process_info(self, pid):
        try:
            p = Process(pid)
        except NoSuchProcess:
            logging.info("Process {} does not exist.".format(pid))
            return None

        try:
            memory_info = p.memory_info()
            vms = memory_info.vms
            rss = memory_info.rss
            shared = memory_info.shared
            num_fds = p.num_fds()
            name = p.name()
        except NoSuchProcess:
            logging.info("Process %s does not exist when get resource_usage." % p.pid)
            return None

        child_list = []
        for child in p.children(recursive=True):
            try:
                child_memory_info = child.memory_info()
                vms += child_memory_info.vms
                rss += child_memory_info.rss
                shared += child_memory_info.shared
                child_num_fds = child.num_fds()
                child_list.append(
                    {
                        "pid": child.pid,
                        "name": child.name(),
                        "vms": child_memory_info.vms,
                        "rss": child_memory_info.rss,
                        "shared": child_memory_info.shared,
                        "num_fds": child_num_fds,
                    }
                )
            except NoSuchProcess:
                logging.info("Child process {} does not exist.".format(child.pid))
                continue

        return {
            "pid": pid,
            "name": name,
            "vms": vms,
            "rss": rss,
            "shared": shared,
            "num_fds": num_fds,
            "child_list": child_list,
        }

    def append_detached_container_process_info(self, pinfo):
        try:
            detachContainerPID = -1
            with open("/run/{}-pid".format(self.service_name)) as f:
                detachContainerPID = int(f.read().strip())
            if detachContainerPID > 1:
                containerProcInfo = self.get_process_info(detachContainerPID)
                if containerProcInfo:
                    # if process is conmon, ignore it
                    if containerProcInfo["name"] == "conmon":
                        return

                    pinfo["vms"] += containerProcInfo["vms"]
                    pinfo["rss"] += containerProcInfo["rss"]
                    pinfo["shared"] += containerProcInfo["shared"]
                    pinfo["num_fds"] += containerProcInfo["num_fds"]
                    pinfo["child_list"].extend(containerProcInfo["child_list"])
        except Exception as e:
            logging.warning(
                "failed to get resources of detached container of service {}: {}".format(self.service_name, e)
            )


def setup_logger(log_name, log_file):
    formatter = logging.Formatter("[%(asctime)s: %(levelname)s] %(message)s")
    handler = RotatingFileHandler(log_file, maxBytes=50000000, backupCount=1)  # max 50mb
    handler.setFormatter(formatter)
    logger = logging.getLogger(log_name)
    logger.setLevel(logging.getLevelName(logging.INFO))
    logger.addHandler(handler)
    logger.propagate = False
    return logger


def acquire_lock(fd):
    try:
        fcntl.lockf(fd, fcntl.LOCK_EX | fcntl.LOCK_NB)
        return True
    except Exception as e:
        logging.error("failed to acquire lock {}: {}".format(PID_LOCK, e))
        return False


def restart_service(service_name):
    cmd = "systemctl status {}".format(service_name)
    _, output = getstatusoutput(cmd)
    logging.info("{}\n{}".format(cmd, output))

    for _ in range(3):
        cmd = "systemctl restart {}".format(service_name)
        logging.info(cmd)
        res, output = getstatusoutput(cmd)
        if res == 0:
            break
        logging.info("{}\n{}".format(cmd, output))

    cmd = "systemctl status {}".format(service_name)
    _, output = getstatusoutput(cmd)
    logging.info("{}\n{}".format(cmd, output))


def check_service_resource():
    service_list = list_services_with_pinfo()
    service_list.sort(key=lambda item: item["service_name"])

    for unit in service_list:
        service_name = unit.get("service_name")
        logging.info(
            "{} {} vms:{} sms:{} rss:{} fds:{}".format(
                service_name,
                "[{}]".format(unit["current_pid"]),
                unit["vms"],
                unit["shared"],
                unit["rss"],
                unit["num_fds"],
            )
        )
        for child in unit.get("child_list"):
            child_name = child["name"]
            logging.info(
                "{} {} vms:{} sms:{} rss:{} fds:{}".format(
                    "child: {}".format(child_name),
                    "[{}]".format(child["pid"]),
                    child["vms"],
                    child["shared"],
                    child["rss"],
                    child["num_fds"],
                )
            )

        memory_limit = SERVICE_MEMORY_LIMIT.get(service_name)
        if memory_limit and unit["rss"] > memory_limit:
            logging.info("service {} exceed memory limit {}".format(service_name, memory_limit))
            restart_service(service_name)
            continue

        fd_limit = SERVICE_FD_LIMIT.get(service_name)
        if fd_limit and unit["num_fds"] > fd_limit:
            logging.info("service {} exceed fd limit {}".format(service_name, fd_limit))
            restart_service(service_name)
            continue
        elif fd_limit and any(child["num_fds"] > fd_limit for child in unit.get("child_list", [])):
            logging.info("service {} child exceed fd limit {}".format(service_name, fd_limit))
            restart_service(service_name)
            continue


def clean_old_file():
    # service_memory_check rename to service_resource_check

    old_pid_file = "/var/run/service_memory_check.pid"
    if os.path.exists(old_pid_file):
        os.remove(old_pid_file)
        logging.info("pid file removed: {}".format(old_pid_file))

    for old_log_file in glob.glob("/var/log/zbs/service_memory_check.*"):
        os.remove(old_log_file)
        logging.info("log file removed: {}".format(old_log_file))


def main():
    with open(PID_LOCK, "w") as fd:
        if not acquire_lock(fd):
            sys.exit(1)
        fd.write(str(os.getpid()))
        clean_old_file()
        check_service_resource()


if __name__ == "__main__":
    setup_logger(log_name=None, log_file="/var/log/zbs/service_resource_check.log")
    try:
        main()
    except Exception as ex:
        logging.exception(ex)
