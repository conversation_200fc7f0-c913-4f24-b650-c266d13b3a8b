#!/bin/bash
exec 1> >(logger -s -t $(basename $0)) 2>&1

systemctl start zbs-chunkd
sleep 60

nfs_exports=`zbs-nfs export list | tail -n+3 | awk '{print $2}'`
echo "nfs_exports=${nfs_exports[@]}"

while read mount_export mount_point; do
    echo "umount -fl $mount_point"
    umount -fl $mount_point
done <<< "$(mount | grep ":/zbs" |  awk '{print $1, $3}')"

zbs_mounts=$(mount | grep ":/zbs" | awk '{print $1, $3}')
echo "zbs mount points: $zbs_mounts "
