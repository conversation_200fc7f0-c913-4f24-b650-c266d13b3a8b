#!/usr/bin/env bash

DESCRIPTION="This script will collect info and log to /root/env_info dir."

BASE_DIR="/root/env_info"
INFO_OUT_FILE="${BASE_DIR}/env.txt"
DB_DUMP_DIR="${BASE_DIR}/db"
CFG_DUMP_DIR="${BASE_DIR}/config"

ZBS_LOG_DIR="/var/log/zbs/"
SYS_LOG="/var/log/messages"

PACKAGES_TO_SHOW="
 zbs
 libzbs
 pyzbs
 fisheye
 elfvirt
 job-center
 pyzbs
"


SERVICES="
 zbs-metad
 zbs-chunkd
 job-center-scheduler
 job-center-worker
 fisheye-collectd
 zbs-deploy-server
 zbs-rest-server
 tuna-rest-server
 elf-rest-server
 mongod
 zookeeper
elf-vm-monitor
"

DATABASES="
 auth
 coral_fish
 fish_eye
 job_center
 jobs
 network
 resources
 smartx
"

function run(){
    ${@} 2>&1 | tee -a ${INFO_OUT_FILE}
}

function h1(){
    run echo ""
    run echo "=========${1}========="
    run echo ""
}

function h2(){
    run echo ""
    run echo "====${1}===="
    run echo ""
}

function initialize(){
    h1 "Do initialization..."
    run rm -fr "${BASE_DIR}/*"
    run mkdir ${BASE_DIR}
    run mkdir ${DB_DUMP_DIR}
    run mkdir ${CFG_DUMP_DIR}
    run echo "" > ${INFO_OUT_FILE}
    h1 "Initialization done"
}

function get_package_info(){
    h1 "Try collecting package information"

    for package in ${PACKAGES_TO_SHOW}; do
        h2 "package ${package} info"
        run rpm -qi --changelog ${package}
    done

    h1 "end collecting package information"
    h1 "Collecting package list"
    run rpm -qa
    h1 "End collecting package list"
}

function dump_logs(){
    h1 "Start packing logs"
    run tar czf "${BASE_DIR}/zbs-logs.tgz" ${ZBS_LOG_DIR}
    run tar czf "${BASE_DIR}/system_logs.tgz" ${SYS_LOG}
    h1 "End packing logs"
}

function get_service_status(){
    h1 "Start collecting service status"
    for service_name in ${SERVICES}; do
        h2 "Service: ${service_name}"
        run systemctl status ${service_name}
    done
    h1 "End collecting service status"
}


function mongo_status(){
    h1 "Start collecting mongo status"
    run zbs-node mongo status
    h1 "End collecting mongo status"
}

function dump_mongo(){
    h1 "Start dumping mongodb"
    mongo_leader=`zbs-node mongo leader`
    for db_name in ${DATABASES};do
        h2 "Dumping: ${db_name}"
        dump_path="${DB_DUMP_DIR}/${db_name}"
        run mongodump -h ${mongo_leader} -d ${db_name} -o ${dump_path}
        run tar czf "${DB_DUMP_DIR}/mongo_${db_name}.tgz" ${dump_path}
        run rm -fr ${dump_path}
    done
    h1 "End dumping mongodb"
}

function get_zbs_info(){
    h1 "Start collecting ZBS cluster info"
    h2 "cluster summary"
    run zbs-meta cluster summary
    h2 "chunk list"
    run zbs-meta chunk list
    h2 "migrate list"
    run zbs-meta migrating list
    h2 "recover list"
    run zbs-meta recover list
    h2 "chunk partition list"
    h1 "End collecting ZBS cluster info"
}

function get_local_zbs_info(){
    h1 "Start collecting local ZBS cluster info"
    run zbs-chunk partition list
    h2 "chunk cache list"
    run zbs-chunk cache list
    h2 "chunk journal list"
    run zbs-chunk journal list
    h2 "zbs nfs list"
    run zbs-nfs export list
    h1 "Start collecting local ZBS cluster info"
}

function get_config_files(){
    h1 "Start collecting config files"
    run tar czf "${CFG_DUMP_DIR}/sys_zbs_metad.tgz" /etc/sysconfig/zbs-metad
    run tar czf "${CFG_DUMP_DIR}/sys_zbs_chunkd.tgz" /etc/sysconfig/zbs-chunkd
    run cp -fR /etc/zbs "${CFG_DUMP_DIR}/zbs"
    run cp -fR /etc/zookeeper "${CFG_DUMP_DIR}/zookeeper"
    h1 "End collection config files"
}

function main(){
    if [ "${1}" != "info" ] && [ "${1}" != "all" ]
    then
        echo "Use 'collect_evn_info.sh all' (with mongodb dump)"
        echo " or 'collect_evn_info.sh info' (without mongodb dump)"
    else
        initialize
        get_package_info
        get_config_files
        dump_logs
        get_service_status
        mongo_status
        get_zbs_info
        get_local_zbs_info
        if [ "${1}" == "all" ]
        then
            dump_mongo
        fi
        echo "All data stored to /root/env_info/"
    fi
}

main ${@}