#!/usr/bin/env bash

LOGROTATE_SMARTX_CONFIG_PATH="/etc/logrotate.smartx.d"
LOGROTATE_TUNA_CONFIG_PATH="/etc/logrotate.tuna.d"
LOGROTATE_STATUS="/var/lib/logrotate/logrotate.tuna.status"

if [ -d "$LOGROTATE_SMARTX_CONFIG_PATH" ]; then
    for conf in `ls $LOGROTATE_SMARTX_CONFIG_PATH`;do
        logrotate -s $LOGROTATE_STATUS $LOGROTATE_SMARTX_CONFIG_PATH/$conf
    done
fi

if [ -d "$LOGROTATE_TUNA_CONFIG_PATH" ]; then
    for conf in `ls $LOGROTATE_TUNA_CONFIG_PATH`;do
        logrotate -s $LOGROTATE_STATUS $LOGROTATE_TUNA_CONFIG_PATH/$conf
    done
fi
