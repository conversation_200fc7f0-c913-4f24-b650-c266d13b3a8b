#!/usr/bin/env bash

cur=`dirname $0`
. $cur/utils.sh

function set_nginx_conf {
    # Nginx has no default configuration at /etc/nginx/nginx.conf after version 1.17,
    # so we re-config nginx.conf when deploy rpm install.

    rm -rf /etc/nginx/conf.d/default.conf
    cat > /etc/nginx/nginx.conf << 'EOF'

# For more information on configuration, see:
#   * Official English Documentation: http://nginx.org/en/docs/
#   * Official Russian Documentation: http://nginx.org/ru/docs/

user nginx;
worker_processes 2;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    sendfile            on;
    tcp_nopush          on;
    tcp_nodelay         on;
    keepalive_timeout   65;
    types_hash_max_size 2048;

    server_tokens       off;

    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;

    # Load modular configuration files from the /etc/nginx/conf.d directory.
    # See http://nginx.org/en/docs/ngx_core_module.html#include
    # for more information.
    include /etc/nginx/conf.d/*.conf;

    server {
        listen       80;
        listen       [::]:80 default_server;
        server_name  ~^(\d+\.\d+\.\d+\.\d+)$ localhost;
        root         /usr/share/nginx/html;

        # Load configuration files for the default server block.
        include /etc/nginx/default.d/*.conf;

        location / {
        }

        error_page 404 /404.html;
            location = /40x.html {
        }

        error_page 500 502 503 504 /50x.html;
            location = /50x.html {
        }
    }

# Settings for a TLS enabled server.
#
#    server {
#        listen       443 ssl http2 default_server;
#        listen       [::]:443 ssl http2 default_server;
#        server_name  _;
#        root         /usr/share/nginx/html;
#
#        ssl_certificate "/etc/pki/nginx/server.crt";
#        ssl_certificate_key "/etc/pki/nginx/private/server.key";
#        ssl_session_cache shared:SSL:1m;
#        ssl_session_timeout  10m;
#        ssl_ciphers HIGH:!aNULL:!MD5;
#        ssl_prefer_server_ciphers on;
#
#        # Load configuration files for the default server block.
#        include /etc/nginx/default.d/*.conf;
#
#        location / {
#        }
#
#        error_page 404 /404.html;
#            location = /40x.html {
#        }
#
#        error_page 500 502 503 504 /50x.html;
#            location = /50x.html {
#        }
#    }

}

EOF

    # This conf should be updated as closer as nginx.conf to avoid non-consistent issues
    # during cluster upgrading. So it should not be installed from RPM directly.
    cat > /etc/nginx/conf.d/invalid_host_block.conf << 'EOF'
server {
    listen 80 default_server;
    server_name _;

    # Not Allowed
    return 405;
}
EOF

}

function config_nginx {
    # config static files
    sed '/\s*location \/ {.*/r'<(
        echo "            root   /usr/share/fisheye;"
        echo '            add_header Cache-Control "no-store, no-cache, must-revalidate";'
        echo '            add_header Pragma "no-cache";'
        echo "            add_header Expires 0;"
        echo "            index  index.html;"
    ) -i -- /etc/nginx/nginx.conf

    if is_xen7 || is_incloud45; then
        # set nginx to 8080
        pattern=".*listen .* 80 .*default_server;"
        line="        listen       8080 default_server;"
        sed -i "s/${pattern}/${line}/g" /etc/nginx/nginx.conf
        pattern=".*listen .*:80 .*default_server;"
        line="        listen       [::]:8080 default_server;"
        sed -i "s/${pattern}/${line}/g" /etc/nginx/nginx.conf
    fi

    # set nginx listen to ipv6
    if ! is_systemd; then
        pattern=".*listen .*80.*default_server;"
        if is_xen7 || is_incloud45; then
            line="    listen       [::]8080 default_server;"
        else
            line="    listen       [::]80 default_server;"
        fi
        sed -i "s/${pattern}/${line}/g" /etc/nginx/conf.d/default.conf
        service nginx restart
        chkconfig nginx on
    else
        set_restart_on_failure_for_nginx
    fi
}

function config_selinux {
    # set selinux to disabled
    setenforce 0
    sed -i "s/SELINUX=.*/SELINUX=disabled/g" /etc/selinux/config
}


function config_firewall {
    # stop & disable firewall
    # if is_incloud45; then
    #     return
    # fi
    if is_systemd; then
        systemctl stop firewalld
        systemctl disable firewalld
        systemctl stop iptables
        systemctl disable iptables
    else
        service iptables stop
        service ip6tables stop
        chkconfig iptables off
        chkconfig ip6tables off
    fi
}

function check_disk_rotational_info {
    # check disk rotational infos
    zbs-node check_disks_rotational_infos
}


function check_disk_health {
    zbs-node smart test --force
}

function start_deploy_server {
    if is_systemd; then
        systemctl restart zbs-deploy-server
        systemctl enable zbs-deploy-server
    else
        restart zbs-deploy-server
    fi
}

function enable_os_pre_boot_service {
    if is_systemd; then
        systemctl enable fisheye-pre-boot
    else
        rm -f /etc/init/fisheye-pre-boot.conf.override
    fi

}

# As tuna obsoletes smartx-tuna package in upgrade stage,
# yum update tuna will install tuna package and erase smartx-tuna,
# this script will be executed in upgrade stage which includes
# cluster-upgrader service restart logic.
# So we return directly when /etc/zbs/.zbsenabled exists which
# is used to indicate the cluster already was deployed.
if [[ -f /etc/zbs/.zbsenabled ]]; then
    exit 0
fi

# modify system config
set_nginx_conf
config_nginx
config_selinux
config_firewall
config_network

update_nginx_logrotate_conf
check_disk_health
check_disk_rotational_info
start_deploy_server
enable_os_pre_boot_service
rm_old_pyc_file
zbs-deploy-manage create_local_ssl_certificate 2>&1 | tee /log
systemctl restart nginx
add_post_action_for_containerd
