#!/usr/bin/env bash

function is_systemd {
    if [[ -f /usr/bin/systemctl ]]; then
        return 0
    else
        return 1
    fi
}

function is_incloud45 {
    if [[ -f /etc/redhat-release ]]; then
        if grep -q "InCloud Sphere release 4.5" /etc/redhat-release; then
            return 0
        else
            return 1
        fi
    fi
    return 1
}

function is_xen7() {
  if [[ -f /etc/redhat-release ]]; then
      if grep -iq "XenServer release 7" /etc/redhat-release; then
          return 0
      else
        return 1
      fi
  fi
  return 1
}

# network
function get_all_ifaces {
    local ifaces=()
    local iface
    for i in /sys/class/net/*; do
        if [[ ! -h $i ]]; then
            continue
        fi
        iface=$(basename $i)
        ifaces+=($iface)
    done
    echo ${ifaces[@]}
}

function get_physical_ifaces {
    local ifaces=($(get_all_ifaces))
    local physical_ifaces=()
    local iface
    for iface in ${ifaces[@]}; do
        if readlink /sys/class/net/$iface | grep -q "virtual"; then
            continue
        fi
        physical_ifaces+=($iface)
    done
    echo ${physical_ifaces[@]}
}

function config_network {
    if is_xen7 || is_incloud45; then
        return
    fi
    if is_systemd; then
        systemctl stop NetworkManager
        systemctl disable NetworkManager
    fi
    ifaces=($(get_physical_ifaces))
    for iface in ${ifaces[@]}; do
        ifup $iface
        # If the nic configuration file does not exist, ifup will fail to execute,
        # use the ip command to set the nic status
        ip link set $iface up
    done
}

function compare_and_modify_nic_config_macaddr {
    declare -A interface_mac
    while read -r line; do
        # get link name
        interface=$(echo "$line" | awk '{print $2}' | tr -d ':')
        # find "link/" next，which is mac
        mac=$(echo "$line" | awk -F'link' '{print $2}' | awk '{print $2}')
        # skip empty
        if [[ -z "$interface" || -z "$mac" ]]; then
            continue
        fi
        interface_mac["$interface"]=$mac
    done < <(ip -0 -o link show)

    for ifcfg in /etc/sysconfig/network-scripts/ifcfg-*; do
        if [ -f "$ifcfg" ]; then
            # find config value
            device=$(grep -i '^DEVICE=' "$ifcfg" | cut -d= -f2)
            hwaddr=$(grep -i '^HWADDR=' "$ifcfg" | cut -d= -f2)

            if [ -n "$device" ] && [ -n "${interface_mac[$device]}" ]; then
                current_mac=${interface_mac[$device]}

                # if mac not equal update
                if [ "$hwaddr" != "$current_mac" ]; then
                    echo "$(date) update $ifcfg , old_mac $hwaddr , new_mac $current_mac"
                    sed -i "s/^HWADDR=.*/HWADDR=$current_mac/" "$ifcfg"
                    ifup $device
                fi
            fi
        fi
    done
}

function set_restart_on_failure_for_nginx {
    if is_systemd; then
        if [ -z "$(grep 'Restart=on-failure' /usr/lib/systemd/system/nginx.service)" ]; then
            sed -i '/ExecStart=/ a Restart=on-failure' /usr/lib/systemd/system/nginx.service
        fi
        systemctl daemon-reload
        systemctl restart nginx
        systemctl enable nginx
    fi
}

function update_nginx_logrotate_conf {
cat > /etc/logrotate.d/nginx << EOF
/var/log/nginx/*log {
    create 0644 nginx nginx
    size 100M
    rotate 2
    nodateext
    missingok
    notifempty
    compress
    sharedscripts
    postrotate
        /bin/kill -USR1 \`cat /run/nginx.pid 2>/dev/null\` 2>/dev/null || true
    endscript
}
EOF

if [[ -f /etc/logrotate.d/nginx.rpmnew ]]; then
    rm -rf /etc/logrotate.d/nginx.rpmnew
fi

if [[ -f /etc/logrotate.d/nginx.rpmsave ]]; then
    rm -rf /etc/logrotate.d/nginx.rpmsave
fi

}

function rm_old_pyc_file {
    find /usr/lib/python2.7  -name "*.py[c|o]" -exec rm -f {} \;
    find /usr/lib64  -name "*.py[c|o]" -exec rm -f {} \;

    if [ -d /usr/share/zbs_deploy ]; then
        find /usr/share/zbs_deploy -name "*.py[c|o]" -exec rm -f {} \;
    fi

    if [ -d /usr/share/tuna ]; then
        find /usr/share/tuna/file/script -name "*.py[c|o]" -exec rm -f {} \;
    fi
}


function add_post_action_for_containerd {
    CONTAINERD_SERVICE="/usr/lib/systemd/system/containerd.service"

    if [ -n "$(grep 'ExecStartPost=' $CONTAINERD_SERVICE)" ]; then
        sed -i '/ExecStartPost=/d' $CONTAINERD_SERVICE
    fi

    sed -i '/ExecStart=/a ExecStartPost=/usr/bin/chmod 0666 /run/containerd/containerd.sock' $CONTAINERD_SERVICE
}
