BLKDISCARD=True
LSM_VERSION=lsm2
IGNORE_SCAN_KVM_HOST=True
IGNORE_SCAN_XEN_HOST=True
IGNORE_SCAN_VMWARE_HOST=True
# GRUB_DUPLICATE_KEY_WHITELIST support multi values, please sperate with space.
# e.g. GRUB_DUPLICATE_KEY_WHITELIST=console fake_key
GRUB_DUPLICATE_KEY_WHITELIST=console


# Enable or disable cluster extend checks in test environment
# Configuration keys directly match method names in CheckNode class
# All check functions are disabled by default

## ZBS component checks
check_node.check_disk_count=False
check_node.check_disk_size=False
check_node.check_zbs_spec=False
check_node.check_zbs_spec_valid=False
check_node.check_disk_type=False
check_node.check_chunk_instance=False
check_node.check_capacity_limit=False
check_node.check_chunk_instance_count_in_meta=False

## Common checks
check_node.check_cgroup=False
check_node.check_hostname=False
check_node.check_host_pwd=False
check_node.check_ip=False
check_node.check_ipmi_account=False
check_node.check_rdma=False
check_node.check_nvme_over_rdma=False
check_node.check_nvme_over_tcp=False
check_node.check_static_route_params=False