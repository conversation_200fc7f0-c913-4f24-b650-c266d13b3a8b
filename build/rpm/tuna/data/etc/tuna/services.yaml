# service definition
# name: service name
# product: the product list that the service supports,
#          indicating that the service can run on these products,
#          currently supports SMTXOS, SMTXZBS, SMTXELF
# platform: the platform list that the service supports,
#           indicating that the service can run on these platforms,
#           currently supports kvm, vmware, xenserver, san, elf
# arch: the arch list that the service supports,
#       indicating that the service can run on these archs,
#       currently supports x86_64, aarch64
# role: the role list that the service supports,
#       indicating that the service can run on these roles,
#       currently supports master, storage, witness
# labels: the label list that the service depends on,
#         indicating that the service depends on one of these labels,
#         the current node meets the product, platform, arch, role conditions,
#         and the node has the one of corresponding labels, the service can run
services:
- name: containerd
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  - witness
  labels:
- name: vnc-proxy
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: zbs-rest-server
  product:
  - SMTXOS
  - SMTXZBS
  platform:
  - kvm
  - san
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  - witness
  labels:
- name: tuna-rest-server
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  - witness
  labels:
- name: elf-rest-server
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: tuna-exporter
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  - witness
  labels:
- name: svcresctld
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  - witness
  labels:
- name: cluster-upgrader
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: cloudtower-installer
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: nginx
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  - witness
  labels:
- name: rsyslog
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: oscar
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: aquarium
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  - witness
  labels:
- name: harbor
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  - witness
  labels:
- name: crab
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  - witness
  labels:
- name: octopus
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: misc-exporter
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  - witness
  labels:
- name: siren
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: zookeeper
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - witness
  labels:
- name: mongod
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - witness
  labels:
- name: master-monitor
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - witness
  labels:
- name: libvirtd
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: elf-vm-monitor
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: elf-vm-watchdog
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: elf-exporter
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: dnsmasq
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: elf-vm-scheduler
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  arch:
  - x86_64
  - aarch64
  role:
  - master
  labels:
- name: job-center-worker
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: job-center-scheduler
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  labels:
- name: elf-dhcp
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  arch:
  - x86_64
  - aarch64
  role:
  - master
  labels:
- name: timemachine
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: zbs-metad
  product:
  - SMTXOS
  - SMTXZBS
  platform:
  - kvm
  - san
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  labels:
- name: zbs-iscsi-redirectord
  product:
  - SMTXOS
  - SMTXZBS
  platform:
  - kvm
  - san
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: zbs-taskd
  product:
  - SMTXOS
  - SMTXZBS
  platform:
  - kvm
  - san
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: zbs-chunkd
  product:
  - SMTXOS
  - SMTXZBS
  platform:
  - kvm
  - san
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: systemd-journald
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  - witness
  labels:
- name: log-collector
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  - witness
  labels:
- name: vmtools-agent
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: consul-server
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - witness
  labels:
- name: consul
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  - witness
  labels:
- name: envoy
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  - witness
  labels:
- name: envoy-xds
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  - witness
  labels:
- name: network-monitor
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  - witness
  labels:
- name: disk-healthd
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: sd-offline
  product:
  - SMTXOS
  - SMTXZBS
  platform:
  - kvm
  - san
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: usbredir-manager
  product:
  - SMTXOS
  - SMTXELF
  - SMTXZBS
  platform:
  - kvm
  - elf
  - san
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: zbs-watchdogd
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  - witness
  labels:
- name: seal
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  labels:
- name: fluent-bit
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  - witness
  labels:
- name: zbs-aurora-monitord
  product:
  - SMTXOS
  - SMTXZBS
  platform:
  - kvm
  - san
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
  - vhost_enabled
- name: zbs-inspectord
  product:
  - SMTXOS
  - SMTXZBS
  platform:
  - kvm
  - san
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: elf-fs
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: vm-security-controller
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: lldpd
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  - witness
  labels:
- name: netreactor
  product:
  - SMTXOS
  - SMTXZBS
  platform:
  - kvm
  - san
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  labels:
- name: vipservice
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: network-firewall
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: net-health-check
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: l2ping@storage
  product:
  - SMTXOS
  - SMTXZBS
  platform:
  - kvm
  - san
  - vmware
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
  - rdma_enabled
  - elf_component_enabled
- name: l2ping@access
  product:
  - SMTXOS
  - SMTXZBS
  platform:
  - kvm
  - san
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
  - l2ping_access_network
- name: netguard
  product:
  - SMTXOS
  - SMTXZBS
  platform:
  - kvm
  - san
  - vmware
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
- name: ntpm
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - vmware
  - xenserver
  - san
  - elf
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  - witness
  labels:
- name: zbs-deploy-server
  product:
  - SMTXOS
  - SMTXZBS
  - SMTXELF
  platform:
  - kvm
  - san
  - elf
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  - witness
  labels:

# dolphin depends on nfs export, should after zbs services
- name: dolphin
  product:
  - SMTXOS
  - SMTXZBS
  platform:
  - kvm
  - san
  - vmware
  - xenserver
  arch:
  - x86_64
  - aarch64
  role:
  - master
  - storage
  labels:
