# This configuration file is mainly to filter out USB devices that do not need to be displayed.
# If you need to customize the filter list, please add it according to the format,
# and then restart the job-center service.
#
# - vendor_id is required, indicating the manufacturer of the usb device that needs to be filtered,
# and the way to view Vendor_id is:
# $ lsusb
#  Bus 001 Device 002: ID 03f0:2927 Hewlett-Packard
#  Bus 001 Device 001: ID 1d6b:0002 Linux Foundation 2.0 root hub
#  Bus 003 Device 069: ID 125f:312a A-DATA Technology Co., Ltd. Superior S102
#  Bus 003 Device 076: ID 0781:5583 SanDisk Corp. Ultra Fit
#
# 03f0 is the vendor_id of device 002, it's product id is 2927.
#
# - vendor: not required.
# - product_ids: required if you only want to filter the specified devices .
#
# Remember to restart the job-center service after modified this file.

- vendor_id: "1D6B"
  vendor: "Linux"
- vendor_id: "46F4"
  vendor: "Qemu"
- vendor_id: "0627"
  vendor: "Adomax Technology Co., Ltd"
- vendor_id: "046B"
  vendor: "American Megatrends, Inc."
- vendor_id: "0624"
  vendor: "Avocent Corp."
- vendor_id: "0557"
  vendor: "ATEN International Co., Ltd"
- vendor_id: "0EA0"
  vendor: "Ours Technology, Inc."
- vendor_id: "17EF"
  vendor: "Lenovo"
  product_ids:
    - "B005"
    - "B000"
    - "B001"
- vendor_id: "04B3"
  vendor: "IBM Corp."
  product_ids:
    - "4010"
- vendor_id: "0BDA"
  vendor: "Realtek Semiconductor Corp."
  product_ids:
    - "0329"
- vendor_id: "05E3"
  vendor: "Genesys Logic, Inc."
- vendor_id: "03F0"
  vendor: "Hewlett-Packard"
  product_ids:
    - "2927"
- vendor_id: "413C"
  vendor: "Dell Computer Corp."
  product_ids:
    - "0000"
    - "0001"
    - "0002"
    - "0006"
    - "a102"
- vendor_id: "0525"
  vendor: "Netchip Technology, Inc."
  product_ids:
    - "A4A5"
