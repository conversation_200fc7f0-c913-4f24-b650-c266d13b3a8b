location ~ /api/v2/logs {
    proxy_pass http://127.0.0.1:10406;
    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

    add_header 'Access-Control-Allow-Headers' 'X-Requested-With, Content-Type, content-length, Cache-Control, Pragma, If-Modified-Since';
    add_header Access-Control-Allow-Origin *;
    break;
}


location ~ /api/v2/deployment/deploy_(version|config) {
    proxy_pass http://127.0.0.1:10411;
    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    break;
}

location ~ /api/v2/management/cluster_extend/.* {
    proxy_pass http://127.0.0.1:10403;
    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_read_timeout 300s;

    add_header 'Access-Control-Allow-Headers' 'X-Requested-With, Content-Type, Cache-Control, Pragma, If-Modified-Since, X-SmartX-Token, Http-Metadata-Token';
    add_header Access-Control-Allow-Origin *;
    break;
}

location ~ /api/v2/deployment/.* {
    proxy_pass http://127.0.0.1:10403;
    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_read_timeout 300s;

    add_header 'Access-Control-Allow-Headers' 'X-Requested-With, Content-Type, Cache-Control, Pragma, If-Modified-Since, X-SmartX-Token, Http-Metadata-Token';
    add_header Access-Control-Allow-Origin *;
    break;
}

location ~ /api/v2/ws {
    proxy_pass http://127.0.0.1:10402;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header Host $host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    break;
}

location ~ /api/v2/services/* {
    proxy_pass http://127.0.0.1:10413;
    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    break;
}

location ~ /api/v2/(management|tools|cluster|vmware|vsphere|xenserver|ipmi|setting|tuna|rack_topos|network|jobs|job_center)/* {
    proxy_pass http://127.0.0.1:10411;
    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    break;
}

location ~ /api/v2/(vms/*|vm_templates/*|vm_snapshots/*|vm_additional_info/*|query_vm_additional_info/*|list_vms_uuid/*|check_vm_unique_name/*|volumes/*|volume_snapshots/*|volume_templates/*|volume_collection/*|resources/*|attributes/*|files/*|folders/*|data_ports/*|parse_ovf/*|passthroughs/*|placement/*|groups/*|random_ssh_key/*|migrate_across_cluster/*|storage_policies/*|images/*|svt_image|mirror_vms/*|compute/.*|elf/.*|iscsi_volume/.*|batch/.*) {
    proxy_pass http://127.0.0.1:10412;
    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    break;
}

location ~ /api/.* {
    proxy_pass http://127.0.0.1:10402;
    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    break;
}

location ~ /websockify {
    set $h $http_host;
    set $p http://127.0.0.1:8000;
    if ($arg_host) {
        set $h $arg_host;
        set $p http://$h;
        set $args "uuid=$arg_uuid&token=$arg_token";
    }
    proxy_pass       $p;
    proxy_set_header Host $h;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";

    # VNC connection timeout
    proxy_read_timeout 1d;

    # Disable cache
    proxy_buffering off;

    break;
}
