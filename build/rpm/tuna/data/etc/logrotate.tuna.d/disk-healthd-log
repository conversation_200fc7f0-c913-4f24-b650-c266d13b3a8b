/var/log/zbs/disk-healthd/disk-healthd.log {
    rotate 2
    size 100M
    nodateext
    compress
    missingok
    notifempty
    create 0644 root root
    su root root
    postrotate
        /bin/kill -USR1 `cat /var/run/disk-healthd.pid 2>/dev/null` 2> /dev/null|| true
    endscript
}

/var/log/zbs/disk-healthd/rest-access.log {
    rotate 1
    size 100M
    nodateext
    compress
    missingok
    notifempty
    create 0644 root root
    su root root
    postrotate
        /bin/kill -USR1 `cat /var/run/disk-healthd.pid 2>/dev/null` 2> /dev/null|| true
    endscript
}
