/var/log/zbs/log-collector.INFO {
    rotate 1
    size 10M
    nodateext
    compress
    missingok
    notifempty
    olddir archive
    create 0644 root root
    su root root
}

/var/log/zbs/log-collector-rest.INFO {
    rotate 1
    size 10M
    nodateext
    compress
    missingok
    notifempty
    olddir archive
    create 0644 root root
    su root root
    postrotate
        /bin/kill -USR1 `cat /var/run/log_collector.pid 2>/dev/null` 2> /dev/null|| true
    endscript
}

/var/log/zbs/log-collector-rest-access.INFO {
    rotate 1
    size 10M
    nodateext
    compress
    missingok
    notifempty
    olddir archive
    create 0644 root root
    su root root
    postrotate
        /bin/kill -USR1 `cat /var/run/log_collector.pid 2>/dev/null` 2> /dev/null|| true
    endscript
}
