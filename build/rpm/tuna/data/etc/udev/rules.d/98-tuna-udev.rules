ENV{PATH}="/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin:/root/bin"
ENV{CHECK_BOOT_SCRIPT_PATH}="/usr/share/tuna/file/script/check_boot_state.sh"

KERNEL=="sd[a-z]", ATTRS{vendor}!="ZBS*", ATTRS{model}!="ZBS iSCSI TARGET", ENV{DEVPATH}!="*usb*", ENV{ID_BUS}!="usb" SUBSYSTEM=="block", ACTION=="add", PROGRAM="$env{CHECK_BOOT_SCRIPT_PATH}", RUN+="/usr/bin/zbs-node --log-file /var/log/zbs/tuna_cmd.INFO handle_disk_add $kernel '$env{ID_SCSI_SERIAL}' '$env{ID_SERIAL_SHORT}' '$env{ID_SERIAL}'"
KERNEL=="nvme[0-9]*n[0-9]*", SUBSYSTEMS=="block", EN<PERSON>{DEVTYPE}=="disk", ENV{ID_BUS}!="usb", ACTION=="add", PROGRAM="$env{CHECK_BOOT_SCRIPT_PATH}", RUN+="/usr/bin/zbs-node --log-file /var/log/zbs/tuna_cmd.INFO handle_disk_add $kernel '$env{ID_SCSI_SERIAL}' '$env{ID_SERIAL_SHORT}' '$env{ID_SERIAL}'"
KERNEL=="vd[a-z]", SUBSYSTEM=="block", ENV{ID_BUS}!="usb", ACTION=="add", PROGRAM="$env{CHECK_BOOT_SCRIPT_PATH}", RUN+="/usr/bin/zbs-node --log-file /var/log/zbs/tuna_cmd.INFO handle_disk_add $kernel '$env{ID_SCSI_SERIAL}' '$env{ID_SERIAL_SHORT}' '$env{ID_SERIAL}'"

KERNEL=="sd[a-z]", ENV{ID_VENDOR}!="ZBS*", ENV{DEVPATH}!="*usb*", ENV{ID_BUS}!="usb", SUBSYSTEM=="block", ACTION=="remove", PROGRAM="$env{CHECK_BOOT_SCRIPT_PATH}", RUN+="/usr/bin/zbs-node --log-file /var/log/zbs/tuna_cmd.INFO handle_disk_remove $kernel '$env{ID_SCSI_SERIAL}' '$env{ID_SERIAL_SHORT}' '$env{ID_SERIAL}'"
KERNEL=="nvme[0-9]*n[0-9]*", SUBSYSTEMS=="block", ENV{DEVTYPE}=="disk", ENV{ID_BUS}!="usb", ACTION=="remove", PROGRAM="$env{CHECK_BOOT_SCRIPT_PATH}", RUN+="/usr/bin/zbs-node --log-file /var/log/zbs/tuna_cmd.INFO handle_disk_remove $kernel '$env{ID_SCSI_SERIAL}' '$env{ID_SERIAL_SHORT}' '$env{ID_SERIAL}'"
KERNEL=="vd[a-z]", SUBSYSTEM=="block", ENV{ID_BUS}!="usb", ACTION=="remove", PROGRAM="$env{CHECK_BOOT_SCRIPT_PATH}", RUN+="/usr/bin/zbs-node --log-file /var/log/zbs/tuna_cmd.INFO handle_disk_remove $kernel '$env{ID_SCSI_SERIAL}' '$env{ID_SERIAL_SHORT}' '$env{ID_SERIAL}'"

SUBSYSTEM=="usb", DRIVERS=="usb", ENV{DEVTYPE}=="usb_device", ATTRS{bDeviceClass}!="09", ACTION=="add", PROGRAM="$env{CHECK_BOOT_SCRIPT_PATH}", RUN+="/usr/bin/zbs-node --log-file /var/log/zbs/tuna_cmd.INFO handle_usb_add $devpath"
SUBSYSTEM=="usb", DRIVERS=="usb", ENV{DEVTYPE}=="usb_device", ACTION=="remove", PROGRAM="$env{CHECK_BOOT_SCRIPT_PATH}", RUN+="/usr/bin/zbs-node --log-file /var/log/zbs/tuna_cmd.INFO handle_usb_remove $devpath"
