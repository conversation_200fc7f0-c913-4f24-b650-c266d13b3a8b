# Turn off the brp-python-bytecompile automagic
%global _python_bytecompile_extra 0
%define _build_id_links none
%undefine __brp_python_bytecompile
%undefine __python
%undefine __python3

# ==================
# Top-level metadata
# ==================

%define use_el7 ("%{?dist}" == ".el7.centos")
%define use_ky10 ("%{?dist}" == ".ky10")
%define use_oe2003 ("%{?dist}" == ".oe1")
%define use_tl3 ("%{?dist}" == ".tl3")
%define use_systemd (0%{?fedora} && 0%{?fedora} >= 18) || (0%{?rhel} && 0%{?rhel} >= 7) || (0%{?suse_version} == 1315) || %{use_oe2003} || %{use_tl3}

%if %{use_tl3}
# disable shebang mangling of python scripts
%undefine __brp_mangle_shebangs
%endif

Name:       tuna-lcm
Version:    %{RPM_VERSION}
Release:    %{?GITTAG}%{?dist}
Summary:    TUNA

Group:      System Environment/Compute
License:    Proprietary

# ==================================
# Conditionals controlling the build
# ==================================

# =====================
# General global macros
# =====================

%define pyshort %(echo "%{python3_version}" | cut -d. -f1-2)
%global venv_path /usr/local/venv/tuna
%global venv_bin %{venv_path}/bin
%global venv_site %{venv_path}/lib/python%{pyshort}/site-packages

# =======================
# Build-time requirements
# =======================
BuildRequires:  which
BuildRequires:  tar
BuildRequires:  make

# provided by Centos and EPEL
AutoReq:    no

Obsoletes:  smartx-disk-healthd <= 5.1.0
Obsoletes:  smartx-deploy <= 5.1.0
Obsoletes:  smartx-collector <= 5.1.0
Obsoletes:  smartx-tuna

# network obsoletes
Obsoletes:  smartx-network <= 5.1.0
Obsoletes:  smartx-dhcp <= 5.1.0

Provides:   smartx-tuna = %{version}-%{release}

Requires:   fping >= 4.2-1
Requires:   cronie
Requires:   numactl
Requires:   psmisc
Requires:   containerd.io
Requires:   ledmon >= 0.92-1
Requires:   storcli >= 007.2002.0000.0000-1
Requires:   Arcconf >= 4.01-24713
Requires:   gdisk
Requires:   mdadm >= 4.0
Requires:   parted
Requires:   nmap
Requires:   ca-certificates
Requires:   ansible
Requires:   nginx
Requires(pre):   nginx
Requires:   systemd
Requires(pre):   systemd
Requires:   logrotate >= 3.8.6-19.el7
Requires:   expect >= 5.45
Requires:   zstd
Requires:   sos >= 4.6.0
%ifarch x86_64
Requires:   perccli >= 007.1623.0000.0000-1
Requires:   ssacli >= 5.20
Requires:   mvcli >= 5.0.13.1109-1
Requires:   mnvcli >= 1.0.20.1050-1
%endif


%if %{use_el7}
%ifarch x86_64
Requires:   ipmctl >= 01.00.00.3474-2.el7
Requires:   ndctl >= 64.1-2.el7
Requires:   libibverbs-utils
Requires:   librdmacm-utils
Requires:   iproute >= 4.11.0-30.el7
%endif
%endif

# network dependencies
Requires:       smartmontools >= 1:7.0-2
Requires:       openvswitch
Requires:       ipmitool
Requires:       ethtool
Requires:       dnsmasq

# provided by zbs
Requires:       zbs-client-py3

%description
cluster management


%prep

# ======================================================
# Configuring and building the code:
# ======================================================
%build

# ======================================================
# Installing the built code:
# ======================================================
%install

# copy venv code
mkdir -p %{buildroot}%{venv_path}
cp -ar %{_builddir}/* %{buildroot}%{venv_path}

# link consle scripts
mkdir -p %{buildroot}%{_bindir}
ln -sfT %{venv_bin}/scc %{buildroot}%{_bindir}/scc
ln -sfT %{venv_bin}/tuna-exporter %{buildroot}%{_bindir}/tuna-exporter
ln -sfT %{venv_bin}/tuna-rest-server %{buildroot}%{_bindir}/tuna-rest-server
ln -sfT %{venv_bin}/network-monitor %{buildroot}%{_bindir}/network-monitor
ln -sfT %{venv_bin}/master-monitor %{buildroot}%{_bindir}/master-monitor
ln -sfT %{venv_bin}/disk-healthd %{buildroot}%{_bindir}/disk-healthd
ln -sfT %{venv_bin}/log-collector %{buildroot}%{_bindir}/log-collector
ln -sfT %{venv_bin}/zbs-node %{buildroot}%{_bindir}/zbs-node
ln -sfT %{venv_bin}/zbs-cluster %{buildroot}%{_bindir}/zbs-cluster
ln -sfT %{venv_bin}/zbs-deploy-server %{buildroot}%{_bindir}/zbs-deploy-server
ln -sfT %{venv_bin}/zbs-deploy-manage %{buildroot}%{_bindir}/zbs-deploy-manage
ln -sfT %{venv_bin}/elf-dhcp %{buildroot}%{_bindir}/elf-dhcp
ln -sfT %{venv_bin}/network-tool %{buildroot}%{_bindir}/network-tool
ln -sfT %{venv_bin}/network-preconfig %{buildroot}%{_bindir}/network-preconfig
ln -sfT %{venv_bin}/systemTUI %{buildroot}%{_bindir}/systemTUI

# copy system config from data dir
mkdir -p %{buildroot}%{_datadir}
mkdir -p %{buildroot}%{_sysconfdir}
mkdir -p %{buildroot}%{_localstatedir}/log/zbs/
mkdir -p %{buildroot}%{_localstatedir}/log/zbs/archive
mkdir -p %{buildroot}%{_localstatedir}/log/zbs/disk-healthd
cp -r %{_sourcedir}/etc/* %{buildroot}%{_sysconfdir}/
cp -r %{_sourcedir}/usr/share/* %{buildroot}%{_datadir}/
%ifnarch x86_64
rm -f %{buildroot}%{_datadir}/tuna/file/script/hag
%endif

%if %{use_systemd}
mkdir -p %{buildroot}%{_unitdir}
cp -r %{_sourcedir}/systemd/* %{buildroot}%{_unitdir}
rm -rf %{buildroot}%{_sysconfdir}/init/*
%endif

# copy venv config file to system dir
mkdir -p %{buildroot}%{_datadir}/tuna/script/
mkdir -p %{buildroot}%{_datadir}/tuna/template/
mkdir -p %{buildroot}%{_datadir}/tuna/playbook/cmd/
mkdir -p %{buildroot}%{_datadir}/tuna/playbook/script/
mkdir -p %{buildroot}%{_datadir}/zbs_deploy/playbook/
mkdir -p %{buildroot}%{_sysconfdir}/tuna/firmware
mkdir -p %{buildroot}%{_sysconfdir}/tuna/hardware_layout
cp -r %{venv_site}/tuna/deployment/script/* %{buildroot}/%{_datadir}/tuna/script/
cp -r %{venv_site}/tuna/deployment/template/* %{buildroot}/%{_datadir}/tuna/template/
cp -r %{venv_site}/tuna/cmd/playbook/* %{buildroot}/%{_datadir}/tuna/playbook/cmd/
cp -r %{venv_site}/tuna/cmd/script/* %{buildroot}/%{_datadir}/tuna/playbook/script/
cp -r %{venv_site}/zbs_deploy/playbook/* %{buildroot}/%{_datadir}/zbs_deploy/playbook/
# copy firmware whitelist file
cp -r %{venv_site}/tuna/hardware/firmware/* %{buildroot}%{_sysconfdir}/tuna/firmware/
# copy hardware layout config files
cp -r %{venv_site}/tuna/hardware_layout/* %{buildroot}%{_sysconfdir}/tuna/hardware_layout/

mkdir -p %{buildroot}%{_sysconfdir}/aquarium/register_conf/
mkdir -p %{buildroot}%{_sysconfdir}/tuna/ssl/
cp -r %{venv_site}/tuna/exporter/tuna_exporter/ %{buildroot}%{_sysconfdir}/aquarium/register_conf/
cp -r %{venv_site}/tuna/server/tuna_rest.json %{buildroot}%{_sysconfdir}/aquarium/register_conf/

# copy fluent-bit
mkdir -p %{buildroot}%{_sysconfdir}/fluent-bit/
cp -r %{venv_site}/tuna/fluent-bit/parser_tuna_cmd.conf %{buildroot}%{_sysconfdir}/fluent-bit/
cp -r %{venv_site}/tuna/fluent-bit/input_tuna_cmd.conf %{buildroot}%{_sysconfdir}/fluent-bit/
cp -r %{venv_site}/tuna/fluent-bit/parser_tuna_rest.conf %{buildroot}%{_sysconfdir}/fluent-bit/
cp -r %{venv_site}/tuna/fluent-bit/input_tuna_rest.conf %{buildroot}%{_sysconfdir}/fluent-bit/
cp -r %{venv_site}/log_collector/fluent-bit/input_log_collector.conf %{buildroot}/%{_sysconfdir}/fluent-bit/
cp -r %{venv_site}/log_collector/fluent-bit/parser_log_collector.conf %{buildroot}/%{_sysconfdir}/fluent-bit/

# install smartx-disk-healthd
mkdir -p %{buildroot}%{_sysconfdir}/disk_healthd/
mkdir -p %{buildroot}%{_datadir}/disk_healthd/
cp -r %{venv_site}/disk_healthd/etc/disk_healthd/disk_healthd.conf %{buildroot}/%{_sysconfdir}/disk_healthd/
cp -r %{venv_site}/disk_healthd/etc/aquarium/register_conf/disk_healthd/ %{buildroot}%{_sysconfdir}/aquarium/register_conf/
cp -r %{venv_site}/disk_healthd/usr/share/disk_healthd/disk_healthd.conf.default %{buildroot}%{_datadir}/disk_healthd/
cp -r %{venv_site}/disk_healthd/usr/share/disk_healthd/gunicorn_logging.conf %{buildroot}%{_datadir}/disk_healthd/

# install smartx-log-collector
mkdir -p %{buildroot}%{_sysconfdir}/log_collector/data/
mkdir -p %{buildroot}/usr/share/tuna/log_collector/logs/
mkdir -p %{buildroot}/usr/share/tuna/log_collector/shell/
mkdir -p %{buildroot}/usr/share/tuna/log_collector/metadata/
cp -r %{venv_site}/log_collector/cmd/collect_local_log.py %{buildroot}/usr/share/tuna/log_collector/shell/
cp -r %{venv_site}/log_collector/cmd/clean_local_log.py %{buildroot}/usr/share/tuna/log_collector/shell/
cp -r %{venv_site}/log_collector/shell/clean_collector_path.py %{buildroot}/usr/share/tuna/log_collector/shell/
cp -r %{venv_site}/log_collector/config/log_config.json %{buildroot}%{_sysconfdir}/log_collector/
cp -r %{venv_site}/log_collector/config/ansible.cfg %{buildroot}%{_sysconfdir}/log_collector/data/
cp -r %{venv_site}/log_collector/playbook/* %{buildroot}%{_sysconfdir}/log_collector/data/


%files
%{venv_path}/

# -----------------------------------------------------------------------------------------------------------------------
%{_bindir}/scc
%{_bindir}/tuna-rest-server
%{_bindir}/tuna-exporter
%{_bindir}/network-monitor
%{_bindir}/master-monitor
%{_bindir}/log-collector
%{_bindir}/disk-healthd
%{_bindir}/zbs-deploy-server
%{_bindir}/zbs-deploy-manage
%{_bindir}/zbs-node
%{_bindir}/zbs-cluster
%{_bindir}/elf-dhcp
%{_bindir}/network-tool
%{_bindir}/network-preconfig
%{_bindir}/systemTUI

# copy from data
%{_datadir}/tuna/config/tuna-exporter/
%{_datadir}/tuna/config/tuna_rest/
%{_datadir}/tuna/config/log_collector/
%{_datadir}/tuna/file/script/init_cgconfig_conf.sh
%{_datadir}/tuna/file/script/init_sync_time.sh
%{_datadir}/tuna/file/script/config_hugepage.sh
%{_datadir}/tuna/file/script/umount_nfs_export.sh
%{_datadir}/tuna/file/script/check_logrotate.sh
%{_datadir}/tuna/file/script/collect_env_info.sh
%{_datadir}/tuna/file/script/check_boot_state.sh
%{_datadir}/tuna/file/script/coredump.sh
%{_datadir}/tuna/file/script/logrotate.sh
%{_datadir}/tuna/file/script/post_install.sh
%{_datadir}/tuna/file/script/pre_boot.sh
%{_datadir}/tuna/file/script/raid_check_recron.py
%{_datadir}/tuna/file/script/service_resource_check.py
%{_datadir}/tuna/file/script/sync_cluster_ips_on_witness.py
%{_datadir}/tuna/file/script/utils.sh
%{_datadir}/tuna/file/script/bind_dsa_driver.sh
%{_datadir}/tuna/file/script/dpdk-devbind.py
%ifarch x86_64
%{_datadir}/tuna/file/script/hag
%endif
%{_datadir}/tuna/log_collector
%{_datadir}/tuna/file/script/dhcp_pre_start.sh
%{_sysconfdir}/consul.d/tuna-exporter.json
%{_sysconfdir}/cron.d/service_resource_check
%{_sysconfdir}/cron.d/raid_check_recron
%{_sysconfdir}/cron.d/rotate_logs_if_necessary
%{_sysconfdir}/cron.d/sync_cluster_ips_on_witness
%{_sysconfdir}/cron.daily/smartctl_tests.sh
%{_sysconfdir}/cron.daily/clean_collector_path.sh
%{_sysconfdir}/logrotate.tuna.d/tunaexporterlog
%{_sysconfdir}/logrotate.tuna.d/tuna-rest-log
%{_sysconfdir}/logrotate.tuna.d/network-monitorlog
%{_sysconfdir}/logrotate.tuna.d/log-collector-log
%{_sysconfdir}/logrotate.tuna.d/disk-healthd-log
%{_sysconfdir}/nginx/conf.d/web-ui.conf
%{_sysconfdir}/nginx/conf.d/access_log.conf
%{_sysconfdir}/nginx/default.d/web-ui.conf
%{_sysconfdir}/rsyslog.d/tuna.conf
%{_sysconfdir}/rsyslog.d/fluent-bit.conf
%{_sysconfdir}/tuna/ignore_usbs.yaml
%{_sysconfdir}/sysconfig/zbs-deploy-server
%{_sysconfdir}/sysconfig/modules/ipmi.modules
%{_sysconfdir}/systemd/
%{_sysconfdir}/udev/rules.d/
%config(noreplace) %{_sysconfdir}/zbs/.mongo_password
%{_sysconfdir}/tuna/dhcp.conf
%{_sysconfdir}/tuna/app.network.conf
%{_sysconfdir}/tuna/services.yaml

%dir %attr(0777, root, root) %{_localstatedir}/log/zbs
%dir %attr(0777, root, root) %{_localstatedir}/log/zbs/archive
%dir %attr(0755, root, root) %{_localstatedir}/log/zbs/disk-healthd

# copy from venv
%{_datadir}/tuna/script/
%{_datadir}/tuna/template/
%{_datadir}/tuna/playbook/cmd/
%{_datadir}/tuna/playbook/script/
%{_datadir}/zbs_deploy/playbook/
%{_datadir}/disk_healthd/disk_healthd.conf.default
%{_datadir}/disk_healthd/gunicorn_logging.conf
%{_sysconfdir}/log_collector/
%{_sysconfdir}/disk_healthd/disk_healthd.conf
%{_sysconfdir}/aquarium/register_conf/disk_healthd/
%{_sysconfdir}/aquarium/register_conf/tuna_exporter/
%{_sysconfdir}/aquarium/register_conf/tuna_rest.json
%config(noreplace) %{_sysconfdir}/tuna/ssl/ca.cert
%config(noreplace) %{_sysconfdir}/tuna/ssl/ca.key
%{_sysconfdir}/tuna/hardware_layout/
%{_sysconfdir}/tuna/firmware/
%{_sysconfdir}/fluent-bit/input_tuna_cmd.conf
%{_sysconfdir}/fluent-bit/parser_tuna_cmd.conf
%{_sysconfdir}/fluent-bit/input_tuna_rest.conf
%{_sysconfdir}/fluent-bit/parser_tuna_rest.conf
%{_sysconfdir}/fluent-bit/input_log_collector.conf
%{_sysconfdir}/fluent-bit/parser_log_collector.conf

%if %{use_systemd}
%config %{_unitdir}/tuna-exporter.service
%config %{_unitdir}/tuna-rest-server.service
%config %{_unitdir}/tuna-init-cgconfig.service
%config %{_unitdir}/tuna-init-sync-time.service
%config(noreplace) %{_unitdir}/master-monitor.service
%config(noreplace) %{_unitdir}/network-monitor.service
%config(noreplace) %{_unitdir}/tuna-config-hugepage.service
%config(noreplace) %{_unitdir}/tuna-umount-nfs-export.service
%config(noreplace) %{_unitdir}/log-collector.service
%config(noreplace) %{_unitdir}/disk-healthd.service
%config(noreplace) %{_unitdir}/zbs-deploy-server.service
%config(noreplace) %{_unitdir}/fisheye-pre-boot.service
%config(noreplace) %{_unitdir}/fisheye-os-shutdown.service
%config(noreplace) %{_unitdir}/elf-dhcp.service
%config(noreplace) %{_unitdir}/tuna-init-dsa.service
%else
%{_sysconfdir}/init/master-monitor.conf
%{_sysconfdir}/init/tuna-exporter.conf
%{_sysconfdir}/init/fisheye-pre-boot.conf
%{_sysconfdir}/init/zbs-deploy-server.conf
%{_sysconfdir}/init/elf-dhcp.conf
%endif


%post
if [ $1 == 1 ];then
   systemctl enable tuna-init-cgconfig.service
   systemctl enable tuna-init-sync-time.service
   systemctl enable tuna-config-hugepage.service
   systemctl enable tuna-init-dsa.service
elif [ $1 == 2 ];then
   systemctl enable tuna-init-cgconfig.service
   systemctl enable tuna-init-sync-time.service
   systemctl enable tuna-config-hugepage.service
   systemctl enable tuna-umount-nfs-export --now
   systemctl enable tuna-init-dsa.service
fi

if [ $1 -eq 1 ]; then
    /usr/share/tuna/file/script/post_install.sh
fi

# %post -n smartx-log-collector:  change log_collector workspace dir
if [[ -d /usr/share/smartx/log_collector/logs && $(ls -A /usr/share/smartx/log_collector/logs/) ]]; then
    mkdir -p /usr/share/tuna/log_collector/logs
    mv -f /usr/share/smartx/log_collector/logs/* /usr/share/tuna/log_collector/logs/
fi

if [[ -d /usr/share/smartx/log_collector/metadata && $(ls -A /usr/share/smartx/log_collector/metadata/) ]]; then
    mkdir -p /usr/share/tuna/log_collector/metadata
    mv -f /usr/share/smartx/log_collector/metadata/* /usr/share/tuna/log_collector/metadata/
fi


%posttrans
if [ -f %{_sysconfdir}/zbs/.mongo_password.rpmnew ]; then
    rm -f %{_sysconfdir}/zbs/.mongo_password.rpmnew
fi

# ======================================================
# Finally, the changelog:
# ======================================================
%changelog

%(cd %{workspace_dir} && git log -n 50 --format="* %cd %aN%n- (%h) %s%d%n" --date=local | sed -r 's/[0-9]+:[0-9]+:[0-9]+ //')
