# Turn off the brp-python-bytecompile automagic
%global _python_bytecompile_extra 0
%define _build_id_links none
%undefine __brp_python_bytecompile
%undefine __python
%undefine __python3

# ==================
# Top-level metadata
# ==================

%define use_el7 ("%{?dist}" == ".el7.centos")
%define use_ky10 ("%{?dist}" == ".ky10")
%define use_oe2003 ("%{?dist}" == ".oe1")
%define use_tl3 ("%{?dist}" == ".tl3")
%define use_systemd (0%{?fedora} && 0%{?fedora} >= 18) || (0%{?rhel} && 0%{?rhel} >= 7) || (0%{?suse_version} == 1315) || %{use_oe2003} || %{use_tl3}

%if %{use_tl3}
# disable shebang mangling of python scripts
%undefine __brp_mangle_shebangs
%endif

Name:       pyzbs
Version:    %{RPM_VERSION}
Release:    %{?GITTAG}%{?dist}
Summary:    pyzbs

Group:      System Environment/Storage
License:    Proprietary

# ==================================
# Conditionals controlling the build
# ==================================

# =====================
# General global macros
# =====================
%define pyshort %(echo "%{python3_version}" | cut -d. -f1-2)
%global venv_path /usr/local/venv/pyzbs
%global venv_bin %{venv_path}/bin
%global venv_site %{venv_path}/lib/python%{pyshort}/site-packages

# =======================
# Build-time requirements
# =======================
BuildRequires:  which
BuildRequires:  tar
BuildRequires:  make


# =======================
# Source code and patches
# =======================


# ==========================================
# Descriptions, and metadata for subpackages
# ==========================================
# provided by Centos and EPEL
AutoReq:    no


# provided by Centos and EPEL
Requires:       nginx
Requires:       nfs-utils

Requires:       librsync

# provided by zbs
Requires:       libzbs == %{ZBS_VERSION}

%description
ZBS REST Server


# ======================================================
# The prep phase of the build:
# ======================================================
%prep

# ======================================================
# Configuring and building the code:
# ======================================================
%build

# ======================================================
# Installing the built code:
# ======================================================
%install
rm -rf %{buildroot}

# copy venv code
mkdir -p %{buildroot}%{venv_path}
cp -ar %{_builddir}/* %{buildroot}%{venv_path}/

# link consle scripts
mkdir -p %{buildroot}%{_bindir}
ln -sfT %{venv_bin}/zbs-rest-server-v2 %{buildroot}%{_bindir}/zbs-rest-server-v2

# copy system config
mkdir -p %{buildroot}%{_datadir}
mkdir -p %{buildroot}%{_sysconfdir}
cp -r %{_sourcedir}/etc/* %{buildroot}%{_sysconfdir}/
cp -r %{_sourcedir}/usr/share/* %{buildroot}%{_datadir}/

%if %{use_systemd}
mkdir -p %{buildroot}%{_unitdir}
cp -r %{_sourcedir}/systemd/* %{buildroot}%{_unitdir}
rm -rf %{buildroot}%{_sysconfdir}/init/*
%endif


# copy venv config file to system dir
mkdir -p %{buildroot}/%{_sysconfdir}/aquarium/register_conf
mkdir -p %{buildroot}/%{_sysconfdir}/fluent-bit

cp -r %{venv_site}/zbs_rest/zbs_rest.json %{buildroot}/%{_sysconfdir}/aquarium/register_conf/
cp -r %{venv_site}/zbs_rest/fluent-bit/parser_zbs_rest.conf %{buildroot}/%{_sysconfdir}/fluent-bit/
cp -r %{venv_site}/zbs_rest/fluent-bit/input_zbs_rest.conf %{buildroot}/%{_sysconfdir}/fluent-bit/

%files
%{venv_path}/

%{_bindir}/zbs-rest-server-v2

# copy from data
%{_sysconfdir}/logrotate.tuna.d/zbsrestlog
%{_datadir}/pyzbs/config/zbs_rest/

# copy from venv
%{_sysconfdir}/aquarium/register_conf/zbs_rest.json
%{_sysconfdir}/fluent-bit/input_zbs_rest.conf
%{_sysconfdir}/fluent-bit/parser_zbs_rest.conf

%if %{use_systemd}
%config %{_unitdir}/zbs-rest-server.service
%else
%{_sysconfdir}/init/zbs-rest-server.conf
%endif


# ======================================================
# Finally, the changelog:
# ======================================================
%changelog
%(cd %{workspace_dir} && git log -n 50 --format="* %cd %aN%n- (%h) %s%d%n" --date=local | sed -r 's/[0-9]+:[0-9]+:[0-9]+ //')
