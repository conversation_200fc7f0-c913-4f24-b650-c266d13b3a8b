/var/log/zbs/zbs-rest.INFO {
    rotate 1
    size 50M
    nodateext
    compress
    missingok
    notifempty
    olddir archive
    create 0644 root root
    su root root
    postrotate
        /bin/kill -USR1 `cat /var/run/zbs_rest.pid 2>/dev/null` 2> /dev/null|| true
    endscript
}

/var/log/zbs/zbs-rest-access.INFO {
    rotate 1
    size 100M
    nodateext
    compress
    missingok
    notifempty
    olddir archive
    create 0644 root root
    su root root
    postrotate
        /bin/kill -USR1 `cat /var/run/zbs_rest.pid 2>/dev/null` 2> /dev/null|| true
    endscript
}
