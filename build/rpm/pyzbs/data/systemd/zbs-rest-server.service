[Unit]
Description=ZBS rest api server
After=network.target mongod.service

[Service]
ExecStart=/usr/local/venv/pyzbs/bin/smtx-gunicorn -n zbs-rest-server -b 127.0.0.1:10402 -k gevent zbs_rest.main:flask_app -w 2 --timeout 60 --pid /var/run/zbs_rest.pid --log-config /usr/share/pyzbs/config/zbs_rest/logging.conf --access-logformat '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(L)s' --capture-output --error-logfile /var/log/zbs/zbs-rest.INFO
Restart=always
RestartSec=1s

[Install]
WantedBy=multi-user.target
