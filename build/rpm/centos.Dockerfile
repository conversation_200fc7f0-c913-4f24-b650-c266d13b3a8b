FROM registry.smtx.io/pyzbs-ci/centos:7.4.1708 AS builder

# configure yum repo
ARG ARCH
RUN rm -f /etc/yum.repos.d/*
COPY ./build/sources/repo/smartxos_${ARCH}.repo /etc/yum.repos.d/

# install common softwares
RUN yum clean all && yum update -y \
    && echo "yum-utils-1.1.31-54 is needed" \
    && yum install -y yum-utils rpm-build-4.11.3 git-2.11.0 make tar sudo which tree gcc gcc-c++ libcap-devel \
        libnl3-devel snappy-devel openssl-devel openssl11 openssl11-devel dbus-devel glib2-devel \
        wget jq genisoimage yum-utils libX11 tk tcl rsync sshpass bash ntp ethtool \
    && yum clean all -q

# install libvirt & libvirt-devel
ARG ELF_LIBVIRT_VERSION_EL7
RUN yum install -y libvirt-${ELF_LIBVIRT_VERSION_EL7} libvirt-devel-${ELF_LIBVIRT_VERSION_EL7} \
    && yum clean all -q

ARG SMARTX_PYTHON3_VERSION
RUN yum install tuna-python3-${SMARTX_PYTHON3_VERSION} -y && yum clean all
ENV PATH=/root/.local/bin:$PATH
ARG PYTHON3_COMMAND
# we use protobuf 3.x.x and grpcio-tools 1.48.2 to avoid the problem of zbs-client-py bytes and str problem
RUN ${PYTHON3_COMMAND} -m pip config --user set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
RUN ${PYTHON3_COMMAND} -m pip config --user set global.trusted-host pypi.tuna.tsinghua.edu.cn
ARG POETRY_VERSION=1.6.1
RUN curl -sSL https://install.python-poetry.org > install-poetry.py \
    && ${PYTHON3_COMMAND} install-poetry.py --version ${POETRY_VERSION} \
    && rm -f install-poetry.py
RUN poetry self add "poetry-dynamic-versioning[plugin]" \
    && poetry config virtualenvs.in-project true
ARG RUFF_VERSION
RUN ${PYTHON3_COMMAND} -m pip install --user ruff==${RUFF_VERSION}
WORKDIR /pyzbs


FROM registry.smtx.io/pyzbs-ci/pyzbs-builder-centos:latest AS unittest

# install common softwares
ARG ZOOKEEPER_VERSION
RUN yum install -y zookeeper-${ZOOKEEPER_VERSION} nfs-utils glibc-2.17 ansible qemu-kvm-ev libaio-0.3.109 dmidecode ipmitool genisoimage \
    && yum clean all
# modify sudoers
RUN sed -i '/Defaults    requiretty/d' /etc/sudoers

# configure zookeeper
COPY ./build/test/conf/zoo.cfg /etc/zookeeper/zoo.cfg

# Below are the part that needs to be re-executed every time the image
# is built. Add a timestamp arg to invalidate the part below from last
# cached data.
ARG TIMESTAMP
ARG ZBS_VERSION
ARG ZBS_CLIENT_PY3_VERSION
RUN yum install -y \
    libzbs-devel-${ZBS_VERSION} \
    zbs-${ZBS_VERSION} \
    libzbs-${ZBS_VERSION} \
    zbs-client-py3-${ZBS_CLIENT_PY3_VERSION}
