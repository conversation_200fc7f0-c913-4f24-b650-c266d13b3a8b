# Turn off the brp-python-bytecompile automagic
%global _python_bytecompile_extra 0
%undefine __brp_python_bytecompile
%undefine __python
%undefine __python3


Name:       %{OEM_PKG_NAME}
Version:    %{RPM_VERSION}
Release:    %{?GITTAG}%{?dist}
Summary:    TUNA product customize package

Group:      System Environment/Storage/Deployment
License:    Proprietary

# =====================
# General global macros
# =====================
%define pyshort %(echo "%{python3_version}" | cut -d. -f1-2)
%global venv_bin /usr/local/venv/tuna/bin
%global venv_site /usr/local/venv/tuna/lib/python%{pyshort}/site-packages

# =======================
# Build-time requirements
# =======================
BuildRequires:  %{python3_command}
BuildRequires:  wget
BuildRequires:  make
Requires:       tuna-lcm
Obsoletes:      %{OLD_OEM_PKG_NAME}

# ==========================================
# Descriptions, and metadata for subpackages
# ==========================================
AutoReq:    no

%description
TUNA product customize scripts and tools

%prep

%build
%{python3_command} -m pip install jinja2==3.1.2
%{python3_command} %{workspace_dir}/tuna_product_customize/gen_oem_script.py %{isOEM} \
  --oem-vender "%{OEM_VENDER}" \
  --oem-hypervisor-name "%{OEM_HYPERVISOR_NAME}" \
  --oem-machine-model "%{OEM_MACHINE_MODEL}"\
  --oem-software-version "%{OEM_SOFTWARE_VERSION}" \
  --oem-storage-name "%{OEM_STORAGE_NAME}"
find . -name "*.py[c|o]" -exec rm -f {} \;

%install

# copy system config from data dir
mkdir -p %{buildroot}%{_sysconfdir}
cp -r %{_sourcedir}/etc/* %{buildroot}%{_sysconfdir}/


%{__install} -d %{buildroot}%{venv_site}/tuna_product_customize/
%{__install} -d %{buildroot}%{_datadir}/tuna/script/

%{__install} -p -D -m 0755 %{workspace_dir}/tuna_product_customize/* %{buildroot}%{venv_site}/tuna_product_customize/

%{__install} -p -D -m 0755 %{buildroot}%{venv_site}/tuna_product_customize/setup_oem.sh %{buildroot}%{_datadir}/tuna/script/

%files
%{venv_site}/tuna_product_customize/
%{_datadir}/tuna/script/setup_oem.sh
%config(noreplace) %{_sysconfdir}/tuna/ssl/ca.cert.arcfra

%post
oem_vender=`zbs-node kv get oem_vendor`
if [ $1 -ne 1 ]; then
    /bin/env bash -x %{_datadir}/tuna/script/setup_oem.sh
fi

%changelog
%(cd %{workspace_dir} && git log -n 20 --format="* %cd %aN%n- (%h) %s%d%n" --date=local | sed -r 's/[0-9]+:[0-9]+:[0-9]+ //')
