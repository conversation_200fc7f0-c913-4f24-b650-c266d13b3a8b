#!/usr/bin/env bash

# Output a message indicating the start of execution
echo "Starting the clean VM screenshot process..."

product_vendor=$(zbs-node kv get product_vendor)

if [ $? -ne 0 ]; then
    echo "Failed to get product_vendor value."
    exit 1
fi

if [ -z "$product_vendor" ] || [ "$product_vendor" == "SmartX" ]; then
    file_path="smartx"
else
    file_path="elfvirt"
fi

# The screenshot directory
screenshot_dir="/usr/share/$file_path/elf/screenshot_file/"

# Find and delete files that haven't been modified in more than 7 days,
deleted_files=$(find "$screenshot_dir" -type f -mtime +7 -delete -print)

# If files were deleted, output the list of deleted files
if [ -n "$deleted_files" ]; then
    echo "The following files have been deleted:"
    echo "$deleted_files"
else
    echo "No files matching the criteria were found for deletion."
fi


## The deletion of the empty directories is only for handling the legacy
## v1 screenshot storage method.
## In the v2 screenshot storage method, the following script has no effect.


# Delete any empty directories under the screenshot_file directory,
# but exclude the screenshot_dir itself
empty_directories=$(find "$screenshot_dir" -mindepth 1 -type d -empty -delete -print)

# If empty directories were deleted, output the list of deleted directories
if [ -n "$empty_directories" ]; then
    echo "The following empty directories have been deleted:"
    echo "$empty_directories"
else
    echo "No empty directories were found for deletion."
fi
