#!/bin/bash

# Generated by Chatgpt

# Check if the argument is provided, otherwise use the default path
CONF_FILE=${1:-/etc/libvirt/libvirtd.conf}

# Check if the file exists
if [ -f "$CONF_FILE" ]; then
    echo "Updating libvirt config file $CONF_FILE"

    # Check if "listen_tls=1" exists, if not, add it or replace "listen_tls=0"
    if grep -q "^listen_tls=" "$CONF_FILE"; then
        sed -i 's/^listen_tls=.*/listen_tls=1/' "$CONF_FILE"
    else
        echo "listen_tls=1" >> "$CONF_FILE"
    fi

    if grep -q "^tls_priority=" "$CONF_FILE"; then
        sed -i 's/^tls_priority=.*/tls_priority="SECURE256"/' "$CONF_FILE"
    else
        echo 'tls_priority="SECURE256"' >> "$CONF_FILE"
    fi

    # Add or update the tls_port to "16514"
    if grep -q "^tls_port=" "$CONF_FILE"; then
        sed -i 's/^tls_port=.*/tls_port="16514"/' "$CONF_FILE"
    else
        echo 'tls_port="16514"' >> "$CONF_FILE"
    fi
else
    echo "Configuration file not found: $CONF_FILE"
    exit 1
fi
