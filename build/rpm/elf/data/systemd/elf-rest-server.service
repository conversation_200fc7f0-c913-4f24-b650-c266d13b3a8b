[Unit]
Description=Elf rest api server
After=network.target mongod.service

[Service]
Environment="GRPC_ENABLE_FORK_SUPPORT=0"
ExecStart=/usr/local/venv/elf/bin/smtx-gunicorn -n elf-rest-server -b 127.0.0.1:10412 -k gevent smartx_app.elf.http.main:flask_app -w 1 --timeout 120 --pid /var/run/elf-rest.pid --log-config /usr/share/elfvirt/config/elf-rest/logging.conf --access-logformat '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(L)s' --capture-output --error-logfile /var/log/zbs/elf-rest.INFO
Restart=on-failure

[Install]
WantedBy=multi-user.target
