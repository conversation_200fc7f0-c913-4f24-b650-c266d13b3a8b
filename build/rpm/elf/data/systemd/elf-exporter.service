[Unit]
Description=elf exporter api server
After=network.target

[Service]
EnvironmentFile=/etc/sysconfig/elf-exporter
ExecStart=/usr/local/venv/elf/bin/smtx-gunicorn -n elf-exporter -b ${BIND_IP}:10405 -k sync 'smartx_app.elf.exporter.main:create_app()' -w 1 --thread 4 --timeout 60 --keep-alive 50 --pid /var/run/elf-exporter.pid --log-config /usr/share/elfvirt/config/elf-exporter/logging.conf --access-logformat '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(L)s' --capture-output --error-logfile /var/log/zbs/elf-exporter.INFO
Restart=on-failure

[Install]
WantedBy=multi-user.target
