# Turn off the brp-python-bytecompile automagic
%global _python_bytecompile_extra 0
%define _build_id_links none
%undefine __brp_python_bytecompile
%undefine __python
%undefine __python3

# ==================
# Top-level metadata
# ==================

%define use_el7 ("%{?dist}" == ".el7.centos")
%define use_ky10 ("%{?dist}" == ".ky10")
%define use_oe2003 ("%{?dist}" == ".oe1")
%define use_tl3 ("%{?dist}" == ".tl3")
%define use_systemd (0%{?fedora} && 0%{?fedora} >= 18) || (0%{?rhel} && 0%{?rhel} >= 7) || (0%{?suse_version} == 1315) || %{use_oe2003} || %{use_tl3}

%if %{use_tl3}
# disable shebang mangling of python scripts
%undefine __brp_mangle_shebangs
%endif

# libvirt tls related
%define cert_dir /pki/libvirt
%define key_dir /pki/libvirt/private
%define qemu_tls_dir /pki/qemu

%define cert /pki/CA/cacert.pem
%define servercert %{cert_dir}/servercert.pem
%define serverkey %{key_dir}/serverkey.pem
%define clientcert %{cert_dir}/clientcert.pem
%define clientkey %{key_dir}/clientkey.pem
%define qemu_ca %{qemu_tls_dir}/ca-cert.pem
%define qemu_servercert %{qemu_tls_dir}/server-cert.pem
%define qemu_serverkey %{qemu_tls_dir}/server-key.pem
%define qemu_clientcert %{qemu_tls_dir}/client-cert.pem
%define qemu_clientkey %{qemu_tls_dir}/client-key.pem

Name:       elfvirt
Version:    %{RPM_VERSION}
Release:    %{?GITTAG}%{?dist}
Summary:    ELF Compute

Group:      System Environment/Compute
License:    Proprietary

# ==================================
# Conditionals controlling the build
# ==================================

# =====================
# General global macros
# =====================
%define pyshort %(echo "%{python3_version}" | cut -d. -f1-2)
%global venv_path /usr/local/venv/elf
%global venv_bin %{venv_path}/bin
%global venv_site %{venv_path}/lib/python%{pyshort}/site-packages

# =======================
# Build-time requirements
# =======================
BuildRequires:  which
BuildRequires:  tar
BuildRequires:  make
%if %{use_oe2003}
BuildRequires:  libvirt-devel == 1:%{ELF_LIBVIRT_VERSION_OE2003}
%endif

%if %{use_el7}
BuildRequires:  libvirt-devel == 1:%{ELF_LIBVIRT_VERSION_EL7}
%endif

%if %{use_tl3}
BuildRequires:  libvirt-devel == 1:%{ELF_LIBVIRT_VERSION_TL3}
%endif

# =======================
# Source code and patches
# =======================
Source1:    allock.so

# ==========================================
# Descriptions, and metadata for subpackages
# ==========================================
# provided by Centos and EPEL
AutoReq:    no

Obsoletes:  smartx-vmtools-agent <= 5.1.0
Obsoletes:  smartx-elf

Requires:   hdparm
%if %{use_el7}
Requires:   glibc >= 2.17-90
Requires:   libzstd == 1.5.5
Requires:   elf-fs
Requires:   elf-fs-tools
Requires:   elf-libvirt-hooks
Requires:   elf-vm-watchdog
Requires:   libiscsi
Requires:   libvirt
Requires:   procurator
Requires:   qemu-6
Requires:   qemu-img-ev
Requires:   qemu-kvm-common-ev
Requires:   qemu-kvm-ev
Requires:   vnc-proxy
%ifarch x86_64
Requires:   edk2.git-ovmf-x64
Requires:   cpuid
%else
Requires:   edk2.git-aarch64
%endif
%endif

%if %{use_ky10}
Requires:   libvirt
Requires:   libiscsi
Requires:   qemu == 2:4.1.0-33.smartx.6.ky10
Requires:   qemu-img == 2:4.1.0-33.smartx.6.ky10
Requires:   vnc-proxy == 1.0.2-rc.1.0.g891c812.ky10
%ifarch x86_64
Requires:   edk2.git-ovmf-x64
%else
Requires:   edk2.git-aarch64
%endif
%endif

%if %{use_oe2003}
Requires:   glibc >= 2.17-90
Requires:   zstd == 1.4.5
Requires:   elf-fs
Requires:   elf-fs-tools
Requires:   elf-libvirt-hooks
Requires:   elf-vm-watchdog
Requires:   libiscsi
Requires:   libvirt
Requires:   procurator
Requires:   qemu-6
Requires:   qemu-img-ev
Requires:   qemu-kvm-common-ev
Requires:   qemu-kvm-ev
Requires:   vnc-proxy
%ifarch x86_64
Requires:   edk2.git-ovmf-x64
Requires:   cpuid
%else
Requires:   edk2.git-aarch64
%endif
%endif

%if %{use_tl3}
Requires:   glibc >= 2.17-90
Requires:   zstd == 1.4.5
Requires:   elf-fs
Requires:   elf-fs-tools
Requires:   elf-libvirt-hooks
Requires:   elf-vm-watchdog
Requires:   libiscsi
Requires:   libvirt
Requires:   procurator
Requires:   qemu-6
Requires:   qemu-img-ev
Requires:   qemu-kvm-common-ev
Requires:   qemu-kvm-ev
Requires:   vnc-proxy
%ifarch x86_64
Requires:   edk2.git-ovmf-x64
Requires:   cpuid
%else
Requires:   edk2.git-aarch64
%endif
%endif


Requires:   vmdk-convert == 0.3.7-3
Requires:   libzbs >= 5.6.0-rc43.0.release.git.ga41ba7a0f%{?dist}.SMTX.HCI, libzbs < 6.0.0
Obsoletes:  libzbs == 6.0.0

Requires:   genisoimage
Requires:   vm-security-controller

%description -n elfvirt
Compute for elf


# ======================================================
# The prep phase of the build:
# ======================================================
%prep

# ======================================================
# Configuring and building the code:
# ======================================================
%build

# ======================================================
# Installing the built code:
# ======================================================
%install
rm -rf %{buildroot}

# copy venv code
mkdir -p %{buildroot}%{venv_path}
cp -ar %{_builddir}/* %{buildroot}%{venv_path}

# allock destination
mkdir -p %{buildroot}%{_libdir}/libvirt/lock-driver/
# allock file
cp -r %{workspace_dir}/smartx_app/elf/allock/allock.so %{buildroot}/%{_libdir}/libvirt/lock-driver/allock.so

# link consle scripts
mkdir -p %{buildroot}%{_bindir}
ln -sfT %{venv_bin}/elf-exporter %{buildroot}%{_bindir}/elf-exporter
ln -sfT %{venv_bin}/elf-tool %{buildroot}%{_bindir}/elf-tool
ln -sfT %{venv_bin}/elf-rest-server %{buildroot}%{_bindir}/elf-rest-server
ln -sfT %{venv_bin}/elf-vm-monitor %{buildroot}%{_bindir}/elf-vm-monitor
ln -sfT %{venv_bin}/elf-vm-scheduler %{buildroot}%{_bindir}/elf-vm-scheduler

ln -sfT %{venv_bin}/vmtools-agent %{buildroot}%{_bindir}/vmtools-agent
ln -sfT %{venv_bin}/vmtools-agent-cli %{buildroot}%{_bindir}/vmtools-agent-cli

# copy system config
mkdir -p %{buildroot}%{_datadir}
mkdir -p %{buildroot}%{_sysconfdir}
cp -r %{_sourcedir}/etc/* %{buildroot}%{_sysconfdir}/
cp -r %{_sourcedir}/usr/share/* %{buildroot}%{_datadir}/

%if %{use_systemd}
mkdir -p %{buildroot}%{_unitdir}
cp -r %{_sourcedir}/systemd/* %{buildroot}%{_unitdir}
rm -rf %{buildroot}%{_sysconfdir}/init/*
%endif


# copy venv config file to system dir
mkdir -p %{buildroot}%{_sysconfdir}/aquarium/register_conf/

mkdir -p %{buildroot}%{_sysconfdir}/fluent-bit/
cp -r %{venv_site}/smartx_app/elf/http/elf_rest.json %{buildroot}%{_sysconfdir}/aquarium/register_conf/
cp -r %{venv_site}/smartx_app/elf/http/fluent-bit/input_elf_rest.conf %{buildroot}%{_sysconfdir}/fluent-bit/
cp -r %{venv_site}/smartx_app/elf/http/fluent-bit/parser_elf_rest.conf %{buildroot}%{_sysconfdir}/fluent-bit/
cp -r %{venv_site}/smartx_app/elf/exporter/elf_exporter.json %{buildroot}%{_sysconfdir}/aquarium/register_conf/
cp -r %{venv_site}/smartx_vmtools/fluent-bit/input_svt.conf %{buildroot}%{_sysconfdir}/fluent-bit/
cp -r %{venv_site}/smartx_vmtools/fluent-bit/parser_svt.conf %{buildroot}%{_sysconfdir}/fluent-bit/

# libvirt tls related
mkdir -p %{buildroot}%{_sysconfdir}%{key_dir}
mkdir -p %{buildroot}%{_sysconfdir}/pki/CA
mkdir -p %{buildroot}%{_sysconfdir}%{qemu_tls_dir}

install -m 644 %{_sourcedir}/etc%{cert} %{buildroot}%{_sysconfdir}%{cert}
install -m 644 %{_sourcedir}/etc%{servercert} %{buildroot}%{_sysconfdir}%{servercert}
install -m 644 %{_sourcedir}/etc%{serverkey} %{buildroot}%{_sysconfdir}%{serverkey}
install -m 644 %{_sourcedir}/etc%{clientcert} %{buildroot}%{_sysconfdir}%{clientcert}
install -m 644 %{_sourcedir}/etc%{clientkey} %{buildroot}%{_sysconfdir}%{clientkey}
install -m 644 %{_sourcedir}/etc%{cert} %{buildroot}%{_sysconfdir}%{qemu_ca}
install -m 644 %{_sourcedir}/etc%{servercert} %{buildroot}%{_sysconfdir}%{qemu_servercert}
install -m 644 %{_sourcedir}/etc%{serverkey} %{buildroot}%{_sysconfdir}%{qemu_serverkey}
install -m 644 %{_sourcedir}/etc%{clientcert} %{buildroot}%{_sysconfdir}%{qemu_clientcert}
install -m 644 %{_sourcedir}/etc%{clientkey} %{buildroot}%{_sysconfdir}%{qemu_clientkey}


%files
%{venv_path}/

%{_bindir}/elf-exporter
%{_bindir}/elf-tool
%{_bindir}/elf-rest-server
%{_bindir}/elf-vm-monitor
%{_bindir}/elf-vm-scheduler
%{_bindir}/vmtools-agent
%{_bindir}/vmtools-agent-cli

# copy from data
%{_sysconfdir}/logrotate.tuna.d/elf-rest-log
%{_sysconfdir}/logrotate.tuna.d/elfexporterlog
%{_sysconfdir}/logrotate.tuna.d/elf-vm-monitorlog
%{_sysconfdir}/logrotate.tuna.d/libvirtlog
%{_sysconfdir}/rsyslog.d/elf_vm_monitor.conf
%{_sysconfdir}/rsyslog.d/libvirtd.conf
%{_sysconfdir}/elfvirt/vmtools_agent.conf
%{_sysconfdir}/elfvirt/elfvirt.conf.tmpl
%{_sysconfdir}/elfvirt/migrate.conf
%{_sysconfdir}/cron.d/clean_screenshot
%{_datadir}/elfvirt/config/elf-exporter/
%{_datadir}/elfvirt/config/elf-rest/
%{_datadir}/elfvirt/script/clean_screenshot.sh
%{_datadir}/elfvirt/script/configure_libvirtd.sh

# copy from venv
%{_sysconfdir}/aquarium/register_conf/elf_exporter.json
%{_sysconfdir}/aquarium/register_conf/elf_rest.json
%{_sysconfdir}/fluent-bit/input_elf_rest.conf
%{_sysconfdir}/fluent-bit/parser_elf_rest.conf
%{_sysconfdir}/fluent-bit/input_svt.conf
%{_sysconfdir}/fluent-bit/parser_svt.conf

%if %{use_systemd}
%config %{_unitdir}/elf-vm-monitor.service
%config %{_unitdir}/elf-exporter.service
%config %{_unitdir}/elf-rest-server.service
%config(noreplace) %{_unitdir}/elf-vm-scheduler.service
%config(noreplace) %{_unitdir}/vmtools-agent.service
%else
%{_sysconfdir}/init/elf-vm-monitor.conf
%{_sysconfdir}/init/elf-exporter.conf
%{_sysconfdir}/init/elf-vm-scheduler.conf
%{_sysconfdir}/init/vmtools-agent.conf
%endif

%attr(0755, root, root) %{_libdir}/libvirt/lock-driver/allock.so

# libvirt tls related
%dir %attr(755, qemu, root) %{_sysconfdir}/pki/libvirt
%dir %attr(750, qemu, root) %{_sysconfdir}/pki/libvirt/private
%dir %attr(755, qemu, root) %{_sysconfdir}%{qemu_tls_dir}
%attr(444, root, root) %{_sysconfdir}%{cert}
%attr(440, qemu, root) %{_sysconfdir}%{servercert}
%attr(440, qemu, root) %{_sysconfdir}%{serverkey}
%attr(440, qemu, root) %{_sysconfdir}%{clientcert}
%attr(440, qemu, root) %{_sysconfdir}%{clientkey}
%attr(444, qemu, root) %{_sysconfdir}%{qemu_ca}
%attr(440, qemu, root) %{_sysconfdir}%{qemu_servercert}
%attr(440, qemu, root) %{_sysconfdir}%{qemu_serverkey}
%attr(440, qemu, root) %{_sysconfdir}%{qemu_clientcert}
%attr(440, qemu, root) %{_sysconfdir}%{qemu_clientkey}


# Udpate the procurator's ctime no mater if the qemu is updated,
# to make sure the libvirt can refresh the qemu-kvm's current capabilities.
%post
    test -f /usr/libexec/procurator && touch /usr/libexec/procurator || true
/bin/bash %{_datadir}/elfvirt/script/configure_libvirtd.sh %{_sysconfdir}/libvirt/libvirtd.conf

# ======================================================
# Finally, the changelog:
# ======================================================
%changelog
%(cd %{workspace_dir} && git log -n 50 --format="* %cd %aN%n- (%h) %s%d%n" --date=local | sed -r 's/[0-9]+:[0-9]+:[0-9]+ //')
