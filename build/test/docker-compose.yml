version: "3.8"

services:
  unittest:
    container_name: pyzbs-unittest-${COMMIT_ID}
    depends_on:
      - mongo_host
    image: ${UNITTEST_IMG_NAME}
    command: bash -x ./build/test/script/init_env.sh
    privileged: true
    ulimits:
      nofile:
        soft: 1048576
        hard: 1048576
    volumes:
      - "/tmp/tuna-python3/.cache/pip:/root/.cache/pip"
      - "/tmp/tuna-python3/.cache/pypoetry:/root/.cache/pypoetry"
      - "${WORKSPACE_DIR}:/pyzbs"
    devices:
      - "/dev/mem:/dev/mem"
    working_dir: "/pyzbs"
    environment:
      - COMMIT_ID=${COMMIT_ID}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:1313"]
      interval: 30s
      timeout: 10s
      retries: 10
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '8.00'
          memory: 3G
        reservations:
          cpus: '2.00'
          memory: 1G

  mongo_host:
    container_name: mongo-host-${COMMIT_ID}
    image: ${MONGO_IMG}
    command: bash -x ./build/test/script/run-mongo.sh
    volumes:
      - "${WORKSPACE_DIR}:/pyzbs"
    working_dir: "/pyzbs"
    deploy:
      resources:
        limits:
          cpus: '1.00'
          memory: 1G
        reservations:
          cpus: '1.00'
          memory: 1G
