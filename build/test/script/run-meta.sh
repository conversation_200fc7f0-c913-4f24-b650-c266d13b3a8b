#!/bin/bash

set -x

BASE_DIR="/var/lib/zbs"

zk_ip=127.0.0.1  # `getent hosts zk_host | awk '{ print $1 }'`
meta_ip=127.0.0.1  # `getent hosts meta_host | awk '{ print $1 }'`

cat > /etc/sysconfig/zbs-metad << EOF
base_dir=/var/lib/zbs
meta_root=/var/lib/zbs/metad
foreground=true

leveldb_path=/var/lib/zbs/metad/db/

ZBS_METAD_OPTIONS="-base_dir=/var/lib/zbs -foreground=false -leveldb_path=/var/lib/zbs/metad/db/"
META_ADDR=${meta_ip}
META_PORT=10100
EOF

. /etc/sysconfig/zbs-metad
zbs-metad -base_dir=${BASE_DIR} -foreground=false -zk_hosts=${zk_ip}:2181 -meta_addr=${meta_ip}