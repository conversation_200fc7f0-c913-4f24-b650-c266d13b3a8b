#!/bin/bash

set -ex

[[ -z "$COMMIT_ID" ]] && echo "missing \$COMMIT_ID arguments" && exit 1
top_dir=$(pwd)

# make install/spec_deps

zkServer.sh start
bash -x ./build/test/script/run-task.sh
bash -x ./build/test/script/run-meta.sh
bash -x ./build/test/script/run-chunk.sh

sleep 15
# run this script from root folder of the pyzbs repo

CHUNK_PORT=10200

WAITE_MAX=3

meta_ip=127.0.0.1  # `getent hosts meta_host | awk '{ print $1 }'`
chunk_ip=127.0.0.1 # `getent hosts chunk_host | awk '{ print $1 }'`

ip a
function log() {
	ps aux

	# show kernel version
	uname -a

	echo rpm package version
	rpm -qa | grep -E "zbs|qemu|libvirt"
}

function die() {
	echo $1
	exit 1
}

function mount_journal() {
	# mount zbs/journal
	zbs-chunk --chunk_ip ${chunk_ip} journal format --force /zbs/journal-1
	zbs-chunk --chunk_ip ${chunk_ip} journal mount /zbs/journal-1
}

function mount_chunk_partition() {
	zbs-chunk --chunk_ip ${chunk_ip} partition format --force /tmp/zbs/partition-1
	zbs-chunk --chunk_ip ${chunk_ip} partition mount /tmp/zbs/partition-1
}

function config_zbs() {
	# set meta cluster
	zbs-tool service set meta ${meta_ip}:10100
	# register
	sleep 5
	zbs-meta --meta_ip ${meta_ip} chunk register ${chunk_ip} ${CHUNK_PORT}

	mount_journal
	mount_chunk_partition

	declare -i i=0
	while (($i <= ${WAITE_MAX})); do
		ret=$(zbs-meta chunk list | grep CONNECTED_HEALTHY)
		if [[ ${ret} != "" ]]; then
			break
		fi
		i=${i}+1
		sleep 5
	done
}

function show_zbs_status() {
	echo "zbs status listed below:"
	zbs-meta --meta_ip ${meta_ip} chunk list
	zbs-chunk --chunk_ip ${chunk_ip} partition list
	#netstat -tnlp
}

function enable_ipv6() {
	echo "sysctl net.ipv6.conf.all.disable_ipv6=0"
	sysctl net.ipv6.conf.all.disable_ipv6=0
}

function setup_cluster_info() {
    /usr/sbin/zkCli.sh -server 127.0.0.1:2181 create /tuna
    /usr/sbin/zkCli.sh -server 127.0.0.1:2181 create /tuna/cfg
    /usr/sbin/zkCli.sh -server 127.0.0.1:2181 create /tuna/cfg/cluster_uuid
    /usr/sbin/zkCli.sh -server 127.0.0.1:2181 create /tuna/cfg/cluster_name
    /usr/sbin/zkCli.sh -server 127.0.0.1:2181 set /tuna/cfg/cluster_uuid "e3d8d1a5-f218-4082-8933-335e425506e8"
    /usr/sbin/zkCli.sh -server 127.0.0.1:2181 set /tuna/cfg/cluster_name "test_cluster"
}

mkdir -p /etc/zbs
mkdir -p /var/log/zbs

cat >/etc/zbs/zbs.conf <<EOF
[network]
data_ip=${chunk_ip}
heartbeat_ip=127.0.0.1
vm_ip=127.0.0.1
web_ip=127.0.0.1

[cluster]
role=standalone
members=${chunk_ip}
zookeeper=127.0.0.1:2181
mongo=mongo_host:27017
EOF

cat >/etc/zbs/uuid <<EOF
387dad3d-6e7d-4ba1-b17f-3fd75fcb8620
EOF

cat >/etc/zbs/role <<EOF
master
EOF

cat >>/etc/resolv.conf <<EOF
nameserver  ************
EOF

touch /var/lib/rpm/*

config_zbs
show_zbs_status
enable_ipv6
setup_cluster_info
log

make gen/proto
rm -rf ./build/dist/test
if [ "${COMMIT_ID}" == "debug" ]; then
	mkdir -p ./build/dist/test/report/
	make report/server
else
	make test/run APPS="elf tuna job-center pyzbs"
	# tmp fix unittest mkdir node_info_*
	find . -name 'node_info_*' -type d -print | xargs rm -rf
fi
