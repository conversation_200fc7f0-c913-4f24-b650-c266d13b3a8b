#!/bin/bash

[[ -z "$APPS" ]] && echo "missing \$APPS arguments" && exit 1

set -ex

function die () {
    local message=$1
    [ -z "$message" ] && message="Died"
    echo "$message (at ${BASH_SOURCE[1]}:${FUNCNAME[1]} line ${BASH_LINENO[0]}.)" >&2
    exit 1
}

# run unittest
function run_unittest () {
    export GRPC_ENABLE_FORK_SUPPORT=0

    for APP in ${APPS}; do
        echo "run ${APP} unittest..."

        modules=$(cat venv/${APP}/pyproject.toml |grep pytest_include |cut -d'=' -f2 |tr -d '"' |awk '{$1=$1};1')
        echo "${APP} venv test modules: ${modules}"
        all_modules="${all_modules} ${modules}"


        test_cmd="venv/${APP}/.venv/bin/coverage run -m gevent.monkey --module pytest --reruns 2 --reruns-delay 5 "
        test_cmd="${test_cmd} --timeout=150 --import-mode=importlib --html=${report_dir}/${APP}-unittest-report.html"
        test_cmd="${test_cmd} ${modules} --log-level=DEBUG --log-cli-level=DEBUG -s"
        echo "test_cmd: ${test_cmd}"

        make env/dev APPS=${APP} && ${test_cmd} 2>&1 | tee -a ${report_dir}/${APP}-shell.log &
    done
    wait
}

function combine_coverage_data() {
    # use tuna venv to run diff-cover and coverage
    # we can set coverage version in tuna venv
    venv/tuna/.venv/bin/coverage combine
    venv/tuna/.venv/bin/coverage html -d ${report_dir}/cover-html/
    venv/tuna/.venv/bin/coverage xml -o ${report_dir}/coverage.xml
}

# check unittest result
function check_unittest_result () {
    for APP in ${APPS}; do
        echo "Check ${APP} unittest result"
        grep '>0 errors<' ${report_dir}/${APP}-unittest-report.html | grep '>0 failed<' 1> /dev/null || die "${APP} Test suite failed"
    done
}

echo "delete old coverage file"
rm -rf .coverage .coverage.*

#workaround for failed unit testcases on branch v3.5.x
#Issues:TUNA-2284, TUNA-2285
echo "kvm" >> /etc/zbs/platform

all_modules=""
report_dir=build/dist/test/report
mkdir -p ${report_dir}

run_unittest
combine_coverage_data
check_unittest_result

# check code diff coverage
if ! git log -n 1 HEAD |grep "no-code-coverage-check"; then
    echo "Checking pyzbs total code diff coverage..."
    venv/tuna/.venv/bin/diff-cover "${report_dir}/coverage.xml" \
    --html-report build/dist/test/report/diff-cover.html \
    --compare-branch=HEAD^ \
    --fail-under=75 \
    || die "Check pyzbs total code diff coverage failed"
fi

# report code coverage
set +x
all_modules=$(echo "${all_modules}" | tr ' ' '\n' | sort -u | tr '\n' ' ')
echo "all_modules_arr: ${all_modules}"
for test_module in ${all_modules}; do
    module_name=${test_module%/test/}
    module_name=${module_name%/test}
    module_name=${module_name%/tests/}
    module_name=${module_name%/tests}
    module_name=${module_name%/}
    venv/tuna/.venv/bin/coverage report --include="${module_name}/*" | grep TOTAL | sed "s:TOTAL:${module_name}:"
done

exit 0
