#!/bin/bash

set -x

sleep 5

BASE_DIR="/var/lib/zbs"

zk_ip=127.0.0.1  # `getent hosts zk_host | awk '{ print $1 }'`
task_ip=127.0.0.1  # `getent hosts task_host | awk '{ print $1 }'`

cat > /etc/sysconfig/zbs-taskd << EOF
base_dir=/var/lib/zbs
task_root=/var/lib/zbs/taskd
foreground=true

leveldb_path=/var/lib/zbs/taskd/db/
ZBS_TASKD_OPTIONS="-foreground=true -leveldb_path=/var/lib/zbs/taskd/db/"
TASKD_ADDR=${task_ip}
TASKD_DISPATCHER_PORT=10600
TASKD_RUNNER_PORT=10601
EOF

. /etc/sysconfig/zbs-taskd
zbs-taskd -foreground=false -taskd_zk_hosts=${zk_ip}:2181