#!/bin/bash

set -x

BASE_DIR="/var/lib/zbs"

mkdir -p /zbs
truncate -s 20G /zbs/journal-1

mkdir -p /tmp/zbs
truncate -s 10G /tmp/zbs/partition-1

zk_ip=127.0.0.1  # `getent hosts zk_host | awk '{ print $1 }'`
chunk_ip=127.0.0.1  # `getent hosts chunk_host | awk '{ print $1 }'`
cat > /etc/sysconfig/zbs-chunkd << EOF
CHUNK_SERVER_RPC_IP=${chunk_ip}
CHUNK_SERVER_RPC_PORT=10200
CHUNK_SERVER_DATA_IP=${chunk_ip}
CHUNK_SERVER_DATA_PORT=10201
ZK_HOSTS=${zk_ip}:2181
EOF

. /etc/sysconfig/zbs-chunkd
zbs-chunkd -base_dir=${BASE_DIR} -foreground=false -chunk_server_rpc_ip=${chunk_ip} \
                                                  -chunk_server_data_ip=${chunk_ip} \
                                                  -zk_hosts=${zk_ip}:2181