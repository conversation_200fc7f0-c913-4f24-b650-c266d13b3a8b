#!/bin/bash
# -*- coding: utf-8 -*-
# Copyright (c) 2013-2015, SMARTX
# All rights reserved.

set -x

mkdir -p /var/log/mongodb
mkdir -p /var/lib/mongodb
touch /var/log/mongodb/mongod.log

/opt/mongodb/5.0.13/bin/mongod -f ./build/test/conf/mongod.conf

sleep 3

echo 'rs.status();' | mongo
# echo 'rs.initiate({"_id": "zbs","members":[{"_id":0, "host": ":27017"}]})' | mongo
echo 'rs.initiate({"_id": "zbs","members":[{"_id":0, "host": "mongo_host:27017"}]})' | mongo

sleep 3

if [ -z "$( echo "rs.status()" | mongo | grep '"ok" : 1' )" ]; then exit 1;  fi

tail -f /dev/null
