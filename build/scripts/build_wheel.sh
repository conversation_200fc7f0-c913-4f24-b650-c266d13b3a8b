#!/bin/bash

set -e

[[ -z "$WHEEL_ROOT" ]] && echo "missing \$WHEEL_ROOT arguments" && exit 1
[[ -z "$APPS" ]] && echo "missing \$WHEEL_ROOT arguments" && exit 1

make gen/proto

for APP in ${APPS}; do
	echo "building ${APP} wheel"

	poetry -C venv/${APP} update zbs-client-py --lock
	poetry -C venv/${APP} lock --check
	mkdir -p build/dist/deps/${APP}
	poetry -C venv/${APP} export -f requirements.txt --output build/dist/deps/${APP}/requirements.txt

	cp ./venv/${APP}/poetry.lock ./venv/${APP}/pyproject.toml ./
	poetry build -f wheel

	mkdir -p ${WHEEL_ROOT}
	cp -r ./dist/* ${WHEEL_ROOT}
	rm -rf ./poetry.lock ./pyproject.toml ./dist
done
