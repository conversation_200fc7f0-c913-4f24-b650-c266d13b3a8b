#!/bin/bash

set -e

[[ -z "$PYTHON3" ]] && echo "missing \PYTHON3 arguments" && exit 1

workspace_dir=$(pwd)

#  sample of git describe output is v5.1.0-rc1-7-g893cba736
git_desc=$(git describe --always --long --match 'v[0-9]*' HEAD| sed -E 's/-/ /g')
# rc tag
rc_id=$(echo ${git_desc} | awk '{print $2}')
# additional commits number after git tag
commit_num=$(echo ${git_desc} | awk '{print $3}')
# commit id of HEAD with "g" + 7-char abbreviation of full commit id
commit_id=$(echo ${git_desc} | awk '{print $4}')
GITTAG=${rc_id}.${commit_num}.git.${commit_id}
VERSION=$(echo ${git_desc} | awk '{print $1}')
RPM_VERSION=$(echo ${VERSION} | sed 's/v//')

#### start oem section ###
OEM_PKG_NAME="tuna-product-customize"
OLD_OEM_PKG_NAME="smartx-oem"
isOEM=${1-${isOEM}}
oemVersion=${2-${oemVersion}}
OEM_MACHINE_MODEL=${3-${OEM_MACHINE_MODEL}}
OEM_HYPERVISOR_NAME=${4-${OEM_HYPERVISOR_NAME}}
OEM_SOFTWARE_VERSION=${5-${OEM_SOFTWARE_VERSION}}
OEM_STORAGE_NAME=${6-${OEM_STORAGE_NAME}}

function set_oem_default(){
    isOEM="false"
    oemVersion="NONE"
    OEM_MACHINE_MODEL="NONE"
    OEM_HYPERVISOR_NAME="NONE"
    OEM_SOFTWARE_VERSION="NONE"
    OEM_STORAGE_NAME="NONE"
}

function gen_oem_pkg_name(){
    echo "isOEM is ${isOEM}, oemVersion is ${oemVersion}"

    if [ -z ${isOEM} ];then
        set_oem_default
        echo "Build without oem command";
    else
        if [ "${isOEM}" == "true" ];then
            OEM_PKG_NAME=${OEM_PKG_NAME}.${oemVersion}
            OLD_OEM_PKG_NAME=${OLD_OEM_PKG_NAME}.${oemVersion}
        else
            set_oem_default
        fi
    fi
}

gen_oem_pkg_name
#### end oem section ###

if [ -z "${OEM_MACHINE_MODEL}" ]; then
    OEM_MACHINE_MODEL="%{nil}"
fi

if [ -z "${OEM_HYPERVISOR_NAME}" ]; then
    OEM_HYPERVISOR_NAME="%{nil}"
fi

if [ -z "${OEM_SOFTWARE_VERSION}" ]; then
    OEM_SOFTWARE_VERSION="%{nil}"
fi

dist=".el7.centos"
if [ "${DISTRO}" == "centos" ]; then
    dist=".el7.centos";
elif [ "${DISTRO}" == "kylin" ]; then
    dist=".ky10";
elif [ "${DISTRO}" == "openeuler" ]; then
    dist=".oe1";
fi

python3_version=$(${PYTHON3} --version |cut -d' ' -f2)

echo "start build ${OEM_PKG_NAME} RPM"
BUILD_DIR=/tmp/rpm/${OEM_PKG_NAME}

# create RPM dirs，and copy source files
mkdir -p ${BUILD_DIR}/{BUILD,BUILDROOT,RPMS,SOURCES,SPECS,SRPMS}
SPEC_ROOT=./build/rpm/tuna-product-customize
cp -ar ${SPEC_ROOT}/SPECS/* ${BUILD_DIR}/SPECS/
cp -ar ${SPEC_ROOT}/data/* ${BUILD_DIR}/SOURCES/
cd ${BUILD_DIR}/

rpmbuild -bb -v \
        --define "workspace_dir ${workspace_dir}" \
        --define "python3_command ${PYTHON3}" \
        --define "python3_version ${python3_version}" \
        --define "RPM_VERSION ${RPM_VERSION}" \
        --define "GITTAG ${GITTAG}" \
        --define "dist ${dist}" \
        --define "_topdir $(pwd)" \
        --define "isOEM ${isOEM}"                      \
        --define "OEM_PKG_NAME ${OEM_PKG_NAME}"        \
        --define "OLD_OEM_PKG_NAME ${OLD_OEM_PKG_NAME}"        \
        --define "OEM_VENDER ${oemVersion}"            \
        --define "OEM_MACHINE_MODEL ${OEM_MACHINE_MODEL}" \
        --define "OEM_SOFTWARE_VERSION ${OEM_SOFTWARE_VERSION}" \
        --define "OEM_HYPERVISOR_NAME ${OEM_HYPERVISOR_NAME}" \
        --define "OEM_STORAGE_NAME ${OEM_STORAGE_NAME}" \
        --buildroot=${BUILD_DIR}/BUILDROOT SPECS/rpm.spec
RC=$?

if [ "$RC" -ne 0 ]; then
    echo "Failed to build rpm."
    exit 1
fi

cd ${workspace_dir}
mkdir -p ${workspace_dir}/build/dist/RPMS/${DISTRO}/$(arch)
cp -r ${BUILD_DIR}/RPMS/* ${workspace_dir}/build/dist/RPMS/${DISTRO}/

exit 0
