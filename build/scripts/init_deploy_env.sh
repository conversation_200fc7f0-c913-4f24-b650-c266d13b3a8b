#!/bin/bash

set -e

[[ -z "$APPS" ]] && echo "missing \$APP arguments" && exit 1
[[ -z "$PYTHON3" ]] && echo "missing \PYTHON3 arguments" && exit 1

for APP in ${APPS}; do
	echo "start init ${APP} deploy venv ..."

	${PYTHON3} -m venv /usr/local/venv/${APP}
	/usr/local/venv/${APP}/bin/pip install wheel --trusted-host pypi.smartx.com -i http://pypi.smartx.com/simple
	/usr/local/venv/${APP}/bin/pip install -r build/dist/deps/${APP}/requirements.txt --trusted-host pypi.smartx.com

    # install all applications in the %{WHEEL_ROOT} folder
	whl_name=${APP}
	if [ "${whl_name}" == "job-center" ]; then
		whl_name="job_center"
	fi

	find . -path ./build/dist/wheel/${whl_name}* -print0 |xargs /usr/local/venv/${APP}/bin/pip install \
		--trusted-host pypi.smartx.com -i http://pypi.smartx.com/simple

	${PYTHON3} -m compileall /usr/local/venv/${APP}/ -f
	echo "init ${APP} deploy venv success"
done

