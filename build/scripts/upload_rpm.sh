#!/bin/bash

set -e -x

[[ -z "$src_rpm_path" ]] && echo "missing \src_rpm_path arguments" && exit 1
[[ -z "$yumrepo_dir" ]] && echo "missing \yumrepo_dir arguments" && exit 1

if [[ ! -d ${src_rpm_path} ]]; then
  echo "rpm folder not found"
  exit 1
fi


function upload() {
  sshpass -p 'HC!r0cks' rsync -e "ssh -o StrictHostKeyChecking=no" -avuP ${src_rpm_path} root@*************:${yumrepo_dir}
}

upload