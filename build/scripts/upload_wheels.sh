#!/usr/bin/env bash

set -e -x

[[ -z "$PYTHON3" ]] && echo "missing \PYTHON3 arguments" && exit 1
[[ -z "$APPS" ]] && echo "missing \APPS arguments" && exit 1

workspace_dir=$(pwd)
mkdir -p ${workspace_dir}/build/dist/cache/wheels

for APP in ${APPS}; do
	cd ${workspace_dir}/venv/${APP}
	poetry export -f requirements.txt --output requirements.txt --without-hashes --with dev --with test --without-urls

	cp ${workspace_dir}/venv/${APP}/requirements.txt ${workspace_dir}/build/dist/cache/wheels
	cd ${workspace_dir}/build/dist/cache/wheels
	${PYTHON3} -m pip download -r requirements.txt download -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple --extra-index-url http://*************:1213/simple --trusted-host *************
	rm -rf requirements.txt
done

rsync -avuP ${workspace_dir}/build/dist/cache/wheels/ root@*************:/data/pypipackages/

for APP in ${APPS}; do
	cd ${workspace_dir}/venv/${APP}
	poetry lock --no-update
done

