#!/bin/bash

set -ex

[[ -z "$APPS" ]] && echo "missing \$APPS arguments" && exit 1
[[ -z "$PYTHON3" ]] && echo "missing \PYTHON3 arguments" && exit 1
[[ -z "$ZBS_VERSION" ]] && echo "missing \ZBS_VERSION arguments" && exit 1

workspace_dir=$(pwd)

#  sample of git describe output is v5.1.0-rc1-7-g893cba736
git_desc=$(git describe --always --long --match 'v[0-9]*' HEAD | sed -E 's/-/ /g')
# rc tag
rc_id=$(echo $git_desc | awk '{print $2}')
# additional commits number after git tag
commit_num=$(echo $git_desc | awk '{print $3}')
# commit id of HEAD with "g" + 7-char abbreviation of full commit id
commit_id=$(echo $git_desc | awk '{print $4}')
GITTAG=${rc_id}.${commit_num}.git.${commit_id}

VERSION=$(echo $git_desc | awk '{print $1}')
RPM_VERSION=$(echo $VERSION | sed 's/v//')
ELF_LIBVIRT_VERSION_EL7=$(echo $ELF_LIBVIRT_VERSION_EL7)
ELF_LIBVIRT_VERSION_OE2003=$(echo $ELF_LIBVIRT_VERSION_OE2003)
ELF_LIBVIRT_VERSION_TL3=$(echo $ELF_LIBVIRT_VERSION_TL3)

dist=".el7.centos"
if [ "${DISTRO}" == "centos" ]; then
	dist=".el7.centos"
elif [ "${DISTRO}" == "kylin" ]; then
	dist=".ky10"
elif [ "${DISTRO}" == "openeuler" ]; then
	dist=".oe1"
elif [ "${DISTRO}" == "tencent" ]; then
	dist=".tl3"
fi

python3_version=$(${PYTHON3} --version | cut -d' ' -f2)

for APP in ${APPS}; do
	echo "start build ${APP} RPM"
	# install application and dependencies in venv
	make env/deploy APPS="${APP}" PYTHON3="${PYTHON3}"

	BUILD_DIR=/tmp/rpm/${APP}

	# create RPM dirs，and copy source files
	mkdir -p ${BUILD_DIR}/{BUILD,BUILDROOT,RPMS,SOURCES,SPECS,SRPMS}
	SPEC_ROOT=./build/rpm/${APP}
	cp -ar ${SPEC_ROOT}/SPECS/* ${BUILD_DIR}/SPECS/
	cp -ar ${SPEC_ROOT}/data/* ${BUILD_DIR}/SOURCES/
	cp -ar /usr/local/venv/${APP}/* ${BUILD_DIR}/BUILD/
	cd ${BUILD_DIR}/

	rpmbuild -bb -v \
		--define "RPM_VERSION ${RPM_VERSION}" \
		--define "ZBS_VERSION ${ZBS_VERSION}" \
		--define "GITTAG ${GITTAG}" \
		--define "python3_version ${python3_version}" \
		--define "dist ${dist}" \
		--define "workspace_dir ${workspace_dir}" \
		--define "_topdir $(pwd)" \
		--define "ELF_LIBVIRT_VERSION_EL7 ${ELF_LIBVIRT_VERSION_EL7}" \
		--define "ELF_LIBVIRT_VERSION_OE2003 ${ELF_LIBVIRT_VERSION_OE2003}" \
		--define "ELF_LIBVIRT_VERSION_TL3 ${ELF_LIBVIRT_VERSION_TL3}" \
		--buildroot=${BUILD_DIR}/BUILDROOT SPECS/rpm.spec
	RC=$?

	if [ "$RC" -ne 0 ]; then
		echo "Failed to build rpm."
		exit 1
	fi

	cd ${workspace_dir}
	mkdir -p ${workspace_dir}/build/dist/RPMS/${DISTRO}/$(arch)
	cp -r ${BUILD_DIR}/RPMS/* ${workspace_dir}/build/dist/RPMS/${DISTRO}/
done

exit 0
