#!/bin/bash

set -e

[[ -z "$COMMIT_ID" ]] && echo "missing \$COMMIT_ID arguments" && exit 1
[[ -z "$WORKSPACE_DIR" ]] && echo "missing \$WORKSPACE_DIR arguments" && exit 1
[[ -z "$UNITTEST_IMG_NAME" ]] && echo "missing \$UNITTEST_IMG_NAME arguments" && exit 1
[[ -z "$MONGO_IMG" ]] && echo "missing \$MONGO_IMG arguments" && exit 1

echo "Run unittest container in debug mode"

BUILD_TEST_PATH="./build/test"
COMPOSE_ENV="${BUILD_TEST_PATH}/docker-compose.env"
COMPOSE_FILE="${BUILD_TEST_PATH}/docker-compose.yml"

cat > ${COMPOSE_ENV} << EOF
COMMIT_ID=${COMMIT_ID}
WORKSPACE_DIR=${WORKSPACE_DIR}
UNITTEST_IMG_NAME=${UNITTEST_IMG_NAME}
MONGO_IMG=${MONGO_IMG}

EOF

docker compose -p debug --env-file ${COMPOSE_ENV} -f ${COMPOSE_FILE} up -d --force-recreate --wait --wait-timeout 300

function enter_unittest_container(){
    echo "unittest Container is ready..."
    test_cmd="--log-level=DEBUG --log-cli-level=DEBUG -s"
    echo -e "\033[34m entering unittest container, if you want to run unittests \033[0m"
    echo -e "\033[34m 1. run init venv command: make env/dev APPS=tuna \033[0m"
    echo -e "\033[34m 2. run debug test command: venv/tuna/.venv/bin/python -m gevent.monkey --module pytest ${test_cmd} \033[0m"
    echo -e "\033[34m 3. do not forget to replace tuna with your venv name \033[0m"

    docker exec -it pyzbs-unittest-debug bash
}

enter_unittest_container

# clean compose env
rm -f ${COMPOSE_ENV}

exit 0
