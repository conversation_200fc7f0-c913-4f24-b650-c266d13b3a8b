#!/bin/bash

set -e

[[ -z "$ZBS_VERSION" ]] && echo "missing \$ZBS_VERSION arguments" && exit 1

WORKSPACE_PATH=$(pwd)

# if allock module no change
if [ "$(git show --name-only | grep allock)" != "" ]; then
    echo "Running allock unittests"
    cd ${WORKSPACE_PATH}/smartx_app/elf/allock/
    make clean
    make docker-unittests TARGET_DISTRO=${DISTRO} ZBS_VERSION=${ZBS_VERSION}
else
    echo -e "No change related to allock detected, skipping allock unittests"
fi
