#!/bin/bash
set -e

[[ -z "$PYTHON3" ]] && echo "missing \PYTHON3 arguments" && exit 1
[[ -z "$HARBOR_VERSION" ]] && echo "missing \$HARBOR_VERSION arguments" && exit 1

# install build tools
# we use python protobuf 3.x.x and grpcio-tools 1.48.2 to avoid the problem of zbs-client-py bytes to str
${PYTHON3} -m pip install --user grpcio-tools=="1.48.2"
yum install -y harbor-proto-${HARBOR_VERSION}

harbor_proto_version=`rpm -q harbor-proto`
echo "Building against: harbor-proto version $harbor_proto_version"

if [[ "$harbor_proto_version" != *"$HARBOR_VERSION"* ]]; then
    echo "harbor-proto version is wrong, expect $HARBOR_VERSION, but get $harbor_proto_version"
    exit 1
fi

version="v3"
source="/usr/include"
current_dir=$(dirname $0)
google_api_target=./
cmd="${PYTHON3} -m grpc_tools.protoc"

function generate {
    local target=$1;
    local project_name=$2;
    local package_name=$3;

    touch ${target}/__init__.py;

    for p in $(ls ${source}/smartx/${project_name}); do
        if [[ ${p} != ${version} ]];then
            # independent service, eg. `smartx/salmon/vms/v3/*.proto`
            ${cmd} -I=${source} --python_out=${target} --grpc_python_out=${target} ${source}/smartx/${project_name}/${p}/${version}/*.proto;
            mv ${target}/smartx/${project_name}/${p}/${version}/*.py ${target} && rm -r ${target}/smartx/${project_name}/${p};
        else
            # simple service, eg. `smartx/timemachine/v3/*.proto`
            ${cmd} -I=${source} --python_out=${target} --grpc_python_out=${target} ${source}/smartx/${project_name}/${version}/*.proto;
            mv ${target}/smartx/${project_name}/${version}/*.py ${target} && rm -r ${target}/smartx/${project_name}/${version};
        fi
    done

    # change the python package
    for p in $(ls ${target}/*.py); do
        # replace `from smartx.project_name.resource_name.v3`
        # and `from smartx.project_name.v3` to the specified package name.
        sed -ri "s/^from smartx\.(.+)\.v3/from smartx_proto.\1/" ${p};
        sed -ri "s/^from smartx_proto\.(.+)\.(.+) import (.+)/from smartx_proto.\1 import \3/" ${p};
    done

    # remove the tmp dir
    rm -r ${target}/smartx;
}

# generate the .pb files for google api
${cmd} -I=${source} --python_out=${google_api_target} --grpc_python_out=${google_api_target} ${source}/google/api/*.proto;

# generate the .pb files for crab project
generate "./smartx_proto/crab" "crab" "smartx_proto.crab";

# generate the dhcp .pb files for smartx_app.network project
generate "./smartx_proto/dhcp" "dhcp" "smartx_proto.dhcp";

# generate the .pb files for migrating from the original protect_center to timemachine
generate "./smartx_proto/timemachine" "timemachine" "smartx_proto.timemachine";

# generate the .pb files for smartx_vmtools_agent project
generate "./smartx_proto/smartx_vmtools_agent" "smartx_vmtools_agent" "smartx_proto.smartx_vmtools_agent";

# generate the .pb files for error codes
${cmd} --python_out="./smartx_proto/errors/" -I"./smartx_proto/errors/" pyerror.proto
