# install docker and docker compose v2 on centos8
.PHONY: install/docker
install/docker:
	@echo "Download wheel from $(WHEEL_URL)"
	PYTHON3="$(PYTHON3)" APPS="$(APPS)" bash ${SCRIPTS_DIR}/install_docker_compose.sh


.PHONY: upload/wheels
upload/wheels:
	@echo "Download wheel from $(WHEEL_URL)"
	PYTHON3="$(PYTHON3)" APPS="$(APPS)" bash ${SCRIPTS_DIR}/upload_wheels.sh


src_rpm_path ?= ""
yumrepo_dir  ?= ""
.PHONY: upload_rpms_to_yum
upload_rpms_to_yum:
	@echo "Upload rpms to yum repo"
	docker run --rm \
		-v=$(shell pwd):$(WORKDIR_IN_BUILDER_DIR) \
		-e src_rpm_path="$(src_rpm_path)" \
		-e yumrepo_dir="$(yumrepo_dir)" \
		$(BUILDER_IMAGE_NAME) \
		bash ${SCRIPTS_DIR}/upload_rpm.sh

# install/spec_deps install rpm spec buildRequires, only works when dnf-plugins-core installed
.PHONY: install/spec_deps
install/spec_deps:
	rpm -q dnf-plugins-core || exit 0; \
	for APP in $(APPS); do \
		echo "installing $$APP buildRequires..."; \
		dnf builddep --spec build/rpm/$$APP/SPECS/rpm.spec -y \
			--define "RPM_VERSION 0.0.0" \
			--define "GITTAG 0.0.0" \
			--define "python3_version 3.0.0" \
			--define "dist .oe1" \
			--define "workspace_dir $(WORKSPACE_DIR)" \
			--define "_topdir $(WORKSPACE_DIR)"; \
	done


GERRIT_CHANGE_ID		?=
GERRIT_COMMIT_ID		?=
GERRIT_COMMENT_MESSAGE  ?=
GERRIT_BASIC_AUTH		?= Basic cHl6YnNjaTpsTkJIU3c3TE8xUHlKQklsdHovNUdnYkpiaXBNdzY3eUFEVk9PRGUyanc=
.PHONY: gerrit_add_comment
gerrit_add_comment:
	curl -i \
		http://gerrit.smartx.com/a/changes/$(GERRIT_CHANGE_ID)/revisions/$(GERRIT_COMMIT_ID)/review \
		-X POST -d '{"message": "$(GERRIT_COMMENT_MESSAGE)"}' \
		--header "Content-Type: application/json" \
		--header "Authorization: $(GERRIT_BASIC_AUTH)"


# build/oem_rpm build oem RPM
.PHONY: build/oem_rpm
build/oem_rpm: build/docker
	docker run --rm \
		-v=$(shell pwd):$(WORKDIR_IN_BUILDER_DIR) \
		-e isOEM="$(IS_OEM)" \
		-e oemVersion="$(OEM_VERSION)" \
		-e OEM_MACHINE_MODEL="$(OEM_MACHINE_MODEL)" \
		-e OEM_HYPERVISOR_NAME="$(OEM_HYPERVISOR_NAME)" \
		-e OEM_SOFTWARE_VERSION="$(OEM_SOFTWARE_VERSION)" \
		-e OEM_STORAGE_NAME="$(OEM_STORAGE_NAME)" \
		-e PYTHON3="$(PYTHON3)" \
		-e DISTRO="${DISTRO}" \
		$(BUILDER_IMAGE_NAME) \
		bash -e ${SCRIPTS_DIR}/build_oem.sh

# oem variable
IS_OEM					?=
OEM_VERSION				?=
OEM_MACHINE_MODEL		?=
OEM_HYPERVISOR_NAME		?=
OEM_SOFTWARE_VERSION	?=
OEM_STORAGE_NAME		?=

.PHONY: rpms_oem_ali
rpms_oem_ali: IS_OEM = true
rpms_oem_ali: OEM_VERSION = AliyunHCI
rpms_oem_ali: OEM_MACHINE_MODEL =
rpms_oem_ali: OEM_HYPERVISOR_NAME =
rpms_oem_ali: OEM_SOFTWARE_VERSION = 1.1.0
rpms_oem_ali: OEM_STORAGE_NAME = sds
rpms_oem_ali: build/oem_rpm

.PHONY: rpms_oem_arcfra
rpms_oem_arcfra: IS_OEM = true
rpms_oem_arcfra: OEM_VERSION = Arcfra
rpms_oem_arcfra: OEM_MACHINE_MODEL =
rpms_oem_arcfra: OEM_HYPERVISOR_NAME =
rpms_oem_arcfra: OEM_SOFTWARE_VERSION =
rpms_oem_arcfra: OEM_STORAGE_NAME = zbs
rpms_oem_arcfra: build/oem_rpm