[OS]
name=OS
baseurl=http://repo.openeuler.org/openEuler-20.03-LTS-SP3/OS/$basearch/
enabled=1
gpgcheck=1
gpgkey=0

[everything]
name=everything
baseurl=http://repo.openeuler.org/openEuler-20.03-LTS-SP3/everything/$basearch/
enabled=1
gpgcheck=1
gpgkey=0

[EPOL]
name=EPOL
baseurl=http://repo.openeuler.org/openEuler-20.03-LTS-SP3/EPOL/main/$basearch/
enabled=1
gpgcheck=1
gpgkey=0

[debuginfo]
name=debuginfo
baseurl=http://repo.openeuler.org/openEuler-20.03-LTS-SP3/debuginfo/$basearch/
enabled=1
gpgcheck=1
gpgkey=0

[source]
name=source
baseurl=http://repo.openeuler.org/openEuler-20.03-LTS-SP3/source/
enabled=1
gpgcheck=1
gpgkey=0

[update]
name=update
baseurl=http://repo.openeuler.org/openEuler-20.03-LTS-SP3/update/$basearch/
enabled=1
gpgcheck=1
gpgkey=0

[smartxos-common]
name=smartxos common
baseurl=http://192.168.17.20/repo/pub/openeuler/oe2003/common/$basearch/
gpgcheck=0
enabled=1
metadata_expire=1

[smartxos-common-noarch]
name=smartxos common noarch
baseurl=http://192.168.17.20/repo/pub/openeuler/oe2003/common/noarch/
gpgcheck=0
enabled=1
metadata_expire=1

[smartx-iso-common-5.1.x]
name=smartx iso common 5.1.x
baseurl=http://192.168.17.20/repo/pub/openeuler/oe2003/smartx-iso-common-5.1.x/$basearch/
gpgcheck=0
enabled=1
metadata_expire=1

[smartxos-rc]
name=smartxos rc
baseurl=http://192.168.17.20/repo/pub/openeuler/oe2003/rc/$basearch/
gpgcheck=0
enabled=1
metadata_expire=1

[smartxos-smtx]
name=smartxos smtx
baseurl=http://192.168.17.20/repo/pub/openeuler/oe2003/smtx/$basearch/
gpgcheck=0
enabled=1
metadata_expire=1

