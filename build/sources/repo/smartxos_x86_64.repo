[base]
name=CentOS-$releasever - Base
#baseurl=http://mirror.centos.org/centos/$releasever/os/$basearch/
baseurl=http://192.168.17.20/repo/pub/centos/$releasever/os/$basearch/
gpgcheck=0

#released updates
[updates]
name=CentOS-$releasever - Updates
baseurl=http://192.168.17.20/repo/pub/centos/$releasever/os/$basearch/
#baseurl=http://mirror.centos.org/centos/$releasever/updates/$basearch/
gpgcheck=0
enabled=0

[extras]
name=CentOS-$releasever - Extras
baseurl=http://192.168.17.20/repo/pub/centos/$releasever/os/$basearch/
#baseurl=http://mirror.centos.org/centos/$releasever/extras/$basearch/
gpgcheck=0
enabled=0

#additional packages that extend functionality of existing packages
[centosplus]
name=CentOS-$releasever - Plus
baseurl=http://192.168.17.20/repo/pub/centos/$releasever/os/$basearch/
#baseurl=http://mirror.centos.org/centos/$releasever/centosplus/$basearch/
gpgcheck=0
enabled=0

[epel]
name=epel
#mirrorlist=https://mirrors.fedoraproject.org/metalink?repo=epel-7&arch=$basearch
baseurl=http://192.168.17.20/repo/pub/epel/$releasever/$basearch/
gpgcheck=0
enabled=1

[smartxos-common]
name=smartxos common
baseurl=http://192.168.17.20/repo/pub/smartxos/el7/common/
gpgcheck=0
enabled=1
metadata_expire=1

[smartx-iso-common-5.1.x]
name=smartx iso common 5.1.x
baseurl=http://192.168.17.20/repo/pub/smartxos/el7/smartx-iso-common-5.1.x/
gpgcheck=0
enabled=1
metadata_expire=1

[smartxos-base]
name=smartxos base
baseurl=http://192.168.17.20/repo/pub/smartxos/el7/2-rc/
gpgcheck=0
enabled=1
metadata_expire=1

[smartxos-smtx]
name=smartxos smtx
baseurl=http://192.168.17.20/repo/pub/smartxos/el7/smtx/
gpgcheck=0
enabled=1
metadata_expire=1