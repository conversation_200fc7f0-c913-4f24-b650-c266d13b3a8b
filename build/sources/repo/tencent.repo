[BaseOS]
name=TencentOS Server $releasever - Base
baseurl=http://192.168.26.252:5000/tlinux/$releasever/BaseOS/$basearch/
gpgcheck=0
enabled=1
module_hotfixes=1

[AppStream]
name=TencentOS Server $releasever - AppStream
baseurl=http://192.168.26.252:5000/tlinux/$releasever/AppStream/$basearch/
gpgcheck=0
enabled=1
module_hotfixes=1

[Extras]
name=TencentOS Server $releasever - Extras
baseurl=http://192.168.26.252:5000/tlinux/$releasever/Extras/$basearch/
gpgcheck=0
enabled=1
module_hotfixes=1

[PowerTools]
name=TencentOS Server $releasever - PowerTools
baseurl=http://192.168.26.252:5000/tlinux/$releasever/PowerTools/$basearch/
gpgcheck=0
enabled=1
module_hotfixes=1

[smartxos-common]
name=smartxos common
baseurl=http://192.168.17.20/repo/pub/tencentos/tl3/common/$basearch/
gpgcheck=0
enabled=1
metadata_expire=1

[smartxos-common-noarch]
name=smartxos common noarch
baseurl=http://192.168.17.20/repo/pub/tencentos/tl3/common/noarch/
gpgcheck=0
enabled=1
metadata_expire=1

[smartx-iso-common-5.1.x]
name=smartx iso common 5.1.x
baseurl=http://192.168.17.20/repo/pub/tencentos/tl3/smartx-iso-common-5.1.x/$basearch/
gpgcheck=0
enabled=1
metadata_expire=1

[smartxos-rc]
name=smartxos rc
baseurl=http://192.168.17.20/repo/pub/tencentos/tl3/rc/$basearch/
gpgcheck=0
enabled=1
metadata_expire=1

[smartxos-smtx]
name=smartxos smtx
baseurl=http://192.168.17.20/repo/pub/tencentos/tl3/smtx/$basearch/
gpgcheck=0
enabled=1
metadata_expire=1

