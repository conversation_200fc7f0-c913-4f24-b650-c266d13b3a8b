# How to

## Job submit

	from job_center.handler.leader.workers import job_submit

	job_submit(
		user="smartx",
		description="test",
		resources={
			"uuid-vm": {
    			"type": "kvm_vm",
        		"uuid": "uuid-123",
                "user": "",
                "vm_name": "my_vm_name_uuid",
                "vcpu": 1,
                "memory": 1024,
                "node_ip": "",
                "vm_ip": "",
                "vnc_port": -1,
                "status": "",
                "token": "",
                "vlan": [],
                "desc": "",
                "mac_addr": "",
                "create_date": int(time.time()),
                'boot_path': "",
                'disks': [],
                'cdrom':[],
                'bus':''
			}
		}
	)

## Keywords
	Job center： 工作中心
	job： 前后端交互单位，一个job包含一个或者多个任务，任务通过json进行描述
	task： job细分下的单位，内部使用，不对前端开放，或者前端只看到任务名称和状态，无法控制
	leader：领导者，这里指获取到job的celery worker，进行任务拆分，控制任务调度等
	follwer：跟从者，这里指获取到task得celery worker，无须感知其他任务，只需要完成当前任务并更新task的状态和job中task得状态
	DAG: 有向无环图，处理多任务下的依赖关系
	Splitter: 拆分器，集成于leader，用于根据前端提供的json和后端mongo保存的json进行对比，然后生成一系列的task供follower执行

## Work flow

	- job_submit worker will construct the complete job json description and submit to celery
	- leader_worker will get the job and start split to a list of follwers according to the splitter
	- use DAG and dependency to sort all the follwers
	- leader submit follwers to celery in chain mode
	- follower will be executed in distributed mode if no queue are specify
	- leader mark done if all follwers are finished or mark failed if one of subtask failed


