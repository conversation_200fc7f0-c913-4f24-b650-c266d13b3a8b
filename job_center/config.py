# Copyright (c) 2013-2015, SMARTX
# All rights reserved.


from datetime import timedelta
import os

import psutil

from common.config.constant import ZBS_CONFIG_FILE
from common.lib.cfg import Config
from common.lib.utils.config_util import get_host_config_uuid

JOB_CENTER_DB_NAME = "job_center"

RESOURCES_DB_NAME = "resources"

JOB_DB_NAME = "jobs"

JOB_CENTER_CELERY_META_COLLECTION = "jobs_meta"

JOB_CENTER_POOL_TYPE_GEVENT = "gevent"
JOB_CENTER_POOL_TYPE_FORK = "prefork"

JOB_CENTER_CONCURRENCY = 100

WORKER_CHECK_INTERVAL = 10  # seconds

VM_START_REBUILD_NUMBER = 3

JOB_CHECK_INTERVAL = 60

JOB_CLEAN_INTERVAL = 60 * 5

# celery related
accept_content = ["json"]

task_serializer = "json"

result_serializer = "json"

beat_schedule = {
    "job-check": {
        "task": "job_center.handler.scheduler.worker.submit_job_check",
        "schedule": timedelta(seconds=JOB_CHECK_INTERVAL),
    },
    "job-clean": {
        "task": "job_center.handler.scheduler.worker.submit_job_clean",
        "schedule": timedelta(seconds=JOB_CLEAN_INTERVAL),
    },
    "worker-check-custom": {
        "task": "job_center.handler.scheduler.worker.submit_worker_check_custom",
        "schedule": timedelta(seconds=WORKER_CHECK_INTERVAL),
    },
}

worker_hijack_root_logger = False

CURRENT_NODE_HOST_UUID = get_host_config_uuid()

CURRENT_NODE_IP = Config(ZBS_CONFIG_FILE).get("network", "data_ip")

CURRENT_NODE_VM_IP = Config(ZBS_CONFIG_FILE).get("network", "vm_ip")

LIBVIRT_LIVE_DETACH_DEVICE_TIMEOUT = 60  # seconds

MONGO_NODE_LIST = Config(ZBS_CONFIG_FILE).get("cluster", "mongo").split(",")

TIMEOUT_EACH_TASK = 60 * 20

MAX_IDE_DEVICES = 4

MAX_VIRTIO_DISK_DEVICES = 32

MAX_SCSI_DISK_DEVICES = 32
MAX_SCSI_DISK_DEVICES_FOR_SFS = 256

# For backwards compatible with one PCI bus(default pci-root,
# v3.0.7-rc2 pre VM have a maximum of 16 virtio disks or nics)
MAX_VIRTIO_DISK_DEVICES_FOR_BACKWARD_COMPATIBILITY = 16

# The maximum number of devices of per scsi controller support
SCSI_CONTROLLER_MAX_DEVICES_SUPPORT = 7

MAX_DISK_DEVICES = MAX_IDE_DEVICES + MAX_VIRTIO_DISK_DEVICES + MAX_SCSI_DISK_DEVICES
MAX_DISK_DEVICES_FOR_SFS = MAX_IDE_DEVICES + MAX_VIRTIO_DISK_DEVICES + MAX_SCSI_DISK_DEVICES_FOR_SFS

INTERNAL_SFS_PRODUCT = "sfs"
INTERNAL_ASIAINFO_PRODUCT = "asiainfo"

BASE_MODULES = [
    "job_center.handler.leader.workers",
    "job_center.handler.leader.base",
    "job_center.handler.hook.worker",
    "job_center.handler.scheduler.worker",
]

FOLLOWER_RETRY_MAX_NUMBER = 3
FOLLOWER_RETRY_INTERVAL = 3  # seconds


broker_connection_timeout = 5
broker_connection_retry = True
broker_connection_max_retries = 3
# broker_heartbeat = 10
# broker_heartbeat_checkrate = 2.0

process = psutil.Process(os.getpid())
process_cmdline = process.cmdline()

broker_transport_options = {
    "connect": False,
    "maxPoolSize": 3 if "worker" in process_cmdline else 2,
    "socketTimeoutMS": 5000,
    "connectTimeoutMS": 5000,
    "serverSelectionTimeoutMS": 5000,
    "w": 0,
}

worker_prefetch_multiplier = 1
worker_redirect_stdouts = False
worker_redirect_stdouts_level = "DEBUG"
broker_pool_limit = 3 if "worker" in process_cmdline else 2

RABBITMQ_USERNAME = "smartx"
RABBITMQ_PASSWORD = "smartx"


FOLLOWER_TIMEOUT = 60 * 15  # secs
LEADER_TIMEOUT = 60 * 60
LEADER_CHECK_FOLLOWER_INTERVAL = 10


CELERY_BEAT_SOCKET_TIMEOUT = 5  # s
