# Copyright (c) 2013-2023, SMARTX
# All rights reserved.
import celery

from job_center.common import constants


def get_mongo_ips():
    from common.config.constant import ZBS_CONFIG_FILE
    from common.lib.cfg import Config

    return ",".join(Config(ZBS_CONFIG_FILE).get_items("cluster", "mongo"))


def get_mongodb_broker():
    from common.mongo import url_cache
    from job_center import config

    mongodb_broker = f"mongodb://{get_mongo_ips()}/{config.JOB_CENTER_DB_NAME}"
    if url_cache.mongo_url_prefix:
        mongodb_broker = "{mongo_prefix_url}{mongo_ips}/{db_name}?authSource=admin".format(
            mongo_prefix_url=url_cache.mongo_url_prefix,
            mongo_ips=get_mongo_ips(),
            db_name=config.JOB_CENTER_DB_NAME,
        )

    return mongodb_broker


class Proxy:
    from job_center.monkey import patch_all

    patch_all()

    TASK_JOB_SUBMIT = "job_center.handler.leader.workers.job_submit"

    def __init__(self, name):
        self._app = celery.Celery(name, broker=get_mongodb_broker())

    def job_submit(
        self,
        user,
        description,
        type=constants.JOB_TYPE_ACTION,  # noqa: A002
        resources=None,
        schedule_task=None,
        one_time_task=None,
        queue=None,
        resource_group=None,
        job_id=None,
        event=None,
    ):
        return self._app.send_task(
            Proxy.TASK_JOB_SUBMIT,
            kwargs={
                "user": user,
                "description": description,
                "type": type,
                "resources": resources,
                "schedule_task": schedule_task,
                "one_time_task": one_time_task,
                "queue": queue,
                "resource_group": resource_group,
                "job_id": job_id,
                "event": event,
            },
        )
