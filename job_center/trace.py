# Copyright (c) 2013-2024, SMARTX
# All rights reserved.


import logging

from gevent import contextvars

DEFAULT_TRACE_ID = ""
TRACE_FILTER_ATTR = "trace_filter"

# the celery frame codebase does not have trace_id. in this situation, the trace_id should be empty
FORMAT_WITH_TRACE_ID = "[%(asctime)s: %(levelname)s/%(processName)s%(trace_id_delimiter)s%(trace_id)s] %(message)s"
LOGGING_FORMATTER = logging.Formatter(FORMAT_WITH_TRACE_ID, defaults={"trace_id": "", "trace_id_delimiter": ""})

trace_id = contextvars.ContextVar("trace_id", default=DEFAULT_TRACE_ID)


def GetTraceID() -> str:
    return trace_id.get()


def SetTraceID(tid: str):
    return trace_id.set(tid)


class TraceFilter(logging.Filter):
    def __init__(self, name=""):
        super().__init__(name)

    def filter(self, record):
        tid = GetTraceID()
        record.trace_id_delimiter = " trace_id=" if tid != DEFAULT_TRACE_ID else ""
        record.trace_id = tid

        return True
