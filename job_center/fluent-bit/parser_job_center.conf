[PARSER]
    Name job_center
    Format regex
    Regex ^\[(?<time>.+): (?<levelname>.+)\/(?<processName>.+)\] \[(?<name>.+):(?<result>.+)\] \[(?<source>.+):(?<userrole>.+):(?<username>.+)\] (?<resources>{.*}) (?<data>{.*}) (?<messages>{.+}) (?<details>{.+})$
    Time_Key time
    Decode_Field_As json resources
    Decode_Field_As json data
    Decode_Field_As json messages
    Decode_Field_As json details
    Time_Format %Y-%m-%d %H:%M:%S,%L