# Copyright (c) 2013-2015, SMARTX
# All rights reserved.

import gevent
from gevent.monkey import patch_all

patch_all(subprocess=True)
threadpool = gevent.hub.get_hub().threadpool
# Currently, each channel corresponds to a single native thread in the
# gevent threadpool. Thus, when the unit test suite spins up hundreds of
# channels concurrently, some will be starved out, causing the test to
# increase in duration. We increase the max size here so this does not
# happen.
threadpool.maxsize = 128
threadpool.size = 16

# Disable grpc fork support
import os  # noqa: E402

os.environ["GRPC_ENABLE_FORK_SUPPORT"] = "0"

from job_center.monkey import patch_all as patch_jc  # noqa: E402

patch_jc()

import logging  # noqa: E402
import pathlib  # noqa: E402
import shelve  # noqa: E402
import sys  # noqa: E402

from celery import Celery, platforms  # noqa: E402
from celery.apps.beat import Beat  # noqa: E402
import click  # noqa: E402

from common import utils as global_utils  # noqa: E402
from common.config.constant import REPSET_NAME  # noqa: E402
from common.mongo import url_cache  # noqa: E402
from job_center import config as job_center_config  # noqa: E402
from job_center import trace as job_center_trace  # noqa: E402
from job_center.common import constants, utils  # noqa: E402
from job_center.config import (  # noqa: E402
    BASE_MODULES,
    CURRENT_NODE_IP,
    JOB_CENTER_CONCURRENCY,
    JOB_CENTER_DB_NAME,
    JOB_CENTER_POOL_TYPE_GEVENT,
)

# this is a very bad solution!!! only temp workaround!
# we should improve our scheduler algorithm and not use one chain
# todo(guojian) remove this after we upgrade leader schedule follower algorithm
# ref: https://github.com/celery/celery/issues/1078
sys.setrecursionlimit(10000)

mongodb_broker = ";".join([f"mongodb://{x}/{JOB_CENTER_DB_NAME}" for x in utils.get_mongo_ips().split(",")])
if url_cache.mongo_url_prefix:
    mongodb_broker = "{mongo_prefix_url}{mongo_ips}/{db_name}?authSource=admin&replicaSet={replica_set}".format(
        mongo_prefix_url=url_cache.mongo_url_prefix,
        mongo_ips=utils.get_mongo_ips(),
        db_name=JOB_CENTER_DB_NAME,
        replica_set=REPSET_NAME,
    )

# load task modules when celery starts
app = Celery("job_center", broker=mongodb_broker)
app.config_from_object(job_center_config)

# allow root to run celery
# I think it's ok for us to run celery with superuser privileges
platforms.C_FORCE_ROOT = True

logger = logging.getLogger()


def setup_logger(log_file, log_level, max_bytes=0, backup_count=0):
    handler = logging.StreamHandler(sys.stdout)
    if log_file:
        from logging.handlers import RotatingFileHandler

        handler = RotatingFileHandler(log_file, maxBytes=max_bytes, backupCount=backup_count)

    handler.setFormatter(job_center_trace.LOGGING_FORMATTER)
    handler.setLevel(logging.getLevelName(log_level))

    logger.setLevel(logging.getLevelName(log_level))
    for hdlr in logger.handlers:
        logger.removeHandler(hdlr)
    logger.addHandler(handler)

    # setup trace filter
    trace_filter = job_center_trace.TraceFilter("job_center_trace")
    logger.addFilter(trace_filter)
    setattr(logger, job_center_trace.TRACE_FILTER_ATTR, trace_filter)


def load_job_center_plugin_config(celery_app):
    all_plugins = utils.find_plugin_modules()
    all_plugins.extend(BASE_MODULES)
    celery_app.conf.include = all_plugins
    # load scheduler config
    scheduler_plugin = utils.load_plugin_scheduler_config()
    if scheduler_plugin:
        job_center_config.beat_schedule.update(scheduler_plugin)

    # load config
    celery_app.config_from_object(job_center_config)


@click.group()
def job_center_cmd():
    pass


@job_center_cmd.command("worker", help="start job center worker")
@click.option("--log-file")
@click.option("--log-level", default="INFO")
@click.option("--pool-type", default=JOB_CENTER_POOL_TYPE_GEVENT)
@click.option("--pool-size", default=JOB_CENTER_CONCURRENCY, type=int)
def worker(log_file, log_level, pool_type, pool_size):
    argv = [
        "worker",
        "--pool=%s" % pool_type,
        "--loglevel=%s" % log_level,
        "--concurrency=%s" % pool_size,
        "--queues=%s,celery" % CURRENT_NODE_IP,
        "--hostname=%s" % CURRENT_NODE_IP,
        "--without-mingle",
        "--without-gossip",
    ]

    import grpc.experimental.gevent

    grpc.experimental.gevent.init_gevent()

    setup_logger(log_file, log_level, 200 * 2**20, 3)

    # patch libvirt
    if pool_type == JOB_CENTER_POOL_TYPE_GEVENT:
        from gevent.monkey import patch_subprocess

        from job_center.common import deploy_metadata

        patch_subprocess()

        if not deploy_metadata.is_scvm_deploy():
            global_utils.patch_libvirt()

    load_job_center_plugin_config(app)
    app.worker_main(argv)
    sys.exit(1)


@job_center_cmd.command("scheduler", help="start job center scheduler")
@click.option("--log-file")
@click.option("--log-level", default="INFO")
def scheduler(log_file, log_level):
    setup_logger(log_file, log_level, 30 * 2**20, 1)

    from job_center.scheduler_patches import (
        patch_beat_scheduler_merge_inplace,
        patch_celery_beat_scheduler_tick,
    )

    patch_celery_beat_scheduler_tick()
    patch_beat_scheduler_merge_inplace()

    persistent_file = pathlib.Path(constants.CELERYBEAT_PERSISTENCE_FILE_DEFAULT_PATH)
    backup_file = pathlib.Path(constants.CELERYBEAT_PERSISTENCE_BACKUP_FILE_DEFAULT_PATH)
    check_celery_beat_persistence_file(persistent_file, backup_file)

    load_job_center_plugin_config(app)
    beat_app = Beat(
        app=app, logfile="[stdout]", loglevel="INFO", socket_timeout=job_center_config.CELERY_BEAT_SOCKET_TIMEOUT
    )
    beat_app.run()  # s
    sys.exit(1)


def check_celery_beat_persistence_file(persistent_file: pathlib.Path, backup_file: pathlib.Path):
    # If we cannot create the file, we think we might be in a race with another create,
    # but it could be that the backup filename exists (that is, is left over from a previous crash)
    # so we try to remove it.
    if backup_file.is_file():
        backup_file.unlink()
        logging.info("%s backup file removed", backup_file.as_uri())

    if not persistent_file.is_file():
        return
    try:
        # Checking integrity of persistent file consults
        # setup_schedule() in site-packages/celery/beat.py in celery 3.1.19
        store = shelve.open(constants.CELERYBEAT_PERSISTENCE_FILE_DEFAULT_PATH, writeback=True)
        assert store["entries"]
    except Exception as e:
        logging.exception(
            "Checking celery beat persistent file raised exception: %s. Persistent file will be removed", str(e)
        )
        persistent_file.unlink()
        logging.info("%s file removed", persistent_file.as_uri())


if __name__ == "__main__":
    job_center_cmd()
