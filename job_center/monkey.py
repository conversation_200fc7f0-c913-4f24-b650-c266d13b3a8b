import logging

from pymongo import uri_parser


def patch_all():
    patch_kombu_url()
    patch_kombu_parse_uri()
    patch_kombu_emergency_dump_state()
    patch_kombu_transport_mongodb_open()


def monkey_kombu_url(url, mask="**"):
    logging.info("using monkey_kombu_url...")
    return url


def patch_kombu_url():
    from kombu.utils import url

    url.maybe_sanitize_url = monkey_kombu_url


def monkey_kombu_parse_uri(self, scheme="mongodb://"):
    logging.info("using monkey_kombu_parse_uri...")
    # See mongodb uri documentation:
    # https://docs.mongodb.org/manual/reference/connection-string/
    client = self.connection.client
    hostname = client.hostname

    if not hostname.startswith(scheme):
        hostname = scheme + hostname

    if not hostname[len(scheme) :]:
        hostname += self.default_hostname

    if client.userid and "@" not in hostname:
        head, tail = hostname.split("://")

        credentials = client.userid
        if client.password:
            credentials += ":" + client.password

        hostname = head + "://" + credentials + "@" + tail

    port = client.port if client.port else self.default_port

    parsed = uri_parser.parse_uri(hostname, port)

    dbname = parsed["database"] or client.virtual_host

    if dbname in ("/", None):
        dbname = self.default_database

    options = {
        "auto_start_request": True,
        "ssl": self.ssl,
        "connectTimeoutMS": (int(self.connect_timeout * 1000) if self.connect_timeout else None),
    }
    options.update(parsed["options"])
    options.update(client.transport_options)
    options = self._prepare_client_options(options)

    return hostname, dbname, options


def patch_kombu_parse_uri():
    from kombu.transport.mongodb import Channel

    Channel._parse_uri = monkey_kombu_parse_uri


def monkey_kombu_emergency_dump_state(state, open_file=open, dump=None, stderr=None):
    """Dump message state to stdout"""
    print(f"EMERGENCY DUMP STATE TO CONSOLE: {state}.", file=stderr)
    return ""


def patch_kombu_emergency_dump_state():
    from kombu.transport.virtual import base
    from kombu.utils import div

    base.emergency_dump_state = monkey_kombu_emergency_dump_state
    div.emergency_dump_state = monkey_kombu_emergency_dump_state
    logging.info("using patch_kombu_emergency_dump_state...")


def patch_kombu_transport_mongodb_open():
    def _open(self, scheme="mongodb://"):
        """Use the shared mongodb instance instead of making a new MongoClient instance at each invoke"""

        from common.mongo.db import mongodb

        hostname, dbname, conf = self._parse_uri(scheme=scheme)
        conf["host"] = hostname
        return mongodb[dbname]

    from kombu.transport.mongodb import Channel

    Channel._open = _open
