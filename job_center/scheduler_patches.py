# Copyright (c) SMARTX
# All rights reserved.

import logging


def patch_celery_beat_scheduler_tick():
    """
    There is a bug in 'celery.beat.Scheduler.tick' method when '0' is returned by
    'celery.beat.Scheduler.adjust'. This bug will cause job-center-scheduler hang
    in a time of celery.beat.DEFAULT_MAX_INTERVAL (300 seconds):
    https://github.com/celery/celery/issues/7290

    The bug already been fixed by this commit in celery 5.3.0 :
    https://github.com/celery/celery/commit/c556648ee1762730c962e818ff40c9aa71fdf2d4
    But there are lots of changes in 5.3.0, we can not upgrade celery to the new version so easy.

    This implementation of the 'tick' method is copy from the version of celery 5.2.7,
    and merges the changes to fix that bug.
    """

    from celery.beat import Scheduler, copy, event_t, heapq

    def is_numeric_value(value):
        return isinstance(value, (int, float)) and not isinstance(value, bool)  # noqa: UP038

    def tick(self, event_t=event_t, min=min, heappop=heapq.heappop, heappush=heapq.heappush):  # noqa: A002
        """Run a tick - one iteration of the scheduler.

        Executes one due task per call.

        Returns:
            float: preferred delay in seconds for next call.
        """
        adjust = self.adjust
        max_interval = self.max_interval

        if self._heap is None or not self.schedules_equal(self.old_schedulers, self.schedule):
            self.old_schedulers = copy.copy(self.schedule)
            self.populate_heap()

        H = self._heap

        if not H:
            return max_interval

        event = H[0]
        entry = event[2]
        is_due, next_time_to_run = self.is_due(entry)
        if is_due:
            verify = heappop(H)
            if verify is event:
                next_entry = self.reserve(entry)
                self.apply_entry(entry, producer=self.producer)
                heappush(H, event_t(self._when(next_entry, next_time_to_run), event[1], next_entry))
                return 0
            else:
                heappush(H, verify)
                return min(verify[0], max_interval)

        # original logic: return min(adjust(next_time_to_run) or max_interval, max_interval)
        adjusted_next_time_to_run = adjust(next_time_to_run)
        return min(
            adjusted_next_time_to_run if is_numeric_value(adjusted_next_time_to_run) else max_interval, max_interval
        )

    Scheduler.tick = tick
    logging.info("celery.beat.Scheduler.tick patched.")


def patch_beat_scheduler_merge_inplace():
    from datetime import timedelta

    from celery.beat import Scheduler

    origin_merge_func = Scheduler.merge_inplace

    def merge_inplace_replacement(scheduler, b):
        origin_merge_func(scheduler, b)

        # dict[string, SchedulerEntry]
        schedule = scheduler.schedule

        # Too many entries be triggered at one time will cause high mongodb CPU usage around
        # that time. In order to relieve this issue, after all schedule entries loaded and merged,
        # we adjust last_run_at of each schedule entry with a random timedelta.
        # We group the schedule with the schedule run_every(the job interval)
        schedule_group = {}
        for k in schedule:
            entry = schedule[k]
            schedule_group.setdefault(entry.schedule.run_every, []).append(entry)

        # To ensure the consistent extra delta to schedule job in every master node, sort the entry list by entry name.
        for key, entries in schedule_group.items():
            schedule_group[key] = sorted(entries, key=lambda entry: entry.name)

        for run_delta, entries in schedule_group.items():
            entry_num = len(entries)
            total_delay = min(run_delta, timedelta(minutes=5))
            avg_delay = total_delay.seconds / entry_num
            logging.info(
                "This schedule group(interval: %s) has %s tasks and with total delay %s average delay %s",
                run_delta.seconds,
                entry_num,
                total_delay.seconds,
                avg_delay,
            )
            for idx, entry in enumerate(entries):
                if entry.last_run_at + entry.schedule.run_every < entry.schedule.now():
                    # task already due, we add some initial delay based on last due time
                    logging.info("Task %s already due, add extra timedelta %s to task", entry.name, avg_delay * idx)
                    entry.last_run_at = (
                        entry.schedule.now() - entry.schedule.run_every + timedelta(seconds=avg_delay * idx)
                    )
                else:
                    # task not due, we add some initial delay based on origin last_run_at
                    logging.info("Task %s not due, add extra timedelta %s to task", entry.name, avg_delay * idx)
                    entry.last_run_at = entry.last_run_at + timedelta(seconds=avg_delay * idx)

    Scheduler.merge_inplace = merge_inplace_replacement

    logging.info("celery.beat.Scheduler.merge_inplace patched.")
