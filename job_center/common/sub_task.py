# Copyright (c) 2013-2016, SMARTX
# All rights reserved.
import logging

from common.lib.time_utils import utc_now_timestamp
from job_center.common import utils
from job_center.common.constants import DEFAULT_TASK_HANDLER, TASK_PENDING
from job_center.common.exceptions import JobFollowerError
from smartx_proto.errors import pyerror_pb2 as py_error


class JobFollowerSubmitError(JobFollowerError):
    pass


class JobSubTask:
    follower_map = None

    def __init__(
        self,
        uuid=None,
        follower_name=None,
        reference=None,
        queue=None,
        state=TASK_PENDING,
        msg=None,
        data=None,
        time_now=None,
        handler=DEFAULT_TASK_HANDLER,
        task_result=None,
    ):
        self.uuid = uuid or str(uuid.uuid4())
        self.follower_name = follower_name
        self.reference = reference
        self.queue = queue
        self.state = state
        self.msg = msg
        self.data = data
        self.time = time_now or int(utc_now_timestamp())
        self.handler = handler
        self.task_result = task_result or {}
        if not JobSubTask.follower_map:
            _, JobSubTask.follower_map, _ = utils.load_plugin_config()

    def __str__(self):
        return str(self.__dict__)

    def dumps(self):
        return self.__dict__

    def submit(self, job_id):
        func = self.follower_map.get(self.follower_name)
        if not func:
            raise JobFollowerSubmitError(
                "Got undefined task %s, please check leader import" % self.follower_name,
                py_error.JOB_WORKER_TASK_NOT_FOUND,
            )
        follower_task = func.s(job_id=job_id, task_uuid=self.uuid, reference=self.reference)
        if self.queue:
            follower_task.set(queue=self.queue)
        follower_task.set(task_id=self.uuid)
        follower_task.apply_async()
        logging.info("Submit task done: %s" % self.uuid)
