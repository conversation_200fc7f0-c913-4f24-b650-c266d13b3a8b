# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
import importlib
import inspect
import logging
import pkgutil
import sys
import time

from pymongo import InsertOne, errors

from common.config.constant import ZBS_CONFIG_FILE
from common.lib.cfg import Config
from common.lib.time_utils import utc_now_timestamp
from common.lib.utils.config_util import get_host_config_uuid
from common.mongo.db import mongodb
from job_center.common.constants import (
    JOB_DONE,
    JOB_FAILED,
    JOB_TYPE_ACTION,
    JOB_TYPE_SCHEDULE,
    WORKER_CONNECTED,
)


def get_mongo_ips():
    return ",".join(Config(ZBS_CONFIG_FILE).get_items("cluster", "mongo"))


def get_all_worker_ips():
    nodes = mongodb.job_center.worker.find({"ip": {"$nin": [None, ""]}}, {"_id": 0})
    return [node["ip"] for node in nodes]


def get_running_worker_ips_from_mongo():
    nodes = mongodb.job_center.worker.find({"state": WORKER_CONNECTED, "ip": {"$nin": [None, ""]}}, {"_id": 0})
    return [node["ip"] for node in nodes]


def get_running_worker_hash():
    nodes = mongodb.job_center.worker.find({"state": WORKER_CONNECTED}, {"_id": 0})
    return [node["latest_hash"] for node in nodes]


def worker_init(hostname, ip, vm_ip, host_uuid=get_host_config_uuid()):
    from job_center.main import app

    latest_hash = app.oid
    mongodb.job_center.worker.update(
        {"ip": ip},
        {
            "$set": {
                "hostname": hostname,
                "ip": ip,
                "state": WORKER_CONNECTED,
                "mtime": int(time.time()),
                "vm_ip": vm_ip,
                "latest_hash": latest_hash,
                "host_uuid": host_uuid,
            }
        },
        upsert=True,
    )


def worker_update(ip, state):
    mongodb.job_center.worker.update({"ip": ip}, {"$set": {"state": state, "mtime": int(time.time())}})


def worker_find():
    return mongodb.job_center.worker.find({"ip": {"$nin": [None, ""]}}, {"_id": 0})


def try_set_cron_job(now, event):
    from job_center.config import CURRENT_NODE_IP

    result = mongodb.jobs.cron_job.update(
        {"time": now, "event": event},
        {"$set": {"time": now, "event": event}, "$setOnInsert": {"host": CURRENT_NODE_IP}},
        upsert=True,
    )
    return True if result.get("upserted") else False


def batch_query_job(job_ids, show_keys: tuple | None = None):
    if not job_ids:
        return []

    _show_keys = {"_id": 0}
    if show_keys:
        for show_key in show_keys:
            _show_keys[show_key] = 1

    return list(mongodb.jobs.job.find({"job_id": {"$in": job_ids}, "type": JOB_TYPE_ACTION}, _show_keys))


def onerror(name):
    exception_type, value, _ = sys.exc_info()
    logging.exception(f"Failed to import module {name}\n" f"exception_type: {exception_type}, exception_value: {value}")


def find_all_plugin_modules() -> list[str]:
    from job_center.common import deploy_metadata
    from smartx_app import network
    import tuna

    packages = list(pkgutil.walk_packages(tuna.__path__, tuna.__name__ + ".", onerror=onerror))
    packages.extend(list(pkgutil.walk_packages(network.__path__, network.__name__ + ".", onerror=onerror)))

    if not deploy_metadata.is_scvm_deploy():
        from smartx_app import elf, vmtools

        packages.extend(
            list(pkgutil.walk_packages(elf.__path__, elf.__name__ + ".", onerror=onerror)),
        )
        packages.extend(
            list(pkgutil.walk_packages(vmtools.__path__, vmtools.__name__ + ".", onerror=onerror)),
        )

    return [result[1] for result in packages if "tests" not in result[1]]


def find_plugin_modules():
    return [
        path
        for path in find_all_plugin_modules()
        if "tests" not in path
        and "config" not in path
        and "rest" not in path
        and "job_center" in path
        and ("follower" in path or "scheduler" in path or "hook" in path)
    ]


task_module_name_min_depth = 3


def find_plugin_config():
    module_names = find_all_plugin_modules()
    config_modules = []
    for name in module_names:
        paths = name.split(".")
        if len(paths) >= task_module_name_min_depth and paths[-1] == "config" and paths[-2] == "job_center":
            config_modules.append(name)
    return config_modules


def verify_plugin_config(path, config_module):
    for name in ["splitter_set", "follower_map", "dependencies"]:
        if not hasattr(config_module, name) or not isinstance(getattr(config_module, name), dict):
            raise AttributeError(f"Plugin {path} do not have attributes {name}")


def load_plugin_config():
    from job_center.handler.follower.map import follower_map

    splitter_set = {}
    dependencies = {}
    for path in find_plugin_config():
        try:
            plugin_config = importlib.import_module(path)
            verify_plugin_config(path, plugin_config)
            splitter_set.update(plugin_config.splitter_set)
            follower_map.update(plugin_config.follower_map)
            dependencies.update(plugin_config.dependencies)
        except ImportError:
            logging.warning("Can not import plugin: %s" % path)
        except AttributeError:
            logging.warning("Plugin config invalid: %s" % path)
    return splitter_set, follower_map, dependencies


def load_plugin_scheduler_config():
    scheduler_config = {}
    for path in find_plugin_config():
        try:
            plugin_config = importlib.import_module(path)
            if hasattr(plugin_config, "get_scheduler_beat") and inspect.isfunction(plugin_config.get_scheduler_beat):
                scheduler_config.update(plugin_config.get_scheduler_beat())
        except ImportError:
            logging.warning("Can not import plugin: %s" % path)
    return scheduler_config


def lock_resources(resources, job_id):
    return bulk_lock_resources(resources, job_id)


def unlock_resources(resources, job_id=None):
    return bulk_unlock_resources(resources, job_id)


def check_resources_lock(resources, job_id):
    return bulk_check_resources_lock(resources, job_id)


def set_resource_lock(resource_uuid, job_id):
    result = mongodb.resources.lock.update(
        {"resource_uuid": resource_uuid},
        {"$set": {"resource_uuid": resource_uuid, "mtime": utc_now_timestamp()}, "$setOnInsert": {"job_id": job_id}},
        upsert=True,
    )
    return True if result.get("upserted") else False


def remove_resource_lock(resource_uuid, job_id=None):
    remove_query = {"resource_uuid": resource_uuid}
    if job_id:
        remove_query["job_id"] = job_id
    mongodb.resources.lock.remove(remove_query)
    return True


def check_resource_lock(resource_uuid, job_id=None):
    check_query = {
        "resource_uuid": resource_uuid,
    }
    if job_id:
        check_query["job_id"] = job_id
    result = mongodb.resources.lock.find_one(check_query)
    return True if result else False


def get_resource_lock(resource_uuid, job_id=None):
    check_query = {
        "resource_uuid": resource_uuid,
    }
    if job_id:
        check_query["job_id"] = job_id
    return mongodb.resources.lock.find_one(check_query)


def filter_locked_resources(resource_uuid_list, job_id=None):
    check_query = {
        "resource_uuid": {"$in": resource_uuid_list},
    }
    if job_id is not None:
        check_query["job_id"] = job_id

    cursor = mongodb.resources.lock.find(check_query, {"resource_uuid": 1})
    return [r["resource_uuid"] for r in cursor]


def clean_jc_schedule_jobs(time_to_delete=None, job_collection=None, cron_job_collection=None):
    time_to_delete = time_to_delete or int(time.time()) - 3600
    job_collection = job_collection or mongodb.jobs.job
    cron_job_collection = cron_job_collection or mongodb.jobs.cron_job

    logging.info("Start cleaning schedule jobs.")

    ret = job_collection.delete_many(
        {"ctime": {"$lt": time_to_delete}, "state": {"$in": [JOB_DONE, JOB_FAILED]}, "type": JOB_TYPE_SCHEDULE}
    )
    logging.info(f"Remove {ret.deleted_count} of schedule jobs done.")

    ret = cron_job_collection.delete_many({"time": {"$lt": time_to_delete}})
    logging.info(f"Remove {ret.deleted_count} of cron records done.")


def clean_jc_action_job(number_keep=1000, job_collection=None):
    job_collection = job_collection or mongodb.jobs.job
    deleted_count, states_to_delete = 0, [JOB_DONE, JOB_FAILED]

    logging.info("Start cleaning action jobs.")

    result = list(
        job_collection.find({"type": JOB_TYPE_ACTION, "state": {"$in": states_to_delete}}, {"_id": 0, "ctime": 1})
        .sort("ctime", -1)
        .limit(1)
        .skip(number_keep)
    )

    if result:
        deleted_count = job_collection.delete_many(
            {"type": JOB_TYPE_ACTION, "state": {"$in": states_to_delete}, "ctime": {"$lte": result[0]["ctime"]}}
        ).deleted_count

    logging.info(f"Remove {deleted_count} of action jobs done.")


def sync_ha_heartbeat_info():
    """
    Clear dirty host heartbeat info in case of data ip conflict
    with local host. Occurs when a host remove and add into cluster with
    a same data ip but different host uuids.
    """
    from job_center.config import CURRENT_NODE_HOST_UUID, CURRENT_NODE_IP

    if not CURRENT_NODE_HOST_UUID or not CURRENT_NODE_IP:
        raise ValueError("Local host uuid or data ip is not set")

    query = {"host_uuid": {"$ne": CURRENT_NODE_HOST_UUID}, "data_ip": CURRENT_NODE_IP}
    # query for logging
    query_res = mongodb.job_center.heartbeat.find(
        query,
        {"_id": 0, "host_uuid": 1},
    )

    dirty_host_heartbeat_infos = list(query_res)

    if len(dirty_host_heartbeat_infos) > 0:
        logging.info("Clear dirty host heartbeat info: {}".format(dirty_host_heartbeat_infos))
        mongodb.job_center.heartbeat.delete_many(query)


def bulk_lock_resources(resources, job_id) -> bool:
    """Bulk lock the specified resources of the job.
    Note:
        1. It will success if the resources are already locked by the same job.
        2. It will fail if JC lock the resources for the same job in parallel.
        3. It will fail if the resources are locked by other jobs.

    :return bool        True if all resources are successfully locked,
                        otherwise return False and does not lock any resources.
    """
    if not resources:
        return True

    lock_table = mongodb.resources.lock
    insert_time = int(time.time())

    with mongodb.start_session() as session:
        with session.start_transaction():
            res_uuids = [x for x in resources]
            locked_resources = list(lock_table.find({"resource_uuid": {"$in": res_uuids}}, session=session))
            ops = list[InsertOne]()
            for res_uuid in resources:
                locked_record = _find_locked_resources(locked_resources, res_uuid)
                if locked_record:
                    if locked_record["job_id"] != job_id:
                        logging.warning(f"Resource [{res_uuid}] is locked by another job [{locked_record['job_id']}]")
                        return False
                    else:
                        logging.info(f"Resource [{res_uuid}] is already locked by job [{job_id}]")
                        continue
                ops.append(InsertOne({"job_id": job_id, "resource_uuid": res_uuid, "mtime": insert_time}))

            if len(ops) == 0:
                logging.info(f"All resources are already locked by job [{job_id}]")
                return True

            try:
                res = lock_table.bulk_write(ops, ordered=False, session=session)
            except errors.PyMongoError as e:
                logging.error(f"Failed to lock job [{job_id}] resources: {e!s}")
                bulk_unlock_resources(resources, job_id)
                return False
            else:
                # It cannot be assumed that each time is a complete insert or update,
                # and partial insert or update need to be considered during reentrancy.
                if res.inserted_count != len(ops):
                    logging.error(f"Failed to lock job [{job_id}] resources: {res.bulk_api_result}")
                    bulk_unlock_resources(resources, job_id)
                    return False

    return True


def _find_locked_resources(resources, res_uuid: str):
    for res in resources:
        if res["resource_uuid"] == res_uuid:
            return res

    return None


def bulk_unlock_resources(resources, job_id=None):
    """Bulk unlock the specified resources of the job.
    :return int     The unlock count.
    """
    if not resources:
        return 0

    if job_id:

        def _q(x):
            return {"job_id": job_id, "resource_uuid": x}

    else:

        def _q(x):
            return {"resource_uuid": x}

    bulk = mongodb.resources.lock.initialize_unordered_bulk_op()
    for res_uuid in resources:
        bulk.find(_q(res_uuid)).remove()

    bulk_write_result = bulk.execute()
    return bulk_write_result.get("nRemoved", 0)


def bulk_check_resources_lock(resources, job_id):
    """Bulk check that the all specified resources of the job are locked.
    :return bool    True if the all resources are locked, otherwise False.
    """
    if not resources:
        return True

    cursor = mongodb.resources.lock.find({"job_id": job_id}, {"_id": 0, "resource_uuid": 1})
    resources_locked = {x["resource_uuid"] for x in cursor}

    for res_uuid in resources:
        if res_uuid not in resources_locked:
            return False

    return True
