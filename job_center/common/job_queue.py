# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
from pymongo import ReturnDocument

from common.lib.time_utils import utc_now_timestamp
from common.mongo.db import mongodb


class JobQueue:
    db = mongodb.jobs.queue

    @classmethod
    def push_job_queue(cls, key, job_id):
        """
        queue json
        {
            "queue_key": "CREATE_VM",
            "queue": ['xxxxxxxx', 'yyyyyyyy'],
            "mtime": 1234567890,
        }

        :param queue_key: job queue key
        :param job_id: id of job
        """
        time_now = utc_now_timestamp()
        return cls.db.find_one_and_update(
            {"key": key},
            {"$push": {"queue": job_id}, "$set": {"mtime": time_now}},
            upsert=True,
            return_document=ReturnDocument.AFTER,
        )

    @classmethod
    def pull_job_queue(cls, key, job_id):
        time_now = utc_now_timestamp()
        return cls.db.find_one_and_update(
            {"key": key},
            {"$pull": {"queue": job_id}, "$set": {"mtime": time_now}},
            return_document=ReturnDocument.AFTER,
        )

    @classmethod
    def is_head_of_queue(cls, queue, job_id):
        """Determine whether this job is on head of queue

        :param queue: the queue json in mongodb
        :param job_id: the ID of a job
        :return: True if queue exist and at head or no queue
        """
        result = True
        if queue and queue["queue"] and job_id != queue["queue"][0]:
            result = False
        return result

    @classmethod
    def get_queue(cls, key):
        queue = cls.db.find_one({"key": key})
        return queue

    @classmethod
    def get_head_of_queue(cls, key):
        queue = cls.db.find_one({"key": key})
        result = None
        if queue and queue["queue"]:
            result = queue["queue"][0]
        return result

    @classmethod
    def in_queue(cls, key, job_id):
        queue = cls.db.find_one({"key": key, "queue": {"$in": [job_id]}})
        if queue:
            return True
        else:
            return False

    @classmethod
    def is_empty_queue(cls, key):
        queue = cls.db.find_one({"key": key})
        if queue and queue["queue"]:
            return False
        else:
            return True
