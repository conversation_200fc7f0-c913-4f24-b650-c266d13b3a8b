# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
import functools
import logging
import time

from common.mongo.db import mongodb
from job_center import config

TABLE = "init_job_mark"
_collection = mongodb[config.JOB_DB_NAME][TABLE]


class DBMarkNode:
    def __init__(self, host_uuid, ctime=None, mtime=None, payload=None):
        t = int(time.time())
        self.host_uuid = host_uuid
        self.ctime = ctime or t
        self.mtime = mtime or t
        self.payload = payload or {}

    def dumps(self):
        return self.__dict__


# TODO(jingming): Support modify mark records multiple times and merge them into the DBMarkJob framework.


def mark(name, current_node_id=config.CURRENT_NODE_HOST_UUID):
    _collection.update_one({"name": name}, {"$push": {"nodes": DBMarkNode(current_node_id).dumps()}}, upsert=True)


def check_mark(name, current_node_id=config.CURRENT_NODE_HOST_UUID):
    return bool(_collection.find_one({"name": name, "nodes.host_uuid": current_node_id}))


def call_once_per_node(mark_name):
    """
    Add logging to a function. level is the logging
    level, name is the logger name, and message is the
    log message. If name and message aren't specified,
    they default to the function's module and name.
    """

    def decorate(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            result = None
            if not check_mark(mark_name):
                try:
                    result = func(*args, **kwargs)
                except Exception:
                    logging.exception(f"[Init Job] Error on call {func.__name__}.")
                else:
                    mark(mark_name)
                    logging.info(f"[Init Job] Execute init job {func.__name__} successfully.")
            else:
                logging.info(
                    "[Init Job] The init job {} with mark name {} has completed, skip it.".format(
                        func.__name__, mark_name
                    )
                )
            return result

        return wrapper

    return decorate


def call_once_per_cluster(mark_name):
    """
    Add logging to a function. level is the logging
    level, name is the logger name, and message is the
    log message. If name and message aren't specified,
    they default to the function's module and name.
    """

    def decorate(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            result = None
            if not check_mark(mark_name, current_node_id="random_node_in_cluster"):
                logging.info(f"[Init Job] Start executing init job {func.__name__}.")
                # noinspection PyBroadException
                try:
                    result = func(*args, **kwargs)
                except Exception:
                    logging.exception(f"[Init Job] Error on call {func.__name__}.")
                else:
                    mark(mark_name, current_node_id="random_node_in_cluster")
                    logging.info(f"[Init Job] Execute init job {func.__name__} successfully.")
            else:
                logging.info(
                    "[Init Job] The init job {} with mark name {} has completed, skip it.".format(
                        func.__name__, mark_name
                    )
                )
            return result

        return wrapper

    return decorate
