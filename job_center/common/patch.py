# Copyright (c) 2013-2022, SMARTX
# All rights reserved.
import functools
import logging
import sys


class PatchWorkerInit:
    """
    The worker_init.connect will catch all exception in celery-4.3.0(ARM).
    This behavior is different from the celery-3.1.19(x86),
    so we explicitly call exit(1) on exception to interrupt JC worker startup.
    """

    def __init__(self):
        from celery import signals

        self._signal = signals.worker_init

    def connect(self, func):
        @functools.wraps(func)
        @self._signal.connect
        def wrapped(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception:
                logging.exception(f"call {func.__name__} failed: ")
                sys.exit(1)

        return wrapped


worker_init = PatchWorkerInit()
