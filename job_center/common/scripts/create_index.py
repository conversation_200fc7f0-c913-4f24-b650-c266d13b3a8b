import logging

import pymongo
from pymongo import ASCENDING, DESCENDING, HASHED, errors

from common.mongo.db import mongodb


def try_create_job_center_worker_index():
    mongodb.job_center.worker.create_index([("ip", 1)], unique=True, background=True)


def try_create_job_center_heartbeat_index():
    mongodb.job_center.heartbeat.create_index([("host_uuid", pymongo.DESCENDING)], background=True)


def try_create_resources_lock_index():
    try:
        mongodb.resources.lock.create_index([("resource_uuid", pymongo.DESCENDING)], background=True, unique=True)
        return True
    except errors.DuplicateKeyError as e:
        # For the upgraded cluster, theoretically there is a situation
        # where the `resource_uuid` is not unique. Currently, the safe way
        # to create a unique index is to wait for the related job to finish.
        logging.warning(
            "[try_create_resources_lock_index] Failed to create the index `lock.resource_uuid_-1`, error: %s" % str(e)
        )
        return False


def try_create_jobs_index():
    try:
        job_indexes = mongodb.jobs.job.index_information()
        cron_job_index = mongodb.jobs.cron_job.index_information()
    except errors.OperationFailure:
        # In case mongo with a higher version returns errors when
        # collections don't exist
        job_indexes = {}
        cron_job_index = {}

    if "job_id_-1" in job_indexes and not job_indexes["job_id_-1"].get("unique"):
        mongodb.jobs.job.drop_index("job_id_-1")
        job_indexes.pop("job_id_-1")

    if "job_id_-1" not in job_indexes:
        try:
            mongodb.jobs.job.create_index([("job_id", DESCENDING)], background=True, unique=True)
        except errors.DuplicateKeyError:
            mongodb.jobs.job.create_index([("job_id", DESCENDING)], background=True)
    if "ctime_-1_type_1" not in job_indexes:
        mongodb.jobs.job.create_index([("ctime", DESCENDING)], background=True)
    if "type_hash" not in job_indexes:
        mongodb.jobs.job.create_index([("type", HASHED)], background=True)
    if "state_hash" not in job_indexes:
        mongodb.jobs.job.create_index(
            [("state", HASHED)],
            background=True,
        )
    if "state_1_type_1_ctime_-1" not in job_indexes:
        mongodb.jobs.job.create_index(
            [("state", ASCENDING), ("type", ASCENDING), ("ctime", DESCENDING)],
            background=True,
        )
    if "time_-1_event_-1" not in cron_job_index:
        mongodb.jobs.cron_job.create_index([("time", DESCENDING), ("event", DESCENDING)], background=True, unique=True)

    mongodb.jobs.job.create_index([("job_id", DESCENDING), ("task_list.uuid", DESCENDING)])
    mongodb.job_center.messages.routing.create_index([("exchange", HASHED)], background=True)

    mongodb.jobs.queue.create_index([("key", ASCENDING)], background=True)
