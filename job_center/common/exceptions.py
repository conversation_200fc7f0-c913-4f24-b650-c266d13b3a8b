# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
from smartx_proto.errors import pyerror_pb2 as py_error
from zbs.lib.zexception import ZException


class ZJobError(ZException):
    def __init__(self, msg="", user_code=None):
        self.user_code = user_code if user_code else py_error.JOB_UNKNOWN_ERROR
        self.message = msg
        self.uc_name = py_error.ErrorCode.Name(user_code) if user_code else None


class JobLeaderError(ZJobError):
    pass


class JobFollowerError(ZJobError):
    pass


class JobFollowerTaskRetry(ZJobError):
    def __init__(self, msg="", user_code=None, timeout=10):
        super().__init__(msg, user_code)
        self.timeout = timeout
