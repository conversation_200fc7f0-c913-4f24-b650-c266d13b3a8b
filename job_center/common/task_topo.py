# Copyright (c) 2013-2016, SMARTX
# All rights reserved.
from collections import defaultdict
import logging
import time

from pymongo import ReturnDocument

from common.lib.time_utils import utc_now_timestamp
from common.lib.utils.pydag import DAG
from common.mongo.db import mongodb
from job_center.common import utils
from job_center.common.constants import (
    DEFAULT_TASK_HANDLER,
    JOB_TYPE_ACTION,
    MAX_JOB_RESOURCES,
    MAX_JOB_SUB_TASKS,
    TASK_DONE,
    TASK_FAILED,
    TASK_PENDING,
    TASK_RUNNING,
)
from job_center.common.exceptions import JobLeaderError
from job_center.common.sub_task import JobSubTask
from job_center.common.utils import get_running_worker_hash, remove_resource_lock
from job_center.handler.leader.splitter import SchedulerAndOneTimeActionSplitter
from job_center.main import app
from smartx_app.common.resource_type import (
    KVM_VM,
    KVM_VM_SNAPSHOT,
    KVM_VM_TEMPLATE,
    KVM_VOL,
    KVM_VOL_ISCSI,
    KVM_VOL_ISCSI_SNAPSHOT,
    KVM_VOL_ISCSI_TEMPLATE,
    KVM_VOL_SNAPSHOT,
    KVM_VOL_TEMPLATE,
)
from smartx_proto.errors import pyerror_pb2 as py_error


def _remove_broker_message(task_uuid, queue=None):
    remove_query = {"payload": {"$regex": '"correlation_id": "%s"' % task_uuid}}
    if queue:
        remove_query["queue"] = queue
    mongodb.job_center.messages.remove(remove_query)


def _mongo_job_update(query, to_be_update, projection=None):
    if not projection:
        projection = {"_id": 1}
    return mongodb.jobs.job.find_one_and_update(
        query, to_be_update, return_document=ReturnDocument.AFTER, projection=projection
    )


class TaskTopo:
    dependencies = {}
    splitter_set = {}

    def __init__(self, job):
        if not TaskTopo.dependencies and not TaskTopo.splitter_set:
            TaskTopo.splitter_set, _, TaskTopo.dependencies = utils.load_plugin_config()
        self._initialize(job)

    def _initialize(self, job):
        self.job = job
        self.ops_map = {}
        self.tasks_dag = DAG()
        self.tasks_dict_map = {}
        self.tasks_indexes = {}
        self.task_ref_resource_group_map = {}
        self.task_type_map = defaultdict(dict)

        if job.get("resources") and len(job["resources"]) > MAX_JOB_RESOURCES:
            logging.warning(
                "len of resources({}) in job {} exceed {}".format(
                    len(job["resources"]), job["job_id"], MAX_JOB_RESOURCES
                )
            )
            raise JobLeaderError(
                f"Too many resources, max support number is {MAX_JOB_RESOURCES}.",
                py_error.JOB_RESOURCES_EXCESS,
            )

        if not self.job.get("task_list"):
            self.job["task_list"] = []
            tasks = self._split_task()
            if len(tasks) > MAX_JOB_SUB_TASKS:
                logging.warning(
                    "len of sub tasks({}) in job {} exceed {}".format(len(tasks), job["job_id"], MAX_JOB_SUB_TASKS)
                )
                raise JobLeaderError("Too many sub tasks", py_error.JOB_SUB_TASKS_EXCESS)
            for i, task in enumerate(tasks):
                self.ops_map[task.uuid] = task.follower_name
                self.tasks_dict_map[task.uuid] = task.dumps()
                self.job["task_list"].append(task.dumps())
                self.tasks_indexes[task.uuid] = i

            if tasks:
                self._handle_resource_group(tasks)
                self._generate_task_group_map()
                self._generate_topo(tasks)
                self.job["task_topo"] = self.raw_topo()
            else:
                self.job["task_topo"] = {}
                self.job["resource_group"] = {}
        else:
            self.tasks_dag.from_dict_no_validate(self.job["task_topo"])
            for i, task_dict in enumerate(self.job["task_list"]):
                self.tasks_dict_map[task_dict["uuid"]] = task_dict
                self.tasks_indexes[task_dict["uuid"]] = i

        for task in list(self.tasks_dict_map.values()):
            if task["state"] == TASK_DONE:
                self.tasks_dag.delete_node(task["uuid"])

    def _handle_resource_group(self, tasks):
        groups = self.job.get("resource_group")
        all_resource_ids = set(list(self.job["resources"].keys()) if self.job["resources"] else [])
        if groups:
            resource_ids_in_group = set()
            for resource_ids in list(groups.values()):
                resource_ids_in_group |= set(resource_ids)
            rest_resource_ids = all_resource_ids - resource_ids_in_group
            self.job["resource_group"]["default"] = list(rest_resource_ids)
        elif self.job["resources"] and self.job["description"] in [
            "VM_START",  # DO NOT import code from elf
            "VM_STOP",
            "VM_STOP_F",
            "VM_CREATE",
            "VM_PAUSE",
            "VM_RESUME",
            "VM_REBOOT",
            "VM_REBOOT_F",
            "VM_DELETE",
        ]:
            # split same vm volume in to one resource group
            # do not consider sharing volume right since sharing volume
            # can not delete with vm
            self._handle_elf_vm_operation_resource_group(tasks)
        elif self.job["resources"] and self.job["description"] in ["VM_SNAPSHOT_BULK_DELETE", "VM_SNAPSHOT_DELETE"]:
            self._handle_elf_vm_snapshot_operation_resource_group(tasks)
        elif self.job["resources"] and self.job["description"] == "VM_TEMPLATE_DELETE":
            self._handle_elf_vm_template_operation_resource_group(tasks)
        else:
            self.job["resource_group"] = {"default": list({task.reference for task in tasks if task.reference})}

        for group_id in self.job["resource_group"]:
            self.task_ref_resource_group_map.update({ref: group_id for ref in self.job["resource_group"][group_id]})

    def _handle_elf_vm_operation_resource_group(self, tasks):
        tmp_vm_paths = {}
        self.job["resource_group"] = {}
        for task in tasks:
            if task.reference and self.job["resources"][task.reference]["type"] == KVM_VM:
                self.job["resource_group"][task.reference] = [task.reference]
                tmp_vm_paths[task.reference] = [
                    device["path"] for device in self.job["resources"][task.reference]["disks"] if device["path"]
                ]
        self.job["resource_group"]["default"] = []
        for task in tasks:
            if task.reference and self.job["resources"][task.reference]["type"] == KVM_VM:
                continue
            if task.reference and self.job["resources"][task.reference]["type"] in [KVM_VOL, KVM_VOL_ISCSI]:
                check_path = self.job["resources"][task.reference]["path"]
                if not check_path:
                    check_path = "@{%s/path}" % task.reference
                for vm_uuid in tmp_vm_paths:
                    if check_path in tmp_vm_paths[vm_uuid]:
                        self.job["resource_group"][vm_uuid].append(task.reference)
                        break
                else:
                    self.job["resource_group"]["default"].append(task.reference)
            elif task.reference:
                self.job["resource_group"]["default"].append(task.reference)

    def _handle_elf_vm_snapshot_operation_resource_group(self, tasks):
        tmp_vol_snapshot_uuids = {}
        self.job["resource_group"] = {}
        for task in tasks:
            if task.reference and self.job["resources"][task.reference]["type"] == KVM_VM_SNAPSHOT:
                self.job["resource_group"][task.reference] = [task.reference]
                tmp_vol_snapshot_uuids[task.reference] = [
                    device["snapshot_uuid"]
                    for device in self.job["resources"][task.reference]["disks"]
                    if device.get("snapshot_uuid")
                ]
        self.job["resource_group"]["default"] = []
        for task in tasks:
            if task.reference and self.job["resources"][task.reference]["type"] == KVM_VM_SNAPSHOT:
                continue
            if task.reference and self.job["resources"][task.reference]["type"] in [
                KVM_VOL_SNAPSHOT,
                KVM_VOL_ISCSI_SNAPSHOT,
            ]:
                snapshot_uuid = self.job["resources"][task.reference]["uuid"]
                for vm_snap_uuid in tmp_vol_snapshot_uuids:
                    if snapshot_uuid in tmp_vol_snapshot_uuids[vm_snap_uuid]:
                        self.job["resource_group"][vm_snap_uuid].append(task.reference)
                        break
                else:
                    self.job["resource_group"]["default"].append(task.reference)
            elif task.reference:
                self.job["resource_group"]["default"].append(task.reference)

    def _handle_elf_vm_template_operation_resource_group(self, tasks):
        tmp_vol_template_uuids = {}
        self.job["resource_group"] = {}
        for task in tasks:
            if task.reference and self.job["resources"][task.reference]["type"] == KVM_VM_TEMPLATE:
                self.job["resource_group"][task.reference] = [task.reference]
                tmp_vol_template_uuids[task.reference] = [
                    device["volume_template_uuid"]
                    for device in self.job["resources"][task.reference]["disks"]
                    if device.get("volume_template_uuid")
                ]
        self.job["resource_group"]["default"] = []
        for task in tasks:
            if task.reference and self.job["resources"][task.reference]["type"] == KVM_VM_TEMPLATE:
                continue
            if task.reference and self.job["resources"][task.reference]["type"] in [
                KVM_VOL_TEMPLATE,
                KVM_VOL_ISCSI_TEMPLATE,
            ]:
                tempalte_uuid = self.job["resources"][task.reference]["uuid"]
                for vm_snap_uuid in tmp_vol_template_uuids:
                    if tempalte_uuid in tmp_vol_template_uuids[vm_snap_uuid]:
                        self.job["resource_group"][vm_snap_uuid].append(task.reference)
                        break
                else:
                    self.job["resource_group"]["default"].append(task.reference)
            elif task.reference:
                self.job["resource_group"]["default"].append(task.reference)

    def _dependency_check(self, left_task_uuid, right_task_uuid):
        groups = self.job.get("resource_group")
        result = False
        if groups:
            left_reference = self.tasks_dict_map[left_task_uuid]["reference"]
            right_reference = self.tasks_dict_map[right_task_uuid]["reference"]
            if left_reference and left_reference in groups["default"] and not right_reference:
                result = True
            elif right_reference and right_reference in groups["default"] and not left_reference:
                result = True
            elif not left_reference and not right_reference:
                result = True
            elif left_reference and right_reference:
                in_same_groups = {
                    key for key in groups if left_reference in groups[key] and right_reference in groups[key]
                }
                if in_same_groups:
                    result = True
        else:
            result = True

        return result

    def _generate_topo(self, tasks):
        for task in tasks:
            self.tasks_dag.add_node(task.uuid)

        for current_task_uuid in self.tasks_dict_map:
            task_dependency = (
                self.dependencies[self.ops_map[current_task_uuid]]
                if self.ops_map[current_task_uuid] in self.dependencies
                else []
            )
            if not task_dependency:
                continue

            group_id = self.task_ref_resource_group_map[self.tasks_dict_map[current_task_uuid]["reference"]]
            # dependencies = self._get_dependencies(
            #     self.ops_map[current_task_uuid],
            #     group_id
            # )
            for dep in task_dependency:
                depend_uuids = self._get_dependencies(dep, group_id)
                for depend_uuid in depend_uuids:
                    self.tasks_dag.add_edge_no_validate(depend_uuid, current_task_uuid)

            # for other_task_uuid in self.tasks_dict_map:
            #     if (self.ops_map[other_task_uuid] in task_dependency
            #             and self._dependency_check(
            #                 current_task_uuid, other_task_uuid
            #             )):
            #         self.tasks_dag.add_edge_no_validate(
            #             other_task_uuid,
            #             current_task_uuid
            #         )
        result, reason = self.tasks_dag.validate(self.tasks_dag.graph)
        if not result:
            logging.warning("Cycle detected")
            raise JobLeaderError("Cycle detected", py_error.JOB_CYCLE_DETECTED)

    def _generate_task_group_map(self):
        for task in list(self.tasks_dict_map.values()):
            task_type = self.ops_map[task["uuid"]]
            group_id = self.task_ref_resource_group_map[task["reference"]]
            if not self.task_type_map[task_type].get(group_id):
                self.task_type_map[task_type][group_id] = [task["uuid"]]
            else:
                self.task_type_map[task_type][group_id].append(task["uuid"])

    def _get_dependencies(self, task_type, resource_group_id):
        return (
            self.task_type_map[task_type][resource_group_id]
            if resource_group_id in self.task_type_map[task_type]
            else []
        )

    def _split_task(self):
        task_list = []
        if not self.job.get("task_list"):
            # generate task list
            try:
                if self.job["type"] == JOB_TYPE_ACTION and self.job["resources"]:
                    for resource in list(self.job["resources"].values()):
                        if resource["type"] not in self.splitter_set:
                            logging.warning("Splitter not found for type: %s" % resource["type"])
                        else:
                            task_list.extend(self.splitter_set[resource["type"]].split(resource))
                elif self.job["type"] == JOB_TYPE_ACTION and self.job["one_time_task"]:
                    for reference, data in list(self.job["one_time_task"].items()):
                        task_list.extend(SchedulerAndOneTimeActionSplitter().split(data, reference))
                else:
                    task_list.extend(SchedulerAndOneTimeActionSplitter().split(self.job["schedule_task"]))
            except JobLeaderError as e:
                logging.warning("Split tasks failed: %s" % str(e))
                raise
        else:
            task_list = [
                JobSubTask(
                    uuid=task["uuid"],
                    follower_name=task["follower_name"],
                    reference=task["reference"],
                    queue=task["queue"],
                    state=task["state"],
                    data=task.get("data"),
                    msg=task.get("msg"),
                    time_now=task.get("time"),
                    handler=task.get("handler"),
                )
                for task in self.job.get("task_list")
            ]
        return task_list

    def _update_local_job(self, task_topo, task_update, task_list_state=None):
        for latest_task in task_list_state or []:
            self.tasks_dict_map[latest_task["uuid"]]["state"] = latest_task["state"]
        for i, task in enumerate(self.job["task_list"]):
            if task["uuid"] == task_update["uuid"]:
                self.job["task_list"][i] = task_update
            elif task_list_state:
                self.job["task_list"][i]["state"] = self.tasks_dict_map[task["uuid"]]["state"]
        self.job["task_topo"] = task_topo

    def reset(self, job):
        self._initialize(job)

    def save(self):
        return _mongo_job_update(
            {"job_id": self.job["job_id"], "task_list": {"$size": 0}},
            {
                "$set": {
                    "task_list": self.job["task_list"],
                    "task_topo": self.job["task_topo"],
                    "resource_group": self.job.get("resource_group"),
                }
            },
            {"_id": 1},
        )

    def raw_topo(self):
        return {task_uuid: list(self.tasks_dag.graph[task_uuid]) for task_uuid in self.tasks_dag.graph}

    def _get_task(self, task_uuid):
        task = self.tasks_dict_map[task_uuid]
        return JobSubTask(
            uuid=task["uuid"],
            follower_name=task["follower_name"],
            reference=task["reference"],
            queue=task["queue"],
            state=task["state"],
            data=task.get("data"),
            msg=task.get("msg"),
            time_now=task.get("time"),
            handler=task.get("handler"),
        )

    def submit_single_task(self, task_uuid):
        self._get_task(task_uuid).submit(self.job["job_id"])

    def submit_tasks(self):
        pending_tasks = self.tasks_dag.all_roots()
        for task_uuid in pending_tasks:
            if self.tasks_dict_map[task_uuid]["state"] == TASK_PENDING:
                self._get_task(task_uuid).submit(self.job["job_id"])
                logging.info("Submit follower task done: %s" % task_uuid)

    def submit_downstreams_tasks(self, depend_tasks):
        """submit task_uuid downstream tasks
        This should be call before unset task_topo

        """
        tasks_can_run = self.tasks_dag.all_roots()
        tasks_to_submit = set(depend_tasks) & set(tasks_can_run)
        for task in list(self.tasks_dict_map.values()):
            if task["uuid"] in tasks_to_submit:
                if task["state"] == TASK_PENDING:
                    self._get_task(task["uuid"]).submit(self.job["job_id"])

    def submit_to_leader(self):
        from job_center.handler.leader.workers import leader_worker

        self.job.pop("_id", None)
        leader_worker.apply_async(kwargs={"job": self.job}, task_id=self.job["job_id"])
        logging.info("Submit back to leader done: %s" % self.job["job_id"])

    def mark_lost_worker_task_failed(self, lost_nodes_ip):
        # 1 mark the task with specific queue failed
        # 2 clean lock and clean broker message
        # 3 submit back to leader if need
        for task in list(self.tasks_dict_map.values()):
            if task["queue"] in lost_nodes_ip and task["state"] not in [TASK_DONE, TASK_FAILED]:
                self.mark_task_and_downstreams_failed(
                    task["uuid"], py_error.ErrorCode.Name(py_error.JOB_FOLLOWER_TIMEOUT), "Worker lost and task timeout"
                )
                # step 2
                # actually remove lock should not put here and should
                # handle by leader, but as we make task failed, then
                # do a fast hack remove here.
                remove_resource_lock(task["reference"])
                _remove_broker_message(task["uuid"])
        # step 3
        # submit back to leader and mark job done
        if self.is_finished:
            self.submit_to_leader()

    def mark_task_and_downstreams_failed(self, task_uuid, error_code, error_message):
        """mark task failed and downstreams task failed and return set query
        of mongo

        :param task_uuid:
        :param job:
        :return:
        """

        # VMTools may return utf-8 encoded error message
        # from the non-English guest OS and needs to be
        # explicitly converted to avoid the `UnicodeEncodeError`.
        logging.info(
            "Set all tasks that depend on task({}) to fail: job_id={}, error=({}, {}).".format(
                task_uuid,
                self.job.get("job_id"),
                error_code,
                error_message.decode("utf-8") if not isinstance(error_message, str) else error_message,
            )
        )
        task = self.tasks_dict_map[task_uuid]
        task["error_code"] = error_code
        task["error_msg"] = error_message
        task["state"] = TASK_FAILED
        task["time"] = utc_now_timestamp()
        task_index = self.tasks_indexes[task_uuid]
        update_query = {
            "$set": {
                "task_list.%s.error_code" % task_index: error_code,
                "task_list.%s.error_msg" % task_index: error_message,
                "task_list.%s.state" % task_index: task["state"],
                "task_list.%s.time" % task_index: task["time"],
            }
        }

        # update other tasks depend on this task and set to failed
        tasks_should_failed = self.tasks_dag.all_downstreams(task_uuid)
        for i, task_in_job in enumerate(self.job["task_list"]):
            if task_in_job["uuid"] in tasks_should_failed:
                state_key = "task_list.%s.state" % i
                ec_key = "task_list.%s.error_code" % i
                update_query["$set"][state_key] = TASK_FAILED
                update_query["$set"][ec_key] = py_error.ErrorCode.Name(py_error.JOB_FOLLOWER_FAILED_DUE_TO_DEPENDENCY)
        latest_job = _mongo_job_update(
            {"job_id": self.job["job_id"]}, update_query, {"task_topo": 1, "task_list.uuid": 1, "task_list.state": 1}
        )
        self._update_local_job(latest_job["task_topo"], task, latest_job["task_list"])
        self.reset(self.job)

    def mark_task_done_and_submit_downstream_tasks(self, task_uuid, task_result=None):
        self.tasks_dag.delete_node(task_uuid)
        task_dumps = self.tasks_dict_map[task_uuid]
        task_dumps["time"] = utc_now_timestamp()
        task_dumps["state"] = TASK_DONE
        task_dumps["task_result"] = task_result
        task_index = self.tasks_indexes[task_uuid]
        update_query = {
            "$set": {
                "task_list.%s.time" % task_index: task_dumps["time"],
                "task_list.%s.state" % task_index: task_dumps["state"],
                "task_list.%s.task_result" % task_index: task_dumps["task_result"],
            }
        }
        pull_query = {}
        for task_in_job in self.job["task_topo"]:
            if task_uuid in self.job["task_topo"][task_in_job]:
                pull_query["task_topo.%s" % task_in_job] = task_uuid
        if pull_query:
            update_query["$pull"] = pull_query
        # update_query['$unset'] = {
        #    "task_topo.%s" % task_uuid: ""
        # }
        latest_job = _mongo_job_update(
            {"job_id": self.job["job_id"]}, update_query, {"task_topo": 1, "task_list.uuid": 1, "task_list.state": 1}
        )
        depend_tasks = self.job["task_topo"].get(task_uuid, [])
        self._update_local_job(latest_job["task_topo"], task_dumps, latest_job["task_list"])
        self.reset(self.job)
        if depend_tasks:
            self.submit_downstreams_tasks(depend_tasks)

    def mark_task_running(self, task_uuid):
        if task_uuid not in self.tasks_dict_map:
            logging.warning(f"Unexpected task encounter: task_uuid={task_uuid}, job={self.job}.")

        task_dumps = self.tasks_dict_map[task_uuid]
        task_dumps["state"] = TASK_RUNNING
        task_dumps["handler"] = app.oid
        task_dumps["time"] = time.time()
        task_index = self.tasks_indexes[task_uuid]

        mark_result = False  # false means set handler failure, true is success

        # set handler when it's default handler or not exists
        latest_job = _mongo_job_update(
            {
                "job_id": self.job["job_id"],
                "$or": [  # this query may cause performance slow
                    {"task_list.%s.handler" % task_index: DEFAULT_TASK_HANDLER},
                    {"task_list.%s.handler" % task_index: {"$exists": False}},
                ],
            },
            {
                "$set": {
                    "task_list.%s.state" % task_index: task_dumps["state"],
                    "task_list.%s.handler" % task_index: task_dumps["handler"],
                    "task_list.%s.time" % task_index: task_dumps["time"],
                }
            },
            {"task_topo": 1},
        )

        if not latest_job:
            latest_job = mongodb.jobs.job.find_one(
                {"job_id": self.job["job_id"]}, {"task_list.handler": 1, "task_list.uuid": 1}
            )
            for task in latest_job["task_list"]:
                if task["uuid"] == task_uuid and task["handler"] not in get_running_worker_hash():
                    latest_job = _mongo_job_update(
                        {"job_id": self.job["job_id"], "task_list.%s.handler" % task_index: task["handler"]},
                        {
                            "$set": {
                                "task_list.%s.state" % task_index: task_dumps["state"],
                                "task_list.%s.handler" % task_index: task_dumps["handler"],
                                "task_list.%s.time" % task_index: task_dumps["time"],
                            }
                        },
                        {"task_topo": 1},
                    )
                    if latest_job:
                        mark_result = True
                    break
        else:
            mark_result = True
        if mark_result:
            self._update_local_job(latest_job["task_topo"], task_dumps)
            self.reset(self.job)
        else:
            latest_job = mongodb.jobs.job.find_one(
                {"job_id": self.job["job_id"]}, {"task_list.uuid": 1, "task_list.time": 1}
            )
            for task in latest_job["task_list"]:
                if task["uuid"] == task_uuid and task["time"] == task_dumps["time"]:
                    mark_result = True
                    break
        return mark_result

    @property
    def is_finished(self):
        """
        the job is considered finished if no task exists or all tasks enter the final state, eg. FAILED and DONE
        """
        return not self.tasks_dict_map or all(
            [task["state"] in [TASK_FAILED, TASK_DONE] for task in list(self.tasks_dict_map.values())]
        )

    @property
    def is_done(self):
        """
        the job is considered done if no task exists or all tasks is in the DONE state
        """
        return not self.tasks_dict_map or all(
            [task["state"] == TASK_DONE for task in list(self.tasks_dict_map.values())]
        )

    @property
    def latest_update_time(self):
        """
        it returns the latest update time of tasks.
        however, if no task has been generated, the latest time would be the created time of job
        """
        if not self.tasks_dict_map:
            return self.job["ctime"]

        return max([task["time"] for task in list(self.tasks_dict_map.values())])
