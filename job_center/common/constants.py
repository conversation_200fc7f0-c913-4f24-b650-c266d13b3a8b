# Copyright (c) 2013-2015, SMARTX
# All rights reserved.

SCHEDULE_TASK_JOB_CHECK = "Scheduler.Task.Job.Check"
SCHEDULE_TASK_JOB_CLEAN = "Scheduler.Task.Job.Clean"
SCHEDULE_TASK_WORKER_CHECK_CUSTOM = "Schedule.Task.Worker.Check.Custom"
SCHEDULE_TASK_WORKER_CHECK_DISCONNECTED = "Schedule.Task.Worker.Check.Disconnect"

JOB_TYPE_SCHEDULE = "schedule"
JOB_TYPE_ACTION = "action"

# celery worker state
WORKER_CONNECTED = "connected"
WORKER_DISCONNECTED = "disconnected"
WORKER_DISCONNECTED_UNEXPECTED = "disconnected_unexpected"

# task state
TASK_PENDING = "pending"
TASK_RUNNING = "running"
TASK_DONE = "done"
TASK_FAILED = "failed"

# follower return code
FOLLOWER_SAVE_RESOURCE_AND_JOB = 1
FOLLOWER_SAVE_JOB_ONLY = 2
FOLLOWER_DONOT_SAVE = 3
FOLLOWER_SAVE_TASK_RESULT = 4

# job
LIFE_CYCLE_RUN = "run"
LIFE_CYCLE_ROLLBACK = "rollback"

JOB_PENDING = "pending"
JOB_PROCESSING = "processing"
JOB_DONE = "done"
JOB_FAILED = "failed"


DEFAULT_DOLPHIN_IMAGE_POOL_NAME = "dolphin-images"
DEFAULT_DOLPHIN_INSTANCE_POOL_NAME = "dolphin-instances"
DEFAULT_PROMETHEUS_POOL_NAME = "prometheus"


# resource
RESOURCE_REFERENCE_PATTERN = r"\@\{[/\[\]A-Za-z0-9_-]+\}"

WORKER_LOST_TIMEOUT = 120
# Job PENDING longest time, 60 seconds
JOB_PENDING_TIMEOUT = 60
# Job running longest time, 24 hours
JOB_PROCESSING_TIMEOUT = 24 * 60 * 60

DEFAULT_TASK_HANDLER = "00000000-0000-0000-0000-000000000000"

MAX_JOB_SUB_TASKS = 300
MAX_JOB_RESOURCES = 300

SLOW_JOB_SECONDS = 600

WORKER_COLLECTIONS = "worker"
HEARTBEAT_COLLECTIONS = "heartbeat"

CELERYBEAT_PERSISTENCE_FILE_DEFAULT_PATH = "/celerybeat-schedule"
CELERYBEAT_PERSISTENCE_BACKUP_FILE_DEFAULT_PATH = "/__db.celerybeat-schedule"
