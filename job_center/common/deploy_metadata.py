import os

# platform category
PLATFORM_VMWARE = "vmware"
PLATFORM_XEN = "xenserver"
PLATFORM_XEN7 = "xenserver7"
PLATFORM_XEN71 = "xenserver7.1"

HOST_PLATFORM_PATH = "/etc/zbs/platform"


def is_scvm_deploy() -> bool:
    platform = get_platform()
    return platform in (PLATFORM_XEN, PLATFORM_XEN71, PLATFORM_VMWARE)


def get_platform() -> str:
    platform = ""
    if os.path.exists(HOST_PLATFORM_PATH):
        with open(HOST_PLATFORM_PATH) as f:
            platform = f.read().strip()
    return platform
