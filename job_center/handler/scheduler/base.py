# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
from functools import wraps
import logging

from celery import Task
import gevent

from common.mongo.db import mongodb
from job_center import trace as job_center_trace
from job_center.common.constants import TASK_DONE, TASK_FAILED
from job_center.common.task_topo import TaskTopo
from job_center.config import JOB_DB_NAME, RESOURCES_DB_NAME
from smartx_proto.errors import pyerror_pb2 as py_error


class ScheduleWorkerBase(Task):
    """Base class for all schedule worker"""

    abstract = True

    resource_db = mongodb[RESOURCES_DB_NAME]
    job_db = mongodb[JOB_DB_NAME]
    db = mongodb

    def resource_update(self, resource_json, update_query=None):
        update_query = update_query or {}
        if "uuid" not in update_query:
            update_query["uuid"] = resource_json["uuid"]

        self.resource_db.resource.update(update_query, {"$set": resource_json})

    def resource_update_unset(self, resource_uuid, unset_dict):
        self.resource_db.resource.update({"uuid": resource_uuid}, {"$unset": unset_dict})

    def _get_job(self, job_id):
        return mongodb.jobs.job.find_one({"job_id": job_id})

    @staticmethod
    def handle_schedule_process(func):
        """decorator to wrap follower celery task

        :param func: celery task function like kvm_vm_create
        :return: func after wrap
        """

        @wraps(func)
        def wrapper(self, job_id, task_uuid, reference):
            """
            todo: scheudler base handler should be merge with follower base handler
            later since follower base should be enough.
            """

            job_center_trace.SetTraceID(str(job_id))

            job_json = self._get_job(job_id)
            if not job_json:
                logging.warning(f"The job({job_id}) associated with the task({task_uuid}) is not found.")
                return

            task_args = job_json["schedule_task"].get("args", [])
            _kwargs = job_json["schedule_task"].get("kwargs", {})

            if _kwargs and _kwargs.get(reference):
                task_args.extend([_kwargs[reference]])

            task_topo = TaskTopo(job_json)
            result = task_topo.mark_task_running(task_uuid)

            if not result:
                logging.warning("Duplicate set task running: task uuid: {}, job_id: {}".format(task_uuid, job_id))
                return

            task_state = TASK_FAILED
            msg = None
            error_code = None
            try:
                func(self, *task_args)
                task_state = TASK_DONE
            except gevent.Timeout as e:
                msg = str(e)
                logging.warning(msg)
                error_code = py_error.ErrorCode.Name(py_error.JOB_FOLLOWER_TIMEOUT)
            except Exception as e:
                msg = "Unknown exception: %s" % str(e)
                logging.exception(msg)
                error_code = py_error.ErrorCode.Name(py_error.JOB_UNKNOWN_ERROR)
            if task_state == TASK_DONE:
                task_topo.mark_task_done_and_submit_downstream_tasks(task_uuid)
            else:
                task_topo.mark_task_and_downstreams_failed(task_uuid, error_code, msg)
            if task_topo.is_finished:
                task_topo.submit_to_leader()

        return wrapper
