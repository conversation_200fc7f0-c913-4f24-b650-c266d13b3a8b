# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
import logging
import time

from common.lib.time_utils import utc_now_timestamp
from common.mongo.db import mongodb
from job_center.common import utils
from job_center.common.constants import (
    DEFAULT_TASK_HANDLER,
    JO<PERSON>_DONE,
    JOB_FAILED,
    JOB_PENDING,
    JOB_PENDING_TIMEOUT,
    JOB_PROCESSING,
    JOB_PROCESSING_TIMEOUT,
    JOB_TYPE_SCHEDULE,
    SCHEDULE_TASK_JOB_CHECK,
    SCHEDULE_TASK_JOB_<PERSON><PERSON><PERSON>,
    SCHEDULE_TASK_WORKER_CHECK_CUSTOM,
    SCHEDULE_TASK_WORKER_CHECK_DISCONNECTED,
    TASK_DONE,
    TASK_FAILED,
    TASK_PENDING,
    TASK_RUNNING,
    WORKER_CONNECTED,
    WORKER_DISCONNECTED_UNEXPECTED,
    WORKER_LOST_TIMEOUT,
)
from job_center.common.job_queue import JobQueue
from job_center.common.task_topo import <PERSON>Topo
from job_center.common.utils import clean_jc_action_job, clean_jc_schedule_jobs, try_set_cron_job
from job_center.config import (
    CURRENT_NODE_IP,
    JOB_CHECK_INTERVAL,
    JOB_CLEAN_INTERVAL,
    WORKER_CHECK_INTERVAL,
)
from job_center.handler.leader.base import LeaderBase
from job_center.handler.scheduler.base import ScheduleWorkerBase
from job_center.main import app
from smartx_proto.errors import pyerror_pb2 as py_error

HANDLER_NOT_SET_TIMEOUT = 2 * 60


def _remove_broker_message(task_uuid, queue=None):
    logging.info(f"Remove the message from celery broker for the task: task_uuid={task_uuid}, queue={queue}")
    remove_query = {"payload": {"$regex": '"correlation_id": "%s"' % task_uuid}}
    if queue:
        remove_query["queue"] = queue
    mongodb.job_center.messages.remove(remove_query)


def _check_broker_message(task_uuid, queue=None):
    query = {"payload": {"$regex": '"correlation_id": "%s"' % task_uuid}}
    if queue:
        query["queue"] = queue
    return mongodb.job_center.messages.find_one(query)


@app.task
def submit_job_check():
    now = int(time.time()) // JOB_CHECK_INTERVAL * JOB_CHECK_INTERVAL
    if try_set_cron_job(now, "job-check"):
        if JobQueue.get_head_of_queue(SCHEDULE_TASK_JOB_CHECK):
            # handle last head job
            for _ in range(len(JobQueue.get_queue(SCHEDULE_TASK_JOB_CHECK)["queue"])):
                apply_job = False
                first_job_id = JobQueue.get_head_of_queue(SCHEDULE_TASK_JOB_CHECK)
                job = mongodb.jobs.job.find_one({"job_id": first_job_id}, {"_id": 0})
                # job do not have task_list and long time no execute then should
                # resubmit
                if (
                    job
                    and not job["task_list"]
                    and utc_now_timestamp() - job["ctime"] > JOB_PENDING_TIMEOUT
                    and (
                        not _check_broker_message(first_job_id)
                        or not job.get("handler")
                        or job.get("handler") not in utils.get_running_worker_hash()
                    )
                ):
                    apply_job = True
                elif job and job["task_list"]:
                    check_task = job["task_list"][0]
                    handler = check_task.get("handler")
                    # If the check task has been completed for
                    # more than 60s, the job should mark as completed.
                    if check_task["state"] in (JOB_DONE, JOB_FAILED) and int(check_task["time"]) + 60 < int(
                        time.time()
                    ):
                        TaskTopo(job).submit_to_leader()
                    elif not handler or handler not in utils.get_running_worker_hash():
                        TaskTopo(job).submit_single_task(check_task["uuid"])
                    elif check_task["state"] in (TASK_PENDING, TASK_RUNNING) and int(check_task["time"]) + 3600 < int(
                        time.time()
                    ):
                        TaskTopo(job).submit_single_task(check_task["uuid"])
                elif not job:
                    JobQueue.pull_job_queue(SCHEDULE_TASK_JOB_CHECK, first_job_id)
                    continue

                if apply_job:
                    from job_center.handler.leader.workers import leader_worker

                    leader_worker.apply_async(kwargs={"job": job}, task_id=job["job_id"])
                break
        else:
            # submit new job
            from job_center.handler.leader.workers import job_submit

            job_submit(
                "Scheduler",
                "Auto check",
                type=JOB_TYPE_SCHEDULE,
                schedule_task={"name": SCHEDULE_TASK_JOB_CHECK},
                queue=SCHEDULE_TASK_JOB_CHECK,
            )


@app.task(base=LeaderBase, bind=True)
def _mark_job_failed(self, job, error_msg=None, error_code=None):
    """
    JOB_FAILED = "failed"
    queue = "job.queue"
    """
    job_id = job["job_id"]
    job_error_msg = "{}, job state: [{}] -> [{}]".format(error_msg, job["state"], JOB_FAILED)

    set_properties = {
        "state": JOB_FAILED,
        "ftime": utc_now_timestamp(),
        "error_msg": job_error_msg,
        "error_code": error_code,
    }
    task_list = _mark_tasks_failed(job, error_msg, error_code)
    if task_list:
        set_properties["task_list"] = task_list
    try:
        # remove the job from queue
        # if not removed immediately, this job will be removed by next job check
        # but if this job is Scheduler.Task.Job.Check, the next job check will never be executed
        self.submit_next_queue(job)
        logging.info("remove the job [%s] from queue success" % job_id)
        # this job will be marked as failed
        mongodb.jobs.job.update({"job_id": job_id}, {"$set": set_properties})
        logging.info("mark job [%s] as failed success" % job_id)
    except Exception as e:
        logging.error("_mark_job_failed error: %s" % e)


# mark all pending tasks as failed
def _mark_tasks_failed(job, error_msg=None, error_code=None):
    task_list = job.get("task_list")
    if not task_list:
        return

    for i in range(len(task_list)):
        if task_list[i]["state"] in [TASK_PENDING, TASK_RUNNING]:
            task_error_msg = "{}, task state: [{}] -> [{}]".format(error_msg, job["state"], JOB_FAILED)
            task_list[i]["state"] = TASK_FAILED
            task_list[i]["error_msg"] = task_error_msg
            task_list[i]["error_code"] = error_code
            _remove_broker_message(task_list[i]["uuid"])
    return task_list


@app.task(base=ScheduleWorkerBase, bind=True)
@ScheduleWorkerBase.handle_schedule_process
def job_check(self):
    """recover job function

    2 types of jobs need to be resubmit

    1) job handler is not connected, then means worker may hang or dead.
    2) job do not have handler for a long time, job should be set a handler
       even only one worker works in cluster

    """
    # todo refine job recover
    from job_center.handler.leader.workers import leader_worker

    # check head of job in queue is done
    queues = mongodb.jobs.queue.find()
    for queue in queues:
        first_job_id = queue["queue"][0] if queue["queue"] else None
        if first_job_id:
            first_queue_job = mongodb.jobs.job.find_one({"job_id": first_job_id}, {"state": 1})
            if not first_queue_job or first_queue_job["state"] in [JOB_DONE, JOB_FAILED]:
                JobQueue.pull_job_queue(queue["key"], first_job_id)

    jobs = mongodb.jobs.job.find({"state": {"$in": [JOB_PENDING, JOB_PROCESSING]}}, {"_id": 0})
    job_count = jobs.count()
    logging.info("Found '%s' pending and processing jobs" % job_count)
    for job in jobs:
        try:
            time_now = utc_now_timestamp()
            # mark job failed when schedule job has Processing for 24 hours
            if job["type"] == JOB_TYPE_SCHEDULE and time_now - job["ctime"] > JOB_PROCESSING_TIMEOUT:
                logging.info(
                    "try to mark job [{}] failed, because it has been processing for [{}] seconds ".format(
                        job["job_id"], time_now - job["ctime"]
                    )
                )
                _mark_job_failed(
                    job,
                    "JOB_PROCESSING_TIMEOUT, mark it as failed",
                    py_error.ErrorCode.Name(py_error.JOB_PROCESSING_TIMEOUT),
                )
                continue

            running_worker_handler = utils.get_running_worker_hash()
            running_worker_node_ip = utils.get_running_worker_ips_from_mongo()
            job_handler = job.get("handler")
            task_list = job.get("task_list")
            resubmit_job = False
            resubmit_tasks = []
            queue_json = None
            if job.get("queue") and job["state"] == JOB_PENDING and not JobQueue.in_queue(job["queue"], job["job_id"]):
                # job not running and job is not waiting for queue
                # so push queue and wait
                logging.info("Job exist but queue is missing: %s" % job["job_id"])
                queue_json = JobQueue.push_job_queue(job["queue"], job["job_id"])
            elif job.get("queue"):
                queue_json = JobQueue.get_queue(job["queue"])

            if (
                (not job_handler or not task_list)
                and (not job.get("queue") or JobQueue.is_head_of_queue(queue_json, job["job_id"]))
                and time_now - job["ctime"] > HANDLER_NOT_SET_TIMEOUT
            ):
                # resubmit job when job do not have handler or task list
                # and longer than 5 minutes
                resubmit_job = True

            if task_list:
                task_topo = TaskTopo(job)
                pend_to_run_tasks = task_topo.tasks_dag.all_roots()
                for task in task_list:
                    if task["state"] not in [TASK_DONE, TASK_FAILED] and task["uuid"] in pend_to_run_tasks:
                        # todo(guojian) need to be refine later, consider the time,
                        # and task maybe submit by follower or scheduler at the same
                        # time

                        if task["queue"] and task["queue"] not in running_worker_node_ip:
                            # if task specific queue and queue is not working than
                            # mark task failed and
                            task_topo.mark_task_and_downstreams_failed(
                                task["uuid"],
                                py_error.ErrorCode.Name(py_error.JOB_FOLLOWER_TIMEOUT),
                                "Node %s is not working" % task["queue"],
                            )
                            _remove_broker_message(task["uuid"])
                        elif (
                            not task.get("handler") or task.get("handler") == DEFAULT_TASK_HANDLER
                        ) and time_now - task["time"] > HANDLER_NOT_SET_TIMEOUT:
                            # if task do not have queue or queue running but handler
                            # do not set for a while
                            resubmit_tasks.append(task["uuid"])
                        elif (
                            task.get("handler")
                            and task["handler"] != DEFAULT_TASK_HANDLER
                            and task["handler"] not in running_worker_handler
                        ):
                            # if task handler is not in running
                            resubmit_tasks.append(task["uuid"])

                for task_uuid in resubmit_tasks:
                    if job["type"] == JOB_TYPE_SCHEDULE:
                        task_topo.mark_task_and_downstreams_failed(
                            task_uuid,
                            py_error.ErrorCode.Name(py_error.JOB_FOLLOWER_TIMEOUT),
                            "Fail schedule task: %s" % task_uuid,
                        )
                        _remove_broker_message(task_uuid)
                    elif not _check_broker_message(task_uuid):
                        task_topo.submit_single_task(task_uuid)
                        logging.info("Task resubmit done: %s" % task_uuid)

            # get latest from mongo in case other follower update job
            if not resubmit_job:
                latest_job = mongodb.jobs.job.find_one({"job_id": job["job_id"]})
                if latest_job and job["state"] not in (JOB_DONE, JOB_FAILED):
                    task_topo = TaskTopo(latest_job)
                    if task_topo.is_finished and time_now - task_topo.latest_update_time > HANDLER_NOT_SET_TIMEOUT:
                        resubmit_job = True

            if resubmit_job and not _check_broker_message(job["job_id"]):
                leader_worker.apply_async(kwargs={"job": job}, task_id=job["job_id"])
                logging.info("Job resubmit done: %s" % job["job_id"])
        except Exception:
            logging.exception("Job check error: job id %s" % job["job_id"])


@app.task
def submit_job_clean():
    now = int(time.time()) // JOB_CLEAN_INTERVAL * JOB_CLEAN_INTERVAL
    if try_set_cron_job(now, "job-clean"):
        from job_center.handler.leader.workers import job_submit

        job_submit.delay(
            "Scheduler", "Auto check", type=JOB_TYPE_SCHEDULE, schedule_task={"name": SCHEDULE_TASK_JOB_CLEAN}
        )


@app.task(base=ScheduleWorkerBase, bind=True)
@ScheduleWorkerBase.handle_schedule_process
def job_clean(self):
    clean_jc_schedule_jobs()
    clean_jc_action_job()


@app.task
def submit_worker_check_custom():
    from job_center.handler.leader.workers import job_submit

    now = int(time.time()) // WORKER_CHECK_INTERVAL * WORKER_CHECK_INTERVAL
    node_ips = utils.get_all_worker_ips()
    if try_set_cron_job(now, "worker-check-custom"):
        task = {"name": SCHEDULE_TASK_WORKER_CHECK_CUSTOM, "hosts": node_ips}
        job_submit.delay("Scheduler", "Auto check", type=JOB_TYPE_SCHEDULE, schedule_task=task)

        disconnect_task = {
            "name": SCHEDULE_TASK_WORKER_CHECK_DISCONNECTED,
        }
        job_submit.delay("Scheduler", "Auto check", type=JOB_TYPE_SCHEDULE, schedule_task=disconnect_task)


@app.task(base=ScheduleWorkerBase, bind=True)
@ScheduleWorkerBase.handle_schedule_process
def worker_state_check_custom(self):
    utils.worker_update(CURRENT_NODE_IP, WORKER_CONNECTED)


@app.task(base=ScheduleWorkerBase, bind=True)
@ScheduleWorkerBase.handle_schedule_process
def worker_state_check_disconnect(self):
    for worker in utils.worker_find():
        if worker["state"] == WORKER_CONNECTED and worker["mtime"] < int(time.time()) - WORKER_LOST_TIMEOUT:
            utils.worker_update(worker["ip"], WORKER_DISCONNECTED_UNEXPECTED)
