# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
import copy
from functools import wraps
import json
import logging
import re
import time

from celery import Task
from celery.exceptions import SoftTimeLimitExceeded
import dpath
import gevent

from common.config.resources import RESOURCE_HIDDEN, RESOURCE_IN_USE, RESOURCE_REMOVED
from common.mongo.db import mongodb
from job_center import trace as job_center_trace
from job_center.common import utils as jc_utils
from job_center.common.constants import (
    FOLLOWER_SAVE_JOB_ONLY,
    FOLLOWER_SAVE_RESOURCE_AND_JOB,
    FOLLOWER_SAVE_TASK_RESULT,
    JOB_FAILED,
    LIFE_CYCLE_RUN,
    RESOURCE_REFERENCE_PATTERN,
    TASK_DONE,
)
from job_center.common.exceptions import JobFollowerError, ZJobError
from job_center.common.task_topo import TaskTopo
from job_center.config import (
    FOLLOWER_RETRY_INTERVAL,
    FOLLOWER_RETRY_MAX_NUMBER,
    JOB_DB_NAME,
)
from smartx_app.common.resource_db_map import get_db_collection
from smartx_proto.errors import pyerror_pb2 as py_error
from zbs.lib.zexception import ZException
from zbs.lib.zk import ZkException


class FollowerBase(Task):
    """Base class for all followers

    This is the base class for each follower, it provide db operations and
    update task life cycle on each job.
    One follower is a sub task to handle one operation like create vm or attach
    devices or vm migration.
    One follower should only handle one write liked operation.
    Follower is assigned by splitter from leader.
    Follower should be idempotent.
    Follower is handle one task in job.task_list
    handle_follower_process will wrap each celery follower task.

    """

    abstract = True

    job_db = mongodb[JOB_DB_NAME]

    @staticmethod
    def retrieve_resource(reference, job_json):
        return job_json["resources"].get(reference)

    @staticmethod
    def is_job_holding_resource_lock(job_id, reference):
        "For testing purposes, need to be a static method in classes"
        return jc_utils.check_resource_lock(reference, job_id)

    def get_runtime_data(self, job_json, reference):
        if job_json["resources"]:
            return self.resolve_reference(self.retrieve_resource(reference, job_json), job_json["resources"])
        else:
            return job_json["one_time_task"][reference]["data"]

    @staticmethod
    def resource_done(json):
        json["resource_state"] = RESOURCE_IN_USE

    @staticmethod
    def resource_removed(json):
        json["resource_state"] = RESOURCE_REMOVED

    @staticmethod
    def resource_hidden(json):
        json["resource_state"] = RESOURCE_HIDDEN

    def _get_resource_collection(self, resource_type):
        try:
            db_name, collection_name = get_db_collection(resource_type)
            return mongodb[db_name][collection_name]
        except KeyError:
            msg = "Can not get collection info for resource type: %s" % resource_type
            logging.warning(msg)
            raise JobFollowerError(msg, py_error.JOB_RESOURCE_COLLECTION_NOT_FOUND)

    def resource_update(self, resource_json, partial_resource=None, unset=None):
        db_collection = self._get_resource_collection(resource_json["type"])
        resource = db_collection.find_one({"uuid": resource_json["uuid"]})
        if resource:
            data = copy.deepcopy(partial_resource)
            upsert = False
        else:
            data = copy.deepcopy(resource_json)
            upsert = True

        update_query = {"$set": data}
        if unset:
            update_query["$unset"] = unset
            delete_fields = [field for field in data if field in unset]
            for field in delete_fields:
                del data[field]
            if "_unset" in data:
                del data["_unset"]
        if data:
            db_collection.update({"uuid": resource_json["uuid"]}, update_query, upsert=upsert)

    def _mongo_job_update(self, query, to_be_update):
        # todo handle update failed
        return self.job_db.job.find_one_and_update(query, to_be_update)

    def job_resource_update(self, job_id, resource):
        return self._mongo_job_update({"job_id": job_id}, {"$set": {"resources.%s" % resource["uuid"]: resource}})

    def _get_job(self, job_id):
        return mongodb.jobs.job.find_one({"job_id": job_id})

    @staticmethod
    def resolve_reference(resource_current, job_resources):
        """replace reference according to current job resources

        :param resource_current: current task resource
        :param job_resources: all task resources in this job
        :return: new resource after replacement
        """
        resource_string = json.dumps(resource_current)
        result = re.findall(RESOURCE_REFERENCE_PATTERN, resource_string)
        for path in result:
            try:
                value = dpath.get(job_resources, path.strip("@{}"))
                if value is None:
                    logging.warning(
                        "Can not get reference, the reference path ({}) exists but the value is None".format(path)
                    )
                    continue
                elif isinstance(value, str):
                    resource_string = resource_string.replace(path, value)
                else:
                    resource_string = resource_string.replace('"%s"' % path, str(value))
            except KeyError:
                logging.warning("Can not get reference, the reference path ({}) does not exist".format(path))
        return json.loads(resource_string)

    @staticmethod
    def include_job_id(func):
        @wraps(func)
        def wrapper(self, job_id, task_uuid, reference, *args, **kwargs):
            kwargs["job_id_for_task"] = job_id
            return func(self, job_id, task_uuid, reference, *args, **kwargs)

        return wrapper

    def update_task_state(self, result, runtime_data, job_json):
        task_result = result

        match result:
            case tuple():
                job_id = job_json["job_id"]
                return_code, return_resource = result
                partial_resource = return_resource or {}
                runtime_data.update(partial_resource)
                unset_fields = partial_resource.pop("_unset") if "_unset" in partial_resource else {}
                self.replace_resource(runtime_data, job_json)
                if return_code == FOLLOWER_SAVE_RESOURCE_AND_JOB:
                    self.resource_update(runtime_data, partial_resource, unset_fields)
                if return_code == FOLLOWER_SAVE_TASK_RESULT:
                    task_result = return_resource
                if return_code in [FOLLOWER_SAVE_RESOURCE_AND_JOB, FOLLOWER_SAVE_JOB_ONLY]:
                    self.job_resource_update(job_id, runtime_data)
            case dict():
                self.update_resource(result, job_json, runtime_data)
            case None:
                self.update_resource({}, job_json, runtime_data)

        return task_result

    def update_resource(self, partial_resource, job_json, runtime_data):
        job_id = job_json["job_id"]

        runtime_data.update(partial_resource)
        unset_fields = partial_resource.pop("_unset") if "_unset" in partial_resource else {}
        self.replace_resource(runtime_data, job_json)
        self.resource_update(runtime_data, partial_resource, unset_fields)
        self.job_resource_update(job_id, runtime_data)

    @staticmethod
    def replace_resource(resource_json, job_json):
        for resource in list(job_json["resources"].values()):
            if resource["uuid"] == resource_json["uuid"]:
                job_json["resources"][resource["uuid"]] = resource_json
                break

    @staticmethod
    def handle_follower_process(func):
        """decorator to wrap follower celery task

        :param func: celery task function like kvm_vm_create
        :return: func after wrap
        """

        @wraps(func)
        def wrapper(self, job_id, task_uuid, reference, *args, **kwargs):
            """wrapper func replace follower celery task

            1. handle all the job process and task process
            2. update task state
            3. replace resource for task
            4. only pass corresponding task json to follower
            5. retry task for some exception like ConnectionError
            6. stop chain after meet critical exception

            :param task_uuid: task uuid in this worker
            :param reference: uuid to job.resources
            :param args: other args
            :param kwargs: other kwargs
            :return: job_json if success else None
            """

            job_center_trace.SetTraceID(str(job_id))
            logging.info("Start follower task: %s" % job_id)

            job_json = self._get_job(job_id)
            if not job_json:
                logging.warning(f"The job({job_id}) associated with the task({task_uuid}) is not found.")
                return

            job_id = job_json["job_id"]
            current_life_cycle = job_json["life_cycle"]
            if current_life_cycle == LIFE_CYCLE_RUN and job_json["state"] != JOB_FAILED:
                runtime_data = self.get_runtime_data(job_json, reference)
                is_one_time_task = True if job_json.get("one_time_task") else False

                task_topo = TaskTopo(job_json)
                result = task_topo.mark_task_running(task_uuid)
                if not result:
                    logging.warning("Duplicate set task running: task uuid: {}, job_id: {}".format(task_uuid, job_id))
                    return

                msg = None
                error_code = None
                task_state = None
                task_result = {}

                for i in range(FOLLOWER_RETRY_MAX_NUMBER):
                    try:
                        if reference and not self.is_job_holding_resource_lock(job_id, reference):
                            msg = (
                                f"The job({job_id}) task ({task_uuid}) "
                                f"does not hold the specified resource({reference}) lock."
                            )
                            logging.warning(msg)
                            error_code = py_error.ErrorCode.Name(py_error.JOB_RESOURCE_LOCK_NOT_HELD)
                            break

                        if runtime_data is None:
                            msg = "Can not retrieve runtime data for task: %s" % task_uuid
                            logging.warning(msg)
                            error_code = py_error.ErrorCode.Name(py_error.JOB_UNKNOWN_ERROR)
                            break
                        # return value can be None, {key:value} or False
                        # if return False, then this resource
                        # will not be updated to mongodb
                        # if return None, then this resource will be updated
                        # to mongodb
                        # if return is {key:value}, then will only update
                        # partial resource to mongodb
                        result = func(self, runtime_data, *args, **kwargs)

                        if not is_one_time_task:
                            task_result = self.update_task_state(result, runtime_data, job_json)
                        task_state = TASK_DONE
                        break
                    except SoftTimeLimitExceeded:
                        msg = "Soft time limit exceeded"
                        error_code = py_error.ErrorCode.Name(py_error.JOB_FOLLOWER_TIMEOUT)
                        break
                    except JobFollowerError as e:
                        msg = str(e)
                        error_code = e.uc_name
                        break
                    except ZJobError as e:
                        msg = str(e)
                        error_code = e.uc_name
                        break
                    except ZException as e:
                        msg = str(e)
                        if hasattr(e, "ec_name"):
                            error_code = e.ec_name
                        else:
                            error_code = getattr(e, "uc_name", None)
                        break
                    except ZkException as e:
                        msg = str(e)
                        error_code = py_error.ErrorCode.Name(py_error.JOB_ZK_CONNECTION_TIMEOUT)
                        # not break and retry zk connection timeout
                    except gevent.Timeout as e:
                        msg = str(e)
                        error_code = py_error.ErrorCode.Name(py_error.JOB_FOLLOWER_TIMEOUT)
                    except Exception as e:
                        try:
                            import libvirt
                        except ImportError:
                            libvirt = None

                        if libvirt and isinstance(e, libvirt.libvirtError):
                            msg = str(e)
                            error_code = py_error.ErrorCode.Name(py_error.JOB_LIBVIRT_ERROR)
                            # do not break and retry libvirtError
                        elif hasattr(e, "uc_name"):
                            error_code = e.uc_name
                            msg = str(e)
                        else:
                            msg = "Unknown exception: %s" % str(e)
                            error_code = py_error.ErrorCode.Name(py_error.JOB_UNKNOWN_ERROR)
                            logging.exception(msg)
                            break

                    logging.warning(
                        "Follower error on task: {}, job: {} msg: {}, retry: {}".format(task_uuid, job_id, msg, i)
                    )
                    time.sleep(FOLLOWER_RETRY_INTERVAL)

                if task_state == TASK_DONE:
                    task_topo.mark_task_done_and_submit_downstream_tasks(task_uuid, task_result=task_result)
                else:
                    task_topo.mark_task_and_downstreams_failed(task_uuid, error_code, msg)

                if task_topo.is_finished:
                    task_topo.submit_to_leader()

        return wrapper
