# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
from job_center.common.constants import (
    SCHEDULE_TASK_JOB_CHECK,
    SCHEDULE_TASK_JOB_CLEAN,
    SCHEDULE_TASK_WORKER_CHECK_CUSTOM,
    SCHEDULE_TASK_WORKER_CHECK_DISCONNECTED,
)
from job_center.handler.scheduler.worker import (
    job_check,
    job_clean,
    worker_state_check_custom,
    worker_state_check_disconnect,
)

follower_map = {
    SCHEDULE_TASK_JOB_CHECK: job_check,
    SCHEDULE_TASK_JOB_CLEAN: job_clean,
    SCHEDULE_TASK_WORKER_CHECK_CUSTOM: worker_state_check_custom,
    SCHEDULE_TASK_WORKER_CHECK_DISCONNECTED: worker_state_check_disconnect,
}
