# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
import logging
import socket
import traceback as sys_tb

from celery.signals import task_failure, worker_shutdown

from common.lib.time_utils import utc_now_timestamp
from common.mongo.constant import COMMON_DB, FISHEYE_DB
from common.mongo.db import mongodb
from job_center.common.constants import WORKER_DISCONNECTED
from job_center.common.patch import worker_init
from job_center.common.task_topo import TaskTopo
from job_center.common.utils import clean_jc_action_job, clean_jc_schedule_jobs, sync_ha_heartbeat_info, worker_update
from job_center.common.utils import worker_init as jc_worker_init
from job_center.config import CURRENT_NODE_IP, CURRENT_NODE_VM_IP
from smartx_proto.errors import pyerror_pb2 as py_error


@worker_init.connect
def set_worker_state(sender=None, instance=None, **kwargs):
    hostname = socket.gethostname()
    jc_worker_init(hostname, CURRENT_NODE_IP, CURRENT_NODE_VM_IP)


@worker_shutdown.connect
def worker_cleanup(sender=None, instance=None, **kwargs):
    worker_update(CURRENT_NODE_IP, WORKER_DISCONNECTED)


@worker_init.connect
def setup_mongo_indexes(sender=None, instance=None, **kwargs):
    from job_center.common.scripts import create_index

    create_index.try_create_jobs_index()
    create_index.try_create_resources_lock_index()
    create_index.try_create_job_center_worker_index()
    create_index.try_create_job_center_heartbeat_index()


@worker_init.connect
def clean_job(sender=None, instance=None, **kwargs):
    clean_jc_schedule_jobs()
    clean_jc_action_job()


@worker_init.connect
def clear_ha_heartbeat(sender=None, instance=None, **kwargs):
    sync_ha_heartbeat_info()


@worker_init.connect
def migrate_mongo_collection(sender=None, instance=None, **kwargs):
    conn = mongodb
    if FISHEYE_DB in conn.database_names():
        try:
            for collection in conn[FISHEYE_DB].collection_names():
                if collection == "system.indexes":
                    continue

                for item in conn[FISHEYE_DB][collection].find():
                    conn[COMMON_DB][collection].update({"_id": item["_id"]}, {"$set": item}, upsert=True)
        except Exception as e:
            logging.exception("Migrate collection error, detail: {}".format(str(e)))
        else:
            conn.drop_database(FISHEYE_DB)


@task_failure.connect
def task_failure_handler(task_id=None, exception=None, args=None, kwargs=None, traceback=None, einfo=None, **kwds):
    """
    Determine follower error or leader error then update to mongo
    """
    logging.error("Job failed due to celery catch exception: %s" % str(exception))
    logging.error("".join(sys_tb.format_tb(traceback)))
    if kwargs:
        if "job" in kwargs:
            job = kwargs["job"]
            job_check = mongodb.jobs.job.find_one({"job_id": task_id}, {"job_id": 1})
            if job_check:
                mongodb.jobs.job.update_one(
                    {"job_id": job["job_id"]},
                    {
                        "$set": {
                            "ftime": utc_now_timestamp(),
                            "error_code": exception.uc_name
                            if hasattr(exception, "uc_name")
                            else py_error.ErrorCode.Name(py_error.JOB_UNKNOWN_ERROR),
                            "error_msg": str(exception),
                        }
                    },
                )
        if "job_id" in kwargs and "task_uuid" in kwargs and "reference" in kwargs:
            task_uuid = kwargs["task_uuid"]
            error_code = (
                exception.uc_name
                if hasattr(exception, "uc_name")
                else py_error.ErrorCode.Name(py_error.JOB_UNKNOWN_ERROR)
            )
            job = mongodb.jobs.job.find_one({"job_id": kwargs["job_id"]}, {"_id": 0})
            task_topo = TaskTopo(job)
            task_topo.mark_task_and_downstreams_failed(task_uuid, error_code, str(exception))
            if task_topo.is_finished:
                task_topo.submit_to_leader()
