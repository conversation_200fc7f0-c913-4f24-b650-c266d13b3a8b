# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
import json
import logging
import time
import uuid

from pymongo.errors import Duplicate<PERSON>eyError

from common.lib.time_utils import utc_now_timestamp
from common.mongo.db import mongodb
from job_center import trace as job_center_trace
from job_center.common.constants import (
    JO<PERSON>_DONE,
    JOB_FAILED,
    JOB_PENDING,
    JOB_TYPE_ACTION,
    JOB_TYPE_SCHEDULE,
    LIFE_CYCLE_RUN,
    SLOW_JOB_SECONDS,
)
from job_center.common.exceptions import (
    JobLeaderError,
    ZJobError,
)
from job_center.common.job_queue import JobQueue
from job_center.common.task_topo import TaskTopo
from job_center.common.utils import (
    check_resources_lock,
    filter_locked_resources,
    lock_resources,
    unlock_resources,
)
from job_center.handler.leader.base import LeaderBase
from job_center.main import app
from smartx_proto.errors import pyerror_pb2 as py_error


@app.task(base=LeaderBase, bind=True)
@LeaderBase.leader_error_handler
def leader_worker(self, job):
    """leader celery worker to handle job process

    1. restore preview crash job
    2. monitor and recover crash leader submit follower tasks(todo)
    3. handle job state # pending, processing, done, failed
    4. split task according resources
    5. sort task with dependency and detect cycle

    :param job: complete job json
    """
    # recover preview job after crash
    job = self.job_restore(job)
    job_id = job["job_id"]

    job_center_trace.SetTraceID(str(job_id))

    logging.info("Job receive: %s" % job_id)

    # only need to lock job type action resources
    # The schedule job does not involve the resource locking.
    if not job["task_list"] and job["type"] == JOB_TYPE_ACTION:
        # check resources is locked, if not lock it
        resources = job["resources"] or job["one_time_task"]
        if not check_resources_lock(resources, job_id):
            lock_job_resources(resources, job_id)

    # set handler id to job
    self.mark_job_handler(job_id, app.oid)
    logging.info(f"Job {job_id} will be handled by {app.oid}")

    run_next_step, job_next_state = self.job_state_machine(job)
    # update job state
    self.job_state_update(job_id, job_next_state)
    if not run_next_step:
        unlock_resources(job["resources"] or job["one_time_task"], job_id)
        logging.info(f"Job {job_id} has become final state")
        return

    task_topo = TaskTopo(job)

    task_list = list(task_topo.tasks_dict_map.values())

    logging.info("Job task topo create done: %s" % job_id)
    logging.info(
        "Job tasks number is {}: [{}]".format(
            len(task_list), ",".join(["{}({})".format(task["follower_name"], task["queue"]) for task in task_list])
        )
    )
    task_topo.save()

    # submit job
    task_topo.submit_tasks()

    all_tasks_finish = task_topo.is_finished

    if all_tasks_finish:
        logging.info("Job all tasks finish: %s" % job_id)
        self.submit_next_queue(job)
        if not task_topo.is_done:
            logging.info("Job failed: %s" % job_id)
            unlock_resources(job["resources"] or job["one_time_task"], job_id)
            self.job_state_update(job_id, JOB_FAILED)
            self.record_events_log(job, JOB_FAILED)
        else:
            logging.info("Job done: %s" % job_id)
            unlock_resources(job["resources"] or job["one_time_task"], job_id)
            self.job_state_update(job_id, JOB_DONE)
            self.record_events_log(job, JOB_DONE)

        # job-clean will perform the cleanup task regularly, and clean up the tasks marked |DONE| or |FAILED|
        # and over 1 hour from mongodb. therefore, when job_restore is invoked here, the corresponding job
        # may have been deleted, which will causes a KeyError in job["ftime"]
        if int(time.time()) - job["ctime"] >= SLOW_JOB_SECONDS:
            job = self.job_restore(job)
            logging.warning("Slow job {} {}".format(json.dumps(job), job.get("ftime", int(time.time())) - job["ctime"]))

    else:
        logging.info("Job not finish, exit and wait for follower: %s" % job_id)


def lock_job_resources(resources, job_id: str):
    if resources:
        logging.info(f"locking job [{job_id}] resources {[x for x in resources]}")
        if lock_resources(resources, job_id):
            logging.info(f"job [{job_id}] lock resources done")
        else:
            raise JobLeaderError("Job resources are not all locked: %s" % job_id, py_error.JOB_RESOURCES_NOT_ALL_LOCKED)
    else:
        logging.info(f"job [{job_id}] has no resources to lock")


@app.task
def job_submit(
    user,
    description,
    type=JOB_TYPE_ACTION,  # noqa: A002
    resources=None,
    schedule_task=None,
    one_time_task=None,
    queue=None,
    resource_group=None,
    job_id=None,
    event=None,
):
    """submit job worker

    :param user: username
    :param description: job description
    :param type: JOB_TYPE_ACTION or JOB_TYPE_SCHEDULE
    :param resources: used by JOB_TYPE_ACTION with ensure resource
    :param schedule_task: schedule tasks detail, used by JOB_TYPE_SCHEDULE
    :param one_time_task: used by JOB_TYPE_ACTION with one time action
    :param job_queue: the key of job queue
    :param queue: job queue
    :param resource_group: grouping resources to define sub-tasks dependencies

    :return: job_id
    """
    # todo use json schema to verify input
    job_id = job_id or str(uuid.uuid4())
    job = {
        "job_id": job_id,
        "state": JOB_PENDING,
        "life_cycle": LIFE_CYCLE_RUN,
        "description": description,
        "type": type,
        "user": user,
        "resources": resources,
        "schedule_task": schedule_task,
        "one_time_task": one_time_task,
        "ctime": utc_now_timestamp(),
        "task_list": [],
        "queue": queue,
        "resource_group": resource_group,
        "event": event,
    }

    try:
        mongodb.jobs.job.insert(job)
    except DuplicateKeyError:
        logging.info("Duplicate insert job found and ignore: %s" % str(job_id))
    finally:
        job.pop("_id", None)

    # The `schedule` job does not involve the resource locking.
    if type == JOB_TYPE_SCHEDULE:
        if not queue or JobQueue.is_head_of_queue(JobQueue.push_job_queue(queue, job_id), job_id):
            leader_worker.apply_async(kwargs={"job": job}, task_id=job_id)
        else:
            logging.info("The schedule job `{}` is waiting in the queue `{}` for scheduling.".format(job_id, queue))

        return job_id

    if filter_locked_resources([x for x in resources or one_time_task or {}]):
        mongodb.jobs.job.update(
            {"job_id": job_id},
            {
                "$set": {
                    "error_code": py_error.ErrorCode.Name(py_error.JOB_RESOURCE_IS_LOCKED),
                    "error_msg": "One or more resources are locked by other job",
                    "state": JOB_FAILED,
                }
            },
        )

        raise ZJobError("Resource is locked: job_id %s" % job_id, py_error.JOB_RESOURCE_IS_LOCKED)
    else:
        if queue and type == JOB_TYPE_ACTION:
            raise ZJobError(f"Job with queue must be schedule job: {job}")
        leader_worker.apply_async(kwargs={"job": job}, task_id=job_id)
        return job_id
