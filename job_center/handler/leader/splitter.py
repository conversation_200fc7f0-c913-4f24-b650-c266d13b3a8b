# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
import logging
import time
import uuid

from common.config.resources import RESOURCE_REMOVED
from common.mongo.db import mongodb
from job_center.common import utils
from job_center.common.constants import TASK_PENDING
from job_center.common.exceptions import JobLeaderError
from job_center.common.sub_task import JobSubTask
from smartx_app.common.resource_db_map import get_db_collection
from smartx_proto.errors import pyerror_pb2 as py_error


class SplitterBase:
    """Base class of splitter, provide common functions for all splitter

    Splitter is the class that generate the sub task according to frontend
    provided resource json and current mongodb resource json.
    Splitter is integrated into leader and each sub task is follower.
    Each resource is defined in job.resources

    example:
    class VM_splitter(SplitterBase):
        def split(resource_json_expected, current_resource_json):
            if resource_json_expected and not current_resource_json:
                self.generate_task(
                    VM_CREATE_TASK, resource_json_expected['uuid']
                )
            if resource_json_expected['resource_state'] == "removed":
                self.generate_task(
                    VM_DELETE_TASK, resource_json_expected['uuid']
                )

    """

    def split(self, json):
        pass

    @staticmethod
    def generate_task(follower_name, reference, queue=None):
        return JobSubTask(
            uuid=str(uuid.uuid4()),
            follower_name=follower_name,
            reference=reference,
            queue=queue,
            state=TASK_PENDING,  # pending, running, done, failed
            msg="",
            data="",
            time_now=int(time.time()),
        )

    def get_resource_collection(self, resource_type):
        try:
            db_name, collection_name = get_db_collection(resource_type)
            return mongodb[db_name][collection_name]
        except KeyError:
            msg = "Can not get collection info for resource type: %s" % resource_type
            logging.warning(msg)
            raise JobLeaderError(msg, py_error.JOB_RESOURCE_COLLECTION_NOT_FOUND)

    def get_old_json(self, uuid, resource_type):
        db_collection = self.get_resource_collection(resource_type)
        resource = db_collection.find_one({"uuid": uuid})
        if resource:
            del resource["_id"]
        return resource

    @staticmethod
    def include_current_json(func):
        """Decorator for splitter split function to including current resource

        :param func: split function of one splitter
        :return: a list of operations
        """

        def wrapper(self, resource_json_expected):
            current_resource_json = self.get_old_json(resource_json_expected["uuid"], resource_json_expected["type"])
            if current_resource_json and current_resource_json["resource_state"] == RESOURCE_REMOVED:
                raise JobLeaderError(
                    "The task resource(uuid={}) has been removed.".format(resource_json_expected["uuid"]),
                    py_error.JOB_TASK_RESOURCE_NOT_FOUND,
                )

            list_of_operations = func(self, resource_json_expected, current_resource_json)
            return list_of_operations

        return wrapper

    @staticmethod
    def check_host_ips_availability(host_ips_expected):
        """
        Check whether the host ips are available.
        :param host_ips_expected:   host ips need to check
        :return:
        """
        running_worker_ips = utils.get_running_worker_ips_from_mongo()
        unavailable_ips = [ip for ip in host_ips_expected if ip not in running_worker_ips]
        if unavailable_ips:
            raise JobLeaderError(
                "expected jc(%s) are not running." % ",".join(unavailable_ips), py_error.JOB_WORKER_STATUS_ERROR
            )


class SchedulerAndOneTimeActionSplitter(SplitterBase):
    def split(self, schedule_task, reference=None):
        schedule_name = schedule_task["name"]
        tasks = []
        if schedule_task.get("hosts"):
            for host in schedule_task["hosts"]:
                tasks.append(
                    self.generate_task(
                        schedule_name,
                        reference or schedule_name,  # referece in schedule is task name
                        # in one time task is reference
                        host,
                    )
                )
        elif schedule_task.get("kwargs"):
            for key in list(schedule_task["kwargs"].keys()):
                tasks.append(self.generate_task(schedule_name, reference=key))
        else:
            tasks.append(self.generate_task(schedule_name, reference or schedule_name))
        return tasks
