# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
from functools import wraps
import logging

from celery import Task

from common.event.event_message import EventMessage, batch_deal, generate_events
from common.lib.time_utils import utc_now_timestamp
from common.mongo.db import mongodb
from job_center.common.constants import JOB_DONE, JOB_FAILED, JOB_PROCESSING, LIFE_CYCLE_ROLLBACK
from job_center.common.exceptions import JobLeaderError
from job_center.common.job_queue import JobQueue
from job_center.common.utils import unlock_resources
from job_center.config import JOB_DB_NAME, LEADER_TIMEOUT  # noqa: F401
from smartx_proto.errors import pyerror_pb2 as py_error


class LeaderBase(Task):
    """Base class for leader

    This is the base class for leader worker in ./workers.py.
    It provide db operations, DAG handler and job state machine.
    Leader is the celery worker to handle one job life cycle.
    It need to ensure job will works as expect or rollback when failed.
    Leader is idempotent, one leader failed, celery will rerun one leader
    to handle the rest of job.

    """

    abstract = True

    db = mongodb[JOB_DB_NAME]

    def job_restore(self, job):
        """try to restore job from mongodb

        :param job: job json
        :type job: dict
        :return: job json, job exist
        """
        if job.get("job_id"):
            current_job = self.db.job.find_one({"job_id": job.get("job_id")}, {"_id": 0})
            if current_job:
                return current_job
            else:
                return job
        else:
            raise JobLeaderError("Job Id not found", py_error.JOB_NO_ID)

    def mark_job_handler(self, job_id, handler_id):
        self.db.job.update({"job_id": job_id}, {"$set": {"handler": handler_id}})

    def job_state_update(self, job_uuid, state, error_msg=None, error_code=None):
        """
        JOB_PENDING = "pending"
        JOB_PROCESSING = "processing"
        JOB_DONE = "done"
        JOB_FAILED = "failed"
        """
        set_properties = {"state": state}
        if state in [JOB_DONE, JOB_FAILED]:
            set_properties["ftime"] = utc_now_timestamp()
        if error_code:
            set_properties["error_code"] = error_code
        if error_msg:
            set_properties["error_msg"] = error_msg
        self.db.job.update({"job_id": job_uuid}, {"$set": set_properties})

    @staticmethod
    def job_state_machine(job):
        """state machine for job

        :param job: job json
        :type job: dict
        :return: (go to next step, next state)
        """
        job_state = job["state"]
        if job_state in [JOB_FAILED, JOB_DONE]:
            return False, job_state
        elif job_state is JOB_PROCESSING and job["life_cycle"] is LIFE_CYCLE_ROLLBACK:
            return False, JOB_FAILED
        return True, JOB_PROCESSING

    def submit_next_queue(self, job):
        job_queue = job.get("queue")
        # check in queue because program may crash after remove queue
        if job_queue and JobQueue.in_queue(job_queue, job["job_id"]):
            # should remove queue before mark job done
            # if crash after remove queue, it's ok since job is almost done
            # then job will recover and
            latest_queue = JobQueue.pull_job_queue(job_queue, job["job_id"])
            next_job_id = latest_queue["queue"][0] if latest_queue["queue"] else None
            if next_job_id:
                next_job = mongodb.jobs.job.find_one({"job_id": next_job_id}, {"_id": 0})
                if next_job:
                    from job_center.handler.leader.workers import leader_worker

                    leader_worker.apply_async(kwargs={"job": next_job}, task_id=next_job["job_id"])
                    logging.info("Submit next queue job success: %s" % next_job_id)

    @staticmethod
    def leader_error_handler(func):
        @wraps(func)
        def wrapper(self, job):
            err_msg: Exception = None
            try:
                return func(self, job)
            except JobLeaderError as e:
                logging.warning(f"Job {job['job_id']} failed: {e}")
                err_msg = e
            except Exception as e:
                logging.exception(e)
                err_msg = e
            unlock_resources(job["resources"] or job["one_time_task"], job["job_id"])
            self.submit_next_queue(job)
            self.job_state_update(
                job["job_id"],
                JOB_FAILED,
                str(err_msg),
                err_msg.uc_name if hasattr(err_msg, "uc_name") else py_error.ErrorCode.Name(py_error.JOB_UNKNOWN_ERROR),
            )
            self.record_events_log(job, JOB_FAILED)

        return wrapper

    def record_events_log(self, job, job_state):
        if job.get("event", None) is None:
            return
        batch_deal(job)
        event = job["event"]
        event_name = event.get("event_name", None)
        event_state = job_state.upper()
        event_user_name = event.get("user_name", None)
        event_data = event.get("data", None)
        event_message = event.get("message", None)
        event_detail = event.get("detail", None)
        resources = event.get("resources", None)
        user_type = event.get("user_type", "USER")
        user_role = event.get("user_role")
        if event_name == "MIGRATE_VM":
            auto_schedule = job["resources"][event_data["vm_id"]]["auto_schedule"]
            if auto_schedule is True:
                new_host = job["resources"][event_data["vm_id"]]["node_ip"]
                arg = {"new_host": new_host}
                event_message = EventMessage.update_detail(event_detail_tmp=event_message, **arg)
        if event_detail is None:
            event_detail = {"zh_CN": "None", "en_US": "None"}
        event = generate_events(
            event_name=event_name,
            event_state=event_state,
            resources=resources,
            user_name=event_user_name,
            data=event_data,
            message=event_message,
            detail=event_detail,
            user_type=user_type,
            user_role=user_role,
        )
        logging.info(event)
