# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
import time
import unittest
from unittest.mock import Mock, patch
import uuid

from common.http import exceptions as rtf_exceptions
from common.mongo.db import mongodb
from job_center.common import utils as jc_utils
from job_center.common import utils as job_center_utils
from job_center.common.constants import (
    FOLLOWER_SAVE_RESOURCE_AND_JOB,
    FOLLOWER_SAVE_TASK_RESULT,
    JOB_PROCESSING,
    LIFE_CYCLE_RUN,
    TASK_DONE,
    TASK_FAILED,
)
from job_center.common.exceptions import ZJobError
from job_center.config import RESOURCES_DB_NAME
from job_center.handler.follower import base
from job_center.handler.follower.base import FollowerBase
from job_center.main import app
from job_center.tests import utils
from smartx_app.elf.job_center.constants import KVM_VM, KVM_VOL, TASK_VM_CREATE
from smartx_proto.errors import pyerror_pb2 as py_error

app.conf.result_backend = ";".join(
    ["mongodb://%s/%s" % (x, "celery_backend") for x in job_center_utils.get_mongo_ips().split(",")]
)


@app.task(base=FollowerBase, bind=True)
@FollowerBase.handle_follower_process
def job_test_follower_base_task(self, json):
    return json


@app.task(base=FollowerBase, bind=True)
@FollowerBase.handle_follower_process
def job_test_follower_base_task_new_return_code(self, json):
    return FOLLOWER_SAVE_RESOURCE_AND_JOB, json


@app.task(base=FollowerBase, bind=True)
@FollowerBase.handle_follower_process
def job_test_follower_base_task_return_save_result(self, json):
    test_result = {"task_result_test": "result_value"}
    return FOLLOWER_SAVE_TASK_RESULT, test_result


@app.task(base=FollowerBase, bind=True)
@FollowerBase.handle_follower_process
def job_test_follower_base_task_raise(self, json):
    raise Exception


@app.task(base=FollowerBase, bind=True)
@FollowerBase.handle_follower_process
def job_test_follower_base_task_raise_zjoberror(self, json):
    raise ZJobError


@app.task(base=FollowerBase, bind=True)
@FollowerBase.handle_follower_process
def job_test_follower_base_task_raise_http_error(self, json):
    raise rtf_exceptions.RestfulClientError(msg="restful error", user_code=py_error.SRIOV_ASSIGN_VFS_FAILED)


@app.task(base=FollowerBase, bind=True)
@FollowerBase.handle_follower_process
def job_test_follower_base_task_json_modify(self, json):
    return {"vm_name": "test_vm_name_modify"}


@app.task(base=FollowerBase, bind=True)
@FollowerBase.handle_follower_process
def job_test_follower_base_task_return_true(self, json):
    return True


@app.task(base=FollowerBase, bind=True)
@FollowerBase.handle_follower_process
def job_test_follower_base_task_return_false(self, json):
    return False


@app.task(base=FollowerBase, bind=True)
@FollowerBase.handle_follower_process
def job_test_follower_base_task_return_none(self, json):
    return


class TestFollowerBase(unittest.TestCase):
    def setUp(self):
        self.vm_json = {
            "type": KVM_VM,
            "uuid": str(uuid.uuid4()),
            "user": "",
            "vm_name": "test_vm_name",
            "vcpu": 1,
            "memory": 1024,
            "node_ip": "***********",
            "vm_ip": "***********",
            "vnc_port": -1,
            "status": "Creating",
            "token": "",
            "vlan": 123,
            "desc": "test",
            "mac_addr": "",
            "create_date": int(time.time()),
            "boot_path": "",
            "disks": [],
            "bus": "ide",
        }
        self.db = mongodb[RESOURCES_DB_NAME]
        app.conf.task_always_eager = True
        self.job = utils.construct_test_job()
        mongodb.jobs.job.insert(self.job)
        self.job.pop("_id", None)

    def ready(self, task):
        while not task.ready():
            time.sleep(5)

    def _set_job(self, job):
        mongodb.jobs.job.update({"job_id": job["job_id"]}, {"$set": job})

    def _get_job(self):
        return mongodb.jobs.job.find_one({"job_id": self.job["job_id"]})

    def test_task_status_done(self):
        from job_center.main import app
        from job_center.tests.follower.test_follower_base import job_test_follower_base_task

        with patch.object(jc_utils, "check_resource_lock"):
            job, task = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
            self._set_job(job)
            celery_result = job_test_follower_base_task.delay(self.job["job_id"], task["uuid"], task["reference"])
            self.ready(celery_result)
            result_job = self._get_job()
            result_task = result_job["task_list"][0]
            self.assertEqual(app.oid, result_task["handler"])
            self.assertEqual(result_task["state"], TASK_DONE)
            self.assertEqual(result_task["uuid"], task["uuid"])

    def test_task_status_done_with_new_return_code(self):
        from job_center.main import app
        from job_center.tests.follower.test_follower_base import job_test_follower_base_task_new_return_code

        with patch.object(jc_utils, "check_resource_lock"):
            job, task = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
            self._set_job(job)
            job_test_follower_base_task_new_return_code(self.job["job_id"], task["uuid"], task["reference"])
            result_job = self._get_job()
            result_task = result_job["task_list"][0]
            self.assertEqual(app.oid, result_task["handler"])
            self.assertEqual(result_task["state"], TASK_DONE)
            self.assertEqual(result_task["uuid"], task["uuid"])

    def test_task_status_done_with_save_task_result(self):
        from job_center.main import app
        from job_center.tests.follower.test_follower_base import (
            job_test_follower_base_task_return_save_result,
        )  # this is required by celery

        with patch.object(jc_utils, "check_resource_lock"):
            job, task = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
            self._set_job(job)
            job_test_follower_base_task_return_save_result(self.job["job_id"], task["uuid"], task["reference"])
            result_job = self._get_job()
            result_task = result_job["task_list"][0]
            print(result_task)
            self.assertEqual(app.oid, result_task["handler"])
            self.assertEqual(result_task["state"], TASK_DONE)
            self.assertEqual(result_task["uuid"], task["uuid"])
            self.assertEqual(result_task["task_result"], {"task_result_test": "result_value"})

    def test_task_status_failed(self):
        from job_center.tests.follower.test_follower_base import job_test_follower_base_task_raise

        with patch.object(jc_utils, "check_resource_lock"):
            job, task = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
            self._set_job(job)
            celery_result = job_test_follower_base_task_raise.delay(self.job["job_id"], task["uuid"], task["reference"])
            self.ready(celery_result)
            result_job = self._get_job()
            result_task = result_job["task_list"][0]
            self.assertEqual(result_task["state"], TASK_FAILED)
            self.assertEqual(result_task["uuid"], task["uuid"])

    def test_task_status_failed_with_zjob_error(self):
        from job_center.tests.follower.test_follower_base import job_test_follower_base_task_raise_zjoberror

        with patch.object(jc_utils, "check_resource_lock"):
            job, task = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
            self._set_job(job)
            job_test_follower_base_task_raise_zjoberror(self.job["job_id"], task["uuid"], task["reference"])
            result_job = self._get_job()
            result_task = result_job["task_list"][0]
            self.assertEqual(result_task["state"], TASK_FAILED)
            self.assertEqual(result_task["uuid"], task["uuid"])

    def test_task_return_true(self):
        from job_center.tests.follower.test_follower_base import job_test_follower_base_task_return_true

        with patch.object(jc_utils, "check_resource_lock"):
            job, task = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
            self._set_job(job)
            celery_result = job_test_follower_base_task_return_true.delay(
                self.job["job_id"], task["uuid"], task["reference"]
            )
            self.ready(celery_result)
            result_job = self._get_job()
            self.assertEqual(result_job["resources"], job["resources"])
            self.assertEqual(result_job["task_list"][0]["task_result"], True)

    def test_task_return_false(self):
        from job_center.tests.follower.test_follower_base import job_test_follower_base_task_return_false

        with patch.object(jc_utils, "check_resource_lock"):
            job, task = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
            self._set_job(job)
            celery_result = job_test_follower_base_task_return_false.delay(
                self.job["job_id"], task["uuid"], task["reference"]
            )
            self.ready(celery_result)
            result_job = self._get_job()
            print(result_job)
            self.assertEqual(result_job["resources"], job["resources"])
            self.assertEqual(result_job["task_list"][0]["task_result"], False)

    def test_task_return_none(self):
        from job_center.tests.follower.test_follower_base import job_test_follower_base_task_return_none

        with patch.object(jc_utils, "check_resource_lock"):
            job, task = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
            self._set_job(job)
            celery_result = job_test_follower_base_task_return_none.delay(
                self.job["job_id"], task["uuid"], task["reference"]
            )
            self.ready(celery_result)
            result_job = self._get_job()
            print(result_job)
            self.assertEqual(result_job["resources"], job["resources"])
            self.assertEqual(result_job["task_list"][0]["task_result"], None)

    def test_task_json_modify(self):
        from job_center.tests.follower.test_follower_base import job_test_follower_base_task_json_modify

        with patch.object(jc_utils, "check_resource_lock"):
            job, task = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
            self._set_job(job)
            celery_result = job_test_follower_base_task_json_modify.delay(
                self.job["job_id"], task["uuid"], task["reference"]
            )
            self.ready(celery_result)
            result_job = self._get_job()
            result_resource = result_job["resources"][task["reference"]]
            self.assertEqual(result_resource["vm_name"], "test_vm_name_modify")

    def test_resource_as_expected(self):
        from job_center.tests.follower.test_follower_base import job_test_follower_base_task_json_modify

        with patch.object(jc_utils, "check_resource_lock"):
            job, task = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
            self._set_job(job)
            celery_result = job_test_follower_base_task_json_modify.delay(
                self.job["job_id"], task["uuid"], task["reference"]
            )
            self.ready(celery_result)
            resource = self.db.resource.find_one({"uuid": task["reference"]})
            del resource["_id"]
            tmp_json = self.vm_json
            tmp_json["vm_name"] = "test_vm_name_modify"
            self.assertDictEqual(resource, tmp_json)

    def test_resource_reference_resolve(self):
        my_uuid = str(uuid.uuid4())
        job_resources = {my_uuid: {"type": KVM_VOL, "path": "test_path"}}
        resource = {"type": KVM_VM, "disks": [{"path": "@{/%s/path}" % my_uuid}]}
        replacement_resource = FollowerBase.resolve_reference(resource, job_resources)
        self.assertEqual(replacement_resource["disks"][0]["path"], "test_path", replacement_resource)

        job_resources = {my_uuid: {"type": KVM_VOL}}
        resource = {"type": KVM_VM, "disks": [{"path": "@{/%s/path}" % my_uuid}]}
        replacement_resource = FollowerBase.resolve_reference(resource, job_resources)
        self.assertEqual(replacement_resource["disks"][0]["path"], "@{/%s/path}" % my_uuid, replacement_resource)

        job_resources = {my_uuid: {"type": KVM_VOL, "path": None}}
        resource = {"type": KVM_VM, "disks": [{"path": "@{/%s/path}" % my_uuid}]}
        replacement_resource = FollowerBase.resolve_reference(resource, job_resources)
        self.assertEqual(replacement_resource["disks"][0]["path"], "@{/%s/path}" % my_uuid, replacement_resource)

    def test_follower_base_task_raise_http_error(self):
        with (
            patch("job_center.handler.follower.base.time") as time_mock,
            patch.object(jc_utils, "check_resource_lock"),
        ):
            time_mock.sleep.return_value = None
            job, task = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
            self._set_job(job)
            job_test_follower_base_task_raise_http_error(self.job["job_id"], task["uuid"], task["reference"])
            result_job = self._get_job()
            result_task = result_job["task_list"][0]
            self.assertEqual(result_task["error_code"], py_error.ErrorCode.Name(py_error.SRIOV_ASSIGN_VFS_FAILED))
            self.assertIn("restful error", result_task["error_msg"])

    def tearDown(self):
        self.db.resource.drop()
        mongodb.jobs.job.remove({"job_id": self.job["job_id"]})


def test_handle_follower_process_with_not_exist_job():
    with patch.object(jc_utils, "check_resource_lock", return_value={"job_id": "job_id"}):

        @FollowerBase.handle_follower_process
        def _func(*args, **kwargs):
            pass

        get_job_stub = Mock()
        get_job_stub._get_job.return_value = None

        _func(get_job_stub, "job_id", None, None)
        assert get_job_stub._get_job.called


def test_handle_follower_process_with_not_lock():
    with (
        patch("job_center.handler.follower.base.TaskTopo") as mock_task_topo,
        patch.object(jc_utils, "check_resource_lock", return_value=False) as mock_check_resource_lock,
    ):

        @base.FollowerBase.handle_follower_process
        def _func(*args, **kwargs):
            pass

        stub = Mock()

        # Prevent this UT from being executed individually without conftest
        try:
            # set in job_center/tests/follower/conftest.py
            stub.is_job_holding_resource_lock = base.FollowerBase._src_is_job_holding_resource_lock_for_unittest
        except AttributeError:
            stub.is_job_holding_resource_lock = base.FollowerBase.is_job_holding_resource_lock

        stub._get_job.return_value = {
            "job_id": "job_id",
            "life_cycle": LIFE_CYCLE_RUN,
            "state": JOB_PROCESSING,
            "resources": {"1": None},
        }

        mock_task_topo_ins = Mock()
        mock_task_topo.return_value = mock_task_topo_ins

        _func(stub, "job_id", "task_uuid", "reference_uuid")
        assert mock_check_resource_lock.called
        assert mock_task_topo_ins.mark_task_and_downstreams_failed.called


if __name__ == "__main__":
    unittest.main()
