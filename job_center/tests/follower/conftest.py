# Copyright (c) 2013-2025, SMARTX
# All rights reserved.
import logging
from unittest import mock

import pytest

from job_center.handler.follower import base


@pytest.fixture(scope="session", autouse=True)
def mock_is_job_holding_resource_lock(request):
    logging.info("mock is_job_holding_resource_lock for all test")
    base.FollowerBase._src_is_job_holding_resource_lock_for_unittest = base.FollowerBase.is_job_holding_resource_lock
    base.FollowerBase.is_job_holding_resource_lock = mock.MagicMock(return_value=True)
