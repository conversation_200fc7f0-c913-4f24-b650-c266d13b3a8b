# Copyright (c) 2013-2015, SMARTX
# All rights reserved.

from unittest.mock import patch
from pymongo.errors import Duplicate<PERSON>eyError
import unittest

from job_center.handler.hook import worker


class TestHook(unittest.TestCase):
    def test_setup_mongo_indexes_exist(self):
        with patch("job_center.handler.hook.worker.mongodb") as mongodb_mock:
            mongodb_mock.jobs.job.index_information.return_value = {"job_id_-1": {"unique": False}}
            mongodb_mock.jobs.job.drop_index.return_value = True
            mongodb_mock.jobs.job.create_index.return_value = True
            worker.setup_mongo_indexes()

    def test_setup_mongo_indexes_crash(self):
        with patch("job_center.handler.hook.worker.mongodb") as mongodb_mock:
            mongodb_mock.jobs.job.index_information.return_value = {"job_id_-1": {"unique": False}}
            mongodb_mock.jobs.job.drop_index.return_value = True
            mongodb_mock.jobs.job.create_index.side_effect = [
                DuplicateKeyError("aa"),
                True,
                True,
                True,
                True,
                True,
                True,
                True,
            ]
            worker.setup_mongo_indexes()


if __name__ == "__main__":
    unittest.main()
