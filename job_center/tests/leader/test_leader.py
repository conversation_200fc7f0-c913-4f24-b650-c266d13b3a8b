# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
import copy
import time
import unittest
from unittest.mock import patch
import uuid

from pymongo.errors import DuplicateKeyError

from common.mongo.db import mongodb
from job_center.common import utils as job_center_utils
from job_center.common.constants import (
    JOB_DONE,
    JOB_FAILED,
    JOB_PENDING,
    JOB_TYPE_ACTION,
    JOB_TYPE_SCHEDULE,
    LIFE_CYCLE_RUN,
    MAX_JOB_RESOURCES,
    MAX_JOB_SUB_TASKS,
    TASK_DONE,
    TASK_FAILED,
    TASK_RUNNING,
)
from job_center.common.exceptions import Job<PERSON>eaderError, ZJobError
from job_center.common.scripts.create_index import try_create_resources_lock_index
from job_center.common.sub_task import JobSubTask
from job_center.common.task_topo import TaskTopo
from job_center.common.utils import (
    check_resource_lock,
    lock_resources,
    set_resource_lock,
    worker_init, check_resources_lock, unlock_resources,
)
from job_center.config import JOB_DB_NAME
from job_center.handler.follower.base import FollowerBase
from job_center.handler.hook.worker import task_failure_handler, setup_mongo_indexes
from job_center.handler.leader import base
from job_center.handler.leader.splitter import SplitterBase
from job_center.handler.leader.workers import job_submit, leader_worker, lock_job_resources
from job_center.main import app
from smartx_app.common.resource_type import KVM_VM
from smartx_proto.errors import pyerror_pb2 as py_error

app.conf.result_backend = ";".join(
    ["mongodb://{}/{}".format(x, "celery_backend") for x in job_center_utils.get_mongo_ips().split(",")]
)

TEST_TASK_FAILED = "test_task_failed"
TEST_TASK_DONE = "test_task_done"
TEST_TASK_RETURN_FALSE = "test_task_return_false"
TEST_TASK_NOT_EXIST = "test_task_not_exist"
TEST_RESOURCE_TYPE = KVM_VM


@app.task(base=FollowerBase, bind=True)
@FollowerBase.handle_follower_process
def job_test_leader_task_raise(self, json):
    raise Exception


@app.task(base=FollowerBase, bind=True)
@FollowerBase.handle_follower_process
def job_test_leader_task_return_false(self, json):
    return False


@app.task(base=FollowerBase, bind=True)
@FollowerBase.handle_follower_process
def job_test_leader_task_done(self, json):
    return json


class LeaderTestSplitterDone(SplitterBase):
    def split(self, vm_json):
        return [self.generate_task(TEST_TASK_DONE, vm_json["uuid"])]


class LeaderTestSplitterWithDoneFailed(SplitterBase):
    def split(self, vm_json):
        return [
            self.generate_task(TEST_TASK_DONE, vm_json["uuid"]),
            self.generate_task(TEST_TASK_RETURN_FALSE, vm_json["uuid"]),
            self.generate_task(TEST_TASK_FAILED, vm_json["uuid"]),
        ]


class LeaderTestSplitter(SplitterBase):
    def split(self, vm_json):
        return [self.generate_task(TEST_TASK_FAILED, vm_json["uuid"])]


class LeaderTestSplitterTaskNotImport(SplitterBase):
    def split(self, vm_json):
        return [self.generate_task(TEST_TASK_NOT_EXIST, vm_json["uuid"])]


class LeaderTestSplitterWorkerNotRunning(SplitterBase):
    def split(self, vm_json):
        return [self.generate_task(TEST_TASK_DONE, vm_json["uuid"], "worker_not_running")]


class LeaderTestSplitterTooManySubTasks(SplitterBase):
    def split(self, vm_json):
        return [
            self.generate_task(TEST_TASK_DONE, vm_json["uuid"], "worker_not_running")
            for i in range(MAX_JOB_SUB_TASKS + 1)
        ]


class LeaderTestSplitterFailedExpected(SplitterBase):
    def split(self, vm_json):
        raise JobLeaderError("test", py_error.JOB_FOLLOWER_TIMEOUT)


class LeaderTestSplitterFailedUnExpected(SplitterBase):
    def split(self, vm_json):
        raise Exception("test_failed")


class TestLeader(unittest.TestCase):
    def setUp(self):
        mongodb.resources.lock.drop()
        try_create_resources_lock_index()
        self.job_id = str(uuid.uuid4())
        self.job_db = mongodb[JOB_DB_NAME]
        self.resource_db = mongodb.resources
        self.resource_uuid = str(uuid.uuid4())
        self.resource_uuid_2 = str(uuid.uuid4())
        self.resources = {self.resource_uuid: {"type": TEST_RESOURCE_TYPE, "test": "test", "uuid": self.resource_uuid}}

        self.one_time_task = {self.resource_uuid: {"hosts": None, "name": TEST_TASK_DONE, "data": {"hello": "world"}}}

        self.splitter_set = {TEST_RESOURCE_TYPE: LeaderTestSplitter()}

        self.follower_map = {
            TEST_TASK_FAILED: job_test_leader_task_raise,
            TEST_TASK_DONE: job_test_leader_task_done,
            TEST_TASK_RETURN_FALSE: job_test_leader_task_return_false,
        }

        TaskTopo.splitter_set = self.splitter_set
        JobSubTask.follower_map = self.follower_map
        app.conf.task_always_eager = True
        worker_init("test_hostname", "test_ip", "test_ip")

    def test_job_submit(self):
        celery_job = job_submit.delay("", "", JOB_TYPE_ACTION, {})
        job_id = celery_job.get()
        self.assertIsNotNone(job_id)

    @patch("job_center.handler.leader.workers.JobQueue")
    @patch("job_center.handler.leader.workers.leader_worker")
    def test_job_submit_with_queue(self, leader_mock, job_queue_mock):
        job_queue_mock.is_head_of_queue.return_value = True
        job_submit("", "", JOB_TYPE_SCHEDULE, {}, queue="test")
        self.assertTrue(job_queue_mock.push_job_queue.called)
        self.assertFalse(job_queue_mock.pull_job_queue.called)
        self.assertTrue(job_queue_mock.is_head_of_queue.called)
        self.assertTrue(leader_mock.apply_async.called)

    @patch("job_center.handler.leader.workers.JobQueue")
    @patch("job_center.handler.leader.workers.leader_worker")
    def test_job_submit_with_queue_not_head(self, leader_mock, job_queue_mock):
        job_queue_mock.is_head_of_queue.return_value = False
        job_submit("", "", JOB_TYPE_SCHEDULE, {}, queue="test")
        self.assertTrue(job_queue_mock.push_job_queue.called)
        self.assertFalse(job_queue_mock.pull_job_queue.called)
        self.assertTrue(job_queue_mock.is_head_of_queue.called)
        self.assertFalse(leader_mock.apply_async.called)

    @patch("job_center.handler.leader.workers.mongodb")
    @patch("job_center.handler.leader.workers.JobQueue")
    @patch("job_center.handler.leader.workers.leader_worker")
    def test_job_submit_with_duplicate_job(self, lock_leader_worker, job_queue_mock, mongodb_mock):
        def mock_insert(job):
            job["_id"] = "test"
            raise DuplicateKeyError("")

        job_queue_mock.is_head_of_queue.return_value = True
        mongodb_mock.jobs.job.insert.side_effect = mock_insert
        job_submit("", "", JOB_TYPE_ACTION, {})
        self.assertFalse(job_queue_mock.push_job_queue.called)
        self.assertFalse(job_queue_mock.pull_job_queue.called)
        self.assertTrue(lock_leader_worker.apply_async.called)

        _, call_kwargs = lock_leader_worker.apply_async.call_args
        self.assertNotIn("_id", call_kwargs["kwargs"]["job"])

    def test_job_submit_with_lock_failed(self):
        app.conf.CELERY_ALWAYS_EAGER = False
        # job_id = job_submit("", "", JOB_TYPE_ACTION, self.resources)
        job_id = str(uuid.uuid4())
        lock_resources(self.resources, job_id)
        with self.assertRaises(ZJobError) as cm:
            job_submit("", "", JOB_TYPE_ACTION, self.resources)
        self.assertEqual(cm.exception.user_code, py_error.JOB_RESOURCE_IS_LOCKED)
        self.assertIsNone(mongodb.resources.lock.find_one({"resource": self.resource_uuid, "job_id": job_id}))
        mongodb.jobs.job.remove({"job_id": job_id})

    def test_job_submit_not_lock_with_schedule(self):
        job_id_1 = job_submit("", "", JOB_TYPE_SCHEDULE, self.resources)
        job_id_2 = job_submit("", "", JOB_TYPE_SCHEDULE, self.resources)
        mongodb.jobs.job.remove({"job_id": job_id_1})
        mongodb.jobs.job.remove({"job_id": job_id_2})

    @patch("job_center.handler.leader.workers.leader_worker")
    def test_lock_resources_success(self, leader_mock):
        job = {
            "job_id": self.job_id,
            "state": JOB_PENDING,
            "life_cycle": LIFE_CYCLE_RUN,
            "description": "",
            "type": JOB_TYPE_ACTION,
            "user": "",
            "resources": self.resources,
            "task_list": [],
            "task_topo": {},
            "queue": None,
        }
        self.job_db.job.insert(job)
        job.pop("_id", None)

        resources = job["resources"] or job["one_time_task"]
        job_id = job["job_id"]
        lock_job_resources(resources, job_id)
        self.assertTrue(check_resources_lock(resources, job_id))

    def test_lock_resources_twice(self):
        job = {
            "job_id": self.job_id,
            "state": JOB_PENDING,
            "life_cycle": LIFE_CYCLE_RUN,
            "description": "",
            "type": JOB_TYPE_ACTION,
            "user": "",
            "resources": self.resources,
            "task_list": [],
            "task_topo": {},
        }
        resources = job["resources"] or job["one_time_task"]
        job_id = job["job_id"]

        try:
            lock_job_resources(resources, job_id)
            self.assertTrue(check_resources_lock(resources, job_id))
            lock_job_resources(resources, job_id)
            self.assertTrue(check_resources_lock(resources, job_id))
        finally:
            unlock_resources(resources, job_id)

    def test_lock_resources_failed(self):
        # lock resource
        job0_id = "test_lock_resources_failed"

        try:
            lock_job_resources(self.resources, job0_id)
            self.assertTrue(check_resources_lock(self.resources, job0_id))

            job = {
                "job_id": self.job_id,
                "state": JOB_PENDING,
                "life_cycle": LIFE_CYCLE_RUN,
                "description": "",
                "type": JOB_TYPE_ACTION,
                "user": "",
                "resources": self.resources,
                "task_list": [],
                "task_topo": {},
            }
            self.job_db.job.insert(job)
            job.pop("_id", None)
            with self.assertRaises(JobLeaderError):
                lock_job_resources(job["resources"] or job["one_time_task"], job["job_id"])
        finally:
            unlock_resources(self.resources, job0_id)

    def test_job_failed_with_lock_check_failed(self):
        TaskTopo.splitter_set = {TEST_RESOURCE_TYPE: LeaderTestSplitterDone()}

        # lock resource
        job0_id = "test_lock_resources_failed"

        try:
            lock_job_resources(self.resources, job0_id)
            self.assertTrue(check_resources_lock(self.resources, job0_id))

            # then handle a new job, which need the same resource
            # but the resource is locked by job0
            # this job should be failed
            job = {
                "job_id": self.job_id,
                "state": JOB_PENDING,
                "life_cycle": LIFE_CYCLE_RUN,
                "description": "",
                "type": JOB_TYPE_ACTION,
                "user": "",
                "resources": self.resources,
                "task_list": [],
                "task_topo": {},
            }
            self.job_db.job.insert(job)
            job.pop("_id", None)
            leader_worker(job)
            job = self.job_db.job.find_one({"job_id": self.job_id})
            self.assertEqual(job["state"], JOB_FAILED)
            self.assertEqual(job["error_code"], "JOB_RESOURCES_NOT_ALL_LOCKED")
        finally:
            unlock_resources(self.resources, job0_id)

    def test_job_done_with_not_check_lock_after_all_task_done(self):
        task_uuid = str(uuid.uuid4())
        TaskTopo.splitter_set = {TEST_RESOURCE_TYPE: LeaderTestSplitterDone()}
        job = {
            "job_id": self.job_id,
            "state": JOB_PENDING,
            "life_cycle": LIFE_CYCLE_RUN,
            "description": "",
            "type": JOB_TYPE_ACTION,
            "user": "",
            "resources": self.resources,
            "ctime": int(time.time()),
            "task_list": [
                {
                    "data": None,
                    "follower_name": "test_task_done",
                    "msg": "",
                    "queue": "",
                    "reference": self.resource_uuid,
                    "state": TASK_DONE,
                    "time": 1458028035,
                    "uuid": task_uuid,
                }
            ],
            "task_topo": {task_uuid: []},
        }
        self.job_db.job.insert(job)
        job.pop("_id", None)
        leader_worker(job)
        job = self.job_db.job.find_one({"job_id": self.job_id})
        self.assertEqual(job["state"], JOB_DONE)

    def test_job_state_done(self):
        TaskTopo.splitter_set = {TEST_RESOURCE_TYPE: LeaderTestSplitterDone()}
        task_uuid = str(uuid.uuid4())
        job = {
            "job_id": self.job_id,
            "state": JOB_PENDING,
            "life_cycle": LIFE_CYCLE_RUN,
            "description": "",
            "type": JOB_TYPE_ACTION,
            "user": "",
            "resources": self.resources,
            "ctime": int(time.time()) - 1200,
            "task_list": [
                {
                    "data": None,
                    "follower_name": "test_task_done",
                    "msg": "",
                    "queue": "",
                    "reference": self.resource_uuid,
                    "state": TASK_DONE,
                    "time": 1458028035,
                    "uuid": task_uuid,
                }
            ],
            "task_topo": {task_uuid: []},
        }
        self.job_db.job.insert(job)
        job.pop("_id", None)
        set_resource_lock(self.resource_uuid, self.job_id)
        with patch("job_center.handler.leader.base.utc_now_timestamp") as timestamp_mocked:
            time_now = int(time.time()) + 3600
            timestamp_mocked.return_value = time_now
            leader_worker(job)
            job_json = self.job_db.job.find_one({"job_id": job["job_id"]})
            assert timestamp_mocked.called
            self.assertIsNotNone(job_json)
            self.assertEqual(job_json["state"], JOB_DONE)
            self.assertEqual(job_json["ftime"], time_now)
            self.assertFalse(check_resource_lock(self.resource_uuid, self.job_id))

    def test_job_state_failure_with_hook_handler(self):
        TaskTopo.splitter_set = {TEST_RESOURCE_TYPE: LeaderTestSplitterDone()}
        task_uuid = str(uuid.uuid4())
        job = {
            "job_id": self.job_id,
            "state": JOB_PENDING,
            "life_cycle": LIFE_CYCLE_RUN,
            "description": "",
            "type": JOB_TYPE_ACTION,
            "user": "",
            "resources": self.resources,
            "ctime": int(time.time()),
            "task_list": [
                {
                    "data": None,
                    "follower_name": "test_task_done",
                    "msg": "",
                    "queue": "",
                    "reference": self.resource_uuid,
                    "state": TASK_RUNNING,
                    "time": 1458028035,
                    "uuid": task_uuid,
                }
            ],
            "task_topo": {task_uuid: []},
        }
        self.job_db.job.insert(job)
        job.pop("_id", None)
        set_resource_lock(self.resource_uuid, self.job_id)

        kwargs = {"job_id": self.job_id, "reference": self.resource_uuid, "task_uuid": task_uuid}

        task_failure_handler(kwargs=kwargs, exception=Exception("test"))

        job_json = self.job_db.job.find_one({"job_id": job["job_id"]})
        self.assertIsNotNone(job_json)
        self.assertEqual(job_json["state"], JOB_FAILED)
        self.assertEqual(job_json["task_list"][0]["state"], TASK_FAILED)

    def test_job_state_done_with_resource_groups(self):
        TaskTopo.splitter_set = {TEST_RESOURCE_TYPE: LeaderTestSplitterWithDoneFailed()}
        TaskTopo.dependencies = {TEST_TASK_DONE: [TEST_TASK_FAILED]}
        resources = {
            self.resource_uuid: {"type": TEST_RESOURCE_TYPE, "test": "test", "uuid": self.resource_uuid},
            self.resource_uuid_2: {"type": TEST_RESOURCE_TYPE, "test": "test", "uuid": self.resource_uuid_2},
        }
        job = {
            "job_id": self.job_id,
            "state": JOB_PENDING,
            "life_cycle": LIFE_CYCLE_RUN,
            "description": "",
            "type": JOB_TYPE_ACTION,
            "user": "",
            "ctime": int(time.time()),
            "resources": resources,
            "task_list": [],
            "task_topo": {},
            "resource_group": {"test": [self.resource_uuid_2], "test_2": [self.resource_uuid]},
        }
        self.job_db.job.insert(job)
        job.pop("_id", None)
        set_resource_lock(self.resource_uuid, self.job_id)
        set_resource_lock(self.resource_uuid_2, self.job_id)
        celery_job = leader_worker.delay(job)
        celery_job.get()
        job_json = self.job_db.job.find_one({"job_id": job["job_id"]})
        self.assertIsNotNone(job_json)
        task_topo = job_json["task_topo"]
        task_done_uuid_1 = None
        task_done_uuid_2 = None
        task_failed_uuid_1 = None
        task_failed_uuid_2 = None
        for task in job_json["task_list"]:
            if task["reference"] == self.resource_uuid and task["follower_name"] == TEST_TASK_DONE:
                task_done_uuid_1 = task["uuid"]
            elif task["reference"] == self.resource_uuid_2 and task["follower_name"] == TEST_TASK_DONE:
                task_done_uuid_2 = task["uuid"]
            elif task["reference"] == self.resource_uuid and task["follower_name"] == TEST_TASK_FAILED:
                task_failed_uuid_1 = task["uuid"]
            elif task["reference"] == self.resource_uuid_2 and task["follower_name"] == TEST_TASK_FAILED:
                task_failed_uuid_2 = task["uuid"]

        self.assertNotIn(task_done_uuid_1, task_topo[task_failed_uuid_2])
        self.assertNotIn(task_done_uuid_2, task_topo[task_failed_uuid_1])

    def test_job_state_done_with_no_resource_groups(self):
        TaskTopo.splitter_set = {TEST_RESOURCE_TYPE: LeaderTestSplitterWithDoneFailed()}
        TaskTopo.dependencies = {TEST_TASK_DONE: [TEST_TASK_FAILED]}
        resources = {
            self.resource_uuid: {"type": TEST_RESOURCE_TYPE, "test": "test", "uuid": self.resource_uuid},
            self.resource_uuid_2: {"type": TEST_RESOURCE_TYPE, "test": "test", "uuid": self.resource_uuid_2},
        }
        job = {
            "job_id": self.job_id,
            "state": JOB_PENDING,
            "life_cycle": LIFE_CYCLE_RUN,
            "description": "",
            "type": JOB_TYPE_ACTION,
            "user": "",
            "ctime": int(time.time()),
            "resources": resources,
            "task_list": [],
            "task_topo": {},
        }
        self.job_db.job.insert(job)
        job.pop("_id", None)
        set_resource_lock(self.resource_uuid, self.job_id)
        set_resource_lock(self.resource_uuid_2, self.job_id)
        celery_job = leader_worker.delay(job)
        celery_job.get()
        job_json = self.job_db.job.find_one({"job_id": job["job_id"]})
        self.assertIsNotNone(job_json)
        task_topo = job_json["task_topo"]
        task_done_uuid_1 = None
        task_done_uuid_2 = None
        task_failed_uuid_1 = None
        task_failed_uuid_2 = None
        for task in job_json["task_list"]:
            if task["reference"] == self.resource_uuid and task["follower_name"] == TEST_TASK_DONE:
                task_done_uuid_1 = task["uuid"]
            elif task["reference"] == self.resource_uuid_2 and task["follower_name"] == TEST_TASK_DONE:
                task_done_uuid_2 = task["uuid"]
            elif task["reference"] == self.resource_uuid and task["follower_name"] == TEST_TASK_FAILED:
                task_failed_uuid_1 = task["uuid"]
            elif task["reference"] == self.resource_uuid_2 and task["follower_name"] == TEST_TASK_FAILED:
                task_failed_uuid_2 = task["uuid"]

        self.assertIn(task_done_uuid_1, task_topo[task_failed_uuid_2], job_json)
        self.assertIn(task_done_uuid_2, task_topo[task_failed_uuid_1], job_json)

    def test_job_state_done_with_one_time_task(self):
        job = {
            "job_id": self.job_id,
            "state": JOB_PENDING,
            "life_cycle": LIFE_CYCLE_RUN,
            "description": "",
            "type": JOB_TYPE_ACTION,
            "user": "",
            "ctime": int(time.time()),
            "resources": None,
            "one_time_task": self.one_time_task,
            "task_list": [],
            "task_topo": {},
        }
        self.job_db.job.insert(job)
        job.pop("_id", None)
        set_resource_lock(self.resource_uuid, self.job_id)
        celery_job = leader_worker.delay(job)
        celery_job.get()
        job_json = self.job_db.job.find_one({"job_id": job["job_id"]})
        self.assertIsNotNone(job_json)
        self.assertEqual(job_json["state"], JOB_DONE, job_json)
        for task in job_json["task_list"]:
            self.assertEqual(task["reference"], self.resource_uuid)
        self.assertFalse(check_resource_lock(self.resource_uuid, self.job_id))

    def test_job_state_failed(self):
        task_uuid = str(uuid.uuid4())
        job = {
            "job_id": self.job_id,
            "state": JOB_PENDING,
            "life_cycle": LIFE_CYCLE_RUN,
            "description": "",
            "ctime": int(time.time()),
            "type": JOB_TYPE_ACTION,
            "user": "",
            "resources": self.resources,
            "task_list": [
                {
                    "data": None,
                    "follower_name": "test_task_done",
                    "msg": "",
                    "queue": "",
                    "reference": self.resource_uuid,
                    "state": TASK_FAILED,
                    "time": 1458028035,
                    "uuid": task_uuid,
                }
            ],
            "task_topo": {task_uuid: []},
        }
        self.job_db.job.insert(job)
        job.pop("_id", None)
        set_resource_lock(self.resource_uuid, self.job_id)
        celery_job = leader_worker.delay(job)
        celery_job.get()
        job_json = self.job_db.job.find_one({"job_id": job["job_id"]})
        self.assertIsNotNone(job_json)
        self.assertEqual(job_json["state"], JOB_FAILED, job_json)
        self.assertEqual(len(job_json["task_list"]), 1)
        self.assertEqual(job_json["task_list"][0]["state"], TASK_FAILED, job_json)
        self.assertFalse(check_resource_lock(self.resource_uuid, self.job_id))

    def test_job_splitter_failed_as_expected(self):
        TaskTopo.splitter_set = {TEST_RESOURCE_TYPE: LeaderTestSplitterFailedExpected()}
        job = {
            "job_id": self.job_id,
            "state": JOB_PENDING,
            "life_cycle": LIFE_CYCLE_RUN,
            "description": "",
            "type": JOB_TYPE_ACTION,
            "user": "",
            "ctime": int(time.time()),
            "resources": self.resources,
            "task_list": [],
            "task_topo": {},
        }
        self.job_db.job.insert(job)
        job.pop("_id", None)
        set_resource_lock(self.resource_uuid, self.job_id)
        celery_job = leader_worker.delay(job)
        celery_job.get()
        job_json = self.job_db.job.find_one({"job_id": job["job_id"]})
        self.assertEqual(job_json["error_code"], py_error.ErrorCode.Name(py_error.JOB_FOLLOWER_TIMEOUT))
        self.assertEqual(job_json["error_msg"], "test")
        self.assertEqual(job_json["state"], JOB_FAILED)
        self.assertIn("error_msg", job_json)

    def test_job_splitter_failed_as_not_expected(self):
        TaskTopo.splitter_set = {TEST_RESOURCE_TYPE: LeaderTestSplitterFailedUnExpected()}
        job = {
            "job_id": self.job_id,
            "state": JOB_PENDING,
            "life_cycle": LIFE_CYCLE_RUN,
            "description": "",
            "type": JOB_TYPE_ACTION,
            "user": "",
            "ctime": int(time.time()),
            "resources": self.resources,
            "task_list": [],
            "task_topo": {},
        }
        self.job_db.job.insert(job)
        job.pop("_id", None)
        set_resource_lock(self.resource_uuid, self.job_id)
        celery_job = leader_worker.delay(job)
        celery_job.get()
        job_json = self.job_db.job.find_one({"job_id": job["job_id"]})
        self.assertEqual(job_json["error_code"], py_error.ErrorCode.Name(py_error.JOB_UNKNOWN_ERROR))
        self.assertEqual(job_json["state"], JOB_FAILED)
        self.assertIn("error_msg", job_json)

    def test_job_failed_as_too_many_resources(self):
        resources = {}
        for i in range(MAX_JOB_RESOURCES + 1):
            res = copy.deepcopy(self.resources[self.resource_uuid])
            res["uuid"] = str(uuid.uuid4())
            resources[res["uuid"]] = res

        job = {
            "job_id": self.job_id,
            "state": JOB_PENDING,
            "life_cycle": LIFE_CYCLE_RUN,
            "description": "",
            "type": JOB_TYPE_ACTION,
            "ctime": int(time.time()),
            "user": "",
            "resources": resources,
            "task_list": [],
            "task_topo": {},
        }
        self.job_db.job.insert(job)
        job.pop("_id", None)
        for resource_uuid in resources:
            set_resource_lock(resource_uuid, self.job_id)
        celery_job = leader_worker.delay(job)
        celery_job.get()
        job_json = self.job_db.job.find_one({"job_id": job["job_id"]})
        self.assertEqual(job_json["error_code"], py_error.ErrorCode.Name(py_error.JOB_RESOURCES_EXCESS))
        self.assertEqual(job_json["state"], JOB_FAILED)

    def test_job_failed_as_too_many_sub_tasks(self):
        TaskTopo.splitter_set = {TEST_RESOURCE_TYPE: LeaderTestSplitterTooManySubTasks()}
        job = {
            "job_id": self.job_id,
            "state": JOB_PENDING,
            "life_cycle": LIFE_CYCLE_RUN,
            "description": "",
            "type": JOB_TYPE_ACTION,
            "user": "",
            "ctime": int(time.time()),
            "resources": self.resources,
            "task_list": [],
            "task_topo": {},
        }
        self.job_db.job.insert(job)
        job.pop("_id", None)
        set_resource_lock(self.resource_uuid, self.job_id)
        celery_job = leader_worker.delay(job)
        celery_job.get()
        job_json = self.job_db.job.find_one({"job_id": job["job_id"]})
        self.assertEqual(job_json["error_code"], py_error.ErrorCode.Name(py_error.JOB_SUB_TASKS_EXCESS))
        self.assertEqual(job_json["state"], JOB_FAILED)

    def test_job_task_not_import(self):
        TaskTopo.splitter_set = {TEST_RESOURCE_TYPE: LeaderTestSplitterTaskNotImport()}
        job = {
            "job_id": self.job_id,
            "state": JOB_PENDING,
            "life_cycle": LIFE_CYCLE_RUN,
            "description": "",
            "type": JOB_TYPE_ACTION,
            "user": "",
            "ctime": int(time.time()),
            "resources": self.resources,
            "task_list": [],
            "task_topo": {},
        }
        self.job_db.job.insert(job)
        job.pop("_id", None)
        set_resource_lock(self.resource_uuid, self.job_id)
        celery_job = leader_worker.delay(job)
        celery_job.get()
        job_json = self.job_db.job.find_one({"job_id": job["job_id"]})
        self.assertEqual(job_json["state"], JOB_FAILED)
        self.assertIn("error_msg", job_json)
        self.assertEqual(job_json["error_code"], py_error.ErrorCode.Name(py_error.JOB_WORKER_TASK_NOT_FOUND))

    def test_leader_error_handler_record_event(self):
        with patch("job_center.handler.leader.base.unlock_resources"), patch.object(
            base.LeaderBase, "record_events_log"
        ) as mock_record_events_log, patch.object(
            base.LeaderBase, "submit_next_queue"
        ) as mock_submit_next_queue, patch.object(
            base.LeaderBase, "job_state_update"
        ) as mock_job_state_update:

            @app.task(base=base.LeaderBase, bind=True)
            @base.LeaderBase.leader_error_handler
            def leader_worker_test(self, job):
                raise JobLeaderError("")

            job = {"event": "123", "resources": {}, "job_id": "test_job_id", "task_list": [], "one_time_task": ""}
            leader_worker_test(job)
            mock_record_events_log.assert_called_once_with(job, JOB_FAILED)

            assert mock_submit_next_queue.called
            assert mock_job_state_update.called

    def tearDown(self):
        self.job_db.job.remove({"uuid": {"$in": [self.resource_uuid, self.resource_uuid_2]}})
        self.resource_db.lock.drop()
        mongodb.job_center.worker.remove(
            {  # desctructor for worker_init in `setUp`
                "hostname": "test_hostname",
                "ip": "test_ip",
                "vm_ip": "test_ip",
            }
        )


if __name__ == "__main__":
    unittest.main()
