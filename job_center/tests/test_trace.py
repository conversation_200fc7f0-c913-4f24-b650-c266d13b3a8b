# Copyright (c) 2013-2015, SMARTX
# All rights reserved.

import io
import logging
from unittest.mock import patch
import unittest

from job_center import trace as job_center_trace

class TestTrace(unittest.TestCase):
    def setUp(self):
        pass

    def tearDown(self):
        job_center_trace.SetTraceID(job_center_trace.DEFAULT_TRACE_ID)

    def test_trace_id(self):
        assert job_center_trace.GetTraceID() == ''

        job_center_trace.SetTraceID('123')
        assert job_center_trace.GetTraceID() == '123'

    def test_logging(self):
        # setup logger
        logger = logging.getLogger('test')

        log_stream = io.StringIO()
        logger_handler = logging.StreamHandler(log_stream)
        logger_handler.setFormatter(job_center_trace.LOGGING_FORMATTER)
        logger.addHandler(logger_handler)

        # setup trace filter
        trace_filter = job_center_trace.TraceFilter('job_center_trace')
        logger.addFilter(trace_filter)

        job_center_trace.SetTraceID('1a57cab0-aee4-4ed8-8b86-f8a644d0ba0d')

        # test logger
        logger.warning('123456789')

        log_content = log_stream.getvalue()
        assert '123456789' in log_content
        assert '1a57cab0-aee4-4ed8-8b86-f8a644d0ba0d' in log_content


if __name__ == "__main__":
    unittest.main()
