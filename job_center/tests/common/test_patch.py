# Copyright (c) 2013-2022, SMARTX
# All rights reserved.



import weakref

from celery import signals
import pytest

from job_center.common.patch import worker_init


def test_patch_worker_init():
    @worker_init.connect
    def raiser():
        raise Exception("test")

    with pytest.raises(SystemExit) as e:
        raiser()
    assert e.value.code == 1

    signals.worker_init.receivers[0][-1] == weakref.ref(raiser)
