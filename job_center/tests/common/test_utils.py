import time
from unittest import mock
import uuid

import pytest

from common.mongo.db import mongodb
from job_center.common.scripts.create_index import try_create_resources_lock_index
from job_center.common.utils import (
    bulk_check_resources_lock,
    bulk_lock_resources,
    bulk_unlock_resources,
    find_plugin_modules,
    get_all_worker_ips,
    get_running_worker_ips_from_mongo,
    load_plugin_scheduler_config,
    worker_find,
)

_spec_resource_uuid = str(uuid.uuid4())


@pytest.fixture
def lock_collection():
    mongodb.resources.lock.delete_many({})
    yield mongodb.resources.lock
    mongodb.resources.lock.delete_many({})


@pytest.yield_fixture
def job_id():
    x = str(uuid.uuid4())
    yield x
    mongodb.resources.lock.delete_many({"job_id": x})
    mongodb.resources.lock.delete_many({"resource_uuid": _spec_resource_uuid})


def test_bulk_lock_resources(job_id):
    coll = mongodb.resources.lock
    resources = [str(uuid.uuid4()) for _ in range(0, 100)]
    result = bulk_lock_resources(resources, job_id)

    assert result is True
    assert coll.count({"job_id": job_id}) == 100

    doc = coll.find_one({"job_id": job_id}, {"_id": 0})
    assert len(doc) == 3
    assert "job_id" in doc
    assert "resource_uuid" in doc
    assert "mtime" in doc


def test_bulk_lock_resources_replay(job_id):
    resources = [str(uuid.uuid4()) for _ in range(0, 100)]

    # Repeated locking is ok
    assert bulk_lock_resources(resources, job_id) is True
    assert bulk_lock_resources(resources, job_id) is True


def test_bulk_lock_resources_failed(job_id):
    try_create_resources_lock_index()

    coll = mongodb.resources.lock
    resources = [str(uuid.uuid4()) for _ in range(0, 100)]
    resources.append(_spec_resource_uuid)
    # `_spec_resource_uuid` is locked by another job
    coll.insert({"job_id": str(uuid.uuid4()), "resource_uuid": _spec_resource_uuid, "mtime": int(time.time())})

    result = bulk_lock_resources(resources, job_id)

    assert result is False
    assert coll.count_documents({"job_id": job_id}) == 0
    assert coll.count_documents({"resource_uuid": _spec_resource_uuid}) == 1


def test_bulk_unlock_resources_with_job_id(job_id):
    coll = mongodb.resources.lock
    resources = [str(uuid.uuid4()) for _ in range(0, 100)]
    coll.insert_many([{"job_id": job_id, "resource_uuid": _} for _ in resources])
    coll.insert({"job_id": job_id, "resource_uuid": _spec_resource_uuid, "mtime": int(time.time())})

    result = bulk_unlock_resources(resources, job_id)
    assert result == 100
    assert coll.count({"job_id": job_id}) == 1


def test_bulk_unlock_resources_without_job_id(job_id):
    coll = mongodb.resources.lock
    resources = [str(uuid.uuid4()) for _ in range(0, 100)]
    coll.insert_many([{"job_id": job_id, "resource_uuid": _, "mtime": int(time.time())} for _ in resources])
    coll.insert({"job_id": job_id, "resource_uuid": _spec_resource_uuid, "mtime": int(time.time())})
    resources.append(_spec_resource_uuid)

    result = bulk_unlock_resources(resources)
    assert result == 101
    assert coll.count({"job_id": job_id}) == 0


def test_bulk_check_resources(job_id):
    coll = mongodb.resources.lock
    resources = [str(uuid.uuid4()) for _ in range(0, 100)]
    coll.insert_many([{"job_id": job_id, "resource_uuid": _, "mtime": int(time.time())} for _ in resources])

    result = bulk_check_resources_lock(resources, job_id)
    assert result is True

    resources.append(_spec_resource_uuid)
    result = bulk_check_resources_lock(resources, job_id)
    assert result is False


def test_find_plugin_modules():
    with mock.patch("job_center.common.utils.find_all_plugin_modules") as find_all_plugin_modules_patched:
        find_all_plugin_modules_patched.return_value = [
            "smartx_app.elf.exporter.collector.scheduler",
            "smartx_app.elf.exporter.metric.scheduler",
            "smartx_app.elf.job_center.follower",
            "smartx_app.elf.job_center.follower.kvm",
            "smartx_app.elf.job_center.follower.kvm.iscsi_snapshot",
        ]

        modules = find_plugin_modules()
        assert len(modules) == 3
        assert "smartx_app.elf.job_center.follower" in modules
        assert "smartx_app.elf.job_center.follower.kvm" in modules
        assert "smartx_app.elf.job_center.follower.kvm.iscsi_snapshot" in modules


def test_try_create_resources_lock_index(lock_collection):
    u_index_name = "resource_uuid_-1"
    if u_index_name in lock_collection.index_information():
        lock_collection.drop_index(u_index_name)
    assert u_index_name not in lock_collection.index_information()

    ret = try_create_resources_lock_index()
    assert ret is True
    assert u_index_name in lock_collection.index_information()


def test_try_create_resources_lock_index_with_duplicate_key_error(lock_collection):
    u_index_name = "resource_uuid_-1"
    if u_index_name in lock_collection.index_information():
        lock_collection.drop_index(u_index_name)
    assert u_index_name not in lock_collection.index_information()

    lock_collection.insert_many(
        [
            {"job_id": "1", "resource_uuid": _spec_resource_uuid, "mtime": int(time.time())},
            {"job_id": "2", "resource_uuid": _spec_resource_uuid, "mtime": int(time.time())},
        ]
    )

    ret = try_create_resources_lock_index()
    assert ret is False
    assert u_index_name not in lock_collection.index_information()


def test_load_plugin_scheduler_config():
    with mock.patch("smartx_app.network.job_center.config.is_in_vmware") as mock_network_is_in_vmware, mock.patch(
        "smartx_app.network.job_center.config.is_in_xen"
    ) as mock_network_is_in_xen, mock.patch(
        "tuna.job_center.config.is_in_vmware"
    ) as mock_tuna_is_in_vmware, mock.patch(
        "tuna.job_center.config.is_in_xen"
    ) as mock_tuna_is_in_xen, mock.patch(
        "job_center.common.deploy_metadata.is_scvm_deploy"
    ) as mock_is_scvm_deploy, mock.patch(
        "smartx_app.elf.common.config.platform.is_in_kvm_or_san"
    ) as mock_is_in_kvm_or_san:
        # "sync-whitelist-for-storage" is elf tasks
        # "ovs-mirror-clean" is network tasks
        # "node-info-collect" is tuna tasks

        # we test load_plugin_scheduler_config on vmware and xen platform
        # on vmware we should not load elf job_center tasks
        mock_is_scvm_deploy.return_value = True
        mock_network_is_in_vmware.return_value = False
        mock_network_is_in_xen.return_value = False
        mock_tuna_is_in_vmware.return_value = True
        mock_tuna_is_in_xen.return_value = True

        scheduler_config = load_plugin_scheduler_config()
        assert len(scheduler_config) > 0
        assert "sync-whitelist-for-storage" not in scheduler_config
        assert "ovs-mirror-clean" in scheduler_config
        assert "node-info-collect" in scheduler_config

        # we test load_plugin_scheduler_config on elf platform
        mock_is_scvm_deploy.return_value = False
        scheduler_config = load_plugin_scheduler_config()
        assert len(scheduler_config) > 0
        assert "sync-whitelist-for-storage" in scheduler_config
        assert "ovs-mirror-clean" in scheduler_config
        assert "node-info-collect" in scheduler_config

        # not load in SMTX ELF
        mock_is_in_kvm_or_san.return_value = False
        scheduler_config = load_plugin_scheduler_config()
        assert len(scheduler_config) > 0
        assert "sync-whitelist-for-storage" not in scheduler_config
        assert "nfs-volume-snapshot-unique-size-sync" not in scheduler_config


def test_load_plugin_scheduler_config_in_elf():
    with (
        mock.patch("smartx_app.network.job_center.config.is_in_vmware") as mock_network_is_in_vmware,
        mock.patch("smartx_app.network.job_center.config.is_in_xen") as mock_network_is_in_xen,
        mock.patch("tuna.node.is_in_vmware") as mock_tuna_is_in_vmware,
        mock.patch("job_center.common.deploy_metadata.is_scvm_deploy") as mock_is_scvm_deploy,
        mock.patch("smartx_app.elf.common.config.platform.is_in_kvm_or_san") as mock_is_in_kvm_or_san,
        mock.patch("smartx_app.elf.common.config.platform.is_in_elf") as mock_is_in_elf,
    ):
        # we test load_plugin_scheduler_config on vmware and xen platform
        # on vmware we should not load elf job_center tasks
        mock_is_scvm_deploy.return_value = False
        mock_tuna_is_in_vmware.return_value = False
        mock_network_is_in_vmware.return_value = False
        mock_network_is_in_xen.return_value = False
        mock_is_in_kvm_or_san.return_value = False
        mock_is_in_elf.return_value = True

        scheduler_config = load_plugin_scheduler_config()
        assert len(scheduler_config) > 0
        assert "ensure-storage-cluster-config" in scheduler_config
        assert "sync-hb-vaild-storages" in scheduler_config


def test_get_all_worker_ips():
    mongodb.job_center.worker.delete_many({})
    mongodb.job_center.worker.insert_many(
        [
            {
                "host_uuid": "0b662b9e-2c49-11ee-a463-525400c1cbbe",
                "hostname": "beihai-600X20230727140952X1",
                "latest_hash": "23b5fd04-c1a5-37e1-9e7e-869dce08c57b",
                "mtime": 1691466327,
                "state": "connected",
                "vm_ip": "**************",
            },
            {
                "ip": "",
                "host_uuid": "0b662b9e-2c49-11ee-a463-525400c1cbbe",
                "hostname": "beihai-600X20230727140952X1",
                "latest_hash": "23b5fd04-c1a5-37e1-9e7e-869dce08c57b",
                "mtime": 1691466327,
                "state": "connected",
                "vm_ip": "**************",
            },
            {
                "ip": "*************",
                "host_uuid": "0b662b9e-2c49-11ee-a463-525400c1cbbe",
                "hostname": "beihai-600X20230727140952X1",
                "latest_hash": "ad0ab3f8-75a2-3f5b-a3d8-f79806d46f75",
                "mtime": 1691476992,
                "state": "connected",
                "vm_ip": "**************",
            },
            {
                "ip": "*************",
                "host_uuid": "169101ce-2c49-11ee-a9df-5254000910c8",
                "hostname": "beihai-600X20230727140952X3",
                "latest_hash": "9e42b712-8416-3fce-bb19-a809324fa7b2",
                "mtime": 1691476976,
                "state": "connected",
                "vm_ip": "************",
            },
            {
                "ip": "*************",
                "host_uuid": "10cf71ee-2c49-11ee-85e2-525400924ace",
                "hostname": "beihai-600X20230727140952X2",
                "latest_hash": "e43231f7-f2c7-333e-b9a2-9f01a3f79038",
                "mtime": 1691477037,
                "state": "connected",
                "vm_ip": "**************",
            },
        ]
    )

    want = ["*************", "*************", "*************"]

    assert get_all_worker_ips() == want
    assert get_running_worker_ips_from_mongo() == want

    workers = worker_find()
    assert workers.count() == 3
    assert [worker["ip"] for worker in workers] == want

    mongodb.job_center.worker.delete_many({})
