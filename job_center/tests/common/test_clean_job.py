# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
import pytest

from common.mongo.db import mongodb
from job_center.common.constants import (
    JOB_DONE,  # noqa: I100,I202
    JOB_FAILED,
    JOB_PENDING,
    JOB_PROCESSING,
    JOB_TYPE_ACTION,
    JOB_TYPE_SCHEDULE,
)
from job_center.common.utils import clean_jc_action_job, clean_jc_schedule_jobs, sync_ha_heartbeat_info
from smartx_proto.errors.pyerror_pb2 import HOST_LABEL_CONFIG_FAILED


@pytest.yield_fixture
def job_coll():
    coll = mongodb.jobs.job_mock
    yield coll
    coll.drop()


@pytest.yield_fixture
def cron_job_coll():
    coll = mongodb.jobs.cron_job_mock
    yield coll
    coll.drop()


@pytest.yield_fixture
def ha_heartheat_coll():
    coll = mongodb.job_center.heartbeat
    yield coll
    coll.drop()


def test_clean_jc_schedule_jobs(job_coll, cron_job_coll):
    job_coll.insert_many([{"ctime": i, "type": JOB_TYPE_SCHEDULE, "state": JOB_DONE} for i in range(500)])
    job_coll.insert_many([{"ctime": i, "type": JOB_TYPE_SCHEDULE, "state": JOB_FAILED} for i in range(500)])

    clean_jc_schedule_jobs(400, job_coll, cron_job_coll)
    assert job_coll.find().count() == 200

    job_coll.insert_many([{"ctime": i, "type": JOB_TYPE_ACTION, "state": JOB_DONE} for i in range(100)])
    job_coll.insert_many([{"ctime": i, "type": JOB_TYPE_SCHEDULE, "state": JOB_PROCESSING} for i in range(100)])
    cron_job_coll.insert_many([{"time": i} for i in range(500)])
    clean_jc_schedule_jobs(450, job_coll, cron_job_coll)

    assert job_coll.find().count() == 300
    assert cron_job_coll.find().count() == 50


def test_clean_action_jobs(job_coll):
    job_coll.insert_many([{"ctime": i, "type": JOB_TYPE_ACTION, "state": JOB_DONE} for i in range(550)])
    job_coll.insert_many([{"ctime": i, "type": JOB_TYPE_ACTION, "state": JOB_FAILED} for i in range(500)])

    clean_jc_action_job(1000, job_coll)
    assert job_coll.find().count() == 1000

    job_coll.insert_many([{"ctime": i, "type": JOB_TYPE_ACTION, "state": JOB_PENDING} for i in range(10)])
    job_coll.insert_many([{"ctime": i, "type": JOB_TYPE_ACTION, "state": JOB_PROCESSING} for i in range(10)])

    clean_jc_action_job(50, job_coll)
    assert job_coll.find().count() == 70

    clean_jc_action_job(100, job_coll)
    assert job_coll.find().count() == 70


def test_clean_dirty_localhost_hb(ha_heartheat_coll):
    from job_center.config import CURRENT_NODE_HOST_UUID, CURRENT_NODE_IP

    expect_hb = [
        {
            "host_uuid": CURRENT_NODE_HOST_UUID,
            "data_ip": CURRENT_NODE_IP,
        },
        {
                "host_uuid": "expect_mock_uuid_1",
                "data_ip": "expect_mock_ip_1",
        },
        {
                "host_uuid": "expect_mock_uuid_2",
                "data_ip": "expect_mock_ip_2",
        },
    ]

    dirty_hb = [
        {
            "host_uuid": "",
            "data_ip": CURRENT_NODE_IP,
        },
        {
            "host_uuid": "dirty_mock_uuid_1",
            "data_ip": CURRENT_NODE_IP,
        },
    ]

    ha_heartheat_coll.insert_many(expect_hb)
    ha_heartheat_coll.insert_many(dirty_hb)

    sync_ha_heartbeat_info()

    hb_infos = list(ha_heartheat_coll.find(
        {},
        {"_id": 0, "host_uuid": 1, "data_ip": 1},
    ))

    host_uuids = [hb_info["host_uuid"] for hb_info in hb_infos]
    data_ips = [hb_info["data_ip"] for hb_info in hb_infos]

    assert len(hb_infos) == 3
    assert all(
        [
            host_uuid in host_uuids for host_uuid
            in ["expect_mock_uuid_1", "expect_mock_uuid_2", CURRENT_NODE_HOST_UUID]
        ]
    )
    assert all(
        [
            data_ip in data_ips for data_ip
            in ["expect_mock_ip_1", "expect_mock_ip_2", CURRENT_NODE_IP]
        ]
    )
