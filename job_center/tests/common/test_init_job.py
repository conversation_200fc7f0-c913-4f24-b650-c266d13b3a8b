# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
from unittest.mock import Mock, patch
import pytest

from common.mongo.db import mongodb
from job_center import config
from job_center.common import init_job
import importlib


@pytest.fixture
def _collection(request):
    collection = mongodb[config.JOB_DB_NAME][init_job.TABLE]

    def fin():
        collection.drop()

    request.addfinalizer(fin)
    return collection


def test_db_mark(_collection):
    with patch.object(config, "CURRENT_NODE_HOST_UUID", new="123"):
        importlib.reload(init_job)

        init_job.mark("test")
        assert _collection.find_one({"name": "test"})
        assert init_job.check_mark("test")


def test_call_once_per_node(_collection):
    with patch.object(config, "CURRENT_NODE_HOST_UUID", new="123"):
        importlib.reload(init_job)

        mock_func = Mock()

        @init_job.call_once_per_node("call_once_per_node")
        def mock_func_with_call_once_per_node():
            mock_func()

        mock_func_with_call_once_per_node()
        assert _collection.find_one({"name": "call_once_per_node"})
        assert mock_func.call_count == 1

        mock_func_with_call_once_per_node()
        assert mock_func.call_count == 1


def test_call_once_per_node_with_error(_collection):
    with patch.object(config, "CURRENT_NODE_HOST_UUID", new="123"):
        importlib.reload(init_job)

        mock_func = Mock()
        mock_func.side_effect = Exception("test")

        @init_job.call_once_per_node("call_once_per_node_1")
        def mock_func_with_call_once_per_node():
            mock_func()

        mock_func_with_call_once_per_node()
        assert not _collection.find_one({"name": "call_once_per_node_1"})
