# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
from copy import deepcopy
import copy
import time
import uuid

from unittest.mock import patch
import pytest
import unittest
from common.mongo.db import mongodb
from job_center.common import task_topo as task_topo_module
from job_center.common.constants import (
    JOB_PENDING,
    JOB_TYPE_ACTION,
    LIFE_CYCLE_RUN,
    MAX_JOB_RESOURCES,
    TASK_DONE,
    TASK_FAILED,
    TASK_PENDING,
)
from job_center.common.exceptions import JobLeaderError
from job_center.common.sub_task import JobSubTask
from job_center.common.task_topo import TaskTopo
from smartx_app.common.resource_type import (
    KVM_VM,
    KVM_VM_SNAPSHOT,
    KVM_VM_TEMPLATE,
    KVM_VOL,
    KVM_VOL_SNAPSHOT,
    KVM_VOL_TEMPLATE,
)
from common.lib import time_utils


class TestTaskTopo(unittest.TestCase):
    def setUp(self):
        self.task_uuid_1 = str(uuid.uuid4())
        self.task_uuid_2 = str(uuid.uuid4())
        self.task_uuid_3 = str(uuid.uuid4())

        self.job_id = str(uuid.uuid4())
        self.resource_uuid = str(uuid.uuid4())
        self.resources = {self.resource_uuid: {"type": KVM_VM, "test": "test", "uuid": self.resource_uuid}}
        self.job = {
            "job_id": self.job_id,
            "state": JOB_PENDING,
            "life_cycle": LIFE_CYCLE_RUN,
            "description": "",
            "type": JOB_TYPE_ACTION,
            "user": "",
            "resources": self.resources,
            "task_list": [
                {
                    "data": None,
                    "follower_name": "test_task_done",
                    "msg": "",
                    "queue": "",
                    "reference": self.resource_uuid,
                    "state": TASK_PENDING,
                    "time": 1458028035,
                    "uuid": self.task_uuid_1,
                },
                {
                    "data": None,
                    "follower_name": "test_task_done",
                    "msg": "",
                    "queue": "",
                    "reference": self.resource_uuid,
                    "state": TASK_PENDING,
                    "time": 1458028035,
                    "uuid": self.task_uuid_2,
                },
                {
                    "data": None,
                    "follower_name": "test_task_done",
                    "msg": "",
                    "queue": "",
                    "reference": self.resource_uuid,
                    "state": TASK_PENDING,
                    "time": 1458028035,
                    "uuid": self.task_uuid_3,
                },
            ],
            "task_topo": {self.task_uuid_1: [], self.task_uuid_2: [self.task_uuid_1], self.task_uuid_3: []},
        }

        self.empty_job_id = str(uuid.uuid4())
        self.empty_job = copy.deepcopy(self.job)
        self.empty_job.update(
            {
                "task_list": [],
                "task_topo": [],
                "job_id": self.empty_job_id,
                "ctime": time_utils.utc_now_timestamp(),
            }
        )

        mongodb.jobs.job.insert(self.job)
        mongodb.jobs.job.insert(self.empty_job)

    def test_with_empty_subtask(self):
        with patch.object(TaskTopo, "_split_task") as mock_split_task:
            mock_split_task.return_value = []

            task_topo = TaskTopo(self.empty_job)

            assert task_topo.job
            assert not task_topo.job["task_topo"]
            assert not task_topo.job["resource_group"]

            assert task_topo.is_finished is True
            assert task_topo.is_done is True
            assert task_topo.latest_update_time == task_topo.job["ctime"]

    def test_task_topo_save(self):
        task_topo = TaskTopo(self.job)
        result = task_topo.save()
        self.assertIsNone(result)

    def test_task_topo_reset(self):
        tmp_job = deepcopy(self.job)
        tmp_job["task_list"] = tmp_job["task_list"][:1]
        del tmp_job["task_topo"][self.task_uuid_2]
        del tmp_job["task_topo"][self.task_uuid_3]
        task_topo = TaskTopo(tmp_job)
        self.assertNotEqual(len(list(task_topo.tasks_dict_map.values())), len(self.job["task_list"]))
        task_topo.reset(self.job)
        self.assertEqual(len(list(task_topo.tasks_dict_map.values())), len(self.job["task_list"]))

    @patch("job_center.common.task_topo.TaskTopo._split_task")
    def test_task_topo_auto_generate_resource_group_for_elf_vm(self, split_task_mock):
        self.job["description"] = "VM_START"
        volume_uuid = str(uuid.uuid4())
        resources = {
            self.resource_uuid: {
                "type": KVM_VM,
                "test": "test",
                "uuid": self.resource_uuid,
                "disks": [{"path": "test"}],
            },
            volume_uuid: {"type": KVM_VOL, "test": "test", "uuid": volume_uuid, "path": "test"},
        }

        self.job["resources"] = resources
        tasks = [JobSubTask(uuid=str(uuid.uuid4()), reference=task["reference"]) for task in self.job["task_list"]]
        self.job["task_list"] = []
        split_task_mock.return_value = tasks

        task_topo = TaskTopo(self.job)
        latest_job = task_topo.job
        self.assertIn(self.resource_uuid, latest_job["resource_group"])
        self.assertIsNotNone(latest_job["resource_group"].get(self.resource_uuid))

    @patch("job_center.common.task_topo.TaskTopo._split_task")
    def test_task_topo_auto_generate_resource_group_for_elf_vm_snapshot(self, split_task_mock):
        self.job["description"] = "VM_SNAPSHOT_BULK_DELETE"
        volume_snapshot_uuid = str(uuid.uuid4())
        resources = {
            self.resource_uuid: {
                "type": KVM_VM_SNAPSHOT,
                "test": "test",
                "uuid": self.resource_uuid,
                "disks": [{"snapshot_uuid": volume_snapshot_uuid}],
            },
            volume_snapshot_uuid: {"type": KVM_VOL_SNAPSHOT, "test": "test", "uuid": volume_snapshot_uuid},
        }
        self.job["resources"] = resources
        tasks = [JobSubTask(uuid=str(uuid.uuid4()), reference=task["reference"]) for task in self.job["task_list"]]
        self.job["task_list"] = []
        split_task_mock.return_value = tasks

        task_topo = TaskTopo(self.job)
        latest_job = task_topo.job
        self.assertIn(self.resource_uuid, latest_job["resource_group"])
        self.assertIsNotNone(latest_job["resource_group"].get(self.resource_uuid))

    @patch("job_center.common.task_topo.TaskTopo._split_task")
    def test_task_topo_auto_generate_resource_group_for_elf_vm_template(self, split_task_mock):
        self.job["description"] = "VM_TEMPLATE_DELETE"
        volume_template_uuid = str(uuid.uuid4())
        resources = {
            self.resource_uuid: {
                "type": KVM_VM_TEMPLATE,
                "test": "test",
                "uuid": self.resource_uuid,
                "disks": [{"volume_template_uuid": volume_template_uuid}],
            },
            volume_template_uuid: {"type": KVM_VOL_TEMPLATE, "test": "test", "uuid": volume_template_uuid},
        }
        self.job["resources"] = resources
        tasks = [JobSubTask(uuid=str(uuid.uuid4()), reference=task["reference"]) for task in self.job["task_list"]]
        self.job["task_list"] = []
        split_task_mock.return_value = tasks

        task_topo = TaskTopo(self.job)
        latest_job = task_topo.job
        self.assertIn(self.resource_uuid, latest_job["resource_group"])
        self.assertIsNotNone(latest_job["resource_group"].get(self.resource_uuid))

    @patch("job_center.common.task_topo.TaskTopo._split_task")
    def test_task_topo_not_auto_generate_resource_group_for_elf(self, split_task_mock):
        self.job["description"] = "test_not_generate"
        volume_uuid = str(uuid.uuid4())
        resources = {
            self.resource_uuid: {"type": KVM_VM, "test": "test", "uuid": self.resource_uuid},
            volume_uuid: {"type": KVM_VOL, "test": "test", "uuid": volume_uuid},
        }

        self.job["resources"] = resources
        tasks = [JobSubTask(uuid=str(uuid.uuid4()), reference=task["reference"]) for task in self.job["task_list"]]
        self.job["task_list"] = []
        split_task_mock.return_value = tasks

        task_topo = TaskTopo(self.job)
        latest_job = task_topo.job
        self.assertNotIn(self.resource_uuid, latest_job["resource_group"])

    def test_submit_tasks(self):
        with patch.object(task_topo_module.JobSubTask, "submit") as submit:
            submit.return_value = True
            task_topo = TaskTopo(self.job)
            task_topo.submit_tasks()
            self.assertEqual(submit.call_count, 2)

    def test_submit_downstreams_tasks_with_all_pending(self):
        with patch.object(task_topo_module.JobSubTask, "submit") as submit:
            submit.return_value = True
            task_topo = TaskTopo(self.job)
            task_topo.submit_downstreams_tasks(self.task_uuid_2)
            self.assertEqual(submit.call_count, 0)

    def test_submit_downstreams_tasks_with_multi_dependencies(self):
        self.job["task_topo"][self.task_uuid_3] = [self.task_uuid_1]
        mongodb.jobs.job.save(self.job)
        with patch.object(task_topo_module.JobSubTask, "submit") as submit:
            submit.return_value = True
            task_topo = TaskTopo(self.job)
            task_topo.mark_task_done_and_submit_downstream_tasks(self.task_uuid_2)
            self.job = task_topo.job
            self.assertEqual(task_topo.tasks_dict_map[self.task_uuid_2]["state"], TASK_DONE)
            self.assertEqual(submit.call_count, 0)

    def test_submit_downstreams_tasks_with_done_task_3(self):
        with patch.object(task_topo_module.JobSubTask, "submit") as submit:
            submit.return_value = True
            task_topo = TaskTopo(self.job)
            task_topo.mark_task_done_and_submit_downstream_tasks(self.task_uuid_3)
            self.job = task_topo.job
            self.assertEqual(task_topo.tasks_dict_map[self.task_uuid_3]["state"], TASK_DONE)
            self.assertEqual(submit.call_count, 0)

    def test_submit_downstreams_tasks_with_done_task_2(self):
        with patch.object(task_topo_module.JobSubTask, "submit") as submit:
            submit.return_value = True
            task_topo = TaskTopo(self.job)
            task_topo.mark_task_done_and_submit_downstream_tasks(self.task_uuid_2)
            self.job = task_topo.job
            self.assertEqual(task_topo.tasks_dict_map[self.task_uuid_2]["state"], TASK_DONE)
            self.assertEqual(submit.call_count, 1)

    def test_submit_downstreams_tasks_with_failed(self):
        with patch.object(task_topo_module.JobSubTask, "submit") as submit:
            submit.return_value = True
            task_topo = TaskTopo(self.job)
            task_topo.mark_task_and_downstreams_failed(self.task_uuid_2, "test_code", "test_msg中文错误信息")
            self.assertEqual(task_topo.tasks_dict_map[self.task_uuid_1]["state"], TASK_FAILED)
            self.assertEqual(task_topo.tasks_dict_map[self.task_uuid_2]["state"], TASK_FAILED)
            latest_job = mongodb.jobs.job.find_one({"job_id": self.job_id})
            task_topo = TaskTopo(latest_job)
            self.assertEqual(task_topo.tasks_dict_map[self.task_uuid_1]["state"], TASK_FAILED)
            self.assertEqual(task_topo.tasks_dict_map[self.task_uuid_2]["state"], TASK_FAILED)
            task_topo.submit_downstreams_tasks(self.task_uuid_2)
            self.assertEqual(submit.call_count, 0)

    def test_mark_lost_worker_task_failed_with_queue(self):
        with patch.object(task_topo_module, "remove_resource_lock") as lock_mock:
            test_queue = ["queue1", "queue2"]
            self.job["task_list"][0]["queue"] = test_queue[0]
            task_topo = TaskTopo(self.job)
            task_topo.mark_lost_worker_task_failed(test_queue)
            self.assertTrue(lock_mock.called)

    def test_mark_lost_worker_task_failed_without_queue(self):
        with patch.object(task_topo_module, "remove_resource_lock") as lock_mock:
            test_queue = ["queue1", "queue2"]
            task_topo = TaskTopo(self.job)
            task_topo.mark_lost_worker_task_failed(test_queue)
            self.assertFalse(lock_mock.called)

    @patch.object(task_topo_module, "get_running_worker_hash")
    def test_mark_running_twice(self, get_running_worker_hash_mock):
        from job_center.main import app

        get_running_worker_hash_mock.return_value = [app.oid]
        task_topo = TaskTopo(self.job)
        result1 = task_topo.mark_task_running(self.task_uuid_1)
        result2 = task_topo.mark_task_running(self.task_uuid_1)
        self.assertTrue(result1)
        self.assertFalse(result2)

    @patch("job_center.common.task_topo.time")
    @patch.object(task_topo_module, "get_running_worker_hash")
    def test_mark_running_twice_with_same_time(self, get_running_worker_hash_mock, time_mock):
        from job_center.main import app

        get_running_worker_hash_mock.return_value = [app.oid]
        task_topo = TaskTopo(self.job)
        time_mock.time.return_value = time.time()
        result1 = task_topo.mark_task_running(self.task_uuid_1)
        result2 = task_topo.mark_task_running(self.task_uuid_1)
        self.assertTrue(result1)
        self.assertTrue(result2)

    @patch.object(task_topo_module, "get_running_worker_hash")
    def test_mark_running_unexpected_task(self, get_running_worker_hash_mock):
        from job_center.main import app

        get_running_worker_hash_mock.return_value = [app.oid]
        task_topo = TaskTopo(self.job)
        task_topo.tasks_dict_map.clear()

        with pytest.raises(KeyError):
            task_topo.mark_task_running(self.task_uuid_1)

    def test_submit_exceed_max_resources(self):
        resources = {}
        for i in range(MAX_JOB_RESOURCES + 1):
            resource_id = str(uuid.uuid4())
            resources[resource_id] = resource_id
        job = {"resources": resources, "job_id": str(uuid.uuid4())}

        with self.assertRaises(JobLeaderError) as context:
            TaskTopo(job)

        self.assertIn(str(MAX_JOB_RESOURCES), context.exception.message)

    def tearDown(self):
        mongodb.jobs.job.remove({"job_id": self.job_id})
        mongodb.jobs.job.remove({"job_id": self.empty_job_id})
