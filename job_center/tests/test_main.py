from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
import pathlib
from unittest import mock
import unittest

from job_center import main
from job_center.common import constants


class TestMain(unittest.TestCase):
    def setUp(self):
        self.persistent_file = pathlib.Path(constants.CELERYBEAT_PERSISTENCE_FILE_DEFAULT_PATH)
        self.backup_file = pathlib.Path(constants.CELERYBEAT_PERSISTENCE_BACKUP_FILE_DEFAULT_PATH)

    def test_check_celery_beat_persistence_file_file_not_exist(self):
        with mock.patch.object(pathlib.Path, "is_file") as isfile_mock, \
                mock.patch.object(pathlib.Path, "unlink") as unlink_mock, \
                mock.patch("shelve.open") as open_mock, \
                mock.patch("logging.exception") as logging_exception_mock:

            isfile_mock.return_value = False
            main.check_celery_beat_persistence_file(self.persistent_file, self.backup_file)

            assert isfile_mock.call_count == 2
            assert open_mock.call_count == 0
            assert logging_exception_mock.call_count == 0
            assert unlink_mock.call_count == 0

    def test_check_celery_beat_persistence_file_corrupted(self):
        with mock.patch.object(pathlib.Path, "is_file") as isfile_mock, \
                mock.patch.object(pathlib.Path, "unlink") as unlink_mock, \
                mock.patch("shelve.open") as open_mock, \
                mock.patch("logging.exception") as logging_exception_mock:
            isfile_mock.return_value = True
            open_mock.return_value = {"key": "value"}
            main.check_celery_beat_persistence_file(self.persistent_file, self.backup_file)

            assert isfile_mock.call_count == 2
            assert open_mock.call_count == 1
            assert logging_exception_mock.call_count == 1
            assert unlink_mock.call_count == 2

    def test_load_job_center_scheduler_plugin_config(self):
        class MockConf:
            def __init__(self):
                self.include = []
                self.beat_schedule = {}

        class MockApp:
            def __init__(self):
                self.conf = MockConf()

            def config_from_object(self, obj):
                self.conf.beat_schedule.update(obj.beat_schedule)

        mock_app = MockApp()
        main.load_job_center_plugin_config(mock_app)

        assert "job_center.handler.leader.workers" in mock_app.conf.include
        assert "job-check" in mock_app.conf.beat_schedule

class TestScheduler(unittest.TestCase):
    @mock.patch("job_center.scheduler_patches.patch_beat_scheduler_merge_inplace")
    @mock.patch("job_center.scheduler_patches.patch_celery_beat_scheduler_tick")
    def test_check_celery_beat_persistence_file_file_not_exist(self, m_patch, m_patch_beat):
        from celery.apps.beat import Beat

        with mock.patch.object(Beat, "run") as m_run:
            runner = CliRunner()
            result = runner.invoke(main.scheduler, [])
            assert result.exit_code == 1

        assert m_patch.mock_calls == [mock.call()]
        assert m_patch_beat.mock_calls == [mock.call()]
        assert m_run.mock_calls == [mock.call()]
