# Copyright (c) 2022, SMARTX
# All rights reserved.

from unittest import mock

from job_center.handler.scheduler import worker


def test_remove_broker_message():
    with mock.patch("job_center.handler.scheduler.worker.mongodb", new_callable=mock.MagicMock) as mongodb_mock:
        job_center_db_mock = mock.Mock()
        remove_mock = mock.Mock(return_value=None)
        job_center_db_mock.messages.remove = remove_mock
        type(mongodb_mock).job_center = mock.PropertyMock(return_value=job_center_db_mock)

        worker._remove_broker_message("task_uuid_x")
        assert remove_mock.called
        assert "queue" not in remove_mock.call_args[0][0]
        assert "payload" in remove_mock.call_args[0][0]

        remove_mock.reset_mock()
        worker._remove_broker_message("task_uuid_x", "queue name")
        assert remove_mock.called
        assert "queue" in remove_mock.call_args[0][0]


def test__mark_job_failed():
    with mock.patch("job_center.handler.scheduler.worker.mongodb", new_callable=mock.MagicMock) as mongodb_mock:
        job_center_db_mock = mock.Mock()
        remove_mock = mock.Mock(return_value=None)
        job_center_db_mock.messages.remove = remove_mock
        type(mongodb_mock).job_center = mock.PropertyMock(return_value=job_center_db_mock)

        jobs_db_mock = mock.Mock()
        update_mock = mock.Mock(return_value=None)
        jobs_db_mock.job.update = update_mock
        type(mongodb_mock).jobs = mock.PropertyMock(return_value=jobs_db_mock)

        job = {
            "job_id": "1234567",
            "queue": "schedule",
            "state": "processing",
            "task_list": [{"uuid": "7654321", "state": "pending"}, {"uuid": "9876543", "state": "pending"}],
        }
        error_msg = "JOB_PROCESSING_TIMEOUT"
        error_code = "JOB_PROCESSING_TIMEOUT"
        worker._mark_job_failed(job, error_msg, error_code)
        assert update_mock.called
