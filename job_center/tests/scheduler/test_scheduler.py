# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
import time
import uuid

from unittest.mock import Mock, PropertyMock, patch  # noqa: I101,I201
import unittest  # noqa: I202

from common.lib.time_utils import utc_now_timestamp
from common.mongo.db import mongodb
from job_center.common.constants import DEFAULT_TASK_HANDLER, JOB_DONE, JOB_FAILED  # noqa: I101,I100,I201
from job_center.config import RESOURCES_DB_NAME  # noqa: I202
from job_center.handler.scheduler.worker import job_check
from job_center.tests import utils
from smartx_app.elf.job_center.constants import KVM_VM, TASK_VM_CREATE


class FakeCursor:
    def __init__(self, jobs):
        self.jobs = jobs
        self.current = 0

    def __iter__(self):
        return self

    def __next__(self):  # Python 3: def __next__(self)  # noqa: A003
        if self.current >= len(self.jobs):
            raise StopIteration
        else:
            self.current += 1
            return self.jobs[self.current - 1]

    def count(self):
        return len(self.jobs)


class TestFollowerBase(unittest.TestCase):
    def setUp(self):
        self.vm_json = {
            "type": KVM_VM,
            "uuid": str(uuid.uuid4()),
            "user": "",
            "vm_name": "test_vm_name",
            "vcpu": 1,
            "memory": 1024,
            "node_ip": "***********",
            "vm_ip": "***********",
            "vnc_port": -1,
            "status": "Creating",
            "token": "",
            "vlan": 123,
            "desc": "test",
            "mac_addr": "",
            "create_date": int(time.time()),
            "boot_path": "",
            "disks": [],
            "bus": "ide",
        }
        self.db = mongodb[RESOURCES_DB_NAME]
        self.job = utils.construct_test_job()
        mongodb.jobs.job.insert(self.job)

    def _set_job(self, job):
        mongodb.jobs.job.update({"job_id": job["job_id"]}, {"$set": job})

    def _get_job(self):
        return mongodb.jobs.job.find_one({"job_id": self.job["job_id"]})

    @patch("job_center.handler.scheduler.worker.mongodb")
    @patch("job_center.handler.leader.workers.leader_worker.apply_async")
    @patch("job_center.handler.scheduler.worker.TaskTopo")
    def test_job_check_job_resubmit(self, task_topo_mock, apply_async_mock, mongodb_mock):
        job, task = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
        job["ctime"] = int(utc_now_timestamp()) - 500
        mongodb_mock.jobs.job.find.return_value = FakeCursor([job])
        mongodb_mock.job_center.messages.find_one.return_value = None
        topo_instance_mock = task_topo_mock.return_value = Mock()
        finish_mock = PropertyMock(return_value=False)
        type(topo_instance_mock).is_finished = finish_mock
        self._set_job(job)
        job_check(self.job["job_id"], task["uuid"], task["reference"])
        self.assertTrue(apply_async_mock.called)
        self.assertFalse(topo_instance_mock.submit_single_task.called)
        self.assertFalse(topo_instance_mock.mark_task_and_downstreams_failed.called)

    @patch("job_center.handler.scheduler.worker.mongodb")
    @patch("job_center.handler.leader.workers.leader_worker.apply_async")
    @patch("job_center.handler.scheduler.worker.TaskTopo")
    def test_job_check_job_resubmit_with_all_finish(self, task_topo_mock, apply_async_mock, mongodb_mock):
        job, task = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
        job["handler"] = "test"
        mongodb_mock.jobs.job.find.return_value = FakeCursor([job])
        mongodb_mock.job_center.messages.find_one.return_value = None
        topo_instance_mock = task_topo_mock.return_value = Mock()
        latest_time_mock = PropertyMock(return_value=int(utc_now_timestamp()) - 500)
        type(topo_instance_mock).latest_update_time = latest_time_mock
        finish_mock = PropertyMock(return_value=True)
        type(topo_instance_mock).is_finished = finish_mock
        self._set_job(job)
        job_check(self.job["job_id"], task["uuid"], task["reference"])
        self.assertTrue(apply_async_mock.called)
        self.assertFalse(topo_instance_mock.submit_single_task.called)
        self.assertFalse(topo_instance_mock.mark_task_and_downstreams_failed.called)

    @patch("job_center.handler.scheduler.worker.mongodb")
    @patch("job_center.handler.leader.workers.leader_worker.apply_async")
    @patch("job_center.handler.scheduler.worker.TaskTopo")
    @patch("job_center.handler.scheduler.base.TaskTopo")
    def test_job_check_job_not_resubmit(self, base_topo_task, task_topo_mock, apply_async_mock, mongodb_mock):
        job, task = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
        job["handler"] = "test"
        mongodb_mock.jobs.job.find.return_value = FakeCursor([job])
        mongodb_mock.job_center.messages.find_one.return_value = None
        topo_instance_mock = task_topo_mock.return_value = Mock()
        latest_time_mock = PropertyMock(return_value=int(utc_now_timestamp()))
        type(topo_instance_mock).latest_update_time = latest_time_mock
        finish_mock = PropertyMock(return_value=False)
        type(topo_instance_mock).is_finished = finish_mock
        topo_instance_mock.tasks_dag.all_roots.return_value = [task["uuid"]]
        base_topo_task.return_value = topo_instance_mock
        self._set_job(job)
        job_check(self.job["job_id"], task["uuid"], task["reference"])
        self.assertFalse(apply_async_mock.called)
        self.assertFalse(topo_instance_mock.submit_single_task.called)
        self.assertFalse(topo_instance_mock.mark_task_and_downstreams_failed.called)

    @patch("job_center.handler.leader.workers.JobQueue")
    @patch("job_center.handler.scheduler.worker.mongodb")
    @patch("job_center.handler.leader.workers.leader_worker.apply_async")
    @patch("job_center.handler.scheduler.worker.TaskTopo")
    @patch("job_center.handler.scheduler.base.TaskTopo")
    def test_job_check_job_not_resubmit_with_queue_not_head(
        self, base_topo_task, task_topo_mock, apply_async_mock, mongodb_mock, job_queue
    ):
        job_queue.in_queue.return_value = True
        job_queue.is_head_of_queue.return_value = False
        job, task = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
        job["handler"] = "test"
        job["queue"] = "test"
        mongodb_mock.jobs.job.find.return_value = FakeCursor([job])
        mongodb_mock.job_center.messages.find_one.return_value = None
        topo_instance_mock = task_topo_mock.return_value = Mock()
        latest_time_mock = PropertyMock(return_value=int(utc_now_timestamp()) - 500)
        type(topo_instance_mock).latest_update_time = latest_time_mock
        finish_mock = PropertyMock(return_value=False)
        type(topo_instance_mock).is_finished = finish_mock
        topo_instance_mock.tasks_dag.all_roots.return_value = [task["uuid"]]
        base_topo_task.return_value = topo_instance_mock
        self._set_job(job)
        job_check(self.job["job_id"], task["uuid"], task["reference"])
        self.assertFalse(apply_async_mock.called)
        self.assertFalse(topo_instance_mock.submit_single_task.called)
        self.assertFalse(topo_instance_mock.mark_task_and_downstreams_failed.called)

    @patch("job_center.handler.scheduler.worker.JobQueue")
    @patch("job_center.handler.scheduler.worker.mongodb")
    @patch("job_center.handler.leader.workers.leader_worker.apply_async")
    @patch("job_center.handler.scheduler.worker.TaskTopo")
    @patch("job_center.handler.scheduler.base.TaskTopo")
    def test_job_check_queue_with_head_done(
        self, base_topo_task, task_topo_mock, apply_async_mock, mongodb_mock, job_queue
    ):
        job_queue.in_queue.return_value = True
        job_queue.is_head_of_queue.side_effect = [True]
        job_queue.get_queue.return_value = {"queue": ["test1", "test2"]}
        job_queue.pull_job_queue.return_value = {"queue": ["test1", "test2"]}
        job, task = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
        job["queue"] = "test"
        mongodb_mock.jobs.job.find.return_value = FakeCursor([job])
        mongodb_mock.job_center.messages.find_one.return_value = None
        mongodb_mock.jobs.job.find_one.return_value = {"state": JOB_DONE}
        mongodb_mock.jobs.queue.find.return_value = [{"key": "test", "queue": ["test1", "test2"]}]
        topo_instance_mock = task_topo_mock.return_value = Mock()
        latest_time_mock = PropertyMock(return_value=int(utc_now_timestamp()) - 500)
        type(topo_instance_mock).latest_update_time = latest_time_mock
        finish_mock = PropertyMock(return_value=False)
        type(topo_instance_mock).is_finished = finish_mock
        topo_instance_mock.tasks_dag.all_roots.return_value = [task["uuid"]]
        base_topo_task.return_value = topo_instance_mock
        self._set_job(job)
        job_check(self.job["job_id"], task["uuid"], task["reference"])
        self.assertTrue(job_queue.pull_job_queue.called)

    @patch("job_center.handler.scheduler.worker.JobQueue")
    @patch("job_center.handler.scheduler.worker.mongodb")
    @patch("job_center.handler.leader.workers.leader_worker.apply_async")
    @patch("job_center.handler.scheduler.worker.TaskTopo")
    @patch("job_center.handler.scheduler.base.TaskTopo")
    def test_job_check_queue_with_head_failed(
        self, base_topo_task, task_topo_mock, apply_async_mock, mongodb_mock, job_queue
    ):
        job_queue.in_queue.return_value = True
        job_queue.is_head_of_queue.side_effect = [True]
        job_queue.get_queue.return_value = {"queue": ["test1", "test2"]}
        job_queue.pull_job_queue.return_value = {"queue": ["test1", "test2"]}
        job, task = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
        job["queue"] = "test"
        mongodb_mock.jobs.job.find.return_value = FakeCursor([job])
        mongodb_mock.job_center.messages.find_one.return_value = None
        mongodb_mock.jobs.job.find_one.return_value = {"state": JOB_FAILED}
        mongodb_mock.jobs.queue.find.return_value = [{"key": "test", "queue": ["test1", "test2"]}]
        topo_instance_mock = task_topo_mock.return_value = Mock()
        latest_time_mock = PropertyMock(return_value=int(utc_now_timestamp()) - 500)
        type(topo_instance_mock).latest_update_time = latest_time_mock
        finish_mock = PropertyMock(return_value=False)
        type(topo_instance_mock).is_finished = finish_mock
        topo_instance_mock.tasks_dag.all_roots.return_value = [task["uuid"]]
        base_topo_task.return_value = topo_instance_mock
        self._set_job(job)
        job_check(self.job["job_id"], task["uuid"], task["reference"])
        self.assertTrue(job_queue.pull_job_queue.called)

    @patch("job_center.handler.scheduler.worker.mongodb")
    @patch("job_center.handler.scheduler.worker.TaskTopo")
    def test_job_check_task_resubmit_with_no_handler(self, task_topo_mock, mongodb_mock):
        job, task = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
        job["handler"] = "test"
        job["ctime"] = int(utc_now_timestamp())
        job["task_list"][0]["time"] = int(utc_now_timestamp()) - 500
        mongodb_mock.jobs.job.find.return_value = FakeCursor([job])
        mongodb_mock.job_center.messages.find_one.return_value = None
        topo_instance_mock = task_topo_mock.return_value = Mock()
        finish_mock = PropertyMock(return_value=False)
        type(topo_instance_mock).is_finished = finish_mock
        topo_instance_mock.tasks_dag.all_roots.return_value = [task["uuid"]]
        self._set_job(job)
        job_check(self.job["job_id"], task["uuid"], task["reference"])
        self.assertTrue(topo_instance_mock.submit_single_task.called)
        self.assertFalse(topo_instance_mock.mark_task_and_downstreams_failed.called)

    @patch("job_center.handler.scheduler.worker.mongodb")
    @patch("job_center.handler.scheduler.worker.TaskTopo")
    @patch("job_center.handler.scheduler.base.TaskTopo")
    def test_job_check_task_resubmit_with_no_handler_no_root(self, base_task_topo_mock, task_topo_mock, mongodb_mock):
        job, task_1 = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
        job, task_2 = utils.add_test_task(job, self.vm_json, TASK_VM_CREATE)

        job["handler"] = "test"
        job["ctime"] = int(utc_now_timestamp())
        job["task_list"][1]["time"] = int(utc_now_timestamp()) - 500
        job["task_topo"][task_1["uuid"]] = [task_2["uuid"]]
        mongodb_mock.jobs.job.find.return_value = FakeCursor([job])
        mongodb_mock.job_center.messages.find_one.return_value = None
        topo_instance_mock = task_topo_mock.return_value = Mock()
        finish_mock = PropertyMock(return_value=False)
        type(topo_instance_mock).is_finished = finish_mock
        topo_instance_mock.tasks_dag.all_roots.return_value = [task_1["uuid"]]
        base_task_topo_mock.return_value = topo_instance_mock
        self._set_job(job)
        job_check(self.job["job_id"], task_1["uuid"], task_1["reference"])
        self.assertFalse(topo_instance_mock.submit_single_task.called)
        self.assertFalse(topo_instance_mock.mark_task_and_downstreams_failed.called)

    @patch("job_center.handler.scheduler.worker.mongodb")
    @patch("job_center.handler.scheduler.worker.TaskTopo")
    def test_job_check_task_resubmit_with_handher(self, task_topo_mock, mongodb_mock):
        job, task = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
        job["handler"] = "test"
        job["ctime"] = int(utc_now_timestamp())
        job["task_list"][0]["handler"] = "test"
        mongodb_mock.jobs.job.find.return_value = FakeCursor([job])
        mongodb_mock.job_center.messages.find_one.return_value = None
        topo_instance_mock = task_topo_mock.return_value = Mock()
        finish_mock = PropertyMock(return_value=False)
        type(topo_instance_mock).is_finished = finish_mock
        topo_instance_mock.tasks_dag.all_roots.return_value = [task["uuid"]]
        self._set_job(job)
        job_check(self.job["job_id"], task["uuid"], task["reference"])
        self.assertTrue(topo_instance_mock.submit_single_task.called)
        self.assertFalse(topo_instance_mock.mark_task_and_downstreams_failed.called)

    @patch("job_center.handler.scheduler.worker.mongodb")
    @patch("job_center.handler.scheduler.worker.TaskTopo")
    def test_job_check_task_not_resubmit_with_default_handler(self, task_topo_mock, mongodb_mock):
        job, task = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
        job["handler"] = "test"
        job["ctime"] = int(utc_now_timestamp())
        job["task_list"][0]["handler"] = DEFAULT_TASK_HANDLER
        mongodb_mock.jobs.job.find.return_value = FakeCursor([job])
        mongodb_mock.job_center.messages.find_one.return_value = None
        topo_instance_mock = task_topo_mock.return_value = Mock()
        finish_mock = PropertyMock(return_value=False)
        type(topo_instance_mock).is_finished = finish_mock
        topo_instance_mock.tasks_dag.all_roots.return_value = [task["uuid"]]
        self._set_job(job)
        job_check(self.job["job_id"], task["uuid"], task["reference"])
        self.assertFalse(topo_instance_mock.submit_single_task.called)

    @patch("job_center.handler.scheduler.worker.mongodb")
    @patch("job_center.handler.scheduler.worker.TaskTopo")
    @patch("job_center.handler.scheduler.worker.utils")
    def test_job_check_task_resubmit_with_queue_alive(self, utils_mock, task_topo_mock, mongodb_mock):
        job, task = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
        job["handler"] = "test"
        job["ctime"] = int(utc_now_timestamp())
        job["task_list"][0]["handler"] = "test_handler"
        job["task_list"][0]["queue"] = "test_queue"
        utils_mock.get_running_worker_ips_from_mongo.return_value = ["test_queue"]
        utils_mock.get_running_worker_hash.return_value = ["test_handler"]
        mongodb_mock.jobs.job.find.return_value = FakeCursor([job])
        mongodb_mock.job_center.messages.find_one.return_value = None
        topo_instance_mock = task_topo_mock.return_value = Mock()
        finish_mock = PropertyMock(return_value=False)
        type(topo_instance_mock).is_finished = finish_mock
        topo_instance_mock.tasks_dag.all_roots.return_value = [task["uuid"]]
        self._set_job(job)
        job_check(self.job["job_id"], task["uuid"], task["reference"])
        self.assertFalse(topo_instance_mock.mark_task_and_downstreams_failed.called)
        self.assertFalse(topo_instance_mock.submit_single_task.called)

    @patch("job_center.handler.scheduler.worker.mongodb")
    @patch("job_center.handler.scheduler.worker.TaskTopo")
    @patch("job_center.handler.scheduler.worker.utils")
    def test_job_check_task_resubmit_with_queue_not_alive(self, utils_mock, task_topo_mock, mongodb_mock):
        job, task = utils.add_test_task(self.job, self.vm_json, TASK_VM_CREATE)
        job["handler"] = "test"
        job["ctime"] = int(utc_now_timestamp())
        job["task_list"][0]["handler"] = "test_handler"
        job["task_list"][0]["queue"] = "test_queue"
        utils_mock.get_running_worker_ips_from_mongo.return_value = []
        utils_mock.get_running_worker_hash.return_value = []
        mongodb_mock.jobs.job.find.return_value = FakeCursor([job])
        mongodb_mock.job_center.messages.find_one.return_value = None
        topo_instance_mock = task_topo_mock.return_value = Mock()
        finish_mock = PropertyMock(return_value=False)
        type(topo_instance_mock).is_finished = finish_mock
        topo_instance_mock.tasks_dag.all_roots.return_value = [task["uuid"]]
        self._set_job(job)
        job_check(self.job["job_id"], task["uuid"], task["reference"])
        self.assertTrue(topo_instance_mock.mark_task_and_downstreams_failed.called)
        self.assertFalse(topo_instance_mock.submit_single_task.called)

    def tearDown(self):
        mongodb.jobs.job.remove({"job_id": self.job["job_id"]})
