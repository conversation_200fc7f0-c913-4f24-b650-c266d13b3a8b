# Copyright (c) 2013-2015, SMARTX
# All rights reserved.

from unittest import mock

from job_center.handler.scheduler.base import ScheduleWorkerBase  # noqa: I100


def test_handle_schedule_process_with_not_exist_job():
    @ScheduleWorkerBase.handle_schedule_process
    def _func(*args, **kwargs):
        pass

    get_job_stub = mock.Mock()
    get_job_stub._get_job.return_value = None

    _func(get_job_stub, "job_id", None, None)
    assert get_job_stub._get_job.called
