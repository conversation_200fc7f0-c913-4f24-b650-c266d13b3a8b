# Copyright (c) 2013-2015, SMARTX
# All rights reserved.


from functools import wraps
import importlib
import time
from unittest.mock import patch
import uuid

from common.lib.time_utils import utc_now_timestamp
from job_center.common.constants import JOB_PROCESSING, JOB_TYPE_ACTION, LIFE_CYCLE_RUN, TASK_PENDING
from job_center.handler.follower import base
from job_center.handler.scheduler import base as scheduler_base


def construct_test_job(job_type=JOB_TYPE_ACTION):
    return {
        "job_id": str(uuid.uuid4()),
        "state": JOB_PROCESSING,
        "life_cycle": LIFE_CYCLE_RUN,
        "description": "test",
        "type": job_type,
        "user": "",
        "resources": {},
        "schedule_task": {},
        "one_time_task": {},
        "task_list": [],
        "task_topo": {},
        "ctime": utc_now_timestamp(),
    }


def construct_test_task(follower_name, reference):
    return {
        "uuid": str(uuid.uuid4()),
        "follower_name": follower_name,
        "reference": reference,  # uuid in details
        "queue": "",
        "state": "pending",  # pending, running, done, failed
        "msg": "",
        "data": "",
        "time": int(time.time()),
    }


def add_test_task(job_json, resource_json, follower_name):
    job_json["resources"][resource_json["uuid"]] = resource_json
    task = construct_test_task(follower_name, resource_json["uuid"])
    job_json["task_list"].append(task)
    job_json["task_topo"][task["uuid"]] = []
    return job_json, task


def add_schedule_test_task(job_json, schedule_task_name):
    job_json["schedule_task"]["name"] = schedule_task_name
    task = construct_test_task(schedule_task_name, schedule_task_name)
    job_json["task_list"].append(task)
    job_json["task_topo"][task["uuid"]] = []
    return job_json, task


def add_one_time_test_task(job_json, reference, one_time_task_name):
    task = {
        "uuid": str(uuid.uuid4()),
        "follower_name": one_time_task_name,
        "reference": reference,  # uuid in details
        "queue": "",
        "state": TASK_PENDING,
        "msg": "",
        "data": "",
        "time": int(time.time()),
    }
    job_json["task_list"].append(task)
    job_json["task_topo"][task["uuid"]] = []
    return job_json, task


class FakeFollowerBase(base.FollowerBase):
    @staticmethod
    def handle_follower_process(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            return func(self, *args, **kwargs)

        return wrapper


class FakeScheduleWorkerBase(scheduler_base.ScheduleWorkerBase):
    @staticmethod
    def handle_schedule_process(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            return func(self, *args, **kwargs)

        return wrapper


def ignore_handle_process(reload_module=None, base_module=None, base_class=None, new_base_class=None):
    def decorator(func):
        def wrapper(*args, **kwargs):
            with patch.object(base_module, base_class, new=new_base_class):
                if reload_module:
                    importlib.reload(reload_module)
                result = func(*args, **kwargs)

            if reload_module:
                importlib.reload(reload_module)
            return result

        return wrapper

    return decorator


def ignore_handle_follower_process(reload_module=None):
    return ignore_handle_process(
        reload_module=reload_module, base_module=base, base_class="FollowerBase", new_base_class=FakeFollowerBase
    )


def ignore_handle_schedule_process(reload_module=None):
    return ignore_handle_process(
        reload_module=reload_module,
        base_module=scheduler_base,
        base_class="ScheduleWorkerBase",
        new_base_class=FakeScheduleWorkerBase,
    )


def follower_caller(func, **kwargs):
    return func.apply(kwargs=kwargs).get()
