# Copyright (c) 2013-2019, SMARTX
# All rights reserved.
import contextlib
import fcntl
import logging
import os
import threading


@contextlib.contextmanager
def nonblocking_lock(lock: threading.Lock):
    locked = lock.acquire(False)
    try:
        yield locked
    finally:
        if locked:
            lock.release()


def close_on_exec(fd):
    flags = fcntl.fcntl(fd, fcntl.F_GETFD)
    flags |= fcntl.FD_CLOEXEC
    fcntl.fcntl(fd, fcntl.F_SETFD, flags)


def set_non_blocking(fd):
    flags = fcntl.fcntl(fd, fcntl.F_GETFL) | os.O_NONBLOCK
    fcntl.fcntl(fd, fcntl.F_SETFL, flags)


def errno_from_exception(e):
    if hasattr(e, "errno"):
        return e.errno
    elif e.args:
        return e.args[0]
    else:
        return None


def patch_pyvmomi_connect():
    """
    https://github.com/vmware/pyvmomi/pull/649/files
    :return:
    """
    import sys  # noqa: F401

    from pyVim import connect
    from pyVmomi.SoapAdapter import SoapStubAdapter

    timeout = 5

    class PatchSoapStubAdapter(SoapStubAdapter):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.schemeArgs["timeout"] = timeout

    connect.SoapStubAdapter = PatchSoapStubAdapter
    return connect


def patch_libvirt():
    from ctypes.util import find_library

    default_lib_path = "/usr/lib64"

    try:
        import greenify

        patch_libs = ["virt", "glib-2.0"]
        greenify.greenify()

        for lib_name in patch_libs:
            path = os.path.join(default_lib_path, find_library(lib_name))
            result = greenify.patch_lib(path)
            logging.info("Is {} patched by greenify? {}".format(path, result))
    except ImportError:
        logging.warning("Can not import greenify")
