# Copyright (c) 2013-2022, SMARTX
# All rights reserved.
import base64
import logging
import os
import urllib.error
import urllib.parse
import urllib.request

from Crypto.Cipher import AES

from common.mongo.constant import MONGO_AES_IV, MONGO_AES_KEY, MONGO_PASSWORD_FILE

SEGMENT_SIZE = 128


def build_mongo_url_prefix():
    url = ""
    username, password = decode_mongo_username_password()
    if username and password:
        url = "mongodb://{}:{}@".format(urllib.parse.quote(username), urllib.parse.quote(password))
    return url


def _unpad_string(value):
    while value[-1] == "\x00":
        value = value[:-1]
    while value[-1] == "%00":
        value = value[:-1]
    return value


def decode_mongo_username_password():
    try:
        mongo_user = mongo_password = ""
        if os.path.exists(MONGO_PASSWORD_FILE):
            with open(MONGO_PASSWORD_FILE) as f:
                mongo_user, mongo_pwd = f.read().strip().split(":")
            aes = AES.new(
                MONGO_AES_KEY.encode("utf8"), AES.MODE_CFB, MONGO_AES_IV.encode("utf8"), segment_size=SEGMENT_SIZE
            )
            encrypted_pwd_bytes = base64.b64decode(mongo_pwd)
            mongo_password = aes.decrypt(encrypted_pwd_bytes)
            mongo_password = _unpad_string(mongo_password.decode())
    except Exception as e:
        logging.warning("Failed to decode mongo username and password: %s", e)
        return "", ""
    return mongo_user, mongo_password


mongo_url_prefix = build_mongo_url_prefix()
