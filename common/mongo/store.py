from common.config.constant import DEFAULT_DB_NAME, SMARTX_KV_STORE


class MongoKVStore:
    def __init__(self, collection):
        self.collection = collection

    def delete(self, key):
        return self.collection.remove({"key": key})

    def set(self, key, value):
        return self.collection.update({"key": key}, {"key": key, "value": value}, upsert=True)

    def get(self, key, get_dict=False):
        result = self.collection.find_one({"key": key}, {"_id": 0, "key": 0})
        if get_dict:
            return result
        return result["value"] if result is not None else ""

    def keys(self, limit=None, offset=0):
        query = self.collection.find({}, {"key": 1, "_id": 0}).skip(offset)
        if limit is not None:
            query.limit(limit)
        return tuple(record["key"] for record in query)


def get_smartx_kv_store():
    """
    :rtype: MongoKVStore
    """
    from common.mongo.db import mongodb

    return MongoKVStore(mongodb[DEFAULT_DB_NAME][SMARTX_KV_STORE])
