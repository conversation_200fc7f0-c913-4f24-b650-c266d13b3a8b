# Copyright (c) 2014, SMARTX
# All rights reserved.
import os

import psutil
from pymongo import MongoClient
from pymongo.read_preferences import ReadPreference

from common.config.constant import (
    MONGO_CONNECT_TIMEOUT,
    MONGO_RETRY_TIMEOUT,
    MONGO_RETRY_TIMEOUT_FOR_JC,
    MONGO_WTIMEOUT,
    REPSET_NAME,
    ZBS_CONFIG_FILE,
)
from common.lib.cfg import Config
from common.mongo import proxy, url_cache

DEFAULT_POOL_SIZE = 2

POOL_SIZE_MAP = {
    "job-center": {"name": "job-center", "argument": "worker", "size": 5, "wait_time": MONGO_RETRY_TIMEOUT_FOR_JC},
    "elf-vm-monitor": {"name": "elf-vm-monitor", "size": 4, "wait_time": 1},
    "elf-tool": {"name": "elf-tool", "argument": "monitor", "size": 1, "wait_time": 1},
}


def DB(
    journal=True,
    read_primary=True,
    write_control="majority",
    socketTimeoutMS=MONGO_CONNECT_TIMEOUT,
    connectTimeoutMS=MONGO_CONNECT_TIMEOUT,
    serverSelectionTimeoutMS=MONGO_CONNECT_TIMEOUT,
    wait_time=MONGO_RETRY_TIMEOUT,
    wTimeoutMS=MONGO_WTIMEOUT,
    pool_size=DEFAULT_POOL_SIZE,
):
    mongo_node_list = Config(ZBS_CONFIG_FILE).get_mongo_members()

    # don't actually do connection at the moment. Let MongoProxy and
    # MongoClient to do the auto reconnection.
    if len(mongo_node_list) > 1:
        client = MongoClient(
            url_cache.mongo_url_prefix + ",".join(mongo_node_list),
            replicaset=REPSET_NAME,
            connect=False,
            read_preference=(ReadPreference.PRIMARY if read_primary else ReadPreference.SECONDARY_PREFERRED),
            maxPoolSize=pool_size,
            socketTimeoutMS=socketTimeoutMS,
            connectTimeoutMS=connectTimeoutMS,
            serverSelectionTimeoutMS=serverSelectionTimeoutMS,
            w=write_control,
            wTimeoutMS=wTimeoutMS,
            journal=journal,
        )
    else:
        client = MongoClient(
            url_cache.mongo_url_prefix + mongo_node_list[0],
            connect=False,
            connectTimeoutMS=connectTimeoutMS,
            socketTimeoutMS=socketTimeoutMS,
            serverSelectionTimeoutMS=serverSelectionTimeoutMS,
            wTimeoutMS=wTimeoutMS,
        )

    conn = proxy.MongoProxy(client, wait_time=wait_time)
    return conn


def db_init_filter():
    pool_size = DEFAULT_POOL_SIZE
    wait_time = MONGO_RETRY_TIMEOUT
    process = psutil.Process(os.getpid())
    process_name = process.name()
    process_cmdline = process.cmdline()
    if process_name in POOL_SIZE_MAP:
        argument = POOL_SIZE_MAP[process_name].get("argument")
        check_argument = True
        if argument and argument not in process_cmdline:
            check_argument = False
        if check_argument:
            process_config = POOL_SIZE_MAP[process_name]
            pool_size = process_config.get("size")
            if "wait_time" in list(process_config.keys()):
                wait_time = process_config.get("wait_time")
    return DB(pool_size=pool_size, wait_time=wait_time)


mongodb = db_init_filter()
