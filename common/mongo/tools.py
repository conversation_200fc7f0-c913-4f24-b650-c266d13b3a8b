# Copyright (c) 2013-2022, SMARTX
# All rights reserved.

import base64
import logging
import os
import random
import string

from Crypto.Cipher import AES

from common.mongo.constant import (
    MONGO_AES_IV,
    MONGO_AES_KEY,
    MONGO_AUTH_KEY_BASE,
    MONG<PERSON>_AUTH_KEYFILE,
    MON<PERSON><PERSON>_DEFAULT_PASSWORD_PREFIX,
)


def generate_mongo_auth_key():
    key = base64.b64encode(MONGO_AUTH_KEY_BASE.encode()).decode()
    return key


def generate_mongo_auth_keyfile():
    try:
        keyfile_content = generate_mongo_auth_key()
        with open(MONGO_AUTH_KEYFILE, "w") as f:
            f.write("%s\n" % str(keyfile_content))
        os.chmod(MONGO_AUTH_KEYFILE, 0o600)
        os.chown(MONGO_AUTH_KEYFILE, 100999, 100999)
        return True
    except Exception as e:
        logging.warning(e)
        return False


def _pad_string(value, block_size=16):
    length = len(value)
    pad_size = block_size - (length % block_size)
    return value.ljust(length + pad_size, "\x00")


def _unpad_string(value):
    while value[-1] == "\x00":
        value = value[:-1]
    while value[-1] == "%00":
        value = value[:-1]
    return value


def encode_mongo_password(password):
    try:
        aes = AES.new(MONGO_AES_KEY.encode("utf-8"), AES.MODE_CFB, MONGO_AES_IV.encode("utf-8"), segment_size=128)
        plaintext = _pad_string(password)
        encrypted_text = aes.encrypt(plaintext.encode("utf-8"))
        encoded_mongo_password = base64.b64encode(encrypted_text)
    except Exception as e:
        logging.exception("encode mongo password failed, error: {}".format(e))
        raise
    return encoded_mongo_password.decode()


def decode_mongo_password(encoded_mongo_password):
    try:
        aes = AES.new(MONGO_AES_KEY.encode("utf-8"), AES.MODE_CFB, MONGO_AES_IV.encode("utf-8"), segment_size=128)
        encrypted_pwd_bytes = base64.b64decode(encoded_mongo_password)
        mongo_password = aes.decrypt(encrypted_pwd_bytes)
        mongo_password = _unpad_string(mongo_password.decode())
    except Exception as e:
        logging.warning("Failed to decode mongo password: %s", e)
        raise
    return mongo_password


def generate_password():
    characters = string.ascii_letters + string.digits + "!#$%&*+-=?@^_"
    password = MONGO_DEFAULT_PASSWORD_PREFIX + "".join(random.choices(characters, k=8))
    return encode_mongo_password(password)
