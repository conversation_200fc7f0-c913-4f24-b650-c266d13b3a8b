# Copyright (c) 2013-2022, SMARTX
# All rights reserved.
import logging
import os
import shutil
from subprocess import getstatusoutput
import time

import yaml

from common.config.constant import ZBS_CONFIG_FILE
from common.lib.cfg import Config

# https://www.mongodb.com/docs/manual/tutorial/resync-replica-set-member/

STATE_PRIMARY = "PRIMARY"
STATE_SECONDARY = "SECONDARY"
STATE_UNKNOWN = "UNKNOWN"

MONGOD_CONF = "/etc/mongod.conf"
MONGOD_DATA_DIR = "/var/lib/mongodb/"


class MongodResyncHelper:
    def __init__(self):
        self.data_ip = self._get_current_data_ip()

    def _get_current_data_ip(self):
        return Config(ZBS_CONFIG_FILE).get("network", "data_ip")

    def _get_mongo_members(self):
        try:
            from common.mongo.db import mongodb

            members = mongodb.conn.admin.command("replSetGetStatus")["members"]
            return list(members)
        except Exception as e:
            logging.error("get mongo members failed: {}".format(e))
            return []

    def pre_check_mongo_status(self):
        primary_exist = False
        other_abnormal = None
        self_state = None
        self_exist = False

        for member in self._get_mongo_members():
            mongo_ip = member["name"].split(":")[0]
            if member["stateStr"] == STATE_PRIMARY:
                primary_exist = True
            if mongo_ip == self.data_ip:
                self_state = member["stateStr"]
                self_exist = True
            elif member["stateStr"] not in [STATE_PRIMARY, STATE_SECONDARY]:
                other_abnormal = member

        if not primary_exist:
            logging.info("pre-check failed: PRIMARY not found")
            return False
        if not self_exist:
            logging.info("pre-check failed: current node is not a member of mongo cluster")
            return False
        if self_state == STATE_PRIMARY:
            logging.info("pre-check failed: current node is PRIMARY, please switch PRIMARY to another node")
            return False
        if other_abnormal:
            name, state = other_abnormal["name"], other_abnormal["stateStr"]
            logging.info("pre-check failed: exist another abnormal member {}: {}".format(name, state))
            return False

        return True

    def _stop_mongod_service(self):
        logging.info("stopping mongod.service ...")

        cmd = "systemctl status mongod"
        status_res, output = getstatusoutput(cmd)
        logging.info("cmd: {} res: {} output: {}".format(cmd, status_res, output))

        mongod_stopped = False
        for _ in range(3):
            cmd = "systemctl stop mongod"
            logging.info(cmd)
            res, output = getstatusoutput(cmd)
            logging.info("cmd: {} res: {} output: {}".format(cmd, res, output))
            mongod_stopped = bool(res == 0)
            if mongod_stopped:
                break
            time.sleep(1)

        cmd = "systemctl status mongod"
        status_res, output = getstatusoutput(cmd)
        logging.info("cmd: {} res: {} output: {}".format(cmd, status_res, output))

        return mongod_stopped

    def _restart_mongod_service(self):
        logging.info("restarting mongod.service ...")

        cmd = "systemctl status mongod"
        status_res, output = getstatusoutput(cmd)
        logging.info("cmd: {} res: {} output: {}".format(cmd, status_res, output))

        mongod_started = False
        for _ in range(3):
            cmd = "systemctl restart mongod"
            logging.info(cmd)
            res, output = getstatusoutput(cmd)
            logging.info("cmd: {} res: {} output: {}".format(cmd, res, output))
            mongod_started = bool(res == 0)
            if mongod_started:
                break
            time.sleep(1)

        cmd = "systemctl status mongod"
        status_res, output = getstatusoutput(cmd)
        logging.info("cmd: {} res: {} output: {}".format(cmd, status_res, output))

        return mongod_started

    def _get_keyfile_path(self):
        keyfile_path = ""
        if os.path.exists(MONGOD_CONF):
            with open(MONGOD_CONF) as f:
                mongod_conf = yaml.load(f, Loader=yaml.Loader)
                keyfile_path = mongod_conf.get("security", {}).get("keyFile", "")
        return keyfile_path

    def _clean_var_lib_mongodb(self):
        logging.info("cleaning {} ...".format(MONGOD_DATA_DIR))
        data_files = os.listdir(MONGOD_DATA_DIR)
        if not data_files:
            logging.info("clean data files: no date files under {}".format(MONGOD_DATA_DIR))
            return True

        keyfile_path = self._get_keyfile_path()
        for item in data_files:
            full_path = os.path.join(MONGOD_DATA_DIR, item)
            if full_path == keyfile_path:
                logging.info("clean data files: skip {}".format(full_path))
                continue
            logging.info("clean data files: {}".format(full_path))
            if os.path.isdir(full_path):
                shutil.rmtree(full_path)
            else:
                os.remove(full_path)
        return True

    def _get_local_mongo_state(self):
        for item in self._get_mongo_members():
            mongo_ip = item["name"].split(":")[0]
            if mongo_ip == self.data_ip:
                return item["stateStr"]
        return STATE_UNKNOWN

    def _loop_check_state(self):
        for r in range(60):
            logging.info("check mongo rs.status(), round {}".format(r))
            state = self._get_local_mongo_state()
            if state in [STATE_PRIMARY, STATE_SECONDARY]:
                logging.info("mongo state become healthy: {}".format(state))
                return True
            else:
                logging.info("wait for mongo recovery: {}".format(state))
                time.sleep(10)
        return False

    def try_rescue_mongod(self):
        logging.info("starting rescue mongod ...")
        if not self._stop_mongod_service():
            logging.info("stop mongod.service failed, no further step would be token.")
            return False

        if not self._clean_var_lib_mongodb():
            logging.info("failed to clean mongo data dir.")
            return False
        if not self._restart_mongod_service():
            logging.info("failed to restart mongod.service")
            return False
        if not self._loop_check_state():
            logging.info("wait mongo recovery timeout, please check mongo cluster status.")
            return False
        return True
