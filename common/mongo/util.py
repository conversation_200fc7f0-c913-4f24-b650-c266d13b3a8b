# Copyright (c) 2014, SMARTX
# All rights reserved.
import logging
import random
from time import sleep
import urllib.error
import urllib.parse
import urllib.request

from pymongo import MongoClient, ReadPreference
from pymongo.errors import AutoReconnect, OperationFailure

from common.config.constant import ZBS_CONFIG_FILE
from common.lib.cfg import Config
from common.mongo import url_cache
from common.mongo.constant import MONGO_DEFAULT_ADMIN
from common.mongo.db import mongodb

HIGH_PRIORITY = 2
MEDIUM_PRIORITY = 1
LOW_PRIORITY = 0
MONGO_PORT = 27017


def mongo_change_primary(force=False):
    # replSetStepDown make the current primary step down and elect a new primary
    # 60 means the stepdown node cannot be elected as primary within 60s

    # force: A boolean that determines whether the primary steps down if no electable
    # and up-to-date secondary exists within the wait period. If true, the primary steps
    # down even if no suitable secondary member exists; this could lead to rollbacks if
    # a secondary with replication lag becomes the new primary.
    try:
        mongodb.conn.admin.command({"replSetStepDown": 60, "force": force})
    except AutoReconnect:
        # Since the AutoReconnect error does not affect the primary stepdown, it can be ignored
        pass


def mongo_create_user(root_username, root_password):
    try:
        mongo_client = MongoClient("localhost:27017")
        mongo_client.admin.add_user(
            MONGO_DEFAULT_ADMIN, root_password, roles=[{"role": "userAdminAnyDatabase", "db": "admin"}]
        )
        mongo_client.admin.authenticate(MONGO_DEFAULT_ADMIN, root_password)
        mongo_client.admin.add_user(root_username, root_password, roles=[{"role": "root", "db": "admin"}])

        return True
    except Exception as e:
        logging.exception("create mongo user failed: {}".format(e))
        return False


def is_mongo_with_user(username, password, node_list=None):
    if not node_list:
        node_list = Config(ZBS_CONFIG_FILE).get("cluster", "mongo").split(",")
    try:
        url = ""
        if username and password:
            url = "mongodb://{}:{}@".format(urllib.parse.quote_plus(username), urllib.parse.quote_plus(password))
        c = MongoClient(url + random.choice(node_list), read_preference=ReadPreference.SECONDARY)
        res = c.admin.command("usersInfo")
        if len(res.get("users", [])) > 1:
            return True
    except OperationFailure as e:
        if "requires authentication" in str(e):
            return False
    return False


def mongo_create_replset(node_list=None, priority_0_member=None):
    """Create a replica set for mongo node
    node_list = ['*************', '*************', '*************']
    """
    if not node_list:
        node_list = Config(ZBS_CONFIG_FILE).get("cluster", "mongo").split(",")

    c = MongoClient("localhost:27017")
    cfg = {
        "_id": "zbs",
        "members": [
            {"_id": index, "host": "%s" % v, "priority": LOW_PRIORITY if v == priority_0_member else MEDIUM_PRIORITY}
            for index, v in enumerate(node_list)
        ],
    }
    return c.admin.command({"replSetInitiate": cfg})


def mongo_reconfig_replset(node_list=None, priority_0_member=None):
    """Reconfig a replica set for mongo node
    node_list = ['*************', '*************', '*************']
    """
    if not node_list:
        node_list = Config(ZBS_CONFIG_FILE).get("cluster", "mongo").split(",")

    c = MongoClient("localhost:27017")
    db = c.local
    exist_conf = db.system.replset.find_one()
    if not exist_conf:
        raise Exception("Mongo replica set has not be initiated")

    version = exist_conf["version"] + 1
    cfg = {
        "_id": "zbs",
        "version": version,
        "members": [
            {
                "_id": member["_id"],
                "host": "%s" % member["host"],
                "priority": LOW_PRIORITY if member["host"] == priority_0_member else MEDIUM_PRIORITY,
            }
            for member in exist_conf["members"]
        ],
    }

    return c.admin.command({"replSetReconfig": cfg, "force": True})


def mongo_reconfig_priority(node_list=None, high_priority_member=None, low_priority_member=None):
    if not node_list:
        node_list = Config(ZBS_CONFIG_FILE).get("cluster", "mongo").split(",")

    if high_priority_member not in node_list:
        raise Exception("high priority member not in mongo list")

    if low_priority_member not in node_list:
        raise Exception("low priority member not in mongo list")

    primary_member = mongo_leader()

    c = MongoClient(url_cache.mongo_url_prefix + primary_member, read_preference=ReadPreference.SECONDARY)
    db = c.local
    exist_conf = db.system.replset.find_one()

    if not exist_conf:
        raise Exception("Mongo replica set has not be initiated")

    version = exist_conf["version"] + 1
    conf = exist_conf
    conf["version"] = version
    members = conf["members"]
    exist_low_priority_member = None
    exist_high_priority_member = None
    for member in members:
        if member.get("priority") == LOW_PRIORITY:
            exist_low_priority_member = member["host"]
        elif member.get("priority") == HIGH_PRIORITY:
            exist_high_priority_member = member["host"]

    if high_priority_member == exist_low_priority_member:
        raise Exception("High priority member can't be exist_low_priority_member")

    if high_priority_member == exist_high_priority_member and exist_low_priority_member == low_priority_member:
        return

    for member in conf["members"]:
        if member["host"] == low_priority_member:
            member["priority"] = LOW_PRIORITY
        elif member["host"] == high_priority_member:
            member["priority"] = HIGH_PRIORITY
        else:
            member["priority"] = MEDIUM_PRIORITY
    return c.admin.command({"replSetReconfig": conf})


def member_action(data_ip=None, action=None):
    if not data_ip:
        raise Exception("Need data ip when change mongo replica set.")

    actions = ["add", "remove"]
    if action not in actions:
        raise Exception("Action need in %s" % actions)

    primary_member = mongo_leader()
    primary_ip = primary_member.split(":")[0]
    local_data_ip = Config(ZBS_CONFIG_FILE).get_local_data_ip()[0]
    if primary_ip != local_data_ip:
        raise Exception("Please execute cmd at primary node: %s" % primary_ip)

    member_info = data_ip + ":" + str(MONGO_PORT)
    client = MongoClient(url_cache.mongo_url_prefix + primary_member, read_preference=ReadPreference.SECONDARY)
    for _ in range(5):  # retry
        exist_conf = client.local.system.replset.find_one()
        if not exist_conf:
            raise Exception("Mongo replica set has not be initiated")

        member_exist = [item for item in exist_conf["members"] if item["host"] == member_info]
        if action == "add":
            if member_exist:
                logging.info("add member: mongo member {} already exist".format(member_exist))
                if "newlyAdded" in member_exist[0]:
                    # Ref: https://github.com/mongodb/mongo/commit/f95ee0caa794#diff-af8c1966ff9524390671a9c8368756d30732bf6d84b7c18a6883e770bd18a7dcR1534-R1538
                    # Omit 'newlyAdded' field if it exists in the config.
                    member_exist[0].pop("newlyAdded")
                    logging.info("add member: mongo member {} has `newlyAdded` field, remove it".format(member_exist))
            else:
                max_member_id = max(member["_id"] for member in exist_conf["members"])
                exist_conf["members"].append({"_id": max_member_id + 1, "host": member_info})
        elif action == "remove":
            if member_exist:
                exist_conf["members"] = [item for item in exist_conf["members"] if item["host"] != member_info]
            else:
                logging.info("remove member: mongo member {} not found in replset".format(member_exist))

        exist_conf["version"] += 1  # update config version
        res = client.admin.command({"replSetReconfig": exist_conf})
        if res.get("ok"):
            break
        else:
            logging.warning("member {} replSetReconfig failed: {}".format(action, res))
            sleep(2)

    sleep(90)
    res = False
    try:
        if action == "add":
            if check_member_status(data_ip=data_ip):
                res = True
        elif action == "remove":
            status = mongo_status()
            members = [member["name"] for member in status["members"]]
            if member_info not in members:
                res = True
    except Exception as e:
        logging.warning(e)
    finally:
        return res


def member_add(data_ip=None):
    return member_action(data_ip=data_ip, action="add")


def member_remove(data_ip=None):
    return member_action(data_ip=data_ip, action="remove")


def check_member_status(data_ip=None):
    if not data_ip:
        raise Exception("Need member data ip.")
    MAX_RETRY = 6
    WAIT_TIME = 5
    retry = 0
    result = False
    check_info = data_ip + ":" + str(MONGO_PORT)
    while retry < MAX_RETRY:
        status = mongo_status()
        for member in status["members"]:
            if member.get("name") == check_info:
                if member.get("stateStr") in ["PRIMARY", "SECONDARY"]:
                    result = True
        if result:
            return result
        else:
            retry += 1
            sleep(WAIT_TIME)
    return result


def mongo_status():
    return mongodb.conn.admin.command("replSetGetStatus")


def mongo_list():
    for v in mongodb.conn.admin.command("replSetGetStatus")["members"]:
        print(v["name"])


def mongo_leader():
    for member in mongodb.conn.admin.command("replSetGetStatus")["members"]:
        if member["stateStr"] == "PRIMARY":
            return member["name"]


def mongo_low_priority():
    try:
        node_list = Config(ZBS_CONFIG_FILE).get("cluster", "mongo").split(",")
        c = MongoClient(url_cache.mongo_url_prefix + random.choice(node_list), read_preference=ReadPreference.SECONDARY)
        db = c.local
        exist_conf = db.system.replset.find_one()

        for member in exist_conf["members"]:
            priority = member.get("priority")
            if priority is not None:
                if priority == LOW_PRIORITY:
                    return member["host"]
    except Exception:
        return None
