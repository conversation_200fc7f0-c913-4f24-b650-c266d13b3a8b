# Copyright (c) 2023, SMARTX
# All rights reserved.
import logging

from gevent.lock import Semaphore

from zbs.lib.rpc import channel
from zbs.lib.zbs_socket import SocketFactory


class PatchedSocketRpcChannel(channel.SocketRpcChannel):
    def __init__(self, host, port, socket_factory=SocketFactory(), socket_timeout=None):
        super().__init__(host, port, socket_factory, socket_timeout)
        self._lock = Semaphore()
        logging.debug(f"patched SocketRpcChannel init: {self} ")

    def CallMethod(self, method, controller, request, response_class, done):
        with self._lock:
            logging.debug(f"channel {self} locked")
            result = super().CallMethod(method, controller, request, response_class, done)
        logging.debug(f"channel {self} unlocked")
        return result


def patch_all():
    channel.SocketRpcChannel = PatchedSocketRpcChannel
    logging.debug(f"{__file__}: patch_all called")
