from abc import ABCMeta, abstractmethod
import logging
import re
import threading

from kazoo.client import KazooC<PERSON>, KazooState
from kazoo.recipe import lock

from common.election.election_info import ElectionNode
from common.election.listener import Listener
from common.election.state import State
from common.utils import nonblocking_lock


class Election(metaclass=ABCMeta):
    @abstractmethod
    def start(self):
        pass

    """ start the election and watch the leader change event
    """

    @abstractmethod
    def is_leader(self) -> bool:
        pass

    """ return whether the current node is the leader
    """

    @abstractmethod
    def leader_id(self) -> str:
        pass

    """ return the leader node id
    """

    @abstractmethod
    def get_election_info(self) -> list[ElectionNode]:
        pass

    """ return all election node info
        node info is sorted by sequence number order by asc
    """

    @abstractmethod
    def re_election(self):
        pass

    """ if release() is called, the leader will be released, but the leader change event are still be watched
        you can call re_election() to reacquire the leader
    """

    @abstractmethod
    def release(self) -> bool:
        pass

    """ release the current node leader role
    """

    @abstractmethod
    def close(self):
        pass

    """ close the election
    """


class ZKElection(Election):
    def __init__(self, zk: KazooClient, path: str, node_id: str, listener: Listener = None):
        self._thread_lock: threading.Lock = threading.Lock()
        self._thread: threading.Thread = None
        self._lock: lock.Lock = None
        self._lock_path: str = ""

        self._state: State = State.NotStarted
        self._leader_path: str = ""
        self._leader_data: str = ""

        self._released: bool = False
        self._closed: bool = False

        self.zk = zk
        self.prefix = path
        self.data = node_id
        self.listener = listener

    def start(self):
        self.zk.add_listener(self._kazoo_connection_callback)
        self._released = False
        self.zk.ensure_path(self.prefix)

        self.zk.ChildrenWatch(path=self.prefix, func=self._watch_children_callback, send_event=True)

    def re_election(self):
        self._released = False
        self._re_election()

    def _re_election(self):
        if self._released or self._closed:
            return

        # previous election thread still running, skip this election
        if self._thread and self._thread.is_alive():
            logging.info("election thread is alive, skip this election")
            return

        self._thread = threading.Thread(target=self._run_election, daemon=True)
        self._thread.start()

    def is_leader(self) -> bool:
        return self._state == State.Leader

    def leader_id(self) -> str:
        return self._leader_data

    def get_election_info(self) -> list[ElectionNode]:
        election_info = []

        for child in self._get_children():
            data, _ = self.zk.get(child)
            info = ElectionNode(fullpath=child, sequence=int(child.split("__lock__")[1]), node_id=data.decode("utf-8"))
            election_info.append(info)

        election_info.sort(key=lambda x: x.sequence)
        return election_info

    def release(self) -> bool:
        self._released = True
        if self._lock:
            logging.info(f"release election [{self.prefix}]")
            return self._lock.release()

    def close(self):
        logging.info(f"close election [{self.prefix}]")

        self._closed = True

        if self._lock:
            self._lock.release()
            self._lock.cancel()

        if self._thread:
            self._thread.join()

        self.zk.stop()
        self.zk.close()

    def _run_election(self):
        if self._released or self._closed:
            return

        with nonblocking_lock(self._thread_lock) as locked:
            if not locked:
                logging.info("election thread is locked by other thread, skip this election")
                return

            try:
                for node in self.get_election_info():
                    if node.node_id == self.data:
                        logging.info(f"current node already created path [{node.fullpath}], skip this election")
                        return

                self._lock = self.zk.Lock(path=self.prefix, identifier=self.data)
                logging.info(f"acquire election [{self._lock.prefix}]")

                if self._lock.acquire(blocking=True):
                    self._lock_path = self._find_node(self._lock.prefix)
                    logging.info(f"acquire lock [{self._lock_path}] success")
                    return
                else:
                    logging.info(f"acquire lock [{self._lock.prefix}] failed")
            except lock.CancelledError:
                logging.info(f"acquire lock [{self.prefix}] canceled")
                return
            except Exception as e:
                logging.exception(f"acquire lock error: {e}")
                return

    def call_listener(self, watch_leader: str):
        if self.listener is None:
            return

        # prevent the listener callback from being called after the election is released
        if watch_leader == "":
            if self._state == State.Leader:
                self.listener.loss_leader()
            return

        match self._state:
            case State.Leader:
                if watch_leader != self._lock_path:
                    self.listener.loss_leader()
            case State.Follower | State.NotStarted | State.ConnectionLost:
                if watch_leader != self._lock_path:
                    self.listener.leader_change()
                else:
                    self.listener.become_leader()
            case _:
                logging.error(f"unknown state [{self._state}]")

    def _update_status(self, leader_path: str, leader_data: str):
        logging.info(
            f"current node election path [{self._lock_path}], status [{self._state}] "
            f"update leader_path: [{leader_path}], leader_data: [{leader_data}]"
        )
        if leader_path == "":
            self._state = State.Follower
        else:
            self._state = State.Leader if leader_path == self._lock_path else State.Follower
        self._leader_path = leader_path
        self._leader_data = leader_data

    def _watch_children_callback(self, children: list, event):
        logging.info(f"received [{children}] children change event {event}")
        if self._closed:
            return

        # listener callback may call before election event,
        # so we need to check the election path again
        if self._lock is None:
            logging.info(f"election is None, try to start election [{self.prefix}]")
            self._re_election()
        else:
            # if the election path is empty, and the current node is the leader,
            # it means that the current node has lost the leader role.
            # so we need to re-election
            self._lock_path = self._find_node(self._lock.prefix)
            if self._lock_path == "" and self._state in (State.ConnectionLost, State.Leader):
                logging.info(f"lock path lost, try to re-election [{self.prefix}]")
                self._re_election()

        if not children:
            logging.info("watch children is empty, waiting for next event...")
            self.call_listener("")
            self._update_status("", "")
            return

        application_nodes = []
        for child in children:
            application_nodes.append(
                {
                    "fullpath": f"{self.prefix}/{child}",
                    "node": child,
                    "sequence": child.split("__lock__")[1],
                }
            )
        logging.info(f"all children node info: {application_nodes}")

        watch_leader = min(application_nodes, key=lambda x: x["sequence"])["fullpath"]

        # ignore the event if the leader is not changed
        # this may happen when some other node is deleted or added
        if self._leader_path != "" and self._leader_path == watch_leader:
            logging.info("leader not change, ignore")
            return

        self.call_listener(watch_leader)
        data, stat = self.zk.get(watch_leader)
        self._update_status(watch_leader, data.decode("utf-8"))

    # this function copied from kazoo
    def _get_children(self) -> list:
        # Node name, after the contender UUID, before the sequence
        # number. Involved in read/write locks.
        _NODE_NAME = "__lock__"

        if not self.zk.exists(self.prefix):
            return []

        try:
            children = self.zk.get_children(self.prefix)
        except Exception as e:
            logging.exception(f"get_children error: {e}")
            return []
        # We want all contenders, including self (this is especially important
        # for r/w locks). This is similar to the logic of `_get_predecessor`
        # except we include our own pattern.
        all_contenders_re = re.compile(r"(?:{patterns})(-?\d{{10}})$".format(patterns="|".join(_NODE_NAME)))
        # Filter out the contenders using the computed regex
        contender_matches = []
        for child in children:
            match = all_contenders_re.search(child)
            if match is not None:
                contender_matches.append(match)

        # Sort the contenders using the sequence number extracted by the regex,
        # then extract the original string.
        return [f"{self.prefix}/{match.string}" for match in sorted(contender_matches, key=lambda m: m.groups())]

    # kazoo election and lock module did not provide a way to get the current lock path,
    # so we have to find it by the locker uuid
    def _find_node(self, uuid: str) -> str:
        children = self.zk.get_children(self.prefix)
        for child in children:
            if child.startswith(uuid):
                return f"{self.prefix}/{child}"
        return ""

    # this function will be called when the zookeeper connection state changed
    # note that this function will be called kazoo client thread and will block the kazoo client thread,
    # so we should not do any blocking operation in this function
    def _kazoo_connection_callback(self, state: KazooState):
        match state:
            case KazooState.LOST | KazooState.SUSPENDED:
                logging.info(f"zookeeper connection state changed to [{state}], try to update leader status to null")
                self._update_status("", "")
                self._state = State.ConnectionLost
            case KazooState.CONNECTED:
                self.zk.handler.spawn(self._trigger_watch_event)

    def _trigger_watch_event(self):
        try:
            children = self.zk.get_children(self.prefix)
            self._watch_children_callback(children, None)
        except Exception as e:
            logging.exception(f"trigger watch event error: {e}")
