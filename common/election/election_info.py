from dataclasses import dataclass
from enum import Enum, unique
import json
import logging
from typing import Any

from kazoo.client import KazooClient
import kazoo.exceptions


@unique
class _election_type(Enum):
    PYTHON = 0
    GO = 1


@dataclass
class _election_service:
    path: str
    name: str
    category: _election_type


_election_services: list[_election_service] = [
    _election_service(path="/smartx/ntpm/leader", name="ntpm", category=_election_type.GO),
    _election_service(path="/monitoring/octopus/leader", name="octopus", category=_election_type.GO),
    _election_service(path="/monitoring/siren/leader", name="siren", category=_election_type.GO),
    _election_service(path="/zos/service/timemachine", name="timemachine", category=_election_type.GO),
    _election_service(path="/aquarium/leader", name="aquarium", category=_election_type.GO),
    _election_service(path="/elf/monitor/leader", name="elf-vm-monitor", category=_election_type.PYTHON),
    # smartx_app/elf/exporter/service_role.py
    _election_service(path="/elf/exporter/leader", name="elf-exporter", category=_election_type.PYTHON),
    # smartx_app/elf/scheduler/service_role.py
    _election_service(path="/elf/scheduler/leader", name="elf-vm-scheduler", category=_election_type.PYTHON),
    _election_service(path="/elf/elf-fs/leader", name="elf-fs", category=_election_type.GO),
]

# service_list is used to check input
service_list = [i.name for i in _election_services]


@dataclass
class ElectionNode:
    def __init__(self, fullpath: str, sequence: int, node_id: str = "", priority: int = 0):
        # fullpath is the full path of the node
        self.fullpath: str = fullpath
        # sequence is the sequence number of the node
        self.sequence: int = sequence
        # node_id is the id of the node
        self.node_id: str = node_id
        # priority is the priority of the node, in PYTHON, it's always 0
        self.priority: int = priority

    def __repr__(self):
        return f"<fullpath:{self.fullpath} sequence:{self.sequence} node_id:{self.node_id} priority:{self.priority}>"

    def from_dict(self, obj: Any):
        assert isinstance(obj, dict)
        self.node_id = obj.get("node_id")
        self.priority = obj.get("priority")


class ElectionServiceState:
    def __init__(self, service_name: str, service_path: str, members: list[ElectionNode]):
        self.service_name: str = service_name
        self.service_path: str = service_path
        self.members: list[ElectionNode] = members


def _get_golang_service_node_info(zk: KazooClient, prefix: str, child: str) -> ElectionNode:
    fullpath = f"{prefix}/{child}"

    data, stat = zk.get(fullpath)

    str_list = child.split("-")
    sequence = int(str_list[len(str_list) - 1])
    e = ElectionNode(fullpath=fullpath, sequence=sequence)
    e.from_dict(json.loads(data))
    return e


def _get_golang_service_info(zk: KazooClient, service: _election_service) -> list[ElectionNode]:
    member_list = list[ElectionNode]()

    try:
        children = zk.get_children(service.path)
    except kazoo.exceptions.NoNodeError:
        return member_list
    except Exception as e:
        logging.error(f"service {service.name} get election info failed: {e!s}")
        return member_list

    for child in children:
        if child == "elected":
            continue
        member_list.append(_get_golang_service_node_info(zk, service.path, child))

    member_list.sort(key=lambda x: x.sequence)

    return member_list


def get_all_service_info(zk: KazooClient, services: list[str]) -> list[ElectionServiceState]:
    check_servers_input(services)

    service_info = list[ElectionServiceState]()
    for service in _get_wanted_services(services):
        match service.category:
            case _election_type.GO:
                member_list = _get_golang_service_info(zk, service)
            case _election_type.PYTHON:
                from common.election.zk_election import ZKElection

                try:
                    election = ZKElection(zk, service.path, "", None)
                    member_list = election.get_election_info()
                except Exception as e:
                    logging.error(f"service {service.name} get election info failed: {e!s}")
                    continue
            case _:
                raise ValueError(f"unknown election category {service.category}")

        if not member_list or len(member_list) == 0:
            logging.warning(f"service [{service.name}] information not found")
            continue

        service_info.append(
            ElectionServiceState(
                service_name=service.name,
                service_path=service.path,
                members=member_list,
            )
        )

    return service_info


def check_servers_input(services: list[str]):
    if not isinstance(services, list):
        raise TypeError("servers must be string list")

    for service in services:
        if not isinstance(service, str):
            raise TypeError("server argument must be string")

        if service not in service_list:
            raise ValueError(f"server argument must be in {service_list}")


def _get_wanted_services(services: list[str]) -> list[_election_service]:
    res = list[_election_service]()

    for service in services:
        for i in _election_services:
            if i.name == service:
                res.append(i)
                break

    return res
