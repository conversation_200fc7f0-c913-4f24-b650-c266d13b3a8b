from enum import Enum


# State is an enum that represents the state of the election
class State(Enum):
    # NotStarted is the initial state of the election
    NotStarted = 0
    # Leader is the state when the current node is the leader
    Leader = 1
    # Follower is the state when the current node is a follower
    Follower = 2
    # ConnectionLost is the state when the current node lost connection to the zookeeper
    ConnectionLost = 3
