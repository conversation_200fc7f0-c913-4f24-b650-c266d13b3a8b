# Copyright (c) 2013-2023, SMARTX
# All rights reserved.

from common.lib.cfg import Config

FILE_MODE = 0o755

DEFAULT_DB_NAME = "smartx"
SMARTX_KV_STORE = "kv_store"

SUCCEED = "Succeed"
FAIL = "Fail"

# mongo
MONGO_PORT = 27017
REPSET_NAME = "zbs"

MONGO_WTIMEOUT = 10000  # ms
MONGO_CONNECT_TIMEOUT = 10000  # ms
MONGO_RETRY_TIMEOUT = 5  # s
MONGO_RETRY_TIMEOUT_FOR_JC = 12 * 3600  # s

ZBS_CONFIG_FILE = "/etc/zbs/zbs.conf"


# config file network ips name
DATA_IP = "data_ip"

# use tuna-exporter as default service username
DEFAULT_SERVICE_USERNAME = "tuna-exporter"
DEFAULT_SERVICE_PASSWORD = "!QAZ2wsx"


AUTH_TOKEN_NAME = "X-SmartX-Token"
AUTH_TOKEN_NAME_NEW = "Http-Metadata-Token"

NO_LEADER_SERVICE_NAMES = ["chunk"]

NODE_ROLE_MASTER = "master"
NODE_ROLE_STORAGE = "storage"

PRODUCT_VENDOR_SMARTX = "SmartX"
PRODUCT_VENDOR_ARCFRA = "Arcfra"

try:
    cfg = Config(ZBS_CONFIG_FILE)
    # from os 6.1.0, use oem_storage_name replaced by product_storage_name
    STORAGE_NAME = (
        cfg.get("cluster", "oem_storage_name", fallback=None)
        or cfg.get("cluster", "product_storage_name", fallback=None)
        or "zbs"
    )
    PRODUCT_VENDOR = cfg.get("cluster", "product_vendor", fallback=None) or PRODUCT_VENDOR_SMARTX
except Exception:
    STORAGE_NAME = "zbs"
    PRODUCT_VENDOR = PRODUCT_VENDOR_SMARTX

# zbs storage
DEFAULT_ZBS_VOLUME_TEMPLATE_POOL_NAME = "nfs-volume-template"
DEFAULT_ZBS_VOLUME_POOL_NAME = STORAGE_NAME + "-volumes"
DEFAULT_ZBS_IMAGE_POOL_NAME = STORAGE_NAME + "-images"
DEFAULT_ZBS_ISCSI_TARGET_PREFIX = STORAGE_NAME + "-iscsi"
DEFAULT_ZBS_ISCSI_ISO_TARGET_PREFIX = STORAGE_NAME + "-iscsi-images"

# volume state, should be same as zbs-rest and elf
VOL_CREATED = "created"
VOL_DELETED = "deleted"
