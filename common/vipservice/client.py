# Copyright (c) 2013-2023, SMARTX
# All rights reserved.

import requests

from common.http.restful_client import request_with_session

VIPSERVICE_IP = "127.0.0.1"
VIPSERVICE_PORT = 8777


class Client:
    session = requests.Session()

    def __init__(self):
        self.server_ip = VIPSERVICE_IP
        self.server_port = VIPSERVICE_PORT
        self.server_schema = "http"
        self.req_timeout = 5
        self.api_version = 1

    def request(self, method, url, **kwargs):
        return request_with_session(self.session, True, self.req_timeout, method, url, **kwargs)

    def gen_request_url(self):
        return "{schema}://{ip}:{port}/api/{version}/services".format(
            schema=self.server_schema,
            ip=self.server_ip,
            port=self.server_port,
            version="v" + str(self.api_version),
        )

    def show_vip(self):
        resp = self.request("get", self.gen_request_url(), timeout=self.req_timeout)
        return resp["data"]

    def set_vip(self, service_name, vip):
        self.request("put", self.gen_request_url(), json={"name": service_name, "vip": vip}, timeout=self.req_timeout)

    def delete_vip(self, service_name):
        req_url = self.gen_request_url() + "/" + service_name
        self.request("delete", req_url, timeout=self.req_timeout)

    def get_vip_leader_ip(self, service_name):
        req_url = self.gen_request_url() + "/" + service_name + "/leader"
        resp = self.request("get", req_url, timeout=self.req_timeout)
        vip_leader_ip = ""
        if resp and resp.get("data") and "/" in str(resp["data"]):
            _, vip_leader_ip = resp["data"].split("/")
        return vip_leader_ip

    def drain_vip(self, service_name):
        req_url = self.gen_request_url() + "/" + service_name + "/drain"
        self.request("post", req_url, timeout=self.req_timeout)
