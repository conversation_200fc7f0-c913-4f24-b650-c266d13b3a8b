# -*- coding: utf-8 -*-
# Copyright (c) 2013-2023, SMARTX
# All rights reserved.

import unittest

from unittest.mock import patch
from requests import Session

from common.tests.http import MockResponse
from common.vipservice.client import Client
from tuna.config.constant import VIP_ISCSI, VIP_MGT


class TestClient(unittest.TestCase):
    def test_show_vip(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(json={
                "data": [{"name": "management", "vip": "********"}, {"name": "iscsi", "vip": "********"}]
            })

            vip_services = Client().show_vip()
            expect_url = "http://127.0.0.1:8777/api/v1/services"
            assert expect_url == mock_get.call_args[0][0]
            assert len(vip_services) == 2
            assert {"name": "management", "vip": "********"} in vip_services
            assert {"name": "iscsi", "vip": "********"} in vip_services

    def test_set_vip(self):
        with patch.object(Session, "put") as mock_put:
            mock_put.return_value = MockResponse()

            Client().set_vip(VIP_MGT, "********")
            expect_url = "http://127.0.0.1:8777/api/v1/services"
            assert expect_url == mock_put.call_args[0][0]
            expect_request = {"name": "management", "vip": "********"}
            assert expect_request == mock_put.call_args[1]["json"]

    def test_set_invalid_vip(self):
        with patch.object(Session, "put") as mock_put:
            mock_put.return_value = MockResponse(status_code=400)
            with self.assertRaises(Exception):
                Client().set_vip(VIP_MGT, "invalid_vip")

    def test_delete_vip(self):
        with patch.object(Session, "delete") as mock_delete:
            mock_delete.return_value = MockResponse()

            Client().delete_vip(VIP_ISCSI)
            expect_url = "http://127.0.0.1:8777/api/v1/services/iscsi"
            assert expect_url == mock_delete.call_args[0][0]

    def test_get_vip_leader_ip(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse()

            Client().get_vip_leader_ip(VIP_ISCSI)
            expect_url = "http://127.0.0.1:8777/api/v1/services/iscsi/leader"
            assert expect_url == mock_get.call_args[0][0]

    def test_drain_vip(self):
        with patch.object(Session, "post") as mock_post:
            mock_post.return_value = MockResponse()

            Client().drain_vip(VIP_ISCSI)
            expect_url = "http://127.0.0.1:8777/api/v1/services/iscsi/drain"
            assert expect_url == mock_post.call_args[0][0]
