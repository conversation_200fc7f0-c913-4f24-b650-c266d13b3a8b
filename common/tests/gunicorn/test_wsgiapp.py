# Copyright (c) 2013-2021, SMARTX
# All rights reserved.


def test_get_proc_name():
    from common.gunicorn import wsgiapp

    assert wsgiapp.get_proc_name("") == wsgiapp._DEFAULT_PROC_NAME
    assert wsgiapp.get_proc_name("master [xxx]") == "xxx"
    assert wsgiapp.get_proc_name("master [[xxx]]") == "[xxx]"
    assert wsgiapp.get_proc_name("", "xxx") == "xxx"
    assert wsgiapp.get_proc_name("xxx") == "xxx"


def test_patch_set_proc_title():
    from common.gunicorn import wsgiapp
    from gunicorn import util

    origin = util._setproctitle
    wsgiapp.patch_set_proc_title()
    assert util._setproctitle is wsgiapp.set_proc_title
    util._setproctitle = origin


def test_patch_arbiter_signals():
    import signal
    from common.gunicorn import wsgiapp
    from gunicorn.arbiter import Arbiter

    wsgiapp.patch_arbiter_signals()
    assert signal.SIGABRT in Arbiter.SIGNALS
    assert hasattr(Arbiter, "handle_abrt")
    assert hasattr(<PERSON>rbiter, "kill_worker")
