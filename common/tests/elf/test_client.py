# Copyright (c) 2013-2024, elfvirt
# All rights reserved.


import json
from common.elf import client
import unittest
from unittest import mock
import requests


class TestElfApiClient(unittest.TestCase):
    def setUp(self) -> None:
        self.client = client.ElfApiClient(
            server="localhost",
            port=8080,
            username="admin",
            password="admin",
            token="token",
        )

    @mock.patch.object(requests, "post")
    def test_post(self, mock_post):
        mock_post.side_effect = [
            mock.Mock(status_code=200, json=lambda: {"token": "token"}),
        ]

        self.client.post("/api/v1/elf", json={"name": "test"})

        mock_post.assert_called_once_with(
            self.client.gen_url("/api/v1/elf"),
            headers=mock.ANY,
            verify=False,
            timeout=self.client._default_timeout,
            json={"name": "test"},
        )

    @mock.patch.object(requests, "get")
    def test_get(self, mock_get):
        mock_get.side_effect = [
            mock.Mock(status_code=200, json=lambda: {"token": "token"}),
        ]

        self.client.get("/api/v1/elf")

        mock_get.assert_called_once_with(
            self.client.gen_url("/api/v1/elf"),
            headers=mock.ANY,
            verify=False,
            timeout=self.client._default_timeout,
        )

    @mock.patch.object(requests, "delete")
    def test_delete(self, mock_delete):
        mock_delete.side_effect = [
            mock.Mock(status_code=200, json=lambda: {"token": "token"}),
        ]

        self.client.delete("/api/v1/elf")

        mock_delete.assert_called_once_with(
            self.client.gen_url("/api/v1/elf"),
            headers=mock.ANY,
            verify=False,
            timeout=self.client._default_timeout,
        )

    @mock.patch.object(requests, "put")
    def test_put(self, mock_put):
        mock_put.side_effect = [
            mock.Mock(status_code=200, json=lambda: {"token": "token"}),
        ]

        self.client.put("/api/v1/elf", json={"name": "test"})

        mock_put.assert_called_once_with(
            self.client.gen_url("/api/v1/elf"),
            headers=mock.ANY,
            verify=False,
            timeout=self.client._default_timeout,
            json={"name": "test"},
        )
