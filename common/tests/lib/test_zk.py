import logging
import unittest
import uuid

from unittest import mock
from kazoo.handlers.gevent import SequentialGeventHandler

from common.lib.zk import Zookeeper


def raise_side_effect(*args, **kwargs):
    raise SequentialGeventHandler.timeout_exception("timeout")


class ZKTestCase(unittest.TestCase):
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    @mock.patch("common.lib.zk.KazooClient.start")
    def test_conn(self, conn_start_mock):
        conn_start_mock.side_effect = raise_side_effect

        with self.assertRaises(Exception) as ctx:
            with Zookeeper():
                pass
        self.assertEqual("timeout", str(ctx.exception))

    @mock.patch("common.lib.zk.KazooClient.get")
    def test_get_string_timeout(self, get_mock):
        get_mock.side_effect = raise_side_effect
        with self.assertRaises(Exception) as ctx:
            with Zookeeper() as zk:
                zk.get_string("")
        self.assertEqual("timeout", str(ctx.exception))

    def test_create_recursive(self):
        with Zookeeper() as zk:
            self.assertIsNone(zk.create_recursive(f"/test/zookeeper/client/{uuid.UUID}/create_recursive", b"test"))

    @mock.patch("common.lib.zk.KazooClient.ensure_path")
    def test_create_recursive_timeout(self, ensure_path_mock):
        ensure_path_mock.side_effect = raise_side_effect

        with self.assertRaises(Exception) as ctx:
            with Zookeeper() as zk:
                zk.create_recursive(f"/test/zookeeper/client/{uuid.UUID}/create_recursive", b"test")
        self.assertEqual("timeout", str(ctx.exception))

    def test_command(self):
        with Zookeeper() as zk:
            self.assertNotEqual(zk.command("127.0.0.1:2181", b'dump'), "")
            self.assertEqual(zk.command("127.0.0.1:2181", b"duss"), "")
            with self.assertRaises(TypeError):
                zk.command("127.0.0.1:2181", "dump")
            with self.assertRaises(ValueError):
                zk.command("127.0.0.1:2181", b"du")

    def test_get_zk_hosts(self):
        with Zookeeper() as zk:
            hosts = zk.get_zk_hosts()
            self.assertEqual(hosts, ["127.0.0.1:2181"])

    def test_get_zk_host_status(self):
        with Zookeeper() as zk:
            status = zk.get_zk_host_status(host="127.0.0.1:2181")
            self.assertEqual(status, "imok")

    def test_get_zk_host_role(self):
        with Zookeeper() as zk:
            role = zk.get_zk_host_role(host="127.0.0.1:2181")
            self.assertEqual(role, "standalone")

            role = zk.get_zk_host_role(host="127.0.0.1:2182")
            self.assertEqual(role, "bad response")

            with mock.patch("common.lib.zk.Zookeeper.command") as mock_command:
                mock_command.side_effect = Exception("error")
                role = zk.get_zk_host_role(host="127.0.0.1:2181")
                self.assertEqual(role, "bad response")

    @mock.patch("common.lib.zk.Zookeeper.command")
    def test_get_zk_host_role_timeout(self, command_mock):
        command_mock.side_effect = raise_side_effect

        with self.assertRaises(Exception) as ctx:
            with Zookeeper() as zk:
                role = zk.get_zk_host_role(host="127.0.0.1:2181")
                pass
        self.assertEqual("timeout", str(ctx.exception))

    def test_is_zk_ok(self):
        with Zookeeper() as zk:
            self.assertEqual(zk.is_zk_ok(host="127.0.0.1:2181"), True)
            self.assertEqual(zk.is_zk_ok(host="127.0.0.1:2182"), False)

            with mock.patch("common.lib.zk.Zookeeper.get_zk_host_status") as mock_status:
                mock_status.side_effect = Exception("error")
                ok = zk.is_zk_ok(host="127.0.0.1:2181")
                self.assertEqual(ok, False)

    @mock.patch("common.lib.zk.Zookeeper.command")
    def test_is_zk_ok_timeout(self, command_mock):
        command_mock.side_effect = raise_side_effect
        with self.assertRaises(Exception) as ctx:
            with Zookeeper() as zk:
                zk.is_zk_ok(host="")
        self.assertEqual("timeout", str(ctx.exception))

    def test_get_zk_leader(self):
        with Zookeeper() as zk:
            leader = zk.get_zk_leader()
            self.assertEqual(leader, "")

            with mock.patch("common.lib.zk.Zookeeper.get_zk_host_role", return_value="leader"):
                leader = zk.get_zk_leader()
                self.assertEqual(leader, "127.0.0.1:2181")

    def test_show_service(self):
        with Zookeeper() as zk:
            service = zk.show_service("meta")
            logging.info(service)
            self.assertEqual(service, {'name': 'meta', 'specified_members': ['127.0.0.1:10100'], 'leader': '127.0.0.1:10100', 'members': ['1'], 'priority': '', 'current_priority': ''})

            service = zk.show_service("chunk")
            logging.info(service)
            self.assertEqual(service, {'name': 'chunk', 'specified_members': [''], 'members': ['1'], 'priority': '', 'current_priority': ''})

    def test_list_service(self):
        with Zookeeper() as zk:
            service = zk.list_service()
            logging.info(service)
            self.assertEqual(service, [{'name': 'chunk', 'specified_members': [''], 'members': ['1'], 'priority': '', 'current_priority': ''}, {'name': 'meta', 'specified_members': ['127.0.0.1:10100'], 'leader': '127.0.0.1:10100', 'members': ['1'], 'priority': '', 'current_priority': ''}, {'name': 'taskd', 'specified_members': [''], 'leader': '127.0.0.1:10601', 'members': ['1'], 'priority': '', 'current_priority': ''}])

    def test_get_dynamic_config(self):
        with Zookeeper() as zk:
            config = zk.get_dynamic_config()
            self.assertEqual(config, '')

