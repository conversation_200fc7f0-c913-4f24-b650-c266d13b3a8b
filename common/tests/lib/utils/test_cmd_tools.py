# Copyright (c) 2013-2024, SMARTX
# All rights reserved.

import subprocess

from unittest.mock import patch

from common.lib.utils.cmd_tools import execute


def test_process_not_start():
    with patch("subprocess.Popen") as mock_popen:
        mock_popen.side_effect = OSError("Failed to start process")

        return_code, stdout, stderr = execute("fake_command")

        # Validate return values when the process fails to start
        assert return_code == -1
        assert stdout == ""
        assert stderr == "Failed to execute command: fake_command"

        # Validate Popen was called with the expected arguments
        mock_popen.assert_called_once_with(
            "fake_command",
            shell=True,
            close_fds=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
        )


def test_process_execution_successful():
    with patch("subprocess.Popen") as mock_popen:
        mock_process = mock_popen.return_value
        mock_process.communicate.return_value = ("output", "error")
        mock_process.returncode = 0

        return_code, stdout, stderr = execute("valid_command")

        # Validate return values for a successful command execution
        assert return_code == 0
        assert stdout == "output"
        assert stderr == "error"

        # Validate <PERSON> was called with the expected arguments
        mock_popen.assert_called_once_with(
            "valid_command",
            shell=True,
            close_fds=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
        )


def test_process_terminate_on_timeout():
    with patch("subprocess.Popen") as mock_popen:
        mock_process = mock_popen.return_value
        mock_process.communicate.side_effect = TimeoutError("Timeout reached")
        mock_process.returncode = None

        return_code, stdout, stderr = execute("timeout_command", timeout=1)

        # Validate return values when process times out
        assert return_code == -1
        assert stdout == ""
        assert stderr == "Failed to execute command: timeout_command"

        # Validate the process termination is logged

        # Validate Popen was called with the expected arguments
        mock_popen.assert_called_once_with(
            "timeout_command",
            shell=True,
            close_fds=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
        )
