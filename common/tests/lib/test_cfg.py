# Copyright (c) 2013-2023, SMARTX
# All rights reserved.
import os
from unittest.mock import patch
from pyfakefs import fake_filesystem

from common.lib.cfg import DeployConfigManager, get_zk_service_priority_path, OS_IN_RAID_PATH


@patch.object(DeployConfigManager, "_read_file")
def test_deploy_config(mock_read_file):
    m = DeployConfigManager()
    mock_read_file.return_value = "mock_read_file"
    assert m.get_node_uuid() == "mock_read_file"
    assert m.get_node_role() == "mock_read_file"
    assert m.get_zk_digest() == "zkusr:byHNeaEv6PcaSSnR"
    assert isinstance(m.get_hostname(), str)


def test_get_zk_service_priority_path():
    assert get_zk_service_priority_path("test") == "/zos/service/test_priority"


def test_is_os_in_soft_raid():
    fs = fake_filesystem.FakeFilesystem()

    os_module = fake_filesystem.FakeOsModule(fs)
    file_open = fake_filesystem.FakeFileOpen(fs)

    with patch("os.path", os_module.path), patch("common.lib.cfg.open", file_open):
        assert DeployConfigManager.is_os_in_soft_raid() is True

        fs.create_file(OS_IN_RAID_PATH, contents="true")
        assert DeployConfigManager.is_os_in_soft_raid() is True

        fs.remove(OS_IN_RAID_PATH)

        fs.create_file(OS_IN_RAID_PATH, contents="false")
        assert DeployConfigManager.is_os_in_soft_raid() is False
