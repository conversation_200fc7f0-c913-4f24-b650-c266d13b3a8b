# Copyright (c) 2013-2022, SMARTX
# All rights reserved.
import gevent
from unittest.mock import patch
import pytest

from common.exporter import cache_utils


class MockThread:
    def __init__(self, target):
        self.target = target

    def start(self):
        return self.target()


@patch("common.exporter.cache_utils.threading")
@patch("common.exporter.cache_utils.time")
@patch("common.exporter.cache_utils.random")
def test_catching_gevent_timeout(mock_random, mock_time, mock_threading):
    class CacheEx(Exception):
        pass

    class TestCollect:
        def __init__(self, name):
            self.name = name

        def collect(self):
            raise gevent.Timeout()

    mock_threading.Thread = MockThread
    mock_random.uniform.return_value = 0
    # `CacheEx` will raise after the gevent.Timeout is caught.
    mock_time.sleep.side_effect = [0, CacheEx]

    # Confirmation of an expected exit error
    with pytest.raises(CacheEx):
        cache_utils.CachedCollectorWrapper(TestCollect)

    assert mock_time.sleep.call_count == 2
