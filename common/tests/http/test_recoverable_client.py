# Copyright (c) 2013-2024, elfvirt
# All rights reserved.


import unittest
from unittest import mock
from common.http import recoverable_client
from common.http import tuna


class TestRecoverableClient(unittest.TestCase):
    def setUp(self):
        self.available_endpoints = ["1", "2", "3"]
        tuna_client = tuna.Client()
        self.client = recoverable_client.Client(
            service_client=tuna_client, available_service_endpoints=self.available_endpoints
        )

    def test_get_nic_infos(self):
        with mock.patch.object(tuna.Client, "get_nic_infos") as mock_get_nic_infos:
            mock_get_nic_infos.side_effect = [
                Exception("error"),
                [{"name": "Mellanox MT27710"}, {"name": "Intel I350"}],
            ]
            result = self.client.get_nic_infos("host_uuid_1")
            assert result == [{"name": "Mellanox MT27710"}, {"name": "Intel I350"}]
            mock_get_nic_infos.assert_has_calls(
                [mock.call("host_uuid_1", host="1"), mock.call("host_uuid_1", host="2")]
            )

    def test_get_nic_infos_with_specified_endpoint(self):
        with mock.patch.object(tuna.Client, "get_nic_infos") as mock_get_nic_infos:
            mock_get_nic_infos.side_effect = [
                Exception("error"),
                [{"name": "Mellanox MT27710"}, {"name": "Intel I350"}],
            ]
            with self.assertRaises(Exception):
                self.client.get_nic_infos("host_uuid_1", host="endpoint_1")

            result = self.client.get_nic_infos("host_uuid_1", host="endpoint_2")
            assert result == [{"name": "Mellanox MT27710"}, {"name": "Intel I350"}]
            mock_get_nic_infos.assert_has_calls(
                [mock.call("host_uuid_1", host="endpoint_1"), mock.call("host_uuid_1", host="endpoint_2")]
            )
