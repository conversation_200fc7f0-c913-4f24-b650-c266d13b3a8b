# Copyright (c) 2013-2021, SMARTX
# All rights reserved.

from unittest.mock import call, patch

import pytest
from requests import Session

from common.http.elf import Client
from common.http.exceptions import RestfulClientError
from common.tests.http import MockResponse


class TestElfRestClient:
    def test_try_migrate_vms(self):
        with patch.object(Session, "post") as mock_post:
            mock_post.return_value = MockResponse(
                status_code=200,
                json={"ec": "EOK", "data": {"succeeded": [], "failed": []}},
            )
            client = Client()
            result = client.try_migrate_vms(vm_ids=[])
            call_args, call_kwargs = mock_post.call_args
            url = "http://127.0.0.1:80/api/v2/batch/vms/try_migrate_to_other_nodes"
            json = {"vm_ids": []}
            assert url in call_args
            assert call_kwargs["json"] == json
            assert result == {"succeeded": [], "failed": []}

            result = client.try_migrate_vms(vm_ids=[], expected_vm_status="stopped")
            call_args, call_kwargs = mock_post.call_args
            url = "http://127.0.0.1:80/api/v2/batch/vms/try_migrate_to_other_nodes"
            json = {"vm_ids": [], "expected_vm_status": "stopped"}
            assert url in call_args
            assert call_kwargs["json"] == json
            assert result == {"succeeded": [], "failed": []}

    def test_try_migrate_vms_v2(self):
        with patch.object(Session, "post") as mock_post:
            mock_post.return_value = MockResponse(
                status_code=200,
                json={"ec": "EOK", "data": {"succeeded": [], "failed": []}},
            )
            client = Client()

            # Test with valid vm_infos
            vms = [{"vm_uuid": "vm1", "priority": 100, "pg_policy": "must", "expected_vm_status": "stopped"}]
            result = client.try_migrate_vms_v2(vms)
            call_args, call_kwargs = mock_post.call_args
            url = "http://127.0.0.1:80/api/v2/batch/vms/try_migrate_to_other_nodes_v2"
            json = {"vm_infos": vms}
            assert url in call_args
            assert call_kwargs["json"] == json
            assert result == {"succeeded": [], "failed": []}

            # Test with empty vms
            with pytest.raises(ValueError):
                client.try_migrate_vms_v2([])

    def test_get_vms(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.side_effect = [
                MockResponse(
                    status_code=200,
                    json={
                        "ec": "EOK",
                        "data": {
                            "count": 200,
                            "start_uuid": "start_uuid",
                            "end_uuid": "end_uuid",
                            "page_entities": 200,
                            "total_entities": 201,
                            "entities": [{"vm_name": str(i)} for i in range(200)],
                        },
                    },
                ),
                MockResponse(
                    status_code=200,
                    json={
                        "ec": "EOK",
                        "data": {
                            "count": 1,
                            "start_uuid": "start_uuid",
                            "end_uuid": "end_uuid",
                            "page_entities": 1,
                            "total_entities": 201,
                            "entities": [{"vm_name": "name1"}],
                        },
                    },
                ),
            ]
            client = Client()
            result = client.get_vms()
            assert mock_get.call_count == 2
            first_get = mock_get.call_args_list[0]
            args, kwargs = first_get
            first_url = "http://127.0.0.1:80/api/v2/vms?count=200&skip_page=1"
            assert first_url in args
            second_get = mock_get.call_args_list[1]
            args, kwargs = second_get
            second_url = "http://127.0.0.1:80/api/v2/vms?count=200&start_uuid=start_uuid&end_uuid=end_uuid&skip_page=1"
            assert second_url in args
            assert len(result) == 201

    def test_get_vm(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(
                status_code=200,
                json={"ec": "EOK", "data": {"vm_name": "test"}},
            )
            client = Client()
            result = client.get_vm("vm_id")
            call_args, call_kwargs = mock_get.call_args
            url = "http://127.0.0.1:80/api/v2/vms/vm_id"
            assert url in call_args
            assert result["vm_name"] == "test"

    def test_get_node_vms(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(
                status_code=200,
                json={
                    "ec": "EOK",
                    "data": {
                        "count": 2,
                        "start_uuid": "start_uuid",
                        "end_uuid": "end_uuid",
                        "page_entities": 2,
                        "total_entities": 2,
                        "entities": [{"vm_name": "name1"}, {"vm_name": "name2"}],
                    },
                },
            )
            client = Client()
            result = client.get_node_vms("node_ip")
            call_args, call_kwargs = mock_get.call_args
            url = "http://127.0.0.1:80/api/v2/vms?count=200&skip_page=1&filter_criteria=node_ip=node_ip"
            assert url in call_args
            assert len(result) == 2

    def test_get_node_running_vms(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(
                status_code=200,
                json={
                    "ec": "EOK",
                    "data": {
                        "count": 2,
                        "start_uuid": "start_uuid",
                        "end_uuid": "end_uuid",
                        "page_entities": 2,
                        "total_entities": 2,
                        "entities": [{"vm_name": "name1"}, {"vm_name": "name2"}],
                    },
                },
            )
            client = Client()
            result = client.get_node_running_vms("node_ip")
            call_args, call_kwargs = mock_get.call_args
            url = "http://127.0.0.1:80/api/v2/vms?count=200&skip_page=1&filter_criteria=status=running;node_ip=node_ip"
            assert url in call_args
            assert len(result) == 2

    def test_start_vm(self):
        with patch.object(Session, "post") as mock_post:
            mock_post.return_value = MockResponse(
                status_code=200,
                json={"ec": "EOK", "data": {"job_id": "job_id"}},
            )
            client = Client()
            result = client.start_vm("vm_id", auto_schedule=True)
            url = "http://127.0.0.1:80/api/v2/vms/vm_id/start"
            call_args, call_kwargs = mock_post.call_args
            assert url in call_args
            assert call_kwargs["json"] == {"auto_schedule": True}
            assert result == "job_id"

            result = client.start_vm("vm_id", auto_schedule=False, node_ip="***********")
            url = "http://127.0.0.1:80/api/v2/vms/vm_id/start"
            call_args, call_kwargs = mock_post.call_args
            assert url in call_args
            assert call_kwargs["json"] == {"node_ip": "***********"}
            assert result == "job_id"

    def test_stop_vm(self):
        with patch.object(Session, "post") as mock_post:
            mock_post.return_value = MockResponse(
                status_code=200,
                json={"ec": "EOK", "data": {"job_id": "job_id"}},
            )
            client = Client()
            result = client.stop_vm("vm_id")
            url = "http://127.0.0.1:80/api/v2/vms/vm_id/stop"
            call_args, call_kwargs = mock_post.call_args
            assert url in call_args
            assert call_kwargs["json"] == {"force": False}
            assert result == "job_id"

            result = client.stop_vm("vm_id", force=True)
            url = "http://127.0.0.1:80/api/v2/vms/vm_id/stop"
            call_args, call_kwargs = mock_post.call_args
            assert url in call_args
            assert call_kwargs["json"] == {"force": True}
            assert result == "job_id"

    def test_migrate_vm(self):
        with patch.object(Session, "post") as mock_post:
            mock_post.return_value = MockResponse(
                status_code=200,
                json={"ec": "EOK", "data": {"job_id": "job_id"}},
            )
            client = Client()
            result = client.migrate_vm("vm_id", node_ip="***********", auto_schedule=False)
            url = "http://127.0.0.1:80/api/v2/vms/vm_id/migrate"
            call_args, call_kwargs = mock_post.call_args
            assert url in call_args
            assert call_kwargs["json"] == {"node_ip": "***********"}
            assert result == "job_id"

            result = client.migrate_vm("vm_id", auto_schedule=True)
            url = "http://127.0.0.1:80/api/v2/vms/vm_id/migrate"
            call_args, call_kwargs = mock_post.call_args
            assert url in call_args
            assert call_kwargs["json"] == {"auto_schedule": True}
            assert result == "job_id"

    def test_batch_migrate_vms(self):
        with patch.object(Session, "post") as mock_post:
            mock_post.return_value = MockResponse(
                status_code=200,
                json={"ec": "EOK", "data": {"job_id": "job_id_123"}},
            )
            client = Client()

            # Test successful case with normal input
            result = client.batch_migrate_vms(["vm1", "vm2"], auto_schedule=True)
            call_args, call_kwargs = mock_post.call_args
            url = "http://127.0.0.1:80/api/v2/batch/vms/migrate"
            assert url in call_args
            assert call_kwargs["json"] == {"auto_schedule": True, "vm_uuids": ["vm1", "vm2"]}
            assert result == "job_id_123"

            # Test with empty input
            with pytest.raises(RestfulClientError):
                mock_post.return_value = MockResponse(
                    status_code=400, json={"ec": "ERROR", "error": {"detail": "vm_uuids cannot be empty"}}
                )
                client.batch_migrate_vms([])

    def test_batch_migrate_vms_v2(self):
        with patch.object(Session, "post") as mock_post:
            mock_post.return_value = MockResponse(
                status_code=200,
                json={"ec": "EOK", "data": {"job_id": "job_id_123"}},
            )
            client = Client()

            # Test successful case with normal input
            vms = [
                {
                    "vm_uuid": "vm1",
                    "priority": 100,
                    "pg_policy": "must",
                },
                {"vm_uuid": "vm2", "priority": 50, "pg_policy": "best_effort"},
            ]
            result = client.batch_migrate_vms_v2(vms)

            call_args, call_kwargs = mock_post.call_args
            url = "http://127.0.0.1:80/api/v2/batch/vms/migrate_v2"
            assert url in call_args
            assert call_kwargs["json"] == {"vm_infos": vms}
            assert result == "job_id_123"

            # Test with empty input
            with pytest.raises(RestfulClientError):
                mock_post.return_value = MockResponse(
                    status_code=400, json={"ec": "EINVAL", "error": {"detail": "vm_infos cannot be empty"}}
                )
                client.batch_migrate_vms_v2([])

            # Test with server error
            with pytest.raises(RestfulClientError):
                mock_post.return_value = MockResponse(
                    status_code=500, json={"ec": "EOTHER", "error": {"detail": "Internal server error"}}
                )
                client.batch_migrate_vms_v2(vms)

    def test_get_license(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(
                status_code=200,
                json={"ec": "EOK", "data": {"license": "test"}},
            )
            client = Client()
            result = client.get_license()
            call_args, _ = mock_get.call_args
            url = "http://127.0.0.1:80/api/v2/elf/license"
            assert url in call_args
            assert result["license"] == "test"

    def test_get_vms_summary(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(
                status_code=200,
                json={"ec": "EOK", "data": {"nodes": {}}},
            )
            client = Client()
            result = client.get_vms_summary()
            call_args, _ = mock_get.call_args
            url = "http://127.0.0.1:80/api/v2/compute/vms_summary"
            assert url in call_args
            assert "nodes" in result

    def test_get_job(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(
                status_code=200,
                json={"ec": "EOK", "data": {"job": {"job_id": "job_id"}}},
            )
            client = Client()
            result = client.get_job("job_id")
            url = "http://127.0.0.1:80/api/v2/jobs/job_id"
            call_args, call_kwargs = mock_get.call_args
            assert url in call_args
            assert result == {"job_id": "job_id"}

    def test_wait_job_done(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(
                status_code=200,
                json={"ec": "EOK", "data": {"job": {"job_id": "job_id", "state": "done"}}},
            )
            client = Client()
            result = client.wait_job_done("job_id")
            url = "http://127.0.0.1:80/api/v2/jobs/job_id"
            call_args, call_kwargs = mock_get.call_args
            assert url in call_args
            assert result

            mock_get.return_value = MockResponse(
                status_code=200,
                json={"ec": "EOK", "data": {"job": {"job_id": "job_id", "state": "failed"}}},
            )
            client = Client()
            result = client.wait_job_done("job_id")
            url = "http://127.0.0.1:80/api/v2/jobs/job_id"
            call_args, call_kwargs = mock_get.call_args
            assert url in call_args
            assert not result

            mock_get.return_value = MockResponse(
                status_code=200,
                json={"ec": "EOK", "data": {"job": {"job_id": "job_id", "state": "test"}}},
            )
            with patch("common.http.elf.sleep") as mock_sleep:
                client = Client()
                result = client.wait_job_done("job_id")
                url = "http://127.0.0.1:80/api/v2/jobs/job_id"
                call_args, call_kwargs = mock_get.call_args
                assert url in call_args
                mock_sleep.assert_called_with(5)
                mock_sleep.call_count == 360
                assert not result

    def test_set_host_schedulability(self):
        with patch.object(Session, "post") as mock_post:
            mock_post.return_value = MockResponse(
                status_code=200,
                json={"ec": "EOK", "data": {"job_id": "job_id"}},
            )
            client = Client()
            client.set_host_schedulability("host_id", True)
            url = "http://127.0.0.1:80/api/v2/elf/hosts/host_id/set_schedulability"
            call_args, call_kwargs = mock_post.call_args
            assert url in call_args
            assert call_kwargs["json"] == {"schedulable": True}

            client.set_host_schedulability("host_id", False)
            url = "http://127.0.0.1:80/api/v2/elf/hosts/host_id/set_schedulability"
            call_args, call_kwargs = mock_post.call_args
            assert url in call_args
            assert call_kwargs["json"] == {"schedulable": False}

    def test_get_placement_groups(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(
                status_code=200,
                json={
                    "data": {
                        "count": 50,
                        "end_uuid": "10fcc668-7b99-48bb-992a-4132ddf70092",
                        "entities": [
                            {
                                "associated_vms": [
                                    "00e735c3-c1a3-4379-94d2-ae4ee9980191",
                                    "73e88a1f-3567-488a-85aa-af00667bc8f8",
                                ],
                                "created_at": 1660635964,
                                "description": "",
                                "enable": False,
                                "modified_at": 1660808873,
                                "name": "test",
                                "policy": {"vm_host_rules": [], "vm_vm_rule": "must_same"},
                                "uuid": "10fcc668-7b99-48bb-992a-4132ddf70092",
                            }
                        ],
                        "filter_criteria": {},
                        "page_entities": 1,
                        "sort_criteria": "-created_at",
                        "start_uuid": "10fcc668-7b99-48bb-992a-4132ddf70092",
                        "total_entities": 1,
                    },
                    "ec": "EOK",
                    "error": {},
                },
            )
            client = Client()
            result = client.get_placement_groups()
            call_args, call_kwargs = mock_get.call_args
            url = "http://127.0.0.1:80/api/v2/placement_groups?count=1024&skip_page=1"
            assert url in call_args
            assert isinstance(result, list)
            assert len(result) == 1
            assert not result[0]["enable"]

    def test_get_vm_exclusive_cpus(self):
        with patch.object(Session, "get") as mock_get, patch("common.lib.cfg.Config"):
            mock_get.return_value = MockResponse(
                status_code=200,
                json={
                    "ec": "EOK",
                    "error": {},
                    "data": {
                        "vms": [
                            {
                                "uuid": "vm_uuid_1",
                                "node_ip": "********",
                                "cpu_exclusive": {"vcpu_to_pcpu": {"0": "6", "1": "7"}},
                            },
                            {
                                "uuid": "vm_uuid_2",
                                "node_ip": "********",
                                "cpu_exclusive": {"vcpu_to_pcpu": {"0": "8", "1": "9"}},
                            },
                            {
                                "uuid": "vm_uuid_3",
                                "node_ip": "********",
                                "cpu_exclusive": {"vcpu_to_pcpu": {"0": "10", "1": "11"}},
                            },
                        ]
                    },
                },
            )
            client = Client()
            cpus, vm_uuids, cpus_of_vms = client.get_vm_exclusive_cpus("********")
            cpus.sort()
            call_args, call_kwargs = mock_get.call_args
            url = "http://127.0.0.1:80/api/v2/batch/vms/get_exclusive_cpu_info"
            assert url in call_args
            assert (cpus, vm_uuids) == ([8, 9, 10, 11], ["vm_uuid_2", "vm_uuid_3"])
            assert cpus_of_vms == {"vm_uuid_2": [8, 9], "vm_uuid_3": [10, 11]}

            cpus, vm_uuids, cpus_of_vms = client.get_vm_exclusive_cpus("********")
            call_args, call_kwargs = mock_get.call_args
            url = "http://127.0.0.1:80/api/v2/batch/vms/get_exclusive_cpu_info"
            assert url in call_args
            assert (cpus, vm_uuids) == ([], [])
            assert cpus_of_vms == {}
            assert mock_get.call_count == 2

    def test_get_vm_exclusive_cpus__api_not_exist(self):
        with patch.object(Session, "get") as mock_get, patch("common.lib.cfg.Config"):
            mock_get.return_value = MockResponse(
                status_code=404,
                json={},
            )
            client = Client()
            cpus, vm_uuids, cpus_of_vms = client.get_vm_exclusive_cpus("********")
            cpus.sort()
            call_args, call_kwargs = mock_get.call_args
            url = "http://127.0.0.1:80/api/v2/batch/vms/get_exclusive_cpu_info"
            assert url in call_args
            assert (cpus, vm_uuids) == ([], [])
            assert cpus_of_vms == {}
            assert mock_get.call_count == 1

    def test_get_vm_exclusive_cpus__error(self):
        with patch.object(Session, "get") as mock_get, patch("common.lib.cfg.Config"):
            mock_get.return_value = MockResponse(status_code=500, json={"ec": "EOTHER"})
            client = Client()

            err = None
            try:
                client.get_vm_exclusive_cpus("********")
            except Exception as e:
                err = e
            assert str(err) == "[REST_API_HTTP_ERROR][500]status code is 500."
            call_args, call_kwargs = mock_get.call_args
            url = "http://127.0.0.1:80/api/v2/batch/vms/get_exclusive_cpu_info"
            assert url in call_args
            assert mock_get.call_count == 5

    @patch.object(Client, "get_vm")
    def test_vm_exclusive_cpus_switch(self, m_get_vm):
        m_get_vm.return_value = {"ha": True}

        with patch.object(Session, "put") as mock_put:
            mock_put.return_value = MockResponse(
                status_code=200,
                json={"ec": "EOK", "error": {}, "data": {"job_id": "d9116acf-40b3-4280-a536-b5011b777398"}},
            )
            client = Client()
            client.vm_exclusive_cpus_switch("vm1", True)

            call_args, call_kwargs = mock_put.call_args
            url = "http://127.0.0.1:80/api/v2/vms/vm1"
            assert url in call_args
            assert m_get_vm.mock_calls == [call("vm1")]

    @patch.object(Client, "get_vms")
    def test_get_unrealized_exclusive_cpus(self, m_get_vms):
        # vm:
        #   uuid
        #   vcpu
        #   cpu_exclusive
        #     expected_enabled
        #     actual_enabled
        m_get_vms.side_effect = [
            [
                {
                    "uuid": "vm10",
                    "status": "running",
                    "vcpu": 8,
                    "cpu_exclusive": {"actual_enabled": True, "expected_enabled": True},
                },
                {
                    "uuid": "vm11",
                    "status": "running",
                    "vcpu": 2,
                    "cpu_exclusive": {"actual_enabled": False, "expected_enabled": True},
                },
                {
                    "uuid": "vm12",
                    "status": "running",
                    "vcpu": 3,
                    "cpu_exclusive": {"actual_enabled": False, "expected_enabled": False},
                },
                {
                    "uuid": "vm13",
                    "status": "running",
                    "vcpu": 4,
                    "cpu_exclusive": {"actual_enabled": False, "expected_enabled": True},
                },
            ],
            [
                {
                    "uuid": "vm20",
                    "status": "suspended",
                    "vcpu": 4,
                    "cpu_exclusive": {"actual_enabled": False, "expected_enabled": True},
                },
                {
                    "uuid": "vm21",
                    "status": "suspended",
                    "vcpu": 5,
                    "cpu_exclusive": {"actual_enabled": False, "expected_enabled": False},
                },
                {
                    "uuid": "vm22",
                    "status": "suspended",
                    "vcpu": 6,
                    "cpu_exclusive": {"actual_enabled": False, "expected_enabled": True},
                },
                {
                    "uuid": "vm23",
                    "status": "suspended",
                    "vcpu": 7,
                    "cpu_exclusive": {"actual_enabled": True, "expected_enabled": True},
                },
            ],
        ]

        client = Client()
        cpu_required_by_vm, suspended_vms = client.get_unrealized_exclusive_cpus("node1")
        assert cpu_required_by_vm == {"vm11": 2, "vm13": 4, "vm20": 4, "vm22": 6}
        assert suspended_vms == ["vm20", "vm22", "vm23"]

    @patch.object(Client, "get_vms")
    def test_get_unrealized_exclusive_cpus_api_request_failed(self, m_get_vms):
        m_get_vms.side_effect = RestfulClientError(
            "Get vms failed. error message: {'detail': '', 'msg': 'No module named crab.client'}"
        )

        client = Client()
        cpu_required_by_vm, suspended_vms = client.get_unrealized_exclusive_cpus("node1")
        assert cpu_required_by_vm == {}
        assert suspended_vms == []
        m_get_vms.assert_called_once_with({"status": "running", "node_ip": "node1"})

    def test_config_libvirt_tcp_port(self):
        with patch.object(Session, "post") as mock_post:
            mock_post.return_value = MockResponse(
                status_code=200,
                json={"ec": "EOK", "data": {"succeeded": [], "failed": []}},
            )
            client = Client()

            client.config_libvirt_tcp_port(action="open")

            call_args, call_kwargs = mock_post.call_args
            json = {"action": "open"}
            url = "http://127.0.0.1:80/api/v2/elf/libvirt_security/config_tcp_port"
            assert url in call_args
            assert call_kwargs["json"] == json
