# Copyright (c) 2013-2023, SMARTX
# All rights reserved.
import unittest
from unittest import mock

import uuid
import pytest

from common.http import network, restful_client
from common.tests import http


class TestVPCVNIC(unittest.TestCase):
    def setUp(self) -> None:
        self._vpc_uuid = str(uuid.uuid4())
        self._subnet_uuid = str(uuid.uuid4())
        self._vlan_uuid = str(uuid.uuid4())
        self._ovsbr_name = "vpcbr"
        self._floating_ip_uuid = str(uuid.uuid4())
        self._vpc_nic_with_ip_config = {
            "vpc_uuid": self._vpc_uuid,
            "subnet_uuid": self._subnet_uuid,
            "mac_addr": "53:53:53:ae:ae:ae",
            "ipv4_addr": "************",
            "floating_ip_uuid": self._floating_ip_uuid,
            "vnic_uuid": str(uuid.uuid4()),
            "vlan_uuid": self._vlan_uuid,
            "ovsbr_name": self._ovsbr_name,
            "skip_ip_validation": False,
        }
        self._vpc_nic_without_ip_config = {
            "vpc_uuid": self._vpc_uuid,
            "subnet_uuid": self._subnet_uuid,
            "mac_addr": "53:53:53:ae:ae:ad",
            "vnic_uuid": str(uuid.uuid4()),
            "vlan_uuid": self._vlan_uuid,
            "ovsbr_name": self._ovsbr_name,
            "skip_ip_validation": False,
            "ipv4_addr": "",
            "floating_ip_uuid": "",
        }

        self._vnic_group_uuid = str(uuid.uuid4())
        self._cluster_uuid = str(uuid.uuid4())
        self._vpc_nics = [self._vpc_nic_with_ip_config, self._vpc_nic_without_ip_config]
        self._response_data = {
            "vnics": [
                {
                    "code": 200,
                    "data": self._vpc_nic_with_ip_config["vnic_uuid"],
                    "reason": "",
                    "message": "",
                    "error": None
                },
                {
                    "code": 200,
                    "data": self._vpc_nic_without_ip_config["vnic_uuid"],
                    "reason": "",
                    "message": "",
                    "error": None
                }
            ]
        }

    def test_validate_create(self):
        with mock.patch.object(restful_client.Client, "post") as mock_post:
            client = network.Client()
            mock_post.return_value = {"data": self._response_data, "ec": "EOK", "error": {}}

            result = client.validate_create_vpc_nics(self._vnic_group_uuid, self._vpc_nics)

            assert len(result) == 2
            mock_post.assert_called_once_with(
                "http://127.0.0.1:80/api/v2/network/vnic_group/{}/validator".format(self._vnic_group_uuid),
                json={
                    "vnics": [
                        {
                            "vpc_uuid": self._vpc_uuid,
                            "subnet_uuid": self._subnet_uuid,
                            "mac_addr": self._vpc_nic_with_ip_config["mac_addr"],
                            "ipv4_addr": self._vpc_nic_with_ip_config["ipv4_addr"],
                            "floating_ip_uuid": self._vpc_nic_with_ip_config["floating_ip_uuid"],
                            "vnic_uuid": self._vpc_nic_with_ip_config["vnic_uuid"],
                            "vlan_uuid": self._vlan_uuid,
                            "ovsbr_name": self._ovsbr_name,
                            "skip_ip_validation": False,
                        },
                        {
                            "vpc_uuid": self._vpc_uuid,
                            "subnet_uuid": self._subnet_uuid,
                            "mac_addr": self._vpc_nic_without_ip_config["mac_addr"],
                            "ipv4_addr": "",
                            "floating_ip_uuid": "",
                            "vnic_uuid": self._vpc_nic_without_ip_config["vnic_uuid"],
                            "vlan_uuid": self._vlan_uuid,
                            "ovsbr_name": self._ovsbr_name,
                            "skip_ip_validation": False,
                        },
                    ]
                }
            )

    def test_validate_create_raise_error(self):
        with mock.patch.object(restful_client.Client, "post") as mock_post:
            client = network.Client()
            mock_post.return_value = {"data": {}, "ec": "REST_API_HTTP_ERROR", "error": {}}

            with pytest.raises(restful_client.rtf_exceptions):
                client.validate_create_vpc_nics(
                    self._vnic_group_uuid, [self._vpc_nic_with_ip_config, self._vpc_nic_without_ip_config]
                )

    def test_create(self):
        with mock.patch.object(restful_client.Client, "post") as mock_post:
            client = network.Client()
            mock_post.return_value = {"data": self._response_data, "ec": "EOK", "error": {}}

            result = client.create_vpc_nics(self._cluster_uuid, self._vnic_group_uuid, self._vpc_nics)

            assert len(result) == 2
            mock_post.assert_called_once_with(
                "http://127.0.0.1:80/api/v2/network/vnic_group",
                json={
                    "cluster_uuid": self._cluster_uuid,
                    "vnic_group_uuid": self._vnic_group_uuid,
                    "vnics": [
                        {
                            "vpc_uuid": self._vpc_uuid,
                            "subnet_uuid": self._subnet_uuid,
                            "mac_addr": self._vpc_nic_with_ip_config["mac_addr"],
                            "ipv4_addr": self._vpc_nic_with_ip_config["ipv4_addr"],
                            "floating_ip_uuid": self._vpc_nic_with_ip_config["floating_ip_uuid"],
                            "vnic_uuid": self._vpc_nic_with_ip_config["vnic_uuid"],
                            "vlan_uuid": self._vlan_uuid,
                            "ovsbr_name": self._ovsbr_name,
                            "skip_ip_validation": False,
                        },
                        {
                            "vpc_uuid": self._vpc_uuid,
                            "subnet_uuid": self._subnet_uuid,
                            "mac_addr": self._vpc_nic_without_ip_config["mac_addr"],
                            "ipv4_addr": "",
                            "floating_ip_uuid": "",
                            "vnic_uuid": self._vpc_nic_without_ip_config["vnic_uuid"],
                            "vlan_uuid": self._vlan_uuid,
                            "ovsbr_name": self._ovsbr_name,
                            "skip_ip_validation": False,
                        },
                    ]
                }
            )

    def test_create_raise_error(self):
        with mock.patch.object(restful_client.Client, "post") as mock_post:
            client = network.Client()
            mock_post.return_value = {"data": {}, "ec": "REST_API_HTTP_ERROR", "error": {}}

            with pytest.raises(restful_client.rtf_exceptions):
                client.create_vpc_nics(self._cluster_uuid, self._vnic_group_uuid, self._vpc_nics)

    def test_delete(self):
        with mock.patch.object(restful_client.Client, "delete") as mock_delete:
            client = network.Client()
            mock_delete.return_value = {"data": self._response_data, "ec": "EOK", "error": {}}

            result = client.delete_vpc_nics(
                self._vnic_group_uuid,
                [self._vpc_nic_with_ip_config["vnic_uuid"], self._vpc_nic_without_ip_config["vnic_uuid"]],
            )

            assert len(result) == 2
            mock_delete.assert_called_once_with(
                "http://127.0.0.1:80/api/v2/network/vnic_group/{}".format(self._vnic_group_uuid),
                json={
                    "vnics": [
                        {"vnic_uuid": self._vpc_nic_with_ip_config["vnic_uuid"]},
                        {"vnic_uuid": self._vpc_nic_without_ip_config["vnic_uuid"]},
                    ]
                }
            )

    def test_delete_raise_error(self):
        with mock.patch.object(restful_client.Client, "post") as mock_post:
            client = network.Client()
            mock_post.return_value = {"data": {}, "ec": "REST_API_HTTP_ERROR", "error": {}}

            with pytest.raises(restful_client.rtf_exceptions):
                client.delete_vpc_nics(
                    self._vnic_group_uuid,
                    [self._vpc_nic_with_ip_config["vnic_uuid"], self._vpc_nic_without_ip_config["vnic_uuid"]],
                )

    def test_get_vpc_nics_of_one_vnic_group(self):
        with mock.patch.object(restful_client.Client, "get") as mock_get:
            client = network.Client()
            vnic_group_uuid = str(uuid.uuid4())
            mock_get.return_value = {
                "ec": "EOK",
                "error": {},
                "data": {
                    "vnics": [
                        {"vnic_uuid": "vnic_uuid_1", "vpc_uuid": "test_vpc_uuid", "subnet_uuid": "test_subnet_uuid"},
                        {"vnic_uuid": "vnic_uuid_2", "vpc_uuid": "test_vpc_uuid", "subnet_uuid": "test_subnet_uuid"},
                    ]
                },
            }

            result = client.get_vpc_nics(vnic_group_uuid)

            assert result == {
                "vnic_uuid_1": {
                    "vnic_uuid": "vnic_uuid_1",
                    "vpc_uuid": "test_vpc_uuid",
                    "subnet_uuid": "test_subnet_uuid",
                },
                "vnic_uuid_2": {
                    "vnic_uuid": "vnic_uuid_2",
                    "vpc_uuid": "test_vpc_uuid",
                    "subnet_uuid": "test_subnet_uuid",
                },
            }

    def test_get_vpc_nics_of_one_vnic_group_raise_error(self):
        with mock.patch.object(restful_client.Client, "get") as mock_get:
            client = network.Client()
            vnic_group_uuid = str(uuid.uuid4())
            mock_get.return_value = {"data": {}, "ec": "REST_API_HTTP_ERROR", "error": {}}

            with pytest.raises(restful_client.rtf_exceptions):
                client.get_vpc_nics(vnic_group_uuid)

    def test_update_vpc_nics_mac_addr(self):
        with mock.patch.object(restful_client.Client, "put") as mock_put:
            client = network.Client()
            mock_put.return_value = {
                "data": {
                    "vnics": [
                        {"code": 200, "data": "vnic_uuid_1", "reason": "", "message": "", "error": None},
                        {"code": 200, "data": "vnic_uuid_2", "reason": "", "message": "", "error": None},
                    ]
                },
                "ec": "EOK",
                "error": {},
            }
            client.update_vpc_nics_mac_addr(
                "vm_uuid_1",
                [
                    {"vnic_uuid": "vnic_uuid_1", "mac_addr": "53:53:53:10:10:10"},
                    {"vnic_uuid": "vnic_uuid_2", "mac_addr": "53:53:53:20:20:20"},
                ],
            )
            mock_put.assert_called_once_with(
                "http://127.0.0.1:80/api/v2/network/vnic_group/{}".format("vm_uuid_1"),
                json={
                    "vnics": [
                        {"vnic_uuid": "vnic_uuid_1", "mac_addr": "53:53:53:10:10:10"},
                        {"vnic_uuid": "vnic_uuid_2", "mac_addr": "53:53:53:20:20:20"},
                    ],
                }
            )

    def test_update_vpc_nics_cluster_uuid(self):
        with mock.patch.object(restful_client.Client, "put") as mock_put:
            mock_put.return_value = {
                "data": {
                    "vnics": [
                        {"code": 200, "data": "vnic_uuid_1", "reason": "", "message": "", "error": None},
                        {"code": 200, "data": "vnic_uuid_2", "reason": "", "message": "", "error": None},
                    ]
                },
                "ec": "EOK",
                "error": {},
            }
            client = network.Client()
            client.update_vpc_nics_cluster_uuid(
                "vm_uuid_1",
                [{"vnic_uuid": "vnic_uuid_1"}, {"vnic_uuid": "vnic_uuid_2"}],
                "test_cluster_uuid",
            )

            mock_put.assert_called_once_with(
                "http://127.0.0.1:80/api/v2/network/vnic_group/{}".format("vm_uuid_1"),
                json={
                    "vnics": [{"vnic_uuid": "vnic_uuid_1"}, {"vnic_uuid": "vnic_uuid_2"}],
                    "cluster_uuid": "test_cluster_uuid",
                }
            )

    def test_update_vpc_nics_cluster_uuid_raise_error(self):
        with mock.patch.object(restful_client.Client, "put") as mock_put:
            client = network.Client()
            mock_put.return_value = {
                "data": {
                    "vnics": [
                        {"code": 409, "data": "vnic_uuid_1", "reason": "", "message": "", "error": None},
                        {"code": 200, "data": "vnic_uuid_2", "reason": "", "message": "", "error": None},
                    ]
                },
                "ec": "REST_API_HTTP_ERROR",
                "error": {},
            }

            with pytest.raises(restful_client.rtf_exceptions):
                client.update_vpc_nics_cluster_uuid(
                    "vm_uuid_1",
                    [{"vnic_uuid": "vnic_uuid_1"}, {"vnic_uuid": "vnic_uuid_2"}],
                    "test_cluster_uuid",
                )


class TestVPCVNICSnapshot(unittest.TestCase):
    def setUp(self):
        self._cluster_uuid = str(uuid.uuid4())
        self._vnic_group_snapshot_uuid = str(uuid.uuid4())
        self._vnic_group_uuid = str(uuid.uuid4())
        self._vnics = [
            {"vnic_uuid": str(uuid.uuid4()), "vnic_snapshot_uuid": str(uuid.uuid4())},
            {"vnic_uuid": str(uuid.uuid4()), "vnic_snapshot_uuid": str(uuid.uuid4())},
        ]
        self._vpc_uuid = str(uuid.uuid4())
        self._subnet_uuid = str(uuid.uuid4())

        self._create_response_data = {
            "vnic_snapshots": [
                {
                    "error": None,
                    "reason": "",
                    "message": "",
                    "code": 201,
                    "vnic_snapshot_uuid": self._vnic_group_uuid,
                    "vnic_uuid": self._vnics[0]["vnic_uuid"],
                    "vpc_uuid": self._vpc_uuid,
                    "subnet_uuid": self._subnet_uuid,
                    "floating_ip_uuid": ""
                },
                {
                    "error": None,
                    "reason": "",
                    "message": "",
                    "code": 201,
                    "vnic_snapshot_uuid": self._vnic_group_uuid,
                    "vnic_uuid": self._vnics[1]["vnic_uuid"],
                    "vpc_uuid": self._vpc_uuid,
                    "subnet_uuid": self._subnet_uuid,
                    "floating_ip_uuid": str(uuid.uuid4()),
                },
            ]
        }
        self._delete_response_data = [
            {"error": None, "reason": "", "message": "", "code": 200},
            {"error": None, "reason": "", "message": "", "code": 200},
        ]
        self._rollback_response_data = [
            {"error": None, "reason": "", "message": "", "code": 200},
            {"error": None, "reason": "", "message": "", "code": 200},
        ]

    def test_create_vpc_nic_snapshot(self):
        with mock.patch.object(restful_client.Client, "post") as mock_post:
            client = network.Client()
            mock_post.return_value = {"data": self._create_response_data, "ec": "EOK", "error": {}}

            result = client.create_vpc_nic_snapshots(
                self._cluster_uuid, self._vnic_group_snapshot_uuid, self._vnic_group_uuid,  self._vnics
            )

            assert len(result) == 2
            for r in result:
                for field in (
                    "code", "vnic_snapshot_uuid", "vnic_uuid", "vpc_uuid", "subnet_uuid", "floating_ip_uuid"
                ):
                    assert field in r
            mock_post.assert_called_once_with(
                "http://127.0.0.1:80/api/v2/network/vnic_group_snapshot",
                json={
                    "cluster_uuid": self._cluster_uuid,
                    "vnic_group_snapshot_uuid": self._vnic_group_snapshot_uuid,
                    "vnic_group_uuid": self._vnic_group_uuid,
                    "vnics": self._vnics,
                }
            )

    def test_create_vpc_nic_snapshot_raise_error(self):
        with mock.patch.object(restful_client.Client, "post") as mock_post:
            client = network.Client()
            mock_post.return_value = {"data": {}, "ec": "REST_API_HTTP_ERROR", "error": {}}

            with pytest.raises(restful_client.rtf_exceptions):
                client.create_vpc_nic_snapshots(
                    self._cluster_uuid, self._vnic_group_snapshot_uuid, self._vnic_group_uuid,  self._vnics
                )

    def test_delete_vpc_nic_snapshot(self):
        with mock.patch.object(restful_client.Client, "delete") as mock_delete:
            client = network.Client()
            mock_delete.return_value = {"data": self._delete_response_data, "ec": "EOK", "error": {}}

            result = client.delete_vpc_nic_snapshots(
                self._vnic_group_snapshot_uuid, [n["vnic_snapshot_uuid"] for n in self._vnics]
            )

            assert len(result) == 2
            for r in result:
                assert "reason" in r
                assert "code" in r

    def test_delete_vpc_nic_snapshot_raise_error(self):
        with mock.patch.object(restful_client.Client, "delete") as mock_delete:
            client = network.Client()
            mock_delete.return_value = {"data": {}, "ec": "REST_API_HTTP_ERROR", "error": {}}

            with pytest.raises(restful_client.rtf_exceptions):
                client.delete_vpc_nic_snapshots(
                    self._vnic_group_snapshot_uuid, [n["vnic_snapshot_uuid"] for n in self._vnics]
                )

    def test_rollback_vpc_nics(self):
        with mock.patch.object(restful_client.Client, "post") as mock_post:
            client = network.Client()
            mock_post.return_value = {"data": self._rollback_response_data, "ec": "EOK", "error": {}}

            result = client.rollback_vpc_nics(
                self._vnic_group_snapshot_uuid, [n["vnic_snapshot_uuid"] for n in self._vnics]
            )

            assert len(result) == 2
            for r in result:
                assert "code" in r
                assert "reason" in r

    def test_rollback_vpc_nics_raise_error(self):
        with mock.patch.object(restful_client.Client, "post") as mock_post:
            client = network.Client()
            mock_post.return_value = {"data": {}, "ec": "REST_API_HTTP_ERROR", "error": {}}

            with pytest.raises(restful_client.rtf_exceptions):
                client.rollback_vpc_nics(
                    self._vnic_group_snapshot_uuid, [n["vnic_snapshot_uuid"] for n in self._vnics]
                )


def test_is_cluster_associated_to_vpc_service():
    with mock.patch.object(restful_client.Client, "get") as mock_get:
        vds_list = [
            {
                "name": "vds-ovsbr-internal",
                "ovsbr_name": "ovsbr-internal",
                "qos_enable": True,
                "type": 2,
                "uuid": "82aa565c-36b1-4ae1-b72a-97249cd63fdf",
            },
            {
                "name": "vpcbr",
                "ovsbr_name": "vpcbr",
                "qos_enable": True,
                "type": 3,
                "uuid": "82aa565c-36b1-4ae1-b72a-97249cd63fdf",
            }
        ]
        mock_get.return_value = {"ec": "EOK", "error": {}, "data": vds_list}

        client = network.Client()
        assert client.is_cluster_associated_to_vpc_service() is True

        vds_list = [
            {
                "name": "vds-ovsbr-internal",
                "ovsbr_name": "ovsbr-internal",
                "qos_enable": True,
                "type": 2,
                "uuid": "82aa565c-36b1-4ae1-b72a-97249cd63fdf",
            }
        ]
        mock_get.return_value = {"ec": "EOK", "error": {}, "data": vds_list}
        assert client.is_cluster_associated_to_vpc_service() is False


def test_get_all_vpc_nics():
    with mock.patch.object(restful_client.Client, "get") as mock_get:
        cluster_uuid = str(uuid.uuid4())
        vm_uuid1 = str(uuid.uuid4())
        vm_uuid2 = str(uuid.uuid4())
        vpc_uuid = str(uuid.uuid4())
        subnet_uuid = str(uuid.uuid4())
        vnic1 = {
            "vnic_uuid": str(uuid.uuid4()),
            "vnic_group_uuid": vm_uuid1,
            "cluster_uuid": cluster_uuid,
            "vpc_uuid": vpc_uuid,
            "subnet_uuid": subnet_uuid,
            "ipv4_addr": "",
            "mac_addr": "52:54:00:37:00:7a",
            "floating_ip_uuid": ""
        }
        vnic2 = {
            "vnic_uuid": str(uuid.uuid4()),
            "vnic_group_uuid": vm_uuid1,
            "cluster_uuid": cluster_uuid,
            "vpc_uuid": vpc_uuid,
            "subnet_uuid": subnet_uuid,
            "ipv4_addr": "",
            "mac_addr": "52:54:00:37:00:7b",
            "floating_ip_uuid": ""
        }
        vnic3 = {
            "vnic_uuid": str(uuid.uuid4()),
            "vnic_group_uuid": vm_uuid2,
            "cluster_uuid": cluster_uuid,
            "vpc_uuid": vpc_uuid,
            "subnet_uuid": subnet_uuid,
            "ipv4_addr": "",
            "mac_addr": "52:54:00:37:00:7c",
            "floating_ip_uuid": ""
        }
        mock_get.side_effect = [
            {"ec": "EOK", "error": {}, "data": {"vnics": [vnic1, vnic2], "continue": "test_continue_str"}},
            {"ec": "EOK", "error": {}, "data": {"vnics": [vnic3]}},
        ]

        client = network.Client()
        result = client.get_all_vpc_nics_relation(cluster_uuid)

        assert len(result) == 2
        assert set(result[vm_uuid1]) == {vnic1["vnic_uuid"], vnic2["vnic_uuid"]}
        assert result[vm_uuid2] == [vnic3["vnic_uuid"]]


def test_get_all_vnic_snapshots():
    with mock.patch.object(restful_client.Client, "get") as mock_get:
        cluster_uuid = str(uuid.uuid4())
        vm_uuid1 = str(uuid.uuid4())
        vm_uuid2 = str(uuid.uuid4())
        vm_snapshot_uuid1 = str(uuid.uuid4())
        vm_snapshot_uuid2 = str(uuid.uuid4())
        vpc_uuid = str(uuid.uuid4())
        subnet_uuid = str(uuid.uuid4())
        vnic_snapshot1 = {
            "vnic_snapshot_uuid": str(uuid.uuid4()),
            "vnic_group_snapshot_uuid": vm_snapshot_uuid1,
            "vnic_group_uuid": vm_uuid1,
            "ipv4_addr": "***********",
            "mac_addr": "52:54:00:78:36:c2",
            "vpc_uuid": vpc_uuid,
            "vnic_uuid": str(uuid.uuid4()),
            "subnet_uuid": subnet_uuid,
            "floating_ip_uuid": ""
        }
        vnic_snapshot2 = {
            "vnic_snapshot_uuid": str(uuid.uuid4()),
            "vnic_group_snapshot_uuid": vm_snapshot_uuid1,
            "vnic_group_uuid": vm_uuid1,
            "ipv4_addr": "***********",
            "mac_addr": "52:54:00:78:36:c3",
            "vpc_uuid": vpc_uuid,
            "vnic_uuid": str(uuid.uuid4()),
            "subnet_uuid": subnet_uuid,
            "floating_ip_uuid": ""
        }
        vnic_snapshot3 = {
            "vnic_snapshot_uuid": str(uuid.uuid4()),
            "vnic_group_snapshot_uuid": vm_snapshot_uuid2,
            "vnic_group_uuid": vm_uuid2,
            "ipv4_addr": "***********",
            "mac_addr": "52:54:00:78:36:c4",
            "vpc_uuid": vpc_uuid,
            "vnic_uuid": str(uuid.uuid4()),
            "subnet_uuid": subnet_uuid,
            "floating_ip_uuid": ""
        }
        mock_get.side_effect = [
            {
                "ec": "EOK",
                "error": {},
                "data": {
                    "data": {"vnic_snapshots": [vnic_snapshot1, vnic_snapshot2]},
                    "continue": "test_continue_str",
                },
            },
            {
                "ec": "EOK",
                "error": {},
                "data": {
                    "data": {"vnic_snapshots": [vnic_snapshot3]},
                },
            }
        ]

        client = network.Client()
        result = client.get_all_vpc_nic_snapshots_relation(cluster_uuid)

        assert len(result) == 2
        assert set(result[vm_snapshot_uuid1]) == {
            vnic_snapshot1["vnic_snapshot_uuid"], vnic_snapshot2["vnic_snapshot_uuid"]
        }
        assert result[vm_snapshot_uuid2] == [vnic_snapshot3["vnic_snapshot_uuid"]]


def test_get_firewall_items():
    with mock.patch.object(restful_client.Client, "get") as mock_get:
        test_items = {"access_ip": "0.0.0.0", "access_item": {}}
        mock_get.side_effect = [
            {
                "ec": "EOK",
                "error": {},
                "data": test_items,
            },
            {
                "ec": "ERR",
                "error": {},
                "data": {},
            },
        ]

        client = network.Client()
        items = client.get_firewall_items()

        mock_get.assert_called_once_with(
            "http://127.0.0.1:80/api/v2/network/network-firewall/all"
        )
        assert items == test_items

        with pytest.raises(restful_client.rtf_exceptions):
            client.get_firewall_items()


def test_add_firewall_item():
    with mock.patch.object(restful_client.Client, "put") as mock_put:
        mock_put.return_value = {
            "ec": "EOK",
            "error": {},
            "data": {},
        }

        client = network.Client()

        item = {"dst_port": "*******"}
        client.add_firewall_item(item)

        mock_put.assert_called_once_with(
            "http://127.0.0.1:80/api/v2/network/network-firewall/item",
            json=[item],
        )


def test_remove_firewall_item():
    with mock.patch.object(restful_client.Client, "delete") as mock_delete:
        mock_delete.return_value = {
            "ec": "EOK",
            "error": {},
            "data": {},
        }

        client = network.Client()

        item = {"dst_port": "*******"}
        client.remove_firewall_item(item)

        mock_delete.assert_called_once_with(
            "http://127.0.0.1:80/api/v2/network/network-firewall/item",
            json=[item],
        )
