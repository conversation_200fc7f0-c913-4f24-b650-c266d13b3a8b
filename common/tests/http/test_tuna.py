# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
import unittest
from unittest.mock import patch

import pytest
from requests import Session

from common.http import restful_client as rtf_client
from common.http.exceptions import RestfulClientError as rtf_exceptions
from common.http.tuna import Client
from common.tests.http import MockResponse

host_id = "vvvv"
pf_id = "uuuu"
assign_ids = ["qqqq-bbb", "wwww-mmm"]
hosts = [
    {
        "host_uuid": host_id,
        "nics": [
            {
                "bonding_detail": None,
                "is_rdma_support": False,
                "up": False,
                "bonding_master": None,
                "gateway": None,
                "is_sriov_support": True,
                "uuid": pf_id,
                "speed": None,
                "ipaddr": "",
                "sriov": {
                    "assigned_vfs": [
                        {"index": 0, "assign_id": "09c3563e-9d6e-43a4-a59b-4236f6cf4545_52:54:00:5b:4a:89"},
                        {"index": 1, "assign_id": "13b4be1f-adbe-4471-8d2c-5d47ca1b2102_52:54:00:69:42:0e"},
                        {"index": 2, "assign_id": "dfd26626-702c-446c-b7af-06ead1a10f1a_52:54:00:99:c1:4e"},
                    ],
                    "name": "enp8s0f0",
                    "numvfs": 15,
                    "uuid": pf_id,
                    "actual_totalvfs": 64,
                    "vfs": [],
                    "actual_numvfs": 15,
                    "update_version": 22,
                    "host_uuid": "d9b642d6-e4b6-11eb-94d4-525400965d29",
                    "totalvfs": 64,
                },
                "serial_number": "",
                "vendor_id": "0x8086",
                "ibdev": None,
                "driver": "i40e",
                "driver_state": "ready",
                "running": False,
                "rdma": False,
                "hwaddr": "f8:f2:1e:d4:a1:9e",
                "bonding_mode": None,
                "vlan": None,
                "name": "enp8s0f0",
                "product_id": "0x37d0",
                "iommu_group": 28,
                "numa_node": "0",
                "netmask": "",
                "mtu": 1500,
                "model": "Intel Corporation Ethernet Connection X722 for 10GbE SFP+ (rev 04)",
                "is_physical": True,
                "pci_slot_name": "0000:08:00",
            }
        ],
    }
]

usb_id = "xx-xx-xx-xx-xx-xx"
usb = {
    "host_uuid": host_id,
    "device_id": usb_id,
    "serial": "xxxxxxxxxxxx",
    "name": "Teclast CoolFlash",
    "vendor_id": "0x10de",
    "product_id": "0x1db4",
    "sys_path": "/sys/devices/pci0000:00/0000:00:14.0/usb1/1-10",
    "usb_type": "2.00",
    "function": "2",
    "devname": "/dev/bus/usb/001/006",
    "busnum": 1,
    "devnum": 6,
    "speed": 480,
    "manufacturer": "Teclast",
    "size": 8001563222016,
    "creat_time": 1662635578,
    "update_time": 1662635578,
}
usb_assign_info = {
    "device_id": usb_id,
    "host_uuid": host_id,
    "update_version": 123,
    "type": "usb",
    "assign_type": "pass-through",
    "assignable": False,
    "assign_id": "",
    "info": {"server": "*******", "port": "8000"},
}


class TestTunaRestClient:
    def test_get_pf(self):
        sriov_pf = {
            "name": "ens1f0",
            "uuid": "uuuu",
            "is_sriov": True,
            "driver_state": "ready",
            "sriov": {
                "totalvfs": 32,
                "numvfs": 4,
                "vfs": [
                    {"index": 0, "domain": "0000", "bus": "86", "slot": "00", "function": "2"},
                    {"index": 1, "domain": "0000", "bus": "86", "slot": "00", "function": "3"},
                ],
                "assigned_vfs": [],
            },
        }
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(json={"ec": "EOK", "error": {}, "data": sriov_pf})
            result = Client().get_pf(host_id, pf_id)
            assert result == sriov_pf["sriov"]
            call_args, _ = mock_get.call_args
            url = "http://127.0.0.1:80/api/v2/management/hosts/{}/nics/{}/get_sriov_info".format(host_id, pf_id)
            assert url in call_args

        with pytest.raises(rtf_exceptions):
            Client().get_pf(host_id, pf_id)

    def test_assign_vfs(self):
        vfs = [
            {"index": 0, "domain": "0000", "bus": "86", "slot": "00", "function": "2"},
            {"index": 1, "domain": "0000", "bus": "86", "slot": "00", "function": "3"},
        ]
        with patch.object(Session, "post") as mock_post:
            mock_post.return_value = MockResponse(json={"data": {"vfs": vfs}, "ec": "EOK", "error": {}})
            result = Client().assign_vfs(host_id, pf_id, assign_ids)
            assert result == vfs
            call_args, call_kwargs = mock_post.call_args
            url = "http://127.0.0.1:80/api/v2/management/hosts/{}/nics/{}/assign_vfs".format(host_id, pf_id)
            assert url in call_args
            assert call_kwargs["json"] == {"assign_ids": assign_ids}

            mock_post.return_value = MockResponse(json={"data": {}, "ec": "REST_API_HTTP_ERROR", "error": {}})
            with pytest.raises(rtf_exceptions):
                Client().assign_vfs(host_id, pf_id, assign_ids)

    def test_release_vfs(self):
        with patch.object(Session, "post") as mock_post:
            mock_post.return_value = MockResponse(json={"data": {}, "ec": "EOK", "error": {}})
            Client().release_vfs(host_id, pf_id, assign_ids)
            call_args, call_kwargs = mock_post.call_args
            url = "http://127.0.0.1:80/api/v2/management/hosts/{}/nics/{}/release_vfs".format(host_id, pf_id)
            assert url in call_args
            assert call_kwargs["json"] == {"release_ids": assign_ids}

            mock_post.return_value = MockResponse(json={"data": {}, "ec": "REST_API_HTTP_ERROR", "error": {}})
            with pytest.raises(rtf_exceptions):
                Client().release_vfs(host_id, pf_id, assign_ids)

    def test_get_cluster_pfs(self):
        with (
            patch.object(Client, "get_host_pfs") as mock_get_host_pfs,
            patch.object(Client, "get_management_hosts") as mock_get_management_hosts,
        ):
            mock_get_management_hosts.return_value = [{"host_uuid": host_id}]
            mock_get_host_pfs.return_value = [{"is_sriov_support": True}]
            assert Client().get_cluster_pfs() == {host_id: [{"is_sriov_support": True}]}

    def test_get_pfs_by_host_uuid(self):
        with patch.object(Client, "get_nic_assign_infos") as mock_get_nic_assign_infos:
            mock_get_nic_assign_infos.return_value = [{"is_sriov_support": True}, {"is_sriov_support": False}]
            res = Client().get_host_pfs(host_id)
            assert len(res) == 1

    def test_get_node_cpus(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(
                json={
                    "data": [
                        {
                            "cgroup": {
                                "qemu_cpu_used_ids": "6-19",
                                "chunk_numa_node": 0,
                                "chunk_numa_free_cpus": [6, 7, 8],
                                "chunk_numa_weak_cpus": [9, 10],
                                "chunk_dynamic_cpus": [11],
                            },
                            "host_uuid": "host_uuid_1",
                            "numa_topology": {
                                "cpus": [
                                    {"socket_id": 0, "core_id": 0, "numa_node": 0, "thread_siblings_list": [0, 10]},
                                    {"socket_id": 0, "core_id": 0, "numa_node": 1, "thread_siblings_list": [1, 11]},
                                    {"socket_id": 0, "core_id": 1, "numa_node": 0, "thread_siblings_list": [2, 12]},
                                    {"socket_id": 0, "core_id": 1, "numa_node": 1, "thread_siblings_list": [3, 13]},
                                    {"socket_id": 0, "core_id": 2, "numa_node": 0, "thread_siblings_list": [4, 14]},
                                    {"socket_id": 0, "core_id": 2, "numa_node": 1, "thread_siblings_list": [5, 15]},
                                    {"socket_id": 0, "core_id": 3, "numa_node": 0, "thread_siblings_list": [6, 16]},
                                    {"socket_id": 0, "core_id": 3, "numa_node": 1, "thread_siblings_list": [7, 17]},
                                    {"socket_id": 0, "core_id": 4, "numa_node": 0, "thread_siblings_list": [8, 18]},
                                    {"socket_id": 0, "core_id": 4, "numa_node": 1, "thread_siblings_list": [9, 19]},
                                ],
                                "node_distances": [
                                    {
                                        "node_id": 0,
                                        "target_nodes": [{"distance": 0, "id": 0}, {"distance": 10, "id": 1}],
                                    },
                                    {
                                        "node_id": 1,
                                        "target_nodes": [{"distance": 0, "id": 1}, {"distance": 10, "id": 0}],
                                    },
                                ],
                            },
                        },
                        {
                            "cgroup": {
                                "qemu_cpu_used_ids": "6-19",
                                "chunk_numa_node": 0,
                                "chunk_numa_free_cpus": [6, 7, 8],
                                "chunk_numa_weak_cpus": [9, 10],
                                "chunk_dynamic_cpus": [11],
                            },
                            "host_uuid": "host_uuid_2",
                            "numa_topology": {
                                "cpus": [
                                    {"socket_id": 0, "core_id": 0, "numa_node": 0, "thread_siblings_list": [0, 10]},
                                    {"socket_id": 0, "core_id": 0, "numa_node": 1, "thread_siblings_list": [1, 11]},
                                    {"socket_id": 0, "core_id": 1, "numa_node": 0, "thread_siblings_list": [2, 12]},
                                    {"socket_id": 0, "core_id": 1, "numa_node": 1, "thread_siblings_list": [3, 13]},
                                    {"socket_id": 0, "core_id": 2, "numa_node": 0, "thread_siblings_list": [4, 14]},
                                    {"socket_id": 0, "core_id": 2, "numa_node": 1, "thread_siblings_list": [5, 15]},
                                    {"socket_id": 0, "core_id": 3, "numa_node": 0, "thread_siblings_list": [6, 16]},
                                    {"socket_id": 0, "core_id": 3, "numa_node": 1, "thread_siblings_list": [7, 17]},
                                    {"socket_id": 0, "core_id": 4, "numa_node": 0, "thread_siblings_list": [8, 18]},
                                    {"socket_id": 0, "core_id": 4, "numa_node": 1, "thread_siblings_list": [9, 19]},
                                ],
                                "node_distances": [
                                    {
                                        "node_id": 0,
                                        "target_nodes": [{"distance": 0, "id": 0}, {"distance": 10, "id": 1}],
                                    },
                                    {
                                        "node_id": 1,
                                        "target_nodes": [{"distance": 0, "id": 1}, {"distance": 10, "id": 0}],
                                    },
                                ],
                            },
                        },
                        {
                            "cgroup": {
                                "qemu_cpu_used_ids": "6-19",
                                "chunk_numa_node": 0,
                                "chunk_numa_free_cpus": [6, 7, 8],
                                "chunk_numa_weak_cpus": [9, 10],
                                "chunk_dynamic_cpus": [11],
                            },
                            "host_uuid": "host_uuid_3",
                            "numa_topology": {
                                "cpus": [
                                    {"socket_id": 0, "core_id": 0, "numa_node": 0, "thread_siblings_list": [0, 10]},
                                    {"socket_id": 0, "core_id": 0, "numa_node": 1, "thread_siblings_list": [1, 11]},
                                    {"socket_id": 0, "core_id": 1, "numa_node": 0, "thread_siblings_list": [2, 12]},
                                    {"socket_id": 0, "core_id": 1, "numa_node": 1, "thread_siblings_list": [3, 13]},
                                    {"socket_id": 0, "core_id": 2, "numa_node": 0, "thread_siblings_list": [4, 14]},
                                    {"socket_id": 0, "core_id": 2, "numa_node": 1, "thread_siblings_list": [5, 15]},
                                    {"socket_id": 0, "core_id": 3, "numa_node": 0, "thread_siblings_list": [6, 16]},
                                    {"socket_id": 0, "core_id": 3, "numa_node": 1, "thread_siblings_list": [7, 17]},
                                    {"socket_id": 0, "core_id": 4, "numa_node": 0, "thread_siblings_list": [8, 18]},
                                    {"socket_id": 0, "core_id": 4, "numa_node": 1, "thread_siblings_list": [9, 19]},
                                ],
                                "node_distances": [
                                    {
                                        "node_id": 0,
                                        "target_nodes": [{"distance": 0, "id": 0}, {"distance": 10, "id": 1}],
                                    },
                                    {
                                        "node_id": 1,
                                        "target_nodes": [{"distance": 10, "id": 0}, {"distance": 0, "id": 1}],
                                    },
                                ],
                            },
                        },
                    ],
                    "ec": "EOK",
                    "error": {},
                }
            )
            result = Client().get_node_cpu_info()

            expected_host_info = {
                "qemu_cpu_used_ids": "6-19",
                "node_cpu_list": {0: [0, 10, 2, 12, 4, 14, 6, 16, 8, 18], 1: [1, 11, 3, 13, 5, 15, 7, 17, 9, 19]},
                "node_distance": {0: {0: 0, 1: 10}, 1: {0: 10, 1: 0}},
                "chunk_numa_node": 0,
                "chunk_numa_free_cpus": [6, 7, 8],
                "chunk_numa_weak_cpus": [9, 10],
                "chunk_socket": 0,
                "chunk_dynamic_cpus": [11],
                "socket_cpu_list": {0: [0, 10, 1, 11, 2, 12, 3, 13, 4, 14, 5, 15, 6, 16, 7, 17, 8, 18, 9, 19]},
                "thread_siblings": {
                    0: [0, 10],
                    1: [1, 11],
                    2: [2, 12],
                    3: [3, 13],
                    4: [4, 14],
                    5: [5, 15],
                    6: [6, 16],
                    7: [7, 17],
                    8: [8, 18],
                    9: [9, 19],
                    10: [0, 10],
                    11: [1, 11],
                    12: [2, 12],
                    13: [3, 13],
                    14: [4, 14],
                    15: [5, 15],
                    16: [6, 16],
                    17: [7, 17],
                    18: [8, 18],
                    19: [9, 19],
                },
                "socket_to_numa": {0: {0, 1}}
            }

            assert result == {
                "host_uuid_1": expected_host_info,
                "host_uuid_2": expected_host_info,
                "host_uuid_3": expected_host_info,
            }, result

    def test_get_node_cpus_raise_key_error(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(
                json={
                    "data": [
                        {
                            "host_uuid": "host_uuid_1",
                            "numa_topology": {
                                "cpus": [
                                    {"core_id": 0, "numa_node": 0, "thread_siblings_list": [0, 10]},
                                    {"core_id": 0, "numa_node": 1, "thread_siblings_list": [1, 11]},
                                    {"core_id": 1, "numa_node": 0, "thread_siblings_list": [2, 12]},
                                    {"core_id": 1, "numa_node": 1, "thread_siblings_list": [3, 13]},
                                    {"core_id": 2, "numa_node": 0, "thread_siblings_list": [4, 14]},
                                    {"core_id": 2, "numa_node": 1, "thread_siblings_list": [5, 15]},
                                    {"core_id": 3, "numa_node": 0, "thread_siblings_list": [6, 16]},
                                    {"core_id": 3, "numa_node": 1, "thread_siblings_list": [7, 17]},
                                    {"core_id": 4, "numa_node": 0, "thread_siblings_list": [8, 18]},
                                    {"core_id": 4, "numa_node": 1, "thread_siblings_list": [9, 19]},
                                ],
                                "node_distances": [
                                    {
                                        "node_id": 0,
                                        "target_nodes": [{"distance": 0, "id": 0}, {"distance": 10, "id": 1}],
                                    },
                                    {
                                        "node_id": 1,
                                        "target_nodes": [{"distance": 10, "id": 0}, {"distance": 0, "id": 1}],
                                    },
                                ],
                            },
                        },
                        {"cgroup": {"qemu_cpu_used_ids": "6-19"}, "host_uuid": "host_uuid_2"},
                        {
                            "cgroup": {"qemu_cpu_used_ids": "6-19"},
                            "host_uuid": "host_uuid_3",
                            "numa_topology": {
                                "cpus": [
                                    {"core_id": 0, "numa_node": 0, "thread_siblings_list": [0, 10]},
                                    {"core_id": 0, "numa_node": 1, "thread_siblings_list": [1, 11]},
                                    {"core_id": 1, "numa_node": 0, "thread_siblings_list": [2, 12]},
                                    {"core_id": 1, "numa_node": 1, "thread_siblings_list": [3, 13]},
                                    {"core_id": 2, "numa_node": 0, "thread_siblings_list": [4, 14]},
                                    {"core_id": 2, "numa_node": 1, "thread_siblings_list": [5, 15]},
                                    {"core_id": 3, "numa_node": 0, "thread_siblings_list": [6, 16]},
                                    {"core_id": 3, "numa_node": 1, "thread_siblings_list": [7, 17]},
                                    {"core_id": 4, "numa_node": 0, "thread_siblings_list": [8, 18]},
                                    {"core_id": 4, "numa_node": 1, "thread_siblings_list": [9, 19]},
                                ],
                                "node_distances": [
                                    {
                                        "node_id": 0,
                                        "target_nodes": [{"distance": 0, "id": 0}, {"distance": 10, "id": 1}],
                                    },
                                    {
                                        "node_id": 1,
                                        "target_nodes": [{"distance": 10, "id": 0}, {"distance": 0, "id": 1}],
                                    },
                                ],
                            },
                        },
                    ],
                    "ec": "EOK",
                    "error": {},
                }
            )

            result = Client().get_node_cpu_info()

            assert result == {
                "host_uuid_2": {"qemu_cpu_used_ids": "6-19"},
                "host_uuid_3": {"qemu_cpu_used_ids": "6-19"},
            }

    def test_get_cpu_topology(self):
        with patch.object(Session, "get") as mock_get:
            cpu_topology = [
                {"core_id": 0, "numa_node": 0, "thread_siblings_list": [0, 10]},
                {"core_id": 0, "numa_node": 1, "thread_siblings_list": [1, 11]},
                {"core_id": 1, "numa_node": 0, "thread_siblings_list": [2, 12]},
                {"core_id": 1, "numa_node": 1, "thread_siblings_list": [3, 13]},
                {"core_id": 2, "numa_node": 0, "thread_siblings_list": [4, 14]},
                {"core_id": 2, "numa_node": 1, "thread_siblings_list": [5, 15]},
                {"core_id": 3, "numa_node": 0, "thread_siblings_list": [6, 16]},
                {"core_id": 3, "numa_node": 1, "thread_siblings_list": [7, 17]},
                {"core_id": 4, "numa_node": 0, "thread_siblings_list": [8, 18]},
                {"core_id": 4, "numa_node": 1, "thread_siblings_list": [9, 19]},
            ]
            mock_get.return_value = MockResponse(
                json={
                    "data": [{"host_uuid": "host_uuid_1", "numa_topology": {"cpus": cpu_topology}}],
                    "ec": "EOK",
                    "error": {},
                }
            )

            result = Client().get_cpu_topology()

            assert result == {"host_uuid_1": cpu_topology}

    def test_get_host_labels_and_version(self):
        with patch.object(Session, "get") as mock_get:
            exist_labels = {"key1": "value1", "key2": "value2"}
            mock_get.return_value = MockResponse(
                json={"data": {"labels": exist_labels, "version": 1}, "ec": "EOK", "error": {}}
            )
            result = Client().get_host_labels_and_version("host_uuid_1")
            assert result == (exist_labels, 1)

            mock_get.return_value = MockResponse(json={"data": {}, "ec": "REST_API_HTTP_ERROR", "error": {}})
            with pytest.raises(rtf_exceptions):
                Client().get_host_labels_and_version("host_uuid_1")

    def test_set_host_labels(self):
        with patch.object(Session, "post") as mock_post:
            updated_labels = {"key1": "value1", "key2": "value2"}
            mock_post.return_value = MockResponse(
                json={"data": {"labels": updated_labels, "version": 2}, "ec": "EOK", "error": {}}
            )
            assert Client().set_host_labels("host_uuid_1", updated_labels, 1) is None

            mock_post.return_value = MockResponse(json={"data": {}, "ec": "REST_API_HTTP_ERROR", "error": {}})
            with pytest.raises(rtf_exceptions):
                Client().set_host_labels("host_uuid_1", updated_labels, 2)

    def test_get_host_services(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(
                {
                    "data": [
                        {
                            "current_pid": 0,
                            "exec_path": "/usr/bin/job-center",
                            "last_pid": 57602,
                            "running_state": "inactive",
                            "service_name": "job-center-scheduler.service",
                            "service_state": "enabled",
                            "state_duration": 72057.70942,
                        },
                        {
                            "current_pid": 21786,
                            "exec_path": "/usr/bin/vmtools-agent",
                            "last_pid": 21786,
                            "running_state": "active",
                            "service_name": "vmtools-agent.service",
                            "service_state": "enabled",
                            "state_duration": 92155.714917,
                        },
                    ],
                    "ec": "EOK",
                    "error": {},
                }
            )
            result = Client().get_host_services()
            assert len(result) == 2
            assert result[0]["service_name"] == "job-center-scheduler.service"
            assert result[1]["service_name"] == "vmtools-agent.service"

    def test_prepare_check_for_mt_mode(self):
        with patch.object(Session, "post") as mock_post:
            # Test with default parameters
            mock_post.return_value = MockResponse(json={"data": {"job_id": "job_id1"}, "ec": "EOK", "error": {}})
            Client().prepare_check_for_mt_mode(host_uuid="host_uuid")
            call_args, call_kwargs = mock_post.call_args
            url = "http://127.0.0.1:80/api/v2/management/hosts/host_uuid/enter_mt_check"
            assert url in call_args
            assert "json" in call_kwargs
            assert call_kwargs.get("json") == {
                "check_recover_multi_times": True,
                "ignore_user_vm_pg_rule": False,
                "system_vms": [],
            }

            # Test with all parameters specified
            system_vms = [
                {
                    "vm_name": "yiran-test-controlplane-sp924",
                    "vm_uuid": "ac869655-ba41-4ff7-bd01-f360a7e14267",
                    "vm_usage": "test",
                },
                {
                    "vm_name": "yiran-test-workergroup1-v9qft",
                    "vm_uuid": "4dcc27f2-426a-45fe-b009-315800c2f347",
                    "vm_usage": "test",
                },
            ]
            Client().prepare_check_for_mt_mode(
                host_uuid="host_uuid",
                check_recover_multi_times=False,
                ignore_user_vm_pg_rule=True,
                system_vms=system_vms,
            )
            call_args, call_kwargs = mock_post.call_args
            assert url in call_args
            assert call_kwargs.get("json") == {
                "check_recover_multi_times": False,
                "ignore_user_vm_pg_rule": True,
                "system_vms": system_vms,
            }

            # Test error case
            mock_post.return_value = MockResponse(json={"data": {}, "ec": "REST_API_HTTP_ERROR", "error": {}})
            with pytest.raises(rtf_exceptions):
                Client().prepare_check_for_mt_mode(host_uuid="host_uuid")

    def test_get_job(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(
                status_code=200, json={"ec": "EOK", "data": {"job": {"job_id": "job_id"}}}
            )
            client = Client()
            result = client.get_job("job_id")
            url = "http://127.0.0.1:80/api/v2/jobs/job_id"
            call_args, call_kwargs = mock_get.call_args
            assert url in call_args
            assert result == {"job_id": "job_id"}

    def test_get_running_jobs(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(
                status_code=200, json={"ec": "EOK", "data": {"jobs": [{"job_id": "job_id"}]}}
            )
            client = Client()
            result = client.get_running_jobs()
            url = "http://127.0.0.1:80/api/v2/jobs"
            call_args, call_kwargs = mock_get.call_args
            assert url in call_args
            assert result == [{"job_id": "job_id"}]

    def test_wait_job_done(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(
                status_code=200, json={"ec": "EOK", "data": {"job": {"job_id": "job_id", "state": "done"}}}
            )
            client = Client()
            result = client.wait_job_done("job_id")
            url = "http://127.0.0.1:80/api/v2/jobs/job_id"
            call_args, call_kwargs = mock_get.call_args
            assert url in call_args
            assert result

            mock_get.return_value = MockResponse(
                status_code=200, json={"ec": "EOK", "data": {"job": {"job_id": "job_id", "state": "failed"}}}
            )
            client = Client()
            result = client.wait_job_done("job_id")
            url = "http://127.0.0.1:80/api/v2/jobs/job_id"
            call_args, call_kwargs = mock_get.call_args
            assert url in call_args
            assert not result

            mock_get.return_value = MockResponse(
                status_code=200, json={"ec": "EOK", "data": {"job": {"job_id": "job_id", "state": "test"}}}
            )
            with patch("common.http.tuna.sleep") as mock_sleep:
                client = Client()
                result = client.wait_job_done("job_id")
                url = "http://127.0.0.1:80/api/v2/jobs/job_id"
                call_args, call_kwargs = mock_get.call_args
                assert url in call_args
                mock_sleep.assert_called_with(5)
                mock_sleep.call_count == 360
                assert not result

    @patch.object(rtf_client.Client, "get_token", return_value="token")
    def test_get_settings_cluster(self, mock_get_token):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(
                status_code=200,
                json={"data": {"cluster_id": "test", "cluster_name": "510-test"}, "ec": "EOK", "error": {}},
            )

            result = Client().get_settings_cluster()
            assert result
            call_args, _ = mock_get.call_args
            url = "http://127.0.0.1:80/api/v2/settings/cluster"
            assert url in call_args
            assert mock_get_token.called


class TestTunaGPUFunctions(unittest.TestCase):
    def setUp(self):
        self.client = Client()

    @patch.object(Session, "post")
    def test_allocate_gpu(self, mock_post):
        mock_post.return_value = MockResponse(json={"data": {}, "ec": "EOK", "error": ""})
        assert self.client.allocate_gpu("vm_uuid_1", "host_uuid_1", "gpu_uuid_1") is None

    @patch.object(Session, "post")
    def test_allocate_gpu_raise_error(self, mock_post):
        mock_post.return_value = MockResponse(json={"data": {}, "ec": "REST_API_HTTP_ERROR", "error": ""})
        with pytest.raises(rtf_exceptions):
            self.client.allocate_gpu("vm_uuid_1", "host_uuid_1", "gpu_uuid_1")

    @patch.object(Session, "post")
    def test_release_gpu(self, mock_post):
        mock_post.return_value = MockResponse(json={"data": {}, "ec": "EOK", "error": ""})
        assert self.client.release_gpu("vm_uuid_1", "host_uuid_1", "gpu_uuid_1") is None

    @patch.object(Session, "post")
    def test_release_gpu_raise_error(self, mock_post):
        mock_post.return_value = MockResponse(json={"data": {}, "ec": "REST_API_HTTP_ERROR", "error": ""})
        with pytest.raises(rtf_exceptions):
            self.client.release_gpu("vm_uuid_1", "host_uuid_1", "gpu_uuid_1")

    @patch.object(Session, "get")
    def test_get_gpu_info(self, mock_get):
        mock_get.return_value = MockResponse(json={"data": {"bus_location": "0000:2f:00.0"}, "ec": "EOK", "error": ""})
        result = self.client.get_gpu_info("host_uuid_1", "gpu_uuid_1")
        assert result["bus_location"] == "0000:2f:00.0"

    @patch.object(Session, "get")
    def test_get_gpu_info_raise_error(self, mock_get):
        mock_get.return_value = MockResponse(json={"data": {}, "ec": "REST_API_HTTP_ERROR", "error": ""})
        with pytest.raises(rtf_exceptions):
            self.client.get_gpu_info("host_uuid_1", "gpu_uuid_1")

    @patch.object(Session, "get")
    def test_get_gpu_infos(self, mock_get):
        mock_get.return_value = MockResponse(
            json={"data": {"gpus": [{"name": "Tesla T4"}, {"name": "Tesla V100"}]}, "ec": "EOK", "error": ""}
        )
        result = self.client.get_gpu_infos("host_uuid_1")
        assert result == [{"name": "Tesla T4"}, {"name": "Tesla V100"}]

    @patch.object(Session, "get")
    def test_get_gpu_infos_raise_error(self, mock_get):
        mock_get.return_value = MockResponse(json={"data": {}, "ec": "REST_API_HTTP_ERROR", "error": ""})
        with pytest.raises(rtf_exceptions):
            self.client.get_gpu_infos("host_uuid_1")

    @patch.object(Session, "get")
    def test_get_gpu_assign_infos(self, mock_get):
        assign_infos = [{"host_uuid": "host_uuid_1", "device_uuid": "gpu_uuid_1", "assign_id": "vm_uuid_1"}]
        mock_get.return_value = MockResponse(
            json={"data": {"gpu_assign_infos": assign_infos}, "ec": "EOK", "error": ""}
        )

        result = self.client.get_gpu_assign_infos("host_uuid_1")
        assert result == assign_infos


class TestTunaNICFunctions(unittest.TestCase):
    def setUp(self):
        self.client = Client()

    @patch.object(Session, "post")
    def test_allocate_nic(self, mock_post):
        mock_post.return_value = MockResponse(json={"data": {}, "ec": "EOK", "error": ""})
        assert self.client.allocate_nic("vm_uuid_1", "host_uuid_1", "nic_uuid_1") is None

    @patch.object(Session, "post")
    def test_allocate_nic_raise_error(self, mock_post):
        mock_post.return_value = MockResponse(json={"data": {}, "ec": "REST_API_HTTP_ERROR", "error": ""})
        with pytest.raises(rtf_exceptions):
            self.client.allocate_nic("vm_uuid_1", "host_uuid_1", "nic_uuid_1")

    @patch.object(Session, "post")
    def test_release_nic(self, mock_post):
        mock_post.return_value = MockResponse(json={"data": {}, "ec": "EOK", "error": ""})
        assert self.client.release_nic("vm_uuid_1", "host_uuid_1", "nic_uuid_1") is None

    @patch.object(Session, "post")
    def test_release_nic_raise_error(self, mock_post):
        mock_post.return_value = MockResponse(json={"data": {}, "ec": "REST_API_HTTP_ERROR", "error": ""})
        with pytest.raises(rtf_exceptions):
            self.client.release_nic("vm_uuid_1", "host_uuid_1", "nic_uuid_1")

    @patch.object(Session, "get")
    def test_get_nic_info(self, mock_get):
        mock_get.return_value = MockResponse(json={"data": {"bus_location": "0000:2f:00.0"}, "ec": "EOK", "error": ""})
        result = self.client.get_nic_info("host_uuid_1", "nic_uuid_1")
        assert result["bus_location"] == "0000:2f:00.0"

    @patch.object(Session, "get")
    def test_get_nic_info_raise_error(self, mock_get):
        mock_get.return_value = MockResponse(json={"data": {}, "ec": "REST_API_HTTP_ERROR", "error": ""})
        with pytest.raises(rtf_exceptions):
            self.client.get_nic_info("host_uuid_1", "nic_uuid_1")

    @patch.object(Session, "get")
    def test_get_nic_infos(self, mock_get):
        mock_get.return_value = MockResponse(
            json={"data": [{"name": "Mellanox MT27710"}, {"name": "Intel I350"}], "ec": "EOK", "error": ""}
        )
        result = self.client.get_nic_infos("host_uuid_1")
        assert result == [{"name": "Mellanox MT27710"}, {"name": "Intel I350"}]

    @patch.object(Session, "get")
    def test_get_nic_infos_raise_error(self, mock_get):
        mock_get.return_value = MockResponse(json={"data": {}, "ec": "REST_API_HTTP_ERROR", "error": ""})
        with pytest.raises(rtf_exceptions):
            self.client.get_nic_infos("host_uuid_1")

    @patch.object(Session, "get")
    def test_get_nic_assign_infos(self, mock_get):
        assign_infos = [{"host_uuid": "host_uuid_1", "device_uuid": "nic_uuid_1", "assign_id": "vm_uuid_1"}]
        mock_get.return_value = MockResponse(
            json={"data": {"nic_assign_infos": assign_infos}, "ec": "EOK", "error": ""}
        )

        result = self.client.get_nic_assign_infos("host_uuid_1")
        assert result == assign_infos


class TestTunaPCIFunctions(unittest.TestCase):
    def setUp(self):
        self.client = Client()

    @patch.object(Session, "post")
    def test_allocate_pci(self, mock_post):
        mock_post.return_value = MockResponse(json={"data": {}, "ec": "EOK", "error": ""})
        assert self.client.allocate_pci("vm_uuid_1", "host_uuid_1", "pci_uuid_1") is None

    @patch.object(Session, "post")
    def test_allocate_pci_raise_error(self, mock_post):
        mock_post.return_value = MockResponse(json={"data": {}, "ec": "REST_API_HTTP_ERROR", "error": ""})
        with pytest.raises(rtf_exceptions):
            self.client.allocate_pci("vm_uuid_1", "host_uuid_1", "pci_uuid_1")

    @patch.object(Session, "post")
    def test_release_pci(self, mock_post):
        mock_post.return_value = MockResponse(json={"data": {}, "ec": "EOK", "error": ""})
        assert self.client.release_pci("vm_uuid_1", "host_uuid_1", "pci_uuid_1") is None

    @patch.object(Session, "post")
    def test_release_pci_raise_error(self, mock_post):
        mock_post.return_value = MockResponse(json={"data": {}, "ec": "REST_API_HTTP_ERROR", "error": ""})
        with pytest.raises(rtf_exceptions):
            self.client.release_pci("vm_uuid_1", "host_uuid_1", "pci_uuid_1")

    @patch.object(Session, "get")
    def test_get_pci_info(self, mock_get):
        mock_get.return_value = MockResponse(json={"data": {"bus_location": "0000:2f:00.0"}, "ec": "EOK", "error": ""})
        result = self.client.get_pci_info("host_uuid_1", "pci_uuid_1")
        assert result["bus_location"] == "0000:2f:00.0"

    @patch.object(Session, "post")
    def test_allocate_vf_pci_devices(self, mock_post):
        mock_post.return_value = MockResponse(
            json={
                "data": {
                    "vfs": [
                        {"index": 0, "bus_location": "0000:d2:01.0", "assign_id": "xxx-yyy"},
                    ]
                },
                "ec": "EOK",
                "error": "",
            }
        )
        vfs = self.client.allocate_sriov_pcis("host_uuid_1", "vm_uuid_1", ["vm_uuid_vf_uuid_1"])
        assert vfs == [
            {"index": 0, "bus_location": "0000:d2:01.0", "assign_id": "xxx-yyy"},
        ]

    @patch.object(Session, "post")
    def test_allocate_vf_pci_devices_raise_error(self, mock_post):
        mock_post.return_value = MockResponse(json={"data": {}, "ec": "REST_API_HTTP_ERROR", "error": ""})
        with pytest.raises(rtf_exceptions):
            self.client.allocate_nic("host_uuid_1", "vm_uuid_1", ["vm_uuid_vf_uuid_1"])

    @patch.object(Session, "post")
    def test_release_vf_pci_devices(self, mock_post):
        mock_post.return_value = MockResponse(json={"data": {}, "ec": "EOK", "error": ""})
        assert self.client.release_sriov_pcis("host_uuid_1", "vm_uuid_1", ["vm_uuid_vf_uuid_1"]) is None

    @patch.object(Session, "post")
    def test_release_vf_pci_devices_raise_error(self, mock_post):
        mock_post.return_value = MockResponse(json={"data": {}, "ec": "REST_API_HTTP_ERROR", "error": ""})
        with pytest.raises(rtf_exceptions):
            self.client.release_sriov_pcis("host_uuid_1", "vm_uuid_1", ["vm_uuid_vf_uuid_1"])

    @patch.object(Session, "get")
    def test_get_pci_info_raise_error(self, mock_get):
        mock_get.return_value = MockResponse(json={"data": {}, "ec": "REST_API_HTTP_ERROR", "error": ""})
        with pytest.raises(rtf_exceptions):
            self.client.get_pci_info("host_uuid_1", "pci_uuid_1")

    @patch.object(Session, "get")
    def test_get_pci_infos(self, mock_get):
        mock_get.return_value = MockResponse(
            json={"data": {"pcis": [{"name": "SC-X1900"}, {"name": "SJK1727"}]}, "ec": "EOK", "error": ""}
        )
        result = self.client.get_pci_infos("host_uuid_1")
        assert result == [{"name": "SC-X1900"}, {"name": "SJK1727"}]

    @patch.object(Session, "get")
    def test_get_pci_infos_raise_error(self, mock_get):
        mock_get.return_value = MockResponse(json={"data": {}, "ec": "REST_API_HTTP_ERROR", "error": ""})
        with pytest.raises(rtf_exceptions):
            self.client.get_pci_infos("host_uuid_1")

    @patch.object(Session, "get")
    def test_get_pci_assign_infos(self, mock_get):
        assign_infos = [{"host_uuid": "host_uuid_1", "device_uuid": "pci_uuid_1", "assign_id": "vm_uuid_1"}]
        mock_get.return_value = MockResponse(
            json={"data": {"pci_assign_infos": assign_infos}, "ec": "EOK", "error": ""}
        )

        result = self.client.get_pci_assign_infos("host_uuid_1")
        assert result == assign_infos

    @patch.object(Session, "get")
    def test_get_pci_assign_info_with_pci_id(self, mock_get):
        device_id = "device_id1"
        assign_info = {
            "device_id": device_id,
            "bus_location": "0000:d2:00.0",
            "class_code": "0x040000",
            "host_uuid": "99e268ac-168a-11ef-bef7-0bbedc96bc91",
            "iommu_group": 61,
            "mdev_num": 1,
        }
        mock_get.return_value = MockResponse(json={"data": assign_info, "ec": "EOK", "error": ""})

        result = self.client.get_pci_assign_info_with_pci_id("host_uuid_1", device_id)
        call_args, _ = mock_get.call_args

        assert call_args[0].endswith(device_id)
        assert result == assign_info

    @patch.object(Session, "post")
    def test_assign_mdevs(self, mock_post):
        mock_post.return_value = MockResponse(
            json={
                "data": {
                    "mdevs": [
                        {"assign_id": "test1", "mdev_uuid": "mdev_uuid1"},
                        {"assign_id": "test2", "mdev_uuid": "mdev_uuid2"},
                        {"assign_id": "test3", "mdev_uuid": "mdev_uuid3"},
                    ]
                },
                "ec": "EOK",
                "error": "",
            }
        )
        mdevs = self.client.assign_mdev("host_uuid_1", "pdev1", "hct1", ["vm_uuid_mdev_uuid_1"])
        assert mdevs == {"test1": "mdev_uuid1", "test2": "mdev_uuid2", "test3": "mdev_uuid3"}

    @patch.object(Session, "post")
    def test_release_mdevs(self, mock_post):
        mock_post.return_value = MockResponse(
            json={
                "data": True,
                "ec": "EOK",
                "error": "",
            }
        )
        self.client.release_mdev("host_uuid_1", "pdev1", "hct1", ["vm_uuid_mdev_uuid_1"])


class TestTunaUSBRestClient:
    def test_get_usbs(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(
                status_code=200, json={"data": {"usbs": [usb]}, "ec": "EOK", "error": {}}
            )

            result = Client().get_usbs(host_id)

            assert isinstance(result, list)
            assert result[0] == usb

            call_args, _ = mock_get.call_args
            url = "http://127.0.0.1:80/api/v2/management/hosts/{}/usbs".format(host_id)
            assert url in call_args

    def test_get_usb(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(status_code=200, json={"data": usb, "ec": "EOK", "error": {}})

            result = Client().get_usb(host_id, usb_id)

            assert isinstance(result, dict)
            assert result == usb

            call_args, _ = mock_get.call_args
            url = "http://127.0.0.1:80/api/v2/management/hosts/{}/usbs/{}".format(host_id, usb_id)
            assert url in call_args

    def test_get_usb_assign_infos(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(
                status_code=200, json={"data": {"usb_assign_infos": [usb_assign_info]}, "ec": "EOK", "error": {}}
            )

            result = Client().get_usb_assign_infos(host_id)

            assert isinstance(result, list)
            assert result[0] == usb_assign_info

            call_args, _ = mock_get.call_args
            url = "http://127.0.0.1:80/api/v2/management/hosts/{}/usb_assign_infos".format(host_id)
            assert url in call_args

    def test_get_usb_assign_info(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(
                status_code=200, json={"data": usb_assign_info, "ec": "EOK", "error": {}}
            )

            result = Client().get_usb_assign_info(host_id, usb_id)

            assert result == usb_assign_info

            call_args, _ = mock_get.call_args
            url = "http://127.0.0.1:80/api/v2/management/hosts/{}/usb_assign_infos/{}".format(host_id, usb_id)
            assert url in call_args

    def test_allocate_over_network_usb(self):
        allocate_result = {
            "device_id": usb_id,
            "host_uuid": host_id,
            "update_version": 123,
            "type": "usb",
            "assignable": True,
            "assign_id": "xxx-id1",
            "info": {"server": "*******", "port": "8000"},
        }

        with patch.object(Session, "post") as mock_post:
            mock_post.return_value = MockResponse(
                status_code=200, json={"data": allocate_result, "ec": "EOK", "error": {}}
            )

            result = Client().allocate_over_network_usb(host_id, usb_id, "vm_id")
            assert result == allocate_result

            call_args, call_kwargs = mock_post.call_args
            url = "http://127.0.0.1:80/api/v2/management/hosts/{}/usbs/{}/assign_over_network".format(host_id, usb_id)
            assert url in call_args
            assert call_kwargs["json"] == {"assign_id": "vm_id"}

    def test_release_over_network_usb(self):
        with patch.object(Session, "post") as mock_post:
            mock_post.return_value = MockResponse(status_code=200, json={"data": True, "ec": "EOK", "error": {}})

            result = Client().release_over_network_usb(host_id, usb_id, "vm_id")
            assert result

            call_args, call_kwargs = mock_post.call_args
            url = "http://127.0.0.1:80/api/v2/management/hosts/{}/usbs/{}/release_over_network".format(host_id, usb_id)
            assert url in call_args
            assert call_kwargs["json"] == {"assign_id": "vm_id"}

    def test_allocate_pass_through_usb(self):
        allocate_result = {
            "device_id": usb_id,
            "host_uuid": host_id,
            "update_version": 123,
            "type": "usb",
            "assignable": True,
            "assign_id": "xxx-id1",
        }

        with patch.object(Session, "post") as mock_post:
            mock_post.return_value = MockResponse(
                status_code=200, json={"data": allocate_result, "ec": "EOK", "error": {}}
            )

            result = Client().allocate_pass_through_usb(host_id, usb_id, "vm_id")
            assert result == allocate_result

            call_args, call_kwargs = mock_post.call_args
            url = "http://127.0.0.1:80/api/v2/management/hosts/{}/usbs/{}/assign".format(host_id, usb_id)
            assert url in call_args
            assert call_kwargs["json"] == {"assign_id": "vm_id"}

    def test_release_pass_through_usb(self):
        with patch.object(Session, "post") as mock_post:
            mock_post.return_value = MockResponse(status_code=200, json={"data": True, "ec": "EOK", "error": {}})

            result = Client().release_pass_through_usb(host_id, usb_id, "vm_id")
            assert result

            call_args, call_kwargs = mock_post.call_args
            url = "http://127.0.0.1:80/api/v2/management/hosts/{}/usbs/{}/release".format(host_id, usb_id)
            assert url in call_args
            assert call_kwargs["json"] == {"assign_id": "vm_id"}


class TestTunaClusterRestClient:
    def test_get_cluster_software(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(status_code=200, json={"data": True, "ec": "EOK", "error": {}})

            result = Client().get_cluster_software()
            assert result

            call_args, _ = mock_get.call_args
            url = "http://127.0.0.1:80/api/v2/cluster/software"
            assert url in call_args

    def test_get_cluster_compute_summary(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(status_code=200, json={"data": True, "ec": "EOK", "error": {}})

            result = Client().get_cluster_compute_summary()
            assert result

            call_args, _ = mock_get.call_args
            url = "http://127.0.0.1:80/api/v2/cluster/compute_summary"
            assert url in call_args

    def test_get_management_hosts(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(status_code=200, json={"data": [True], "ec": "EOK", "error": {}})

            result = Client().get_management_hosts(host_uuid="123", fields=["data_ip", "memory"])
            assert result

            call_args, call_kw = mock_get.call_args

            url = "http://127.0.0.1:80/api/v2/management/hosts"
            assert url in call_args
            assert call_kw["params"]["fields"] == "data_ip,memory"
            assert call_kw["params"]["host_uuid"] == "123"

    def test_get_management_host(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(status_code=200, json={"data": [True], "ec": "EOK", "error": {}})

            result = Client().get_management_host(host_uuid="test", fields=["data_ip", "memory"])
            assert result

            call_args, call_kw = mock_get.call_args

            url = "http://127.0.0.1:80/api/v2/management/hosts"
            assert url in call_args
            assert call_kw["params"]["fields"] == "data_ip,memory"
            assert call_kw["params"]["host_uuid"] == "test"

    def test_get_management_hosts_summary(self):
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(status_code=200, json={"data": True, "ec": "EOK", "error": {}})

            result = Client().get_management_hosts_summary()
            assert result

            call_args, _ = mock_get.call_args
            url = "http://127.0.0.1:80/api/v2/management/hosts/summary"
            assert url in call_args

    @patch.object(Session, "get")
    def test_get_all_nodes_software_features(self, mock_get):
        fake_response_json = {
            "ec": "EOK",
            "data": {"rdma_enabled": False, "vhost_enabled": False},
            "error": {},
        }
        mock_get.return_value = MockResponse(status_code=200, json=fake_response_json)

        fake_hosts = ["*************", "*************", "*************"]
        client = Client()
        results = client.get_all_nodes_software_features(fake_hosts)

        assert len(results) == 3
        assert results["*************"]["vhost_enabled"] is False


class TestTunaVGPUFunction(unittest.TestCase):
    def setUp(self):
        self.client = Client()

    @patch.object(Session, "post")
    def test_assign_vgpu(self, mock_post):
        mock_post.return_value = MockResponse(
            json={
                "data": {"vgpus": [{"assign_id": "assign_id_1", "mdev_uuid": "mdev_uuid_1"}]},
                "ec": "EOK",
                "error": "",
            }
        )
        result = self.client.assign_vgpu("vm_uuid_1", "host_uuid_1", "nvidia-553", "assign_id_1")
        assert result == {"assign_id_1": "mdev_uuid_1"}, result

    @patch.object(Session, "post")
    def test_assign_vgpu_raise_error(self, mock_post):
        mock_post.return_value = MockResponse(json={"data": {}, "ec": "REST_API_HTTP_ERROR", "error": ""})
        with pytest.raises(rtf_exceptions):
            self.client.assign_vgpu("vm_uuid_1", "host_uuid_1", "nvidia-553", "assign_id_1")

    @patch.object(Session, "post")
    def test_release_vgpu(self, mock_post):
        mock_post.return_value = MockResponse(json={"data": {}, "ec": "EOK", "error": ""})
        assert self.client.release_vgpu("vm_uuid_1", "host_uuid_1", "nvidia-553", "assign_id_1") is None

    @patch.object(Session, "post")
    def test_release_vgpu_raise_error(self, mock_post):
        mock_post.return_value = MockResponse(json={"data": {}, "ec": "REST_API_HTTP_ERROR", "error": ""})
        with pytest.raises(rtf_exceptions):
            self.client.release_vgpu("vm_uuid_1", "host_uuid_1", "nvidia-553", "assign_id_1")
