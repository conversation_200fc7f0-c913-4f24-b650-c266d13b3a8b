# Copyright (c) 2013-2023, SMARTX
# All rights reserved.
from unittest.mock import patch

from requests import Session

from common.http import restful_client as rtf_client
from common.http.zbs import Client
from common.tests.http import MockResponse


def test_copy_volume():
    with patch.object(Session, "post") as mock_post:
        mock_post.return_value = MockResponse(
            status_code=200,
            json={"data": {"id": "test", "state": "510-test"}, "ec": "EOK", "error": {}},
        )

        result = Client(
            rtf_client.RemoteClient(svc_ip="*********", svc_port=80, svc_username="root", svc_password="123")
        ).copy_volume(src_volume_id="test_src_volume_id", dst_volume_id="test_dst_volume_id", token="test_token", bps_bytes_max=100)
        assert result
        call_args, call_kw = mock_post.call_args
        url = "http://*********:80/api/v2/task_center/copy_volume"
        assert url in call_args
        assert call_kw["json"] == {
            "src_hosts": "127.0.0.1:10201:10206",
            "dst_hosts": "127.0.0.1:10201:10206",
            "src_volume_id": "test_src_volume_id",
            "dst_volume_id": "test_dst_volume_id",
            "bps_max": 100,
        }


def test_show_task():
    with patch.object(Session, "get") as mock_get:
        mock_get.return_value = MockResponse(
            status_code=200,
            json={"data": {"id": "test", "state": "510-test"}, "ec": "EOK", "error": {}},
        )

        result = Client(
            rtf_client.RemoteClient(svc_ip="*********", svc_port=80, svc_username="root", svc_password="123")
        ).show_task(task_id="test_task", token="test_token")
        assert result
        call_args, _ = mock_get.call_args
        url = "http://*********:80/api/v2/task_center/copy_volume/test_task"
        assert url in call_args

def test_set_bps_max():
    with patch.object(Session, "post") as mock_post:
        mock_post.return_value = MockResponse(
            status_code=200,
            json={"data": {"id": "test", "state": "510-test"}, "ec": "EOK", "error": {}},
        )

        result = Client(
            rtf_client.RemoteClient(svc_ip="*********", svc_port=80, svc_username="root", svc_password="123")
        ).set_bps_max(task_id="test_task", bps_bytes_max=200, token="test_token")
        assert result
        call_args, call_kw = mock_post.call_args
        url = "http://*********:80/api/v2/task_center/copy_volume/test_task/set_bps_max"
        assert url in call_args
        assert call_kw["json"] == {
            "bps_max": 200,
        }
