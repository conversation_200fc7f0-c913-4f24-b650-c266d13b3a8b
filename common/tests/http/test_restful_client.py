# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
from unittest.mock import ANY, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest
from requests import Session, exceptions

from common.http import restful_client as http_client
from common.http.exceptions import RestfulClientError as rtf_exceptions
from common.http.restful_client import Client
from common.tests.http import MockResponse
from smartx_proto.errors import pyerror_pb2


@pytest.fixture
def restful_client():
    return Client.from_config()


class TestRestClient:
    def test_normal_request(self, restful_client):
        from common.config import constant

        url = restful_client.gen_url("/vms")
        assert url == "http://127.0.0.1:80/api/v2/vms"

        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse(json="test")

            result = restful_client.get(url)

            call_args, call_kwargs = mock_get.call_args
            assert url in call_args
            assert "headers" in call_kwargs
            assert constant.AUTH_TOKEN_NAME_NEW in call_kwargs["headers"]
            assert result == "test"

    def test_connect_error(self, restful_client):
        url = restful_client.gen_url("/vms")

        with patch.object(Session, "post") as mock_post:
            mock_post.side_effect = exceptions.ConnectionError()

            with pytest.raises(rtf_exceptions) as e:
                restful_client.post(url)
            assert e.value.user_code == pyerror_pb2.REST_API_CONNECTION_ERROR

    def test_http_error(self, restful_client):
        url = restful_client.gen_url("/vms")
        with patch.object(Session, "put") as mock_put:
            mock_put.return_value = MockResponse(json="test", status_code=401)

            with pytest.raises(rtf_exceptions) as e:
                restful_client.put(url)
            assert e.value.user_code == pyerror_pb2.REST_API_HTTP_ERROR

    def test_timeout(self, restful_client):
        url = restful_client.gen_url("/vms")
        with patch.object(Session, "delete") as mock_delete:
            mock_delete.side_effect = exceptions.Timeout()

            with pytest.raises(rtf_exceptions) as e:
                restful_client.delete(url)
            assert e.value.user_code == pyerror_pb2.REST_API_TIMEOUT

    def test_json_decode_error(self, restful_client):
        url = restful_client.gen_url("/vms")
        with patch.object(Session, "get") as mock_get:
            mock_get.return_value = MockResponse()

            with pytest.raises(rtf_exceptions) as e:
                restful_client.get(url)
            assert e.value.user_code == pyerror_pb2.REST_API_JSON_DECODE_ERROR

    def test_delete_with_json(self, restful_client):
        with patch.object(Session, "delete", autospec=True) as mock_delete:
            url = restful_client.gen_url("network/vnic_group/test_vnic_uuid")
            body = {"vnics": [{"uuid": "vnic_uuid_1"}]}
            restful_client.delete(url, json=body)

            mock_delete.assert_called_once_with(ANY, url, headers=ANY, json=body, timeout=ANY, data=None)


def test_remote_release_token():
    with patch.object(http_client, "request_with_session") as mock_request_with_session:
        a = http_client.RemoteClient("root", "111111", "*********", 80)
        a.release_token("test_token")
        _, call_kwargs = mock_request_with_session.call_args
        assert call_kwargs["url"] == "http://*********:80/api/v3/session"
        assert call_kwargs["headers"] == {"Grpc-Metadata-Token": "test_token"}


def test_remote_get_token():
    with patch.object(http_client, "request_with_session") as mock_request_with_session:
        c = http_client.RemoteClient("root", "111111", "*********", 80)
        mock_func = MagicMock
        c.release_token = mock_func
        mock_request_with_session.return_value = {"token": "test"}
        assert c.get_token() == "test"

        _, call_kwargs = mock_request_with_session.call_args
        assert call_kwargs["url"] == "http://*********:80/api/v3/sessions"
        assert call_kwargs["json"] == {"username": "root", "password": "111111"}
        assert mock_func.called


def test_remote_post():
    with patch.object(http_client.RemoteClient, "get_token", return_value="test"), patch.object(
        http_client.RemoteClient, "_request"
    ) as mock_request:
        c = http_client.RemoteClient("root", "111111", "test_ip", 80)
        mock_func = MagicMock
        c.release_token = mock_func

        mock_request.return_value = MockResponse(
            json={
                "user_id": "root",
                "token": "test",
                "create_time": "2023-07-31T06:26:30.175464289Z",
                "expire_time": "2023-08-07T06:26:30.175464339Z",
                "update_time": "2023-07-31T06:26:30.175464409Z",
            }
        )
        assert c.post("/path", version="v3")
        _, call_kwargs = mock_request.call_args
        assert call_kwargs["url"] == "http://test_ip:80/api/v3/path"


def test_request_with_session_timeout():
    mock_session = Mock()
    mock_session.get.side_effect = exceptions.ConnectTimeout

    with pytest.raises(rtf_exceptions) as e:
        http_client.request_with_session(mock_session, False, 1, "get", "path")
    assert e.value.user_code == pyerror_pb2.REST_API_TIMEOUT

    _, call_kwargs = mock_session.get.call_args
    assert call_kwargs["timeout"] == 1


def test_remote_request_verify():
    with patch.object(http_client, "request_with_session") as mock_request_with_session:
        c = http_client.RemoteClient("root", "111111", "test_ip", 443)
        mock_func = MagicMock
        c.release_token = mock_func

        mock_request_with_session.return_value = "test"
        c.get("/path", version="v3", token="123")
        _, kwargs = mock_request_with_session.call_args
        assert kwargs["verify"] is False


def test_raise_for_ec_unknown():
    response = {"ec": "UNKNOWN_CODE"}
    with pytest.raises(rtf_exceptions) as e:
        http_client.RemoteClient.raise_for_ec(response)
    assert e.value.user_code == pyerror_pb2.REST_UNKNOWN_ERROR
