# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
from unittest.mock import MagicMock
from requests import exceptions


class MockResponse(MagicMock):
    def __init__(self, json=None, status_code=200, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._json = json
        self.text = json
        self.status_code = status_code

    def json(self):
        if not self._json:
            raise ValueError()
        return self._json

    def raise_for_status(self):
        if self.status_code != 200:
            raise exceptions.HTTPError("status code is {}.".format(self.status_code), response=self)
