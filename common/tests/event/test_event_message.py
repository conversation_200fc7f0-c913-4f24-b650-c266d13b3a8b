# Copyright (c) 2013-2015, SMARTX
# All rights reserved.

from unittest.mock import patch

from common.event import event_message

job = {
    "event": {"event_name": "BATCH_DELETE_VM", "batch": [], "detail": {"en_US": None, "zh_CN": None}, "data": "xxxx"},
    "task_list": [{"reference": "123"}],
}


def test_batch_deal():
    with patch.object(event_message.EventMessage, "get_event_detail") as get_event_detail_mock:
        get_event_detail_mock.return_value = "aabb"

        event_message.batch_deal(job)
        assert job["event"]["data"] == {}
        assert job["event"]["detail"]["en_US"] == "aabb"
        assert job["event"]["detail"]["zh_CN"] == "aabb"


def test_need_handle_special_characters():
    value = 10
    assert event_message.need_handle_special_characters(value) is False

    value = 10.9
    assert event_message.need_handle_special_characters(value) is False

    value = int(100000)
    assert event_message.need_handle_special_characters(value) is False


def test_merge_event_mod():
    event_mod1 = {"zh_CN": "事件内容, ${key1}=${value1}。", "en_US": "event, ${key1}=${value1}."}
    event_mod2 = {"zh_CN": "额外信息, ${key2}=${value2}。", "en_US": "additional_info, ${key2}=${value2}."}
    result = event_message.EventMessage.merge_event_mod(event_mod1, event_mod2)
    assert result == {
        "zh_CN": "事件内容, ${key1}=${value1}。额外信息, ${key2}=${value2}。",
        "en_US": "event, ${key1}=${value1}.additional_info, ${key2}=${value2}."
    }

def test_fill_multilang_placeholders():
    from smartx_app.elf.common.events import message_i18n_elf as i18n
    from smartx_app.elf.common.events import constants

    event_mod = i18n.event_message.get(constants.EVENT_VM_NETWORK_HA)

    # test rebuild
    action_desc = i18n.event_message.get("VM_NETWORK_HA_REBUILD")
    result = event_message.EventMessage.fill_multilang_placeholders(event_mod, action_desc=action_desc)
    assert result == {
        "zh_CN": "主机 ${origin_host_name} 上的虚拟机 ${vm_name} 因虚拟机网络故障触发高可用，重建至主机 ${dest_host_name}。",
        "en_US": "VM ${vm_name} on host ${origin_host_name} was rebuilt to host ${dest_host_name} due to network HA.",
    }

    # test migrate
    action_desc = i18n.event_message.get("VM_NETWORK_HA_MIGRATE")
    result = event_message.EventMessage.fill_multilang_placeholders(event_mod, action_desc=action_desc)
    assert result == {
        "zh_CN": "主机 ${origin_host_name} 上的虚拟机 ${vm_name} 因虚拟机网络故障触发高可用，热迁移至主机 ${dest_host_name}。",
        "en_US": "VM ${vm_name} on host ${origin_host_name} was migrated to host ${dest_host_name} due to network HA.",
    }

    # test none
    result = event_message.EventMessage.fill_multilang_placeholders(event_mod, action_desc=None)
    assert result == event_mod

    # test not dict
    result = event_message.EventMessage.fill_multilang_placeholders(event_mod, action_desc="test")
    assert result == event_mod

    # test not in event_mod
    result = event_message.EventMessage.fill_multilang_placeholders(event_mod, test={"zh_CN": "热迁移", "en_US": "migrated"})
    assert result == event_mod
