# Copyright (c) 2013-2022, SMARTX
# All rights reserved.
import os

from common.mongo.tools import decode_mongo_password, encode_mongo_password, generate_mongo_auth_keyfile, generate_password
from common.mongo.constant import MONGO_DEFAULT_PASSWORD_PREFIX


def test_generate_mongo_auth_keyfile():
    if not os.path.exists("/var/lib/mongodb"):
        os.makedirs("/var/lib/mongodb")
    res = generate_mongo_auth_keyfile()
    assert res is True


def test_generate_password():
    new_password = generate_password()
    raw_password = decode_mongo_password(new_password)
    assert raw_password.startswith(MONGO_DEFAULT_PASSWORD_PREFIX)


def test_decode_mongo_password():
    raw_password = decode_mongo_password("jnVdNB78t7oTqch/kzfOnw==")
    assert raw_password == "smartx@passw0rd"


def test_encode_mongo_password():
    encoded_password = encode_mongo_password("smartx@passw0rd")
    assert encoded_password == "jnVdNB78t7oTqch/kzfOnw=="
