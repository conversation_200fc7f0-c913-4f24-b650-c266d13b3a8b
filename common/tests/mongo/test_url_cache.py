# Copyright (c) 2013-2022, SMARTX
# All rights reserved.

from unittest.mock import mock_open, patch


@patch("builtins.open", mock_open(read_data="test_user:jnVdNB78t7oTqch/kzfOnw=="))
@patch("os.path.exists", return_value=True)
def test_build_mongo_url_prefix(*args):
    import common.mongo.url_cache as mongo_url
    # if use mongo_url.mongo_url_prefix directly, it will return None
    # because the mock_open() will not work, mongo_url.mongo_url_prefix will run before patch mock_open()
    # so we need to call the function
    mongo_url_prefix = mongo_url.build_mongo_url_prefix()
    assert "mongodb://test" in mongo_url_prefix
