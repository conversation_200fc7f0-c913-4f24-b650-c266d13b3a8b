# Copyright (c) 2013-2022, SMARTX
# All rights reserved.

from copy import deepcopy
from unittest.mock import Mock, call, patch

from common.mongo import util as mongo_util


@patch("common.mongo.util.sleep")
@patch("common.mongo.util.MongoClient")
@patch("common.mongo.util.Config")
@patch("common.mongo.util.mongo_status")
@patch("common.mongo.util.check_member_status")
@patch("common.mongo.util.mongo_leader")
def test_member_action(
    mock_mongo_leader, mock_check_member_status, mock_mongo_status, mock_conf_cls, mock_mongo_client_cls, mock_sleep
):
    mock_mongo_leader.return_value = "127.0.0.1:27017"

    mock_conf = Mock()
    mock_conf.get_local_data_ip = Mock(return_value=["127.0.0.1"])
    mock_conf_cls.return_value = mock_conf

    m_client = Mock()
    m_client.local = Mock()
    m_client.local.system = Mock()
    m_client.local.system.replset = Mock()
    m_client.admin = Mock()
    m_client.admin.command = Mock(return_value={"ok": 1})
    mock_mongo_client_cls.return_value = m_client

    mock_check_member_status.return_value = True
    mock_mongo_status.return_value = {
        "members": [
            {"name": "***********:27017", "stateStr": "PRIMARY"},
            {"name": "***********:27017", "stateStr": "SECONDARY"},
        ]
    }

    not_exist = {
        "_id": "zbs",
        "version": 1,
        "members": [{"host": "*********:27017", "_id": 1}, {"host": "*********:27017", "_id": 2}],
    }
    exist = {
        "_id": "zbs",
        "version": 1,
        "members": [
            {"host": "127.0.0.1:27017", "_id": 1},
            {"host": "*********:27017", "_id": 1},
            {"host": "*********:27017", "_id": 2},
        ],
    }
    exist_with_newlyAdded_field = {
        "_id": "zbs",
        "version": 1,
        "members": [
            {"host": "127.0.0.1:27017", "_id": 1, "newlyAdded": True},
            {"host": "*********:27017", "_id": 1},
            {"host": "*********:27017", "_id": 2},
        ],
    }

    m_client.local.system.replset.find_one = Mock(return_value=deepcopy(not_exist))
    assert mongo_util.member_action(data_ip="127.0.0.1", action="add") is True
    m_client.local.system.replset.find_one = Mock(return_value=deepcopy(exist))
    assert mongo_util.member_action(data_ip="127.0.0.1", action="add") is True
    m_client.local.system.replset.find_one = Mock(return_value=deepcopy(exist_with_newlyAdded_field))
    assert mongo_util.member_action(data_ip="127.0.0.1", action="add") is True
    assert m_client.admin.command.call_args_list[-1] == call(
        {'replSetReconfig': {'_id': 'zbs',
                     'members': [{'_id': 1, 'host': '127.0.0.1:27017'},
                                 {'_id': 1, 'host': '*********:27017'},
                                 {'_id': 2, 'host': '*********:27017'}],
                     'version': 2}}
    )

    m_client.local.system.replset.find_one = Mock(return_value=deepcopy(exist))
    assert mongo_util.member_action(data_ip="127.0.0.1", action="remove") is True
    m_client.local.system.replset.find_one = Mock(return_value=deepcopy(not_exist))
    assert mongo_util.member_action(data_ip="127.0.0.1", action="remove") is True


def test_mongo_create_user():
    with patch.object(mongo_util, "MongoClient") as mock_mongodb:
        m_client = Mock()
        m_client.admin = Mock()
        m_client.admin.add_user = Mock()
        m_client.admin.authenticate = Mock()
        mock_mongodb.return_value = m_client
        mock_admin = mock_mongodb.return_value.admin = Mock()
        mock_admin.return_value.add_user = Mock(return_value={"ok": 1})
        mongo_util.mongo_create_user("test", "test")


def test_mongo_create_user_failed():
    with patch.object(mongo_util, "MongoClient") as mock_mongodb:
        mock_mongodb.side_effect = Exception("test failed")
        mongo_util.mongo_create_user("test", "test")


def test_is_mongo_with_user():
    with patch.object(mongo_util, "MongoClient") as mock_client:
        m_client = Mock()
        m_client.admin = Mock()
        m_client.admin.command = Mock(return_value={"users": [{"name": "admin"}, {"name": "root"}]})
        mock_client.return_value = m_client
        res = mongo_util.is_mongo_with_user("test", "test")
        assert res is True
