# Copyright (c) 2013-2022, SMARTX
# All rights reserved.
import textwrap
from unittest.mock import Mock, mock_open, patch

from common.mongo.recovering_resync import MongodResyncHelper


@patch.object(MongodResyncHelper, "_get_current_data_ip", return_value="127.0.0.1")
@patch("common.mongo.db.mongodb")
def test_get_mongo_members(mock_mongodb, mock_get_data_ip):
    mock_mongodb.conn = Mock()
    mock_mongodb.conn.admin = Mock()
    mock_mongodb.conn.admin.command = lambda x: {
        "members": [{"name": "127.0.0.1:27017"}, {"name": "***********:27017"}, {"name": "***********:27017"}]
    }
    assert len(MongodResyncHelper()._get_mongo_members()) == 3


@patch.object(MongodResyncHelper, "_get_current_data_ip", return_value="127.0.0.1")
@patch.object(Mongod<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, "_get_mongo_members")
def test_pre_check_mongo_status(mock_get_mongo_members, mock_get_data_ip):
    mock_get_mongo_members.return_value = [
        {"name": "127.0.0.1:27017", "stateStr": "PRIMARY"},
        {"name": "***********:27017", "stateStr": "SECONDARY"},
        {"name": "***********:27017", "stateStr": "SECONDARY"},
    ]
    assert MongodResyncHelper().pre_check_mongo_status() is False
    mock_get_mongo_members.return_value = [
        {"name": "127.0.0.1:27017", "stateStr": "SECONDARY"},
        {"name": "***********:27017", "stateStr": "PRIMARY"},
        {"name": "***********:27017", "stateStr": "(not reachable/healthy)"},
    ]
    assert MongodResyncHelper().pre_check_mongo_status() is False
    mock_get_mongo_members.return_value = [
        {"name": "*********:27017", "stateStr": "SECONDARY"},
        {"name": "***********:27017", "stateStr": "PRIMARY"},
        {"name": "***********:27017", "stateStr": "SECONDARY"},
    ]
    assert MongodResyncHelper().pre_check_mongo_status() is False
    mock_get_mongo_members.return_value = [
        {"name": "127.0.0.1:27017", "stateStr": "SECONDARY"},
        {"name": "***********:27017", "stateStr": "PRIMARY"},
        {"name": "***********:27017", "stateStr": "SECONDARY"},
    ]
    assert MongodResyncHelper().pre_check_mongo_status() is True


@patch.object(MongodResyncHelper, "_get_current_data_ip", return_value="127.0.0.1")
@patch("common.mongo.recovering_resync.getstatusoutput")
def test_stop_mongod_service(mock_getstatusoutput, mock_get_data_ip):
    mock_getstatusoutput.return_value = 0, ""
    assert MongodResyncHelper()._stop_mongod_service() is True
    assert mock_getstatusoutput.call_count == 3


@patch.object(MongodResyncHelper, "_get_current_data_ip", return_value="127.0.0.1")
@patch("common.mongo.recovering_resync.getstatusoutput")
def test_restart_mongod_service(mock_getstatusoutput, mock_get_data_ip):
    mock_getstatusoutput.return_value = 0, ""
    assert MongodResyncHelper()._restart_mongod_service() is True
    assert mock_getstatusoutput.call_count == 3


@patch.object(MongodResyncHelper, "_get_current_data_ip", return_value="127.0.0.1")
@patch("common.mongo.recovering_resync.os.path.exists")
def test_get_keyfile_path(mock_os_exists, mock_get_data_ip):
    mongod_conf = textwrap.dedent(
        """
        processManagement:
           fork: false
        systemLog:
          destination: file
          path: /var/log/mongodb/mongod.log
          logAppend: true
          logRotate: reopen
        storage:
          dbPath: /var/lib/mongodb
          journal:
            enabled: true
          wiredTiger:
            engineConfig:
              cacheSizeGB: 1.6
        net:
          unixDomainSocket:
            enabled: false
          bindIp: 127.0.0.1,***********
          port: 27017
          maxIncomingConnections: 51200
        replication:
          replSetName: zbs
          oplogSizeMB: 4096
        security:
          keyFile: /var/lib/mongodb/mongo-keyfile
        """
    )
    with patch("builtins.open", mock_open(read_data=mongod_conf.strip())):
        assert MongodResyncHelper()._get_keyfile_path() == "/var/lib/mongodb/mongo-keyfile"


@patch.object(MongodResyncHelper, "_get_current_data_ip", return_value="127.0.0.1")
@patch.object(MongodResyncHelper, "_get_keyfile_path")
@patch("common.mongo.recovering_resync.os.remove")
@patch("common.mongo.recovering_resync.shutil.rmtree")
@patch("common.mongo.recovering_resync.os.path.isdir")
@patch("common.mongo.recovering_resync.os.listdir")
def test_clean_var_lib_mongodb(
    mock_listdir, mock_isdir, mock_rmtree, mock_remove, mock_get_keyfile_path, mock_get_current_data_ip
):
    mock_listdir.return_value = ["test_file.db", "mongo-keyfile"]
    mock_isdir.return_value = False
    mock_get_keyfile_path.return_value = "/var/lib/mongodb/mongo-keyfile"
    assert MongodResyncHelper()._clean_var_lib_mongodb() is True
    assert not mock_rmtree.called
    assert mock_remove.call_count == 1


@patch.object(MongodResyncHelper, "_get_current_data_ip", return_value="127.0.0.1")
@patch.object(MongodResyncHelper, "_get_mongo_members")
def test_get_local_mongo_state(mock_get_mongo_members, mock_get_data_ip):
    mock_get_mongo_members.return_value = [
        {"name": "127.0.0.1:27017", "stateStr": "SECONDARY"},
        {"name": "***********:27017", "stateStr": "PRIMARY"},
        {"name": "***********:27017", "stateStr": "SECONDARY"},
    ]
    assert MongodResyncHelper()._get_local_mongo_state() == "SECONDARY"
    assert mock_get_mongo_members.called


@patch.object(MongodResyncHelper, "_get_current_data_ip", return_value="127.0.0.1")
@patch.object(MongodResyncHelper, "_get_local_mongo_state", return_value="SECONDARY")
def test_loop_check_state(mock_get_local_mongo_state, mock_get_data_ip):
    assert MongodResyncHelper()._loop_check_state() is True
    assert mock_get_local_mongo_state.called


@patch.object(MongodResyncHelper, "_get_current_data_ip", return_value="127.0.0.1")
@patch.object(MongodResyncHelper, "_loop_check_state", return_value=True)
@patch.object(MongodResyncHelper, "_restart_mongod_service", return_value=True)
@patch.object(MongodResyncHelper, "_clean_var_lib_mongodb", return_value=True)
@patch.object(MongodResyncHelper, "_stop_mongod_service", return_value=True)
def test_try_rescue_mongod(*args):
    assert MongodResyncHelper().try_rescue_mongod() is True
    for item in args:
        assert item.called
