import logging
import time
import unittest

from common.election.listener import Listener
from common.election.zk_election import ZKElection
from kazoo.client import KazooClient


class TestListener(Listener):
    become_leader_call_times: int = 0
    loss_leader_call_times: int = 0
    leader_change_call_times: int = 0

    def become_leader(self):
        logging.info("become_leader called")
        self.become_leader_call_times += 1

    def loss_leader(self):
        logging.info("loss_leader called")
        self.loss_leader_call_times += 1

    def leader_change(self):
        logging.info("leader_change called")
        self.leader_change_call_times += 1


def new_election(path, node_id: str, listener: Listener) -> ZKElection:
    zookeeper = KazooClient(hosts="127.0.0.1:2181")
    zookeeper.start()
    return ZKElection(zk=zookeeper, path=path, node_id=node_id, listener=listener)


class ElectionCase(unittest.TestCase):
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    def test_start(self):
        listener = TestListener()
        path = "/service/election/test/start"
        node_id = "test_start"

        election = new_election(path, node_id, listener)
        try:
            election.start()
            for i in range(5):
                logging.info("is_leader: %s", election.is_leader())
                if election.is_leader():
                    break
                time.sleep(1)
            self.assertEqual(node_id, election.leader_id())
            self.assertEqual(1, listener.become_leader_call_times)
            self.assertEqual(0, listener.loss_leader_call_times)
            self.assertEqual(0, listener.leader_change_call_times)
        finally:
            election.close()

    def test_release(self):
        listener = TestListener()
        path = "/service/election/test/release"
        node_id = "test_release"

        election = new_election(path, node_id, listener)
        try:
            election.start()
            for i in range(5):
                logging.info("is_leader: %s", election.is_leader())
                if election.is_leader():
                    break
                time.sleep(1)

            self.assertEqual(True, election.release())
            for i in range(5):
                logging.info("loss_leader_call_times called times: %d", listener.loss_leader_call_times)
                if listener.loss_leader_call_times == 1:
                    break
                time.sleep(1)
            self.assertEqual(False, election.is_leader())
            self.assertEqual(1, listener.become_leader_call_times)
            self.assertEqual(1, listener.loss_leader_call_times)
            self.assertEqual(0, listener.leader_change_call_times)
            self.assertEqual("", election.leader_id())

            # test re_election after release
            election.re_election()
            for i in range(5):
                logging.info("is_leader: %s", election.is_leader())
                if election.is_leader():
                    break
                time.sleep(1)
            self.assertEqual(True, election.is_leader())
            self.assertEqual(2, listener.become_leader_call_times)
            self.assertEqual(1, listener.loss_leader_call_times)
            self.assertEqual(0, listener.leader_change_call_times)
            self.assertEqual(node_id, election.leader_id())

        finally:
            election.close()

    def test_re_election(self):
        listener = TestListener()
        path = f"/service/election/test/re_election"
        node_id = "test_re_election"

        election = new_election(path, node_id, listener)

        try:
            election.re_election()
            for i in range(5):
                if election._lock_path != "":
                    break
                time.sleep(1)
            lock_path = election._lock_path

            for i in range(5):
                election.re_election()
            # wait for background thread to finish
            time.sleep(1)

            self.assertEqual(lock_path, election._lock_path)
            # only one node in election
            self.assertEqual(1, len(election.get_election_info()))
        finally:
            election.close()

    def test_wait(self):
        path = "/service/election/test/wait"

        listener1 = TestListener()
        node_id1 = "wait1"

        listener2 = TestListener()
        node_id2 = "wait2"

        election1 = new_election(path, node_id1, listener1)
        election2 = new_election(path, node_id2, listener2)
        try:
            logging.info("start election1")
            election1.start()
            for i in range(5):
                if election1.is_leader():
                    break
                time.sleep(1)
            self.assertEqual(node_id1, election1.leader_id())
            self.assertEqual(1, listener1.become_leader_call_times)

            logging.info("start election2")
            election2.start()
            for i in range(5):
                logging.info("election2 leader_id: %s", election2.leader_id())
                if election2.leader_id() not in ["", None] and election2._lock_path != "":
                    break
                time.sleep(1)
            # election2 wait for election1 release leader
            self.assertEqual(node_id1, election2.leader_id())
            self.assertEqual(0, listener2.become_leader_call_times)
            self.assertEqual(0, listener2.loss_leader_call_times)
            self.assertEqual(1, listener2.leader_change_call_times)

            # election1 release leader, then election2 become leader
            logging.info("release election1")
            election1.release()
            for i in range(5):
                logging.info("election2 is_leader: %s", election2.is_leader())
                if election2.is_leader():
                    break
                time.sleep(1)

            self.assertEqual(False, election1.is_leader())
            self.assertEqual(node_id2, election1.leader_id())
            self.assertEqual(1, listener1.become_leader_call_times)
            self.assertEqual(1, listener1.loss_leader_call_times)
            self.assertEqual(0, listener1.leader_change_call_times)
            self.assertEqual(True, election2.is_leader())
            self.assertEqual(node_id2, election2.leader_id())
            self.assertEqual(1, listener2.become_leader_call_times)
            self.assertEqual(0, listener2.loss_leader_call_times)
            self.assertEqual(1, listener2.leader_change_call_times)

        finally:
            election2.close()
            election1.close()

    def test_get_election_info(self):
        path = "/service/election/test/get_election_info"

        listener1 = TestListener()
        node_id1 = "node1"

        listener2 = TestListener()
        node_id2 = "node2"

        election1 = new_election(path, node_id1, listener1)
        election2 = new_election(path, node_id2, listener2)

        try:
            logging.info("start election1")
            election1.start()
            for i in range(5):
                if election1.is_leader():
                    break
                time.sleep(1)
            self.assertEqual(node_id1, election1.leader_id())
            self.assertEqual(1, listener1.become_leader_call_times)

            logging.info("start election2")
            election2.start()
            for i in range(5):
                logging.info("election2 leader_id: %s", election2.leader_id())
                if election2.leader_id() not in ["", None] and election2._lock_path != "":
                    break
                time.sleep(1)

            info = election1.get_election_info()
            self.assertEqual(2, len(info))

            logging.error(f"info: {info}")
            logging.error(f"info[0]: {info[0].fullpath}")
            self.assertNotEqual(None, info[0].fullpath)
            self.assertNotEqual("", info[0].fullpath)
            self.assertEqual(election1.__getattribute__("_lock_path"), info[0].fullpath)
            self.assertEqual(int(election1.__getattribute__("_lock_path").split("__lock__")[1]), info[0].sequence)
            self.assertEqual(node_id1, info[0].node_id)
            self.assertEqual(0, info[0].priority)

            self.assertEqual(election2.__getattribute__("_lock_path"), info[1].fullpath)
            self.assertEqual(int(election2.__getattribute__("_lock_path").split("__lock__")[1]), info[1].sequence)
            self.assertEqual(node_id2, info[1].node_id)
            self.assertEqual(0, info[1].priority)

        finally:
            election2.close()
            election1.close()

    def test_trigger_watch_event(self):
        path = "/service/election/test/trigger_watch_event"
        node_id = "node"
        election = new_election(path, node_id, None)

        try:
            election.re_election()
            for i in range(5):
                if election._lock_path != "":
                    break
                time.sleep(1)
            lock_path = election._lock_path

            for i in range(5):
                election.zk.handler.spawn(election._trigger_watch_event)
            time.sleep(1)

            self.assertEqual(lock_path, election._lock_path)
            self.assertEqual(1, len(election.get_election_info()))

        finally:
            election.close()
