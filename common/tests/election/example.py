import signal
import threading
import time
import logging

from kazoo.client import KazooClient

import sys

from kazoo.retry import KazooRetry

sys.path.append('/pyzbs')

from common.election.listener import Listener
from common.election.zk_election import ZKElection


class MyListener(Listener):
    become_leader_call_times: int = 0
    loss_leader_call_times: int = 0
    leader_change_call_times: int = 0

    def __init__(self):
        pass

    def become_leader(self):
        self.become_leader_call_times += 1
        print("become_leader")

    def loss_leader(self):
        self.loss_leader_call_times += 1
        print("loss_leader")

    def leader_change(self):
        self.leader_change_call_times += 1
        print("leader_change")


def test_election(elec: ZKElection):
    pass


if __name__ == "__main__":
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    zookeeper = KazooClient(hosts="127.0.0.1:2181",
                            connection_retry=KazooRetry(
                                max_tries=-1,
                                max_delay=10,
                                sleep_func=time.sleep,
                            ),
                            command_retry=KazooRetry(
                                max_tries=3,
                                sleep_func=time.sleep,
                            ),)
    zookeeper.start()
    elec = ZKElection(zk=zookeeper, path="/smartx/test/leader", node_id="22", listener=MyListener())
    elec.start()

    i = 0
    while i < 10:
        print("leader: ", elec.leader_id())
        print("is leader: ", elec.is_leader())
        print("chidlren: ", elec.get_election_info())
        time.sleep(5)
        i += 1

    elec.close()
