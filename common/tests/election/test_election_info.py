import unittest
import logging

from kazoo.client import KazooClient
from common.lib.zk import Zookeeper


class MyTestCase(unittest.TestCase):
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    # zookeeper is strong reference to prevent gc close the connection
    zookeeper = Zookeeper()
    zk: KazooClient = zookeeper.conn

    def setUp(self) -> None:
        self.zk.restart()
        value_leader = b'{"node_id": "test_leader_node", "priority": 10}'
        value_follower = b'{"node_id": "test_follower_node", "priority": 10}'
        self.zk.create(path="/smartx/ntpm/leader/elected", value=value_leader, ephemeral=True, makepath=True)
        self.zk.create(path="/smartx/ntpm/leader/_c_23f5c68abc5b0e43e83b2ad84813b1f6-0000000085",
                       value=value_leader, makepath=True, ephemeral=True)
        self.zk.create(path="/smartx/ntpm/leader/_c_04ff6b8c0814172a7363fef4b9598e5b-0000000087",
                       value=value_follower, makepath=True, ephemeral=True)

        value_node1 = b'node1'
        value_node2 = b'node2'
        self.zk.create(path="/elf/monitor/leader/87e5c647cdbc43409d735be6f3abe97f__lock__0000000060",
                       value=value_node1, makepath=True, ephemeral=True)
        self.zk.create(path="/elf/monitor/leader/87e5c647cdbc43409d735be6f3abe97a__lock__0000000062",
                       value=value_node2, makepath=True, ephemeral=True)

    def test_get_golang_service_node_info(self):
        from common.election.election_info import _get_golang_service_node_info

        children = self.zk.get_children("/smartx/ntpm/leader")
        self.assertEqual(len(children), 3)

        node = _get_golang_service_node_info(self.zookeeper.conn, "/smartx/ntpm/leader", children[1])
        self.assertEqual(node.fullpath, "/smartx/ntpm/leader/_c_23f5c68abc5b0e43e83b2ad84813b1f6-0000000085")
        self.assertEqual(node.sequence, 85)
        self.assertEqual(node.node_id, "test_leader_node")
        self.assertEqual(node.priority, 10)

    def test_get_all_service_info(self):
        from common.election.election_info import get_all_service_info

        nodes = get_all_service_info(self.zookeeper.conn, ["ntpm"])

        self.assertEqual(len(nodes), 1)
        self.assertEqual(len(nodes[0].members), 2)
        self.assertEqual(nodes[0].service_name, "ntpm")
        self.assertEqual(nodes[0].service_path, "/smartx/ntpm/leader")
        self.assertEqual(nodes[0].members[0].fullpath,
                         "/smartx/ntpm/leader/_c_23f5c68abc5b0e43e83b2ad84813b1f6-0000000085")
        self.assertEqual(nodes[0].members[0].sequence, 85)
        self.assertEqual(nodes[0].members[0].node_id, "test_leader_node")
        self.assertEqual(nodes[0].members[0].priority, 10)

        self.assertEqual(nodes[0].members[1].fullpath,
                         "/smartx/ntpm/leader/_c_04ff6b8c0814172a7363fef4b9598e5b-0000000087")
        self.assertEqual(nodes[0].members[1].sequence, 87)
        self.assertEqual(nodes[0].members[1].node_id, "test_follower_node")
        self.assertEqual(nodes[0].members[1].priority, 10)

        nodes = get_all_service_info(self.zookeeper.conn, ["ntpm", "elf-vm-monitor"])
        self.assertEqual(len(nodes), 2)
        self.assertEqual(len(nodes[1].members), 2)
        self.assertEqual(nodes[1].service_name, "elf-vm-monitor")
        self.assertEqual(nodes[1].service_path, "/elf/monitor/leader")
        self.assertEqual(nodes[1].members[0].fullpath,
                         "/elf/monitor/leader/87e5c647cdbc43409d735be6f3abe97f__lock__0000000060")
        self.assertEqual(nodes[1].members[0].sequence, 60)
        self.assertEqual(nodes[1].members[0].node_id, "node1")
        self.assertEqual(nodes[1].members[0].priority, 0)

        self.assertEqual(nodes[1].members[1].fullpath,
                         "/elf/monitor/leader/87e5c647cdbc43409d735be6f3abe97a__lock__0000000062")
        self.assertEqual(nodes[1].members[1].sequence, 62)
        self.assertEqual(nodes[1].members[1].node_id, "node2")
        self.assertEqual(nodes[1].members[1].priority, 0)

    def test_get_all_service_info_no_exist_service(self):
        from common.election.election_info import get_all_service_info

        self.zk.delete("/elf/monitor/leader/87e5c647cdbc43409d735be6f3abe97f__lock__0000000060")
        self.zk.delete("/elf/monitor/leader/87e5c647cdbc43409d735be6f3abe97a__lock__0000000062")

        nodes = get_all_service_info(self.zookeeper.conn, ["timemachine", "elf-vm-monitor"])
        self.assertEqual(len(nodes), 0)

    def test_check_servers_input(self):
        from common.election import election_info
        self.assertIsNone(election_info.check_servers_input(["ntpm"]))
        self.assertIsNone(election_info.check_servers_input(["ntpm", "octopus"]))
        self.assertIsNone(election_info.check_servers_input(["ntpm", "octopus", "siren"]))
        self.assertIsNone(election_info.check_servers_input(["ntpm", "octopus", "siren", "timemachine"]))
        self.assertIsNone(election_info.check_servers_input([]))
        with self.assertRaises(TypeError):
            election_info.check_servers_input(None)
        with self.assertRaises(TypeError):
            election_info.check_servers_input("")
        with self.assertRaises(TypeError):
            election_info.check_servers_input(1)

    def test_get_wanted_services(self):
        from common.election import election_info
        services = election_info._get_wanted_services(["ntpm"])
        self.assertEqual(len(services), 1)
        self.assertEqual(services[0].path, "/smartx/ntpm/leader")
        self.assertEqual(services[0].name, "ntpm")

        services = election_info._get_wanted_services(["ntpm", "timemachine"])
        self.assertEqual(len(services), 2)
        self.assertEqual(services[0].path, "/smartx/ntpm/leader")
        self.assertEqual(services[0].name, "ntpm")
        self.assertEqual(services[1].path, "/zos/service/timemachine")
        self.assertEqual(services[1].name, "timemachine")

    def tearDown(self):
        self.zookeeper.close()


if __name__ == '__main__':
    unittest.main()
