# Copyright (c) 2013-2015, SMARTX
# All rights reserved.
import copy
from functools import wraps
import json
import logging
from string import Template


def event_enclosure(func):
    @wraps(func)
    def event_wraps(*args, **kwargs):
        event = None
        try:
            event = func(*args, **kwargs)
        except Exception as e:
            logging.exception("get event error {}".format(e))
        return event

    return event_wraps


# The value filled in the event template does not exist the dict or list
def need_handle_special_characters(value):
    if not value or isinstance(value, int | float):
        return False
    return True


class EventMessage:
    def __init__(self, event_mod=None):
        self.event_mod = event_mod

    def get_event_message(self, **kwargs):
        if kwargs:
            for k in kwargs:
                if kwargs[k] and not isinstance(kwargs[k], int):
                    if isinstance(kwargs[k], bytes):
                        kwargs[k] = json.dumps(kwargs[k].decode())[1:-1]
                    else:
                        kwargs[k] = json.dumps(kwargs[k])[1:-1]
        tmp = Template(json.dumps(self.event_mod, ensure_ascii=False))
        message = tmp.substitute(**kwargs)
        return json.loads(message)

    @staticmethod
    def get_event_detail(data, **kwargs):
        tmp = Template(json.dumps(data, ensure_ascii=False))
        if kwargs:
            for k in kwargs:
                if need_handle_special_characters(kwargs[k]):
                    if isinstance(kwargs[k], bytes):
                        kwargs[k] = json.dumps(kwargs[k].decode())[1:-1]
                    else:
                        kwargs[k] = json.dumps(kwargs[k])[1:-1]
        detail = tmp.safe_substitute(**kwargs)
        return json.loads(detail, strict=False)

    @classmethod
    def update_detail(cls, details=None, event_detail_tmp=None, **kwargs):
        if details is None and event_detail_tmp is None:
            return None
        if details is None:
            back_detail = copy.deepcopy(event_detail_tmp)
            return cls.get_event_detail(back_detail, **kwargs)
        front_detail = copy.deepcopy(details)
        if event_detail_tmp is not None:
            back_detail = copy.deepcopy(event_detail_tmp)
            front_detail["zh_CN"] += back_detail["zh_CN"]
            front_detail["en_US"] += back_detail["en_US"]
        return cls.get_event_detail(front_detail, **kwargs)

    @classmethod
    def batch_detail_kwargs_splicing(cls, details=None, detail=None):
        if details is None:
            return detail
        details["zh_CN"] += detail["zh_CN"]
        details["en_US"] += detail["en_US"]
        return details

    @classmethod
    def merge_event_mod(cls, origin: dict, new: dict):
        """Merge two event message models into one
        e.g.
        origin = {"zh_CN": "事件内容, ${key1}=${value1}。", "en_US": "event, ${key1}=${value1}."}
        new = {"zh_CN": "额外信息, ${key2}=${value2}。", "en_US": "additional_info, ${key2}=${value2}."}
        The merging result should be:
        {
            "zh_CN": "事件内容, ${key1}=${value1}。额外信息, ${key2}=${value2}。"
            "en_US": "event, ${key1}=${value1}.additional_info, ${key2}=${value2}."
        }
        """
        return {"zh_CN": origin["zh_CN"] + new["zh_CN"], "en_US": origin["en_US"] + new["en_US"]}

    @classmethod
    def fill_multilang_placeholders(cls, event_mod, **kwargs):
        """
        Replace placeholders in event_mod with multi-language values from kwargs.
        If a value in kwargs is a dict with 'zh_CN' and 'en_US', it will be replaced in
        event_mod['zh_CN'] and event_mod['en_US'] respectively.
        """
        if not event_mod or not isinstance(event_mod, dict):
            return event_mod

        zh_mod = event_mod.get("zh_CN", "")
        en_mod = event_mod.get("en_US", "")
        for k, v in kwargs.items():
            if isinstance(v, dict) and "zh_CN" in v and "en_US" in v:
                zh_mod = zh_mod.replace(f"${{{k}}}", str(v["zh_CN"]))
                en_mod = en_mod.replace(f"${{{k}}}", str(v["en_US"]))

        return {"zh_CN": zh_mod, "en_US": en_mod}


def build_event(
    event_name=None,
    user=None,
    user_type="USER",
    user_role=None,
    resources=None,
    detail=None,
    data=None,
    message=None,
    event_mod=None,
    batch=None,
    **kwargs,
):
    if message is None:
        message = EventMessage(event_mod).get_event_message(**kwargs)
    return {
        "event_name": event_name,
        "user_name": user,
        "user_role": user_role,
        "resources": resources,
        "message": message,
        "detail": detail,
        "data": data,
        "user_type": user_type,
        "batch": batch,
    }


@event_enclosure
def generate_events(
    event_name=None,
    event_state=None,
    resources=None,
    user_type="USER",
    user_name=None,
    data=None,
    message=None,
    detail=None,
    batch=None,
    user_role=None,
):
    event = "[{}:{}] [{}:{}:{}] {} {} {} {}".format(
        event_name,
        event_state,
        user_type,
        user_role,
        user_name,
        json.dumps(resources, ensure_ascii=False),
        json.dumps(data, ensure_ascii=False),
        json.dumps(message, ensure_ascii=False),
        json.dumps(detail, ensure_ascii=False),
    )
    return event


@event_enclosure
def record_event(event):
    logging.info(event)


@event_enclosure
def batch_deal(job):
    if job["event"].get("event_name", None) not in [
        "BATCH_DELETE_VM",
        "BATCH_FORCE_REBOOT_VM",
        "BATCH_SHUTDOWN_VM",
        "BATCH_FORCE_SHUTDOWN_VM",
        "BATCH_REBOOT_VM",
        "BATCH_START_VM",
        "BATCH_SUSPEND_VM",
        "BATCH_MIGRATE_VM",
        "BATCH_RESUME_VM",
        "BATCH_CREATE_VM_FROM_TEMPLATE",
    ]:
        return
    en_arg = {}
    zh_arg = {}
    for task in job["task_list"]:
        if task["reference"] in job["event"].get("batch"):
            uuid = copy.deepcopy(task["reference"])
            uuid = "u" + uuid[:8] + uuid[9:13] + uuid[14:18] + uuid[19:23] + uuid[24:]
            en_arg.update({uuid: job["event"].get("batch")[task["reference"]][task["state"]]["en_US"]})
            zh_arg.update({uuid: job["event"].get("batch")[task["reference"]][task["state"]]["zh_CN"]})
    job["event"]["detail"]["en_US"] = EventMessage().get_event_detail(job["event"]["detail"]["en_US"], **en_arg)
    job["event"]["detail"]["zh_CN"] = EventMessage().get_event_detail(job["event"]["detail"]["zh_CN"], **zh_arg)
    job["event"]["data"] = {}
