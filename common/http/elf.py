# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
import logging
from time import sleep

from common.http import restful_client as rtf_client
from common.http.exceptions import RestfulClientError


class Client:
    def __init__(self, restful_client=None, timeout=None):
        self._rest_client = restful_client or rtf_client.Client.from_config(timeout=timeout or 60)

    def raise_for_ec(self, response, err_msg=""):
        self._rest_client.raise_for_ec(response, err_msg)

    def gen_url(self, path, host="127.0.0.1"):
        return self._rest_client.gen_url(path, host=host)

    def try_migrate_vms(self, vm_ids=None, expected_vm_status=None):
        if not vm_ids:
            vm_ids = []
        request_body = {"vm_ids": vm_ids}
        if expected_vm_status:
            request_body["expected_vm_status"] = expected_vm_status
        url = self.gen_url("batch/vms/try_migrate_to_other_nodes")
        result = self._rest_client.post(url, json=request_body)
        self.raise_for_ec(
            result, err_msg="Try migrate vms failed. error message: {error}".format(error=result.get("error"))
        )
        logging.info("Try migrate vms successfully.")
        return result["data"]

    def try_migrate_vms_v2(self, vms=None):
        # Request Body:
        # [{
        #   "vm_uuid": "x",
        #   "prioirty": 100,
        #   "pg_policy": "must",
        #   "expected_vm_status": "stopped", # optional
        # }]
        if not vms:
            raise ValueError("vms parameter cannot be empty")

        url = self.gen_url("batch/vms/try_migrate_to_other_nodes_v2")
        result = self._rest_client.post(url, json={"vm_infos": vms})
        self.raise_for_ec(
            result,
            err_msg="Try migrate vms with v2 endpoint failed. error message: {error}".format(error=result.get("error")),
        )
        return result["data"]

    def get_vms(self, filter_criteria={}):
        MAX_VM_COUNT = 200
        vms = []
        start_uuid = ""
        end_uuid = ""
        skip_page = 1
        query_params = "?count={count}&skip_page={skip_page}".format(count=MAX_VM_COUNT, skip_page=skip_page)
        while True:
            if filter_criteria:
                query_params += "&filter_criteria=" + ";".join(
                    ["{k}={v}".format(k=k, v=v) for k, v in list(filter_criteria.items())]
                )
            url = self.gen_url("vms{}".format(query_params))
            result = self._rest_client.get(url)
            self.raise_for_ec(
                result, err_msg="Get vms failed. error message: {error}".format(error=result.get("error"))
            )
            data = result["data"]
            vms.extend(data["entities"])

            if not data["start_uuid"] or not data["end_uuid"] or data["page_entities"] < MAX_VM_COUNT:
                break
            start_uuid = data["start_uuid"]
            end_uuid = data["end_uuid"]
            query_params = "?count={count}&start_uuid={start_uuid}&end_uuid={end_uuid}&skip_page={skip_page}".format(
                count=MAX_VM_COUNT, start_uuid=start_uuid, end_uuid=end_uuid, skip_page=skip_page
            )

        logging.info("Get vms successfully.")
        return vms

    def get_vm(self, vm_uuid=None):
        url = self.gen_url("vms/{vm_uuid}".format(vm_uuid=vm_uuid))
        result = self._rest_client.get(url)
        self.raise_for_ec(result, err_msg="Get vm failed. error message: {error}".format(error=result.get("error")))
        return result["data"]

    def get_node_vms(self, node_ip):
        filter_criteria = {"node_ip": node_ip}
        return self.get_vms(filter_criteria)

    def get_node_running_vms(self, node_ip):
        filter_criteria = {"status": "running", "node_ip": node_ip}
        return self.get_vms(filter_criteria)

    def start_vm(self, vm_uuid, node_ip="", auto_schedule=False):
        url = self.gen_url("vms/{vm_uuid}/start".format(vm_uuid=vm_uuid))
        if auto_schedule:
            body = {"auto_schedule": auto_schedule}
        else:
            body = {"node_ip": node_ip}
        result = self._rest_client.post(url, json=body)
        self.raise_for_ec(
            result,
            err_msg="Start vm({vm_uuid}) failed. error message: {error}".format(
                vm_uuid=vm_uuid, error=result.get("error")
            ),
        )
        logging.info("Start vm({vm_uuid}) successfully.".format(vm_uuid=vm_uuid))
        return result["data"]["job_id"]

    def stop_vm(self, vm_uuid, force=False):
        url = self.gen_url("vms/{vm_uuid}/stop".format(vm_uuid=vm_uuid))
        result = self._rest_client.post(url, json={"force": force})
        self.raise_for_ec(
            result,
            err_msg="Stop vm({vm_uuid}) failed. error message: {error}".format(
                vm_uuid=vm_uuid, error=result.get("error")
            ),
        )
        logging.info("Stop vm({vm_uuid}) successfully.".format(vm_uuid=vm_uuid))
        return result["data"]["job_id"]

    def migrate_vm(self, vm_uuid, node_ip="", auto_schedule=False):
        url = self.gen_url("vms/{vm_uuid}/migrate".format(vm_uuid=vm_uuid))
        if auto_schedule:
            body = {"auto_schedule": auto_schedule}
        else:
            body = {"node_ip": node_ip}
        result = self._rest_client.post(url, json=body)
        self.raise_for_ec(
            result,
            err_msg="Migrate vm({vm_uuid}) failed. error message: {error}".format(
                vm_uuid=vm_uuid, error=result.get("error")
            ),
        )
        logging.info("Migrate vm({vm_uuid}) successfully.".format(vm_uuid=vm_uuid))
        return result["data"]["job_id"]

    def batch_migrate_vms(self, vm_uuids, node_ip="", auto_schedule=False):
        # URL: /api/v2/batch/vms/migrate
        url = self.gen_url("batch/vms/migrate")

        body = {}
        body["vm_uuids"] = vm_uuids
        if auto_schedule:
            body["auto_schedule"] = auto_schedule
        else:
            body["node_ip"] = node_ip

        result = self._rest_client.post(url, json=body)
        self.raise_for_ec(
            result, err_msg="Batch migrate vms failed. error message: {error}".format(error=result.get("error"))
        )
        logging.info("Batch migrate vms successfully.")
        return result["data"]["job_id"]

    def batch_migrate_vms_v2(self, vms):
        # URL: /api/v2/batch/vms/migrate_v2
        # support ignore placement group policy and priority
        url = self.gen_url("batch/vms/migrate_v2")

        result = self._rest_client.post(url, json={"vm_infos": vms})
        self.raise_for_ec(
            result, err_msg="Batch migrate vms failed. error message: {error}".format(error=result.get("error"))
        )
        logging.info("Batch migrate vms successfully.")
        return result["data"]["job_id"]

    def get_license(self):
        """query the license in SMTX ELF. If you want to get license in SMTX OS, pls use TUNA api
        Returns:
            license dict content
        """
        url = self.gen_url("elf/license")
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result, err_msg="Get elf license failed. error message: {error}".format(error=result.get("error"))
        )
        logging.info("Get elf license successfully.")
        return result["data"]

    def get_vms_summary(self):
        url = self.gen_url("compute/vms_summary")
        result = self._rest_client.get(url)
        self.raise_for_ec(result, err_msg=f"Get elf vms summary failed. error message: {result.get('error')}")
        logging.info("Get elf vms summary successfully.")
        return result["data"]

    def get_job(self, job_id):
        url = self.gen_url("jobs/{job_id}".format(job_id=job_id))
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result,
            err_msg="Get job({job_id}) failed. error message: {error}".format(job_id=job_id, error=result.get("error")),
        )
        logging.info("Get job({job_id}) successfully.".format(job_id=job_id))
        return result["data"]["job"]

    def wait_job_done(self, job_id, wait_timeout=1800):
        JOB_DONE = "done"
        JOB_FAILED = "failed"

        SLEEP_TIME = 5
        RETRY_TIMES = int(wait_timeout / SLEEP_TIME)

        for _ in range(RETRY_TIMES):
            job = self.get_job(job_id)
            if job["state"] == JOB_DONE:
                logging.info("Job({job_id}) is done.".format(job_id=job_id))
                return True
            elif job["state"] == JOB_FAILED:
                logging.error("Job({job_id}) is failed.".format(job_id=job_id))
                return False
            else:
                logging.info("Job({job_id}) is {state}. sleep 5 seconds.".format(job_id=job_id, state=job["state"]))
                sleep(5)

        return False

    def set_host_schedulability(self, host_uuid, schedulable=False):
        url = self.gen_url("elf/hosts/{host_uuid}/set_schedulability".format(host_uuid=host_uuid))
        body = {"schedulable": schedulable}
        result = self._rest_client.post(url, json=body)
        self.raise_for_ec(
            result,
            err_msg="Set host({host_uuid}) schedulability failed. error message: {error}".format(
                host_uuid=host_uuid, error=result.get("error")
            ),
        )
        logging.info("Set host({host_uuid}) schedulability successfully.".format(host_uuid=host_uuid))
        return True

    def get_placement_groups(self):
        # Current Elf placement groups API is not implemented start uuid params
        MAX_GROUP_COUNT = 1024
        url = self.gen_url("placement_groups?count={count}&skip_page=1".format(count=MAX_GROUP_COUNT))
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result, err_msg="Get VM placement groups failed. error message: {error}".format(error=result.get("error"))
        )
        data = result["data"]
        return data["entities"]

    def get_vm_exclusive_cpus(self, host, max_try=5):
        """query the CPUs which have been used for VM CPU exclusive feature.
        Args:
            host (string): Data IP of host
            max_try (int): max number of trying
        Returns:
            list of int: The CPUs which have been used for VM CPU exclusive.
            list of uuid: The UUIDs of VMs which CPU exclusive enabled
            dict[vm_uuid, list[int]]: The exclusive CPUs of each VM
        """

        url = self.gen_url("batch/vms/get_exclusive_cpu_info")
        last_error = None
        for n in range(max_try):
            try:
                result = self._rest_client.get(url)
                self.raise_for_ec(
                    result,
                    err_msg="Get VM exclusive CPUs failed. error message: {error}".format(error=result.get("error")),
                )
                last_error = None
                break
            except Exception as e:
                if isinstance(e, RestfulClientError) and e.http_status_code == 404:
                    logging.info("api not exist %s, %s", url, e)
                    return [], [], {}
                logging.warning("query vm exclusive cpu info failed, try %s/%s, %s", n + 1, max_try, e)
                last_error = e

        if last_error:
            # some error may occurred in cluster, ignore exclusive CPUs of VMs
            raise last_error

        cpu_used = []
        vm_uuids = []
        cpus_of_vms = {}
        for host_vm_exclusive_cpu_info in result["data"].get("vms", []):
            if host_vm_exclusive_cpu_info.get("node_ip") == host:
                vm_uuid = host_vm_exclusive_cpu_info["uuid"]
                vcpu_to_pcpu = host_vm_exclusive_cpu_info.get("cpu_exclusive", {}).get("vcpu_to_pcpu", {})
                cpus = [int(cpu) for cpu in list(vcpu_to_pcpu.values())]

                cpu_used.extend(cpus)
                vm_uuids.append(vm_uuid)
                cpus_of_vms[vm_uuid] = cpus

        return cpu_used, vm_uuids, cpus_of_vms

    def vm_exclusive_cpus_switch(self, uuid, cpu_exclusive_enable):
        # query current 'ha' value, 'ha' must be specified when update 'cpu_exclusive'
        vm_info = self.get_vm(uuid)
        ha = vm_info["ha"]

        url = self.gen_url("vms/" + uuid)
        data = {"cpu_exclusive": {"expected_enabled": cpu_exclusive_enable}, "ha": ha}
        result = self._rest_client.put(url, json=data)

        self.raise_for_ec(
            result, err_msg="Switch VM CPU exclusive failed. error message: {error}".format(error=result.get("error"))
        )

        return result["data"].get("job_id")

    def get_unrealized_exclusive_cpus(self, node_ip: str) -> tuple[dict[str, int], list[str]]:
        """query the VMs with unrealized exclusive cpus
        Args:
            host (string): Data IP of host
            max_try (int): max number of trying
        Returns:
            dict[str, int]: The exclusive CPUs required of each VM
            list[str]: UUIDs of all suspended VMs with cpu exclusive feature enabled
        """
        exclusive_cpu_unrealized_vms = {}

        try:
            node_running_vms = self.get_vms({"status": "running", "node_ip": node_ip})
            node_suspended_vms = self.get_vms({"status": "suspended", "node_ip": node_ip})
        except Exception as e:
            logging.warning("get vms failed, %s", e)
            return {}, []

        for vm in node_running_vms + node_suspended_vms:
            expected_enabled = vm.get("cpu_exclusive", {}).get("expected_enabled", False)
            actual_enabled = vm.get("cpu_exclusive", {}).get("actual_enabled", False)
            vcpu = vm.get("vcpu", 0)
            if expected_enabled and not actual_enabled:
                exclusive_cpu_unrealized_vms[vm["uuid"]] = int(vcpu)

        suspended_exclusive_cpu_vms = []
        for vm in node_suspended_vms:
            expected_enabled = vm.get("cpu_exclusive", {}).get("expected_enabled", False)
            if expected_enabled:
                suspended_exclusive_cpu_vms.append(vm["uuid"])

        return exclusive_cpu_unrealized_vms, suspended_exclusive_cpu_vms

    def config_libvirt_tcp_port(self, action="open", host="127.0.0.1"):
        url = self.gen_url("elf/libvirt_security/config_tcp_port", host=host)
        result = self._rest_client.post(url, json={"action": action})

        self.raise_for_ec(
            result,
            err_msg="Failed to config libvirt tcp port, error message: {error}".format(error=result.get("error")),
        )
