# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
from smartx_proto.errors import pyerror_pb2 as py_error


class RestfulClientError(Exception):
    def __init__(self, msg="", user_code=None, http_status_code=None):
        self.user_code = user_code if user_code else py_error.REST_UNKNOWN_ERROR
        self.http_status_code = http_status_code
        self.uc_name = py_error.ErrorCode.Name(self.user_code)
        self.message = "[{}][{}]{}".format(self.uc_name, self.http_status_code, msg)

    def __str__(self):
        return self.message
