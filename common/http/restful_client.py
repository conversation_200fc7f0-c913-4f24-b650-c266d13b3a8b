# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
from collections import OrderedDict
import logging
import threading

import requests
from requests import exceptions

from common.config import constant
from common.http.exceptions import RestfulClientError as rtf_exceptions
from grpc_client.crab import client as crab_client
from smartx_proto.errors import pyerror_pb2


class _TokenCache:
    def __init__(self):
        self._tokens = OrderedDict()
        self._token_lens = 10
        self._tokens_lock = threading.RLock()

    def save(self, svc_username, svc_password, token):
        with self._tokens_lock:
            self._tokens.update({"{}_{}".format(svc_username, svc_password): token})
            if len(self._tokens) > self._token_lens:
                self._tokens.popitem(last=False)

    def get(self, svc_username, svc_password):
        return self._tokens.get("{}_{}".format(svc_username, svc_password))


class Client:
    _tokens = _TokenCache()
    session = requests.Session()

    def __init__(self, svc_username, svc_password, timeout=5):
        self._svc_username = svc_username
        self._svc_password = svc_password
        self._timeout = timeout

    @classmethod
    def get_token(cls, svc_username, svc_password):
        cc = crab_client.CrabClient()
        cached_token = cls._tokens.get(svc_username, svc_password)

        # Check if the token is still valid
        if not cached_token or not cc.get_current_user(cached_token):
            session = cc.create_session(svc_username, svc_password)
            if session is not None:
                cls._tokens.save(svc_username, svc_password, session["token"])
            else:
                logging.warning("Failed to create session")

        return cls._tokens.get(svc_username, svc_password)

    @classmethod
    def gen_url(cls, path, host="127.0.0.1", port=80, version="v2"):
        """
        :type path: str
        :param path: for example cluster/storage
        :type host: str
        :param host:
        :type port: int
        :param port:
        :type version: str
        :param version:
        :return: just like http://127.0.0.1:80/api/v2/cluster/storage
        """
        return "{protocol}://{host}:{port}/api/{version}/{path}".format(
            protocol=("https" if port == 443 else "http"),
            path=(path[1:] if path.startswith("/") else path),
            host=host,
            port=port,
            version=version,
        )

    @classmethod
    def raise_for_ec(cls, response, err_msg=""):
        if response.get("ec") != "EOK":
            logging.error(err_msg)
            raise rtf_exceptions(err_msg, user_code=pyerror_pb2.ErrorCode.Value(response["ec"]), http_status_code=200)

    @classmethod
    def from_config(cls, timeout=5, service_user_name=None, service_password=None):
        return cls(
            service_user_name or constant.DEFAULT_SERVICE_USERNAME,
            service_password or constant.DEFAULT_SERVICE_PASSWORD,
            timeout,
        )

    @property
    def headers(self):
        token = self.get_token(self._svc_username, self._svc_password)
        return {constant.AUTH_TOKEN_NAME: token, constant.AUTH_TOKEN_NAME_NEW: token}

    def request(self, method, url, **kwargs):
        return request_with_session(self.session, False, self._timeout, method, url, **kwargs)

    def get(self, url, params=None):
        return self.request("get", url, headers=self.headers, timeout=self._timeout, params=params)

    def post(self, url, data=None, json=None):
        return self.request("post", url, headers=self.headers, timeout=self._timeout, data=data, json=json)

    def put(self, url, data=None, json=None):
        return self.request("put", url, headers=self.headers, timeout=self._timeout, data=data, json=json)

    def delete(self, url, data=None, json=None):
        return self.request("delete", url, headers=self.headers, timeout=self._timeout, data=data, json=json)


class RemoteClient:
    session = requests.Session()

    def __init__(self, svc_username, svc_password, svc_ip, svc_port=80, timeout=5):
        self._svc_username = svc_username
        self._svc_password = svc_password
        self._svc_ip = svc_ip
        self._svc_port = svc_port
        self._timeout = timeout
        self._protocol = "https" if self._svc_port == 443 else "http"

    @classmethod
    def raise_for_ec(cls, response, err_msg=""):
        if response.get("ec") != "EOK":
            logging.error(err_msg)
            try:
                user_code = pyerror_pb2.ErrorCode.Value(response["ec"])
            except ValueError:
                logging.info(
                    f"[RemoteClient.raise_for_ec] Unknown error code: {response.get('ec')}, "
                    f"will change to REST_UNKNOWN_ERROR"
                )
                user_code = None

            raise rtf_exceptions(err_msg, user_code=user_code, http_status_code=200)

    def _gen_url(self, path, version):
        """
        :type path: str
        :param path: for example cluster/storage
        :param version:
        :return: just like http://127.0.0.1:80/api/v2/cluster/storage
        """
        return "{protocol}://{host}:{port}/api/{version}/{path}".format(
            protocol=self._protocol,
            path=(path[1:] if path.startswith("/") else path),
            host=self._svc_ip,
            port=self._svc_port,
            version=version,
        )

    def _request(self, method, url, token, **kwargs):
        kwargs["verify"] = False
        if not token:
            token = self.get_token()
            try:
                return request_with_session(
                    self.session,
                    False,
                    self._timeout,
                    method,
                    url,
                    headers={constant.AUTH_TOKEN_NAME: token, constant.AUTH_TOKEN_NAME_NEW: token},
                    **kwargs,
                )
            finally:
                if token:
                    self.release_token(token)
        else:
            return request_with_session(
                self.session,
                False,
                self._timeout,
                method,
                url,
                headers={constant.AUTH_TOKEN_NAME: token, constant.AUTH_TOKEN_NAME_NEW: token},
                **kwargs,
            )

    def get_token(self):
        r = request_with_session(
            session=self.session,
            ignore_empty_response=False,
            req_timeout=self._timeout,
            method="post",
            url=self._gen_url("sessions", "v3"),
            json={"username": self._svc_username, "password": self._svc_password},
            verify=False,
        )
        return r["token"]

    def release_token(self, token):
        if token:
            request_with_session(
                session=self.session,
                ignore_empty_response=False,
                req_timeout=self._timeout,
                method="delete",
                url=self._gen_url("session", "v3"),
                headers={"Grpc-Metadata-Token": token},
                verify=False,
            )

    def get(self, path, version="v2", params=None, token=None):
        return self._request("get", url=self._gen_url(path, version), token=token, params=params)

    def post(self, path, version="v2", data=None, json=None, token=None):
        return self._request("post", url=self._gen_url(path, version), token=token, data=data, json=json)

    def put(self, path, version="v2", data=None, json=None, token=None):
        return self._request("put", url=self._gen_url(path, version), token=token, data=data, json=json)

    def delete(self, path, version="v2", data=None, token=None):
        return self._request("delete", url=self._gen_url(path, version), token=token, data=data)


def request_with_session(session, ignore_empty_response, req_timeout, method, url, **kwargs):
    try:
        kwargs["timeout"] = req_timeout
        resp = getattr(session, method.lower())(url, **kwargs)
        resp.raise_for_status()
    except exceptions.Timeout:
        raise rtf_exceptions("Request ({}) timeout({}).".format(url, req_timeout), pyerror_pb2.REST_API_TIMEOUT)
    except exceptions.ConnectionError as e:
        raise rtf_exceptions(str(e), pyerror_pb2.REST_API_CONNECTION_ERROR)
    except exceptions.HTTPError as e:
        # test e.response will return false if status code is not 2xx
        status_code = e.response.status_code if e.response is not None else None
        raise rtf_exceptions(str(e), pyerror_pb2.REST_API_HTTP_ERROR, http_status_code=status_code)
    else:
        try:
            if ignore_empty_response and not resp.text:
                return None
            return resp.json()
        except ValueError:
            raise rtf_exceptions(resp.text, pyerror_pb2.REST_API_JSON_DECODE_ERROR, http_status_code=resp.status_code)
