# Copyright (c) 2013-2023, SMARTX
# All rights reserved.
from common.http.restful_client import RemoteClient

RSYNC_TASK_HOST = "127.0.0.1:10201:10206"


# Note: In the ZBS implementation, the bps_max parameter is measured in bytes per second,
# including both request and response.
class Client:
    def __init__(self, restful_client: RemoteClient):
        """
        No captible with restful_client.Client,
        It's design for call remote restful api

        :param restful_client:
        :type restful_client: RemoteClient
        """
        self._rest_client = restful_client

    def raise_for_ec(self, response, err_msg=""):
        self._rest_client.raise_for_ec(response, err_msg)

    def copy_volume(
        self,
        src_volume_id,
        dst_volume_id,
        task_id=None,
        preferred_cid=None,
        src_hosts=RSYNC_TASK_HOST,
        dst_hosts=RSYNC_TASK_HOST,
        token=None,
        bps_bytes_max=None,
    ):
        body = {
            "src_hosts": src_hosts,
            "dst_hosts": dst_hosts,
            "src_volume_id": src_volume_id,
            "dst_volume_id": dst_volume_id,
        }
        if task_id:
            body["id"] = task_id
        if preferred_cid:
            body["preferred_cid"] = preferred_cid
        if bps_bytes_max:
            # Note: For ZBS, the unit of the bps_max parameter is in bytes.
            body["bps_max"] = bps_bytes_max

        result = self._rest_client.post("task_center/copy_volume", json=body, token=token)
        self.raise_for_ec(
            result,
            err_msg=f"Create copy volume task failed, error message: {result.get('error')}, ec: {result.get('ec')}",
        )
        return result["data"]

    def show_task(self, task_id, token=None):
        result = self._rest_client.get(f"task_center/copy_volume/{task_id}", token=token)
        self.raise_for_ec(
            result,
            err_msg=f"Get job({task_id}) failed. error message: {result.get('error')}, ec: {result.get('ec')}",
        )
        return result["data"]

    def set_bps_max(self, task_id, bps_bytes_max, token=None):
        body = {
            "bps_max": bps_bytes_max,  # Note: For ZBS, the unit of the bps_max parameter is in bytes
        }
        result = self._rest_client.post(f"task_center/copy_volume/{task_id}/set_bps_max", json=body, token=token)
        self.raise_for_ec(
            result,
            err_msg=f"Get job({task_id}) failed. error message: {result.get('error')}, ec: {result.get('ec')}",
        )
        return result["data"]
