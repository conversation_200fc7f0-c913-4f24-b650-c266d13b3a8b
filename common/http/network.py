# Copyright (c) 2013-2023, SMARTX
# All rights reserved.


from common.http import restful_client as rtf_client

# The type of VDS returned by 'GET /api/v2/network/vds' is a number.
# The VDS type of VPC is fixed to 3
VPC_VDS_TYPE = 3

NETWORK_TYPE_VPC_SYS = "vpc_sys"

INTPORT_FUNCTION_VPC = "port-vpc"

VPC_VDS_OVSBR_NAME = "vpcbr"


class Client:
    def __init__(self, host_ip="127.0.0.1", restful_client=None):
        self._rest_client = restful_client or rtf_client.Client.from_config(timeout=30)
        self._host_ip = host_ip

    def raise_for_ec(self, response, err_msg=""):
        self._rest_client.raise_for_ec(response, err_msg)

    def gen_url(self, path):
        return self._rest_client.gen_url(path, host=self._host_ip)

    def validate_create_vpc_nics(self, vnic_group_uuid, vpc_nics):
        """Validate whether the input parameter can create VPC NICs successfully.
        :param vnic_group_uuid:  The owner of VPC NICs, for example, VM uuid.
        :param vpc_nics:   A list of VPC NICs parameters, the format of each element is:
        {
            "vpc_uuid": string
            "subnet_uuid": string
            "mac_addr": string
            "ipv4_addr": string
            "floating_ip_uuid": string
            "vnic_uuid": string
            "vlan_uuid": string
            "ovsbr_name": string
            "skip_ip_validation": boolean
        }

        the format of restful client return value is:
        {
            "ec": "EOK",
            "error": {},
            "data": {
                "vnics": [
                    {
                        "code": 200,
                        "data": "test-vnic-uuid-1",
                        "reason": "",
                        "message": "",
                        "error": null
                    }
                ]
            }
        }
        """

        # Using 'validate' in url path will trigger a bug in the framework. So the network rest-server use
        # 'validator' as the resource action.
        url = self.gen_url("network/vnic_group/{}/validator".format(vnic_group_uuid))
        body = {"vnics": vpc_nics}
        result = self._rest_client.post(url, json=body)
        self.raise_for_ec(
            result,
            err_msg="Validate create VPC NICs for vnic_group({}) failed, request body is {}, error is {}".format(
                vnic_group_uuid, body, result.get("error")
            ),
        )

        return result["data"]["vnics"]

    def create_vpc_nics(self, cluster_uuid, vnic_group_uuid, vpc_nics):
        """Use the input parameter can create VPC NICs successfully.
        :param cluster_uuid: The cluster_uuid of current cluster.
        :param vnic_group_uuid:  The owner of VPC NICs, for example, VM uuid.
        :param vpc_nics:   A list of VPC NICs parameters, the format of each element is:
        {
            "vpc_uuid": string
            "subnet_uuid": string
            "mac_addr": string
            "ipv4_addr": string
            "floating_ip_uuid": string
            "vnic_uuid": string
            "vlan_uuid": string
            "ovsbr_name": string
            "skip_ip_validation": boolean
        }

        restful client result format:
        SUCCESS RESULT:
        {
            "ec": "EOK",
            "error": {},
            "data": {
                "vnics": [
                    {
                        "code": 201,
                        "error": null,
                        "reason": "",
                        "message": ""
                    }
                ]
            }
        }
        FAILURE RESULT:
        {
            "ec": "EOK",
            "error": {},
            "data": {
                "vnics": [
                    {
                        "code": 409,
                        "error": {
                            "ErrStatus": {
                                "metadata": {},
                                "status": "Failure",
                                "message": "vnics.vpc.everoute.io \"gmy-test-vnic-uuid-1\" already exists",
                                "reason": "AlreadyExists",
                                "details": {
                                    "name": "gmy-test-vnic-uuid-1",
                                    "group": "vpc.everoute.io",
                                    "kind": "vnics"
                                },
                                "code": 409
                            }
                        },
                        "reason": "AlreadyExists",
                        "message": "xxxxxxxxx"
                    }
                ]
            }
        }
        """
        url = self.gen_url("network/vnic_group")
        body = {"cluster_uuid": cluster_uuid, "vnic_group_uuid": vnic_group_uuid, "vnics": vpc_nics}
        result = self._rest_client.post(url, json=body)
        self.raise_for_ec(
            result,
            err_msg="Create VPC NICs for vnic_group({}) failed, request body is {}, error is {}".format(
                vnic_group_uuid, body, result.get("error")
            ),
        )

        return result["data"]["vnics"]

    def get_vpc_nics(self, vnic_group_uuid):
        url = self.gen_url("network/vnic_group/{}".format(vnic_group_uuid))
        result = self._rest_client.get(url)
        # RESULT FORMAT:
        # {
        #     "ec": "EOK",
        #     "error": {},
        #     "data": {
        #         "vnics": [
        #             {
        #                 "vnic_uuid": "gmy-test-vnic-uuid-1",
        #                 "vnic_group_uuid": "gmy-test-vm-uuid-1",
        #                 "cluster_uuid": "79393f79-7d2f-4cb7-bd78-b3c9cdb08085",
        #                 "vpc_uuid": "4a45c6b6-199a-45f6-a561-c9182f8aa577",
        #                 "subnet_uuid": "default-4a45c6b6-199a-45f6-a561-c9182f8aa577",
        #                 "ipv4_addr": "",
        #                 "mac_addr": "52:54:00:22:f4:9d",
        #                 "floating_ip_uuid": ""
        #             },
        #             {
        #                 "vnic_uuid": "gmy-test-vnic-uuid-2",
        #                 "vnic_group_uuid": "gmy-test-vm-uuid-1",
        #                 "cluster_uuid": "79393f79-7d2f-4cb7-bd78-b3c9cdb08085",
        #                 "vpc_uuid": "4a45c6b6-199a-45f6-a561-c9182f8aa577",
        #                 "subnet_uuid": "default-4a45c6b6-199a-45f6-a561-c9182f8aa577",
        #                 "ipv4_addr": "*************",
        #                 "mac_addr": "52:54:00:22:f4:9e",
        #                 "floating_ip_uuid": ""
        #             }
        #         ],
        #         "error": null,
        #         "reason": "",
        #         "message": "",
        #         "code": 200
        #     }
        # }
        self.raise_for_ec(
            result,
            err_msg="Get VPC NICs of vnic_group({}) failed, error is {}".format(vnic_group_uuid, result.get("error")),
        )

        return {nic["vnic_uuid"]: nic for nic in result["data"]["vnics"]}

    def delete_vpc_nics(self, vnic_group_uuid, vnic_uuids):
        """Delete certain VNICs of a vnic_group"""
        url = self.gen_url("network/vnic_group/{}".format(vnic_group_uuid))
        result = self._rest_client.delete(url, json={"vnics": [{"vnic_uuid": x} for x in vnic_uuids]})
        self.raise_for_ec(
            result,
            err_msg="Delete VPC NICs({})failed for vnic_group({}), error is {}".format(
                vnic_uuids, vnic_group_uuid, result.get("error")
            ),
        )

        return result["data"]["vnics"]

    def create_vpc_nic_snapshots(self, cluster_uuid, vnic_snapshot_group_uuid, vnic_group_uuid, vnics):
        """Create VPC NIC snapshot for a vnic_snapshot_group_uuid
        :param cluster_uuid: cluster_uuid of current cluster
        :param vnic_snapshot_group_uuid: The group uuid of vnic snapshot, for example, vm snapshot uuid
        :param vnic_group_uuid: The owner of VPC NICs, for example, VM uuid.
        :param vnics: info of vnics
        :return: A list, format like:
        [
            {
                "error": None,
                "reason": "",
                "message": "",
                "code": 201,
                "vnic_snapshot_uuid": "001d3209-9ebb-4cf1-bccc-2ffc6e7502b6",
                "vnic_uuid": "gmy-test-vnic-uuid-2",
                "vpc_uuid": "4a45c6b6-199a-45f6-a561-c9182f8aa577",
                "subnet_uuid": "default-4a45c6b6-199a-45f6-a561-c9182f8aa577",
                "floating_ip_uuid": ""
            },
            {
                "error": None,
                "reason": "",
                "message": "",
                "code": 201,
                "vnic_snapshot_uuid": "518faa1c-7488-4ecd-909d-a72949c8f567",
                "vnic_uuid": "gmy-test-vnic-uuid-1",
                "vpc_uuid": "4a45c6b6-199a-45f6-a561-c9182f8aa577",
                "subnet_uuid": "default-4a45c6b6-199a-45f6-a561-c9182f8aa577",
                "floating_ip_uuid": ""
            }
        ]
        """
        url = self.gen_url("network/vnic_group_snapshot")
        body = {
            "cluster_uuid": cluster_uuid,
            "vnic_group_snapshot_uuid": vnic_snapshot_group_uuid,
            "vnic_group_uuid": vnic_group_uuid,
            "vnics": [{"vnic_uuid": x["vnic_uuid"], "vnic_snapshot_uuid": x["vnic_snapshot_uuid"]} for x in vnics],
        }

        result = self._rest_client.post(url, json=body)
        self.raise_for_ec(
            result,
            err_msg="Create VPC NIC snapshot for vnic_group({}) failed, vnic_snapshot_group_uuid={}, vnics={}, "
            "error is {}".format(vnic_group_uuid, vnic_snapshot_group_uuid, vnics, result.get("error")),
        )

        return result["data"]["vnic_snapshots"]

    def delete_vpc_nic_snapshots(self, vnic_group_snapshot_uuid, vnic_snapshot_uuids):
        """
        :param vnic_group_snapshot_uuid: snapshot_uuid of vnic_group, for example, vm_snapshot_uuid
        :param vnic_snapshot_uuids: A list of vnic_snapshot_uuid
        :return: A list representing operation_result, for example:
        [
            {
                "code": 200,
                "data": "5acd29c9-4aa7-4d87-bf2e-3e8adb2be25c",
                "reason": "",
                "message": "",
                "error": None,
            },
            {
                "code": 200,
                "data": "dd00f1a8-361b-485c-922b-6abd64934e45",
                "reason": "",
                "message": "",
                "error": None,
            }
        ]
        """
        url = self.gen_url("network/vnic_group_snapshot/{}".format(vnic_group_snapshot_uuid))
        body = {"vnic_snapshots": [{"uuid": x} for x in vnic_snapshot_uuids]}
        result = self._rest_client.delete(url, json=body)

        self.raise_for_ec(
            result,
            err_msg="Delete VPC VNIC snapshots for vnic_group_snapshot_uuid({}) failed, vnic_snapshot_uuids={}, "
            "error is {}".format(vnic_group_snapshot_uuid, vnic_group_snapshot_uuid, result.get("error")),
        )

        return result["data"]

    def rollback_vpc_nics(self, vnic_group_snapshot_uuid, vnic_snapshot_uuids):
        url = self.gen_url("network/vnic_group_snapshot/{}/rollback".format(vnic_group_snapshot_uuid))
        body = {"vnics": [{"vnic_snapshot_uuid": x} for x in vnic_snapshot_uuids]}
        result = self._rest_client.post(url, json=body)

        # Result format:
        # {
        #     "ec": "EOK",
        #     "error": {},
        #     "data": [
        #         {
        #             "code": 200,
        #             "data": "070665af-8a4a-4d34-87f1-e9513bc26391",
        #             "reason": "",
        #             "message": "",
        #             "error": null
        #         },
        #         {
        #             "code": 200,
        #             "data": "9274ca01-2791-4ad6-a30e-5ce8dce50bb8",
        #             "reason": "",
        #             "message": "",
        #             "error": null
        #         }
        #     ]
        # }

        self.raise_for_ec(
            result,
            err_msg="Rollback VPC VNIC snapshots for vnic_group_snapshot_uuid({}) failed, vnic_snapshot_uuids={}, "
            "error is {}".format(vnic_group_snapshot_uuid, vnic_group_snapshot_uuid, result.get("error")),
        )

        return result["data"]

    def is_cluster_associated_to_vpc_service(self):
        url = self.gen_url("network/vds")
        result = self._rest_client.get(url)
        self.raise_for_ec(result, err_msg="List VDS failed, error={}".format(result.get("error")))

        all_vds = result["data"]
        if all([x.get("type") != VPC_VDS_TYPE for x in all_vds]):
            return False

        return True

    def update_vpc_nics_mac_addr(self, vnic_group_uuid, vpc_vnics):
        """
        :param vnic_group_uuid: The owner of VPC NICs, for example, VM uuid.
        :param vpc_vnics: List of vnics. Format: [{"vnic_uuid": "xxx", "mac_addr": "xxx}]
        """
        url = self.gen_url("network/vnic_group/{}".format(vnic_group_uuid))
        body = {"vnics": vpc_vnics}
        result = self._rest_client.put(url, json=body)
        self.raise_for_ec(
            result,
            err_msg="Update VPC NIC mac address for vnic_group({}) failed, vpc_nics to be updated={}, "
            "error is {}".format(vnic_group_uuid, vpc_vnics, result.get("error")),
        )

        return result["data"]["vnics"]

    def update_vpc_nics_cluster_uuid(self, vnic_group_uuid, vpc_vnics, dst_cluster_uuid):
        """Update the cluster uuid of VPC NICs after VM is migrated to other cluster
        :param vnic_group_uuid:  uuid of nic group, for example, vm_uuid
        :param vpc_vnics: A list of VPC NICs, format example:
        [
            {"vnic_uuid": "xxx"}, {"vnic_uuid": "xxxx"}
        ]
        :param dst_cluster_uuid: uuid of dst cluster
        """
        url = self.gen_url("network/vnic_group/{}".format(vnic_group_uuid))
        body = {"cluster_uuid": dst_cluster_uuid, "vnics": vpc_vnics}
        result = self._rest_client.put(url, json=body)
        self.raise_for_ec(
            result,
            err_msg="Update VPC NIC cluster uuid for vnic_group({}) failed, vpc_nics to be updated={}, "
            "error is {}".format(vnic_group_uuid, vpc_vnics, result.get("error")),
        )

        return result["data"]["vnics"]

    def page_query_vpc_nics(self, cluster_uuid, page_size, page_token):
        """Query VPC NICs using the page query API
        :param cluster_uuid: The UUID of cluster.
        :param page_size: The count of VPC NIC snapshot in one page. The page_size limit in network API is 1000
        :param page_token: A string representing the page query continue position. If page_token is not in query
        parameters, the query should start from the beginning.
        :return: (A list of VPC NICs, page_token). If there's still VPC resources left after the query,
        page_token will be returned. It can be used for next query. If all the resources are already queried,
        'continue' won't be in response.
        """
        base_url = "network/vnic_group"
        url = self.gen_url(base_url)
        query_param = {"cluster_uuid": cluster_uuid, "count": page_size}
        if page_token:
            query_param["continue"] = page_token
        result = self._rest_client.get(url, params=query_param)
        # RESULT FORMAT:
        # {
        #     "ec": "EOK",
        #     "error": {},
        #     "data": {
        #         "continue": "eyJ2IjoibWV0YS5rOHMuaW8vdjEiLCJydiI6MjU4NTM3OTAsInN0YXJ0IjoiZGVmYXVsdC8wM2YzYzAxZC00MTY
        #         2LTQ5ZGQtOWY2ZS02MTA2OTAxNjY0NDJcdTAwMDAifQ",
        #         "vnics": [
        #             {
        #                 "vnic_uuid": "18e7e18b-79dd-495d-b6c4-759a38cfc491",
        #                 "vnic_group_uuid": "7f2dea05-b46d-4d76-b068-22cb8a284432",
        #                 "cluster_uuid": "79393f79-7d2f-4cb7-bd78-b3c9cdb08085",
        #                 "vpc_uuid": "4a45c6b6-199a-45f6-a561-c9182f8aa577",
        #                 "subnet_uuid": "default-4a45c6b6-199a-45f6-a561-c9182f8aa577",
        #                 "ipv4_addr": "",
        #                 "mac_addr": "52:54:00:37:00:7a",
        #                 "floating_ip_uuid": ""
        #             },
        #         ]
        #     }
        # }
        self.raise_for_ec(result, err_msg="Page query VPC NICs failed, error is {}".format(result.get("error")))

        return result["data"]["vnics"], result["data"].get("continue")

    def get_all_vpc_nics_relation(self, cluster_uuid):
        """Get all VPC NICs relationship of a cluster
        : return: A dict. Key is vnic_group_uuid. Value is a list of vnic_uuids.
        RETURN VALUE FORMAT:
        {
            "vnic_group_uuid1": ["vnic_uuid_1", "vnic_uuid_2"],
            "vnic_group_uuid2": ["vnic_uuid_3", "vnic_uuid_4"],
        }
        """
        page_size = 500
        page_token = None
        vm_to_nics_relation = {}

        while True:
            page_query_result, page_token = self.page_query_vpc_nics(cluster_uuid, page_size, page_token)
            for x in page_query_result:
                vm_to_nics_relation.setdefault(x["vnic_group_uuid"], []).append(x["vnic_uuid"])
            if page_token is None:
                break

        return vm_to_nics_relation

    def page_query_vpc_nics_snapshots(self, cluster_uuid, page_size, page_token):
        """Query VPC NICs snapshots using the page query API
        :param cluster_uuid: The UUID of cluster.
        :param page_size: The count of VPC NIC snapshot in one page. The page_size limit in network API is 1000.
        :param page_token: A string representing the page query continue position. If page_token is not in query
        parameters, the query should start from the beginning.
        :return: (A list of VPC NIC snapshots, page_token). If there's still VPC resources left after the query,
        page_token will be returned. It can be used for next query. If all the resources are already queried,
        'continue' won't be in response.
        """
        base_url = "network/vnic_group_snapshot"
        url = self.gen_url(base_url)
        query_param = {"cluster_uuid": cluster_uuid, "count": page_size}
        if page_token:
            query_param["continue"] = page_token
        result = self._rest_client.get(url, params=query_param)
        # RESULT FORMAT
        # {
        #     "ec": "EOK",
        #     "error": {},
        #     "data": {
        #         "continue": "eyJ2IjoibWV0YS5rOHMuaW8vdjEiLCJydiI6MjU4NTM3OTAsInN0YXJ0IjoiZGVmYXVsdC8wM2YzYzAxZC00MTY
        #         2LTQ5ZGQtOWY2ZS02MTA2OTAxNjY0NDJcdTAwMDAifQ",
        #         "data": {
        #             "vnic_snapshots": [
        #                 {
        #                     "vnic_snapshot_uuid": "03f3c01d-4166-49dd-9f6e-************",
        #                     "vnic_group_snapshot_uuid": "121f5056-4ca2-460c-b7d6-b6398428a433",
        #                     "vnic_group_uuid": "fb4c6b24-4570-4418-9cb8-732bfb2e9543",
        #                     "ipv4_addr": "***********",
        #                     "mac_addr": "52:54:00:78:36:c2",
        #                     "vpc_uuid": "4a45c6b6-199a-45f6-a561-c9182f8aa577",
        #                     "vnic_uuid": "72397a3e-8b7e-41c4-88ca-9417396e4360",
        #                     "subnet_uuid": "default-4a45c6b6-199a-45f6-a561-c9182f8aa577",
        #                     "floating_ip_uuid": ""
        #                 },
        #             ]
        #         }
        #     }
        # }
        self.raise_for_ec(
            result, err_msg="Page query VPC NIC snapshots failed, error is {}".format(result.get("error"))
        )

        return result["data"]["data"]["vnic_snapshots"], result["data"].get("continue")

    def get_all_vpc_nic_snapshots_relation(self, cluster_uuid):
        """Get all VPC NIC snapshot relationship of a cluster
        : return: A dict. Key is vnic_group_snapshot_uuid. Value is a list of vnic snapshot uuids.
        RETURN VALUE FORMAT:
        {
            "vnic_group_snapshot_uuid1": ["vnic_snapshot_uuid_1", "vnic_snapshot_uuid_2"],
            "vnic_group_snapshot_uuid2": ["vnic_snapshot_uuid_3", "vnic_snapshot_uuid_4"],
        }
        """

        page_size = 500
        page_token = None
        vm_snapshot_to_nic_snapshots_relation = {}

        while True:
            page_query_result, page_token = self.page_query_vpc_nics_snapshots(cluster_uuid, page_size, page_token)
            for x in page_query_result:
                vm_snapshot_to_nic_snapshots_relation.setdefault(x["vnic_group_snapshot_uuid"], []).append(
                    x["vnic_snapshot_uuid"]
                )
            if page_token is None:
                break

        return vm_snapshot_to_nic_snapshots_relation

    def get_vpc_nic_snapshots(self, vnic_group_snapshot_uuid):
        url = self.gen_url("network/vnic_group_snapshot/{}".format(vnic_group_snapshot_uuid))
        result = self._rest_client.get(url)
        # RESULT FORMAT:
        # {
        #     "ec": "EOK",
        #     "error": {},
        #     "data": {
        #         "data": {
        #             "vnic_snapshots": [
        #                 {
        #                     "vnic_snapshot_uuid": "0090cbdd-734b-4c6e-b2d2-a9ac4325bcef",
        #                     "vnic_group_snapshot_uuid": "6f8c794e-754c-4538-a256-905129bae806",
        #                     "vnic_group_uuid": "02c881a1-7a17-4c36-bde9-2df58b67ea77",
        #                     "ipv4_addr": "",
        #                     "mac_addr": "52:54:00:8c:40:ac",
        #                     "vpc_uuid": "4a45c6b6-199a-45f6-a561-c9182f8aa577",
        #                     "vnic_uuid": "3d2bf4f3-55e4-40a4-a2d0-8f46cb79e4d5",
        #                     "subnet_uuid": "default-4a45c6b6-199a-45f6-a561-c9182f8aa577",
        #                     "floating_ip_uuid": ""
        #                 },
        #                 {
        #                     "vnic_snapshot_uuid": "8ef24b15-4476-4999-96d2-a9e54032eb7c",
        #                     "vnic_group_snapshot_uuid": "6f8c794e-754c-4538-a256-905129bae806",
        #                     "vnic_group_uuid": "02c881a1-7a17-4c36-bde9-2df58b67ea77",
        #                     "ipv4_addr": "",
        #                     "mac_addr": "52:54:00:10:6e:41",
        #                     "vpc_uuid": "4a45c6b6-199a-45f6-a561-c9182f8aa577",
        #                     "vnic_uuid": "f2d8c33f-acc8-41c8-8153-14c320f2de66",
        #                     "subnet_uuid": "default-4a45c6b6-199a-45f6-a561-c9182f8aa577",
        #                     "floating_ip_uuid": ""
        #                 }
        #             ]
        #         },
        #         "error": None,
        #         "reason": "",
        #         "message": ""
        #     }
        # }
        self.raise_for_ec(
            result,
            err_msg="Get VPC NIC snapshots of vnic_group_snapshot({}) failed, error is {}".format(
                vnic_group_snapshot_uuid, result.get("error")
            ),
        )

        return {nic["vnic_snapshot_uuid"]: nic for nic in result["data"]["data"]["vnic_snapshots"]}

    def list_vds(self):
        """
        get all vds in cluster, include vpc vds and non-vpc vds.
        :return a list:
        [
            {
                "uuid": "cbd4aa08-8910-41ce-aa9a-ffa2a7461f81",
                "bond_mode": null,
                "bond_name": "bond-m-al7e2utxr",
                "bond_type": "ovs_bond",
                "hosts_initialized": [
                    "2576aa3a-1345-11ee-868b-5254004ab59f",
                ],
                "name": "ng-vds",
                "ovsbr_name": "ovsbr-al7e2utxr",
                "qos_enable": true,
                "dpdk_enable": false,
                "rb_interval": "",
                "work_mode": "single",
                "hosts_associated": [
                    {
                        "host_uuid": "2576aa3a-1345-11ee-868b-5254004ab59f",
                        "data_ip": "************",
                        "nics_associated": [
                            "eth0"
                        ],
                        "bond_mode": "",
                        "rb_interval": "",
                        "bond_type": "ovs_bond",
                        "nics_associated_state": {},
                        "host": "node03",
                        "management_ip": "**************"
                    }
                ],
                "vlans_count": 5,
                "type": 1
            }
        ]
        """
        url = self.gen_url("network/vds")
        result = self._rest_client.get(url)

        self.raise_for_ec(result, err_msg="Failed to list vds: err={}".format(result.get("error")))

        return result["data"]

    def list_system_networks(self):
        """
        Returns: a list of system network
        [{
            "uuid": "d6495215-e560-4025-ab90-33757604c2b3",
            "attached_vds": "cbd4aa08-8910-41ce-aa9a-ffa2a7461f81",
            "mode": {
                "network_identities": [
                    0
                ],
                "type": "vlan_access"
            },
            "name": "vpc-sys-network",
            "network_config": {
                "route": [
                    {
                        "target_ip": "*******",
                        "subnet": "***********",
                        "gateway": "************"
                    }
                ],
                "service": {
                    "hosts_associated": [
                        {
                            "service_interface_name": "port-vpc",
                            "service_interface_ip": "**************",
                            "netmask": "*************",
                            "host_uuid": "2576aa3a-1345-11ee-868b-5254004ab59f"
                        },
                        {
                            "service_interface_name": "port-vpc",
                            "service_interface_ip": "**************",
                            "netmask": "*************",
                            "host_uuid": "49060e14-1345-11ee-bdb8-52540072bd04"
                        },
                        {
                            "service_interface_name": "port-vpc",
                            "service_interface_ip": "**************",
                            "netmask": "*************",
                            "host_uuid": "362ca4ce-1345-11ee-8357-5254004fdab0"
                        }
                    ]
                }
            },
            "network_type": "vpc_sys"
        }]
        """
        url = self.gen_url("network/system_network")
        result = self._rest_client.get(url)

        self.raise_for_ec(result, err_msg="Failed to list system network: err={}".format(result.get("error")))

        return result["data"]

    def get_firewall_items(self) -> dict:
        url = self.gen_url("network/network-firewall/all")
        result = self._rest_client.get(url)

        self.raise_for_ec(result, err_msg="Failed get acl items: err={}".format(result.get("error")))

        return result["data"]

    def add_firewall_item(self, item: dict):
        url = self.gen_url("network/network-firewall/item")
        result = self._rest_client.put(url, json=[item])

        self.raise_for_ec(result, err_msg="Failed put firewall items: err={}".format(result.get("error")))

    def remove_firewall_item(self, item: dict):
        url = self.gen_url("network/network-firewall/item")
        result = self._rest_client.delete(url, json=[item])

        self.raise_for_ec(result, err_msg="Failed delete firewall items: err={}".format(result.get("error")))
