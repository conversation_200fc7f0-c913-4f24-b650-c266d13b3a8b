# Copyright (c) 2013-2021, SMARTX
# All rights reserved.
import logging
from time import sleep

from gevent.pool import Pool

from common.http import restful_client as rtf_client


class Client:
    def __init__(self, restful_client=None, timeout=None):
        self._rest_client = restful_client or rtf_client.Client.from_config(timeout=timeout or 30)

    def raise_for_ec(self, response, err_msg=""):
        self._rest_client.raise_for_ec(response, err_msg)

    def gen_url(self, path, host="127.0.0.1"):
        return self._rest_client.gen_url(path, host=host)

    def get_pf(self, host_uuid, pf_id):
        url = self.gen_url("management/hosts/{}/nics/{}/get_sriov_info".format(host_uuid, pf_id))
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result, err_msg="Get SRIOV pf({}) info error. error message: {}".format(pf_id, result.get("error"))
        )
        logging.info("Get SRIOV pf({}) info {}".format(pf_id, result["data"]))

        return result["data"]["sriov"]

    def get_cluster_pfs(self):
        cluster_pfs = {}
        host_uuids = [host["host_uuid"] for host in self.get_management_hosts(fields=["host_uuid"])]
        for host_uuid in host_uuids:
            cluster_pfs[host_uuid] = self.get_host_pfs(host_uuid)
        return cluster_pfs

    def get_host_pfs(self, host_uuid):
        host_pfs = []
        nic_sriovs = self.get_nic_assign_infos(host_uuid)
        for items in nic_sriovs:
            if items.get("is_sriov_support"):
                host_pfs.append(items)
        return host_pfs

    def assign_vfs(self, host_uuid, pf_id, assign_ids):
        url = self.gen_url("management/hosts/{}/nics/{}/assign_vfs".format(host_uuid, pf_id))
        result = self._rest_client.post(url, json={"assign_ids": assign_ids})
        self.raise_for_ec(
            result,
            err_msg="Assign vfs with id {} from SRIOV pf({}) failed. error message: {}".format(
                assign_ids, pf_id, result.get("error")
            ),
        )
        logging.info("Assign vfs {} with id {} from SRIOV pf({}).".format(result["data"]["vfs"], assign_ids, pf_id))

        return result["data"]["vfs"]

    def release_vfs(self, host_uuid, pf_id, release_ids):
        url = self.gen_url("management/hosts/{}/nics/{}/release_vfs".format(host_uuid, pf_id))
        result = self._rest_client.post(url, json={"release_ids": release_ids})
        self.raise_for_ec(
            result,
            err_msg="Release vfs with id {} from SRIOV pf({}) failed. error message: {}".format(
                release_ids, pf_id, result.get("error")
            ),
        )
        logging.info("Release vfs {} from SRIOV pf({}).".format(release_ids, pf_id))

    def get_node_cpu_info(self):
        """
        :return: A dict of CPU info of each host.
        e.g.
        {
            "host_uuid_1": {
                "qemu_cpu_used_ids": "6-15",
                "node_cpu_list": {0: [1, 3, 5, 7], 1: [0, 2, 4, 6]},
                "node_distance": {0: {0: 0, 1: 10}, 1: {0: 10, 1: 0}},
                "chunk_numa_free_cpus": [4, 5, 16, 17]
                "chunk_numa_weak_cpus": [14, 15],
                "chunk_numa_node": 0,
                "chunk_socket": 0,
                "socket_cpu_list": {0: [1, 3, 5, 7], 1: [0, 2, 4, 6]},
                "thread_siblings": {
                    0: [0, 4], 1: [1, 5], 2: [2, 6], 3: [3, 7], 4: [0, 4], 5: [1, 5], 6: [2, 6], 7: [3, 7]
                },
                "chunk_dynamic_cpus": [6],
                "socket_to_numa": {0: {0,1}, 1: {7,8}},
            }
        }
        There may be other CPUs than chunk_numa_free_cpus and chunk_numa_weak_cpus on chunk NUMA node. But these CPUs
        are reserved for ZBS. VMs cannot use these CPUs.
        """
        url = self.gen_url("management/hosts/cpu_info")
        result = self._rest_client.get(url)
        self.raise_for_ec(result, err_msg="Get node CPU info failed, error message: {}".format(result.get("error")))

        cpu_info = {}
        for host in result["data"]:
            try:
                host_uuid = host["host_uuid"]
                cpu_info[host_uuid] = {"qemu_cpu_used_ids": host["cgroup"]["qemu_cpu_used_ids"]}

                numa_node_cpu_list = {}
                socket_cpu_list = {}
                thread_siblings = {}
                numa_to_socket = {}
                socket_to_numa: dict[int : set[int]] = {}

                for cpu in host["numa_topology"]["cpus"]:
                    numa_node_cpu_list.setdefault(cpu["numa_node"], []).extend(cpu["thread_siblings_list"])
                    socket_cpu_list.setdefault(cpu["socket_id"], []).extend(cpu["thread_siblings_list"])
                    numa_to_socket[cpu["numa_node"]] = cpu["socket_id"]
                    socket_to_numa.setdefault(cpu["socket_id"], set()).add(cpu["numa_node"])
                    for cpu_id in cpu["thread_siblings_list"]:
                        thread_siblings[cpu_id] = cpu["thread_siblings_list"]

                numa_node_distance = {}
                for d in host["numa_topology"]["node_distances"]:
                    target_distance = {}
                    for target in d["target_nodes"]:
                        target_distance[target["id"]] = target["distance"]
                    numa_node_distance[d["node_id"]] = target_distance

                cpu_info[host_uuid].update(
                    {
                        "node_cpu_list": numa_node_cpu_list,
                        "node_distance": numa_node_distance,
                        "socket_cpu_list": socket_cpu_list,
                        "thread_siblings": thread_siblings,
                        "socket_to_numa": socket_to_numa,
                    }
                )

                cpu_info[host_uuid].update(
                    {
                        "chunk_numa_free_cpus": host["cgroup"]["chunk_numa_free_cpus"],
                        "chunk_numa_weak_cpus": host["cgroup"]["chunk_numa_weak_cpus"],
                        "chunk_numa_node": host["cgroup"]["chunk_numa_node"],
                        "chunk_socket": numa_to_socket.get(host["cgroup"]["chunk_numa_node"], -1),
                        "chunk_dynamic_cpus": host["cgroup"]["chunk_dynamic_cpus"],
                    }
                )

            except KeyError as e:
                logging.error("Get node cpus failed, error={}, host_info={}".format(str(e), host))

        return cpu_info

    def get_cpu_topology(self):
        """
        :return: A dict of CPU info of each host.
        """
        url = self.gen_url("management/hosts/cpu_info")
        result = self._rest_client.get(url)
        self.raise_for_ec(result, err_msg="Get CPU topology failed, error message: {}".format(result.get("error")))

        cpu_topology = {}
        for host in result["data"]:
            host_uuid = host["host_uuid"]
            cpu_topology[host_uuid] = host["numa_topology"]["cpus"]

        return cpu_topology

    def get_host_labels_and_version(self, host_uuid, host="127.0.0.1"):
        url = self.gen_url("management/hosts/{}/labels".format(host_uuid), host=host)
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result, err_msg="Get labels from host({}) failed, error={}".format(host_uuid, result.get("error"))
        )
        # labels is in format {"key1":"value1", "key2": "value2"}
        labels = result["data"]["labels"]
        version = result["data"]["version"]
        return labels, version

    def set_host_labels(self, host_uuid, labels, version):
        data = {"labels": labels, "version": version}
        url = self.gen_url("management/hosts/{}/labels".format(host_uuid))
        result = self._rest_client.post(url, json=data)
        self.raise_for_ec(
            result,
            err_msg="Set labels for host({}) failed, version={}, error={}".format(
                host_uuid, version, result.get("error")
            ),
        )

    def get_host_services(self, host="127.0.0.1"):
        url = self.gen_url("services", host=host)
        result = self._rest_client.get(url)
        self.raise_for_ec(result, err_msg="Get node services failed, error message: {}".format(result.get("error")))
        return result["data"]

    def prepare_check_for_mt_mode(
        self, host_uuid=None, check_recover_multi_times=True, ignore_user_vm_pg_rule=False, system_vms=None
    ):
        system_vms = system_vms or []

        url = self.gen_url("management/hosts/{}/enter_mt_check".format(host_uuid))
        data = {
            "check_recover_multi_times": check_recover_multi_times,
            "ignore_user_vm_pg_rule": ignore_user_vm_pg_rule,
            "system_vms": system_vms,
        }
        result = self._rest_client.post(url, json=data)
        self.raise_for_ec(result, err_msg="Enter mt check failed, error message: {}".format(result.get("error")))
        return result["data"]["job_id"]

    def get_job(self, job_id):
        url = self.gen_url("jobs/{job_id}".format(job_id=job_id))
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result,
            err_msg="Get job({job_id}) failed. error message: {error}".format(job_id=job_id, error=result.get("error")),
        )
        logging.info("Get job({job_id}) successfully.".format(job_id=job_id))
        return result["data"]["job"]

    def get_running_jobs(self):
        url = self.gen_url("jobs")
        params = {"show_running": "true"}
        result = self._rest_client.get(url, params=params)
        self.raise_for_ec(
            result,
            err_msg="Get running jobs failed. error message: %s" % result.get("error"),
        )
        logging.info("Get running jobs successfully.")
        return result["data"]["jobs"]

    def wait_job_done(self, job_id, wait_timeout=1800):
        JOB_DONE = "done"
        JOB_FAILED = "failed"

        SLEEP_TIME = 5
        RETRY_TIMES = int(wait_timeout / SLEEP_TIME)

        for _ in range(RETRY_TIMES):
            job = self.get_job(job_id)
            if job["state"] == JOB_DONE:
                logging.info("Job({job_id}) is done.".format(job_id=job_id))
                return True
            elif job["state"] == JOB_FAILED:
                logging.error("Job({job_id}) is failed.".format(job_id=job_id))
                return False
            else:
                logging.info("Job({job_id}) is {state}. sleep 5 seconds.".format(job_id=job_id, state=job["state"]))
                sleep(5)

        return False

    def allocate_gpu(self, vm_uuid, host_uuid, gpu_id):
        url = self.gen_url("management/hosts/{}/gpus/{}/assign".format(host_uuid, gpu_id))
        body = {"assign_id": vm_uuid}
        result = self._rest_client.post(url, json=body)
        self.raise_for_ec(
            result,
            err_msg="Allocate GPU({}) for VM({}) on host({}) failed. Error message: {}".format(
                gpu_id, vm_uuid, host_uuid, result.get("error")
            ),
        )
        logging.info("Allocate GPU({}) for VM({}) on host({}) succeed".format(gpu_id, vm_uuid, host_uuid))

    def release_gpu(self, vm_uuid, host_uuid, gpu_id):
        url = self.gen_url("management/hosts/{}/gpus/{}/release".format(host_uuid, gpu_id))
        body = {"assign_id": vm_uuid}
        result = self._rest_client.post(url, json=body)
        self.raise_for_ec(
            result,
            err_msg="Release GPU({}) for VM({}) on host({}) failed. Error message: {}".format(
                gpu_id, vm_uuid, host_uuid, result.get("error")
            ),
        )
        logging.info("Release GPU({}) for VM({}) on host({}) succeed".format(gpu_id, vm_uuid, host_uuid))

    def get_gpu_info(self, host_uuid, gpu_id):
        url = self.gen_url("management/hosts/{}/gpus/{}".format(host_uuid, gpu_id))
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result,
            err_msg="Get GPU({}) info on host({}) failed. Error message: {}".format(
                gpu_id, host_uuid, result.get("error")
            ),
        )
        return result["data"]

    def get_gpu_infos(self, host_uuid):
        url = self.gen_url("management/hosts/{}/gpus".format(host_uuid))
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result,
            err_msg="Get GPU info list on host({}) failed. Error message: {}".format(host_uuid, result.get("error")),
        )
        return result["data"]["gpus"]

    def allocate_nic(self, vm_uuid, host_uuid, nic_id):
        url = self.gen_url("management/hosts/{}/nics/{}/assign".format(host_uuid, nic_id))
        body = {"assign_id": vm_uuid}
        result = self._rest_client.post(url, json=body)
        self.raise_for_ec(
            result,
            err_msg="Allocate NIC({}) for VM({}) on host({}) failed. Error message: {}".format(
                nic_id, vm_uuid, host_uuid, result.get("error")
            ),
        )
        logging.info("Allocate NIC({}) for VM({}) on host({}) succeed".format(nic_id, vm_uuid, host_uuid))

    def release_nic(self, vm_uuid, host_uuid, nic_id):
        url = self.gen_url("management/hosts/{}/nics/{}/release".format(host_uuid, nic_id))
        body = {"assign_id": vm_uuid}
        result = self._rest_client.post(url, json=body)
        self.raise_for_ec(
            result,
            err_msg="Release NIC({}) for VM({}) on host({}) failed. Error message: {}".format(
                nic_id, vm_uuid, host_uuid, result.get("error")
            ),
        )
        logging.info("Release NIC({}) for VM({}) on host({}) succeed".format(nic_id, vm_uuid, host_uuid))

    def get_nic_info(self, host_uuid, nic_id, host="127.0.0.1"):
        url = self.gen_url("management/hosts/{}/nics/{}".format(host_uuid, nic_id), host=host)
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result,
            err_msg="Get NIC({}) info on host({}) failed. Error message: {}".format(
                nic_id, host_uuid, result.get("error")
            ),
        )
        return result["data"]

    def get_nic_infos(self, host_uuid, host="127.0.0.1"):
        """
        Get the NIC information of the current node according to the host_uuid

        return: list: The data structure of TUNA return value is {data: []}
        """
        url = self.gen_url("management/hosts/{}/nics".format(host_uuid), host=host)
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result,
            err_msg="Get NIC info list on host({}) failed. Error message: {}".format(host_uuid, result.get("error")),
        )
        return result["data"]

    def allocate_pci(self, vm_uuid, host_uuid, pci_id):
        url = self.gen_url("management/hosts/{}/pcis/{}/assign".format(host_uuid, pci_id))
        body = {"assign_id": vm_uuid}
        result = self._rest_client.post(url, json=body)
        self.raise_for_ec(
            result,
            err_msg="Allocate PCI device({}) for VM({}) on host({}) failed. Error message: {}".format(
                pci_id, vm_uuid, host_uuid, result.get("error")
            ),
        )
        logging.info("Allocate PCI device({}) for VM({}) on host({}) succeed".format(pci_id, vm_uuid, host_uuid))

    def release_pci(self, vm_uuid, host_uuid, pci_id):
        url = self.gen_url("management/hosts/{}/pcis/{}/release".format(host_uuid, pci_id))
        body = {"assign_id": vm_uuid}
        result = self._rest_client.post(url, json=body)
        self.raise_for_ec(
            result,
            err_msg="Release PCI device({}) for VM({}) on host({}) failed. Error message: {}".format(
                pci_id, vm_uuid, host_uuid, result.get("error")
            ),
        )
        logging.info("Release PCI device({}) for VM({}) on host({}) succeed".format(pci_id, vm_uuid, host_uuid))

    def allocate_sriov_pcis(self, host_uuid, pci_uuid, assign_ids):
        url = self.gen_url("management/hosts/{}/pcis/{}/assign_vfs".format(host_uuid, pci_uuid))
        result = self._rest_client.post(url, json={"assign_ids": assign_ids})
        self.raise_for_ec(
            result,
            err_msg="Assign vfs with id {} from PCI({}) failed. error message: {}".format(
                assign_ids, pci_uuid, result.get("error")
            ),
        )
        logging.info("Assign vfs {} with id {} from PCI({}).".format(result["data"]["vfs"], assign_ids, pci_uuid))

        return result["data"]["vfs"]

    def release_sriov_pcis(self, host_uuid, pci_uuid, release_ids):
        url = self.gen_url("management/hosts/{}/pcis/{}/release_vfs".format(host_uuid, pci_uuid))
        result = self._rest_client.post(url, json={"assign_ids": release_ids})
        self.raise_for_ec(
            result,
            err_msg="Release vfs with id {} from PCI({}) failed. error message: {}".format(
                release_ids, pci_uuid, result.get("error")
            ),
        )
        logging.info("Release vfs {} from PCI({}).".format(release_ids, pci_uuid))

    def get_pci_info(self, host_uuid, pci_id):
        url = self.gen_url("management/hosts/{}/pcis/{}".format(host_uuid, pci_id))
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result,
            err_msg="Get PCI device({}) info on host({}) failed. Error message: {}".format(
                pci_id, host_uuid, result.get("error")
            ),
        )
        return result["data"]

    def get_pci_infos(self, host_uuid, host="127.0.0.1"):
        """
        Get the normal PCI device information of the current node according to the host_uuid

        return: list: The data structure of TUNA return value is {data: []}
        """
        url = self.gen_url("management/hosts/{}/pcis".format(host_uuid), host=host)
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result,
            err_msg="Get PCI info list on host({}) failed. Error message: {}".format(host_uuid, result.get("error")),
        )
        return result["data"]["pcis"]

    def get_usbs(self, host_uuid):
        url = self.gen_url("management/hosts/{}/usbs".format(host_uuid))
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result, err_msg="Get host({}) usbs failed, error message: {}".format(host_uuid, result.get("error"))
        )
        return result["data"]["usbs"]

    def get_usb(self, host_uuid, usb_id):
        url = self.gen_url("management/hosts/{}/usbs/{}".format(host_uuid, usb_id))
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result,
            err_msg="Get host({}) usb({}) failed, error message: {}".format(host_uuid, usb_id, result.get("error")),
        )
        return result["data"]

    def get_usb_assign_infos(self, host_uuid):
        url = self.gen_url("management/hosts/{}/usb_assign_infos".format(host_uuid))
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result,
            err_msg="Get host({}) usb assign infos failed, error message: {}".format(host_uuid, result.get("error")),
        )
        return result["data"]["usb_assign_infos"]

    def get_usb_assign_info(self, host_uuid, usb_id):
        url = self.gen_url("management/hosts/{}/usb_assign_infos/{}".format(host_uuid, usb_id))
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result,
            err_msg="Get host({}) usb({}) assign info failed, error message: {}".format(
                host_uuid, usb_id, result.get("error")
            ),
        )
        return result["data"]

    def allocate_over_network_usb(self, host_uuid, usb_id, assign_id):
        url = self.gen_url("management/hosts/{}/usbs/{}/assign_over_network".format(host_uuid, usb_id))
        result = self._rest_client.post(url, json={"assign_id": assign_id})
        self.raise_for_ec(
            result,
            err_msg="Allocate host({}) over-network usb({}) with assign_id {} failed, error message: {}".format(
                host_uuid, usb_id, assign_id, result.get("error")
            ),
        )

        logging.info("Allocate host({}) over-network usb({}) with assign_id {}.".format(host_uuid, usb_id, assign_id))
        return result["data"]

    def release_over_network_usb(self, host_uuid, usb_id, assign_id):
        url = self.gen_url("management/hosts/{}/usbs/{}/release_over_network".format(host_uuid, usb_id))
        result = self._rest_client.post(url, json={"assign_id": assign_id})
        self.raise_for_ec(
            result,
            err_msg="Release host({}) over-network usb({}) with assign_id {} failed, error message: {}".format(
                host_uuid, usb_id, assign_id, result.get("error")
            ),
        )

        logging.info("Release host({}) over-network usb({}) with assign_id {}.".format(host_uuid, usb_id, assign_id))
        return result["data"]

    def allocate_pass_through_usb(self, host_uuid, usb_id, assign_id):
        url = self.gen_url("management/hosts/{}/usbs/{}/assign".format(host_uuid, usb_id))
        result = self._rest_client.post(url, json={"assign_id": assign_id})
        self.raise_for_ec(
            result,
            err_msg="Allocate host({}) pass-through usb({}) with assign_id {} failed, error message: {}".format(
                host_uuid, usb_id, assign_id, result.get("error")
            ),
        )

        logging.info("Allocate host({}) pass-through usb({}) with assign_id {}.".format(host_uuid, usb_id, assign_id))
        return result["data"]

    def release_pass_through_usb(self, host_uuid, usb_id, assign_id):
        url = self.gen_url("management/hosts/{}/usbs/{}/release".format(host_uuid, usb_id))
        result = self._rest_client.post(url, json={"assign_id": assign_id})
        self.raise_for_ec(
            result,
            err_msg="Release host({}) pass-through usb({}) with assign_id {} failed, error message: {}".format(
                host_uuid, usb_id, assign_id, result.get("error")
            ),
        )

        logging.info("Release host({}) pass-through usb({}) with assign_id {}.".format(host_uuid, usb_id, assign_id))
        return result["data"]

    def get_gpu_assign_infos(self, host_uuid, host="127.0.0.1"):
        url = self.gen_url("management/hosts/{}/gpu_assign_infos".format(host_uuid), host=host)
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result,
            err_msg="Get GPU assign infos on host({}) failed, error message: {}".format(host_uuid, result.get("error")),
        )

        return result["data"]["gpu_assign_infos"]

    def get_nic_assign_infos(self, host_uuid, host="127.0.0.1"):
        url = self.gen_url("management/hosts/{}/nic_assign_infos".format(host_uuid), host=host)
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result,
            err_msg="Get NIC assign infos on host({}) failed, error message: {}".format(host_uuid, result.get("error")),
        )

        return result["data"]["nic_assign_infos"]

    def get_pci_assign_infos(self, host_uuid, host="127.0.0.1"):
        url = self.gen_url("management/hosts/{}/pci_assign_infos".format(host_uuid), host=host)
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result,
            err_msg="Get PCI device assign infos on host({}) failed, error message: {}".format(
                host_uuid, result.get("error")
            ),
        )

        return result["data"]["pci_assign_infos"]

    def get_pci_assign_info_with_pci_id(self, host_uuid, pci_id):
        url = self.gen_url("management/hosts/{}/pci_assign_infos/{}".format(host_uuid, pci_id))
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result,
            err_msg="Get PCI device assign info on host({}) PCI({}) failed, error message: {}".format(
                host_uuid, pci_id, result.get("error")
            ),
        )

        return result["data"]

    def get_cluster_software(self):
        base_url = "cluster/software"
        url = self.gen_url(base_url)
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result,
            err_msg="[get_cluster_software] Get cluster software failed. error message: {error}".format(
                error=result.get("error")
            ),
        )
        return result["data"]

    def get_cluster_compute_summary(self):
        base_url = "cluster/compute_summary"
        url = self.gen_url(base_url)
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result,
            err_msg="[get_cluster_compute_summary] Get cluster compute summary failed. error message: {error}".format(
                error=result.get("error")
            ),
        )
        return result["data"]

    def get_management_hosts(self, host_uuid=None, fields=None):
        """
        Args:
            host_uuid (str): Nodes that need to be displayed only uuid,
                             if not filled, the whole cluster is displayed.
                             with host_uuid, URL will like: `management/hosts?host_uuid=123`
            fields  ([]str): Array with the fields to be displayed, e.g. ["data_ip", "zone_name"]
                             with fields, URL will like: `management/hosts?fields=data_ip,zone_name`
        """
        base_url = "management/hosts"
        query_param = None
        if host_uuid or fields:
            query_param = {}
            if host_uuid:
                query_param["host_uuid"] = host_uuid
            if fields:
                query_param["fields"] = ",".join(fields)

        url = self.gen_url(base_url)
        result = self._rest_client.get(url, params=query_param)
        self.raise_for_ec(
            result,
            err_msg="[get_management_hosts] Get management hosts failed. error message: {error}".format(
                error=result.get("error")
            ),
        )
        return result["data"]

    def get_management_host(self, host_uuid, fields=None):
        result = self.get_management_hosts(host_uuid, fields)
        if result:
            return result[0]

    def get_management_hosts_summary(self):
        base_url = "management/hosts/summary"
        url = self.gen_url(base_url)
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result,
            err_msg="[get_management_hosts_summary] Get management hosts summary failed. error message: {error}".format(
                error=result.get("error")
            ),
        )
        return result["data"]

    def assign_vgpu(self, host_uuid, gpu_uuid, vgpu_type_id, assign_ids):
        url = self.gen_url("management/hosts/{}/gpus/{}/assign_vgpu".format(host_uuid, gpu_uuid))
        result = self._rest_client.post(url, json={"assign_ids": assign_ids, "vgpu_type_id": vgpu_type_id})
        self.raise_for_ec(
            result,
            err_msg="Assign VGPU of GPU({}) on host({}) failed, vgpu_type_id={}, assign_ids={}, "
            "error message: {}".format(gpu_uuid, host_uuid, vgpu_type_id, assign_ids, result.get("error")),
        )

        return {assign_result["assign_id"]: assign_result["mdev_uuid"] for assign_result in result["data"]["vgpus"]}

    def release_vgpu(self, host_uuid, gpu_uuid, vgpu_type_id, assign_ids):
        url = self.gen_url("management/hosts/{}/gpus/{}/release_vgpu".format(host_uuid, gpu_uuid))
        result = self._rest_client.post(url, json={"assign_ids": assign_ids, "vgpu_type_id": vgpu_type_id})
        self.raise_for_ec(
            result,
            err_msg="Release VGPU of GPU({}) on host({}) failed, vgpu_type={}, assign_ids={}, "
            "error message: {}".format(gpu_uuid, host_uuid, vgpu_type_id, assign_ids, result.get("error")),
        )

    def get_settings_cluster(self):
        base_url = "settings/cluster"
        url = self.gen_url(base_url)
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result,
            err_msg="[get_settings_cluster] Get cluster settings failed. error message: {error}".format(
                error=result.get("error")
            ),
        )
        return result["data"]

    def get_management_host_state(self, host_uuid):
        """
        Get the state of the host.
        :param host_uuid: host_uuid of host
        :return data: host_uuid of host and host_state.
        e.g.
            {
                "host_uuid": "<host_uuid>",
                "host_state": {
                    "uuid": "<host_uuid>",
                    "state": "in_use",
                    "maintenance_job_id": ""
                }
            }
        The state field currently contains: in_use, entering_maintenance_mode, maintenance_mode, removing.
        """
        base_url = "management/hosts?host_uuid={}&show_host_states=true&fields=host_uuid,host_state".format(host_uuid)
        url = self.gen_url(base_url)
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result,
            err_msg="[get_management_hosts_state] Get management hosts state failed. error message: {error}".format(
                error=result.get("error")
            ),
        )
        return result["data"]

    def _get_node_software_features(self, node_ip):
        base_url = "cluster/software"
        url = self.gen_url(base_url, host=node_ip)
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result,
            err_msg="[get_node_software_features] Get node software features failed. error message: {error}".format(
                error=result.get("error")
            ),
        )
        return node_ip, result["data"]

    def get_all_nodes_software_features(self, host_list):
        pool = Pool(10)
        results = {res[0]: res[1] for res in pool.imap(self._get_node_software_features, host_list)}
        return results

    def get_nic_states(self, host_uuid):
        """
        List the NIC States of the current node according to the host_uuid

        return: list: The data structure of TUNA return value is {data: []}
        """
        url = self.gen_url("management/hosts/{}/nic_states".format(host_uuid))
        result = self._rest_client.get(url)
        self.raise_for_ec(
            result,
            err_msg="List NIC states on host({}) failed. Error message: {}".format(host_uuid, result.get("error")),
        )
        return result["data"]["nic_states"]

    def assign_mdev(self, host_uuid, pdev_uuid, mdev_type_id, assign_ids) -> dict:
        url = self.gen_url("management/hosts/{}/pcis/{}/assign_mdev".format(host_uuid, pdev_uuid))
        result = self._rest_client.post(url, json={"assign_ids": assign_ids, "mdev_type_id": mdev_type_id})
        self.raise_for_ec(
            result,
            err_msg="Assign mdev failed(mdev_type_id={}, pdev_uuid={}, assign_ids={}). error message: {}".format(
                mdev_type_id, pdev_uuid, assign_ids, result.get("error")
            ),
        )
        logging.info("Assigned mdevs: {} on device {}.".format(result["data"]["mdevs"], pdev_uuid))

        return {assign_result["assign_id"]: assign_result["mdev_uuid"] for assign_result in result["data"]["mdevs"]}

    def release_mdev(self, host_uuid, pdev_uuid, mdev_type_id, assign_ids):
        url = self.gen_url("management/hosts/{}/pcis/{}/release_mdev".format(host_uuid, pdev_uuid))
        result = self._rest_client.post(url, json={"assign_ids": assign_ids, "mdev_type_id": mdev_type_id})
        self.raise_for_ec(
            result,
            err_msg="Release mdev failed(mdev_type_id={}, pdev_uuid={}, assign_ids={}). error message: {}".format(
                mdev_type_id, pdev_uuid, assign_ids, result.get("error")
            ),
        )
        logging.info("Released mdevs: {} on device {}.".format(assign_ids, pdev_uuid))

        return result["data"]
