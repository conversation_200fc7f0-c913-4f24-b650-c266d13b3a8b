# Copyright (c) 2013-2024, elfvirt
# All rights reserved.


import collections
import logging


class Client:
    """
    The recoverable client is a wrapper for some service client, which can recover from
    a single endpoint failure. If a service has no frontend proxy as the ingress
    and the service client access one endpoint only, that will cause the single point of
    failure. The recoverable client provides a way to access multiple endpoints for this
    type of service client.

    The recover mechanism:
    1. Serial: according to the endpoint access sort, the recoverable client will access the
        available service endpoints in order, until the request is successful.
    2. Concurrent(TODO): Request to all available service endpoints concurrently, and return the first
        successful response.

    The sort of endpoint access:
    1. Default: The recoverable client will access the available service endpoints by the
       original order.
    2. Random(TODO): The recoverable client will access the available service endpoints by the
         random order for each request.
    3. Latency(TODO): The recoverable client probes the latency of each available service endpoint
        in a background thread. And it will provide the endpoints order by the latency.


    If you want to specify the endpoint for a request, you can pass the specified_endpoint
    parameter to the request method.
    Usage: recoverable_client.get_nic_infos(args, host="xxx")
    """

    ENDPOINT_ACCESS_SORT_DEFAULT = "default"

    def __init__(
        self,
        service_client,
        available_service_endpoints=("127.0.0.1",),
        endpoint_access_sort=ENDPOINT_ACCESS_SORT_DEFAULT,
    ):
        # Remove the duplicate endpoint and keep the order
        self._available_service_endpoints = tuple(collections.OrderedDict.fromkeys(available_service_endpoints).keys())
        self._service_client = service_client
        self._endpoint_access_sort = endpoint_access_sort

    @property
    def available_service_endpoints(self):
        return self._available_service_endpoints

    def __getattr__(self, key):
        attr = getattr(self._service_client, key)

        if not callable(attr):
            return attr

        def wrapper(*args, **kwargs):
            if kwargs.get("host"):
                available_service_endpoints = (kwargs.pop("host"),)
            else:
                available_service_endpoints = self.available_service_endpoints

            for endpoint in available_service_endpoints:
                try:
                    kwargs["host"] = endpoint
                    return attr(*args, **kwargs)
                except Exception as e:
                    logging.error("Request to endpoint({}) failed: err={}".format(endpoint, str(e)))
                    if endpoint == available_service_endpoints[-1]:
                        raise

        return wrapper
