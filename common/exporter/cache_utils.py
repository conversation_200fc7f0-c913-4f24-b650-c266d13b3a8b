# Copyright (c) 2013-2017, SMARTX
# All rights reserved.
import datetime
import logging
import random
import threading
import time

import gevent

logger = logging.getLogger(__file__)


class CachedCollectorWrapper:
    def __init__(self, origin_collector, interval=30, target=None):
        self._id = self._job_id(origin_collector)
        self._interval = interval
        self._collector = origin_collector
        self._expired = self._interval * 3
        self._target = target
        logger.info("add cache job<{}>".format(self._id))
        self._cache = (time.time() - interval, None)
        t = threading.Thread(target=self._run)
        t.daemon = True
        t.start()
        self._worker = t

    @classmethod
    def _job_id(cls, collector):
        return ".".join([collector.__module__, collector.__name__])

    def _run(self):
        random_delay = random.uniform(1, 25)
        logger.info("{} will random delay: {}".format(self._id, random_delay))
        time.sleep(random_delay)

        logger.info("{} started at {}".format(self._id, threading.current_thread().ident))
        while True:
            ts, _ = self._cache
            start = time.time()
            accumulate = max(int(start - ts), 0)
            if accumulate >= self._interval:
                try:
                    logger.info("job<{}> start at {}".format(self._id, datetime.datetime.fromtimestamp(start)))
                    if self._target:
                        data = self._collector(target=self._target).collect()
                    else:
                        data = self._collector().collect()
                    finish = time.time()
                    cost = finish - start
                    self._expired = cost + self._interval * 3
                    run_after = max(self._interval - cost, 5)
                    self._update_cache(data, finish + run_after - self._interval)
                    logger.info(
                        "job<{}> synced with data len:{}, interval: {}, start at: {}, cost: {}, "
                        "next loop after {}.".format(
                            self._id, len(data), self._interval, datetime.datetime.fromtimestamp(start), cost, run_after
                        )
                    )
                except (gevent.Timeout, Exception):
                    logger.exception(
                        "error collecting data: {}, job will restart after {}".format(self._id, self._interval / 2)
                    )
                    time.sleep(self._interval / 2)
            else:
                logger.info("job<{}> sleep {}".format(self._id, self._interval - accumulate))
                time.sleep(self._interval - accumulate)

    def collect(self):
        return self._get_efficient_cache()

    def _get_efficient_cache(self):
        ts, data = self._cache
        now = int(time.time())
        if data is None:
            logger.warning("no data cached on cache<{}>".format(self._id))
            return []
        if self._expired > now - ts:
            return data
        logger.warning("cache<{}> data is out of date, return none instead".format(self._id))
        return []

    def _update_cache(self, data, ts):
        self._cache = (ts, data)
