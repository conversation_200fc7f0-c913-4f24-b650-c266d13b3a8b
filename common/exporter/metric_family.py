# Copyright (c) 2013-2023, SMARTX
# All rights reserved.

import logging
import threading

from inotify_simple import INotify, flags
from prometheus_client.core import CounterMetricFamily, GaugeMetricFamily, HistogramMetricFamily

from common.lib.cfg import DeployConfigManager


class InotifyHostInfo:
    def __init__(self):
        self._manager = DeployConfigManager()
        self.node_uuid = self._manager.get_node_uuid()
        self.node_role = self._manager.get_node_role()
        self.hostname = self._manager.get_hostname()
        self.cluster_uuid = self._manager.get_cluster_uuid()

        self._inotify = INotify()
        self._worker = None
        self._start()

    def _start(self):
        t = threading.Thread(target=self._run)
        t.daemon = True
        t.start()

        self._worker = t

    def _refresh_hostname(self):
        try:
            new_hostname = self._manager.get_hostname()
            logging.info("hostname changed: {} --> {}".format(self.hostname, new_hostname))
            self.hostname = new_hostname
        except Exception as e:
            logging.exception(e)

    def _run(self):
        # `man inotify`: IN_DELETE_SELF and IN_IGNORED event
        watch_flags = flags.DELETE_SELF | flags.IGNORED
        self._inotify.add_watch("/etc/hostname", watch_flags)

        while True:
            # block until first event coming and wait another 3s for following events
            for event in self._inotify.read(read_delay=3000):
                event_flags = [str(f) for f in flags.from_mask(event.mask)]
                logging.info("inotify: {} {}".format(event, "|".join(event_flags)))
                if event.mask & flags.DELETE_SELF:
                    self._refresh_hostname()
                if event.mask & flags.IGNORED:
                    logging.info("re-watch new /etc/hostname")
                    self._inotify.add_watch("/etc/hostname", watch_flags)


hostify = InotifyHostInfo()

TARGET_CLUSTER = "cluster"
TARGET_HOST = "host"
TARGET_ELF_HOST = "elf_host"
TARGET_NODE = "node"
TARGET_SCVM = "scvm"
TARGET_WITNESS = "witness"


def get_metric_class(target):
    metric_class = HostGaugeMetricFamily
    if target is None:
        return metric_class
    if target == TARGET_SCVM:
        metric_class = ScvmGaugeMetricFamily
    elif target == TARGET_HOST:
        metric_class = HostGaugeMetricFamily
    elif target == TARGET_CLUSTER:
        metric_class = ClusterGaugeMetricFamily
    elif target == TARGET_WITNESS:
        metric_class = WitnessGaugeMetricFamily
    return metric_class


def get_counter_class(target):
    metric_class = HostCounterMetricFamily
    if target is None:
        return metric_class
    if target == TARGET_SCVM:
        metric_class = ScvmCounterMetricFamily
    elif target == TARGET_HOST:
        metric_class = HostCounterMetricFamily
    return metric_class


def get_histogram_class(target):
    metric_class = HostHistogramMetricFamily
    if target is None:
        return metric_class
    if target == TARGET_SCVM:
        metric_class = ScvmHistogramMetricFamily
    elif target == TARGET_HOST:
        metric_class = HostHistogramMetricFamily
    return metric_class


class ScvmGaugeMetricFamily(GaugeMetricFamily):
    def __init__(self, name, desc, labels=[]):
        GaugeMetricFamily.__init__(self, name, desc, labels=["_scvm", "role", "hostname", *labels])

    def add_metric(self, label_values, v):
        label_v = [hostify.node_uuid, hostify.node_role, hostify.hostname, *label_values]
        GaugeMetricFamily.add_metric(self, label_v, v)


class ScvmHistogramMetricFamily(HistogramMetricFamily):
    def __init__(self, name, desc, labels=[]):
        HistogramMetricFamily.__init__(self, name, desc, labels=["_scvm", "role", "hostname", *labels])

    def add_metric(self, label_values, buckets=None, sum_value=None):
        label_v = [hostify.node_uuid, hostify.node_role, hostify.hostname, *label_values]
        HistogramMetricFamily.add_metric(self, label_v, buckets=buckets, sum_value=sum_value)


class ScvmCounterMetricFamily(CounterMetricFamily):
    def __init__(self, name, desc, labels=[]):
        CounterMetricFamily.__init__(self, name, desc, labels=["_scvm", "role", "hostname", *labels])

    def add_metric(self, labels, value):
        label_v = [hostify.node_uuid, hostify.node_role, hostify.hostname, *labels]
        CounterMetricFamily.add_metric(self, label_v, value)


class HostGaugeMetricFamily(GaugeMetricFamily):
    def __init__(self, name, desc, labels=[]):
        GaugeMetricFamily.__init__(self, name, desc, labels=["_host", "role", "hostname", *labels])

    def add_metric(self, label_values, v):
        label_v = [hostify.node_uuid, hostify.node_role, hostify.hostname, *label_values]
        GaugeMetricFamily.add_metric(self, label_v, v)


class HostHistogramMetricFamily(HistogramMetricFamily):
    def __init__(self, name, desc, labels=[]):
        HistogramMetricFamily.__init__(self, name, desc, labels=["_host", "role", "hostname", *labels])

    def add_metric(self, label_values, buckets=None, sum_value=None):
        label_v = [hostify.node_uuid, hostify.node_role, hostify.hostname, *label_values]
        HistogramMetricFamily.add_metric(self, label_v, buckets=buckets, sum_value=sum_value)


class HostCounterMetricFamily(CounterMetricFamily):
    def __init__(self, name, desc, labels=[]):
        CounterMetricFamily.__init__(self, name, desc, labels=["_host", "role", "hostname", *labels])

    def add_metric(self, labels, value):
        label_v = [hostify.node_uuid, hostify.node_role, hostify.hostname, *labels]
        CounterMetricFamily.add_metric(self, label_v, value)


class ClusterGaugeMetricFamily(GaugeMetricFamily):
    def __init__(self, name, desc, labels=[]):
        GaugeMetricFamily.__init__(self, name, desc, labels=["_cluster", *labels])

    def add_metric(self, label_values, v):
        GaugeMetricFamily.add_metric(self, [hostify.cluster_uuid, *label_values], v)


class WitnessGaugeMetricFamily(GaugeMetricFamily):
    def __init__(self, name, desc, labels=[]):
        GaugeMetricFamily.__init__(self, name, desc, labels=["_witness", "role", "hostname", *labels])

    def add_metric(self, label_values, v):
        label_v = [hostify.node_uuid, hostify.node_role, hostify.hostname, *label_values]
        GaugeMetricFamily.add_metric(self, label_v, v)
