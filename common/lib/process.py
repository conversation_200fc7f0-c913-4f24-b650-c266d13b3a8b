# Copyright (c) 2014, SMARTX
# All rights reserved.

# port from openstack


import os
import shlex
import signal
import subprocess
from time import sleep


def _subprocess_setup():
    # Python installs a SIGPIPE handler by default. This is usually not what
    # non-Python subprocesses expect.
    signal.signal(signal.SIGPIPE, signal.SIG_DFL)


def subprocess_popen(args, stdin=None, stdout=None, stderr=None, shell=False, env=None, text=True):
    return subprocess.Popen(
        args,
        shell=shell,
        stdin=stdin,
        stdout=stdout,
        stderr=stderr,
        preexec_fn=_subprocess_setup,  # noqa: PLW1509
        close_fds=True,
        env=env,
        text=text,
    )


def create_process(cmd, root_helper=None, addl_env=None):
    """Create a process object for the given command.

    The return value will be a tuple of the process object and the
    list of command arguments used to create it.

    @root_helper: the command to become root, e.g. "sudo"
    """
    if root_helper:
        cmd = shlex.split(root_helper) + cmd
    cmd = list(map(str, cmd))

    env = os.environ.copy()
    if addl_env:
        env.update(addl_env)

    obj = subprocess_popen(
        cmd, shell=False, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE, env=env
    )

    return obj, cmd


def execute(cmd, root_helper=None, process_input=None, addl_env=None, check_exit_code=True, return_stderr=False):
    try:
        obj, cmd = create_process(cmd, root_helper=root_helper, addl_env=addl_env)
        _stdout, _stderr = process_input and obj.communicate(process_input) or obj.communicate()
        obj.stdin.close()
        m = "\nCommand: {cmd}\nExit code: {code}\nStdout: {stdout!r}\nStderr: {stderr!r}".format(
            cmd=cmd,
            code=obj.returncode,
            stdout=_stdout,
            stderr=_stderr,
        )
        if obj.returncode and check_exit_code:
            raise RuntimeError(m + "\n" + _stdout + "\n" + _stderr)
    finally:
        # NOTE(termie): this appears to be necessary to let the subprocess
        # call clean something up in between calls, without
        # it two execute calls in a row hangs the second one
        sleep(0)

    return return_stderr and (_stdout, _stderr) or _stdout


def find_child_pids(pid):
    """Retrieve a list of the pids of child processes of the given pid."""

    try:
        raw_pids = execute(["ps", "--ppid", pid, "-o", "pid="])
    except RuntimeError as e:  # noqa: F841
        return []
    return [x.strip() for x in raw_pids.split("\n") if x.strip()]
