# Copyright (c) 2013-2020, SMARTX
# All rights reserved.
import logging
import re
from subprocess import PIPE, Popen

from common.lib.utils.cmd_tools import execute
from common.lib.utils.pysmart import Device, DeviceList
from common.lib.utils.pysmart.utils import OS, rescan_device_busses


class ZDevice(Device):
    def __init__(self, name, interface=None):
        super().__init__(name, interface)

    def run_selftest(self, test_type, force_start=False):
        if force_start:
            Popen("smartctl -X /dev/%s" % self.name, shell=True, text=True).communicate()
        super().run_selftest(test_type)

    def is_healthy(self):
        if self.interface is None:
            err_msg = "{}: interface is None, skip check healthy".format(self.name)
            logging.warning(err_msg)
            return True

        if self.tests:
            logging.info("{}: S.M.A.R.T. test has been run.".format(self.name))
        if self.assessment is None:
            return True
        return self.assessment in ["PASS", "WARN"]


class ZDeviceList(DeviceList):
    def _initialize(self):
        """
        Scans system busses for attached devices and add them to the
        `DeviceList` as `Device` objects.
        """
        # On Windows machines we should re-initialize the system busses
        # before scanning for disks
        if OS == "Windows":
            rescan_device_busses()
        cmd = Popen("smartctl --scan-open", shell=True, stdout=PIPE, stderr=PIPE, text=True)
        _stdout, _stderr = cmd.communicate()
        for line in _stdout.split("\n"):
            if not ("failed:" in line or line == ""):
                name = line.split(" ")[0].replace("/dev/", "")
                # CSMI devices are explicitly of the 'csmi' type and do not
                # require further disambiguation
                if name[0:4] == "csmi":
                    self.devices.append(ZDevice(name, interface="csmi"))
                # Other device types will be disambiguated by Device.__init__
                else:
                    self.devices.append(ZDevice(name))
        # Remove duplicates and unwanted devices (optical, etc.) from the list
        self._cleanup()
        # Sort the list alphabetically by device name
        self.devices.sort(key=lambda device: device.name)


def run_short_tests(test_type="short", force=True):
    """
    Test type should be a string in ("short", "long", "conveyance")
    :param test_type:
    :return:
    """
    for device in ZDeviceList().devices:
        device.run_selftest(test_type, force)


def get_results():
    """
    Return if the disk is health
    :return: a dict, the key is the device name, value is a bool value
    """
    result = {}
    for device in ZDeviceList().devices:
        result[device.name] = device.is_healthy()
    return result


def get_disk_temperature(dev_path):
    rtcode, stdout, stderr = execute("hddtemp", dev_path)

    if rtcode == 0:
        if not stderr:
            # \xb0C is "°C"
            result = re.findall(r"(\d+)°C", stdout)
            if result:
                return int(result[0])
    return None
