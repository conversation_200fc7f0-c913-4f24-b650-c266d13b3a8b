# Copyright (c) 2013-2022, SMARTX
# All rights reserved.

import datetime
import errno
import os
import socket
import struct


class Singleton(type):
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super().__call__(*args, **kwargs)
        return cls._instances[cls]


def ensure_directory(d):
    try:
        os.makedirs(d)
    except OSError as e:
        if e.errno != errno.EEXIST:
            raise


def ensure_mode(path, mode):
    os.chmod(path, mode)


def int32_to_ip(int_ip):
    """Input a int32 ip number return a ip string
    @param int_ip: int32 ip number
    @return: a ip string
    """
    return socket.inet_ntoa(struct.pack("I", int_ip))


# handler time and timestamp


def utc_mktime(dt, epoch=datetime.datetime(1970, 1, 1)):
    td = dt - epoch
    return int((td.microseconds + (td.seconds + td.days * 86400) * 10**6) / float(10**6))
