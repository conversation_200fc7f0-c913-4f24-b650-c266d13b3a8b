# Copyright (c) 2013-2022, SMARTX
# All rights reserved.

import logging

from common.config.constant import ZBS_CONFIG_FILE
from common.lib.cfg import Config


def get_host_config_uuid():
    HOST_UUID = "/etc/zbs/uuid"
    host_config_uuid = ""
    try:
        with open(HOST_UUID) as f:
            host_config_uuid = f.read().strip()
    except OSError:
        logging.exception("Failed to access host uuid file({}).".format(HOST_UUID))
    return host_config_uuid


def get_config_ip(ip_name, config_file=ZBS_CONFIG_FILE):
    cfg = Config(config_file)
    return cfg.get("network", ip_name)
