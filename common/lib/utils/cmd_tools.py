import logging
import subprocess
from threading import Thread


def execute(command, *args, **kwargs):
    """
    Execute an command then return its return code.
    :type command: str or unicode
    :param timeout: timeout of this command
    :type timeout: int
    :rtype: (int, str, str)
    """

    timeout = kwargs.pop("timeout", None)
    command_list = [
        command,
    ]
    command_list.extend(args)

    ref = {
        "process": None,
        "stdout": None,
        "stderr": None,
    }
    cmd_str = " ".join(command_list)

    def target():
        try:
            ref["process"] = subprocess.Popen(
                cmd_str,
                shell=True,
                close_fds=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
            )
            if ref["process"] is not None:
                ref["stdout"], ref["stderr"] = ref["process"].communicate()
        except Exception as err:
            logging.warning(f"Command execute: {cmd_str} error: {err}")
            ref["process"] = None  # Ensure process is None if it fails

    thread = Thread(target=target)
    thread.start()
    thread.join(timeout=timeout)
    if thread.is_alive():
        # Skip signalling a process that we know has already died.
        if ref["process"] is not None and ref["process"].returncode is None:
            try:
                ref["process"].terminate()
            except OSError as e:
                logging.warning(e)
            ref["process"].wait()
        thread.join()
    # Return a default error code if the process was not started
    if ref["process"] is None:
        logging.warning(f"cmd: {cmd_str} process failed to start.")
        return -1, "", f"Failed to execute command: {cmd_str}"
    return ref["process"].returncode, ref["stdout"], ref["stderr"]
