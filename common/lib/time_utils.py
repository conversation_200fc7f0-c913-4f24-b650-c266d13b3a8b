# Copyright (c) 2013-2022, SMARTX
# All rights reserved.


import calendar
from datetime import datetime
import http.client
import json

from dateutil import tz


def utc_now_timestamp():
    return calendar.timegm(datetime.utcnow().timetuple())


def utc_stamp_to_local(utc_timestamp):
    utc_dt = datetime.utcfromtimestamp(utc_timestamp)
    return utc_dt_to_local(utc_dt)


def utc_dt_to_local(utc_dt):
    local_tz = tz.tzlocal()
    utc_tz = tz.gettz("UTC")
    local_dt = utc_dt.replace(tzinfo=utc_tz).astimezone(local_tz)
    return local_dt


def request_master_timestamp(master_data_ip):
    socket_time = None
    before_request = utc_now_timestamp()

    conn = http.client.HTTPConnection(master_data_ip)
    conn.request("GET", "/api/v2/tools/time")
    resp = conn.getresponse()
    if resp.status != 200:
        socket_time = None
    else:
        result = json.loads(resp.read())
        if result["ec"] != "EOK":
            socket_time = None
        else:
            socket_time = float(result["data"]["utc_time"])
    conn.close()

    after_request = utc_now_timestamp()

    return before_request, socket_time, after_request
