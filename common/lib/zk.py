import logging
import os

import gevent
from kazoo.client import Kazoo<PERSON><PERSON>
from kazoo.handlers.gevent import SequentialGeventHandler
from kazoo.handlers.threading import SequentialThreadingHandler
from kazoo.retry import KazooRetry

from common.config.constant import NO_LEADER_SERVICE_NAMES, ZBS_CONFIG_FILE
from common.lib.cfg import ZK_ZOS_SERVICE_PATH, Config, DeployConfigManager, get_zk_service_priority_path


class ZkException(Exception):
    """
    :raises: :exc:`ZkException` if zk client fail
    """


def _zk_stat_to_mode(stat):
    if isinstance(stat, Exception):
        return "not reachable: " + str(stat), None
    if stat == "This ZooKeeper instance is not currently serving requests\n":
        return "reachable, but not responsible", None
    if not stat.startswith("Zookeeper version:"):
        return "bad response", None
    mode = "bad response"
    for line in stat.splitlines():
        if line.startswith("Mode: "):
            mode = line[len("Mode: ") :]
    return mode


class Zookeeper:
    ZK_DYNAMIC_CONFIG = "/zookeeper/config"

    def __init__(self, config_file: str = ZBS_CONFIG_FILE, thread_mode: bool = False, timeout=5):
        if thread_mode:
            handler = SequentialThreadingHandler()
        else:
            handler = SequentialGeventHandler()

        zbs_conf = Config(config_file)
        self.timeout = timeout
        self.conn = KazooClient(
            hosts=",".join(zbs_conf.get_zk_members()),
            timeout=timeout,
            handler=handler,
            connection_retry=KazooRetry(
                max_tries=-1,
                max_delay=10,
                sleep_func=handler.sleep_func,
            ),
            command_retry=KazooRetry(
                max_tries=3,
                sleep_func=handler.sleep_func,
            ),
            auth_data=[("digest", DeployConfigManager.get_zk_digest())],
        )

        try:
            self.conn.start()
        except Exception as e:
            raise ZkException(f"cannot connect to zk: {e}")
        except gevent.Timeout as e:
            raise ZkException(e.exception)
        else:
            logging.debug("zk connection established")

    def get_string(self, path: str):
        """
        Get the string value of the path
        """
        try:
            return str(self.conn.get(path)[0], encoding="utf-8")
        except Exception as e:
            raise ZkException(f"cannot get path {path}: {e}")
        except gevent.Timeout as e:
            raise ZkException(e.exception)

    # kazoo client do not support run command on specific zookeeper server,
    # so we need rewrite command() function
    def command(self, peer: str, cmd: bytes = b"ruok", timeout=None):
        """Sent a management command to the given ZK server.

        Examples are `ruok`, `envi` or `stat`.

        :returns: An unstructured textual response.
        :rtype: str

        :raises:
            :exc:`ConnectionLoss` if there is no connection open, or
            possibly a :exc:`socket.error` if there's a problem with
            the connection used just for this command.
        """

        if len(cmd) != 4:
            raise ValueError("Command must be 4 letters")
        if not isinstance(cmd, bytes):
            raise TypeError("Command must be bytes")

        addr = peer.split(":")

        if timeout is None:
            timeout = self.timeout

        sock = self.conn.handler.create_connection(
            addr,
            timeout=timeout,
            use_ssl=self.conn.use_ssl,
            ca=self.conn.ca,
            certfile=self.conn.certfile,
            keyfile=self.conn.keyfile,
            keyfile_password=self.conn.keyfile_password,
            verify_certs=self.conn.verify_certs,
        )
        sock.sendall(cmd)

        out = []
        while True:
            data = sock.recv(8192)
            if not data:
                break
            out.append(data)
        sock.close()

        result = b"".join(out)
        logging.debug(f"zk command [{cmd}] to peer {peer} result: {result}")
        return result.decode("utf-8", "replace")

    def create_recursive(self, path: str, value: bytes):
        """
        Create the path recursively.
        """

        try:
            self.conn.ensure_path(path)
            self.conn.set(path, value)
        except Exception as e:
            raise ZkException(f"cannot create path {path}: {e}")
        except gevent.Timeout as e:
            raise ZkException(e.exception)

    def get_zk_hosts(self):
        hosts = []

        for host, port in self.conn.hosts:
            hosts.append(f"{host}:{port}")
        logging.debug(f"zk hosts: {hosts}")
        return hosts

    def get_zk_host_status(self, host: str, timeout=None):
        return self.command(host, b"ruok", timeout)

    def get_zk_host_role(self, host: str, timeout=None):
        result = "bad response"
        try:
            result = self.command(peer=host, cmd=b"stat", timeout=timeout)
        except Exception as e:
            logging.error(f"get zk server [{host}] info fail: {e}")
            return result
        except gevent.Timeout as e:
            raise ZkException(e.exception)

        logging.debug(f"get zk server [{host}] info: {result}")
        return _zk_stat_to_mode(result)

    def is_zk_ok(self, host, timeout=None, catch_except=True):
        try:
            return self.get_zk_host_status(host, timeout) == "imok"
        except Exception as e:
            if not catch_except:
                raise e
            return False
        except gevent.Timeout as e:
            raise ZkException(e.exception)

    def get_zk_leader(self):
        for peer in self.get_zk_hosts():
            mode = self.get_zk_host_role(peer)
            if mode == "leader":
                return peer

        return ""

    def get_specified_members(self, value):
        logging.debug(f"get_specified_members: {value}")
        members = []
        for v in value.split(","):
            members.append(v)
        return members

    def parse_leader(self, path, children):
        seq = 1 << 31
        leader = None
        for c in children:
            _unused, s = c.split("-")
            s = int(s)
            if s < seq:
                seq = s
                leader = c
        if leader is None:
            return None

        leader_path = os.path.join(path, leader)
        return self.get_string(leader_path)

    def show_service(self, service_name):
        spath = os.path.join(ZK_ZOS_SERVICE_PATH, service_name)
        if not self.conn.exists(spath):
            return None

        service = {
            "name": service_name,
            "specified_members": self.get_specified_members(self.get_string(spath)),
        }

        members = self.conn.get_children(spath)
        if service_name not in NO_LEADER_SERVICE_NAMES:
            service["leader"] = self.parse_leader(spath, members)

        members_arr = []
        for m in members:
            mpath = os.path.join(spath, m)
            if self.conn.exists(mpath):
                members_arr.append(self.get_string(mpath)[0])
        service["members"] = members_arr

        priority_path = get_zk_service_priority_path(service_name)
        if self.conn.exists(priority_path):
            priority = self.get_string(priority_path)
        else:
            priority = ""
        service["priority"] = priority

        current_priority_path = get_zk_service_priority_path(service_name) + "/current"
        if self.conn.exists(current_priority_path):
            current_priority = self.get_string(current_priority_path)
        else:
            current_priority = ""
        service["current_priority"] = current_priority
        return service

    def list_service(self, service_path: str = ZK_ZOS_SERVICE_PATH):
        service = []
        try:
            children = self.conn.get_children(service_path)
        except Exception:
            raise ZkException("cannot list services")
        for s in children:
            # if find timemachine, skip it
            if s.find("timemachine") != -1:
                continue
            elif s.find("_priority") == -1:
                service.append(self.show_service(s))
        return service

    def get_dynamic_config(self):
        return self.get_string(self.ZK_DYNAMIC_CONFIG)

    def close(self):
        try:
            self.conn.stop()
        except Exception as e:
            raise e
        except gevent.Timeout as e:
            raise ZkException(e.exception)
        finally:
            self.conn.close()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
