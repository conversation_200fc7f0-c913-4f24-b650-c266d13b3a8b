# Copyright (c) 2014-2015, SMARTX
# All rights reserved.


import argparse


class Cmd:
    def __init__(self, parser, subparsers):
        self.parser = parser
        self.subparsers = subparsers

    def add_subcmd(self, sub_cmd, func, help=""):  # noqa: A002
        sub_parser = self.subparsers.add_parser(sub_cmd, help=help, formatter_class=argparse.RawTextHelpFormatter)
        sub_parser.set_defaults(func=func)
        return sub_parser

    def add_argument(self, *args, **kwargs):
        self.parser.add_argument(*args, **kwargs)
        return self.parser


class Parser:
    def __init__(self):
        self.parser = argparse.ArgumentParser()

        self.parser.add_argument("-v", action="store_true", dest="verbose", help="verbose")

        self.subparsers = self.parser.add_subparsers(help="sub-command help")
        self.args = None

    def add_cmd(self, cmd, help="", func=None):  # noqa: A002
        parser = self.subparsers.add_parser(cmd, help=help)
        if func:
            parser.set_defaults(func=func)
            return parser
        subparsers = parser.add_subparsers(help="sub-command help")
        return Cmd(parser, subparsers)

    def parse(self):
        self.args = self.parser.parse_args()
        return self.args

    def run(self):
        self.args.func(self.args)


class ToolParser(Parser):
    def __init__(self):
        Parser.__init__(self)
        self.add_parser_common_options()

    def add_parser_common_options(self):
        self.parser.add_argument("-f", type=str, help="json/table/dict", default="table")

    # An error occurs when the elf-tool command is run without a subcommand.
    # This issue is caused by a bug in the argparse library of Python3 (https://bugs.python.org/issue16308).
    # It is resolved by setting the default 'func' to 'print_help()' during the initialization phase of the
    # command line.
    def init_default_func(self):
        self.parser.set_defaults(func=self._default_print_help)

    def _default_print_help(self, _):
        self.parser.print_help()
