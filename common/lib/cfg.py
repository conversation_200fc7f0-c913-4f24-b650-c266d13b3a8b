# Copyright (c) 2013-2023, SMARTX
# All rights reserved.

import configparser
import fcntl
import logging
import os
import socket

HOST_UUID_PATH = "/etc/zbs/uuid"
HOST_ROLE_PATH = "/etc/zbs/role"
CLUSTER_UUID_PATH = "/etc/zbs/cluster_uuid"
# file contains zooKeeper ACL account
ZK_DIGEST_FILE = "/etc/zbs/.zk_digest"
# ZooKeeper default ACL account, used when ZK_DIGEST_FILE not exist
DEFAULT_ZK_DIGEST = "zkusr:byHNeaEv6PcaSSnR"
# zos service prefix
ZK_ZOS_SERVICE_PATH = "/zos/service"
OS_IN_RAID_PATH = "/etc/zbs/is_os_in_raid1"


class Config:
    """
    Use it like use ConfigParser's methods:
    get
    getint
    getfloat
    etc.

    What we added is a get_items method which could interpret configs like:
    zookeeper=************,************,************

    and returns an array contains the items
    """

    def __init__(self, cfg, optionxform=None):
        if not os.path.exists(cfg):
            raise RuntimeError("Config file " + cfg + " not found.")
        self.cfg = cfg

        with open(cfg) as f:
            fcntl.flock(f, fcntl.LOCK_SH)
            cfg_str = f.read()

        self.parser = configparser.ConfigParser()
        if optionxform:
            self.parser.optionxform = optionxform
        self.parser.read_string(cfg_str)

    def get_items(self, section, key, seperator=","):
        value = self.parser.get(section, key)
        items = value.strip().split(seperator)
        return items

    # delegate all other calls
    def __getattr__(self, name):
        return getattr(self.parser, name)

    def get_local_vm_ip(self):
        return self.get_items("network", "vm_ip")

    def get_local_data_ip(self):
        return self.get_items("network", "data_ip")

    def get_mongo_members(self, remove_port=False):
        """
        :return: ['*********:27017', '*********:27017', '*********:27017']
        """
        result = self.get_items("cluster", "mongo")
        if remove_port:
            result = [host.split(":")[0] for host in result]
        return result

    def get_zk_members(self, remove_port=False):
        """
        :return: ['*********:2181', '*********:2181', '*********:2181']
        """
        result = self.get_items("cluster", "zookeeper")
        if remove_port:
            result = [host.split(":")[0] for host in result]
        return result

    def get_zbs_members(self):
        """
        :return: ['*********', '*********']
        """
        return self.get_items("cluster", "members")

    def get_master_ips(self):
        return self.get_zbs_members()

    def get_cluster_mgt_ips(self):
        return self.get_items("cluster", "cluster_mgt_ips")

    def get_cluster_storage_ips(self):
        return self.get_items("cluster", "cluster_storage_ips")

    def save(self, cfg=None):
        if cfg is None:
            cfg = self.cfg
        with open(cfg, "w+") as f:
            fcntl.flock(f, fcntl.LOCK_EX)
            self.parser.write(f)


class DeployConfigManager:
    def _read_file(self, path):
        with open(path) as f:
            return f.read().strip()

    def get_node_uuid(self):
        try:
            return self._read_file(HOST_UUID_PATH)
        except OSError:
            logging.exception("Failed to access node uuid file({}).".format(HOST_UUID_PATH))
        return None

    def get_cluster_uuid(self):
        try:
            return self._read_file(CLUSTER_UUID_PATH)
        except OSError:
            logging.exception("Failed to access cluster uuid file({}).".format(CLUSTER_UUID_PATH))
        return None

    def get_node_role(self):
        try:
            return self._read_file(HOST_ROLE_PATH)
        except OSError:
            logging.exception("Failed to access node role file({}).".format(HOST_ROLE_PATH))
        return ""

    def get_hostname(self):
        """Return current node's hostname"""
        if not os.path.exists("/etc/hostname"):
            return socket.gethostname()
        with open("/etc/hostname") as f:
            return f.read().strip() or socket.gethostname()

    @staticmethod
    def get_zk_digest():
        """Return current zooKeeper digest"""
        if not os.path.exists(ZK_DIGEST_FILE):
            return DEFAULT_ZK_DIGEST
        with open(ZK_DIGEST_FILE) as f:
            return f.read().strip()

    @staticmethod
    def is_os_in_soft_raid():
        if not os.path.exists(OS_IN_RAID_PATH):
            return True

        with open(OS_IN_RAID_PATH) as f:
            return f.read().strip() == "true"


def get_zk_service_priority_path(service_name):
    return f"{ZK_ZOS_SERVICE_PATH}/{service_name}_priority"
