import re

try:
    import setproctitle
except ImportError:
    setproctitle = None

_DEFAULT_PROC_NAME = "smtx-gunicorn"


def get_proc_name(title, default=_DEFAULT_PROC_NAME):
    if not title:
        return default or _DEFAULT_PROC_NAME

    # gunicorn will call like this:
    # `util._setproctitle("master [%s]" % self.proc_name)`
    # `util._setproctitle("worker [%s]" % self.proc_name)`
    n = re.findall(r"\[(.*?)\]$", title)
    return n[0] if n and n[0] else title


def set_proc_title(title):  # pragma: no cover
    if setproctitle is not None:
        setproctitle.setproctitle(get_proc_name(title))


def patch_set_proc_title():
    from gunicorn import util

    util._setproctitle = set_proc_title


def patch_arbiter_signals():
    """
    gunicorn master does not register SIGABRT signal handler.
    After the gunicorn worker is forked, if SIGAB<PERSON> is received
    before worker.init_signals is called, the worker will perform
    the default action for SIGABRT, which is core dump.

    For gunicorn master:

    This function makes the gunicorn master to register a handler
    for SIGABRT, which handles SIGABRT just like SIGQUIT.

    For gunicorn worker:

    Before worker.init_signals is called, worker will **inherit**
    signal handlers from master, which is Arbiter.signal. It just
    puts the signal into the queue without doing any actual action,
    because the forked worker does not call the logic to consume the queue.
    After the gunicorn master sends an SIGABRT to the worker, if the worker
    is still alive, it will send the SIGKILL signal again. After that
    the worker will exit without core dump.

    After worker.init_signals is called, all signal handlers have been
    reset to the expected state. For SIGABRT, its handler will be reset
    to worker.handle_abort as normal. (also no core dump)
    """
    import signal

    from gunicorn.arbiter import Arbiter

    Arbiter.SIG_NAMES[signal.SIGABRT] = "abrt"
    if signal.SIGABRT not in Arbiter.SIGNALS:
        Arbiter.SIGNALS.append(signal.SIGABRT)
    if not hasattr(Arbiter, "handle_abrt"):
        Arbiter.handle_abrt = Arbiter.handle_quit

    raw_kill_worker = Arbiter.kill_worker

    def kill_worker_replace_sigabrt(self, pid, sig):
        if sig == signal.SIGABRT:
            self.log.info("gunicorn kill worker replace SIGABRT with SIGKILL")
            sig = signal.SIGKILL
        raw_kill_worker(self, pid, sig)

    # disable SIGABRT to avoid unnecessary core dump when signal handler not ready
    Arbiter.kill_worker = kill_worker_replace_sigabrt


def run():
    from gunicorn.app.wsgiapp import run

    patch_set_proc_title()
    patch_arbiter_signals()
    run()


if __name__ == "__main__":
    run()
