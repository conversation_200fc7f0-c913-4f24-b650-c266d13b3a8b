# Copyright (c) 2013-2018, SMARTX
# All rights reserved.


import logging
import time

import requests

from common.config.constant import AUTH_TOKEN_NAME, AUTH_TOKEN_NAME_NEW
from smartx_proto.errors import pyerror_pb2 as py_error
from zbs.lib.zexception import ZException


class ElfRestClientException(ZException):
    def __init__(self, msg="", user_code=None):
        self.user_code = user_code or py_error.REST_UNKNOWN_ERROR
        self.uc_name = py_error.ErrorCode.Name(self.user_code)
        self.short_message = "[%s]" % self.uc_name
        self.message = self.short_message + msg


class ElfRestException:
    class Account:
        class InvalidAccount(ElfRestClientException):
            pass

    class ElfAPIException(ElfRestClientException):
        pass

    class JobFailedException(ElfRestClientException):
        pass

    class NicMappingException(ElfRestClientException):
        pass


class ElfApiClient:
    """The RESTful client for the API v2/v3."""

    def __init__(self, server=None, port=None, username=None, password=None, token=None, default_timeout=30):
        self.username = username
        self.password = password
        self.server = server
        self.port = port
        self.token = token
        self._protocol = "https" if self.port == 443 else "http"
        self._default_timeout = default_timeout

    @classmethod
    def test(cls, host, port, username, password):
        client = cls(host, port, username, password)
        token = None
        # noinspection PyBroadException
        try:
            token = client.get_token()
        except Exception:
            logging.exception("Failed to test the elf account, host: '{}'.".format(host))
        finally:
            if token:
                # noinspection PyBroadException
                client.release(token)
            return bool(token)

    def get_token(self):
        path = "{protocol}://{server}:{port}/api/v3/sessions".format(
            protocol=self._protocol,
            server=self.server,
            port=self.port,
        )
        try:
            r = requests.post(url=path, json={"username": self.username, "password": self.password}, verify=False)
            r.raise_for_status()
        except requests.exceptions.RequestException:
            logging.exception("Cannot fetch token.")
            return None
        else:
            return r.json()["token"]

    def release(self, token):
        if not token:
            return False

        path = "{protocol}://{server}:{port}/api/v3/session".format(
            protocol=self._protocol,
            server=self.server,
            port=self.port,
        )
        try:
            r = requests.delete(url=path, headers={"Grpc-Metadata-Token": token}, verify=False)
            r.raise_for_status()
        except requests.exceptions.RequestException:
            logging.exception("Failed to delete token '{}'".format(token))
            return False
        else:
            return True

    def gen_url(self, path, is_v3=False):
        if path.startswith("/"):
            path = path[1:]

        return "{protocol}://{server}:{port}/api/{version}/{path}".format(
            protocol=self._protocol, server=self.server, port=self.port, version="v3" if is_v3 else "v2", path=path
        )

    def _call_method(self, method, url, check_ec, timeout, **kwargs):
        token = self.token or self.get_token()
        if not token:
            raise ElfRestException.Account.InvalidAccount("Unauthorized")

        m = getattr(requests, method)
        headers = {AUTH_TOKEN_NAME: token, AUTH_TOKEN_NAME_NEW: token, "Grpc-Metadata-Token": token}
        try:
            r = m(url, headers=headers, verify=False, timeout=timeout or self._default_timeout, **kwargs)
            r.raise_for_status()
        except requests.exceptions.RequestException as e:
            raise ElfRestException.ElfAPIException("An elf API exception occurred, error={}.".format(str(e)))
        else:
            if check_ec:
                res = r.json()
                if res.get("ec") != "EOK":
                    raise ElfRestException.ElfAPIException(
                        "An elf API exception occurred, error={}.".format(res["error"])
                    )
                return res["data"]
            return r.json()
        finally:
            if not self.token:
                self.release(token)

    def post(self, path, json, is_v3=False, check_ec=False, timeout=None):
        return self._call_method(
            method="post", url=self.gen_url(path, is_v3=is_v3), check_ec=check_ec, timeout=timeout, json=json
        )

    def post_with_options(self, path, json, is_v3=False, check_ec=False, timeout=None, **kwargs):
        return self._call_method(
            method="post", url=self.gen_url(path, is_v3=is_v3), check_ec=check_ec, timeout=timeout, json=json, **kwargs
        )

    def get(self, path, is_v3=False, check_ec=False, timeout=None, **kwargs):
        return self._call_method(
            method="get", url=self.gen_url(path, is_v3=is_v3), check_ec=check_ec, timeout=timeout, **kwargs
        )

    def delete(self, path, is_v3=False, check_ec=False, timeout=None, **kwargs):
        return self._call_method(
            method="delete", url=self.gen_url(path, is_v3), check_ec=check_ec, timeout=timeout, **kwargs
        )

    def put(self, path, json, is_v3=False, check_ec=False, timeout=None):
        return self._call_method(
            method="put", url=self.gen_url(path, is_v3=is_v3), check_ec=check_ec, timeout=timeout, json=json
        )

    def wait_job(self, job_id):
        url = "/jobs/{}".format(job_id)
        while True:
            res = self.get(url)
            state = res["data"]["job"]["state"]

            if state == "done":
                return res
            if state == "failed":
                logging.warning("job {} failed ".format(job_id))
                return None

            time.sleep(1)
