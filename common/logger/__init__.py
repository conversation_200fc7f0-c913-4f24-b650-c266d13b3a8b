# Copyright (c) 2014-2015, SMARTX
# All rights reserved.

"""
Logger for VM
"""

import logging
import os
import sys

from common.config.constant import FILE_MODE


def init_logger(verbose=False, fname=None):
    if verbose:
        level = logging.INFO
    else:
        level = logging.ERROR

    if not os.path.isdir("/var/log/zbs"):
        os.makedirs("/var/log/zbs", FILE_MODE)

    format = "%(asctime)s, %(levelname)s, %(filename)s:%(lineno)d, %(message)s"  # noqa: A001
    if fname:
        logging.basicConfig(format=format, level=level, filename=fname)
    else:
        logging.basicConfig(format=format, level=level, stream=sys.stderr)
