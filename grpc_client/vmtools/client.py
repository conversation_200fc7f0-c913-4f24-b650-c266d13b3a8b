# Copyright (c) 2013-2022, SMARTX
# All rights reserved.
import functools
import logging

import grpc

from google.protobuf.json_format import MessageToDict, ParseDict
from grpc_client.common import handle_error
from grpc_client.common.constant import VM_TOOLS_SERVER_DEFAULT_HOST, VM_TOOLS_SERVER_DEFAULT_PORT
from smartx_proto.smartx_vmtools_agent import svt_pb2
from smartx_proto.smartx_vmtools_agent.svt_pb2_grpc import SvtStub

# The response time of the grpc server,
# if it exceeds this time, will raise (StatusCode.DEADLINE_EXCEEDED, Deadline Exceeded)
DEFAULT_GRPC_TIMEOUT = 60
# The maximum timeout for executing the qga command fsfreeze is 240s,
# and the fsfreeze grpc call timeout here should be greater than that.
FSFREEZE_GRPC_TIMEOUT = 250
# The timeout for upgrading VM tools is set to 360 seconds, which exceeds
# the maximum expected duration of 300 seconds for the operation.
UPGRADE_GRPC_TIMEOUT = 360
# The timeout for managing guest service is set to 360 seconds, which exceeds
# the maximum expected duration of 300 seconds for the operation.
MANAGE_SERVICE_GRPC_TIMEOUT = 360

logger = logging.getLogger(__name__)


def retry_on_grpc_error(retry_times=1):
    def _call_func(func):
        @functools.wraps(func)
        def _func(self, *args, **kwargs):
            times = 0
            while True:
                try:
                    return func(self, *args, **kwargs)
                except grpc.RpcError as e:
                    logging.warning(f"call `{func.__name__}` failed {times + 1} times.")
                    if times < retry_times:
                        times += 1
                    else:
                        logging.error("call grpc `{}` error: {}".format(func.__name__, str(e)))
                        raise

        return _func

    return _call_func


class VmToolsClient:
    def __init__(self, host=None, port=None):
        self.host = host or VM_TOOLS_SERVER_DEFAULT_HOST
        self.port = port or VM_TOOLS_SERVER_DEFAULT_PORT

    @property
    def grpc_channel(self):
        """Create a grpc channel that can be reused by multi stub wrapper."""
        logger.debug("Create grpc channel to host: {}, port:{}.".format(self.host, self.port))
        return grpc.insecure_channel("{}:{}".format(self.host, self.port))

    @property
    def svt_stub(self):
        logger.debug("Create vmtools grpc stub.")
        return SvtStub(self.grpc_channel)

    @handle_error
    def batch_get_vm_perf_stats(self, domain_names, token="", timeout=None):
        """Note that the max calling wait time in server side is 20 seconds.
        :arg timeout: vmtools as optimize service is dispensable.
        python grpc client will try to reconnect the unavailable server for
        60s, set default deadline to 23s. The max calling cost is about 22.3
        sec in test.
        """
        response = self.svt_stub.BatchGetPerformanceData(
            svt_pb2.BatchGetPerformanceDataRequest(id="-", vm_uuids=domain_names),
            metadata=[("token", token)],
            timeout=timeout or DEFAULT_GRPC_TIMEOUT,
        )
        return MessageToDict(response, True, True)

    def update_svt_properties(self, vm_uuid, update_mask, properties, token="", timeout=None):
        # covert update mask from snake case to camel case
        components = update_mask.split("_")
        update_mask = components[0] + "".join(x.title() for x in components[1:])
        request_json = {"id": vm_uuid, "update_mask": update_mask, "properties": properties}
        result = self.svt_stub.UpdateProperties(
            ParseDict(request_json, svt_pb2.UpdatePropertiesRequest(), ignore_unknown_fields=True),
            metadata=[("token", token)],
            timeout=timeout or DEFAULT_GRPC_TIMEOUT,
        )
        return MessageToDict(result, True, True)

    def freeze_svt_file_system(self, vm_uuid, token="", timeout=None):
        response = self.svt_stub.FreezeFileSystem(
            svt_pb2.FreezeFileSystemRequest(id=vm_uuid),
            metadata=[("token", token)],
            timeout=timeout or FSFREEZE_GRPC_TIMEOUT,
        )
        return MessageToDict(response, True, True)

    def thaw_svt_file_system(self, vm_uuid, token="", timeout=None):
        response = self.svt_stub.ThawFileSystem(
            svt_pb2.ThawFileSystemRequest(id=vm_uuid),
            metadata=[("token", token)],
            timeout=timeout or DEFAULT_GRPC_TIMEOUT,
        )
        return MessageToDict(response, True, True)

    def get_svt_file_system_status(self, vm_uuid, token="", timeout=None):
        response = self.svt_stub.GetFileSystemStatus(
            svt_pb2.GetFileSystemStatusRequest(id=vm_uuid),
            metadata=[("token", token)],
            timeout=timeout or DEFAULT_GRPC_TIMEOUT,
        )
        return MessageToDict(response, True, True)

    def sync_vm_time_with_host(self, vm_uuid, token="", timeout=None):
        self.svt_stub.SyncVMTimeWithHost(
            svt_pb2.SyncVMTimeWithHostRequest(vm_uuid=vm_uuid),
            metadata=[("token", token)],
            timeout=timeout or DEFAULT_GRPC_TIMEOUT,
        )

    def delete_svt_data_for_non_existent_vms(self, token="", timeout=None):
        result = self.svt_stub.DeleteSVTDataForNonExistentVMS(
            svt_pb2.Empty(), metadata=[("token", token)], timeout=timeout or DEFAULT_GRPC_TIMEOUT
        )
        return MessageToDict(result, True, True)

    @retry_on_grpc_error()
    def get_vm_svt_data(self, vm_uuid, token="", timeout=None):
        response = self.svt_stub.GetVmSvtData(
            svt_pb2.GetVmSvtDataRequest(vm_uuid=vm_uuid),
            metadata=[("token", token)],
            timeout=timeout or DEFAULT_GRPC_TIMEOUT,
        )
        return MessageToDict(response, True, True)

    @retry_on_grpc_error()
    def batch_get_vm_svt_data(self, vm_uuids, token="", timeout=None):
        response = self.svt_stub.BatchGetVmSvtData(
            svt_pb2.BatchGetVmSvtDataRequest(vm_uuids=vm_uuids),
            metadata=[("token", token)],
            timeout=timeout or DEFAULT_GRPC_TIMEOUT,
        )
        return MessageToDict(response, True, True)

    @retry_on_grpc_error()
    def reset_svt_data(self, vm_uuid, token="", timeout=None):
        self.svt_stub.ClearVmSvtData(
            svt_pb2.ClearVmSvtDataRequest(vm_uuid=vm_uuid),
            metadata=[("token", token)],
            timeout=timeout or DEFAULT_GRPC_TIMEOUT,
        )

    @retry_on_grpc_error()
    def batch_get_svt_images(self, name=None, uuid=None, version=None, path=None, latest=False, token="", timeout=None):
        response = self.svt_stub.BatchGetSvtImages(
            svt_pb2.BatchGetSvtImagesRequest(name=name, uuid=uuid, version=version, path=path, latest=latest),
            metadata=[("token", token)],
            timeout=timeout or DEFAULT_GRPC_TIMEOUT,
        )
        # Warning: response SvtImage fileds version(int64) size(int64) create_time(int64) will be converted to str.
        # ref: https://github.com/protocolbuffers/protobuf/issues/2954
        # ref: https://github.com/protocolbuffers/protobuf/issues/12252
        return MessageToDict(response, True, True)

    @retry_on_grpc_error()
    def delete_svt_images(self, uuids, token="", timeout=None):
        self.svt_stub.BatchDeleteSvtImages(
            svt_pb2.BatchDeleteSvtImagesRequest(uuids=uuids),
            metadata=[("token", token)],
            timeout=timeout or DEFAULT_GRPC_TIMEOUT,
        )

    @retry_on_grpc_error()
    def create_svt_image(
        self,
        image_name,
        uuid,
        path,
        size,
        file_name,
        description="",
        token="",
        timeout=None,
        version=0,
        storage_cluster_uuid="",
    ):
        response = self.svt_stub.CreateSvtImage(
            svt_pb2.CreateSvtImageRequest(
                name=image_name,
                uuid=uuid,
                path=path,
                size=size,
                file_name=file_name,
                description=description,
                version=version,
                storage_cluster_uuid=storage_cluster_uuid,
            ),
            metadata=[("token", token)],
            timeout=timeout or DEFAULT_GRPC_TIMEOUT,
        )
        # Warning: response SvtImage fileds version(int64) size(int64) create_time(int64) will be converted to str.
        # ref: https://github.com/protocolbuffers/protobuf/issues/2954
        # ref: https://github.com/protocolbuffers/protobuf/issues/12252
        return MessageToDict(response, True, True)

    @retry_on_grpc_error()
    def search_macs(self, ips, token="", timeout=None):
        response = self.svt_stub.SearchMacs(
            svt_pb2.SearchMacsRequest(ips=ips), metadata=[("token", token)], timeout=timeout or DEFAULT_GRPC_TIMEOUT
        )
        return MessageToDict(response, True, True)

    @retry_on_grpc_error()
    def batch_update_svt_data(self, vm_uuids, token, timeout=None):
        result = self.svt_stub.BatchUpdateSvtData(
            ParseDict({"vm_uuids": vm_uuids}, svt_pb2.BatchUpdateSvtDataRequest(), ignore_unknown_fields=True),
            metadata=[("token", token)],
            timeout=timeout or DEFAULT_GRPC_TIMEOUT,
        )
        return MessageToDict(result, True, True)

    @retry_on_grpc_error()
    def upgrade_svt_service(self, vm_uuid, token=None, timeout=None):
        result = self.svt_stub.UpgradeVMTools(
            svt_pb2.UpgradeVMToolsRequest(vm_uuid=vm_uuid),
            metadata=[("token", token)],
            timeout=timeout or UPGRADE_GRPC_TIMEOUT,
        )
        return MessageToDict(result, True, True)

    @retry_on_grpc_error()
    def manage_guest_service(self, vm_uuid, service_name, action, need_vmtools_iso, token=None, timeout=None):
        result = self.svt_stub.ManageGuestService(
            svt_pb2.ManageGuestServiceRequest(
                vm_uuid=vm_uuid, service_name=service_name, action=action, need_vmtools_iso=need_vmtools_iso
            ),
            metadata=[("token", token)],
            timeout=timeout or MANAGE_SERVICE_GRPC_TIMEOUT,
        )
        return MessageToDict(result, True, True)

    @retry_on_grpc_error()
    def batch_manage_guest_service(self, vm_uuids, service_name, action, need_vmtools_iso, token=None, timeout=None):
        result = self.svt_stub.BatchManageGuestService(
            svt_pb2.BatchManageGuestServiceRequest(
                vm_uuids=vm_uuids, service_name=service_name, action=action, need_vmtools_iso=need_vmtools_iso
            ),
            metadata=[("token", token)],
            timeout=timeout or MANAGE_SERVICE_GRPC_TIMEOUT / 4 * len(vm_uuids),
        )
        return MessageToDict(result, True, True)


vmtools_client = VmToolsClient()


if __name__ == "__main__":
    domain_uuids = [
        "1ef117fd-5258-4304-b933-073085620f16",
        "0c75bb9a-47ef-4a6e-aee6-25a2102304b0",
        "31921105-800a-4d11-bb1c-33f7acd4150d",
        "8c564032-5a23-43e9-8adf-9d87706ae444",
    ]
    print(vmtools_client.batch_get_vm_perf_stats(domain_uuids))
