# Copyright (c) 2013-2018, SMARTX
# All rights reserved.

from functools import wraps
import logging

import grpc

from google.protobuf import empty_pb2
from grpc_client.common.constant import CRAB_DEFAULT_SERVER, CRAB_PORT
from grpc_client.common.protobuf_to_dict import pb2dict
from smartx_proto.crab import crab_pb2, crab_pb2_grpc


class CrabClient:
    """
    Client for the crab server
    """

    ROLE_UNSPECIFIED = "ROLE_UNSPECIFIED"
    READ_ONLY = "READ_ONLY"
    READ_ONLY_SERVICE = "READ_ONLY_SERVICE"
    ADMIN = "ADMIN"
    ADMIN_SERVICE = "ADMIN_SERVICE"
    ROOT = "ROOT"
    ROOT_SERVICE = "ROOT_SERVICE"

    def __init__(self, server=CRAB_DEFAULT_SERVER, port=CRAB_PORT):
        self.__service_stub = None
        self.__server_host = None
        self.__server = server

    def get_service_ip(self):
        return self.__server

    def error_handler(func):
        @wraps(func)
        def decorated_func(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except grpc.RpcError as e:
                logging.error("code={}, error_message={}".format(e.code(), e.details()))
                return None

        return decorated_func

    @property
    def service_stub(self):
        server_host = self.get_service_ip()

        if self.__service_stub is None or self.__server_host != server_host:
            logging.debug("Create channel to {}:{}".format(server_host, CRAB_PORT))
            self.__service_stub = crab_pb2_grpc.CrabStub(grpc.insecure_channel("{}:{}".format(server_host, CRAB_PORT)))
            self.__server_host = server_host

        return self.__service_stub

    @error_handler
    def create_session(self, username, password):
        response = self.service_stub.CreateSession(crab_pb2.CreateSessionRequest(username=username, password=password))
        return pb2dict(response)

    @error_handler
    def delete_session(self, token):
        response = self.service_stub.DeleteSession(empty_pb2.Empty(), metadata=[("token", token)])
        return pb2dict(response)

    @error_handler
    def get_current_user(self, token):
        metadata = [("token", token)]
        response = self.service_stub.GetCurrentUser(empty_pb2.Empty(), metadata=metadata, timeout=5)
        return pb2dict(response)

    @error_handler
    def list_user(self, token):
        metadata = [("token", token)]
        response = self.service_stub.ListUsers(crab_pb2.ListUsersRequest(), metadata=metadata)
        return [pb2dict(user) for user in response.users]

    @error_handler
    def create_user(self, token, username, password, email, role):
        metadata = [("token", token)]
        response = self.service_stub.CreateUser(
            crab_pb2.CreateUserRequest(
                user=crab_pb2.User(username=username, email=email, password=password, role=role)
            ),
            metadata=metadata,
        )
        return pb2dict(response)

    @error_handler
    def delete_user(self, token, name):
        metadata = [("token", token)]
        response = self.service_stub.DeleteUser(crab_pb2.DeleteUserRequest(name=name), metadata=metadata)
        return pb2dict(response)
