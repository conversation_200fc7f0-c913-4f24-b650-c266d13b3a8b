# Copyright (c) 2013-2018, SMARTX
# All rights reserved.
import logging

import grpc

from google.protobuf.json_format import MessageToDict
from grpc_client.common.constant import TIMEMACHINE_DEFAULT_SERVER, TIMEMACHINE_PORT
from smartx_proto.timemachine import timemachine_pb2, timemachine_pb2_grpc

LOG = logging.getLogger(__name__)


class TimeMachineClient:
    def __init__(self, server=TIMEMACHINE_DEFAULT_SERVER, port=TIMEMACHINE_PORT):
        target = "{}:{}".format(server, port)
        LOG.info("The target of timemachine channel is `{}`".format(target))

        self.channel = grpc.insecure_channel(target)

    @property
    def timemachine_stub(self):
        return timemachine_pb2_grpc.TimemachineStub(self.channel)

    def list_protect_plan(self, filter_type=timemachine_pb2.TIMEMACHINE_FILTER_TYPE_ALL, token=""):
        metadata = [("token", token)]
        res = self.timemachine_stub.ListProtectPlan(
            timemachine_pb2.ListProtectPlanRequest(filter=filter_type), metadata=metadata
        )
        # pb2dict can't process map<key,value> in Message
        res_dict = MessageToDict(res, including_default_value_fields=True, preserving_proto_field_name=True)
        return res_dict.get("plans", [])

    def list_protect_site(self, token=""):
        metadata = [("token", token)]
        res = self.timemachine_stub.ListProtectSite(timemachine_pb2.ListProtectSiteRequest(), metadata=metadata)
        # pb2dict can't process map<key,value> in Message
        res_dict = MessageToDict(res, including_default_value_fields=True, preserving_proto_field_name=True)
        return res_dict.get("sites", [])


if __name__ == "__main__":
    from zbs_rest.api.common.client import get_service_token

    token = get_service_token()
    c = TimeMachineClient()
    print(c.list_protect_plan(token=token))
    print(c.list_protect_site(token=token))
