# Copyright (c) 2013-2018, SMARTX
# All rights reserved.
import functools
import logging

import grpc

logger = logging.getLogger(__name__)


def cached_property(func):
    """Memoize property for class"""

    @functools.wraps(func)
    def wrapper(self):
        if not hasattr(self, "_cached_property"):
            self._cached_property = {}
        if func.__name__ not in self._cached_property:
            self._cached_property[func.__name__] = func(self)
        return self._cached_property[func.__name__]

    return property(wrapper)


def handle_error(func):
    """handle grpc stub call error
    :param func:
    :return:
    """

    @functools.wraps(func)
    def decorated_func(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except grpc.RpcError as e:
            logger.error("code={}, error_message={}".format(e.code(), e.details()))
            return None

    return decorated_func
