# Copyright (c) 2013-2018, SMARTX
# All rights reserved.

import logging

from google.protobuf.descriptor import FieldDescriptor

__all__ = ["pb2dict", "TYPE_CALLABLE_MAP"]

TYPE_CALLABLE_MAP = {
    FieldDescriptor.TYPE_DOUBLE: float,
    FieldDescriptor.TYPE_FLOAT: float,
    FieldDescriptor.TYPE_INT32: int,
    FieldDescriptor.TYPE_INT64: int,
    FieldDescriptor.TYPE_UINT32: int,
    FieldDescriptor.TYPE_UINT64: int,
    FieldDescriptor.TYPE_SINT32: int,
    FieldDescriptor.TYPE_SINT64: int,
    FieldDescriptor.TYPE_FIXED32: int,
    FieldDescriptor.TYPE_FIXED64: int,
    FieldDescriptor.TYPE_SFIXED32: int,
    FieldDescriptor.TYPE_SFIXED64: int,
    FieldDescriptor.TYPE_BOOL: bool,
    FieldDescriptor.TYPE_STRING: str,
    FieldDescriptor.TYPE_BYTES: lambda b: b.decode("utf-8"),
    FieldDescriptor.TYPE_ENUM: int,
}


def repeated(type_callable):
    return lambda value_list: [type_callable(value) for value in value_list]


def enum_label_name(field, value):
    return field.enum_type.values_by_number[int(value)].name


def _include_default_value_fields(pb, exclude_fields, fields):
    for field in pb.DESCRIPTOR.fields:
        # Oneof fields will not be affected.
        if hasattr(field, "containing_oneof") and field.containing_oneof:
            continue
        # Skip the field which has been included already or is exclusive
        if field not in fields and field.name not in exclude_fields and field.has_default_value:
            fields[field] = field.default_value
    return fields


def pb2dict(
    pb,
    type_callable_map=TYPE_CALLABLE_MAP,
    use_enum_labels=False,
    exclude_fields=None,
    including_default_value_fields=True,
):
    exclude_fields = exclude_fields or []
    if isinstance(exclude_fields, str):
        exclude_fields = [exclude_fields]

    result_dict = {}
    fields = dict(pb.ListFields())
    # Serialize default value if including_default_value_fields is True.
    if including_default_value_fields:
        fields = _include_default_value_fields(pb, exclude_fields, fields)

    for field, value in list(fields.items()):
        if field.name in exclude_fields:
            continue
        if field.type == FieldDescriptor.TYPE_MESSAGE:
            # recursively encode protobuf sub-message
            type_callable = lambda pb: pb2dict(  # noqa: E731
                pb,
                type_callable_map=type_callable_map,
                use_enum_labels=use_enum_labels,
                including_default_value_fields=including_default_value_fields,
            )
        elif field.type in type_callable_map:
            type_callable = type_callable_map[field.type]
        else:
            raise TypeError("Field %s.%s has unrecognised type id %d" % (pb.__class__.__name__, field.name, field.type))
        if use_enum_labels and field.type == FieldDescriptor.TYPE_ENUM:
            type_callable = lambda value: enum_label_name(field, value)  # noqa: E731
        if field.label == FieldDescriptor.LABEL_REPEATED:
            type_callable = repeated(type_callable)

        try:
            result_dict[field.name] = type_callable(value)
        except UnicodeDecodeError:
            logging.warning(
                "Field decoding failure(class=%s, name=%s, type=%d)." % (pb.__class__.__name__, field.name, field.type)
            )
            result_dict[field.name] = value.decode("latin-1")

    return result_dict
