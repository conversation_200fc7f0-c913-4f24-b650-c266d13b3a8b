# Copyright (c) 2023, SMARTX
# All rights reserved.
from unittest.mock import Mo<PERSON>, PropertyMock, patch
import uuid
import grpc
import pytest

from google.protobuf import json_format
from grpc_client.vmtools import client
from smartx_app.elf.common import constants as elf_common_constants
from smartx_proto.smartx_vmtools_agent import svt_pb2


@pytest.fixture
def svt_data():
    return {
        "vm_uuid": "vm_uuid",
        "last_update": "111",
        "static_info": {
            "ga_state": elf_common_constants.SVT_ACTIVE,
            "ga_version": "1.0.0",
            "kernel_info": "3.10.0-327.el7.x86_64",
            "os_version": "CentOS Linux release 7.2.1511 (Core) ",
            "hostname": "localhost.localdomain",
            "dns_servers": ["***************"],
            "ntp_servers": [],
            "gateway_ips": ["************"],
            "nics": [
                {
                    "name": "lo",
                    "ip_addresses": [
                        {"ip_address_type": "ipv4", "ip_address": "127.0.0.1", "prefix": 8, "netmask": "*********"},
                        {"ip_address_type": "ipv6", "ip_address": "::1", "prefix": 128},
                    ],
                    "hardware_address": "00:00:00:00:00:00",
                    "ip_type": "unknown",
                    "gateway_ip": "",
                },
                {
                    "name": "eth0",
                    "ip_addresses": [
                        {
                            "ip_address_type": "ipv4",
                            "ip_address": "**************",
                            "prefix": 17,
                            "netmask": "*************",
                        },
                        {"ip_address_type": "ipv6", "ip_address": "fe80::5054:ff:fe80:2e36", "prefix": 64},
                    ],
                    "hardware_address": "52:54:00:80:2e:36",
                    "ip_type": "static",
                    "gateway_ip": "************",
                },
            ],
        },
        "storage_info": {
            "disks": [
                {"name": "disk1", "serial": "05f46f8f-a634-355d-a", "size": 42944186880, "used": 88588288},
                {"name": "disk0", "serial": "21619ee0-ba79-3d78-b", "size": 64420392960, "used": 11332669440},
            ],
            "size": 107364579840,
            "used": 11421257728,
        },
    }


class TestVmToolsClient:
    def test_batch_get_vm_perf_stats(self):
        res = client.VmToolsClient().batch_get_vm_perf_stats("vm_uuid", timeout=1)
        assert res is None

    @patch("grpc_client.vmtools.client.VmToolsClient.grpc_channel", new_callable=PropertyMock)
    def test_update_svt_properties(self, mock_grpc_channel):
        timeout_list = []

        def foo(arg, metadata="", timeout=None):
            timeout_list.append(timeout)
            return json_format.ParseDict(
                {"id": "vm_uuid", "update_mask": "hostname", "properties": {"hostname": "dev"}},
                svt_pb2.UpdatePropertiesRequest(),
                ignore_unknown_fields=True,
            )

        m = Mock()
        m.unary_unary.return_value = foo
        mock_grpc_channel.return_value = m

        res = client.VmToolsClient().update_svt_properties("vm_uuid", "hostname", {"hostname": "dev"}, timeout=1)
        assert len(timeout_list) == 1
        assert timeout_list[0] == 1
        assert res["properties"]["hostname"] == "dev"

    @patch("grpc_client.vmtools.client.VmToolsClient.grpc_channel", new_callable=PropertyMock)
    def test_freeze_svt_file_system(self, mock_grpc_channel):
        timeout_list = []

        def foo(arg, metadata="", timeout=None):
            timeout_list.append(timeout)
            return json_format.ParseDict(
                {"num_fs_frozen": 1}, svt_pb2.FreezeFileSystemResponse(), ignore_unknown_fields=True
            )

        m = Mock()
        m.unary_unary.return_value = foo
        mock_grpc_channel.return_value = m

        res = client.VmToolsClient().freeze_svt_file_system("vm_uuid", timeout=1)
        assert len(timeout_list) == 1
        assert timeout_list[0] == 1
        assert res["num_fs_frozen"] == 1

    @patch("grpc_client.vmtools.client.VmToolsClient.grpc_channel", new_callable=PropertyMock)
    def test_thaw_svt_file_system(self, mock_grpc_channel):
        timeout_list = []

        def foo(arg, metadata="", timeout=None):
            timeout_list.append(timeout)
            return json_format.ParseDict(
                {"num_fs_thawed": 1}, svt_pb2.ThawFileSystemResponse(), ignore_unknown_fields=True
            )

        m = Mock()
        m.unary_unary.return_value = foo
        mock_grpc_channel.return_value = m

        res = client.VmToolsClient().thaw_svt_file_system("vm_uuid", timeout=1)
        assert len(timeout_list) == 1
        assert timeout_list[0] == 1
        assert res["num_fs_thawed"] == 1

    @patch("grpc_client.vmtools.client.VmToolsClient.grpc_channel", new_callable=PropertyMock)
    def test_get_svt_file_system_status(self, mock_grpc_channel):
        timeout_list = []

        def foo(arg, metadata="", timeout=None):
            timeout_list.append(timeout)
            return json_format.ParseDict({"status": "status"}, svt_pb2.FileSystemStatus(), ignore_unknown_fields=True)

        m = Mock()
        m.unary_unary.return_value = foo
        mock_grpc_channel.return_value = m

        res = client.VmToolsClient().get_svt_file_system_status("vm_uuid", timeout=1)
        assert len(timeout_list) == 1
        assert timeout_list[0] == 1
        assert res["status"] == "status"

    @patch("grpc_client.vmtools.client.VmToolsClient.grpc_channel", new_callable=PropertyMock)
    def test_sync_vm_time_with_host(self, mock_grpc_channel):
        timeout_list = []

        def foo(arg, metadata="", timeout=None):
            timeout_list.append(timeout)
            return json_format.ParseDict("", svt_pb2.Empty(), ignore_unknown_fields=True)

        m = Mock()
        m.unary_unary.return_value = foo
        mock_grpc_channel.return_value = m

        res = client.VmToolsClient().sync_vm_time_with_host("vm_uuid", timeout=1)
        assert len(timeout_list) == 1
        assert timeout_list[0] == 1
        assert res is None

    @patch("grpc_client.vmtools.client.VmToolsClient.grpc_channel", new_callable=PropertyMock)
    def test_delete_svt_data_for_non_existent_vms(self, mock_grpc_channel):
        timeout_list = []

        def foo(arg, metadata="", timeout=None):
            timeout_list.append(timeout)
            return json_format.ParseDict({"vm_uuids": ["uuid"]}, svt_pb2.VmIdListInfo(), ignore_unknown_fields=True)

        m = Mock()
        m.unary_unary.return_value = foo
        mock_grpc_channel.return_value = m

        res = client.VmToolsClient().delete_svt_data_for_non_existent_vms(timeout=1)
        assert len(timeout_list) == 1
        assert timeout_list[0] == 1
        assert res["vm_uuids"] == ["uuid"]

    @patch("grpc_client.vmtools.client.VmToolsClient.grpc_channel", new_callable=PropertyMock)
    def test_get_vm_svt_data(self, mock_grpc_channel):
        timeout_list = []

        def foo(arg, metadata="", timeout=None):
            timeout_list.append(timeout)
            return json_format.ParseDict({"vm_uuid": "uuid"}, svt_pb2.GetVmSvtDataRequest(), ignore_unknown_fields=True)

        m = Mock()
        m.unary_unary.return_value = foo
        mock_grpc_channel.return_value = m

        res = client.VmToolsClient().get_vm_svt_data("vm_uuid", timeout=1)
        assert len(timeout_list) == 1
        assert timeout_list[0] == 1
        assert res["vm_uuid"] == "uuid"

    @patch("grpc_client.vmtools.client.VmToolsClient.grpc_channel", new_callable=PropertyMock)
    def test_batch_get_svt_info(self, mock_grpc_channel, svt_data):
        timeout_list = []
        response = {
            "vm_svt_data": [
                {
                    "vm_uuid": svt_data["vm_uuid"],
                    "last_update": svt_data["last_update"],
                    "static_info": {
                        "ga_state": svt_data["static_info"]["ga_state"],
                        "ga_version": svt_data["static_info"]["ga_version"],
                        "kernel_info": svt_data["static_info"]["kernel_info"],
                        "os_version": svt_data["static_info"]["os_version"],
                        "hostname": svt_data["static_info"]["hostname"],
                        "dns_servers": svt_data["static_info"]["dns_servers"],
                        "gateway_ips": svt_data["static_info"]["gateway_ips"],
                        "nics": svt_data["static_info"]["nics"],
                        "ntp_servers": svt_data["static_info"].get("ntp_servers", []),
                    },
                    "storage_info": {
                        "size": svt_data["storage_info"]["size"],
                        "used": svt_data["storage_info"]["used"],
                        "disks": svt_data["storage_info"]["disks"],
                    },
                }
            ]
        }

        def foo(arg, metadata="", timeout=None):
            timeout_list.append(timeout)
            return json_format.ParseDict(response, svt_pb2.BatchGetVmSvtDataResponse(), ignore_unknown_fields=True)

        m = Mock()
        m.unary_unary.return_value = foo
        mock_grpc_channel.return_value = m

        res = client.VmToolsClient().batch_get_vm_svt_data(["vm_uuid"], timeout=1)
        assert len(timeout_list) == 1
        assert timeout_list[0] == 1
        assert res["vm_svt_data"][0]["static_info"]["ga_state"] == svt_data["static_info"]["ga_state"]
        assert res["vm_svt_data"][0]["static_info"]["hostname"] == svt_data["static_info"]["hostname"]

    @patch("grpc_client.vmtools.client.VmToolsClient.svt_stub", new_callable=PropertyMock)
    def test_reset_svt_data(self, mock_svt_stub):
        timeout_list = []

        def foo(arg, metadata="", timeout=None):
            timeout_list.append(timeout)
            return json_format.ParseDict(
                {"vm_uuid": "uuid"}, svt_pb2.ClearVmSvtDataRequest(), ignore_unknown_fields=True
            )

        m = Mock()
        m.ClearVmSvtData.side_effect = foo
        mock_svt_stub.return_value = m

        client.VmToolsClient().reset_svt_data("uuid", timeout=1)
        assert len(timeout_list) == 1
        assert timeout_list[0] == 1

    @patch("grpc_client.vmtools.client.VmToolsClient.svt_stub", new_callable=PropertyMock)
    def test_batch_delete_svt_images(self, mock_svt_stub):
        timeout_list = []
        def foo(arg, metadata="", timeout=None):
            timeout_list.append(timeout)
            return json_format.ParseDict({}, svt_pb2.Empty(), ignore_unknown_fields=True)

        m = Mock()
        m.BatchDeleteSvtImages.side_effect = foo
        mock_svt_stub.return_value = m

        res = client.VmToolsClient().delete_svt_images(uuids=[str(uuid.uuid4())], timeout=1)
        assert len(timeout_list) == 1
        assert timeout_list[0] == 1
        assert res is None

    @patch("grpc_client.vmtools.client.VmToolsClient.svt_stub", new_callable=PropertyMock)
    def test_batch_get_svt_images(self, mock_svt_stub):
        timeout_list = []

        def foo(arg, metadata="", timeout=None):
            timeout_list.append(timeout)
            response_json = [
                {
                    "name": "SMTX_VMTOOLS-3.0.2-2304031315",
                    "uuid": "2461085b-6745-4e69-acf7-f6b482293195",
                    "description": "svt_image",
                    "file_name": "2461085b-6745-4e69-acf7-f6b482293195",
                    "path": "/usr/share/smartx/images/vmtools/2461085b-6745-4e69-acf7-f6b482293195",
                    "version": 3000002,
                    "size": 776888320,
                    "create_time": 1680517106,
                    "resource_state": "in-use"
                }
            ]
            return json_format.ParseDict(
                {"svt_images": response_json}, svt_pb2.BatchGetSvtImagesResponse(), ignore_unknown_fields=True
            )

        m = Mock()
        m.BatchGetSvtImages.side_effect = foo
        mock_svt_stub.return_value = m

        res = client.VmToolsClient().batch_get_svt_images("name", timeout=1)
        assert len(timeout_list) == 1
        assert timeout_list[0] == 1
        assert res["svt_images"][0]["name"] == "SMTX_VMTOOLS-3.0.2-2304031315"
        assert res["svt_images"][0]["uuid"] == "2461085b-6745-4e69-acf7-f6b482293195"

    @patch("grpc_client.vmtools.client.VmToolsClient.svt_stub", new_callable=PropertyMock)
    def test_create_svt_image(self, mock_svt_stub):
        timeout_list = []
        res_json = {
            "name": "SMTX_VMTOOLS-3.0.2-2304031315",
            "uuid": "2461085b-6745-4e69-acf7-f6b482293195",
            "path": "/usr/share/smartx/images/vmtools/2461085b-6745-4e69-acf7-f6b482293195",
            "size": 776888320,
            "description": "svt_image",
            "file_name": "2461085b-6745-4e69-acf7-f6b482293195",
            "version": 3000002,
            "create_time": 1680517106,
            "resource_state": "in-use"
        }

        def foo(arg, metadata="", timeout=None):
            timeout_list.append(timeout)
            response_json = res_json
            return json_format.ParseDict(
                response_json, svt_pb2.SvtImage(), ignore_unknown_fields=True
            )

        m = Mock()
        m.CreateSvtImage.side_effect = foo
        mock_svt_stub.return_value = m

        res = client.VmToolsClient().create_svt_image(
            res_json["name"], res_json["uuid"], res_json["path"], res_json["size"], res_json["file_name"], timeout=1
        )
        assert len(timeout_list) == 1
        assert timeout_list[0] == 1
        assert res["name"] == res_json["name"]
        assert res["uuid"] == res_json["uuid"]

    @patch("grpc_client.vmtools.client.VmToolsClient.svt_stub", new_callable=PropertyMock)
    def test_search_macs(self, mock_svt_stub, svt_data):
        timeout_list = []

        def foo(arg, metadata="", timeout=None):
            timeout_list.append(timeout)
            return json_format.ParseDict(
                {
                    "macs": [
                        svt_data["static_info"]["nics"][0]["hardware_address"],
                        svt_data["static_info"]["nics"][1]["hardware_address"],
                    ]
                },
                svt_pb2.SearchMacsResponse(),
                ignore_unknown_fields=True,
            )

        m = Mock()
        m.SearchMacs.side_effect = foo
        mock_svt_stub.return_value = m

        res = client.VmToolsClient().search_macs(["199.196"], timeout=1)
        assert len(timeout_list) == 1
        assert timeout_list[0] == 1
        assert len(res["macs"]) == 2
        assert res["macs"][0] == svt_data["static_info"]["nics"][0]["hardware_address"]
        assert res["macs"][1] == svt_data["static_info"]["nics"][1]["hardware_address"]

    @patch("grpc_client.vmtools.client.VmToolsClient.grpc_channel", new_callable=PropertyMock)
    def test_batch_update_svt_data(self, mock_grpc_channel):
        timeout_list = []
        vm_uuid = "uuid"

        def foo(arg, metadata="", timeout=None):
            timeout_list.append(timeout)
            return json_format.ParseDict(
                {"vm_uuids": [vm_uuid]},
                svt_pb2.BatchUpdateSvtDataRequest(),
                ignore_unknown_fields=True,
            )
        m = Mock()
        m.unary_unary.return_value = foo
        mock_grpc_channel.return_value = m

        res = client.VmToolsClient().batch_update_svt_data([vm_uuid], "token", timeout=1)
        assert len(timeout_list) == 1
        assert timeout_list[0] == 1
        assert res["vm_uuids"][0] == vm_uuid


def test_retry_on_grpc_error():
    res = []

    class TestDecorator:
        @client.retry_on_grpc_error()
        def foo(self):
            res.append(1)

        @client.retry_on_grpc_error()
        def foo_exception(self):
            res.append(2)
            raise grpc.RpcError("foo_exception")

        @client.retry_on_grpc_error(retry_times=2)
        def foo_exception_retry(self):
            res.append(3)
            raise grpc.RpcError("foo_exception_retry")

    TestDecorator().foo()
    assert len(res) == 1
    assert res[0] == 1

    with pytest.raises(grpc.RpcError):
        TestDecorator().foo_exception()
    assert len(res) == 3
    assert res[1] == 2
    assert res[2] == 2

    with pytest.raises(grpc.RpcError):
        TestDecorator().foo_exception_retry()
    assert len(res) == 6
    assert res[3] == 3
    assert res[4] == 3
    assert res[5] == 3
