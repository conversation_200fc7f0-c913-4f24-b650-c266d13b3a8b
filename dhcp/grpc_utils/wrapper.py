# Copyright (c) 2013-2018, SMARTX
# All rights reserved.


from functools import wraps
import itertools
import logging

from grpc import ServicerContext, StatusCode

from dhcp.common.exceptions import DhcpError, ErrorCode

logger = logging.getLogger("dhcp_network_manager")


def server_error_wrapper(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            for v in itertools.chain(args, list(kwargs.values())):
                # https://stackoverflow.com/questions/40998199/raising-a-server-error-to-the-client-with-grpc
                if isinstance(v, ServicerContext):
                    if isinstance(e, DhcpError):
                        logger.info(str(e))
                        v.abort(StatusCode.FAILED_PRECONDITION, "{}: {}".format(e.code[1], str(e)))
                    else:
                        logger.exception("Uncaught error:{}".format(str(e)))
                        v.abort(
                            StatusCode.UNKNOWN,
                            "{}: {}".format(ErrorCode.UNKNOWN[1], e.msg if hasattr(e, "msg") else str(e)),
                        )
            raise

    return wrapper
