# Copyright (c) 2013-2018, SMARTX
# All rights reserved.


import json
import logging

import grpc

from dhcp.common.exceptions import InvalidArgumentError
from dhcp.grpc_utils.wrapper import server_error_wrapper
from google.protobuf import empty_pb2
from google.protobuf.json_format import ParseDict
from smartx_proto.dhcp import dhcps_pb2_grpc
from smartx_proto.dhcp.dhcps_pb2 import BatchGetDhcpsResponse, Dhcp, ListDhcpsResponse
from smartx_proto.dhcp.dhcps_pb2_grpc import DhcpsServicer

logger = logging.getLogger("dhcp_network_manager")


class DhcpsService(DhcpsServicer):
    def __init__(self, dm_instance=None, grpc_server_port=None):
        self.dm_instance = dm_instance
        self.grpc_server_port = grpc_server_port

    def dhcp_message_builder(self, dhcp):
        if dhcp and ("dhcp_lease_duration" in dhcp) and ("seconds" in dhcp["dhcp_lease_duration"]):
            seconds_str = "{}s".format(str(int(dhcp["dhcp_lease_duration"]["seconds"])))
            dhcp.pop("dhcp_lease_duration")
            dhcp["dhcp_lease_duration"] = seconds_str
        return ParseDict(dhcp, Dhcp(), ignore_unknown_fields=True)

    @server_error_wrapper
    def GetDhcp(self, request, context):
        if not self.dm_instance.is_master():
            leader_ip = self.dm_instance.get_dhcp_manager_leader_ip()
            logger.info("Route GetDhcp request to meta leader: {}.".format(leader_ip))
            channel = grpc.insecure_channel("{}:{}".format(leader_ip, self.grpc_server_port))
            stub = dhcps_pb2_grpc.DhcpsStub(channel)
            response = stub.GetDhcp(request)
            return response
        else:
            params = request.name.split("/")
            if len(params) == 2:
                network_uuid = params[1]
                dhcp_info = self.dm_instance.get_dhcp(network_uuid)
                logger.info("GetDhcp retrived {}".format(json.dumps(dhcp_info)))
                if dhcp_info:
                    return self.dhcp_message_builder(dhcp_info)
                else:
                    return Dhcp()
            else:
                msg = "Failed to parse request.name: {} for GetDhcp.".format(request.name)
                logger.error(msg)
                raise InvalidArgumentError(msg)

    @server_error_wrapper
    def CreateDhcp(self, request, context):
        if not self.dm_instance.is_master():
            leader_ip = self.dm_instance.get_dhcp_manager_leader_ip()
            logger.info("Route CreateDhcp request to meta leader: {}.".format(leader_ip))
            channel = grpc.insecure_channel("{}:{}".format(leader_ip, self.grpc_server_port))
            stub = dhcps_pb2_grpc.DhcpsStub(channel)
            response = stub.CreateDhcp(request)
            return response
        else:
            dhcp_info = self.dm_instance.create_dhcp(request)
            if dhcp_info:
                return self.dhcp_message_builder(dhcp_info)
            else:
                return Dhcp()

    @server_error_wrapper
    def ListDhcps(self, request, context):
        if not self.dm_instance.is_master():
            leader_ip = self.dm_instance.get_dhcp_manager_leader_ip()
            logger.info("Route ListDhcps request to meta leader: {}.".format(leader_ip))
            channel = grpc.insecure_channel("{}:{}".format(leader_ip, self.grpc_server_port))
            stub = dhcps_pb2_grpc.DhcpsStub(channel)
            response = stub.ListDhcps(request)
            return response
        else:
            return ListDhcpsResponse(
                dhcps=[self.dhcp_message_builder(dhcp_info) for dhcp_info in self.dm_instance.list_dhcps()]
            )

    @server_error_wrapper
    def BatchGetDhcps(self, request, context):
        if not self.dm_instance.is_master():
            leader_ip = self.dm_instance.get_dhcp_manager_leader_ip()
            logger.info("Route BatchGetDhcps request to meta leader: {}.".format(leader_ip))
            channel = grpc.insecure_channel("{}:{}".format(leader_ip, self.grpc_server_port))
            stub = dhcps_pb2_grpc.DhcpsStub(channel)
            response = stub.BatchGetDhcps(request)
            return response
        else:
            return BatchGetDhcpsResponse(
                dhcps=[
                    self.dhcp_message_builder(dhcp_info)
                    for dhcp_info in self.dm_instance.batch_get_dhcps(request.network_uuids)
                ]
            )

    @server_error_wrapper
    def DeleteDhcp(self, request, context):
        if not self.dm_instance.is_master():
            leader_ip = self.dm_instance.get_dhcp_manager_leader_ip()
            logger.info("Route DeleteDhcp request to meta leader: {}.".format(leader_ip))
            channel = grpc.insecure_channel("{}:{}".format(leader_ip, self.grpc_server_port))
            stub = dhcps_pb2_grpc.DhcpsStub(channel)
            response = stub.DeleteDhcp(request)
            return response
        else:
            params = request.name.split("/")
            if len(params) == 2:
                network_uuid = params[1]
                self.dm_instance.delete_dhcp(network_uuid)
            else:
                msg = "Failed to parse request.name: {} for DeleteDhcp.".format(request.name)
                logger.error(msg)
                raise InvalidArgumentError(msg)
            return empty_pb2.Empty()
