# Copyright (c) 2013-2018, SMARTX
# All rights reserved.


from concurrent import futures
import time

import grpc

from dhcp.grpc_utils.dhcp import DhcpsService
from dhcp.grpc_utils.macipmapping import MacipmappingsService
from smartx_proto.dhcp.dhcps_pb2_grpc import add_DhcpsServicer_to_server
from smartx_proto.dhcp.macipmappings_pb2_grpc import add_MacipmappingsServicer_to_server


def serve(host, port, dm_instance):
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
    add_DhcpsServicer_to_server(DhcpsService(dm_instance, port), server)
    add_MacipmappingsServicer_to_server(MacipmappingsService(dm_instance, port), server)
    server.add_insecure_port("{}:{}".format(host, port))
    server.start()

    try:
        while True:
            time.sleep(86400)
    except KeyboardInterrupt:
        server.stop(0)
