# Copyright (c) 2013-2018, SMARTX
# All rights reserved.


import logging

import grpc

from dhcp.common.exceptions import InvalidArgumentError
from dhcp.grpc_utils.wrapper import server_error_wrapper
from google.protobuf import empty_pb2
from google.protobuf.json_format import ParseDict
from smartx_proto.dhcp import macipmappings_pb2_grpc
from smartx_proto.dhcp.macipmappings_pb2 import (
    BatchGetMacipmappingsResponse,
    FilterAvailableIpsResponse,
    ListMacipmappingsResponse,
    Macipmapping,
    SearchMacipmappingsByIpTextResponse,
)
from smartx_proto.dhcp.macipmappings_pb2_grpc import MacipmappingsServicer

logger = logging.getLogger("dhcp_network_manager")


class MacipmappingsService(MacipmappingsServicer):
    def __init__(self, dm_instance=None, grpc_server_port=None):
        self.dm_instance = dm_instance
        self.grpc_server_port = grpc_server_port

    @server_error_wrapper
    def GetMacipmapping(self, request, context):
        if not self.dm_instance.is_master():
            leader_ip = self.dm_instance.get_dhcp_manager_leader_ip()
            logger.info("Route GetMacip/mapping request to meta leader: {}.".format(leader_ip))
            channel = grpc.insecure_channel("{}:{}".format(leader_ip, self.grpc_server_port))
            stub = macipmappings_pb2_grpc.MacipmappingsStub(channel)
            response = stub.GetMacipmapping(request)
            return response
        else:
            params = request.name.split("/")
            if len(params) == 4:
                network_uuid = params[1]
                mac = params[3]
                ip = self.dm_instance.get_ip_for_nic(mac, network_uuid)
                return Macipmapping(name=request.name, mac=mac, ip=ip)
            else:
                msg = "Failed to parse GetMacipmapping request: {}.".format(request.name)
                logger.error(msg)
                raise InvalidArgumentError(msg)

    @server_error_wrapper
    def ListMacipmappings(self, request, context):
        if not self.dm_instance.is_master():
            leader_ip = self.dm_instance.get_dhcp_manager_leader_ip()
            logger.info("Route ListMacipmappings request to meta leader: {}.".format(leader_ip))
            channel = grpc.insecure_channel("{}:{}".format(leader_ip, self.grpc_server_port))
            stub = macipmappings_pb2_grpc.MacipmappingsStub(channel)
            response = stub.ListMacipmappings(request)
            return response
        else:
            params = request.parent.split("/")
            if len(params) == 2:
                if params[1] == "-":
                    network_uuid = None
                else:
                    network_uuid = params[1]
                dm_req = {}
                if network_uuid:
                    dm_req["network_uuid"] = network_uuid
                elif request.ip:
                    dm_req["ips"] = [request.ip]
                elif request.mac:
                    dm_req["macs"] = [request.mac]
                dm_response = self.dm_instance.list_macipmappings(dm_req)
                listmacipmappings_response = {"macipmappings": dm_response}
                return ParseDict(listmacipmappings_response, ListMacipmappingsResponse(), ignore_unknown_fields=True)
            else:
                msg = "Failed to parse ListMacipmappings request: {}.".format(request.parent)
                logger.error(msg)
                raise InvalidArgumentError(msg)

    @server_error_wrapper
    def CreateMacipmapping(self, request, context):
        if not self.dm_instance.is_master():
            leader_ip = self.dm_instance.get_dhcp_manager_leader_ip()
            logger.info("Route CreateMacipmapping request to meta leader: {}.".format(leader_ip))
            channel = grpc.insecure_channel("{}:{}".format(leader_ip, self.grpc_server_port))
            stub = macipmappings_pb2_grpc.MacipmappingsStub(channel)
            response = stub.CreateMacipmapping(request)
            return response
        else:
            dm_req = {}
            parent = request.parent
            params = parent.split("/")
            if len(params) == 2:
                if params[1] == "-":
                    msg = "Missing network_uuid when calling CreateMacipmapping."
                    logger.error(msg)
                    raise InvalidArgumentError(msg)
                else:
                    dm_req["network_uuid"] = params[1]
                    dm_req["mac"] = request.macipmapping.mac
                    name = "{}/macIps/{}".format(parent, request.macipmapping.mac)
                    dm_req["name"] = name
                    if request.macipmapping.ip:
                        dm_req["ip"] = request.macipmapping.ip
                    dm_response = self.dm_instance.reserve_ip_for_nic(dm_req)
                    if dm_response:
                        if "name" not in dm_response:
                            dm_response["name"] = name
                        return ParseDict(dm_response, Macipmapping(), ignore_unknown_fields=True)
                    else:
                        return Macipmapping()
            else:
                msg = "Failed to parse CreateMacipmapping request: {}.".format(request.parent)
                logger.error(msg)
                raise InvalidArgumentError(msg)

    @server_error_wrapper
    def UpdateMacipmapping(self, request, context):
        if not self.dm_instance.is_master():
            leader_ip = self.dm_instance.get_dhcp_manager_leader_ip()
            logger.info("Route UpdateMacipmapping request to meta leader: {}.".format(leader_ip))
            channel = grpc.insecure_channel("{}:{}".format(leader_ip, self.grpc_server_port))
            stub = macipmappings_pb2_grpc.MacipmappingsStub(channel)
            response = stub.UpdateMacipmapping(request)
            return response
        else:
            dm_req = {}
            params = request.name.split("/")
            if len(params) == 4:
                network_uuid = params[1]
                params[3]
                dm_req["network_uuid"] = network_uuid
                dm_req["mac"] = request.macipmapping.mac
                dm_req["ip"] = request.macipmapping.ip
                dm_req["name"] = request.name
                ip = self.dm_instance.update_ip_for_nic(dm_req)
                if ip:
                    response = {}
                    response["name"] = request.name
                    response["mac"] = dm_req["mac"]
                    response["ip"] = ip
                    return ParseDict(response, Macipmapping(), ignore_unknown_fields=True)
                else:
                    return Macipmapping()
            else:
                msg = "Failed to parse UpdateMacipmapping request: {}.".format(request.name)
                logger.error(msg)
                raise InvalidArgumentError(msg)

    @server_error_wrapper
    def DeleteMacipmapping(self, request, context):
        if not self.dm_instance.is_master():
            leader_ip = self.dm_instance.get_dhcp_manager_leader_ip()
            logger.info("Route DeleteMacipmapping request to meta leader: {}.".format(leader_ip))
            channel = grpc.insecure_channel("{}:{}".format(leader_ip, self.grpc_server_port))
            stub = macipmappings_pb2_grpc.MacipmappingsStub(channel)
            response = stub.DeleteMacipmapping(request)
            return response
        else:
            params = request.name.split("/")
            if len(params) == 4:
                dm_req = {"network_uuid": params[1], "mac": params[3]}
                self.dm_instance.remove_ip_for_nic(dm_req)
                return empty_pb2.Empty()
            else:
                msg = "Failed to parse DeleteMacipmapping request: {}.".format(request.name)
                logger.error(msg)
                raise InvalidArgumentError(msg)

    @server_error_wrapper
    def FilterAvailableIps(self, request, context):
        if not self.dm_instance.is_master():
            leader_ip = self.dm_instance.get_dhcp_manager_leader_ip()
            logger.info("Route FilterAvailableIps request to meta leader: {}.".format(leader_ip))
            channel = grpc.insecure_channel("{}:{}".format(leader_ip, self.grpc_server_port))
            stub = macipmappings_pb2_grpc.MacipmappingsStub(channel)
            response = stub.FilterAvailableIps(request)
            return response
        else:
            params = request.parent.split("/")
            if len(params) == 2:
                if params[1] == "-":
                    network_uuid = None
                else:
                    network_uuid = params[1]
            else:
                msg = "Failed to parse FilterAvailableIps request: {}.".format(request.parent)
                logger.error(msg)
                raise InvalidArgumentError(msg)
            if not network_uuid:
                msg = "Failed to read network_uuid from FilterAvailableIps request: {}.".format(request.parent)
                logger.error(msg)
                raise InvalidArgumentError(msg)
            dm_req = {"network_uuid": network_uuid, "ips": request.ips}
            filter_available_ips_response = self.dm_instance.filter_available_ips(dm_req)
            if filter_available_ips_response and len(filter_available_ips_response) > 0:
                return ParseDict(
                    {"ips": filter_available_ips_response}, FilterAvailableIpsResponse(), ignore_unknown_fields=True
                )
            else:
                return FilterAvailableIpsResponse()

    @server_error_wrapper
    def BatchGetMacipmappings(self, request, context):
        if not self.dm_instance.is_master():
            leader_ip = self.dm_instance.get_dhcp_manager_leader_ip()
            logger.info("Route BatchGetMacipmappings request to meta leader: {}.".format(leader_ip))
            channel = grpc.insecure_channel("{}:{}".format(leader_ip, self.grpc_server_port))
            stub = macipmappings_pb2_grpc.MacipmappingsStub(channel)
            response = stub.BatchGetMacipmappings(request)
            return response
        else:
            parent = request.parent
            params = parent.split("/")
            if len(params) == 2:
                if params[1] == "-":
                    network_uuid = None
                else:
                    network_uuid = params[1]
                dm_req = {}
                if network_uuid:
                    dm_req["network_uuid"] = network_uuid
                dm_req["macs"] = [mac for mac in request.macs]
                batch_get_macipmappings_response = self.dm_instance.batch_get_macipmappings(dm_req)
                if batch_get_macipmappings_response and len(batch_get_macipmappings_response) > 0:
                    """batch get for specified network"""
                    return ParseDict(
                        {"macipmappings": batch_get_macipmappings_response},
                        BatchGetMacipmappingsResponse(),
                        ignore_unknown_fields=True,
                    )
                else:
                    return BatchGetMacipmappingsResponse()
            else:
                msg = "Failed to parse BatchGetMacipmappings request: {}".format(parent)
                logger.error(msg)
                raise InvalidArgumentError(msg)

    @server_error_wrapper
    def SearchMacipmappingsByIpText(self, request, context):
        if not self.dm_instance.is_master():
            leader_ip = self.dm_instance.get_dhcp_manager_leader_ip()
            logger.info("Route SearchMacipmappingsByIpText request to meta leader: {}.".format(leader_ip))
            channel = grpc.insecure_channel("{}:{}".format(leader_ip, self.grpc_server_port))
            stub = macipmappings_pb2_grpc.MacipmappingsStub(channel)
            response = stub.SearchMacipmappingsByIpText(request)
            return response
        else:
            parent = request.parent
            params = parent.split("/")
            if len(params) == 2:
                if params[1] == "-":
                    network_uuid = None
                else:
                    network_uuid = params[1]
                dm_req = {}
                if network_uuid:
                    dm_req["network_uuid"] = network_uuid
                dm_req["ip_text"] = request.ip_text
                search_macipmappings_by_ip_text_response = self.dm_instance.search_mappings_by_ip_text(dm_req)
                return ParseDict(
                    {"macipmappings": search_macipmappings_by_ip_text_response},
                    SearchMacipmappingsByIpTextResponse(),
                    ignore_unknown_fields=True,
                )
            else:
                msg = "Failed to parse SearchMacipmappingsByIpTextRequest request: {}".format(parent)
                logger.error(msg)
                raise InvalidArgumentError(msg)

    @server_error_wrapper
    def HeartbeatMacipmappings(self, request, context):
        if not self.dm_instance.is_master():
            leader_ip = self.dm_instance.get_dhcp_manager_leader_ip()
            logger.info("Route HeartbeatMacipmappings request to meta leader: {}.".format(leader_ip))
            channel = grpc.insecure_channel("{}:{}".format(leader_ip, self.grpc_server_port))
            stub = macipmappings_pb2_grpc.MacipmappingsStub(channel)
            response = stub.HeartbeatMacipmappings(request)
            return response
        else:
            parent = request.parent
            params = parent.split("/")
            if len(params) == 2:
                if params[1] == "-":
                    network_uuid = None
                else:
                    network_uuid = params[1]
                dm_req = {}
                if not network_uuid:
                    msg = "Failed to read network_uuid from HeartbeatMacipmappings request: {}".format(parent)
                    logger.error(msg)
                    raise InvalidArgumentError(msg)
                else:
                    dm_req["network_uuid"] = network_uuid
                    dm_req["macs"] = [mac for mac in request.macs]
                    self.dm_instance.heartbeat_macipmappings(dm_req)
                    return empty_pb2.Empty()
            else:
                msg = "Failed to parse HeartbeatMacipmappings request: {}".format(parent)
                logger.error(msg)
                raise InvalidArgumentError(msg)
