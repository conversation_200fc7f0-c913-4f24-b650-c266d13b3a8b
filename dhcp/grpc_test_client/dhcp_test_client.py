# Copyright (c) 2013-2018, SMARTX
# All rights reserved.


import uuid

import grpc

from dhcp.grpc_utils.proto import dhcps_pb2, dhcps_pb2_grpc


def testGet(network_uuid):
    channel = grpc.insecure_channel("localhost:6767")
    stub = dhcps_pb2_grpc.DhcpsStub(channel)
    name = "dhcps/{}".format(network_uuid)
    response = stub.GetDhcp(dhcps_pb2.GetDhcpRequest(name=name))
    print("testGet received: " + str(response))


def testCreate(network_uuid, vlan_id):
    channel = grpc.insecure_channel("localhost:6767")
    stub = dhcps_pb2_grpc.DhcpsStub(channel)
    ip_ranges = []
    for i in range(0, 3):
        ip_range = dhcps_pb2.Dhcp.IpRange()
        ip_range.start_ip = "172.0.1.{}".format(str(1 + i * 40))
        ip_range.end_ip = "172.0.1.{}".format(str(1 + i * 40 + 29))
        ip_ranges.append(ip_range)
    network = dhcps_pb2.Dhcp()
    network.name = "dhcps/{}".format(network_uuid)
    network.network_uuid = network_uuid
    network.cidr = "*********/24"
    network.vlan_id = vlan_id
    network.ovs_br = "ovsbr-mgt"
    network.dhcp_interface_ip = "**********"
    network.gateway_ip = "*********"
    network.ip_ranges.extend(ip_ranges)
    network.dns_servers = "**********,**********,***********"
    network.dhcp_lease_duration.seconds = 21600
    response = stub.CreateDhcp(dhcps_pb2.CreateDhcpRequest(dhcp=network))
    print("testCreate received: " + str(response))


def testList():
    channel = grpc.insecure_channel("localhost:6767")
    stub = dhcps_pb2_grpc.DhcpsStub(channel)
    response = stub.ListDhcps(dhcps_pb2.ListDhcpsRequest())
    for dhcp in response.dhcps:
        print("testList received: " + str(dhcp))


def testDelete(network_uuid):
    channel = grpc.insecure_channel("localhost:6767")
    stub = dhcps_pb2_grpc.DhcpsStub(channel)
    name = "dhcps/{}".format(network_uuid)
    response = stub.DeleteDhcp(dhcps_pb2.DeleteDhcpRequest(name=name))
    print("testDelete received: " + str(response))


def testBatchGet(network_uuids):
    channel = grpc.insecure_channel("localhost:6767")
    stub = dhcps_pb2_grpc.DhcpsStub(channel)
    response = stub.BatchGetDhcps(dhcps_pb2.BatchGetDhcpsRequest(network_uuids=network_uuids))
    for dhcp in response.dhcps:
        print("testBatchGet received: " + str(dhcp))


def run():
    network_uuids = []
    try:
        for i in range(21, 24):
            network_uuid = str(uuid.uuid4())
            network_uuids.append(network_uuid)
            testCreate(network_uuid, i)
        for network_uuid in network_uuids:
            testGet(network_uuid)
        testList()
        testBatchGet(network_uuids)
    finally:
        for network_uuid in network_uuids:
            testDelete(network_uuid)


if __name__ == "__main__":
    run()
