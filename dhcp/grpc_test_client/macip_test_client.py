# Copyright (c) 2013-2018, SMARTX
# All rights reserved.


import uuid

import grpc

from dhcp.grpc_utils.proto import dhcps_pb2, dhcps_pb2_grpc, macipmappings_pb2, macipmappings_pb2_grpc

network_uuids = []
macs = {}


def createDhcpNetwork(network_uuid, vlan_id):
    channel = grpc.insecure_channel("localhost:6767")
    stub = dhcps_pb2_grpc.DhcpsStub(channel)
    ip_ranges = []
    for i in range(0, 3):
        ip_range = dhcps_pb2.Dhcp.IpRange()
        ip_range.start_ip = "172.0.{}.{}".format(vlan_id, str(1 + i * 40))
        ip_range.end_ip = "172.0.{}.{}".format(vlan_id, str(1 + i * 40 + 29))
        ip_ranges.append(ip_range)
    network = dhcps_pb2.Dhcp()
    network.name = "dhcps/{}".format(network_uuid)
    network.network_uuid = network_uuid
    network.cidr = "172.0.{}.0/24".format(vlan_id)
    network.vlan_id = vlan_id
    network.ovs_br = "ovsbr-mgt"
    network.dhcp_interface_ip = "172.0.{}.19".format(vlan_id)
    network.gateway_ip = "172.0.{}.1".format(vlan_id)
    network.ip_ranges.extend(ip_ranges)
    network.dns_servers = "172.0.{}.21,172.0.{}.84,172.0.{}.37".format(vlan_id, vlan_id, vlan_id)
    network.dhcp_lease_duration.seconds = 21600
    response = stub.CreateDhcp(dhcps_pb2.CreateDhcpRequest(dhcp=network))
    print("testCreate received: " + str(response))


def setupDhcpNetworks():
    global network_uuids
    for i in range(49, 54):
        network_uuid = str(uuid.uuid4())
        network_uuids.append(network_uuid)
        createDhcpNetwork(network_uuid, i)


def cleanupDhcpNetworks():
    channel = grpc.insecure_channel("localhost:6767")
    stub = dhcps_pb2_grpc.DhcpsStub(channel)
    global network_uuids
    for network_uuid in network_uuids:
        name = "dhcps/{}".format(network_uuid)
        stub.DeleteDhcp(dhcps_pb2.DeleteDhcpRequest(name=name))


def testGetMacipmapping(network_uuid, mac):
    channel = grpc.insecure_channel("localhost:6767")
    stub = macipmappings_pb2_grpc.MacipmappingsStub(channel)
    name = "dhcps/{}/macIps/{}".format(network_uuid, mac)
    response = stub.GetMacipmapping(macipmappings_pb2.GetMacipmappingRequest(name=name))
    print("testGetMacipmapping received: " + str(response))


def testListMacipmappings(network_uuid=None, mac=None, ip=None):
    channel = grpc.insecure_channel("localhost:6767")
    stub = macipmappings_pb2_grpc.MacipmappingsStub(channel)
    if network_uuid:
        parent = "dhcps/{}".format(network_uuid)
        response = stub.ListMacipmappings(macipmappings_pb2.ListMacipmappingsRequest(parent=parent))
    else:
        parent = "dhcps/{}".format("-")
        if mac:
            response = stub.ListMacipmappings(macipmappings_pb2.ListMacipmappingsRequest(parent=parent, mac=mac))
        elif ip:
            response = stub.ListMacipmappings(macipmappings_pb2.ListMacipmappingsRequest(parent=parent, ip=ip))
    print("testListMacipmappings received: " + str(response))


def testCreateMacipmapping(network_uuid, mac, ip=None):
    global macs
    channel = grpc.insecure_channel("localhost:6767")
    stub = macipmappings_pb2_grpc.MacipmappingsStub(channel)
    macipmapping = macipmappings_pb2.Macipmapping()
    macipmapping.mac = mac
    if network_uuid in macs:
        macs[network_uuid].append(mac)
    else:
        macs[network_uuid] = [mac]
    if ip:
        macipmapping.ip = ip
    parent = "dhcps/{}".format(network_uuid)
    response = stub.CreateMacipmapping(
        macipmappings_pb2.CreateMacipmappingRequest(parent=parent, macipmapping=macipmapping)
    )
    print("testCreateMacipmapping received: " + str(response))


def testUpdateMacipmapping():
    channel = grpc.insecure_channel("localhost:6767")
    stub = macipmappings_pb2_grpc.MacipmappingsStub(channel)
    macipmapping = macipmappings_pb2.Macipmapping()
    macipmapping.mac = "tc:20:18:19:cd:a6"
    macipmapping.ip = "**********"
    network_uuid = "0096ab8b-1aa4-4ec9-add8-75a2ff7fdfa2"
    name = "dhcps/{}/macIps/{}".format(network_uuid, macipmapping.mac)
    macipmapping.name = name
    response = stub.UpdateMacipmapping(
        macipmappings_pb2.UpdateMacipmappingRequest(name=name, macipmapping=macipmapping)
    )
    print("testUpdateMacipmapping received: " + str(response))


def testDeleteMacipmapping(network_uuid, mac):
    channel = grpc.insecure_channel("localhost:6767")
    stub = macipmappings_pb2_grpc.MacipmappingsStub(channel)
    name = "dhcps/{}/macIps/{}".format(network_uuid, mac)
    response = stub.DeleteMacipmapping(macipmappings_pb2.DeleteMacipmappingRequest(name=name))
    print("testDeleteMacipmapping received: " + str(response))


def testBatchGetMacipmappings(macs, network_uuid=None):
    channel = grpc.insecure_channel("localhost:6767")
    stub = macipmappings_pb2_grpc.MacipmappingsStub(channel)
    parent = "dhcps/{}".format(network_uuid)
    response = stub.BatchGetMacipmappings(macipmappings_pb2.BatchGetMacipmappingsRequest(parent=parent, macs=macs))
    print("testBatchGetMacipmappings: " + str(response))


def testFilterAvailableIps(network_uuid):
    channel = grpc.insecure_channel("localhost:6767")
    stub = macipmappings_pb2_grpc.MacipmappingsStub(channel)
    parent = "dhcps/{}".format(network_uuid)
    ips = []
    for i in range(109, 139):
        ips.append("172.0.49.{}".format(i))
    response = stub.FilterAvailableIps(macipmappings_pb2.FilterAvailableIpsRequest(parent=parent, ips=ips))
    print("testFilterAvailableIps: " + str(response))


def testSearchMacipmappingsByIpText(ip_text):
    channel = grpc.insecure_channel("localhost:6767")
    stub = macipmappings_pb2_grpc.MacipmappingsStub(channel)
    parent = "dhcps/{}".format("-")
    response = stub.SearchMacipmappingsByIpText(
        macipmappings_pb2.SearchMacipmappingsByIpTextRequest(parent=parent, ip_text=ip_text)
    )
    print("testSearchMacipmappingsByIpText: " + str(response))


def testHeartbeatMacipmappings(network_uuid, macs):
    channel = grpc.insecure_channel("localhost:6767")
    stub = macipmappings_pb2_grpc.MacipmappingsStub(channel)
    parent = "dhcps/{}".format(network_uuid)
    response = stub.HeartbeatMacipmappings(macipmappings_pb2.HeartbeatMacipmappingsRequest(parent=parent, macs=macs))
    print("testHeartbeatMacipmappings: " + str(response))


def run():
    global macs
    global network_uuids
    setupDhcpNetworks()
    try:
        for network_uuid in network_uuids:
            for i in range(0, 5):
                mac = str(uuid.uuid4())
                testCreateMacipmapping(network_uuid, mac)
        for network_uuid in network_uuids:
            if network_uuid in macs:
                for mac in macs[network_uuid]:
                    testGetMacipmapping(network_uuid, mac)
        for network_uuid in network_uuids:
            testListMacipmappings(network_uuid)
        for network_uuid in network_uuids:
            if network_uuid in macs:
                testBatchGetMacipmappings(macs[network_uuid])
                testHeartbeatMacipmappings(network_uuid, macs[network_uuid])
        # testUpdateMacipmapping()
        for network_uuid in network_uuids:
            if network_uuid in macs:
                for mac in macs[network_uuid]:
                    testDeleteMacipmapping(network_uuid, mac)
        network_uuid = network_uuids[0]
        testFilterAvailableIps(network_uuid)
    finally:
        cleanupDhcpNetworks()


if __name__ == "__main__":
    run()
