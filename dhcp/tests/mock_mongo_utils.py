# Copyright (c) 2013-2018, SMARTX
# All rights reserved.

# data structure for mock mongodb table
'''{network_uuid: {'network_uuid':1231, 'ip_pool':'*********', '*********'...}, ...}'''
ip_pools = {}
'''{network_uuid: {mac_1:ip_1, mac_2:ip_2, mac_3:ip_3, ...}, ...}'''
mac_to_ip_mappings = {}

dhcp_networks = {}

class NetworksDAO:
    def __init__(self, db, collection):
        self.__db = db
        self.__collection = collection

    def get_last_heartbeat_timestamp(self, network_uuid):
        print('Call get_last_heartbeat_timestamp from mock NetworksDAO object'
              ' with network_uuid: {}'.format(network_uuid))
        global dhcp_networks
        network = dhcp_networks.get(network_uuid, None)
        if network:
            return network.get('heartbeat_timestamp', None)
        else:
            return None

    def get_network_by_id(self, network_uuid):
        print('Call get_network_by_id from mock NetworksDAO object'
              ' with network_uuid: {}'.format(network_uuid))
        global dhcp_networks
        return dhcp_networks.get(network_uuid, None)

    def create_new_network(self, network):
        print('Call create_new_network from mock NetworksDAO object.')
        global dhcp_networks
        network_uuid = network.get('network_uuid', None)
        if network_uuid:
            dhcp_networks[network_uuid] = network

    def delete_network_by_id(self, network_uuid):
        print('Call delete_network_by_id from mock NetworksDAO object'
              ' with network_uuid: {}'.format(network_uuid))
        global dhcp_networks
        return dhcp_networks.pop(network_uuid, 0)

class IpPoolsDAO:
    def __init__(self, db, collection):
        self.__db = db
        self.__collection = collection

    def get_available_ips_for_network(self, network_uuid):
        print('Call get_available_ips_for_network from mock IpPoolsTable instance.')
        global ip_pools
        if network_uuid in ip_pools:
            return ip_pools[network_uuid]['ip_pool']
        else:
            return None

    def create_ip_pool_for_network(self, network_uuid, doc):
        print('Call create_ip_pool_for_network from mock IpPoolsTable instance.')
        global ip_pools
        ip_pools[network_uuid] = doc

    def update_ip_pool_for_network(self, network_uuid, doc):
        print('Call update_ip_pool_for_network from mock IpPoolsTable instance.')
        self.create_ip_pool_for_network(network_uuid, doc)
        return 1

    def remove_ip_pool_for_network(self, network_uuid):
        global ip_pools
        return ip_pools.pop(network_uuid, 0)

class MacIpMappingsDAO:
    def __init__(self, db, collection):
        self.__db = db
        self.__collection = collection

    def drop_allocations_for_network(self, doc):
        print('Call drop_allocations_for_network from mock MacIpMappingsTable instance.')
        global ip_pools
        if self.__collection in ip_pools:
            del ip_pools[self.__collection]

    def persist_macipmapping(self, mac, ip, name):
        print('Call persist_macipmapping from mock MacIpMappingsTable instance.')
        global mac_to_ip_mappings
        if self.__collection in mac_to_ip_mappings:
            mac_to_ip_mappings[self.__collection][mac] = ip
        else:
            mac_to_ip_mappings[self.__collection] = {mac:ip}
        return True

    def get_ip_by_mac_addr(self, mac):
        print('Call get_ip_by_mac_addr from mock MacIpMappingsTable instance.')
        global mac_to_ip_mappings
        if self.__collection in mac_to_ip_mappings and mac in mac_to_ip_mappings[self.__collection]:
            return mac_to_ip_mappings[self.__collection][mac]

    def remove_ip_for_nic(self, mac):
        print('Call remove_ip_for_nic from mock MacIpMappingsTable instance.')
        global mac_to_ip_mappings
        if self.__collection in mac_to_ip_mappings and mac in mac_to_ip_mappings[self.__collection]:
            del mac_to_ip_mappings[self.__collection][mac]
            return True
        return False
