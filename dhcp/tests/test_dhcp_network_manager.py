# Copyright (c) 2013-2018, SMARTX
# All rights reserved.

import copy
import pytest
import time
import uuid
from unittest.mock import (
    call,
    Mock,
    patch
)
from netaddr import (
    IPAddress,
    IPNetwork
)
from dhcp.tests import mock_mongo_utils
from dhcp.dhcp_manager_utils.dhcp_network_manager import DhcpNetworkManager

def create_dhcp_network(vlan_id, heartbeat_timestamp):
    network = {}
    ip_ranges = []
    ip_ranges.append({'start_ip': IPAddress('172.0.{}.1'.format(vlan_id)),
                      'end_ip': IPAddress('172.0.{}.88'.format(vlan_id))})
    ip_ranges.append({'start_ip': IPAddress('172.0.{}.98'.format(vlan_id)),
                      'end_ip': IPAddress('172.0.{}.128'.format(vlan_id))})
    ip_ranges.append({'start_ip': IPAddress('172.0.{}.168'.format(vlan_id)),
                      'end_ip': IPAddress('172.0.{}.255'.format(vlan_id))})
    network['ip_ranges'] = ip_ranges
    network['cidr'] = IPNetwork('172.0.{}.0/24'.format(vlan_id))
    network['dhcp_interface_ip'] = '172.0.{}.5'.format(vlan_id)
    network['gateway_ip'] = '172.0.{}.1'.format(vlan_id)
    network['ovs_br'] = 'ovs_test_br'
    network['heartbeat_timestamp'] = heartbeat_timestamp
    network['network_uuid'] = str(uuid.uuid4())
    return network

@pytest.fixture
@patch('dhcp.dhcp_manager_utils.dhcp_network_manager.networks_dao', mock_mongo_utils)
@patch('dhcp.dhcp_manager_utils.dhcp_network_manager.ip_pools_dao', mock_mongo_utils)
def create_clean_dm_instance():
    with patch(
        'dhcp.dhcp_manager_utils.dhcp_network_manager.file_utils.ensure_tree',
        autospec=True
    ) as mock_ensure_tree, patch.object(
        DhcpNetworkManager,
        'register_exit_handler',
        autospec=True
    ) as mock_register_exit_handler:
        mock_ensure_tree.return_value = True
        mock_register_exit_handler.return_value = True
        dm = DhcpNetworkManager()
        return dm

def test_reclaim_dangle_ips(create_clean_dm_instance):
    dm = create_clean_dm_instance
    ''' if elf heartbeat does not occur for more than 24h,
        do not reclaim ip for that network. '''
    network_1 = create_dhcp_network(0, int(time.time()) - 90000)
    mock_mongo_utils.dhcp_networks[network_1.get('network_uuid')] = network_1
    mock_dnsmasq = Mock()
    mock_dnsmasq.reclaim_dangle_ips.return_value = True
    dm.uuid_to_driver_mapping[network_1.get('network_uuid')] = mock_dnsmasq
    dm.reclaim_dangle_ips()
    assert not mock_dnsmasq.reclaim_dangle_ips.called
    ''' if elf heartbeat occurs for less than 24h,
        run reclaim ip for that network. '''
    dm.uuid_to_driver_mapping.clear()
    network_2 = create_dhcp_network(0, int(time.time()) - 900)
    mock_mongo_utils.dhcp_networks[network_2.get('network_uuid')] = network_2
    dm.uuid_to_driver_mapping[network_2.get('network_uuid')] = mock_dnsmasq
    dm.reclaim_dangle_ips()
    assert mock_dnsmasq.reclaim_dangle_ips.called

def test_disable_dhcp_for_network(create_clean_dm_instance):
    dm = create_clean_dm_instance
    network = create_dhcp_network(0, int(time.time()))
    mock_mongo_utils.dhcp_networks[network.get('network_uuid')] = network
    mock_dnsmasq = Mock()
    mock_dnsmasq.disable.return_value = True
    dm.uuid_to_driver_mapping[network.get('network_uuid')] = mock_dnsmasq
    dm.disable_dhcp_for_network(network.get('network_uuid'))
    assert mock_dnsmasq.disable.called
    assert network.get('network_uuid') not in dm.uuid_to_driver_mapping

def test_remove_network_from_data_source(create_clean_dm_instance):
    dm = create_clean_dm_instance
    network = create_dhcp_network(0, int(time.time()))
    mock_mongo_utils.dhcp_networks[network.get('network_uuid')] = network
    mock_dnsmasq = Mock()
    mock_dnsmasq.drop_allocations_for_network.return_value = True
    dm.uuid_to_driver_mapping[network.get('network_uuid')] = mock_dnsmasq
    dm.remove_network_from_data_source(network.get('network_uuid'))
    assert mock_dnsmasq.drop_allocations_for_network.called
    assert network.get('network_uuid') not in mock_mongo_utils.dhcp_networks

def test_get_network_uuid_from_cmdline(create_clean_dm_instance):
    dm = create_clean_dm_instance
    cmd_1 = ('dnsmasq --no-hosts --no-resolv --strict-order --except-interface=lo '
             '--pid-file=/var/dhcp_manager/ad1f071d-55ba-48d1-944c-90263a5d34e6/pid '
             '--dhcp-hostsfile=/var/dhcp_manager/ad1f071d-55ba-48d1-944c-90263a5d34e6/host '
             '--dhcp-optsfile=/var/dhcp_manager/ad1f071d-55ba-48d1-944c-90263a5d34e6/opts '
             '--dhcp-leasefile=/var/dhcp_manager/ad1f071d-55ba-48d1-944c-90263a5d34e6/leases '
             '--bind-interfaces --interface=dhcp_v_1_0 --dhcp-range=*************,static,*************,infinite '
             '--dhcp-option=3,************* --server=*************** '
             '--conf-file=/var/dhcp_manager/ad1f071d-55ba-48d1-944c-90263a5d34e6/dnsmasq.conf')
    cmd_sections = cmd_1.split(' ')
    network_uuid, pid_file = dm._get_network_uuid_from_cmdline(cmd_sections)
    assert network_uuid == 'ad1f071d-55ba-48d1-944c-90263a5d34e6'
    assert pid_file == '/var/dhcp_manager/ad1f071d-55ba-48d1-944c-90263a5d34e6/pid'
    cmd_2 = ('/sbin/dnsmasq --conf-file=/var/lib/libvirt/dnsmasq/default.conf '
             '--leasefile-ro --dhcp-script=/usr/libexec/libvirt_leaseshelper')
    cmd_sections = cmd_2.split(' ')
    network_uuid, pid_file = dm._get_network_uuid_from_cmdline(cmd_sections)
    assert network_uuid == None
    assert pid_file == None

def test_configure_dhcp_for_network(create_clean_dm_instance):
    dm = create_clean_dm_instance
    with patch(
            'dhcp.dhcp_manager_utils.dhcp_network_manager.dnsmasq.Dnsmasq',
            Mock):
        network = create_dhcp_network(0, int(time.time()) - 90000)
        mock_mongo_utils.dhcp_networks[network.get('network_uuid')] = network
        dm.configure_dhcp_for_network(network.get('network_uuid'))
        assert network.get('network_uuid') in dm.uuid_to_network_mapping
        assert network.get('network_uuid') in dm.uuid_to_driver_mapping
        assert dm.uuid_to_driver_mapping[network.get('network_uuid')].enable.called
        '''same network, configure again, reload_allocations should be called'''
        dm.configure_dhcp_for_network(network.get('network_uuid'))
        assert network.get('network_uuid') in dm.uuid_to_network_mapping
        assert network.get('network_uuid') in dm.uuid_to_driver_mapping
        assert dm.uuid_to_driver_mapping[network.get('network_uuid')].reload_allocations.called

def test_delete_stale_namespaces(create_clean_dm_instance):
    dm = create_clean_dm_instance
    with patch.object(DhcpNetworkManager, '_get_netns_from_data_plane'
    ) as mock_get_netns_from_data_plane:
        mock_get_netns_from_data_plane.return_value=[
            'dhcp_ns_aaaaa',
            'dhcp_ns_bbbbb',
            'dhcp_ns_ccccc',
            'dhcp_ns_ddddd'
        ]
        dm.managed_networks_uuids.extend(['aaaaa', 'bbbbb'])
        with patch('dhcp.dhcp_manager_utils.dhcp_network_manager.network_utils.'
                   'IpNetnsCommand.remove_network_namespace'
            ) as mock_remove_network_namespace:
            dm.delete_stale_namespaces()
            calls = [call('dhcp_ns_ccccc'), call('dhcp_ns_ddddd')]
            mock_remove_network_namespace.assert_has_calls(calls)

def test_search_mappings_by_ip_text(create_clean_dm_instance):
    dm = create_clean_dm_instance
    with patch.object(DhcpNetworkManager, '_ensure_ready_for_api_calls') as mock_ensure_ready:
        network = create_dhcp_network(0, int(time.time()))
        mock_mongo_utils.dhcp_networks[network.get('network_uuid')] = network
        mock_dnsmasq = Mock()
        dm.uuid_to_driver_mapping[network.get('network_uuid')] = mock_dnsmasq
        req = {
            "ip_text": "*************",
            "network_uuid": "aabbccdd"
        }
        with pytest.raises(Exception):
            dm.search_mappings_by_ip_text(req)
