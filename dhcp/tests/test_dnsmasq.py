# Copyright (c) 2013-2018, SMARTX
# All rights reserved.

import copy
import pytest
import time
import uuid
from unittest.mock import (
    call,
    Mock,
    patch
)
from netaddr import (
    IPAddress,
    IPNetwork
)
from dhcp.tests import mock_mongo_utils
from dhcp.dnsmasq_utils.dnsmasq import Dnsmasq

def create_dhcp_network(vlan_id, heartbeat_timestamp):
    network = {}
    ip_ranges = []
    ip_ranges.append({'start_ip': IPAddress('172.0.{}.1'.format(vlan_id)),
                      'end_ip': IPAddress('172.0.{}.88'.format(vlan_id))})
    ip_ranges.append({'start_ip': IPAddress('172.0.{}.98'.format(vlan_id)),
                      'end_ip': IPAddress('172.0.{}.128'.format(vlan_id))})
    ip_ranges.append({'start_ip': IPAddress('172.0.{}.168'.format(vlan_id)),
                      'end_ip': IPAddress('172.0.{}.255'.format(vlan_id))})
    network['ip_ranges'] = ip_ranges
    network['cidr'] = IPNetwork('172.0.{}.0/24'.format(vlan_id))
    network['dhcp_interface_ip'] = '172.0.{}.5'.format(vlan_id)
    network['gateway_ip'] = '172.0.{}.1'.format(vlan_id)
    network['ovs_br'] = 'ovs_test_br'
    network['heartbeat_timestamp'] = heartbeat_timestamp
    network['network_uuid'] = str(uuid.uuid4())
    network['dns_servers'] = '172.0.{}.254,172.1.{}.254'.format(vlan_id, vlan_id)
    return network

@pytest.fixture
@patch('dhcp.dnsmasq_utils.dnsmasq.ipam.IpAddrAllocator')
@patch('dhcp.dnsmasq_utils.dnsmasq.device_manager.DeviceManager')
def create_clean_dnsmasq_instance(mock_IpAddrAllocator, mock_DeviceManager):
    mock_DeviceManager.setup_device_for_dnsmasq.return_value = True
    mock_DeviceManager.get_dnsmasq_interface.return_value = 'test_eth0'
    with patch(
        'dhcp.dnsmasq_utils.dnsmasq.file_utils.ensure_tree',
        autospec=True
    ) as mock_ensure_tree:
        mock_ensure_tree.return_value = True
        network = create_dhcp_network(0, int(time.time()))
        ip_pools_dao = mock_mongo_utils.IpPoolsDAO('test_db', 'test_collection')
        dnsmasq = Dnsmasq(network, '/fake/dir', ip_pools_dao)
        return dnsmasq

def test_enable(create_clean_dnsmasq_instance):
    dnsmasq = create_clean_dnsmasq_instance
    with patch.object(Dnsmasq, '_spawn_or_reload_process') as mock_spawn_or_reload_process:
        dnsmasq.enable()
        assert mock_spawn_or_reload_process.is_called
        assert dnsmasq.device_manager.setup_device_for_dnsmasq.is_called

def test_build_cmdline_callback(create_clean_dnsmasq_instance):
    dnsmasq = create_clean_dnsmasq_instance
    dnsmasq.interface = 'test_eth0'
    cmd = dnsmasq._build_cmdline_callback('/var/test/dhcp_network_mgr/pid')
    for section in cmd:
        if section.startswith('--pid-file='):
            assert section[len('--pid-file='):] == '/var/test/dhcp_network_mgr/pid'
        elif section.startswith('--dhcp-hostsfile='):
            assert section[len('--dhcp-hostsfile='):] == '/fake/dir/host'
        elif section.startswith('--interface='):
            assert section[len('--interface='):] == 'test_eth0'
        elif section.startswith('--dhcp-range='):
            assert section[len('--dhcp-range='):] == '*********,static,*************,infinite'
        elif section.startswith('--dhcp-option=3,'):
            assert section[len('--dhcp-option=3,'):] == '*********'
        elif section.startswith('--dhcp-option=6,'):
            assert section[len('--dhcp-option=6,'):] == '***********,***********'
        elif section.startswith('--conf-file='):
            assert section[len('--conf-file='):] == '/fake/dir/dnsmasq.conf'
