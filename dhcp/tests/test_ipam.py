# Copyright (c) 2013-2018, SMARTX
# All rights reserved.

import copy
import pytest
import time
import uuid
from unittest.mock import patch
from netaddr import (
    IPAddress,
    IPNetwork
)

from dhcp.tests import mock_mongo_utils
from dhcp.ipam.ipam import IpAddrAllocator

def create_network_for_test(cidr=None, ip_ranges=None, vlan_id=None,
                             dhcp_interface_ip=None, gateway_ip=None):
    network = {}
    network_uuid = str(uuid.uuid4())
    network['network_uuid'] = network_uuid
    network['ip_ranges'] = ip_ranges
    network['cidr'] = cidr
    network['vlan_id'] =  vlan_id
    network['dhcp_interface_ip'] = str(dhcp_interface_ip)
    network['gateway_ip'] = str(gateway_ip)
    return network

@pytest.fixture(scope="module")
def setup_network():
    ip_ranges = []
    ip_ranges.append({'start_ip': IPAddress('*********'), 'end_ip': IPAddress('**********')})
    ip_ranges.append({'start_ip': IPAddress('**********'), 'end_ip': IPAddress('*********28')})
    ip_ranges.append({'start_ip': IPAddress('*********68'), 'end_ip': IPAddress('***********')})
    #start_ip = '*********'
    #end_ip = '***********'
    cidr = IPNetwork('*********/24')
    vlan_id = 20
    dhcp_interface_ip = '*********'
    gateway_ip = '*********'
    return create_network_for_test(cidr=cidr, ip_ranges=ip_ranges, vlan_id=vlan_id,
                    dhcp_interface_ip=dhcp_interface_ip, gateway_ip=gateway_ip)


@patch('dhcp.ipam.ipam.mac_ip_mappings_dao', mock_mongo_utils)
def test_initialize_ip_alloc_pool_for_network(setup_network):
    network = setup_network
    exclude_ip_list=['*********0', '*********1', '*********2', '**********']
    ip_pools_dao = mock_mongo_utils.IpPoolsDAO('dhcp', 'ip_pools')
    ip_addr_allocator = IpAddrAllocator(network=network, exclude_ip_list=exclude_ip_list,
                                                              ip_pools_dao=ip_pools_dao)
    ip_pool = ip_addr_allocator.get_ip_pool()
    exclude_ip_list.append(ip_addr_allocator.network['dhcp_interface_ip'])
    exclude_ip_list.append(ip_addr_allocator.network['gateway_ip'])
    for ip in exclude_ip_list:
        '''ip_addrs from exclude_ip_list should be excluded from alloc pool'''
        assert ip not in ip_pool
    for ip_range in network['ip_ranges']:
        start_ip = ip_range['start_ip']
        end_ip = ip_range['end_ip']
        for ip in range(start_ip, end_ip):
            ip_str = str(IPAddress(ip))
            '''Everything else should be up for allocation'''
            if ip_str not in exclude_ip_list:
                assert ip_str in ip_pool
        '''end_ip should also be in the list'''
        end_ip_str = str(IPAddress(end_ip))
        if end_ip_str not in exclude_ip_list:
            assert end_ip_str in ip_pool
    '''dhcp_interface_ip and gateway_ip should not be in the pool'''
    assert network['dhcp_interface_ip'] not in ip_pool
    assert network['gateway_ip'] not in ip_pool

    def is_ip_in_ip_ranges(ip, network):
        ip_ranges = network['ip_ranges']
        for ip_range in ip_ranges:
            if ip >= ip_range['start_ip'] and ip <= ip_range['end_ip']:
                return True
        return False
    '''oob ip addresses should not be included in the pool'''
    cidr = network['cidr']
    for ip in cidr:
        if not is_ip_in_ip_ranges(ip, network):
            assert str(ip) not in ip_pool

@patch('dhcp.ipam.ipam.mac_ip_mappings_dao', mock_mongo_utils)
def test_ip_allocation_and_recycle(setup_network):
    '''ip allocation'''
    network = setup_network
    exclude_ip_list=['*********0', '*********1', '*********2', '**********']
    ip_pools_dao = mock_mongo_utils.IpPoolsDAO('dhcp', 'ip_pools')
    ip_addr_allocator = IpAddrAllocator(network=network, exclude_ip_list=exclude_ip_list,
                                                             ip_pools_dao=ip_pools_dao)
    ip_pool = ip_addr_allocator.get_ip_pool()
    ip_pool_copy = copy.deepcopy(ip_pool)
    for item in ip_pool_copy:
        ip = ip_addr_allocator.get_next_ip_from_pool()
        '''allocated ip_addr should be predictable'''
        assert item == ip
    '''up to here, should be no more ip for allocation'''
    assert len(ip_pool) == 0
    '''ip recycle'''
    for ip in ip_pool_copy:
        ip_addr_allocator.return_free_ip_to_pool(ip)
        '''returned ip should always get appended to the end of the list'''
        assert ip_pool[-1] == ip

@patch('dhcp.ipam.ipam.mac_ip_mappings_dao', mock_mongo_utils)
def test_ip_assignment_for_nic(setup_network):
    network = setup_network
    exclude_ip_list=['*********0', '*********1', '*********2', '**********']
    ip_pools_dao = mock_mongo_utils.IpPoolsDAO('dhcp', 'ip_pools')
    ip_addr_allocator = IpAddrAllocator(network=network, exclude_ip_list=exclude_ip_list,
                                                             ip_pools_dao=ip_pools_dao)
    '''reserve ip for nic'''
    for i in range(0,50):
        mac = '02:42:79:07:0a:a3' + str(i)
        data = {'network_uuid' : network['network_uuid'],
                'mac' : mac}
        ip = ip_addr_allocator.reserve_ip_for_nic(data)
    mac_to_ip_mappings = mock_mongo_utils.mac_to_ip_mappings
    ip_pool = ip_addr_allocator.get_ip_pool()
    allocations = mac_to_ip_mappings[network['network_uuid']]
    for ip in list(allocations.values()):
        '''allocated ip_addrs should not be available for allocation'''
        assert ip not in ip_pool

    '''get ip for nic'''
    for i in range(0,50):
        mac = '02:42:79:07:0a:a3' + str(i)
        ip = ip_addr_allocator.get_ip_for_nic(mac)
        '''what we get should be what we reserved'''
        assert ip == mac_to_ip_mappings[network['network_uuid']][mac]
    for i in range(51,100):
        mac = '02:42:79:07:0a:a3' + str(i)
        ip = ip_addr_allocator.get_ip_for_nic(mac)
        '''have not reserved for these mac yet'''
        assert ip is None

    '''test update ip for nic'''
    ip_pool_copy = copy.deepcopy(ip_pool)
    for i in range(0, 50):
        mac = '02:42:79:07:0a:a3' + str(i)
        ip = ip_addr_allocator.get_ip_for_nic(mac)
        data = {'network_uuid' : network['network_uuid'],
                'mac' : mac, 'name' : mac}
        new_ip = ip_pool_copy[i]   # the ip to be used
        data['ip'] = new_ip
        ip_addr_allocator.update_ip_for_nic(data)
        cur_ip = ip_addr_allocator.get_ip_for_nic(mac)
        assert cur_ip == new_ip
        assert new_ip not in ip_pool
        assert ip in ip_pool

    '''test remove ip for nic'''
    for i in range(0, 50):
        mac = '02:42:79:07:0a:a3' + str(i)
        data = {'network_uuid' : network['network_uuid'],
                'mac' : mac}
        ip = ip_addr_allocator.remove_ip_for_nic(data)
        assert ip in ip_pool
        assert ip_addr_allocator.get_ip_for_nic(mac) == None
