# Copyright (c) 2013-2018, SMARTX
# All rights reserved.

import argparse
import configparser
import logging
import logging.handlers
import os

from common.config.constant import ZBS_CONFIG_FILE
from common.lib.cfg import Config
from dhcp.dhcp_manager_utils import dhcp_network_manager
from dhcp.grpc_utils import server


def set_up_logger(log_dir, logger_name, level=None, capacity=102400, formatter=None, encoding=None):
    logger = logging.getLogger(logger_name)
    if not log_dir:
        raise Exception("Failed to configure log, no log_dir {}, logger_name: {}".format(log_dir, logger_name))
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    full_file_name = os.path.join(log_dir, logger_name)
    """Each log file is 10mb in size, keep 8 in total."""
    hdlr = logging.handlers.RotatingFileHandler(
        full_file_name, maxBytes=10 * 1024 * 1024, backupCount=8, encoding=encoding
    )

    if not formatter:
        formatter = logging.Formatter("%(asctime)s %(process)d %(thread)d %(name)s %(levelname)s %(message)s")
    hdlr.setFormatter(formatter)
    if level:
        log_level = level
    else:
        log_level = logging.INFO
    mhdlr = logging.handlers.MemoryHandler(capacity, log_level, hdlr)
    logger.addHandler(mhdlr)
    logger.setLevel(log_level)


def load_config(path):
    cfg = configparser.ConfigParser()
    if os.path.isfile(path):
        cfg.read(path)
    else:
        raise ValueError("Config file: {} does not exist!".format(path))
    return cfg


def run_dhcp_service():
    parser = argparse.ArgumentParser()
    parser.add_argument("-C", dest="config_path", action="store", default="", help="Path to configuration file.")
    results = parser.parse_args()
    cfg_path = results.config_path.strip()
    if not cfg_path or cfg_path == "" or (not os.path.isfile(cfg_path)):
        cur_path = os.path.dirname(os.path.realpath(__file__))
        cfg_path = os.path.join(cur_path, "config/config.ini")
    cfg = load_config(cfg_path)

    log_path = "/var/log/zbs"
    log_name = "dhcp_network_manager"
    if ("LOGGER" in cfg.sections()) and ("working_path" in cfg.options("LOGGER")):
        log_path = cfg.get("LOGGER", "working_path")
    if ("LOGGER" in cfg.sections()) and ("name" in cfg.options("LOGGER")):
        log_name = cfg.get("LOGGER", "name")
    set_up_logger(log_path, log_name, level=logging.DEBUG)
    logger = logging.getLogger(log_name)

    macipmapping_max_timeout = elf_heartbeat_max_interval = 86400
    if "DHCP_NETWORK_MGR" in cfg.sections():
        if "macipmapping_max_timeout" in cfg.options("DHCP_NETWORK_MGR"):
            macipmapping_max_timeout = int(cfg.get("DHCP_NETWORK_MGR", "macipmapping_max_timeout").strip())
        if "elf_heartbeat_max_interval" in cfg.options("DHCP_NETWORK_MGR"):
            elf_heartbeat_max_interval = int(cfg.get("DHCP_NETWORK_MGR", "elf_heartbeat_max_interval").strip())
        logger.info(
            "macipmapping_max_timeout: {}, elf_heartbeat_max_interval: {}".format(
                macipmapping_max_timeout, elf_heartbeat_max_interval
            )
        )
    dm = dhcp_network_manager.DhcpNetworkManager(
        macipmapping_max_timeout=macipmapping_max_timeout, elf_heartbeat_max_interval=elf_heartbeat_max_interval
    )
    logger.info("start dhcp_network_manager")
    dm.run()
    grpc_server_host = grpc_server_port = None
    zbs_cfg = Config(ZBS_CONFIG_FILE)
    local_data_ip = zbs_cfg.get_local_data_ip()
    if local_data_ip:
        grpc_server_host = local_data_ip[0]
    if ("GRPC_SERVER" in cfg.sections()) and ("port" in cfg.options("GRPC_SERVER")):
        grpc_server_port = cfg.getint("GRPC_SERVER", "port")
    logger.info("Running grpc server on host: {}, port: {}".format(grpc_server_host, grpc_server_port))
    server.serve(grpc_server_host, grpc_server_port, dm)


if __name__ == "__main__":
    run_dhcp_service()
