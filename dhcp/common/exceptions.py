# Copyright (c) 2013-2018, SMARTX
# All rights reserved.


class ErrorCode:
    """The error codes holder."""

    OK = 0
    UNKNOWN = (9999, "UNKNOWN")
    FORM_ERROR = (9998, "FORM_ERROR")
    NO_SERVICE_FOUND = (9997, "NO_SERVICE_FOUND")
    INTERNAL = (9996, "UNKNOWN")
    UNKNOWN_TYPE = (1, "UNKNOWN_TYPE")
    NOT_FOUND_RESOURCE = (2, "NOT_FOUND_RESOURCE")
    NOT_SUPPORT_OPERATION = (3, "NOT_SUPPORT_OPERATION")
    INVALID_ARGUMENT = (5, "INVALID_ARGUMENT")
    NO_AVAILABLE_RESOURCE = (6, "NO_AVAILABLE_RESOURCE")
    PROCESS_EXECUTION = (7, "PROCESS_EXECUTION")


class DhcpError(Exception):
    _code = ErrorCode.UNKNOWN

    def __init__(self, msg, code=None, detail=None):
        super().__init__(msg)
        self.code = code or self._code
        self.detail = detail


class NotFoundResourceError(DhcpError):
    """Raise when the resource is not found."""

    _code = ErrorCode.NOT_FOUND_RESOURCE


class NoResourceAvailableError(DhcpError):
    """Raise when there is no available resource."""

    _code = ErrorCode.NO_AVAILABLE_RESOURCE


class InvalidArgumentError(DhcpError):
    """Raise when the service handle an invalid argument."""

    _code = ErrorCode.INVALID_ARGUMENT


class InternalError(DhcpError):
    """Raise when the service handle an invalid argument."""

    _code = ErrorCode.INTERNAL


class ProcessExecutionError(DhcpError):
    def __init__(self, msg, returncode):
        super().__init__(msg=msg, code=ErrorCode.PROCESS_EXECUTION, detail=returncode)
