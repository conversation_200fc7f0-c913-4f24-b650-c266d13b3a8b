# Copyright (c) 2013-2018, SMARTX
# All rights reserved.

import errno
import logging
import shlex
import subprocess
import time

import pyroute2
from pyroute2 import IPRoute, netns

from dhcp.common.exceptions import ProcessExecutionError

logger = logging.getLogger("dhcp_network_manager")


def create_subprocess(cmd, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=False):
    if isinstance(cmd, str):
        args = shlex.split(cmd)
        logger.info("Create process with cmd: {}.".format(cmd))
    else:
        args = cmd
        logger.info("Create process with cmd: {}.".format(",".join(arg for arg in args)))
    process = subprocess.Popen(args, stdin=stdin, stdout=stdout, stderr=stderr, shell=shell)
    return process


def execute_helper(cmd, process_input=None, check_exit_code=True, extra_ok_codes=None, return_stderr=False):
    """execute bash command in network namespace"""
    _stdout = _stderr = returncode = None
    try:
        sub_proc = create_subprocess(cmd)
        _stdout, _stderr = sub_proc.communicate(process_input)
        returncode = sub_proc.returncode
        sub_proc.stdin.close()
        """verify return code"""
        extra_ok_codes = extra_ok_codes or []
        """returncode 0 is omitted from extra_ok_codes and following if clause"""
        if returncode and returncode not in extra_ok_codes:
            msg = "Exit code: %(returncode)d; Stdin: %(stdin)s; Stdout: %(stdout)s; Stderr: %(stderr)s" % {
                "returncode": returncode,
                "stdin": process_input or "",
                "stdout": _stdout,
                "stderr": _stderr,
            }
            if check_exit_code:
                raise ProcessExecutionError(msg, returncode=returncode)
    except OSError as e:
        logger.exception(e)
        raise
    finally:
        time.sleep(0)
    return (_stdout, _stderr) if return_stderr else _stdout


class IpNetnsCommand:
    """handle network namespace related commands"""

    COMMAND = ["ip", "netns", "exec"]

    @staticmethod
    def create_network_namspace(name, **kwargs):
        try:
            execute_helper(["ip", "netns", "add", name])
        except Exception as e:
            if "File exists" not in str(e):
                raise

    @staticmethod
    def remove_network_namespace(name, **kwargs):
        try:
            netns.remove(name, **kwargs)
        except OSError as e:
            if e.errno != errno.ENOENT:
                raise

    @staticmethod
    def list_network_namespaces(**kwargs):
        return netns.listnetns(**kwargs)

    @staticmethod
    def list_links_from_namespace(namespace):
        if namespace in IpNetnsCommand.list_network_namespaces():
            try:
                ns = pyroute2.NetNS(namespace)
                return ns.get_links()
            finally:
                ns.close()
        else:
            return None

    @staticmethod
    def exists(namespace):
        net_ns_list = IpNetnsCommand.list_network_namespaces()
        return namespace in net_ns_list

    @staticmethod
    def get_all_devices_from_ns(namespace):
        try:
            ns = pyroute2.NetNS(namespace)
            if ns:  # pragma: no cover
                return [x.get_attr("IFLA_IFNAME") for x in ns.get_links()]  # pragma: no cover
            else:  # pragma: no cover
                logger.warning("IpNetnsCommand not found namespace: {}".format(namespace))  # pragma: no cover
        finally:
            ns.close()

    @staticmethod
    def get_device_state_from_ns(ifname, net_ns):
        try:
            ns = pyroute2.NetNS(net_ns)
            dev_list = ns.get_links(ifname=ifname)
            if len(dev_list == 1):
                dev = dev_list[0]
                return dev.get_attr("IFLA_OPERSTATE")
            else:
                """multiple devices with same name in this network ns"""
                return None
        finally:
            ns.close()

    @staticmethod
    def ensure_device_is_ready(ifname, namespace):
        """ensure device is up in network ns"""
        retVal = False
        try:
            ns = pyroute2.NetNS(namespace)
            dev_idx = ns.link_lookup(ifname=ifname)[0]
            ns.link("set", index=dev_idx, state="up")
            retVal = True
        except IndexError:
            """log device does not exist"""
            logger.exception(
                "IpNetnsCommand device not found ifname: {}, \
                              namespace: {}".format(ifname, namespace)
            )
        finally:
            ns.close()
            return retVal

    @staticmethod
    def add_ip_to_device(ifname, namespace, ip_addr, mask=None):
        ip_addr = str(ip_addr)
        try:
            ns = pyroute2.NetNS(namespace)
            idx = ns.link_lookup(ifname=ifname)[0]
            ns.addr("add", index=idx, address=ip_addr, mask=mask)
        except IndexError:
            """log device does not exist"""
            logger.exception(
                "IpNetnsCommand device not found ifname: {}, \
                              namespace: {}".format(ifname, namespace)
            )
        finally:
            ns.close()

    @staticmethod
    def flush_ip_for_device(ifname, namespace):
        try:
            ipr = IPRoute()
            idx = ipr.link_lookup(ifname=ifname)[0]
            ipr.flush_addr(index=idx)
        except IndexError:
            """device not found"""
            logger.exception(
                "IpNetnsCommand device not found ifname: {}, \
                              namespace: {}".format(ifname, namespace)
            )
        finally:
            ipr.close()

    @staticmethod
    def remove_device_from_ns(ifname, namespace):
        try:
            ns = pyroute2.NetNS(namespace)
            links = ns.link_lookup(ifname=ifname)  # pragma: no cover
            if len(links) == 0:  # pragma: no cover
                logger.warning(
                    "IpNetnsCommand device not found ifname: {}, \
                                namespace: {}".format(ifname, namespace)
                )  # pragma: no cover
            else:  # pragma: no cover
                idx = links[0]  # pragma: no cover
                ns.link("del", index=idx)  # pragma: no cover
        except IndexError:
            """log device does not exist"""
            logger.exception(
                "IpNetnsCommand device not found ifname: {}, \
                              namespace: {}".format(ifname, namespace)
            )
        finally:
            ns.close()

    @staticmethod
    def execute(cmds, check_exit_code=True, return_stderr=False, extra_ok_codes=None, namespace=None):
        """execute bash command in network namespace"""
        if namespace:
            default_params = IpNetnsCommand.COMMAND
            full_cmds = [*default_params, namespace, *list(cmds)]
        else:
            full_cmds = cmds
        return execute_helper(
            full_cmds, check_exit_code=check_exit_code, return_stderr=return_stderr, extra_ok_codes=extra_ok_codes
        )


class IpLinkCommand:
    COMMAND = ["ip", "link"]

    @staticmethod
    def create_veth_pair_with_netns(ifname1, ifname2, namespace):
        """create veth pair (veth1, veth2) and move veth2 to namespace"""
        if (not namespace) and (not IpNetnsCommand.exists(namespace)):
            return
        try:
            ipr = IPRoute()
            ipr.link("add", ifname=ifname1, peer=ifname2, kind="veth")
            IpLinkCommand.move_device_to_netns(ifname2, namespace)
        finally:
            ipr.close()

    @staticmethod
    def ensure_device_is_ready(ifname):
        """ensure device is up in network ns"""
        retVal = False
        try:
            ipr = IPRoute()
            idx = ipr.link_lookup(ifname=ifname)[0]
            ipr.link("set", index=idx, state="up")
            retVal = True
        except IndexError:
            """log device does not exist"""
            logger.exception("IpLinkCommand device not found: {}".format(ifname))
        finally:
            ipr.close()
            return retVal

    @staticmethod
    def move_device_to_netns(ifname, namespace):
        """move a veth from current netns to netns_name"""
        if (not namespace) and (not IpNetnsCommand.exists(namespace)):
            return
        try:
            ipr = IPRoute()
            idx = ipr.link_lookup(ifname=ifname)[0]
            ipr.link("set", index=idx, net_ns_fd=namespace)
        except IndexError:
            """device not found"""
            logger.exception("IpLinkCommand device not found: {}".format(ifname))
        finally:
            ipr.close()

    @staticmethod
    def exists(ifname):
        pass

    @staticmethod
    def set_device_down(ifname):
        try:
            ipr = IPRoute()
            idx = ipr.link_lookup(ifname=ifname)[0]
            ipr.link("set", index=idx, state="down")
        except IndexError:
            """device not found"""
            logger.exception("IpLinkCommand device not found: {}".format(ifname))
        finally:
            ipr.close()

    @staticmethod
    def get_link_state():
        pass

    @staticmethod
    def get_links():
        try:
            ipr = IPRoute()
            links = [x.get_attr("IFLA_IFNAME") for x in ipr.get_links()]
            return links
        finally:
            ipr.close()

    @staticmethod
    def add_ip_to_device(ifname, ip_addr, mask):
        try:
            ipr = IPRoute()
            idx = ipr.link_lookup(ifname=ifname)[0]
            ipr.addr("add", index=idx, address=ip_addr, mask=mask)
        except IndexError:
            """device not found"""
            logger.exception("IpLinkCommand device not found: {}".format(ifname))
        finally:
            ipr.close()

    @staticmethod
    def flush_ip_for_device(ifname):
        try:
            ipr = IPRoute()
            idx = ipr.link_lookup(ifname=ifname)[0]
            ipr.flush_addr(index=idx)
        except IndexError:
            """device not found"""
            logger.exception("IpLinkCommand device not found: {}".format(ifname))
        finally:
            ipr.close()

    @staticmethod
    def remove_device(ifname):
        try:
            ipr = IPRoute()
            idx = ipr.link_lookup(ifname=ifname)[0]
            ipr.link("del", index=idx)
        except IndexError:
            """device not found"""
            logger.exception("IpLinkCommand device not found: {}".format(ifname))
        finally:
            ipr.close()

    @staticmethod
    def execute(cmds, check_exit_code=True, extra_ok_codes=None, _return_stderr=False):
        """execute bash command in network namespace"""
        default_params = IpLinkCommand.COMMAND
        full_cmds = default_params + list(cmds)
        return execute_helper(
            full_cmds, check_exit_code=check_exit_code, extra_ok_codes=extra_ok_codes, return_stderr=_return_stderr
        )


class OvsVsctlCommand:
    COMMAND = ["ovs-vsctl"]

    @staticmethod
    def create_ovs_bridge(br):
        args = ["add-br", br]
        return OvsVsctlCommand.execute(cmds=args)

    @staticmethod
    def add_interface_to_bridge(br, ifname, vlan_id):
        args = ["add-port", br, ifname, "tag={}".format(vlan_id)]
        return OvsVsctlCommand.execute(cmds=args)

    @staticmethod
    def delete_interface_from_bridge(br, ifname):
        args = ["del-port", br, ifname]
        return OvsVsctlCommand.execute(cmds=args)

    @staticmethod
    def list_interfaces_from_bridge(br):
        args = ["list-ports", br]
        ifaces_byte = OvsVsctlCommand.execute(cmds=args).splitlines()
        ifaces_str = [x.decode() for x in ifaces_byte]
        return ifaces_str

    @staticmethod
    def delete_ovs_bridge(br):
        args = ["del-br", br]
        return OvsVsctlCommand.execute(cmds=args)

    @staticmethod
    def list_bridge():
        args = ["list-br"]
        return OvsVsctlCommand.execute(cmds=args)

    @staticmethod
    def exist_bridge(br):
        stdout = OvsVsctlCommand.list_bridge()
        bridges_byte = stdout.splitlines()
        bridges_str = [x.decode() for x in bridges_byte]
        return br in bridges_str

    @staticmethod
    def execute(cmds, check_exit_code=True, extra_ok_codes=None, return_stderr=False):
        """execute bash command in network namespace"""
        default_params = OvsVsctlCommand.COMMAND
        full_cmds = default_params + list(cmds)
        return execute_helper(
            full_cmds, check_exit_code=check_exit_code, extra_ok_codes=extra_ok_codes, return_stderr=return_stderr
        )
