# Copyright (c) 2013-2018, SMARTX
# All rights reserved.

import signal

MON<PERSON><PERSON>_DB = "dhcp"

MONGO_COLLECTION_MAP = {
    "NETWORKS_UUIDS_TABLE": "networks_uuid",
    "NETWORKS_TABLE": "dhcp_networks",
    "IP_POOLS": "ip_pools",
}

WORKING_DIR = "/var/dhcp_manager"
"""prefix of network namespaces created and managed by the service"""
DHCP_NS_PREFIX = "dhcp_ns_"
DHCP_INTERFACE_PREFIX = "dhcp"
SIG_LIST = [signal.SIGTERM, signal.SIGHUP]
DNSMASQ_PROC_NAME = "dnsmasq"
