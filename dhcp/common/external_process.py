# Copyright (c) 2013-2018, SMARTX
# All rights reserved.

import abc
import logging
import os.path

import psutil

from dhcp.common import constants, file_utils, network_utils

logger = logging.getLogger("dhcp_network_manager")


class MonitoredProcess(metaclass=abc.ABCMeta):
    @abc.abstractproperty
    def active(self):
        """Boolean representing the running state of the process."""

    @abc.abstractmethod
    def enable(self):
        """Enable the service, or respawn the process."""


# revised from https://github.com/openstack/neutron/blob/master/neutron/agent/linux/external_process.py
class ProcessManager(MonitoredProcess):
    """An external process manager for Neutron spawned processes.

    Note: The manager expects uuid to be in cmdline.
    """

    def __init__(self, uuid, namespace=None, default_cmd_callback=None, pid_file=None, custom_reload_callback=None):
        self.uuid = uuid
        self.namespace = namespace
        self.default_cmd_callback = default_cmd_callback
        self.pid_file = pid_file
        self.custom_reload_callback = custom_reload_callback
        file_utils.ensure_tree(os.path.dirname(self.get_pid_file_name()), mode=0o755)

    def enable(self, cmd_callback=None, reload_cfg=False):
        if not self.active:
            if not cmd_callback:
                cmd_callback = self.default_cmd_callback
            cmd = cmd_callback(self.get_pid_file_name())
            network_utils.IpNetnsCommand.execute(cmd, namespace=self.namespace)
        elif reload_cfg:
            self.reload_cfg()

    def reload_cfg(self):
        if self.custom_reload_callback:
            self.disable(get_stop_command=self.custom_reload_callback)
        else:
            self.disable("HUP")

    def disable(self, sig="9", get_stop_command=None):
        pid = self.pid

        if self.active:
            if get_stop_command:
                cmd = get_stop_command(self.get_pid_file_name())
                network_utils.IpNetnsCommand.execute(cmd, namespace=self.namespace)
            else:
                cmd = ["kill", "-%s" % (sig), str(pid)]
                logger.info("cmd: {}".format(" ".join(cmd)))
                network_utils.execute_helper(cmd)
                # In the case of shutting down, remove the pid file
                if sig == "9":
                    file_utils.delete_if_exists(self.get_pid_file_name())
        elif pid:
            logger.debug(
                "Process for %(uuid)s pid %(pid)d is stale, ignoring signal %(signal)s",
                {"uuid": self.uuid, "pid": pid, "signal": sig},
            )
        else:
            logger.debug("No process started for %s", self.uuid)

    def get_pid_file_name(self):
        """Returns the file name for a given kind of config file."""
        return self.pid_file

    @property
    def pid(self):
        """Last known pid for this external process spawned for this uuid."""
        return file_utils.get_value_from_file(self.get_pid_file_name(), int)

    @property
    def active(self):
        cmdline = self.cmdline
        return self.uuid in cmdline if cmdline else False

    @property
    def cmdline(self):
        pid = self.pid
        if not pid:
            return
        try:
            return " ".join(psutil.Process(pid).cmdline())
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return

    @staticmethod
    def get_all_dnsmasq_processes():
        dnsmasq_proc_list = []
        try:
            for proc in psutil.process_iter():
                if proc.name() == constants.DNSMASQ_PROC_NAME:
                    dnsmasq_proc_list.append(proc)
            return dnsmasq_proc_list
        except Exception as e:
            logger.error("Error fetching dnsmasq process... reason: {}".format(str(e)))
        return None

    @staticmethod
    def disable_by_pid(pid, sig="9", pid_file=None):
        try:
            cmd = ["kill", "-%s" % (sig), str(pid)]
            logger.info("cmd: {}".format(" ".join(cmd)))
            network_utils.execute_helper(cmd)
            # In the case of shutting down, remove the pid file
            if sig == "9" and pid_file:
                logger.info("Remove pid file: {}".format(pid_file))
                file_utils.delete_if_exists(pid_file)
        except Exception as e:
            logger.error("Error disable process by pid: {}... reason: {}".format(pid, str(e)))
