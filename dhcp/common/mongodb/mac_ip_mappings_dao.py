# Copyright (c) 2013-2018, SMARTX
# All rights reserved.

import json
import logging
import time

from dhcp.common.mongodb.mongo_utils import MongoUtils

logger = logging.getLogger("dhcp_network_manager")


class MacIpMappingsDAO(MongoUtils):
    """target table: mac to ip addr mappings. Each network has a unique collection,
    and collection name is network_uuid. Documents of this collection are mac to
    ip address mappings."""

    def __init__(self, db, network_uuid):
        self.__db = db
        self.__collection = network_uuid

    def get_allocations(self, search_filter=None):
        """collection id: network_uuid (559e3357-1531-4176-a8d1-4740e2ffe1c3)
        document: {
                     'mac': '9e:78:5c:5a:a8:b0'
                     'ip': '**********'
                     'hostname': 'host1'
                   }"""
        projection = {"_id": False}
        if not search_filter:
            """return all mac to ip mappings for this network"""
            logger.info("Get all allocations for network: {}.".format(self.__collection))
            return self.find(self.__db, self.__collection, projection=projection)
        else:
            logger.info(
                "Get allocations for network: {}, with filter: {}.".format(self.__collection, json.dumps(search_filter))
            )
            return self.find(self.__db, self.__collection, search_filter=search_filter, projection=projection)

    def drop_allocations_for_network(self):
        """drop entire collection"""
        logger.info("Drop collection: {} from db: {}.".format(self.__collection, self.__db))
        self.drop(self.__db, self.__collection)
        return True

    def get_ip_by_mac_addr(self, mac):
        """return ip in string, otherwise, None"""
        search_filter = {"mac": mac}
        rec = self.find_one(self.__db, self.__collection, search_filter)
        if rec:
            return rec.get("ip", None)
        return None

    def persist_macipmapping(self, mac, ip, name=None):
        ip = str(ip)
        if name:
            doc = {"mac": mac, "ip": ip, "name": name, "timestamp": int(time.time())}
        else:
            doc = {"mac": mac, "ip": ip, "timestamp": int(time.time())}
        search_filter = {"mac": mac}
        if self.find_one(self.__db, self.__collection, search_filter):
            return self.replace_one(self.__db, self.__collection, search_filter, doc)
        else:
            return self.insert_one(self.__db, self.__collection, doc)

    def remove_ip_for_nic(self, mac):
        del_filter = {"mac": mac}
        logger.info("Delete all mac to ip mappings for network: {} and mac: {}.".format(self.__collection, mac))
        return self.delete_many(self.__db, self.__collection, del_filter)

    def remove_macipmappings(self, del_filter):
        return self.delete_many(self.__db, self.__collection, del_filter)

    def update_heartbeat(self, update_filter, timestamp):
        if not update_filter:
            return None
        updates = {"$set": {"timestamp": timestamp}}
        logger.info(
            "Update heartbeat with update_filter: {}, updates: {}, for collection: {}.".format(
                json.dumps(update_filter), json.dumps(updates), self.__collection
            )
        )
        return self.update_many(self.__db, self.__collection, update_filter=update_filter, updates=updates)
