# Copyright (c) 2013-2018, SMARTX
# All rights reserved.

import logging

from common.mongo.db import DB

logger = logging.getLogger("dhcp_network_manager")

"""
    MongoDB data structure
    network_settings collection: contains settings for each network identified by network_uuid
    ip_pool collection: records free ip list for each network
    mac_to_ip_address_mapping collections: each collection's name is network_uuid, documents are mac to ip_addr mappings
"""

client = None


def get_mongo_client():
    global client
    if not client:
        client = DB()
    return client


def tear_down():
    """close client"""
    client = get_mongo_client()
    if client:
        client.close()


class MongoUtils:
    """base case to operate on mongodb"""

    def __init__(self):
        pass

    def insert_one(self, db, collection, document):
        client = get_mongo_client()
        if (not client) or (not db) or (not collection):
            logger.error("Insufficient params for insert_one: db: {}, collection: {}.".format(db, collection))
            return None
        try:
            res = client[db][collection].insert_one(document)
            """ return pymongo.results.InsertOneResult obj,
                use inserted_id to tell if operation succeeds or not. """
            return res.inserted_id
        except Exception as e:
            logger.exception("Failed to insert_one record from mongo: {}".format(e))
            raise

    def replace_one(self, db, collection, rep_filter, document):
        client = get_mongo_client()
        if (not client) or (not db) or (not collection):
            logger.error("Insufficient params for replace_one: db: {}, collection: {}.".format(db, collection))
            return None
        try:
            res = client[db][collection].replace_one(rep_filter, document)
            """ return pymongo.results.UpdateResult obj,
                use modified_count(matched_count?) to tell if operation succeeds or not. """
            return res.modified_count
        except Exception as e:
            logger.exception("Failed to replace_one record from mongodb: {}".format(e))
            raise

    def update_one(self, db, collection, update_filter, updates):
        client = get_mongo_client()
        if (not client) or (not db) or (not collection) or (not update_filter):
            logger.error(
                "Insufficient params for update_one: db: {}, collection: {}, update_filter: {}.".format(
                    db, collection, update_filter
                )
            )
            return None
        try:
            res = client[db][collection].update_one(update_filter, updates)
            """ return pymongo.results.UpdateResult obj,
                use modified_count(matched_count?) to tell if operation succeeds or not. """
            return res.modified_count
        except Exception as e:
            logger.exception("Failed to update_one record from mongodb: {}".format(e))
            raise

    def update_many(self, db, collection, update_filter, updates):
        client = get_mongo_client()
        if (not client) or (not db) or (not collection) or (not update_filter):
            logger.error(
                "Insufficient params for update_one: db: {}, collection: {}, update_filter: {}.".format(
                    db, collection, update_filter
                )
            )
            return None
        try:
            res = client[db][collection].update_many(update_filter, updates)
            """ return pymongo.results.UpdateResult obj,
                use modified_count(matched_count?) to tell if operation succeeds or not. """
            return res.modified_count
        except Exception as e:
            logger.exception("Failed to update_many records from mongodb: {}".format(e))
            raise

    def delete_one(self, db, collection, del_filter):
        client = get_mongo_client()
        if (not client) or (not db) or (not collection) or (not del_filter):
            logger.error(
                "Insufficient params for delete_one: db: {}, collection: {}, del_filter: {}.".format(
                    db, collection, del_filter
                )
            )
            return None
        try:
            res = client[db][collection].delete_one(del_filter)
            """ return pymongo.results.DeleteResult obj,
                use deleted_count to tell if operation succeeds or not. """
            return res.deleted_count
        except Exception as e:
            logger.exception("Failed to delete_one record from mongodb: {}".format(e))
            raise

    def delete_many(self, db, collection, del_filter):
        client = get_mongo_client()
        if (not client) or (not db) or (not collection) or (not del_filter):
            logger.error(
                "Insufficient params for delete_many: db: {}, collection: {}, del_filter: {}.".format(
                    db, collection, del_filter
                )
            )
            return None
        try:
            res = client[db][collection].delete_many(del_filter)
            """ return pymongo.results.DeleteResult obj,
                use deleted_count to tell if operation succeeds or not. """
            return res.deleted_count
        except Exception as e:
            logger.exception("Failed to delete_one record from mongodb: {}".format(e))
            raise

    def find_one(self, db, collection, search_filter):
        client = get_mongo_client()
        if (not client) or (not db) or (not collection) or (not search_filter):
            logger.error(
                "Insufficient params for find_one: db: {}, collection: {}, search_filter: {}.".format(
                    db, collection, search_filter
                )
            )
            return None
        try:
            res = client[db][collection].find_one(search_filter)
            """find_one returns None or data obj"""
            return res
        except Exception as e:
            logger.exception("Failed to find_one record from mongodb: {}".format(e))
            raise

    def find(self, db, collection, search_filter=None, projection=None):
        client = get_mongo_client()
        if (not client) or (not db) or (not collection):
            logger.error("Insufficient params for find: db: {}, collection: {}".format(db, collection))
            return None
        try:
            if search_filter:
                cursor = client[db][collection].find(search_filter, projection=projection)
            else:
                cursor = client[db][collection].find(projection=projection)
            records = []
            for item in cursor:
                records.append(item)
            return records
        except Exception as e:
            logger.exception("Failed to find record from mongodb: {}".format(e))
            raise

    def drop(self, db, collection):
        client = get_mongo_client()
        if (not client) or (not db) or (not collection):
            logger.error("Insufficient params for drop: db: {}, collection: {}".format(db, collection))
            return None
        """drop entire collection"""
        try:
            db = client[db]
            if collection in db.collection_names():
                logger.info("drop collection: {} from db: {}".format(collection, db))
                db[collection].drop()
            else:
                logger.info("collection: {} does not exist in db: {}, skip.".format(collection, db))
        except Exception as e:
            logger.exception("Failed to drop collection: {}".format(e))
            raise

    def get_document_counts(self, db, collection):
        client = get_mongo_client()
        if (not client) or (not db) or (not collection):
            logger.error("Insufficient params for get_document_counts: db: {}, collection: {}".format(db, collection))
            return None
        try:
            return client[db][collection].count()
        except Exception as e:
            logger.exception("Failed to get document counts: {}".format(e))
            raise
