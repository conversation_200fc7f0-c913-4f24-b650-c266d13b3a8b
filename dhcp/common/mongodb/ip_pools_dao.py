# Copyright (c) 2013-2018, SMARTX
# All rights reserved.

import json
import logging

from dhcp.common.mongodb.mongo_utils import MongoUtils

logger = logging.getLogger("dhcp_network_manager")


class IpPoolsDAO(MongoUtils):
    """target table: ip_pools"""

    def __init__(self, database, collection):
        self.__db = database
        self.__collection = collection

    def get_available_ips_for_network(self, network_uuid):
        if not network_uuid:
            logger.error("Network uuid is not set.")
            return None
        search_filter = {"network_uuid": network_uuid}
        record = self.find_one(self.__db, self.__collection, search_filter)
        ip_pool = None
        if record and ("ip_pool" in record):
            ip_pool = record["ip_pool"]
        return ip_pool

    def remove_ip_pool_for_network(self, network_uuid):
        if not network_uuid:
            logger.error("Network uuid is not set.")
            return None
        del_filter = {"network_uuid": network_uuid}
        if self.find_one(self.__db, self.__collection, del_filter):
            logger.info("Delete IP pool with filter: {}".format(json.dumps(del_filter)))
            return self.delete_one(self.__db, self.__collection, del_filter)
        else:
            """Failed to find ip_pool by network_uuid, return directy"""
            logger.warning("Failed to remove ip pool using filter: {}".format(network_uuid))
            return True

    def create_ip_pool_for_network(self, network_uuid, doc):
        if not network_uuid:
            logger.error("Network uuid is not set.")
            return None
        return self.insert_one(self.__db, self.__collection, doc)

    def update_ip_pool_for_network(self, network_uuid, doc):
        if not network_uuid:
            logger.error("Network uuid is not set.")
            return None
        search_filter = {"network_uuid": network_uuid}
        logger.info("Replace IP Pool: {} with doc: {}".format(network_uuid, json.dumps(doc)))
        return self.replace_one(self.__db, self.__collection, search_filter, doc)
