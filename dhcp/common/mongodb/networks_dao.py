# Copyright (c) 2013-2018, SMARTX
# All rights reserved.

import json
import logging

from dhcp.common.exceptions import NoResourceAvailableError
from dhcp.common.mongodb.mongo_utils import MongoUtils

logger = logging.getLogger("dhcp_network_manager")


class NetworksDAO(MongoUtils):
    """target table: networks_table"""

    MAX_DHCP_NETWORKS = 128

    def __init__(self, db, collection):
        self.__db = db
        self.__collection = collection

    def get_network_by_id(self, network_uuid):
        if not network_uuid:
            logger.error("Network uuid is not set.")
            return None
        network = self.find_one(self.__db, self.__collection, {"network_uuid": network_uuid})
        if network and ("_id" in network):
            del network["_id"]
        if network and "name" not in network:
            """protobuf message use name field instead of network_uuid"""
            network["name"] = "dhcps/{}".format(network["network_uuid"])
        return network

    def batch_get_networks(self, search_filter):
        projection = {"_id": False}
        return self.find(self.__db, self.__collection, search_filter=search_filter, projection=projection)

    def create_new_network(self, network):
        if not network:
            logger.error("Network uuid is not set.")
            return None
        count = self.get_document_counts(self.__db, self.__collection)
        logger.info("Number of networks with DHCP enabled: {}".format(count))
        if count >= self.MAX_DHCP_NETWORKS:
            msg = "Number of networks created with DHCP enabled exceeds limit: {}.".format(self.MAX_DHCP_NETWORKS)
            logger.error(msg)
            raise NoResourceAvailableError(msg)
        logger.info("Create network: {}".format(json.dumps(network)))
        return self.insert_one(self.__db, self.__collection, network)

    def delete_network_by_id(self, network_uuid):
        if not network_uuid:
            logger.error("Network uuid is not set.")
            return None
        del_filter = {"network_uuid": network_uuid}
        if self.find_one(self.__db, self.__collection, del_filter):
            logger.info("Found dhcp network with uuid: {}".format(network_uuid))
            return self.delete_one(self.__db, self.__collection, del_filter)
        else:
            """Failed to find network by id, return directly"""
            logger.warning("Failed to find dhcp network with uuid: {}".format(network_uuid))
            return True

    def update_network_settings(self, filters, settings):
        logger.info("Update network with filters: {}, settings: {}".format(json.dumps(filters), json.dumps(settings)))
        return self.update_one(self.__db, self.__collection, filters, {"$set": settings})

    def replace_network_settings(self, filters, settings):
        logger.info("Update network with filters: {}, settings: {}".format(json.dumps(filters), json.dumps(settings)))
        return self.replace_one(self.__db, self.__collection, filters, settings)

    def get_network_uuids(self):
        """return a list of network_uuid"""
        network_uuids = []
        projection = {"_id": False, "network_uuid": True}
        records = self.find(self.__db, self.__collection, projection=projection)
        for item in records:
            network_uuids.append(item["network_uuid"])
        return network_uuids

    def update_last_heartbeat_timestamp(self, network_uuid, timestamp):
        if not network_uuid:
            return None
        update_filter = {"network_uuid": network_uuid}
        updates = {"$set": {"heartbeat_timestamp": timestamp}}
        logger.info(
            "Update last heartbeat timestamp with update_filter: {}, updates: {} for collection: {}.".format(
                json.dumps(update_filter), json.dumps(updates), self.__collection
            )
        )
        return self.update_one(self.__db, self.__collection, update_filter=update_filter, updates=updates)

    def get_last_heartbeat_timestamp(self, network_uuid=None):
        if not network_uuid:
            return None
        network = self.find_one(self.__db, self.__collection, {"network_uuid": network_uuid})
        return network.get("heartbeat_timestamp", None)
