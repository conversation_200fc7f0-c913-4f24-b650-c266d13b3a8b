# Copyright (c) 2013-2018, SMARTX
# All rights reserved.

import errno
import logging
import os
import tempfile

logger = logging.getLogger("dhcp_network_manager")


def get_value_from_file(filename, converter=None):
    try:
        with open(filename) as f:
            try:
                return converter(f.read()) if converter else f.read()
            except ValueError:
                logger.error("Unable to convert value in %s", filename)
    except OSError:
        logger.debug("Unable to access %s", filename)


def delete_if_exists(path):
    """Delete a file, but ignore file not found error."""
    try:
        os.remove(path)
    except OSError as e:
        if e.errno != errno.ENOENT:
            raise


_DEFAULT_MODE = 0o644


def ensure_tree(path, mode=_DEFAULT_MODE):
    """Create a directory (and any ancestor directories required)
    :param path: Directory to create
    :param mode: Directory creation permissions
    """
    try:
        os.makedirs(path, mode)
    except OSError as exc:
        if exc.errno == errno.EEXIST:
            if not os.path.isdir(path):
                raise
        else:
            raise


def replace_file(file_name, data, file_mode=0o644):
    """Replaces the contents of file_name with data in a safe manner.
    First write to a temp file and then rename. Since POSIX renames are
    atomic, the file is unlikely to be corrupted by competing writes.
    We create the tempfile on the same device to ensure that it can be renamed.
    :param file_name: Path to the file to replace.
    :param data: The data to write to the file.
    :param file_mode: The mode to use for the replaced file.
    :returns: None.
    """

    base_dir = os.path.dirname(os.path.abspath(file_name))
    with tempfile.NamedTemporaryFile("w+", dir=base_dir, delete=False) as tmp_file:
        tmp_file.write(data)
    os.chmod(tmp_file.name, file_mode)
    os.rename(tmp_file.name, file_name)


def touch_file(file_name, times=None):
    with open(file_name, "a"):
        os.utime(file_name, times)
