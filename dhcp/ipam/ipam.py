# Copyright (c) 2013-2018, SMARTX
# All rights reserved.
import json
import logging
import re
import time

from netaddr import IPAddress, IPNetwork

from dhcp.common import constants
from dhcp.common.exceptions import InvalidArgumentError, NoResourceAvailableError
from dhcp.common.mongodb import mac_ip_mappings_dao

logger = logging.getLogger("dhcp_network_manager")


class IpAddrAllocator:
    def __init__(self, network, exclude_ip_list=None, ip_pools_dao=None):
        self.network = network
        self.network_uuid = network.get("network_uuid")
        self.__ip_pools_dao = ip_pools_dao
        self.__mac_ip_mappings_dao = mac_ip_mappings_dao.MacIpMappingsDAO(constants.MONGO_DB, self.network_uuid)
        cidr = None
        if "cidr" in network:
            cidr = network["cidr"]
            if cidr and isinstance(cidr, str):
                cidr = IPNetwork(cidr)
            else:
                cidr = None
        self.cidr = cidr
        ip_ranges = []
        if network.get("ip_ranges"):
            for ip_range in network["ip_ranges"]:
                if len(ip_range) == 2:
                    start_ip = ip_range["start_ip"]
                    end_ip = ip_range["end_ip"]
                    if start_ip and isinstance(start_ip, str):
                        start_ip = IPAddress(start_ip)
                    if end_ip and isinstance(end_ip, str):
                        end_ip = IPAddress(end_ip)
                    if start_ip and end_ip and start_ip < end_ip:
                        ip_ranges.append((start_ip, end_ip))
                    else:
                        logger.error("start_ip is larger than end_ip for range: {}, skip.".format(ip_range))
                        continue
                else:
                    logger.error("Found malformed ip_range: {}, ignore.".format(ip_range))
                    continue
        self.ip_ranges = ip_ranges
        if exclude_ip_list:
            self.exclude_ip_list = exclude_ip_list  # ips in exclude_ip_list should be in string format
        else:
            self.exclude_ip_list = []
        self.exclude_ip_list.append(str(self.network["dhcp_interface_ip"]))
        self.exclude_ip_list.append(str(self.network["gateway_ip"]))
        if self.network.get("dns_servers"):
            dns_servers = [server.strip() for server in self.network["dns_servers"].split(",")]
            for dns_server in dns_servers:
                self.exclude_ip_list.append(dns_server)
        self.initialize_ip_alloc_pool_for_network()

    def _generate_ip_alloc_pool_for_network(self):
        """call once when network being created"""
        ip_pool = []
        if self.ip_ranges and len(self.ip_ranges) > 0:
            logger.info("Initialize IP allocation pool using ip_ranges list: {}.".format(self.ip_ranges))
            for ip_range in self.ip_ranges:
                for ip in range(ip_range[0], ip_range[1]):
                    """enumerate ip addresses in the range"""
                    ip_pool.append(str(IPAddress(ip)))
                """end_ip is also a valid ip for allocation"""
                ip_pool.append(str(ip_range[1]))
        elif self.cidr:
            logger.info("Initialize IP allocation pool through cidr block: {}".format(self.cidr))
            """initialze pool from cidr"""
            for ip in self.cidr:
                ip_pool.append(str(ip))
        else:
            raise InvalidArgumentError(
                "Failed to initialize ip allocation pool for network:{network_uuid}, "
                "both cidr and ip_ranges are not defined.".format(network_uuid=self.network_uuid)
            )
        return ip_pool

    def initialize_ip_alloc_pool_for_network(self):
        ip_pool = self.__ip_pools_dao.get_available_ips_for_network(self.network_uuid)
        if ip_pool is not None:
            """skip initialization if there is an existed ip_pool. ip_pool can be empty if run out of IPs."""
            ip_pool_str = " ".join(str(e) for e in ip_pool)
            logger.info("Found existing ip_pool: {} for network: {}".format(self.network_uuid, ip_pool_str))
            return True
        logger.info("Initialize ip_pool for network: {}".format(self.network_uuid))
        ip_pool = self._generate_ip_alloc_pool_for_network()
        if self.exclude_ip_list:
            for ip in self.exclude_ip_list:
                if ip in ip_pool:
                    logger.info("remove excluded ip: {} from ip allocation pool.".format(ip))
                    ip_pool.remove(ip)
                else:
                    logger.info("Failed to find excluded ip: {} from ip allocation pool, skip.".format(ip))
        """persiste ip_pool to mongodb"""
        doc = {"network_uuid": self.network_uuid, "ip_pool": ip_pool}
        return self.__ip_pools_dao.create_ip_pool_for_network(self.network_uuid, doc)

    def get_next_ip_from_pool(self):
        available_ips = self.__ip_pools_dao.get_available_ips_for_network(self.network_uuid)
        first_pick = None
        if available_ips and len(available_ips) > 0:
            first_pick = available_ips.pop(0)
            """persist to mongodb"""
            new_doc = {"network_uuid": self.network_uuid, "ip_pool": available_ips}
            updateResult = self.__ip_pools_dao.update_ip_pool_for_network(self.network_uuid, new_doc)
            if updateResult == 1:
                """successfully persisted the list"""
                return first_pick
        else:
            msg = "No available IP left for network: {}.".format(self.network_uuid)
            logger.error(msg)
            raise NoResourceAvailableError(msg)
        return None

    def reserve_mac_ip_pair(self, mac, ip_addr, name):
        available_ips = self.__ip_pools_dao.get_available_ips_for_network(self.network_uuid)
        if ip_addr not in available_ips:
            msg = "IP address requested: {} is not available in network: {}.".format(ip_addr, self.network_uuid)
            logger.error(msg)
            raise NoResourceAvailableError(msg)
        # persist available_ips back to mongo
        available_ips.remove(str(ip_addr))
        new_doc = {"network_uuid": self.network_uuid, "ip_pool": available_ips}
        updateResult = self.__ip_pools_dao.update_ip_pool_for_network(self.network_uuid, new_doc)
        if updateResult != 1:
            """successfully persisted the list"""
            logger.error("Failed to persis updated ip list for network: {}".format(self.network_uuid))
            return None
        # persist mac ip mappings to mongo
        if self.persist_mac_to_ip_mapping(mac, ip_addr, name):
            return ip_addr
        else:
            logger.error("Failed to persist mac: {} to ip: {} mapping to mongo".format(mac, ip_addr))
            return None

    def return_free_ip_to_pool(self, ip_addr):
        available_ips = self.__ip_pools_dao.get_available_ips_for_network(self.network_uuid)
        if isinstance(ip_addr, list | tuple):
            available_ips.extend([str(ip) for ip in ip_addr])
        else:
            available_ips.append(str(ip_addr))
        """persist to mongodb"""
        new_doc = {"network_uuid": self.network_uuid, "ip_pool": available_ips}
        updateResult = self.__ip_pools_dao.update_ip_pool_for_network(self.network_uuid, new_doc)
        if updateResult == 1:
            """successfully persisted the list"""
            return True
        else:
            return False

    def filter_available_ips(self, ips):
        available_ips = self.__ip_pools_dao.get_available_ips_for_network(self.network_uuid)
        response = []
        for ip in ips:
            if str(ip) in available_ips:
                response.append(ip)
        return response

    def generate_regex_text_query_criteria(self, search_fields, text):
        if not isinstance(text, list | tuple):
            text = [text]

        def _g(search_field):
            return {
                search_field: {
                    "$regex": "|".join(
                        [re.sub(r"(\*|\.|\?|\+|\$|\^|\[|\]|\(|\)|\{|\}|\||\\|/)", r"\\\1", g.strip()) for g in text]
                    ),
                    "$options": "i",
                }
            }

        query_criteria = {}
        if len(search_fields) > 1:
            query_criteria.update({"$or": [_g(x) for x in search_fields]})
        else:
            query_criteria.update(_g(search_fields[0]))

        return query_criteria

    def search_mappings_by_ip_text(self, text):
        logger.info("Search mac_ip mappings for network: {} using filter: {}.".format(self.network_uuid, text))
        search_fields = ["ip"]
        text_fields = text.split(",")
        query_criteria = self.generate_regex_text_query_criteria(search_fields, text_fields)
        return self.__mac_ip_mappings_dao.get_allocations(query_criteria)

    def drop_allocations_for_network(self):
        self.__mac_ip_mappings_dao.drop_allocations_for_network()

    def clean_up_network(self):
        """clean up all allocations when deleting network"""
        logger.info("Drop mac_to_ip mappings for network: {}".format(self.network_uuid))
        self.drop_allocations_for_network()
        logger.info("Drop available ip pool for network: {}".format(self.network_uuid))
        return self.__ip_pools_dao.remove_ip_pool_for_network(self.network_uuid)

    def persist_mac_to_ip_mapping(self, mac, ip, name=None):
        if (not mac) or (not ip):
            return None
        return self.__mac_ip_mappings_dao.persist_macipmapping(mac, ip, name)

    def get_ip_pool(self):
        return self.__ip_pools_dao.get_available_ips_for_network(self.network_uuid)

    def get_macipmappings_for_network(self, search_filter=None):
        return self.__mac_ip_mappings_dao.get_allocations(search_filter)

    def update_heartbeat(self, update_filter, timestamp):
        return self.__mac_ip_mappings_dao.update_heartbeat(update_filter, timestamp)

    def reclaim_dangle_ips(self, macipmapping_max_timeout):
        """Find all records with timestamp last updated 24 hours ago"""
        cur_timestamp = int(time.time())
        aged_timestamp = cur_timestamp - macipmapping_max_timeout
        search_filter = {"timestamp": {"$lt": aged_timestamp}}
        stale_records = self.__mac_ip_mappings_dao.get_allocations(search_filter)
        if stale_records and len(stale_records) > 0:
            logger.info("reclaim_dangle_ips found stale records: {}.".format(json.dumps(stale_records)))
            """ Remove these records """
            del_filter = {"mac": {"$in": [macipmapping.get("mac") for macipmapping in stale_records]}}
            logger.info("Delete macipmappings using filter: {}.".format(json.dumps(del_filter)))
            self.__mac_ip_mappings_dao.remove_macipmappings(del_filter)
            """ Free IP addresses for these records """
            ips_to_free = [macipmapping.get("ip") for macipmapping in stale_records]
            logger.info("reclaim_dangle_ips frees {}.".format(json.dumps(ips_to_free)))
            self.return_free_ip_to_pool(ips_to_free)
            return stale_records
        else:
            logger.info("reclaim_dangle_ips found no stale record for this run.")
            return None

    def reserve_ip_for_nic(self, req):
        mac = req.get("mac")
        name = req.get("name", None)
        existed_ip = self.get_ip_for_nic(mac)
        if existed_ip:
            logger.info("Call reserve_ip_for_nic found existed_ip: {} for mac: {}.".format(existed_ip, mac))
            return existed_ip
        if req.get("ip"):
            ip = req["ip"]
            return self.reserve_mac_ip_pair(mac, ip, name)
        else:
            ip = self.get_next_ip_from_pool()
            logger.info("Assign ip: {} to mac {}.".format(ip, mac))
            if ip and self.persist_mac_to_ip_mapping(mac, ip, name):
                return ip
            else:
                return None

    """defer sanity check for data obj to flask_app entries"""

    def update_ip_for_nic(self, req):
        mac = req["mac"]
        new_ip = req["ip"]
        name = req["name"]
        old_ip = self.get_ip_for_nic(mac)
        if not old_ip:
            msg = "Failed to find ip allocation for mac: {}, with network: {}.".format(mac, self.network_uuid)
            logger.error(msg)
            raise NoResourceAvailableError(msg)
        """When requested ip address is currently assigned to this mac, return directly."""
        if new_ip == old_ip:
            logger.info("Requested IP: {} is same for current IP: {} for mac: {}, return.".format(new_ip, old_ip, mac))
            return new_ip
        logger.info("Replace old_ip: {} for mac {} with new_ip: {}.".format(old_ip, mac, new_ip))
        """try to reserve new_ip first"""
        available_ips = self.__ip_pools_dao.get_available_ips_for_network(self.network_uuid)
        if available_ips and len(available_ips) > 0:
            if new_ip in available_ips:
                logger.info("Found ip: {} in available ip list.".format(new_ip))
                available_ips.remove(new_ip)
                """persist to mongodb"""
                new_doc = {"network_uuid": self.network_uuid, "ip_pool": available_ips}
                updateResult = self.__ip_pools_dao.update_ip_pool_for_network(self.network_uuid, new_doc)
                if updateResult == 1:
                    """persist new mac to ip mapping to mongo"""
                    if self.persist_mac_to_ip_mapping(mac, new_ip, name):
                        """free old_ip, if failed to persist, rely on sync for recycle"""
                        self.return_free_ip_to_pool(old_ip)
                        return new_ip
                    else:
                        logger.error("Failed to persist mac: {} to ip:{} mapping".format(mac, new_ip))
                else:
                    logger.error("Failed to update available ip pools for reservation")
                    return None
            else:
                msg = "New ip: {} is not found from available ip list of network: {}.".format(new_ip, self.network_uuid)
                logger.error(msg)
                raise NoResourceAvailableError(msg)
        else:
            msg = "No available IP left for network: {}.".format(self.network_uuid)
            logger.error(msg)
            raise NoResourceAvailableError(msg)
        return None

    def remove_ip_for_nic(self, data):
        mac = data["mac"]
        ip = self.get_ip_for_nic(mac)
        if not ip:
            msg = "Failed to find IP for mac: {} from network: {}.".format(mac, self.network_uuid)
            logger.error(msg)
            raise NoResourceAvailableError(msg)
        if self.__mac_ip_mappings_dao.remove_ip_for_nic(mac) and self.return_free_ip_to_pool(ip):
            return ip
        else:
            logger.error("Failed to remove ip: {} for mac: {}".format(ip, mac))
            return None

    def get_ip_for_nic(self, mac):
        return self.__mac_ip_mappings_dao.get_ip_by_mac_addr(mac)

    def sync(self):
        """
        placeholder: for network: network_uuid, make sure mac_to_ip_addr_mapping
        is consistent with ip_allocation_pool.
        """
        pass
