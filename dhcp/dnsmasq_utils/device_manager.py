# Copyright (c) 2013-2018, SMARTX
# All rights reserved.

import logging

from dhcp.common import network_utils
from dhcp.common.exceptions import InternalError, InvalidArgumentError

logger = logging.getLogger("dhcp_network_manager")


class DeviceManager:
    def __init__(self, network, namespace):
        """setup device id, network ns, type, etc"""
        self.network = network
        self.namespace = namespace
        self.vlan_id = network["vlan_id"]
        self.dhcp_interface_ip = network["dhcp_interface_ip"]
        if self.network.get("ovs_br"):
            self.ovs_br = self.network["ovs_br"]
        else:
            msg = "Failed to find ovs_br defination for network: {}".format(network["network_uuid"])
            logger.error(msg)
            raise InvalidArgumentError(msg)
        logger.info("DeviceManager use ovs_bridge {} for network {}.".format(self.ovs_br, self.network["network_uuid"]))
        """ dev name cannot be very long:
            Error: argument "559e3357-1531-4176-a8d1-4740e2ffe1c3_30_1" is wrong: "name" too long
            maximum length of device name is 15 bytes.
        """
        self.ifname1 = self.network["tap"]
        self.ifname2 = self.network["veth"]
        logger.info("DeviceManager use devices name: {}, {}.".format(self.ifname1, self.ifname2))

    def ensure_device_ready(self):
        network_utils.IpLinkCommand.ensure_device_is_ready(self.ifname1)
        network_utils.IpNetnsCommand.ensure_device_is_ready(self.ifname2, self.namespace)

    def create_veth_pair_with_netns(self):
        logger.info(
            "create_veth_pair_with_netns: {} and {}, netns: {}".format(self.ifname1, self.ifname2, self.namespace)
        )
        return network_utils.IpLinkCommand.create_veth_pair_with_netns(self.ifname1, self.ifname2, self.namespace)

    def delete_orphan_devices_from_ns(self):
        devs = network_utils.IpNetnsCommand.get_all_devices_from_ns(self.namespace)
        for dev in devs:
            if dev != "lo":
                logger.warning("Remove orphan device: {} from namespace: {}".format(dev, self.namespace))
                network_utils.IpNetnsCommand.remove_device_from_ns(dev, self.namespace)

    def ensure_namespace(self):
        logger.info("ensure_namespace: {}".format(self.namespace))
        return network_utils.IpNetnsCommand.create_network_namspace(self.namespace)

    def assign_ip_to_device(self):
        mask = None
        if self.network.get("mask"):
            mask = self.network["mask"]
        logger.info(
            "Assign ip: {} to interface: {}, namespace:{}, netmask: {}.".format(
                self.dhcp_interface_ip, self.ifname2, self.namespace, mask
            )
        )
        return network_utils.IpNetnsCommand.add_ip_to_device(self.ifname2, self.namespace, self.dhcp_interface_ip, mask)

    def connect_to_ovs_bridge(self, ifname, vlan_id):
        logger.info("Connect device:{} to ovs_bridge, vlan_id:{}.".format(ifname, vlan_id))
        if not network_utils.OvsVsctlCommand.exist_bridge(self.ovs_br):
            """This should never happen"""
            msg = "Fatal: ovs_bridge: {} does not exist.".format(self.ovs_br)
            logger.error(msg)
            raise InternalError(msg)
        """if device already on ovs_br, remove it"""
        ports = network_utils.OvsVsctlCommand.list_interfaces_from_bridge(self.ovs_br)
        logger.info(
            "Found existing ports: {} from ovs_bridge:{}.".format(",".join(port for port in ports), self.ovs_br)
        )
        if ifname in ports:
            logger.warning("Device: {} is already connected to ovs_br: {}, remove it.".format(ifname, self.ovs_br))
            network_utils.OvsVsctlCommand.delete_interface_from_bridge(self.ovs_br, ifname)
        network_utils.OvsVsctlCommand.add_interface_to_bridge(self.ovs_br, ifname, vlan_id)

    def setup_device_for_dnsmasq(self):
        # 1. create network namespace
        self.ensure_namespace()
        # 2.1 create veth pair
        self.delete_orphan_devices_from_ns()
        self.create_veth_pair_with_netns()
        # 2.2 assign ip_addr to ifname2
        self.assign_ip_to_device()
        # 2.1 bring up both devices
        self.ensure_device_ready()
        # 3. connect to ovs_bridge
        self.connect_to_ovs_bridge(self.ifname1, self.vlan_id)

    def cleanup_stale_devices(self):
        self.unplug_device_from_ovs_bridge(self.ovs_br, self.ifname1)
        self.flush_ips_for_devices()
        self.delete_devices()

    def disable(self, remove_namespace=True):
        self.cleanup_stale_devices()
        if remove_namespace:
            self.remove_namespace(self.namespace)

    def get_devices_name(self):
        return self.ifname1, self.ifname2

    def unplug_device_from_ovs_bridge(self, ovs_br, ifname):
        logger.info("Unplug device: {} from ovs_bridge: {}.".format(ifname, ovs_br))
        network_utils.OvsVsctlCommand.delete_interface_from_bridge(ovs_br, ifname)

    def flush_ips_for_devices(self):
        self.flush_ips_for_devices_helper(self.ifname1)
        self.flush_ips_for_devices_helper(self.ifname2, self.namespace)

    def flush_ips_for_devices_helper(self, ifname, namespace=None):
        logger.info("Flush ip for device: {}, namespace: {}".format(ifname, namespace))
        """should check devices existence"""
        if namespace and network_utils.IpNetnsCommand.exists(namespace):
            network_utils.IpNetnsCommand.flush_ip_for_device(ifname, namespace)
        else:
            network_utils.IpLinkCommand.flush_ip_for_device(ifname)

    def delete_devices(self):
        self.delete_devices_helper(self.ifname1)
        """exception might get raised since only one interface
           need to be deleted from veth pair"""
        self.delete_devices_helper(self.ifname2, self.namespace)

    def delete_devices_helper(self, ifname, namespace=None):
        logger.info("Delete device: {}, namespace: {}".format(ifname, namespace))
        if namespace and network_utils.IpNetnsCommand.exists(namespace):
            network_utils.IpNetnsCommand.remove_device_from_ns(ifname, namespace)
        else:
            network_utils.IpLinkCommand.remove_device(ifname)

    def remove_namespace(self, namespace=None):
        logger.info("Remove namespace: {}".format(namespace))
        if namespace and network_utils.IpNetnsCommand.exists(namespace):
            return network_utils.IpNetnsCommand.remove_network_namespace(namespace)

    def sync_devices(self):
        """loop through all settings to make sure devices/ovs/namespace are in desired state"""
        pass

    def get_dnsmasq_interface(self):
        return self.ifname2
