# Copyright (c) 2013-2018, SMARTX
# All rights reserved.

import json
import logging
import os
import shutil

import netaddr
import six

from dhcp.common import constants, external_process, file_utils, network_utils
from dhcp.dnsmasq_utils import device_manager
from dhcp.ipam import ipam

logger = logging.getLogger("dhcp_network_manager")


class Dnsmasq:
    """manage dnsmasq instance"""

    def __init__(self, network, network_conf_dir, ip_pools_dao, dnsmasq_run_as_user="elf-dhcp"):
        self.network_conf_dir = network_conf_dir
        file_utils.ensure_tree(self.network_conf_dir, mode=0o755)
        self.pid_file = self._get_pid_file_name()
        self.network = network
        self.__ip_pools_dao = ip_pools_dao
        self.namespace = constants.DHCP_NS_PREFIX + network["network_uuid"]
        self.device_manager = device_manager.DeviceManager(self.network, self.namespace)
        self.pm = None
        self.ip_addr_allocator = ipam.IpAddrAllocator(network, ip_pools_dao=self.__ip_pools_dao)
        self.dnsmasq_conf_file_path = os.path.join(self.network_conf_dir, "dnsmasq.conf")
        self.dnsmasq_run_as_user = dnsmasq_run_as_user

    def drop_allocations_for_network(self):
        self.ip_addr_allocator.drop_allocations_for_network()

    def _build_cmdline_callback(self, pid_file):
        # We ignore local resolv.conf if dns servers are specified
        # or if local resolution is explicitly disabled.
        _no_resolv = "--no-resolv"
        cmd = [
            "dnsmasq",
            "--user=%s" % self.dnsmasq_run_as_user,
            "--no-hosts",
            _no_resolv,
            "--strict-order",
            "--except-interface=lo",
            "--pid-file=%s" % pid_file,
            "--dhcp-hostsfile=%s" % self._get_conf_file_name("host"),
            "--dhcp-optsfile=%s" % self._get_conf_file_name("opts"),
            "--dhcp-leasefile=%s" % self._get_conf_file_name("leases"),
        ]
        cmd += [
            "--bind-interfaces",
            "--interface=%s" % self.interface,
        ]

        if ("dhcp_lease_duration" not in self.network) or (self.network["dhcp_lease_duration"]["seconds"] == -1):
            lease = "infinite"
        else:
            lease = "%ss" % self.network["dhcp_lease_duration"]["seconds"]

        if self.network.get("cidr"):
            cidr = netaddr.IPNetwork(self.network["cidr"])
            cmd.append("--dhcp-range={},{},{},{}".format(cidr.network, "static", cidr.netmask, lease))

        # http://lists.thekelleys.org.uk/pipermail/dnsmasq-discuss/2004q4/000006.html
        if self.network.get("gateway_ip"):
            cmd.append("--dhcp-option=3,%s" % self.network["gateway_ip"])

        # DHCP Option 6 provides DNS server IP addresses to hosts on a network
        # https://www.rfc-editor.org/rfc/rfc2132#section-3.8
        dns_servers_str = self.network.get("dns_servers", None)
        if dns_servers_str:
            cmd.append("--dhcp-option=6,%s" % dns_servers_str.replace(" ", ""))

        """We need to output a separate empty conf_file so that /etc/dnsmasq.cof will not be read"""
        cmd.append("--conf-file=%s" % self.dnsmasq_conf_file_path)
        logger.info("_build_cmdline_callback returns: {}".format(" ".join(str(e) for e in cmd)))
        return cmd

    def enable(self):
        logger.info("Enable dhcp for network: {}".format(self.network["network_uuid"]))
        """ensure devices are ready"""
        self.device_manager.setup_device_for_dnsmasq()
        self.interface = self.device_manager.get_dnsmasq_interface()
        """create dnsmasq for network for the first time, manage dnsmasq life cycle"""
        self._spawn_or_reload_process(reload_with_HUP=False)

    # this func need to be synced
    def _spawn_or_reload_process(self, reload_with_HUP):
        """Spawns or reloads a Dnsmasq process for the network.

        When reload_with_HUP is True, dnsmasq receives a HUP signal,
        otherwise it's spawned if the process is not running.

        When there is a new mac to ip_addr mapping being added, we don't
        append to hostfile, and send HUP signal. Instead, we fallback to
        output_config_files function, which triggers a resync with mongodb
        on all the existed mappings, and send HUP signal afterwards.
        """

        self._output_config_files()

        pm = self._get_process_manager(cmd_callback=self._build_cmdline_callback)

        pm.enable(reload_cfg=reload_with_HUP)

    def _release_lease(self, mac_address, ip):
        """Release a DHCP lease."""
        cmd = ["dhcp_release", self.interface, ip, mac_address]
        try:
            network_utils.IpNetnsCommand.execute(cmd, namespace=self.namespace)
        except RuntimeError as e:
            # when failed to release single lease there's
            # no need to propagate error further
            logger.error("DHCP release failed for %(cmd)s. Reason: %(e)s", {"cmd": cmd, "e": e})

    def reload_allocations(self):
        """each reload will cause a regereration of hostfile and reload by SIGHUP"""
        """this may need to be updated"""
        self._spawn_or_reload_process(reload_with_HUP=True)
        logger.info("Reload allocations for network: %s", self.network["network_uuid"])

    def _output_init_lease_file(self):
        # do we need to migrate lease file from previous master during reshuffle?
        """pleceholder for output lease file"""
        pass

    def _output_config_files(self):
        self._output_dnsmasq_conf_file()
        self._output_hosts_file()
        self._output_addn_hosts_file()
        self._output_opts_file()

    def _output_dnsmasq_conf_file(self):
        """Output an empty dnsmasq.conf file under network_dir"""
        file_utils.touch_file(self.dnsmasq_conf_file_path)

    def _output_hosts_file(self):
        buf = six.StringIO()
        fn = self._get_conf_file_name("host")
        for host_tuple in self.ip_addr_allocator.get_macipmappings_for_network():
            mac_addr = host_tuple.get("mac", None)
            ip_addr = host_tuple.get("ip", None)
            hostname = host_tuple.get("hostname", None)
            if hostname:
                buf.write("{},{},{}\n".format(mac_addr, hostname, ip_addr))
            else:
                buf.write("{},{}\n".format(mac_addr, ip_addr))
        file_utils.replace_file(fn, buf.getvalue())
        buf.close()
        return fn

    def _output_addn_hosts_file(self):
        pass

    def _output_opts_file(self):
        """generate gateway, dns, etc config as dhcp-options for the given network"""
        pass

    def _get_pid_file_name(self):
        return self._get_conf_file_name("pid")

    def _get_conf_file_name(self, kind):
        return os.path.join(self.network_conf_dir, kind)

    def disable(self, retain_interface=False, retain_namespace=False):
        self._get_process_manager().disable()
        if (not retain_interface) and (not retain_namespace):
            """remove both namespaces and devices"""
            self.device_manager.disable()
        elif retain_namespace:
            self.device_manager.cleanup_stale_devices()
        else:
            """should never be here"""
            logger.error(
                "Call disable with param retain_interface:{}, \
                    retain_namespace: {}".format(retain_interface, retain_namespace)
            )
        self._remove_config_files()

    def _remove_config_files(self):
        shutil.rmtree(self.network_conf_dir, ignore_errors=True)

    def _get_process_manager(self, cmd_callback=None):
        if self.pm:
            return self.pm
        else:
            self.pm = external_process.ProcessManager(
                uuid=self.network["network_uuid"],
                namespace=self.namespace,
                default_cmd_callback=cmd_callback,
                pid_file=self._get_pid_file_name(),
            )
            return self.pm

    def reserve_ip_for_nic(self, req):
        ip = None
        mac_ip_mapping = None
        try:
            ip = self.ip_addr_allocator.reserve_ip_for_nic(req)
            if ip:
                self.reload_allocations()
                mac_ip_mapping = {}
                mac_ip_mapping["mac"] = req["mac"]
                mac_ip_mapping["ip"] = ip
            else:
                logger.error("Failed to reserve_ip_for_nic with mac_addr: {}".format(req["mac"]))
        except Exception as e:
            logger.error("Exception caught when calling reserve_ip_for_nic: {}".format(str(e)))
            raise
        else:
            return mac_ip_mapping

    def get_ip_for_nic(self, req):
        ip = None
        try:
            mac = req["mac"]
            ip = self.ip_addr_allocator.get_ip_for_nic(mac)
            if not ip:
                logger.error("Failed to get_ip_for_nic with mac_addr: {}".format(mac))
        except Exception as e:
            logger.error("Exception caught when call get_ip_for_nic: {}".format(str(e)))
            raise
        else:
            return ip

    def list_ip_for_nic(self, req=None):
        search_filter = None
        if req and req.get("macs", None):
            macs = req.get("macs")
            if not isinstance(macs, list | tuple):
                macs = [macs]  # single mac address
            search_filter = {"mac": {"$in": macs}}
        elif req and req.get("ips", None):
            ips = req.get("ips")
            if not isinstance(ips, list | tuple):
                ips = [ips]  # single ip address
            search_filter = {"ip": {"$in": ips}}
        return self.ip_addr_allocator.get_macipmappings_for_network(search_filter)

    def filter_available_ips(self, ips):
        return self.ip_addr_allocator.filter_available_ips(ips)

    def search_mappings_by_ip_text(self, ip_text):
        return self.ip_addr_allocator.search_mappings_by_ip_text(ip_text)

    def update_heartbeat(self, macs, timestamp):
        update_filter = None
        if not isinstance(macs, list | tuple):
            macs = [macs]  # single mac address
        update_filter = {"mac": {"$in": macs}}
        return self.ip_addr_allocator.update_heartbeat(update_filter, timestamp)

    def reclaim_dangle_ips(self, macipmapping_max_timeout):
        reclaimed_macipmappings = self.ip_addr_allocator.reclaim_dangle_ips(macipmapping_max_timeout)
        if reclaimed_macipmappings and len(reclaimed_macipmappings) > 0:
            logger.info(
                "macipmappings: {} get reclaimed for network: {}".format(
                    json.dumps(reclaimed_macipmappings), self.network.get("network_uuid")
                )
            )
            self.reload_allocations()

    def update_ip_for_nic(self, req):
        ip = None
        try:
            ip = self.ip_addr_allocator.update_ip_for_nic(req)
            if ip:
                self.reload_allocations()
            else:
                logger.error("Failed to update_ip_for_nic with mac_addr: {}".format(req["mac"]))
        except Exception as e:
            logger.error("Exception caught when calling update_ip_for_nic: {}".format(str(e)))
            raise
        else:
            return ip

    def remove_ip_for_nic(self, data):
        ip = None
        try:
            ip = self.ip_addr_allocator.remove_ip_for_nic(data)
            if ip:
                self.reload_allocations()
            else:
                logger.error("Failed to remove_ip_for_nic with mac_addr: {}".format(data["mac"]))
        except Exception as e:
            logger.error("Exception caught when call remove_ip_for_nic: {}.".format(str(e)))
            raise
        else:
            return ip
