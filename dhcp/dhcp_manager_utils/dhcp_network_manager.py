# Copyright (c) 2013-2018, SMARTX
# All rights reserved.

import atexit
from functools import wraps
import json
import logging
import os
import re
import signal
import threading
import time
import uuid

from netaddr import IPAddress, IPNetwork

from common.config.constant import ZBS_CONFIG_FILE
from common.lib.cfg import Config
from dhcp.common import constants, external_process, file_utils, network_utils
from dhcp.common.exceptions import InternalError, InvalidArgumentError, NoResourceAvailableError, NotFoundResourceError
from dhcp.common.mongodb import ip_pools_dao, networks_dao
from dhcp.dnsmasq_utils import dnsmasq
from tuna.cluster.zk_manager import ZKManager  # pragma: no cover

logger = logging.getLogger("dhcp_network_manager")

"""Flask server based on WSGI is single threaded by default,
   periodic resync is another thread."""
lock = threading.RLock()

"""synchronize write operations to the data source"""


def synchronized(my_func):
    """synchronize function calls using re-entrant lock"""

    @wraps(my_func)
    def wrapper(*args, **kwargs):
        try:
            lock.acquire()
            t = threading.currentThread()
            logger.info("Thread: {} acquired the lock, with func: {}".format(t.name, my_func.__name__))
            return my_func(*args, **kwargs)
        finally:
            logger.info("Thread: {} released the lock, with func: {}".format(t.name, my_func.__name__))
            lock.release()

    return wrapper


# sync networks managed by dhcp_manager
class DhcpNetworkManager:
    """manage dnsmasq processes for all the networks"""

    DEDAULT_DHCP_LEASE_IN_SECONDS = 43200
    MAX_NUM_OF_ALLOCABLE_DHCP_IPS = 4096  # allow at most a /20 CIDR block

    def __init__(self, macipmapping_max_timeout=86400, elf_heartbeat_max_interval=86400, resync_interval=200):
        self.macipmapping_max_timeout = macipmapping_max_timeout
        self.elf_heartbeat_max_interval = elf_heartbeat_max_interval
        self.resync_interval = resync_interval
        self.threads_map = {}
        self.uuid = str(uuid.uuid4())
        self.managed_networks_uuids = []  # networks activly managed by dhcp_network_mgr
        self.uuid_to_network_mapping = {}  # map network_uuid to network descriptions
        self.uuid_to_driver_mapping = {}  # map network_uuid to dnsmasq drivers
        self.working_dir = constants.WORKING_DIR
        file_utils.ensure_tree(self.working_dir, mode=0o755)
        self.ready_for_api_call = False
        self.register_exit_handler()
        self.__networks_dao = networks_dao.NetworksDAO(
            constants.MONGO_DB, constants.MONGO_COLLECTION_MAP["NETWORKS_TABLE"]
        )
        self.__ip_pools_dao = ip_pools_dao.IpPoolsDAO(constants.MONGO_DB, constants.MONGO_COLLECTION_MAP["IP_POOLS"])

    def run(self):
        self.init_when_start()
        self.periodic_resync()

    def init_when_start(self, backoff_interval=60):
        start_time = time.time()
        while True:
            try:
                if self.is_master():
                    self.sync_state_as_leader()
                else:
                    self.sync_state_as_slave()
            except Exception as e:
                logger.exception("Exception caught during initialization: {}".format(e))
                time.sleep(backoff_interval)
            else:
                duration = time.time() - start_time
                logger.info("Finished initialization, takes {} seconds.".format(duration))
                return

    @synchronized
    def sync_state_as_leader(self):
        """sync local dhcp state between mongodb"""
        """so far we don't support network update"""
        logger.info("start syncing state as leader.")
        start_time = time.time()
        active_networks_uuids = self.get_network_uuids_from_data_source()
        common_networks_uuids = list(set(active_networks_uuids).intersection(self.managed_networks_uuids))
        new_networks_uuids = list(set(active_networks_uuids) ^ set(common_networks_uuids))
        logger.info("Found newly created networks: {}.".format(" ".join(str(e) for e in new_networks_uuids)))
        stale_networks_uuids = list(set(self.managed_networks_uuids) ^ set(common_networks_uuids))
        if len(stale_networks_uuids) > 0:
            logger.info("Found stale networks: {}.".format(" ".join(str(e) for e in stale_networks_uuids)))
            for networks_uuid in stale_networks_uuids:
                self.remove_dhcp_for_network(networks_uuid)
        for network_uuid in new_networks_uuids:
            """setup network devices to get ready for """
            self.managed_networks_uuids.append(network_uuid)
        for network_uuid in self.managed_networks_uuids:
            self.configure_dhcp_for_network(network_uuid)
        """sync with local data plane"""
        self.delete_orphan_dnsmasq_process()
        self.delete_stale_namespaces()
        self.reclaim_dangle_ips()
        self.ready_for_api_call = True
        duration = time.time() - start_time
        logger.info("Finished syncing state as leader, takes {} seconds.".format(duration))

    @synchronized
    def sync_state_as_slave(self):
        logger.info("Sync state as slave - remove dnsmasq from data plane.")
        start_time = time.time()
        for network_uuid in self.managed_networks_uuids:
            self.disable_dhcp_for_network(network_uuid)
            logger.info("Remove network_uuid:{} from managed_networks_uuids.".format(network_uuid))
            self.managed_networks_uuids[:] = [uuid for uuid in self.managed_networks_uuids if uuid != network_uuid]
        logger.info("Clean up uuid_to_driver_mapping.")
        self.uuid_to_driver_mapping.clear()
        logger.info("Clean up uuid_to_network_mapping.")
        self.uuid_to_network_mapping.clear()
        """Clean orphan dnsmasq processes. Up to here since managed_networks_uuids is empty, all the dnsmasq procs
           in data plane are treated as orphan procs."""
        self.delete_orphan_dnsmasq_process()
        self.delete_stale_namespaces()
        self.ready_for_api_call = False
        duration = time.time() - start_time
        logger.info("Finished syncing state as slave, takes {} seconds.".format(duration))

    def periodic_resync(self):
        t = threading.Thread(name=self.uuid, target=self._periodic_resync_helper)
        self.threads_map[self.uuid] = t
        t.start()

    def _periodic_resync_helper(self):
        while True:
            time.sleep(self.resync_interval)
            try:
                if self.is_master():
                    self.sync_state_as_leader()
                else:
                    self.sync_state_as_slave()
            except Exception as e:
                """ Do not crash sync thread """
                logger.exception("Exception caught when syncing state: {}.".format(e))

    def configure_dhcp_for_network(self, network_uuid):
        # check if network is ready
        logger.info("configure_dhcp_for_network: {}".format(network_uuid))
        network = None
        if network_uuid not in self.uuid_to_network_mapping:
            logger.info(
                "Failed to find network_uuid:{} from uuid_to_network_mapping, retrive from data source.".format(
                    network_uuid
                )
            )
            """retrive network from data source"""
            network = self.__networks_dao.get_network_by_id(network_uuid)
            if not network:
                logger.error("Failed to find network:{} from data source.".format(network_uuid))
                return
            self.uuid_to_network_mapping[network_uuid] = network
        else:
            network = self.uuid_to_network_mapping.get(network_uuid)

        """initialize dnsmasq driver for given network"""
        if network_uuid in self.uuid_to_driver_mapping:
            """sync mac to ip mappings with mongo and signal SIGHUP"""
            logger.info("Reload allocations for network: {}".format(network_uuid))
            driver = self.uuid_to_driver_mapping.get(network_uuid)
            """reload will first check if process is up, if not, calls enable instead"""
            driver.reload_allocations()
        else:
            """new network being created"""
            logger.info("Initialize driver for network: {}".format(network_uuid))
            network_conf_dir = os.path.join(self.working_dir, network_uuid)
            driver = dnsmasq.Dnsmasq(
                network=network, network_conf_dir=network_conf_dir, ip_pools_dao=self.__ip_pools_dao
            )
            driver.enable()
            self.uuid_to_driver_mapping[network_uuid] = driver

    def disable_dhcp_for_network(self, network_uuid=None):
        logger.info("Call disable_dhcp_for_network for network: {}".format(network_uuid))
        if not network_uuid:
            return False
        if network_uuid in self.uuid_to_driver_mapping:
            driver = self.uuid_to_driver_mapping[network_uuid]
            driver.disable()
            """remove from uuid_to_driver_mapping"""
            del self.uuid_to_driver_mapping[network_uuid]
            return True
        else:
            """should never happen, log error and raise exception"""
            logger.error("Failed to find network_uuid: {} from uuid_to_driver_mapping".format(network_uuid))
            return False

    def get_network_uuids_from_data_source(self):
        """get active networks from data source"""
        return self.__networks_dao.get_network_uuids()

    def remove_network_from_data_source(self, network_uuid):
        """remove all data objects from mongo"""
        ret_val = True
        """collection.drop does not return anything..."""
        if network_uuid in self.uuid_to_driver_mapping:
            driver = self.uuid_to_driver_mapping[network_uuid]
            driver.drop_allocations_for_network()
        else:
            logger.error("Failed to find driver for network: {}.".format(network_uuid))
            ret_val = False
        if not self.__ip_pools_dao.remove_ip_pool_for_network(network_uuid):
            logger.error("Failed to remove ip pool for network: {}".format(network_uuid))
            ret_val = False
        if not self.__networks_dao.delete_network_by_id(network_uuid):
            logger.error("Failed to remove network info for network: {}".format(network_uuid))
            ret_val = False
        return ret_val

    def remove_dhcp_for_network(self, network_uuid):
        logger.info("Cleanup dhcp settings for network_uuid: {}".format(network_uuid))
        """remove related objects from data source"""
        if not self.remove_network_from_data_source(network_uuid):
            logger.error("Failed to remove network: {} from data source".format(network_uuid))
        """stop dnsmasq process"""
        self.disable_dhcp_for_network(network_uuid)
        if network_uuid in self.uuid_to_network_mapping:
            del self.uuid_to_network_mapping[network_uuid]

        logger.info("Remove network_uuid:{} from managed_networks_uuids.".format(network_uuid))
        """Should remove network_uuid only if above steps succeed, so that this procedure will get retried"""
        self.managed_networks_uuids[:] = [uuid for uuid in self.managed_networks_uuids if uuid != network_uuid]
        return True

    def get_dhcp_manager_leader_ip(self):
        server_host, _ = ZKManager().get_leader()  # pragma: no cover
        return server_host  # pragma: no cover

    def get_data_ip(self):
        cfg = Config(ZBS_CONFIG_FILE)
        return cfg.get("network", "data_ip")

    def is_master(self):
        leader_ip = self.get_dhcp_manager_leader_ip()
        logger.info("Meta leader ip: {}.".format(leader_ip))
        data_ip = self.get_data_ip()
        logger.info("Data ip: {}.".format(data_ip))
        return leader_ip == data_ip

    def delete_stale_namespaces(self):
        ns_data_plane = self._get_netns_from_data_plane()
        stale_ns = []
        for ns in ns_data_plane:
            if ns.startswith(constants.DHCP_NS_PREFIX):
                network_uuid = ns[len(constants.DHCP_NS_PREFIX) :]
            else:
                """if ns not start with constants.DHCP_NS_PREFIX, ignore."""
                logger.error("Call _get_netns_from_data_plane returned malformed namespace: {}".format(ns))
                continue
            if network_uuid not in self.managed_networks_uuids:
                stale_ns.append(ns)
        for ns in stale_ns:
            """remove devices from ns"""
            """remove ns"""
            logger.info("Remove stale network namespace: {}".format(ns))
            network_utils.IpNetnsCommand.remove_network_namespace(ns)

    def _get_netns_from_data_plane(self):
        namespaces = network_utils.IpNetnsCommand.list_network_namespaces()
        managed_namespaces = []
        for ns in namespaces:
            if ns.startswith(constants.DHCP_NS_PREFIX):
                logger.info("Found namespace: {} from data plane.".format(ns))
                managed_namespaces.append(ns)
        return managed_namespaces

    def delete_orphan_dnsmasq_process(self):
        dnsmasq_proc_list = self._get_dnsmasq_from_data_plane()
        orphan_proc_list = []
        if dnsmasq_proc_list is None or len(dnsmasq_proc_list) == 0:  # pragma: no cover
            logger.warning("Found 0 dnsmasq process from data plane.")
            return
        for proc in dnsmasq_proc_list:
            network_uuid, pid_file = self._get_network_uuid_from_cmdline(proc.cmdline())
            """only remove dnsmasq procs created by dhcp_network_mgr"""
            if network_uuid and (network_uuid not in self.managed_networks_uuids):
                orphan_proc_list.append((proc, pid_file))
        for tup in orphan_proc_list:
            proc = tup[0]
            pid_file = tup[1]
            logger.info(
                "Remove orphan dnsmasq process with pid: {}, cmdline: {}.".format(proc.pid, " ".join(proc.cmdline()))
            )
            external_process.ProcessManager.disable_by_pid(pid=proc.pid, pid_file=pid_file)

    def _get_dnsmasq_from_data_plane(self):
        return external_process.ProcessManager.get_all_dnsmasq_processes()

    def _get_network_uuid_from_cmdline(self, cmdline):
        """retrieve network_uuid from dnsmasq cmdline"""
        for section in cmdline:
            """example: --pid-file=/var/dhcp_manager/ad1f071d-55ba-48d1-944c-90263a5d34e6/pid"""
            if section.startswith("--pid-file="):
                pid_file = section[len("--pid-file=") :]
                if pid_file.startswith("/var/dhcp_manager/"):
                    parts = pid_file[len("/var/dhcp_manager/") :].split("/")
                    if len(parts) == 2:
                        logger.info("Found network_uuid: {} from cmd: {}.".format(parts[0], " ".join(cmdline)))
                        return parts[0], pid_file
        logger.info("Failed to find network_uuid from malformed cmdline: {}.".format(" ".join(cmdline)))
        return None, None

    def reclaim_dangle_ips(self):
        cur_time = int(time.time())
        for network_uuid in list(self.uuid_to_driver_mapping.keys()):
            last_heartbeat_timestamp = self.__networks_dao.get_last_heartbeat_timestamp(network_uuid)
            logger.info(
                "Last heartbeat timestamp for network: {} is {}.".format(network_uuid, last_heartbeat_timestamp)
            )
            if last_heartbeat_timestamp and (cur_time - last_heartbeat_timestamp < self.elf_heartbeat_max_interval):
                logger.info("Run reclaim dangle ip for network: {}.".format(network_uuid))
                driver = self.uuid_to_driver_mapping.get(network_uuid)
                driver.reclaim_dangle_ips(self.macipmapping_max_timeout)
            else:
                logger.info("Skip reclaim dangle ip for network: {}.".format(network_uuid))

    """serve api calls"""

    def _ensure_ready_for_api_calls(self):
        """make sure dhcp_network_mgr sync at least once before answering api calls"""
        if self.is_master() and (not self.ready_for_api_call):
            logger.info("Sync state before answering API calls.")
            """Since sync and api funcs are guarded by re-entrant lock, there should be no deadlock here"""
            self.sync_state_as_leader()

    @synchronized
    def reserve_ip_for_nic(self, req):
        """
        { 'network_uuid': '559e3357-1531-4176-a8d1-4740e2ffe1c3',
          'mac': '9e:78:5c:5a:a8:b0'}
        """
        logger.info("Call reserve_ip_for_nic with data: {}.".format(json.dumps(req)))
        self._ensure_ready_for_api_calls()
        if req["network_uuid"] in self.uuid_to_driver_mapping:
            driver = self.uuid_to_driver_mapping[req["network_uuid"]]
            return driver.reserve_ip_for_nic(req)
        else:
            msg = "Failed to find network: {} from existing networks.".format(req["network_uuid"])
            logger.error(msg)
            raise NotFoundResourceError(msg)

    def get_ip_for_nic_helper(self, mac, network_uuid=None):
        if network_uuid in self.uuid_to_driver_mapping:
            driver = self.uuid_to_driver_mapping[network_uuid]
            data = {"mac": mac}  # should remove this in the future
            return driver.get_ip_for_nic(data)
        else:
            msg = "Failed to find network: {} from existing networks.".format(network_uuid)
            logger.error(msg)
            raise NotFoundResourceError(msg)

    def get_ip_for_nic(self, mac, network_uuid=None):
        """
        { 'network_uuid': '559e3357-1531-4176-a8d1-4740e2ffe1c3',
          'mac': '9e:78:5c:5a:a8:b0'}
        """
        logger.info("Call get_ip_for_nic with mac: {}, network_uuid: {}.".format(mac, network_uuid))
        self._ensure_ready_for_api_calls()
        return self.get_ip_for_nic_helper(mac, network_uuid)

    def list_macipmappings_helper(self, req=None):
        """If 'network_uuid' exists in req, list macipmappings for that network.
        If 'macs' or 'ips' exists in req, list macipmappings for all networks filter by these fields.
        If none of above keys exist, list macipmappings for all network without filter.
        """
        if req and req.get("network_uuid", None):
            network_uuid = req.get("network_uuid")
            logger.info("List macipmappings for network: {}.".format(network_uuid))
            if network_uuid in self.uuid_to_driver_mapping:
                driver = self.uuid_to_driver_mapping[network_uuid]
                response = driver.list_ip_for_nic()
                return response
            else:
                msg = "Failed to find network: {} from existing networks.".format(network_uuid)
                logger.error(msg)
                raise NotFoundResourceError(msg)
        elif req and (("macs" in req) or ("ips" in req)):
            """logically only one of mac or ip should be in data"""
            logger.info("List macipmappings for all networks using filter: {}".format(json.dumps(req)))
            response = []
            for driver in list(self.uuid_to_driver_mapping.values()):
                items = driver.list_ip_for_nic(req)
                if items:
                    response.extend(items)
            return response
        else:
            logger.info("List all mac_ip mappings for all networks without pagination.")
            response = []
            for driver in list(self.uuid_to_driver_mapping.values()):
                items = driver.list_ip_for_nic()
                if items:
                    response.extend(items)
            return response

    def list_macipmappings(self, req=None):
        logger.info("Call list_macipmappings with data: {}.".format(json.dumps(req)))
        self._ensure_ready_for_api_calls()
        return self.list_macipmappings_helper(req)

    def batch_get_macipmappings(self, req):
        self._ensure_ready_for_api_calls()
        network_uuid = None
        if req and "network_uuid" in req:
            network_uuid = req["network_uuid"]
            logger.info("batch_get_mac_ip_mappings with network_uuid: {}.".format(network_uuid))
        else:
            logger.info("batch_get_mac_ip_mappings for all networks.")
        response = []
        if network_uuid:
            """batch get for specified network"""
            for mac in req["macs"]:
                ip = self.get_ip_for_nic_helper(mac, network_uuid)
                if ip:
                    name = "dhcps/{}/macIps/{}".format(network_uuid, mac)
                    response.append({"mac": mac, "ip": ip, "name": name})
            return response
        else:
            """batch get for all networks"""
            response.extend(self.list_macipmappings_helper(req))
            return response

    @synchronized
    def heartbeat_macipmappings(self, req):
        self._ensure_ready_for_api_calls()
        network_uuid = req.get("network_uuid", None)
        macs = req.get("macs", [])
        logger.info("Call heartbeat_macipmappings for network: {} with macs: {}".format(network_uuid, json.dumps(macs)))
        if network_uuid in self.uuid_to_driver_mapping:
            cur_time = int(time.time())
            driver = self.uuid_to_driver_mapping.get(network_uuid)
            res = driver.update_heartbeat(macs, cur_time)
            logger.info("Number of records updated for heartbeat: {}.".format(res))
            """update heartbeat time for network"""
            self.__networks_dao.update_last_heartbeat_timestamp(network_uuid, cur_time)
        else:
            msg = "Failed to find network: {} from existing networks.".format(network_uuid)
            logger.error(msg)
            raise NotFoundResourceError(msg)

    def filter_available_ips(self, req):
        network_uuid = req["network_uuid"]
        logger.info(
            "Call filter_available_ips for network: {}, with ips: {}.".format(
                network_uuid, ",".join(str(ip) for ip in req["ips"])
            )
        )
        self._ensure_ready_for_api_calls()
        response = []
        if network_uuid and (network_uuid in self.uuid_to_driver_mapping):
            driver = self.uuid_to_driver_mapping[network_uuid]
            response.extend(driver.filter_available_ips(req["ips"]))
            return response
        else:
            msg = "Failed to find network: {} from existing networks.".format(network_uuid)
            logger.error(msg)
            raise NotFoundResourceError(msg)

    def search_mappings_by_ip_text(self, req):
        logger.info("Call search_mappings_by_text with text: {}.".format(req["ip_text"].encode("utf-8")))
        self._ensure_ready_for_api_calls()
        mappings = []
        if req.get("network_uuid"):
            network_uuid = req["network_uuid"]
            if network_uuid in self.uuid_to_driver_mapping:
                driver = self.uuid_to_driver_mapping[network_uuid]
                mappings.extend(driver.search_mappings_by_ip_text(req["ip_text"]))
            else:
                msg = "Failed to find network_uuid: {} from managed networks.".format(network_uuid)
                logger.error(msg)
                raise InvalidArgumentError(msg)
        else:
            for network_uuid in self.managed_networks_uuids:
                driver = self.uuid_to_driver_mapping[network_uuid]
                mappings.extend(driver.search_mappings_by_ip_text(req["ip_text"]))
        return mappings

    @synchronized
    def update_ip_for_nic(self, req):
        """
        { 'network_uuid': '559e3357-1531-4176-a8d1-4740e2ffe1c3',
          'mac': '9e:78:5c:5a:a8:b0', 'ip':'**********'}
        """
        logger.info("Call update_ip_for_nic with data: {}.".format(json.dumps(req)))
        self._ensure_ready_for_api_calls()
        if req["network_uuid"] in self.uuid_to_driver_mapping:
            driver = self.uuid_to_driver_mapping[req["network_uuid"]]
            return driver.update_ip_for_nic(req)
        else:
            msg = "Failed to find network: {} from existing networks.".format(req["network_uuid"])
            logger.error(msg)
            raise NotFoundResourceError(msg)

    @synchronized
    def remove_ip_for_nic(self, req):
        """
        { 'network_uuid': '559e3357-1531-4176-a8d1-4740e2ffe1c3',
          'mac': '9e:78:5c:5a:a8:b0'}
        """
        logger.info("Call remove_ip_for_nic with request data: {}.".format(json.dumps(req)))
        self._ensure_ready_for_api_calls()
        if req["network_uuid"] in self.uuid_to_driver_mapping:
            driver = self.uuid_to_driver_mapping[req["network_uuid"]]
            return driver.remove_ip_for_nic(req)
        else:
            msg = "Failed to find network: {} from existing networks.".format(req["network_uuid"])
            logger.error(msg)
            raise NotFoundResourceError(msg)

    def _validate_dhcp_configuration(self, network):
        """make sure ip ranges are within network cidr"""
        cidr_string = network.get("cidr", None)
        ip_ranges = network.get("ip_ranges", None)
        cidr = None
        if cidr_string:
            cidr = IPNetwork(cidr_string)
        if cidr and ip_ranges:
            for ip_range in ip_ranges:
                if (IPAddress(ip_range.get("start_ip")) not in cidr) or (IPAddress(ip_range.get("end_ip")) not in cidr):
                    msg = "IP range: {} is not included in cidr: {} for network: {}.".format(
                        ip_range, cidr_string, network.get("network_uuid")
                    )
                    logger.error(msg)
                    raise InvalidArgumentError(msg)

        """ make sure number of allocable IPs is supported """
        num_of_total_dhcp_ips = 0
        if ip_ranges:
            for ip_range in ip_ranges:
                num_of_ips = int(IPAddress(ip_range.get("end_ip"))) - int(IPAddress(ip_range.get("start_ip"))) + 1
                if num_of_ips <= 0:
                    msg = "Start IP for IP range: {} is larger than end IP.".format(ip_range)
                    logger.error(msg)
                    raise InvalidArgumentError(msg)
                num_of_total_dhcp_ips += num_of_ips
        elif cidr:
            num_of_total_dhcp_ips = cidr.size
        else:
            msg = "Failed to identify ip ranges for network: {}.".format(network.get("network_uuid"))
            logger.error(msg)
            raise InvalidArgumentError(msg)
        if num_of_total_dhcp_ips >= self.MAX_NUM_OF_ALLOCABLE_DHCP_IPS:
            msg = "Total number of allocable IPs: {} for network: {} exceeds the limit: {}.".format(
                num_of_total_dhcp_ips, network.get("network_uuid"), self.MAX_NUM_OF_ALLOCABLE_DHCP_IPS
            )
            logger.error(msg)
            raise InvalidArgumentError(msg)

        """ make sure ovs_br, vlan_id pair is unique in the system """
        search_filter = {"ovs_br": network["ovs_br"], "vlan_id": network["vlan_id"]}
        existed_networks = self.__networks_dao.batch_get_networks(search_filter)
        if existed_networks and len(existed_networks) > 0:
            msg = "Network defined by vlan_id: {} and ovs_br: {} already exists.".format(
                network["vlan_id"], network["ovs_br"]
            )
            logger.error(msg)
            raise InvalidArgumentError(msg)

    def _get_next_device_id(self, vlan_id):
        tap_dev_prefix = "{}_{}_{}_".format(constants.DHCP_INTERFACE_PREFIX, "t", vlan_id)
        regx = re.compile("^{}".format(tap_dev_prefix), re.IGNORECASE)
        search_filter = {"tap": regx}
        networks_with_tap = self.__networks_dao.batch_get_networks(search_filter)
        existing_ids = []
        if networks_with_tap and len(networks_with_tap) > 0:
            for network in networks_with_tap:
                if network["tap"].startswith(tap_dev_prefix):
                    parts = network["tap"].split("_")
                    if len(parts) == 4:
                        link_id = int(parts[3])
                        existing_ids.append(link_id)
        for id_ in range(0, 1000):
            if id_ not in existing_ids:
                return id_
        msg = "Failed to find available id for tap device."
        logger.error(msg)
        raise NoResourceAvailableError(msg)

    @synchronized
    def create_dhcp(self, create_dhcp_request):
        dhcp_payload = create_dhcp_request.dhcp
        logger.info("Call create_dhcp with data: {}, type: {}.".format(dhcp_payload, type(dhcp_payload)))
        """copy from dhcp_payload obj to network obj"""
        self._ensure_ready_for_api_calls()
        network_uuid = dhcp_payload.network_uuid
        logger.info("Network_uuid: {}".format(network_uuid))
        if self.__networks_dao.get_network_by_id(network_uuid):
            msg = "Found network with same uuid: {} from data source.".format(network_uuid)
            logger.error(msg)
            raise InvalidArgumentError(msg)
        network = {}
        network["network_uuid"] = network_uuid
        network["name"] = dhcp_payload.name
        network["cidr"] = dhcp_payload.cidr
        network["vlan_id"] = dhcp_payload.vlan_id
        if dhcp_payload.dhcp_interface_ip:
            network["dhcp_interface_ip"] = dhcp_payload.dhcp_interface_ip
        else:
            """use last ip in cidr to setup interface for dnsmasq"""
            last_ip = IPNetwork(network["cidr"])[-1]
            logger.info("Use IP address: {} to setup dnsmasq interface.".format(last_ip))
            network["dhcp_interface_ip"] = str(last_ip)
        if dhcp_payload.dhcp_lease_duration.seconds == 0:
            network["dhcp_lease_duration"] = {"seconds": self.DEDAULT_DHCP_LEASE_IN_SECONDS}
        else:
            network["dhcp_lease_duration"] = {"seconds": int(dhcp_payload.dhcp_lease_duration.seconds)}
        if dhcp_payload.ip_ranges and len(dhcp_payload.ip_ranges) > 0:
            ip_ranges = []
            for ip_range in dhcp_payload.ip_ranges:
                ip_ranges.append({"start_ip": ip_range.start_ip, "end_ip": ip_range.end_ip})
            network["ip_ranges"] = ip_ranges
        if dhcp_payload.gateway_ip:
            network["gateway_ip"] = dhcp_payload.gateway_ip
        else:
            network["gateway_ip"] = None
        if dhcp_payload.dns_servers:
            network["dns_servers"] = dhcp_payload.dns_servers
        else:
            network["dns_servers"] = None
        if dhcp_payload.ovs_br:
            network["ovs_br"] = dhcp_payload.ovs_br
        else:
            msg = "Ovs bridge is not defined for network: {}.".format(network["network_uuid"])
            logger.error(msg)
            raise InvalidArgumentError(msg)
        # setup tap and veth device pair for this network
        dev_id = self._get_next_device_id(network["vlan_id"])
        logger.info("Use id: {} for tap and veth devices for network: {}.".format(dev_id, network["network_uuid"]))
        network["tap"] = "{}_{}_{}_{}".format(constants.DHCP_INTERFACE_PREFIX, "t", network["vlan_id"], dev_id)
        network["veth"] = "{}_{}_{}_{}".format(constants.DHCP_INTERFACE_PREFIX, "v", network["vlan_id"], dev_id)
        self._validate_dhcp_configuration(network)
        if self.__networks_dao.create_new_network(network):
            self.configure_dhcp_for_network(network["network_uuid"])
        else:
            logger.error("Failed to persist data to mongodb.")
        return network

    def get_dhcp(self, network_uuid):
        """this call does not need _ensure_ready_for_api_calls"""
        record = self.__networks_dao.get_network_by_id(network_uuid)
        if not record:
            msg = "Failed to find network: {} from data source.".format(network_uuid)
            logger.warning(msg)
            raise NotFoundResourceError(msg)
        if ("network_uuid" in record) and (record["network_uuid"] == network_uuid):
            return record
        else:
            logger.error("Failed to parse response from get_network_by_id: {}".format(json.dumps(record)))
            msg = "Found corrupted data for network: {}.".format(network_uuid)
            raise InternalError(msg)

    def _get_dhcps_from_network_uuid_list(self, network_uuids):
        """return dhcps whose network_uuid belongs to the list"""
        dhcps = []
        for network_uuid in network_uuids:
            record = self.__networks_dao.get_network_by_id(network_uuid)
            if record and ("network_uuid" in record) and (record["network_uuid"] == network_uuid):
                logger.info("Found dhcp configurations for network_uuid: {}.".format(network_uuid))
                dhcps.append(record)
            else:
                logger.error("DHCP configurations not found for network: {}.".format(network_uuid))
        return dhcps

    def list_dhcps(self):
        logger.info("Call list_dhcps service.")
        self._ensure_ready_for_api_calls()
        network_uuids = self.get_network_uuids_from_data_source()
        return self._get_dhcps_from_network_uuid_list(network_uuids)

    def batch_get_dhcps(self, network_uuids):
        logger.info(
            "Call batch_get_dhcps with network_uuid list: {}".format(
                ",".join(str(network_uuid) for network_uuid in network_uuids)
            )
        )
        self._ensure_ready_for_api_calls()
        return self._get_dhcps_from_network_uuid_list(network_uuids)

    @synchronized
    def delete_dhcp(self, network_uuid):
        logger.info("Call delete_dhcp with network_uuid: {}.".format(network_uuid))
        self._ensure_ready_for_api_calls()
        return self.remove_dhcp_for_network(network_uuid)

    def register_exit_handler(self):
        """to avoid orphan dnsmasq process"""
        atexit.register(self._handle_exit)
        for sig in constants.SIG_LIST:
            logger.info("Register exit handler for signal: {}".format(sig))
            signal.signal(sig, self._handle_exit)

    def _handle_exit(self, signum=None, frame=None):
        logger.info("Execute exit handler.")
        for network_uuid in self.managed_networks_uuids:
            logger.info("Disable dhcp for network: {}".format(network_uuid))
            self.disable_dhcp_for_network(network_uuid)
            """remove network_uuid from list in place"""
            self.managed_networks_uuids[:] = [uuid for uuid in self.managed_networks_uuids if uuid != network_uuid]
        logger.info("Finish disable dhcp for all networks.")
        """In the future, we should replace this line by gracefully shutdown sync and flask threads"""
        os._exit(0)
