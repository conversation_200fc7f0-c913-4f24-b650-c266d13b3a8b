# Copyright (c) 2013-2015, SMARTX
# All rights reserved.

from contextlib import ExitStack
from unittest.mock import (  # noqa: I101,I201
    patch,
    Mock,
)
import pytest


@pytest.fixture
def mocked_requests():
    r = Mock()
    r.get.return_value = "hello_world"
    return r


@pytest.fixture
def mocked_parse():
    return Mock()


def test00_should_fetch_meta(mocked_parse):
    import requests  # noqa: F401
    from schema_sugar.client import RestClient

    with patch("requests.get") as get:
        return_meta = {
            "/api/v2 datastores": {},
        }
        response = get.return_value = Mock()
        response.json.return_value = return_meta
        client = RestClient(mocked_parse)
        assert client.meta == return_meta


def test10_should_use_correct_method(mocked_parse):
    from schema_sugar.client import RestClient

    status = {"method": None}

    def mocked_get(*arg, **kwargs):
        status["method"] = "get"

    def mocked_post(*arg, **kwargs):
        status["method"] = "post"

    with ExitStack() as stack:
        stack.enter_context(patch("requests.get", mocked_get))
        stack.enter_context(patch("requests.post", mocked_post))
        stack.enter_context(patch.object(RestClient, "_fetch_meta", Mock()))
        client = RestClient(mocked_parse)
        client.send_request(
            "/api/v2", "GET", params=None,
        )
        assert status["method"] == "get"
        client.send_request("/api/v2", "PosT")
        assert status["method"] == "post"


def test11_extra_actions_in_rules():
    support_operations = ["index", "update", "create", "delete", "show", "rebuild", "clone", "move", "bulk_delete"]
    extra_actions = {
        "rebuild": {"http_method": "post"},
        "clone": {"http_method": "post"},
        "move": {"http_method": "post"},
    }

    def _return_rules(support_operations, extra_actions):
        from schema_sugar.constant import OP2HTTP_MAP
        from schema_sugar.client.parser import _mk_url as mk_url

        res_name = "test"
        url_prefix = "/api/v2"
        is_singular = False
        return dict(
            list(map(
                lambda op: (
                    (op, {"url": mk_url(res_name, url_prefix, is_singular, op), "method": OP2HTTP_MAP[op]})
                    if OP2HTTP_MAP.get(op, None)
                    else (
                        op,
                        {
                            "url": mk_url(res_name, url_prefix, is_singular, op),
                            "method": extra_actions.get(op).get("http_method"),
                        },
                    )
                ),
                support_operations,
            ))
        )

    rules = _return_rules(support_operations, extra_actions)
    assert rules["index"]["url"] == "/api/v2/test"
    assert rules["create"]["url"] == "/api/v2/test"
    assert rules["update"]["url"] == "/api/v2/test/<id>"
    assert rules["show"]["url"] == "/api/v2/test/<id>"
    assert rules["delete"]["url"] == "/api/v2/test/<id>"
    assert rules["bulk_delete"]["url"] == "/api/v2/test"
    assert rules["rebuild"]["url"] == "/api/v2/test/<id>/rebuild"
    assert rules["clone"]["url"] == "/api/v2/test/<id>/clone"
    assert rules["move"]["url"] == "/api/v2/test/<id>/move"

    assert rules["index"]["method"] == "get"
    assert rules["create"]["method"] == "post"
    assert rules["update"]["method"] == "put"
    assert rules["show"]["method"] == "get"
    assert rules["delete"]["method"] == "delete"
    assert rules["bulk_delete"]["method"] == "delete"
    assert rules["rebuild"]["method"] == "post"
    assert rules["clone"]["method"] == "post"
    assert rules["move"]["method"] == "post"


if __name__ == "__main__":
    test11_extra_actions_in_rules()
