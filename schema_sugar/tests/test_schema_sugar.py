from abc import abstractmethod

import unittest
from jsonschema import ValidationError  # noqa: I100

from schema_sugar import (  # noqa: I101,I202
    method2op,
    resources_method2op,
    is_abs_method,
    JsonForm,
    cli_arg_generator,
    OutputSchemaValidator,
    SugarConfig,
    SchemaSugarBase,
)
from schema_sugar.exceptions import OutputSchemaError  # noqa: I201


class TestMethod2OPCONV(unittest.TestCase):
    def test_method2op_letter_case(self):
        self.assertEqual(method2op("GET"), "show")
        self.assertEqual(method2op("get"), "show")
        self.assertEqual(method2op("Show"), "show")

    def test_method2op_name_conv(self):
        input_values = [
            "get",
            "put",
            "post",
            "delete",
            "show",
            "update",
            "create",
            "delete",
        ]
        expect_values = [
            "show",
            "update",
            "create",
            "delete",
            "show",
            "update",
            "create",
            "delete",
        ]
        for input_value, expect_value in zip(input_values, expect_values):
            self.assertEqual(method2op(input_value), expect_value)

    def test_method2op_unexpected(self):
        e = None
        try:
            method2op("haha")
        except ValueError as ve:
            e = ve
            pass

        self.assertIsNotNone(e)


class TestResourcesMethod2OPCONV(unittest.TestCase):
    def test_resources_method2op(self):
        input_values = ["post", "get", "POST", "mymethod"]
        expect_values = ["create", "index", "create", "mymethod"]
        for input_value, expect_value in zip(input_values, expect_values):
            self.assertEqual(resources_method2op(input_value), expect_value)


class TestIsAbcMethod(unittest.TestCase):
    def setUp(self):
        self.is_abs_method = is_abs_method

    def test_normal_func(self):
        def normal():
            pass

        @abstractmethod
        def is_abc():
            pass

        self.assertTrue(self.is_abs_method(is_abc))
        self.assertFalse(self.is_abs_method(normal))

    def test_class_method(self):
        class Test:
            @abstractmethod
            def test_method(self):
                pass

            def normal_method(self):
                pass

            @classmethod
            @abstractmethod
            def cls_method(cls):
                pass

        test = Test()

        self.assertTrue(self.is_abs_method(test.test_method))
        self.assertTrue(self.is_abs_method(Test.cls_method))
        self.assertFalse(self.is_abs_method(test.normal_method))


class TestJsonForm(unittest.TestCase):
    def setUp(self):
        self.JsonForm = JsonForm
        self.base_schema = {
            "type": "object",
            "properties": {"field1": {"type": "string"}, "field2": {"type": "string"}},
            "required": ["field1"],
        }
        self.base_valid_data = {"field1": "haha"}

    def test_normal_schema(self):
        class MyForm(self.JsonForm):
            schema = self.base_schema

        form = MyForm(self.base_valid_data)
        self.assertEqual(self.base_schema, form.schema)

    def test_live_schema(self):
        form = self.JsonForm(self.base_valid_data, live_schema=self.base_schema)

        self.assertEqual(form.schema, self.base_schema)

    # def test_both_schema(self):
    #     class MyForm(self.JsonForm):
    #         schema = self.base_schema
    #
    #     live_schema = {
    #         "type": "object",
    #         "properties": {"field2": {"type": "number"}, "field3": {"type": "string"}},
    #         "required": ["field2"],
    #     }
    #     form = MyForm({}, live_schema=live_schema)
    #     self.assertEqual(
    #         form.schema,
    #         {
    #             "type": "object",
    #             "properties": {
    #                 "field1": {"type": "string"},
    #                 "field2": {"type": "number"},
    #                 "field3": {"type": "string"},
    #             },
    #             "required": ["field1", "field2"],
    #         },
    #     )

    def test_validation_success(self):
        class MyForm(self.JsonForm):
            schema = self.base_schema

        form = MyForm(self.base_valid_data)
        self.assertTrue(form.validate())

    def test_validataion_fail(self):
        class MyForm(self.JsonForm):
            schema = self.base_schema

        form = MyForm({})
        self.assertFalse(form.validate())
        self.assertIsNotNone(form.errors)

    def test_data_filter(self):
        class MyForm(self.JsonForm):
            schema = self.base_schema

        form = MyForm({"field1": "hello", "field2": "hello", "field3": "hello"})
        self.assertFalse("filed3" in form.data)

    def test_json_form_with_null_string(self):
        schema = {
            "type": "object",
            "properties": {"field1": {"type": "string", "migLength": 1}},
        }
        form = JsonForm({"field1": ""}, live_schema=schema)
        self.assertNotIn("filed1", form.data)

    def test_non_strict_mode(self):
        # In non-strict mode, string and int field will be
        # converted automatically if they do not match the
        # definition.
        class MyForm(self.JsonForm):
            schema = self.base_schema

        form = MyForm(
            {"field1": "hello", "field2": "1"},
            live_schema={"type": "object", "properties": {"field2": {"type": "number"}}, "required": ["field2"]},
        )
        self.assertEqual(form.data["field2"], 1)

    def test_strict_mode(self):
        class MyForm(self.JsonForm):
            schema = self.base_schema

        form = MyForm({"field1": "hello", "field2": "1"}, strict=True,)
        self.assertEqual(form.data["field2"], "1")

    def test_should_errors_be_dict(self):
        class MyForm(self.JsonForm):
            schema = self.base_schema

        form = MyForm({"field1": "hello", "field2": 1},)
        field_key = "field2"
        self.assertEqual(form.validate(), False)
        self.assertIsInstance(form.errors, dict)
        self.assertIn(field_key, form.errors)
        self.assertIsInstance(form.errors[field_key], list)
        self.assertEqual(len(form.errors[field_key]), 1)
        self.assertIsInstance(form.errors[field_key][0], str)


class TestArgGenerator(unittest.TestCase):
    def test_default_type(self):
        class Hello:
            pass

        e = None

        try:
            cli_arg_generator(Hello)
        except ValueError as ve:
            e = ve
            pass
        self.assertIsNotNone(e)


class TestOutputSchemaOperator(unittest.TestCase):
    def test_validate_function_success(self):
        config_dict = {
            "output_schema": {
                "index": {
                    "type": "object",
                    "properties": {"username": {"type": "string"}, "password": {"type": "string"}},
                    "required": ["username", "password"],
                },
                "update": {"type": "null"},
            }
        }
        validator = OutputSchemaValidator(config_dict["output_schema"])
        validator.check_output_schema()
        self.assertTrue(validator.is_valid_schema)

    def test_validate_function_fail(self):
        config_dict = {"output_schema": {"index": {"type": "string"}}}

        validator = OutputSchemaValidator(config_dict["output_schema"])
        validator.check_output_schema()
        self.assertFalse(validator.is_valid_schema, False)

    def test_should_all_output_schema_has_type_field(self):
        """
            In this test case
            'index' method in the first floor is array, which
            is not right

            'show' method 'username' should not be 'root',
            it should be {"type": "string"}
        """  # noqa: D208

        config_dict = {
            "output_schema": {
                "create": {"type": "null", "help": "create method return nothing"},
                "show": {
                    "type": "object",
                    "properties": {
                        "active": {"type": "boolean"},
                        "email": {"type": "string"},
                        "role": {"type": "array", "items": [{"type": "string"}]},
                        "timestamp": {
                            "type": "object",
                            "properties": {"$date": {"type": "number"}},
                            "required": ["$date"],
                        },
                        "username": "root",
                    },
                    "required": ["active", "email", "role", "timestamp", "username"],
                },
                "index": {
                    "type": "array",
                    "items": [
                        {
                            "type": "object",
                            "properties": {
                                "active": {"type": "boolean"},
                                "email": {"type": "string"},
                                "role": {"type": "array", "items": [{"type": "string"}]},
                                "timestamp": {
                                    "type": "object",
                                    "properties": {"$date": {"type": "number"}},
                                    "required": ["$date"],
                                },
                                "username": "root",
                            },
                            "required": ["active", "email", "role", "timestamp", "username"],
                        }
                    ],
                },
                "delete": {"type": "null", "help": "delete method return nothing"},
                "update": {"type": "null", "help": "update method return nothing"},
            }
        }

        validator = OutputSchemaValidator(config_dict["output_schema"])
        validator.check_output_schema()
        self.assertFalse(validator.is_valid_schema, False)

    def test_sugar_config_with_output_schema(self):
        config_dict = {
            "schema": {
                "index": {"type": "object", "proeprties": {"show_me": {"type": "boolean"}}, "required": ["show_me"]}
            },
            "output_schema": {"index": {"type": "string"}},
        }

        self.assertRaises(OutputSchemaError, lambda: SugarConfig(config_dict))

    def test_is_valid_iterator_success(self):
        _iterator = [
            {
                "type": "object",
                "properties": {"trick": {"type": "boolean"}, "name": {"type": "string"}},
                "required": ["trick", "name"],
            }
        ]

        validator = OutputSchemaValidator({})
        sign = validator._is_valid(_iterator)
        self.assertEqual(sign, True)

    def test_is_valid_iterator_failed(self):
        _iterator = [("object", "properties")]

        validator = OutputSchemaValidator({})
        sign = validator._is_valid(_iterator)
        self.assertFalse(sign, False)

    def test_is_valid_iterator_inside_failed(self):
        _iterator = [
            {
                "type": "object",
                "properties": {"girlfriend": {"type": "string"}, "name": "string"},
                "required": ["girlfriend", "name"],
            }
        ]

        validator = OutputSchemaValidator({})
        sign = validator._is_valid(_iterator)
        self.assertFalse(sign, False)

    def test_dispatch_mission_success(self):
        schema_obj = {
            "outside": {"type": "object", "properties": {"trick": {"type": "boolean"}}, "required": ["trick"]}
        }

        validator = OutputSchemaValidator({})
        sign = validator._dispatch_mission(schema_obj)
        self.assertTrue(sign, True)

    def test_dispatch_mission_failed(self):
        schema_obj = {"type": "object", "properties": {"trick": {"type": "boolean"}}, "required": ["trick"]}

        validator = OutputSchemaValidator({})
        sign = validator._dispatch_mission(schema_obj)
        self.assertFalse(sign, False)

    def test_can_handler_first_step_is_array(self):
        output_schema = {
            "index": {
                "type": "array",
                "items": [
                    {
                        "type": "object",
                        "properties": {
                            "active": {"type": "boolean"},
                            "email": {"type": "string"},
                            "role": {"type": "array", "items": [{"type": "string"}]},
                        },
                        "required": ["active", "email", "role"],
                    }
                ],
            }
        }

        validator = OutputSchemaValidator(output_schema)
        validator.check_output_schema()
        self.assertTrue(validator.is_valid_schema)

    def test_validate_output_index_with_schema(self):
        config_dict = {
            "schema": {"index": {"type": "object"}},
            "resources": "vms",
            "output_schema": {
                "index": {
                    "type": "array",
                    "items": [
                        {
                            "type": "object",
                            "properties": {
                                "create_time": {"type": "number"},
                                "disks": {
                                    "type": "array",
                                    "items": [
                                        {
                                            "type": "object",
                                            "properties": {
                                                "boot": {"type": "number"},
                                                "bus": {"type": "string"},
                                                "path": {"type": "string"},
                                                "type": {"type": "string"},
                                            },
                                            "required": ["boot", "bus", "path", "type"],
                                        }
                                    ],
                                },
                                "ha": {"type": "boolean"},
                                "memory": {"type": "number"},
                                "nics": {
                                    "type": "array",
                                    "items": [
                                        {
                                            "type": "object",
                                            "properties": {
                                                "mac_address": {"type": "string"},
                                                "vlans": {
                                                    "type": "array",
                                                    "items": [
                                                        {
                                                            "type": "object",
                                                            "properties": {"vlan_id": {"type": "number"}},
                                                            "required": ["vlan_id"],
                                                        }
                                                    ],
                                                },
                                            },
                                            "required": ["mac_address", "vlans"],
                                        }
                                    ],
                                },
                                "node_ip": {"type": "string"},
                                "resource_state": {"type": "string"},
                                "status": {"type": "string"},
                                "type": {"type": "string"},
                                "uuid": {"type": "string"},
                                "vcpu": {"type": "number"},
                                "vm_name": {"type": "string"},
                            },
                            "required": [
                                "create_time",
                                "disks",
                                "ha",
                                "memory",
                                "nics",
                                "node_ip",
                                "resource_state",
                                "status",
                                "type",
                                "uuid",
                                "vm_name",
                            ],
                        }
                    ],
                }
            },
        }

        result = {
            "data": [
                {
                    "create_time": 1468900989,
                    "description": "",
                    "disks": [
                        {
                            "boot": 1,
                            "bus": "virtio",
                            "path": "/usr/share/smartx/volumes/f87e8976-e34f-49dc-bf51-************",
                            "type": "disk",
                        },
                        {
                            "boot": 2,
                            "bus": "virtio",
                            "path": "/usr/share/smartx/volumes/9a2b62be-5ca2-4505-8018-6e50efd8d1c5",
                            "type": "disk",
                        },
                    ],
                    "ha": True,
                    "memory": 1073741824,
                    "nics": [{"mac_address": "52:54:00:14:1e:1b", "vlans": [{"vlan_id": 0}]}],
                    "node_ip": "************",
                    "resource_state": "in-use",
                    "status": "running",
                    "type": "KVM_VM",
                    "uuid": "d23bca9b-03ec-4a18-b44c-c3b62ea248ea",
                    "vcpu": 1,
                    "vm_name": "yiran-test",
                }
            ],
            "ec": "E_OK",
        }

        sugarschema = SchemaSugarBase(config_dict)
        self.assertIsNone(sugarschema.out_schema_filter(result, "index"))

    def test_validate_output_show_raise_validation_error(self):
        config_dict = {
            "schema": {"show": {"type": "object"}},
            "resource": "summary",
            "output_schema": {
                "show": {
                    "type": "object",
                    "properties": {
                        "provisioned_cpu_cores": {"type": "number"},
                        "provisioned_memory": {"type": "number"},
                        "provisioned_memory_in_byte": {"type": "number"},
                        "total_cpu_cores": {"type": "number"},
                        "total_memory": {"type": "number"},
                        "total_memory_in_byte": {"type": "number"},
                        "used_memory": {"type": "number"},
                        "used_memory_in_byte": {"type": "number"},
                        "total_vms": {"type": "number"},
                        "vms_state": {
                            "type": "object",
                            "properties": {
                                "poweredOff": {"type": "boolean"},
                                "poweredOn": {"type": "boolean"},
                                "suspended": {"type": "number"},
                            },
                            "required": ["poweredOff", "poweredOn", "suspended"],
                        },
                    },
                    "required": [
                        "provisioned_cpu_cores",
                        "provisioned_memory",
                        "provisioned_memory_in_byte",
                        "total_cpu_cores",
                        "total_memory",
                        "total_memory_in_byte",
                        "used_memory",
                        "used_memory_in_byte",
                        "total_vms",
                        "vms_state",
                    ],
                }
            },
        }

        result = {
            "data": {
                "provisioned_cpu_cores": 68,
                "provisioned_memory": 289792,
                "provisioned_memory_in_byte": 303868936192,
                "total_cpu_cores": 72,
                "total_memory": 192694,
                "total_memory_in_byte": 202055282688,
                "total_vms": 53,
                "used_memory": 62464,
                "used_memory_in_byte": 65498251264,
                "vms_state": {"poweredOff": 42, "suspended": 0},
            },
            "ec": "E_OK",
        }

        sugarschema = SchemaSugarBase(config_dict)
        self.assertRaises(ValidationError, lambda: sugarschema.out_schema_filter(result, "show"))

        self.assertRaises(OutputSchemaError, lambda: sugarschema.out_schema_filter(result, "update"))

        result["ec"] = "E_ERROR"
        self.assertIsNone(sugarschema.out_schema_filter(result, "show"))
