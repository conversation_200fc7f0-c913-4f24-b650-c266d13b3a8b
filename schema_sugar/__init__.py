# Copyright (c) 2013-2015, SMARTX
# All rights reserved.

from abc import abstractmethod
from collections import (
    OrderedDict,
    defaultdict,
)
import inspect
import json

import click
import jsonschema
from jsonschema import Draft4Validator, FormatChecker
from jsonschema.exceptions import SchemaError, ValidationError

from schema_sugar.constant import (  # noqa: F401
    CLI2OP_MAP,
    CREATE_OP,
    HTTP_GET,
    OPERATIONS,
    RESOURCES_HTTP2OP_MAP,
    SHOW_OP,
    UPDATE_OP,
    method2op,
    resources_method2op,
)
from schema_sugar.exceptions import (  # noqa: F401
    ConfigError,
    FormError,
    MethodNotImplement,
    OutputSchemaError,
    OutputSchemaNotImplement,
)

__version__ = "0.0.1"


def is_abs_method(method):
    if not callable(method):
        raise ValueError("You passed a non-callable method, expect func, got %s" % type(method))
    if hasattr(method, "__isabstractmethod__") and method.__isabstractmethod__ is True:
        return True
    else:
        return False


def _convert_boolean(data):
    if isinstance(data, str):
        if data.lower() in ("true", "1"):
            return True
        if data.lower() in ("false", "0"):
            return False
    return data


def _convert_int(data):
    try:
        return int(data)
    except (ValueError, TypeError):
        return data


def _convert_number(data):
    if isinstance(data, int | float):
        return data
    if isinstance(data, str):
        if data.isdigit():
            return int(data)
    try:
        return float(data)
    except (ValueError, TypeError):
        return data


def _convert_array(data):
    if isinstance(data, str):
        try:
            return json.loads(data)
        except (TypeError, ValueError):
            return data

    return data


converter_map = {
    "boolean": _convert_boolean,
    "integer": _convert_int,
    "number": _convert_number,
    "array": _convert_array,
}


def convert_type(data, type):  # noqa: A002
    """
    Try converting given data to specified type.
    :param data: any data
    :param type: type definition in json-schema
    """
    converter = converter_map.get(type)
    if converter is not None:
        return converter(data)
    return data


class JsonForm:
    """
    Form class that use JsonSchema to do wtforms-like validation.
    Please refer to JsonSchema(in Google) to write the Schema.
    """

    schema = {}

    def __init__(self, json_data, strict=False, live_schema=None):
        """
        Got Json data and validate it by given schema.

        :type json_data: dict
        :param strict: if strict is not True, the data will be handlered
          by _filter_data function.It will remove unnecessary field(which
          does not exist in schema) automatically before validation works.
        :param live_schema: if you haven't inherited JsonForm and overwrite
          the class property 'schema', you can pass and live_schema here to
          do the validation.
        :param try_convert: if True, json-form will try to convert given data to
          expected type. For example , convert "true" "1" to True, etc.
        """
        self.live_schema = live_schema
        if not isinstance(json_data, dict):
            raise TypeError("json_data must be a dict.")
        if (not self.schema) and (live_schema is None):
            raise NotImplementedError("schema not implemented!")
        if live_schema is not None:
            if not self.schema:
                self.schema = live_schema
            else:
                self.schema["properties"].update(live_schema["properties"])
                if "required" in self.schema and "required" in live_schema:
                    self.schema["required"] = list(set(self.schema["required"]) | set(live_schema["required"]))

        Draft4Validator.check_schema(self.schema)

        self.data = {}
        if not strict:
            self._filter_data(json_data, self.schema["properties"], self.data)
        else:
            self.data = json_data
        self.validator = Draft4Validator(self.schema, format_checker=FormatChecker())
        self.errors = None
        self.error_msg = None

    def validate(self):
        try:
            self.validator.validate(self.data, self.schema)
            return True
        except jsonschema.ValidationError as e:
            self.error_msg = str(e)
            self.errors = defaultdict(list)
            for error in self.validator.iter_errors(self.data):
                self.errors[".".join(str(path_part) for path_part in error.absolute_path)].append(error.message)
            return False

    def _filter_data(self, data, properties, output):
        if not properties:
            output.update(data)
            return
        for key in data:
            if data[key] in (None,):
                continue
            if key in properties:
                if "type" not in properties[key]:
                    output[key] = data[key]
                    continue
                if properties[key]["type"].lower() == "object":
                    output[key] = {}
                    self._filter_data(data[key], properties[key]["properties"], output[key])
                else:
                    output[key] = convert_type(data[key], properties[key]["type"])


# more adapter is required for "optional", "default", etc

ARG_CONV_MAP = {
    "number": lambda name: click.argument(name, type=click.INT),
    "string": lambda name: click.argument(name, type=click.STRING),
    "boolean": lambda name: click.option("--" + name, is_flag=True, default=True),
    "default": lambda name: click.argument(name, type=click.STRING),
}


def cli_arg_generator(arg_type):
    """
    Return click argument generator function
    :param arg_type: argument type such as `string` , `number`, etc
    :type arg_type: basestring
    :rtype callable
    """
    if not isinstance(arg_type, str):
        raise ValueError("arg_type shoud be a string, got %s" % type(arg_type))
    return ARG_CONV_MAP.get(arg_type, ARG_CONV_MAP["default"])


class SugarConfig:
    _validation_schema = {
        "type": "object",
        "properties": {
            "help": {"type": "string"},
            "schema": {"type": "object"},
            "resources": {"type": "string", "minLength": 1},
            "resource": {"type": "string", "minLength": 1},
            "version": {"type": "number"},
            "extra_actions": {"type": "object"},
            "out_fields": {"type": "object"},
        },
        "oneOf": [{"required": ["resources"]}, {"required": ["resource"]}],
        "required": ("schema",),
        "additionalProperties": "true",
    }
    _form_schema = {
        "type": "object",
        "properties": {
            "type": {"type": "string"},
            "help": {"type": "string"},
            "properties": {"type": "object"},
            "required": {"type": "array"},
        },
        "required": ("type",),
    }
    _out_fields_schema = {
        "type": "array",
        "items": {"type": "string"},
    }

    def __init__(self, config_dict):
        """
        :type config_dict: dict
        """
        self.config = config_dict
        if self.config.get("extra_actions", None) is None:
            self.config["extra_actions"] = {}
        if self.config.get("out_fields", None) is None:
            self.config["out_fields"] = {}
        if self.config.get("output_schema", None) is None:
            self.config["output_schema"] = {}
            # TODO (raytlty) APISugar should implemented output_schema
            # raise OutputSchemaNotImplement
        for operation_name in self.schema:
            # TODO(winkidney): move support_operations to another place
            if operation_name != "support_operations":
                if "properties" not in self.schema[operation_name]:
                    self.schema[operation_name]["properties"] = {}

        self._check_config(self.config)

    @classmethod
    def _check_config(cls, config_dict):
        # check output_schema
        output_schema_validator = OutputSchemaValidator(config_dict["output_schema"])
        if config_dict["output_schema"] == {}:
            pass
        else:
            output_schema_validator.check_output_schema()
            if output_schema_validator.is_valid_schema is False:
                raise OutputSchemaError(
                    "output_schema is not valid check out\n{}".format(
                        output_schema_validator.validate_emsg
                        if output_schema_validator.validate_emsg
                        else output_schema_validator.schema_emsg
                        if output_schema_validator.schema_emsg
                        else config_dict["output_schema"]
                    )
                )

        try:
            validator = Draft4Validator(cls._validation_schema)
            validator.validate(config_dict)

            # check validation schema
            form_validator = Draft4Validator(cls._form_schema)
            for key, method_schema in list(config_dict["schema"].items()):
                # TODO(winkidney): move support_operations to another place
                if key != "support_operations":
                    form_validator.validate(method_schema)

            out_fields_validator = Draft4Validator(cls._out_fields_schema)
            for out_fields_obj in list(config_dict["out_fields"].values()):
                out_fields_validator.validate(out_fields_obj)

        except ValidationError as e:
            msg = "Syntax Error in your config_dict:\n" + str(e)
            raise ConfigError("{}\n Your config is: {}".format(msg, config_dict))

    @property
    def is_plural(self):
        if "resources" in self.config:
            return True
        return False

    def add_action(self, action_name, http_method):
        self.extra_actions[action_name] = {
            "http_method": http_method,
        }

    def get_out_fields(self, operation_name):
        """
        :rtype : list or None
        """
        return self.config["out_fields"].get(operation_name, None)

    @property
    def support_operations(self):
        return self.schema.get("support_operations")

    @property
    def resource_root(self):
        if self.config.get("resources"):
            return "/" + self.config["resources"]
        else:
            return "/" + self.config["resource"]

    @property
    def resource_detail(self):
        """
        Return resource type(Singular or  plurality)
        :return: {"is_singular": True, "name": "resource_name"}
        """
        if self.is_plural:
            return {"is_singular": False, "name": self.config["resources"]}
        else:
            return {"is_singular": True, "name": self.config["resource"]}

    @property
    def schema(self):
        return self.config["schema"]

    @property
    def output_schema(self):
        return self.config["output_schema"]

    def get_validation_schema(self, operation):
        """
        Get given operation's validation schema.
        If schema not implemented, return default value.
        :type operation: str or unicode
        :rtype :dict
        """
        return self.schema.get(operation, {"type": "object", "properties": {}})

    @property
    def version(self):
        return self.config.get("version", 0)

    @property
    def extra_actions(self):
        return self.config["extra_actions"]

    @property
    def cli_methods(self):
        return self.config.get("cli_methods", list(CLI2OP_MAP.keys()))

    @property
    def http_methods(self):
        return self.config.get("http_methods", list(RESOURCES_HTTP2OP_MAP.keys()))

    @classmethod
    def from_string(cls, config_string):
        """
        Create a new instance from given serialized schema string.
        :param config_string:
        """
        config = json.loads(config_string)
        return cls(config)

    def dumps(self):
        return self.config


class OutputSchemaValidator:
    """
    output schema validate and expand
    """

    _validation_schema = {
        "type": "object",
        "properties": {
            "type": {
                "anyOf": [
                    {"type": "string", "enum": ["string", "null", "object", "array", "integer", "number", "boolean"]},
                    {
                        "type": "array",
                        "items": [
                            {
                                "type": "string",
                                "enum": ["string", "null", "object", "array", "integer", "number", "boolean"],
                            }
                        ],
                    },
                ]
            },
            "properties": {"type": "object"},
            "patternProperties": {"type": "object"},
            "required": {"type": "array"},
        },
        "required": ["type"],
    }

    def __init__(self, output_schema, *args, **kwargs):
        self.output_schema = output_schema
        self.form_validator = Draft4Validator(self._validation_schema)
        if not isinstance(self.output_schema, dict):
            raise TypeError("output_schema must be a dict.\nnow it is {}".format(type(self.output_schema)))

        self.validate_emsg = None
        self.schema_emsg = None
        self._is_valid_schema = False

    @property
    def is_valid_schema(self):
        return self._is_valid_schema

    def check_output_schema(self):
        for action in self.output_schema:
            try:
                self._is_valid_schema = self._validate_schema(self.output_schema.get(action, {}), True)
            except (ValidationError, SchemaError):
                self._is_valid_schema = False
                break
            else:
                if not self._is_valid_schema:
                    break
        else:
            self._is_valid_schema = True if self.output_schema else False

    def _is_valid(self, iterator):
        for item in iterator:
            if not isinstance(item, dict):
                return False
            try:
                res = self._validate_schema(item)
            except SchemaError as e:
                self.schema_emsg = e
                return False
            else:
                if not res:
                    return False
        return True if iterator else False

    def _schema_list_type(self, schema_obj):
        return self._is_valid(schema_obj)

    def _schema_dict_type(self, schema_obj):
        return self._is_valid(list(schema_obj.values()))

    def _dispatch_mission(self, schema_obj):
        return (
            self._schema_list_type(schema_obj)
            if isinstance(schema_obj, list)
            else self._schema_dict_type(schema_obj)
            if isinstance(schema_obj, dict)
            else False
        )

    def _validate_schema(self, schema, is_first_step=False):
        """

        :param schema: output_schema which you define
        :param is_first_step: judge if it should be 'object' or 'null' or not
        :return: True or False

            _validate_schema function aim to check the output_schema
            you write in each config_dict, which define at the class inherit
            APISugar

            It should be like that
                config_dict = {
                    # ... something above such as schema, resource/resources
                    "output_schema": {
                        "index": {
                            "type": "object" # or "null" # type in this floor only can be 'object' or 'null'
                            "properties": {
                                "name": {"type": "string"},
                                "girlfriends": {
                                    "type": "array",
                                    "items": [
                                        {
                                            "type": "string"
                                        }
                                    ]
                                },
                                "phone_number": {"type": "string"},
                                "is_male": {"type": "boolean"},
                                "age": {"type": "number"} # integer belong to number, so number range is bigger
                                "family": {
                                    "type": "array",
                                    "items": [
                                        {
                                            "type": "object",
                                            "properties": {
                                                "name": {"type": "string"},
                                                "nickname": {"type": "string"}
                                            }
                                        }
                                    ]
                                }
                            }
                            # ... something
                        },
                        #another action which write in your APISugar
                    }
                }
        """
        schema_type = schema.get("type", None)
        if is_first_step:
            if not schema_type or not ("object" in schema_type or "array" in schema_type or "null" in schema_type):
                raise SchemaError(
                    "SchemaError happen in first step "
                    "type could only be 'object', 'array' or 'null' "
                    "in {}".format(schema)
                )
        try:
            self.form_validator.validate(schema)
        except ValidationError as e:
            emsg = "ValidationError so check the schema {}\n".format(schema) + str(e)
            self.validate_emsg = emsg
            return False
        else:
            # if schema can pass form_validator
            # schema.get("type") would not be None
            if not ("object" in schema_type or "array" in schema_type):
                return True

            if "array" in schema_type:
                if "items" not in schema:
                    return True

                schema_array = schema.get("items", None)
                if not self._dispatch_mission(schema_array):
                    return False

            if "object" in schema_type:
                if "properties" not in schema and "patternProperties" not in schema:
                    return True

                properties = schema.get("properties", None) or schema.get("patternProperties", None)

                if not self._dispatch_mission(properties):
                    return False

            return True


def action(action_name, http_method=HTTP_GET):
    """
    add an extra_action to a SchemaSugarBase object
    :param action_name: the action name in url(as postfix)
    :param http_method: how does this action been visited
    """

    def wrapper(func):
        func.__is_action__ = True
        func.__action_name__ = action_name
        func.__http_method__ = http_method
        return func

    return wrapper


class SchemaSugarBase:
    """
    Generate resource or blue print to web app and CLI app.
    """

    _default_operation = SHOW_OP

    def __init__(self, config_dict=None):
        """
        :type config_dict: dict
        :param config_dict: if config_dict already existed in class
            properties, this will be ignored.
        """
        if not hasattr(self, "config_dict") and config_dict is not None:
            # TODO(winkidney): config validation
            self.config_dict = config_dict
        elif hasattr(self, "config_dict"):
            pass
        else:
            raise ValueError("config_dict can not be None, expect dict, got %s" % config_dict)
        self.config = SugarConfig(self.config_dict)
        self._make_registry()

    def _make_registry(self):
        # make extra action map
        operations = set(OPERATIONS)
        self.config.schema["support_operations"] = []
        for name, method in inspect.getmembers(self, predicate=inspect.ismethod):
            if hasattr(method, "__is_action__"):
                self.config.add_action(
                    method.__action_name__,
                    method.__http_method__,
                )
            if not is_abs_method(method):
                if name in operations:
                    self.config.schema["support_operations"].append(name)
                if hasattr(method, "__is_action__"):
                    self.config.schema["support_operations"].append(method.__action_name__)

    @abstractmethod
    def make_resources(self, *args, **kwargs):
        pass

    def cli_response(self, result, **kwargs):
        click.echo(result)
        return result

    @abstractmethod
    def web_response(self, result, *args, **kwargs):
        pass

    @abstractmethod
    def index(self, data, web_request, **kwargs):
        pass

    @abstractmethod
    def create(self, data, web_request, **kwargs):
        pass

    @abstractmethod
    def show(self, data, web_request, **kwargs):
        return data

    @abstractmethod
    def update(self, data, web_request, **kwargs):
        pass

    @abstractmethod
    def delete(self, data, web_request, **kwargs):
        pass

    def process(self, operation, data, web_request, **kwargs):
        validate_schema = self.config.get_validation_schema(operation)
        processed_data = self.validate(validate_schema, data)
        return getattr(self, operation)(processed_data, web_request, **kwargs)

    def crud_api(self, raw_method_name, data, web_request=None, **kwargs):
        operation = method2op(raw_method_name)
        return self._api_run(operation, data, web_request, **kwargs)

    def resources_api(self, raw_method_name, data, web_request=None, **kwargs):
        operation = resources_method2op(raw_method_name)
        return self._api_run(operation, data, web_request, **kwargs)

    def action_api(self, raw_method_name):
        operation = raw_method_name
        return lambda passed_operation, data, web_request, **kwargs: self._api_run(
            operation, data, web_request, **kwargs
        )

    def _api_run(self, operation, data, web_request=None, **kwargs):
        """
        :param operation: in self.config.schema.keys()
           (index, create, show, delete, update, etc)
        """
        data = self.pre_process(data, web_request, **kwargs)
        if is_abs_method(getattr(self, operation)):
            raise MethodNotImplement("Operation `%s` not supported" % operation)
        result = self.process(operation, data, web_request, **kwargs)

        # test output result with output_schema
        self.out_schema_filter(result, operation)

        if isinstance(result, dict | OrderedDict):
            result = self.out_filter(result, operation)
        if web_request is not None:
            return self.web_response(result)
        else:
            return self.cli_response(result)

    def out_filter(self, result, operation):
        """
        :type result: dict
        :type operation: str or unicode
        :return: dict
        """
        out_dict = {}
        out_fields = self.config.get_out_fields(operation)
        if out_fields is None:
            return result

        out_fields = set(out_fields)
        for key, value in list(result.items()):
            if key in out_fields:
                out_dict[key] = value
        return out_dict

    def out_schema_filter(self, result, operation):
        """
        :type result: dict
        :type operation: str or unicode
        """

        output_schema = self.config.output_schema
        if not (output_schema and result and isinstance(result, dict)):
            return
        if "OK" not in result["ec"]:
            return

        input_data = result.get("data", None)
        if input_data == {}:
            input_data = None
        if operation not in output_schema:
            raise OutputSchemaError(
                "Checkout you output_schema\nAnd if {} have operation\n{}".format(output_schema, operation)
            )
        else:
            form_validator = Draft4Validator(output_schema[operation])
            form_validator.validate(input_data)

    @staticmethod
    def validate(validate_schema, data):
        schema = validate_schema
        form = JsonForm(data, live_schema=schema)
        if not form.validate():
            raise FormError(form, form.error_msg, form.errors)
        return form.data

    def pre_process(self, data, web_request, **kwargs):
        return data

    def get_doc(self, *args, **kwargs):
        return "This is the example doc data:\n" + str(self.config.schema)


class SugarJarBase:
    def __init__(self, name):
        self.name = name

    @abstractmethod
    def run(self):
        pass

    @abstractmethod
    def register(self, schema_sugar):
        pass
